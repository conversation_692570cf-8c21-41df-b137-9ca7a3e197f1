import { StatusCodes } from "http-status-codes";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.updateMany({
    where: {
      workspaceID: "fa621041-c098-42e9-a0d5-375149b1e72c",
    },
    data: {
      workspaceID: "a0178d32-40c0-45ae-8407-ed08b028c726",
    },
  });

  res.status(StatusCodes.OK).send({
    numPosts: posts.count,
  });
};

export default handler;
