import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const workspaces = await db.workspace.findMany({
    where: {
      subscription: {
        tier: "Standard",
        engagementProfileLimit: 6,
      },
    },
    select: {
      id: true,
      name: true,
      company: {
        select: {
          name: true,
        },
      },
      _count: {
        select: {
          linkedInIntegrations: {
            where: { type: "Engagement" },
          },
        },
      },
    },
  });

  const workspacesWithNoLinkedInIntegrations = workspaces.filter(
    (workspace) => workspace._count.linkedInIntegrations === 0
  );

  await db.workspaceSubscription.updateMany({
    where: {
      workspaceID: {
        in: workspacesWithNoLinkedInIntegrations.map(
          (workspace) => workspace.id
        ),
      },
    },
    data: {
      engagementProfileLimit: 0,
    },
  });

  const workspacesWithLinkedInIntegrations = workspaces.filter(
    (workspace) => workspace._count.linkedInIntegrations > 0
  );

  res.status(200).send({
    workspacesWithLinkedInIntegrations,
    workspacesWithNoLinkedInIntegrations,
  });
};

export default handler;
