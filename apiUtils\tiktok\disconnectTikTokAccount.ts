import { TRPCError } from "@trpc/server";
import catchZenstackDeleteError from "apiUtils/catchZenstackDeleteError";
import removeSubscriptionItem from "apiUtils/stripe/removeSubscriptionItem";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const disconnectTikTokAccount = async ({
  integrationID,
  workspaceID,
  userID,
}: {
  integrationID: string;
  workspaceID: string;
  userID: string;
}) => {
  const tikTokIntegration = await db.tikTokIntegration.findFirst({
    where: {
      id: integrationID,
      workspaceID,
    },
    select: {
      id: true,
      account: {
        select: {
          username: true,
        },
      },
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
    },
  });

  if (!tikTokIntegration) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "No integration found",
    });
  } else {
    const workspace = tikTokIntegration.workspace;

    const postsToUpdate = await db.post.findMany({
      where: {
        tikTokIntegrationID: tikTokIntegration.id,
        scheduledPosts: {
          some: {
            status: "Scheduled",
            channel: "TikTok",
            publishAt: {
              gt: new Date(),
            },
          },
        },
      },
      select: {
        id: true,
      },
    });
    const postIDs = postsToUpdate.map((post) => post.id);

    // Cancel all scheduled posts for just tikTok
    await db.scheduledPost.updateMany({
      where: {
        channel: "TikTok",
        status: "Scheduled",
        publishAt: {
          gt: new Date(),
        },
        post: {
          id: {
            in: postIDs,
          },
        },
      },
      data: {
        status: "Cancelled",
      },
    });

    const updatedPosts = await db.post.findMany({
      where: {
        id: {
          in: postIDs,
        },
      },
      select: {
        id: true,
        _count: {
          select: {
            scheduledPosts: {
              where: {
                status: "Scheduled",
              },
            },
          },
        },
      },
    });

    const postsWithoutScheduledPosts = updatedPosts.filter(
      (post) => post._count.scheduledPosts === 0
    );
    await db.post.updateMany({
      where: {
        id: {
          in: postsWithoutScheduledPosts.map((post) => post.id),
        },
      },
      data: {
        status: "Finalized",
      },
    });

    const postDefaults = await db.postDefault.findFirst({
      where: {
        workspaceID: workspaceID,
      },
      select: {
        tikTokIntegrationID: true,
        workspace: {
          select: {
            tikTokIntegrations: {
              where: {
                id: {
                  not: tikTokIntegration.id,
                },
              },
              select: {
                id: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    if (postDefaults?.tikTokIntegrationID === tikTokIntegration.id) {
      const oldestTikTokIntegration =
        postDefaults.workspace.tikTokIntegrations.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
        )[0];

      await db.postDefault.update({
        where: {
          workspaceID: workspaceID,
        },
        data: {
          tikTokIntegrationID:
            oldestTikTokIntegration != null ? oldestTikTokIntegration.id : null,
        },
      });
    }

    await catchZenstackDeleteError(async () => {
      await db.tikTokIntegration.update({
        where: {
          id: tikTokIntegration.id,
        },
        data: {
          deletedTimeStamp: Math.floor(new Date().getTime() / 1000),
          deletedByID: userID,
        },
      });
    });

    await removeSubscriptionItem({
      workspaceID,
      itemName: "ADDITIONAL_SOCIAL_PROFILE",
      quantity: 1,
    });

    posthog.capture({
      distinctId: userID,
      event: POSTHOG_EVENTS.socialAccount.disconnected,
      properties: {
        channel: "TikTok",
      },
      groups: {
        workspace: workspaceID,
        company: workspace.companyID,
      },
    });

    return tikTokIntegration.account;
  }
};

export default disconnectTikTokAccount;
