import { InformationCircleIcon } from "@heroicons/react/24/outline";
import Tooltip from "~/components/Tooltip";

type Props = {
  label: string;
  value?: string | number | null;
  tooltipText?: string;
  isLoading?: boolean;
};

const Stat = ({ label, value, tooltipText, isLoading }: Props) => {
  if (typeof value === "number") {
    if (value > 1000 && value < 10000) {
      value = value.toLocaleString();
    } else if (value >= 10000) {
      value = `${(value / 1000).toLocaleString()}K`;
    }
  }

  if (value == null && !isLoading) {
    return null;
  }

  return (
    <div>
      <div className="flex items-center">
        <div className="font-eyebrow text-medium-gray">{label}</div>
        {tooltipText ? (
          <>
            <div className="pl-1"> </div>
            <Tooltip side="top" value={tooltipText}>
              <InformationCircleIcon className="text-secondary h-3 w-3" />
            </Tooltip>
          </>
        ) : null}
      </div>
      {isLoading ? (
        <div className="bg-light-gray h-12 w-24 animate-pulse rounded-md" />
      ) : (
        <div className="h3">{value}</div>
      )}
    </div>
  );
};

export default Stat;
