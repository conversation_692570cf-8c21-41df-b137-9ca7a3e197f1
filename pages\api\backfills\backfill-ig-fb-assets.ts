import getAssetBuffer from "apiUtils/getAssetBuffer";
import getAssetsForFacebook from "apiUtils/getAssetsForFacebook";
import getAssetsForInstagram from "apiUtils/getAssetsForInstagram";
import imageSize from "image-size";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

import { removeEmptyElems } from "~/utils";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.findMany({
    take: 500,
    where: {
      channels: {
        hasSome: ["Facebook", "Instagram"],
      },
      // OR: [
      //   {
      //     instagramContent: {
      //       assets: {
      //         some: {
      //           assetID: null,
      //         },
      //       },
      //     },
      //   },
      //   {
      //     facebookContent: {
      //       assets: {
      //         some: {
      //           assetID: null,
      //         },
      //       },
      //     },
      //   },
      // ],
    },
    select: {
      id: true,
      workspaceID: true,
      createdByID: true,
      instagramContent: {
        select: {
          id: true,
          type: true,
          coverPhoto: true,
          assets: {
            select: {
              id: true,
              fileName: true,
              position: true,
              asset: true,
              assetID: true,
              tags: {
                select: {
                  id: true,
                  username: true,
                  x: true,
                  y: true,
                },
              },
            },
          },
        },
      },
      facebookContent: {
        select: {
          id: true,
          coverPhoto: true,
          assets: {
            select: {
              id: true,
              position: true,
              fileName: true,
              assetID: true,
              asset: true,
            },
          },
        },
      },
    },
  });

  console.log(`Num posts to backfill ${posts.length}`);

  const postAssetPromises = posts.map(async (post) => {
    const assetResponses = await Promise.all([
      getAssetsForInstagram(post),
      getAssetsForFacebook(post),
    ]);

    const instagramAssets = assetResponses[0];
    const facebookAssets = assetResponses[1];

    return {
      ...post,
      instagramAssets,
      facebookAssets,
    };
  });

  const postsWithAssets = await Promise.all(postAssetPromises);

  console.log("Got all assets for posts");

  const postsToBackfill = postsWithAssets.filter((post) => {
    return !(
      post.instagramAssets.assets.length === 0 &&
      post.instagramAssets.coverPhoto == null &&
      post.facebookAssets.assets.length === 0 &&
      post.facebookAssets.coverPhoto == null
    );
  });

  console.log(`Backfilling ${postsToBackfill.length} posts`);

  const getImageDimensions = async (
    asset: {
      mimetype: string;
      url: string;
    } | null
  ) => {
    try {
      if (asset && asset.mimetype.includes("image/")) {
        const buffer = await getAssetBuffer(asset.url);
        const dimensions = imageSize(buffer);
        const width = dimensions.width as number;
        const height = dimensions.height as number;
        return { width, height };
      } else {
        return {
          width: null,
          height: null,
        };
      }
    } catch (error) {
      return {
        width: null,
        height: null,
      };
    }
  };

  const allAssets = postsToBackfill.flatMap((post) => {
    const { instagramAssets, facebookAssets } = post;

    const assets = removeEmptyElems([
      ...instagramAssets.assets,
      instagramAssets.coverPhoto,
      ...facebookAssets.assets,
      facebookAssets.coverPhoto,
    ]).map((asset) => ({
      ...asset,
      workspaceID: post.workspaceID,
      userID: post.createdByID,
    }));

    return assets;
  });

  const allAssetsWithDimensions = await Promise.all(
    allAssets.map(async (asset) => {
      const imageDimensions = await getImageDimensions(asset);

      return {
        ...asset,
        ...imageDimensions,
      };
    })
  );

  await db.asset.createMany({
    data: allAssetsWithDimensions.map((asset) => ({
      id: asset.id,
      name: asset.name,
      bucket: "assets",
      path: asset.path,
      eTag: asset.eTag,
      mimetype: asset.mimetype,
      size: asset.sizeBytes,
      url: asset.url,
      width: asset.width,
      height: asset.height,
      urlExpiresAt: asset.urlExpiresAt,
      workspaceID: asset.workspaceID,
      userID: asset.userID,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
    })),
    skipDuplicates: true,
  });

  const backfillPromises = postsToBackfill.map(async (post) => {
    const {
      instagramAssets,
      instagramContent,
      facebookAssets,
      facebookContent,
    } = post;

    const updateInstagramAssetIDPromises = (instagramContent?.assets || []).map(
      async (asset) => {
        if (!asset.assetID) {
          const matchingAsset = instagramAssets.assets.find(
            (instagramAsset) => instagramAsset.name === asset.fileName
          );

          if (!matchingAsset) {
            console.error(
              `Backfill Asset Error post:${post.id}: No matching Instagram asset ${asset.id}`
            );
          } else {
            return await db.instagramAsset.update({
              where: {
                id: asset.id,
              },
              data: {
                assetID: matchingAsset.id,
              },
            });
          }
        } else {
          return null;
        }
      }
    );

    if (updateInstagramAssetIDPromises?.length) {
      await Promise.all(updateInstagramAssetIDPromises);
    }

    if (instagramAssets.coverPhoto) {
      await db.instagramContent.update({
        where: {
          id: instagramContent!.id,
        },
        data: {
          coverPhotoID: instagramAssets.coverPhoto.id,
        },
      });
    }

    const updateFacebookAssetIDPromises = facebookContent?.assets.map(
      async (asset) => {
        if (!asset.assetID) {
          const matchingAsset = facebookAssets.assets.find(
            (facebookAsset) => facebookAsset.name === asset.fileName
          );

          if (!matchingAsset) {
            console.error(
              `Backfill Asset Error post:${post.id}: No matching Facebook asset ${asset.id}`
            );
          } else {
            return await db.facebookAsset.update({
              where: {
                id: asset.id,
              },
              data: {
                assetID: matchingAsset.id,
              },
            });
          }
        } else {
          return null;
        }
      }
    );

    if (updateFacebookAssetIDPromises?.length) {
      await Promise.all(updateFacebookAssetIDPromises);
    }

    if (facebookAssets.coverPhoto) {
      await db.facebookContent.update({
        where: {
          id: facebookContent!.id,
        },
        data: {
          coverPhotoID: facebookAssets.coverPhoto.id,
        },
      });
    }
  });

  await Promise.all(backfillPromises);

  await db.post.updateMany({
    where: {
      id: {
        in: posts.map((post) => post.id),
      },
    },
    data: {
      hasBackfilledAssets: true,
    },
  });

  res.status(200).send({
    numPostsBackfilled: postsToBackfill.length,
  });
};

export default handler;
