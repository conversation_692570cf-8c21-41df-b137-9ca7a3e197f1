import { Node, ReactNodeViewRenderer } from "@tiptap/react";
import { ImageUpload as ImageUploadComponent } from "./view/ImageUpload";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    imageUpload: {
      setImageUpload: (options: {
        onUpload?: (file: File) => Promise<string>;
      }) => ReturnType;
    };
  }
}

export interface ImageUploadOptions {
  onUpload?: (file: File) => Promise<string>;
}

export const ImageUpload = Node.create<ImageUploadOptions>({
  name: "imageUpload",

  isolating: true,

  defining: true,

  group: "block",

  draggable: true,

  selectable: true,

  inline: false,

  addOptions() {
    return {
      onUpload: undefined,
    };
  },

  parseHTML() {
    return [
      {
        tag: `div[data-type="${this.name}"]`,
      },
    ];
  },

  renderHTML() {
    return ["div", { "data-type": this.name }];
  },

  addCommands() {
    return {
      setImageUpload:
        (options = {}) =>
        ({ commands }) => {
          if (options.onUpload) {
            this.options.onUpload = options.onUpload;
          }
          return commands.insertContent(`<div data-type="${this.name}"></div>`);
        },
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(ImageUploadComponent);
  },

  addAttributes() {
    return {
      onUpload: {
        default: undefined,
        parseHTML: () => undefined,
        renderHTML: () => ({}),
      },
    };
  },
});

export default ImageUpload;
