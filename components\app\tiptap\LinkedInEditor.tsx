import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Document from "@tiptap/extension-document";
import FontFamily from "@tiptap/extension-font-family";
import { History } from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import TextStyle from "@tiptap/extension-text-style";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import classNames from "classnames";
import { ClipboardEvent, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import AutoHashtag from "~/components/app/tiptap/extensions/AutoHashtag";
import CharacterCount from "~/components/app/tiptap/extensions/CharacterCount";
import LinkedInMention from "~/components/app/tiptap/extensions/LinkedInMention";
import { CHANNEL_ICON_MAP } from "~/types";
import { stripLiveblocksMarks } from "~/utils";
import escapeSpecialCharacters from "~/utils/linkedin/escapeSpecialCharacters";
import { Threads } from "./Threads";
import {
  CustomCommentThreadMark,
  Emoji,
  Link,
  PasteHandler,
} from "./extensions";
import BoldUnicodeButton from "./ui/BoldUnicodeButton";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import FloatingToolbar from "./ui/FloatingToolbar";
import ItalicsUnicodeButton from "./ui/ItalicsUnicodeButton";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  title?: string;
  postID?: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  size?: "sm" | "md";
  disableTagging?: boolean;
  escapeContent?: boolean;
  isEditable?: boolean;
  roomID: string | null;
};

type LinkedInEditorProps = Omit<BaseProps, "roomID">;

const createBaseExtensions = (
  postID: string | undefined,
  placeholder: string,
  disableTagging: boolean,
  size: "sm" | "md"
) => [
  Document,
  Paragraph.configure({
    HTMLAttributes: {
      class: size === "sm" ? "font-body-sm" : "font-body",
    },
  }),
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  Text,
  TextStyle,
  FontFamily,
  Link.configure({
    isLinkShorteningEnabled: !!postID ? true : false,
    postID,
  }),
  History,
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  Emoji,
  AutoHashtag.configure({
    urlFn: (hashtag) =>
      `https://www.linkedin.com/feed/hashtag/?keywords=${hashtag}`,
  }),
  ...(!disableTagging ? [LinkedInMention] : []),
  CharacterCount.configure({
    limit: 3000,
  }),
  PasteHandler,
];

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const LinkedInEditorFallback = ({
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  size = "md",
  isReadOnly = false,
  disableTagging = false,
  escapeContent = true,
  isEditable = false,
}: LinkedInEditorProps) => {
  const cleanContent = stripLiveblocksMarks(initialValue);
  const [editorState, setEditorState] = useState<JSONContent | null>(
    cleanContent
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(cleanContent) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const editor = useEditor({
    extensions: createBaseExtensions(postID, placeholder, disableTagging, size),
    immediatelyRender: false,
    content: cleanContent,
    editable: isEditable && !isReadOnly,
    onUpdate: ({ editor }) => {
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });

      if (onUpdateContent) {
        if (escapeContent) {
          const cleanedContent = escapeSpecialCharacters(textContent);
          onUpdateContent(cleanedContent);
        } else {
          onUpdateContent(textContent);
        }
      }

      setEditorState(editor.getJSON());
    },
  });

  if (!editor) return null;

  return isEditable && !isReadOnly ? (
    <div
      className={classNames({
        "py-0": size === "sm",
        "py-2": size === "md",
      })}
    >
      <FloatingToolbar editor={editor} shouldHideWithLink={true}>
        <BoldUnicodeButton editor={editor} useCustomToolbarButton={true} />
        <ItalicsUnicodeButton editor={editor} useCustomToolbarButton={true} />
      </FloatingToolbar>
      <EditorContent editor={editor} />
    </div>
  ) : (
    <EditorContent editor={editor} />
  );
};

const LinkedInEditor = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  size = "md",
  disableTagging = false,
  escapeContent = true,
}: LinkedInEditorProps) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(postID, placeholder, disableTagging, size),
    ],
    immediatelyRender: false,
    editable: !isReadOnly,
    onUpdate: ({ editor }) => {
      // TipTap doubles newlines for paragraph breaks (e.g., pressing Enter once creates two \n).
      // We halve the newlines to match LinkedIn's expected format while preserving paragraph structure.
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      if (escapeContent) {
        const cleanedContent = escapeSpecialCharacters(textContent);
        onUpdateContent(cleanedContent);
      } else {
        onUpdateContent(textContent);
      }

      setEditorState(editor.getJSON());
    },
  });

  const isEditorReady = useIsEditorReady();

  if (!editor) return null;

  return (
    <div
      className="py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor} shouldHideWithLink={true}>
        <ToolbarActions
          editor={editor}
          features={{
            unicodeFormatting: true,
          }}
        />
      </CustomFloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}

      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["LinkedIn"].label,
            }}
          />
          <ClientSideSuspense fallback={null}>
            <Threads editor={editor} />
          </ClientSideSuspense>
        </>
      ) : (
        <LinkedInEditorFallback
          initialValue={initialValue}
          size={size}
          postID={postID}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
        />
      )}
    </div>
  );
};

const LinkedInEditorWrapper = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  size = "md",
  disableTagging,
  escapeContent,
  roomID,
}: BaseProps) => {
  if (!roomID) {
    return (
      <LinkedInEditorFallback
        postID={postID}
        initialValue={initialValue}
        placeholder={placeholder}
        onSave={onSave}
        onUpdateContent={onUpdateContent}
        size={size}
        disableTagging={disableTagging}
        escapeContent={escapeContent}
        isReadOnly={isReadOnly}
        isEditable={!isReadOnly}
      />
    );
  }

  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense
        fallback={
          <div className="py-2">
            <LinkedInEditorFallback
              initialValue={initialValue}
              size={size}
              postID={postID}
              placeholder={placeholder}
              onSave={onSave}
              onUpdateContent={onUpdateContent}
            />
          </div>
        }
      >
        <LinkedInEditor
          title={title}
          postID={postID}
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
          onImagePaste={onImagePaste}
          isReadOnly={isReadOnly}
          size={size}
          disableTagging={disableTagging}
          escapeContent={escapeContent}
        />
      </ClientSideSuspense>
    </RoomProvider>
  );
};

export default LinkedInEditorWrapper;
