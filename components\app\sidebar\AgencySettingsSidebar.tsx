import { CalendarIcon, UsersIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { useRouter } from "next/router";
import { useMemo } from "react";
import Sidebar from "~/components/ui/Sidebar";
import { useUser } from "~/providers";
import { trpc } from "~/utils";
import MenuSidebarContent, { Group } from "./MenuSidebarContent";
export const AgencySettingsSidebar = () => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();
  const billingPortalUrlQuery = trpc.stripe.getBillingPortalUrl.useQuery();

  const { user } = useUser();

  const billingPortalUrl = billingPortalUrlQuery.data?.billingPortalUrl;

  const agencyItems: Group[] = useMemo(
    () => [
      {
        name: "Agency Settings",
        items: [
          {
            name: "Members",
            icon: <UsersIcon className="h-4 w-4" />,
            href: "/agency/settings/members",
          },
        ],
      },
    ],
    []
  );

  const calendarItems: Group[] = useMemo(
    () => [
      {
        name: "Calendar",
        items: [
          {
            name: "Master Calendar",
            icon: <CalendarIcon className="h-4 w-4" />,
            href: "/agency/settings/calendar",
          },
        ],
      },
    ],
    []
  );

  const sidebarGroups: Group[] = useMemo(
    () => [...calendarItems, /* ...agencyItems */],
    [calendarItems, /* agencyItems */]
  );

  return (
    <Sidebar.Root>
      <Sidebar.Header className="px-5 pt-6">
        <div className="flex items-start justify-between">
          <div className="font-body-sm flex flex-col justify-start gap-0">
            <span className="font-medium">Agency Settings</span>
            <span className="text-medium-dark-gray">{user?.agency?.name}</span>
          </div>
          <Link
            className="text-medium-dark-gray hover:text-basic transition-colors"
            href="/"
          >
            <XMarkIcon className="h-4 w-4" />
          </Link>
        </div>
      </Sidebar.Header>
      <MenuSidebarContent groups={sidebarGroups} />
    </Sidebar.Root>
  );
};
