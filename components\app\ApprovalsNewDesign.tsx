import { useEffect, useState } from "react";

import classNames from "classnames";

import {
  CheckIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  EyeSlashIcon,
  PlusIcon,
} from "@heroicons/react/24/outline";
import { ApprovalStatus } from "@prisma/client";
import { DateTime } from "luxon";
import { toast } from "sonner";
import { OptionsDropdown, Tooltip, UserAvatar } from "~/components";
import Button from "~/components/ui/Button";
import { useBreakpoints } from "~/hooks";
import { useUser } from "~/providers";
import { capitalizeWords, getFullName, handleTrpcError, trpc } from "~/utils";
import Separator from "../ui/Separator";
import RequestApprovalModal from "./posts/RequestApprovalModal";

export type Approval = {
  id: string;
  userID: string;
  status: ApprovalStatus;
  dueDate: Date | null;
  approvedAt: Date | null;
  requestedByID: string;
  requestedBy?: {
    firstName: string | null;
    lastName: string | null;
  };
  message: string | null;
  isBlocking: boolean;
  user: {
    id: string;
    firstName: string | null;
    lastName: string | null;
  };
};

type Props = {
  id: string;
  type: "post" | "campaign";
  approvals: Approval[];
  isReadOnly?: boolean;
  publishDate?: Date;
  publishAt?: Date | null;
};

const ApprovalsNewDesign = ({
  id,
  type,
  approvals: initialApprovals,
  isReadOnly = false,
  publishDate,
  publishAt,
}: Props) => {
  const trpcUtils = trpc.useUtils();

  const { user: currentUser } = useUser();
  const [approvals, setApprovals] = useState<Approval[]>(initialApprovals);
  const { isDesktop } = useBreakpoints();
  const [requestApprovalModalOpen, setRequestApprovalModalOpen] =
    useState<boolean>(false);
  const [isApprovalsExpanded, setIsApprovalsExpanded] =
    useState<boolean>(false);

  useEffect(() => {
    if (approvals.length === 0) {
      setIsApprovalsExpanded(false);
    }
  }, [approvals.length]);

  const getWorkspaceUsersQuery = trpc.workspace.getUsers.useQuery();
  const workspaceUsers = getWorkspaceUsersQuery.data?.users || [];

  const unrequestedWorkspaceUsers = workspaceUsers.filter(
    (user) => !approvals.map((approval) => approval.userID).includes(user.id)
  );

  const approveMutation = trpc.approval.approve.useMutation({
    onError: (error) => {
      handleTrpcError({
        title: `Failed to Approve ${capitalizeWords(type)}`,
        error,
      });
    },
    onSuccess: (data) => {
      setApprovals(data.approvals);
      toast.success(`${capitalizeWords(type)} Approved`);
    },
  });

  const unapproveMutation = trpc.approval.unapprove.useMutation({
    onError: (error) => {
      handleTrpcError({
        title: `Failed to Unapprove ${capitalizeWords(type)}`,
        error,
      });
    },
    onSuccess: (data) => {
      setApprovals(data.approvals);
      toast.success(`${capitalizeWords(type)} Unapproved`);
    },
  });

  const deleteApprovalRequestMutation = trpc.approval.delete.useMutation({
    onSuccess: (data) => {
      setApprovals(data.approvals);
      toast.success(`Approval Request Deleted`);
    },
    onError: (error) => {
      handleTrpcError({ title: `Failed to Delete Request`, error });
    },
  });

  const handleDeleteApprovalRequest = (approvalID: string) => {
    deleteApprovalRequestMutation.mutate({
      id: approvalID,
    });
  };

  const handleApproveContent = (approvalID: string) => {
    approveMutation.mutate({
      id: approvalID,
    });
  };

  const handleUnapproveContent = (approvalID: string) => {
    unapproveMutation.mutate({
      id: approvalID,
    });
  };

  const currentUserPendingApproval = approvals.find(
    (approval) =>
      approval.user.id === currentUser?.id && approval.status !== "Approved"
  );

  const approvedApprovals = approvals.filter(
    (approval) => approval.status === "Approved"
  );

  return (
    <div className="grid w-full grid-cols-1 rounded-xl">
      <div>
        <div className="mb-3.5 hidden items-center gap-3 lg:flex">
          <span className="font-eyebrow-sm text-medium-dark-gray">
            Approvals:
          </span>
          {!isReadOnly && (
            <button
              className={classNames(
                "bg-lightest-gray hover:bg-light-gray rounded-full p-0.5 text-black transition-colors"
              )}
              onClick={() => {
                setRequestApprovalModalOpen(true);
              }}
            >
              <PlusIcon className="h-3 w-3" />
            </button>
          )}
        </div>
        {approvals.length > 0 && (
          <div className="-ml-1 flex lg:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsApprovalsExpanded(!isApprovalsExpanded)}
            >
              {isApprovalsExpanded ? (
                <EyeSlashIcon className="h-4 w-4" />
              ) : (
                <EyeIcon className="h-4 w-4" />
              )}
              {isApprovalsExpanded
                ? `Hide ${approvedApprovals.length}/${approvals.length} approvals`
                : `Show ${approvedApprovals.length}/${approvals.length} approvals`}
            </Button>
          </div>
        )}
        <div>
          {(isApprovalsExpanded || isDesktop) && (
            <>
              <div className="mt-4 grid w-full grid-cols-1 gap-2">
                {approvals.map((approval) => {
                  const { status, dueDate, approvedAt } = approval;

                  let dueDateText = "No Due Date";
                  if (status === "Requested" && dueDate) {
                    dueDateText = `Due ${DateTime.fromJSDate(dueDate, {
                      zone: "utc",
                    }).toFormat("MM/dd")}`;
                  } else if (status === "Approved" && approvedAt) {
                    dueDateText = `Approved on ${DateTime.fromJSDate(
                      approvedAt
                    ).toFormat("MM/dd")}`;
                  }

                  const approverIsCurrentUser =
                    approval.user.id === currentUser?.id;

                  const deleteRequest =
                    status === "Approved" ||
                    approval.requestedByID !== currentUser?.id
                      ? undefined
                      : () => handleDeleteApprovalRequest(approval.id);

                  return (
                    <div
                      key={approval.id}
                      className="font-body-sm flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <div className="relative">
                          <UserAvatar
                            key={approval.id}
                            user={approval.user}
                            tooltipText={getFullName(approval.user)}
                            backgroundColor="secondary"
                            isLoading={
                              (deleteApprovalRequestMutation.isPending &&
                                deleteApprovalRequestMutation.variables?.id ===
                                  approval.id) ||
                              (unapproveMutation.isPending &&
                                unapproveMutation.variables?.id === approval.id)
                            }
                            onXClick={isReadOnly ? undefined : deleteRequest}
                            showCheck={status === "Approved"}
                          />
                        </div>
                        {getFullName(approval.user, {
                          shortenedLastName: true,
                        })}
                        {approval.isBlocking && status !== "Approved" && (
                          <Tooltip
                            side="bottom"
                            value="This approval was set as blocking which means that the post will not automatically post if not approved by the scheduled time."
                          >
                            <div className="bg-warning flex h-4 w-4 items-center justify-center rounded-xs text-white">
                              <ExclamationTriangleIcon className="h-2.5 w-2.5" />
                            </div>
                          </Tooltip>
                        )}
                      </div>
                      {!approverIsCurrentUser || status !== "Approved" ? (
                        <div
                          className={classNames("", {
                            "text-danger":
                              !!dueDate &&
                              DateTime.fromJSDate(dueDate) < DateTime.now() &&
                              status !== "Approved",
                            "text-medium-gray": !dueDate,
                          })}
                        >
                          {dueDateText}
                        </div>
                      ) : null}
                      {approverIsCurrentUser && status === "Approved" && (
                        <OptionsDropdown
                          position="top-right"
                          options={[
                            {
                              key: "unapprove",
                              label: "Unapprove",
                              intent: "danger",
                              onClick: () => {
                                handleUnapproveContent(approval.id);
                              },
                            },
                          ]}
                        />
                      )}
                    </div>
                  );
                })}
                {approvals.length === 0 && (
                  <div className="text-dark-gray text-xs">
                    No approvers requested
                  </div>
                )}
              </div>
              {approvals.length > 0 && isApprovalsExpanded && !isDesktop && (
                <div className="mt-4 block lg:hidden">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => {
                      setRequestApprovalModalOpen(true);
                    }}
                  >
                    Add Approval
                  </Button>
                </div>
              )}
            </>
          )}
          {approvals.length === 0 && (
            <div className="mt-4 block lg:hidden">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => {
                  setRequestApprovalModalOpen(true);
                }}
              >
                Add Approval
              </Button>
            </div>
          )}
          {currentUserPendingApproval && (
            <div className="border-light-gray mt-4 flex flex-col gap-3 rounded-[5px] border p-4">
              <div className="font-body-sm flex flex-col gap-1">
                <span className="font-medium">
                  {getFullName(currentUserPendingApproval.requestedBy)}{" "}
                  requested your approval
                </span>
                <span className="text-medium-dark-gray border-light-gray line-clamp-4 border-l-2 pl-2.5 font-normal">
                  {currentUserPendingApproval.message}
                </span>
              </div>
              <Button
                size="sm"
                isLoading={
                  approveMutation.isPending &&
                  approveMutation.variables?.id ===
                    currentUserPendingApproval.id
                }
                onClick={() =>
                  handleApproveContent(currentUserPendingApproval.id)
                }
              >
                <CheckIcon className="h-4 w-4" /> Approve
              </Button>
            </div>
          )}
        </div>
      </div>
      <RequestApprovalModal
        id={id}
        type={type}
        publishDate={publishDate}
        publishAt={publishAt}
        open={requestApprovalModalOpen}
        setOpen={setRequestApprovalModalOpen}
        users={unrequestedWorkspaceUsers}
        onSuccess={(newApprovals) => {
          if (type === "post") {
            trpcUtils.post.get.invalidate({ id });
          } else if (type === "campaign") {
            trpcUtils.campaign.get.invalidate({ id });
          }

          if (approvals.length === 0 && newApprovals.length > 0) {
            setIsApprovalsExpanded(true);
          }
          setApprovals(newApprovals);
        }}
      />
      <Separator orientation="horizontal" className="mt-6 mb-3 lg:mb-6" />
    </div>
  );
};

export default ApprovalsNewDesign;
