import getAuthedTwitterClient from "apiUtils/getAuthedTwitterClient";
import { slugify } from "inngest";
import { db, slack } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { inngest } from "../inngest";

export default inngest.createFunction(
  {
    id: slugify("Tweet Thread Engagement"),
    name: "Tweet Thread Engagement",
    onFailure: async ({ error, event }) => {
      const data = event.data.event.data;
      const runID = event.data.run_id;
      const { engagementID, permalink } = data;
      console.log(
        `Twitter Engagement:${engagementID}: Errored ${error.message}`
      );

      const engagement = await db.twitterPostEngagement.findFirst({
        where: {
          id: engagementID,
        },
        select: {
          integration: {
            select: {
              account: {
                select: {
                  username: true,
                },
              },
            },
          },
          content: {
            select: {
              post: {
                select: {
                  id: true,
                  title: true,
                  workspace: {
                    select: {
                      name: true,
                      company: {
                        select: { name: true },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!engagement) {
        console.error(`Twitter Engagement:${engagementID}: Engagement deleted`);
        return;
      } else {
        const account = engagement.integration.account;

        const profile = {
          name: `@${account.username}`,
          url: `https://x.com/${account.username}`,
        };

        await slack.uncaughtFailedEngagement({
          channel: "Twitter",
          post: engagement.content.post,
          postUrl: permalink,
          workspace: engagement.content.post.workspace,
          engagementID,
          error: error.message,
          profile,
          runID,
        });
      }
    },
  },
  { event: "twitter.postEngagement" },
  async ({ event, step }) => {
    const { delayUntil, engagementID, externalPostID: tweetID } = event.data;

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    await step.run("Post Tweet Thread Engagement", async () => {
      console.log(`Twitter Engagement:${engagementID}: Finding record`);
      const engagement = await db.twitterPostEngagement.findFirst({
        where: {
          id: engagementID,
        },
        select: {
          id: true,
          type: true,
          copy: true,
          url: true,
          postDelaySeconds: true,
          content: {
            select: {
              post: {
                select: {
                  id: true,
                  createdByID: true,
                  workspace: {
                    select: {
                      id: true,
                      companyID: true,
                    },
                  },
                },
              },
            },
          },
          integration: {
            select: {
              accessToken: true,
              accessSecret: true,
              account: {
                select: {
                  id: true,
                  username: true,
                },
              },
            },
          },
        },
      });
      if (!engagement) {
        return {
          skipped: true,
          reason: "Engagement deleted",
        };
      }

      console.log(`Twitter Engagement:${engagementID}: Found record`);

      if (engagement.url != null) {
        console.error(
          `Twitter Engagement:${engagementID}: No url to engage with`
        );
        return {
          error: "No url to engage with",
        };
      }

      const { integration, type, copy } = engagement;

      const engagementClient = getAuthedTwitterClient(integration);

      let url = null;

      if (type === "Comment") {
        if (copy == null || copy.length === 0) {
          console.error(
            `Twitter Engagement:${engagementID}: No copy to comment with`
          );
          return {
            skipped: true,
            reason: "No copy to comment with",
          };
        }

        const { data, errors } = await engagementClient.v2.reply(copy, tweetID);

        if (data) {
          url = `https://twitter.com/${integration.account.username}/status/${data.id}`;
          console.log(`Twitter Engagement:${engagementID}: Posted to ${url}`);
        } else {
          console.error(`Twitter Engagement:${engagementID}: Errored`, errors);
          const error =
            errors?.map((error) => error.detail).join(". ") ||
            "Error posting reply";
          throw new Error(error);
        }
      } else if (engagement.type === "Like") {
        return {
          skipped: true,
          reason: "Like engagement not supported",
        };
      } else {
        // This means we simply retweet the tweet
        if (copy == null || copy.length === 0) {
          const { data, errors } = await engagementClient.v2.retweet(
            integration.account.id,
            tweetID
          );

          if (data) {
            url = `https://twitter.com/${integration.account.username}`;
            console.log(
              `Twitter Engagement:${engagementID}: Retweeted at ${url}`
            );
          } else {
            console.error(
              `Twitter Engagement:${engagementID}: Errored`,
              errors
            );
            const error =
              errors?.map((error) => error.detail).join(". ") ||
              "Error retweeting";
            throw new Error(error);
          }
        } else {
          const { data, errors } = await engagementClient.v2.tweet(copy, {
            quote_tweet_id: tweetID,
          });

          if (data) {
            url = `https://twitter.com/${integration.account.username}/status/${data.id}`;
            console.log(
              `Twitter Engagement:${engagementID}: Quote tweeted at ${url}`
            );
          } else {
            console.error(
              `Twitter Engagement:${engagementID}: Errored`,
              errors
            );
            const error =
              errors?.map((error) => error.detail).join(". ") ||
              "Error retweeting";
            throw new Error(error);
          }
        }
      }

      if (url) {
        await db.twitterPostEngagement.update({
          where: {
            id: engagement.id,
          },
          data: {
            url,
          },
        });

        posthog.capture({
          distinctId: engagement.content.post.createdByID,
          event: POSTHOG_EVENTS.engagement.published,
          properties: {
            channel: "LinkedIn",
            engagementType: engagement.type,
            postID: engagement.content.post.id,
            delaySeconds: engagement.postDelaySeconds,
          },
          groups: {
            workspace: engagement.content.post.workspace.id,
            company: engagement.content.post.workspace.companyID,
          },
        });

        return {
          url,
        };
      }
    });
  }
);
