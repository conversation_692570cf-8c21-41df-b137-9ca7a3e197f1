import { ArrowPathIcon, ChevronDownIcon } from "@heroicons/react/24/outline";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import { arrayMoveImmutable } from "array-move";
import assert from "assert";
import classNames from "classnames";
import parsePhoneNumber from "libphonenumber-js";
import kebabCase from "lodash/kebabCase";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { WebflowFieldValidation } from "~/clients/Webflow";
import {
  Combobox,
  DatePicker,
  Select,
  TextInput,
  TextareaInput,
  TimeInput,
} from "~/components";
import { InputType } from "~/components/TextInput";
import Button from "~/components/ui/Button";
import { ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import { AssetType } from "~/types/Asset";
import {
  cleanFileName,
  getDateUTC,
  getImageProperties,
  handleTrpcError,
  joinWithAnd,
  trpc,
} from "~/utils";
import getFileProperties from "~/utils/getFileProperties";
import Asset from "../../files/Asset";
import Assets from "../../files/Assets";
import { UploadingAsset } from "../../files/AssetsNewDesign";
import FileDropzone from "../../files/FileDropzone";

export type WebflowField = {
  id: string;
  value: string | null;
  displayName: string;
  helpText: string | null;
  slug: string;
  position: number;
  isRequired: boolean;
} & WebflowFieldValidation;

const TextInputField = ({
  value,
  onChange,
  onBlur,
  isSubmitted,
  isReadOnly,
  setIsSubmitted,
  reValidateMode = "onBlur",
  checkIsValid,
  type,
  width,
}: {
  fieldName: string;
  value: string | null;
  onChange: (value: string) => void;
  onBlur: (value: string | null) => void;
  isSubmitted: boolean;
  isReadOnly?: boolean;
  setIsSubmitted: (value: boolean) => void;
  reValidateMode?: "onChange" | "onBlur";
  checkIsValid: (value: string | null) =>
    | {
        isValid: true | null;
        title?: string;
        description?: string;
      }
    | {
        isValid: false;
        title: string;
        description: string;
      };
  type?: InputType;
  width: number;
}) => {
  let isInvalid: boolean = false;
  if (reValidateMode === "onBlur" && isSubmitted) {
    isInvalid = !!checkIsValid && !checkIsValid(value).isValid;
  } else if (reValidateMode === "onChange") {
    isInvalid = !!checkIsValid && !checkIsValid(value).isValid;
  }

  return (
    <div
      className="flex items-center gap-2"
      style={{
        width,
      }}
    >
      <TextInput
        placeholder="Enter a value"
        kind="outline"
        type={type}
        value={value || ""}
        isReadOnly={isReadOnly}
        isInvalid={isInvalid}
        onChange={(value) => {
          onChange(value);
        }}
        onBlur={() => {
          if (checkIsValid) {
            const { isValid, title, description } = checkIsValid(value);

            if (isValid === false) {
              setIsSubmitted(true);
              return toast.error(title, {
                description,
              });
            } else if (isValid === null) {
              return;
            }
          }

          setIsSubmitted(false);
          onBlur(value);
        }}
      />
    </div>
  );
};

type Props = {
  field: WebflowField;
  blogTitle: string | null;
  assets: AssetType[];
  isReadOnly?: boolean;
  storagePath: string;
  onChange: (value: string | null) => void;
  onUpdateAssets: (assets: AssetType[]) => void;
};

const WebflowFieldInput = ({
  field,
  blogTitle,
  assets,
  isReadOnly,
  storagePath,
  onChange,
  onUpdateAssets,
}: Props) => {
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const createAssetsMutation =
    trpc.webflowContent.createFieldAssets.useMutation();
  const deleteAssetMutation =
    trpc.webflowContent.deleteFieldAsset.useMutation();
  const updateAssetPositionsMutation =
    trpc.webflowContent.updateAssetPositions.useMutation();

  const onSortEnd = (oldIndex: number, newIndex: number) => {
    const newAssets = arrayMoveImmutable(assets, oldIndex, newIndex);

    updateAssetPositionsMutation.mutate({
      assets: newAssets.map((asset, index) => ({
        id: asset.id,
        position: index,
      })),
    });

    onUpdateAssets(newAssets);
  };

  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);

  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const [currentValue, setCurrentValue] = useState<string | null>(field.value);
  const [date, setDate] = useState<Date | null>(
    field.value ? new Date(field.value) : null
  );
  const [time, setTime] = useState<Date | null>(
    field.value ? new Date(field.value) : null
  );
  const [timeAutofocus, setTimeAutofocus] = useState<boolean>(false);
  const [uppyInstance, setUppyInstance] = useState<Uppy | null>(null);

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppy = new Uppy({
        restrictions: {
          maxNumberOfFiles: field.type === "MultiImage" ? 25 : 1,
        },
        autoProceed: true,
      }).use(Tus, options);

      uppy.on("file-added", async (file) => {
        setUploadingAssets((uploadingAssets) => {
          return [
            ...uploadingAssets,
            {
              id: file.id,
              name: file.name!,
              size: file.size!,
              type: file.type,
              uploadProgress: 0,
              updateAssetID: file.meta.updateAssetID as string | undefined,
              width: 0,
              height: 0,
            },
          ];
        });

        const objectName = `${storagePath}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: ONE_YEAR_IN_SECONDS,
        };

        return file;
      });

      uppy.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppy.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppy.on("complete", async (data) => {
        const fileNames = data.successful!.map((file) => file.name!);
        const filesWithProperties = await getFileProperties(
          data.successful!.map((file) => ({
            name: file.name!,
            data: file.data!,
            mimetype: file.meta.type as string,
          }))
        );
        getNewAssetsMutation.mutate(
          {
            path: storagePath,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const newAssetsWithProperties = data.assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );
                if (!assetProperties) {
                  return {
                    ...asset,
                    id: uuidv4(),
                    assetID: asset.id,
                    md5Hash: "",
                    width: null,
                    height: null,
                    duration: null,
                  };
                } else {
                  return {
                    ...asset,
                    id: uuidv4(),
                    assetID: asset.id,
                    md5Hash: assetProperties.md5Hash,
                    width: assetProperties.width,
                    height: assetProperties.height,
                    duration: assetProperties.duration,
                  };
                }
              });
              const nextPosition = assets.length;
              createAssetsMutation.mutate(
                {
                  fieldID: field.id,
                  assets: newAssetsWithProperties.map((asset, index) => ({
                    id: asset.id,
                    assetID: asset.assetID,
                    name: asset.name,
                    path: asset.path,
                    size: asset.sizeBytes,
                    md5Hash: asset.md5Hash,
                    eTag: asset.eTag,
                    mimetype: asset.mimetype,
                    width: asset.width,
                    height: asset.height,
                    duration: asset.duration,
                    url: asset.signedUrl,
                    urlExpiresAt: asset.urlExpiresAt,
                    position: nextPosition + index,
                  })),
                },
                {
                  onError: (error) => {
                    handleTrpcError({ title: "Error Creating Asset", error });
                  },
                }
              );

              setUploadingAssets([]);
              const newAssets = [...assets, ...newAssetsWithProperties];
              const newValue = newAssets.map((asset) => asset.id).join(",");
              onChange(newValue);
              onUpdateAssets(newAssets);
            },
          }
        );
      });

      setUppyInstance(uppy);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppyInstance) {
        uppyInstance.destroy();
      }
    };
  }, [field.type, storagePath]);

  useEffect(() => {
    const handleFieldValueChanged = () => {
      setCurrentValue(field.value);
      if (field.type === "DateTime") {
        setDate(field.value ? new Date(field.value) : null);
        setTime(field.value ? new Date(field.value) : null);
      }
    };
    handleFieldValueChanged();
  }, [field.value]);

  useEffect(() => {
    if (field.type === "DateTime") {
      const hasTimeInput = field.validations?.format === "date-time";

      if (date && hasTimeInput) {
        if (time) {
          const newDate = new Date(date);
          newDate.setHours(time.getHours());
          newDate.setMinutes(time.getMinutes());
          onChange(newDate.toISOString());
        } else {
          setTimeAutofocus(true);
        }
      } else if (date) {
        onChange(date.toISOString());
      } else if (date !== field.value) {
        onChange(null);
      }
    }
  }, [date, time]);

  switch (field.type) {
    case "Email":
      return (
        <TextInputField
          fieldName={field.displayName}
          value={currentValue}
          onChange={setCurrentValue}
          onBlur={(value) => {
            onChange(value);
          }}
          isSubmitted={isSubmitted}
          setIsSubmitted={setIsSubmitted}
          checkIsValid={(value) => {
            if (!field.isRequired && (!value || value.length === 0)) {
              return {
                isValid: true,
              };
            }

            const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value || "");

            return {
              isValid,
              title: "Email is Invalid",
              description: "Please enter a valid email address",
            };
          }}
          width={300}
        />
      );
    case "Phone":
      return (
        <TextInputField
          fieldName={field.displayName}
          value={currentValue}
          onChange={setCurrentValue}
          onBlur={(value) => {
            const phoneNumber = parsePhoneNumber(value || "", "US");
            setCurrentValue(phoneNumber?.nationalNumber || null);
            onChange(phoneNumber?.nationalNumber || null);
          }}
          isSubmitted={isSubmitted}
          setIsSubmitted={setIsSubmitted}
          checkIsValid={(value) => {
            if (!field.isRequired && (!value || value.length === 0)) {
              return {
                isValid: true,
              };
            }

            const isValid = !!parsePhoneNumber(value || "", "US")?.isValid();

            return {
              isValid,
              title: "Invalid Phone Number",
              description: "Please enter a valid US phone number",
            };
          }}
          width={300}
        />
      );
    case "Number":
      return (
        <TextInputField
          fieldName={field.displayName}
          value={currentValue}
          onChange={setCurrentValue}
          onBlur={(value) => {
            onChange(value);
          }}
          isSubmitted={isSubmitted}
          setIsSubmitted={setIsSubmitted}
          checkIsValid={(value) => {
            if (!field.isRequired && (!value || value.length === 0)) {
              return {
                isValid: true,
              };
            }

            const validations = field.validations;
            const number = Number(value);
            if (validations) {
              const { minValue, maxValue, precision, allowNegative, format } =
                validations;
              if (minValue && number < minValue) {
                return {
                  isValid: false,
                  title: "Number is Too Low",
                  description: `Please enter a number greater than or equal to ${minValue}`,
                };
              } else if (allowNegative === false) {
                if (number < 0) {
                  return {
                    isValid: false,
                    title: "Negative Numbers Not Allowed",
                    description: "Please enter a positive number",
                  };
                }
              } else if (maxValue && number > maxValue) {
                return {
                  isValid: false,
                  title: "Number is Too High",
                  description: `Please enter a number less than or equal to ${maxValue}`,
                };
              } else if (
                format !== "integer" &&
                precision &&
                value &&
                value.split(".")[1]?.length > precision
              ) {
                return {
                  isValid: false,
                  title: "Invalid Precision",
                  description: `Please enter a number with at most ${precision} decimal places`,
                };
              }
            }

            return {
              isValid: true,
            };
          }}
          type="number"
          width={300}
        />
      );
    case "PlainText":
      const { singleLine, minLength, maxLength } = field.validations || {};

      if (singleLine === false) {
        const isTooShort = (currentValue?.length || 0) < (minLength || 0);
        const isTooLong = (currentValue?.length || 0) > (maxLength || Infinity);
        const isInvalid = isTooShort || isTooLong;

        return (
          <TextareaInput
            value={currentValue || ""}
            onChange={setCurrentValue}
            rows={3}
            placeholder="Enter a value"
            onBlur={() => {
              if (isTooShort) {
                toast.error("Text is Too Short", {
                  description: `Please enter a text with at least ${minLength} characters`,
                });
              } else if (isTooLong) {
                toast.error("Text is Too Long", {
                  description: `Please enter a text with at most ${maxLength} characters`,
                });
              } else {
                onChange(currentValue);
              }
            }}
            isInvalid={isInvalid}
          />
        );
      } else {
        const isSlugField = field.slug === "slug";

        return (
          <div>
            <TextInputField
              fieldName={field.displayName}
              value={currentValue}
              onChange={setCurrentValue}
              onBlur={(value) => {
                if (field.slug === "slug") {
                  const cleanedValue = kebabCase(value || "");
                  setCurrentValue(cleanedValue || "");
                  onChange(cleanedValue || "");
                } else {
                  setCurrentValue(value);
                  onChange(value);
                }
              }}
              isReadOnly={isReadOnly}
              isSubmitted={isSubmitted}
              setIsSubmitted={setIsSubmitted}
              reValidateMode="onBlur"
              checkIsValid={(value) => {
                if (!field.isRequired && (!value || value.length === 0)) {
                  return {
                    isValid: true,
                  };
                }

                if (field.isRequired && (!value || value.length === 0)) {
                  // Only throw an error if they have changed something
                  if (field.value != null && field.value !== "") {
                    return {
                      isValid: false,
                      title: "Text is Required",
                      description: "Please enter a value",
                    };
                  } else {
                    return {
                      isValid: null,
                    };
                  }
                } else if (maxLength && (value?.length || 0) > maxLength) {
                  return {
                    isValid: false,
                    title: "Text is Too Long",
                    description: `Please enter a text with at most ${maxLength} characters`,
                  };
                } else if (minLength && (value?.length || 0) < minLength) {
                  return {
                    isValid: false,
                    title: "Text is Too Short",
                    description: `Please enter a text with at least ${minLength} characters`,
                  };
                } else if (isSlugField) {
                  const cleanedValue = kebabCase(value || "");
                  if (maxLength && cleanedValue.length > maxLength) {
                    return {
                      isValid: false,
                      title: "Invalid Slug",
                      description: `Please enter a slug with at most ${maxLength} characters`,
                    };
                  }
                }

                return {
                  isValid: true,
                };
              }}
              width={500}
            />
            {isSlugField && !isReadOnly && (
              <div className="pt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    if (blogTitle == null || blogTitle.length === 0) {
                      return toast.error("Blog Title is Required", {
                        description:
                          "Please enter a blog title to convert to slug",
                      });
                    } else {
                      const slug = kebabCase(blogTitle).substring(0, 256);
                      setCurrentValue(slug);
                      onChange(slug);
                    }
                  }}
                >
                  <ArrowPathIcon className="h-4 w-4" />
                  Sync with Blog Title
                </Button>
              </div>
            )}
          </div>
        );
      }
    case "VideoLink":
    case "Link":
      return (
        <TextInputField
          fieldName={field.displayName}
          value={currentValue}
          onChange={setCurrentValue}
          onBlur={(value) => {
            onChange(value);
          }}
          isSubmitted={isSubmitted}
          setIsSubmitted={setIsSubmitted}
          reValidateMode="onBlur"
          checkIsValid={(url) => {
            if (!field.isRequired && (url == null || url.length === 0)) {
              return {
                isValid: true,
              };
            }

            assert(url != null, "URL is required");

            try {
              new URL(url.startsWith("http") ? url : `http://${url}`);

              return {
                isValid: true,
              };
            } catch (error) {
              return {
                isValid: false,
                title: "Link is Invalid",
                description: "Please enter a valid url",
              };
            }
          }}
          width={300}
        />
      );
    case "Option":
    case "Reference":
      const options = field.validations?.options || [];

      return (
        <Select
          value={currentValue}
          options={options.map((option) => ({
            label: option.name,
            value: option.id,
          }))}
          disabled={isReadOnly}
          isSearchable={options.length > 5}
          isClearable={!field.isRequired}
          onChange={(value) => {
            setCurrentValue(value);
            onChange(value);
          }}
        />
      );
    case "MultiReference":
      return (
        <Combobox
          value={currentValue?.split(",") || []}
          options={
            field.validations?.options.map((option) => ({
              key: option.id,
              label: option.name,
              value: option.id,
            })) || []
          }
          disabled={isReadOnly}
          placeholder={
            <div className="w-content bg-surface border-light-gray text-medium-gray hover:text-secondary hover:border-secondary inline-flex h-6 items-center justify-center gap-1.5 rounded-full border-[0.25px] px-2 whitespace-nowrap transition-colors">
              <span className="font-body-sm text-medium-dark-gray">
                Select options
              </span>
              <ChevronDownIcon className="h-4 w-4 text-inherit" />
            </div>
          }
          onChange={(value) => {
            setCurrentValue(value.join(","));
            onChange(value.join(","));
          }}
          isMulti={true}
        />
      );
    case "Switch":
      return (
        <Select
          value={currentValue}
          options={[
            { label: "True", value: "true" },
            { label: "False", value: "false" },
          ]}
          onChange={(value) => {
            setCurrentValue(value);
            onChange(value);
          }}
          disabled={isReadOnly}
        />
      );
    case "MultiImage":
      return (
        <div className="space-y-4">
          {assets != null && assets.length > 0 && (
            <Assets
              assets={assets}
              refetchAssets={() => {}}
              onDeleteClick={(asset) => {
                const newAssets = assets.filter((a) => a.id !== asset.id);
                onUpdateAssets(newAssets);

                deleteAssetMutation.mutate(
                  {
                    webflowAssetID: asset.id,
                  },
                  {
                    onError: (error) => {
                      handleTrpcError({ title: "Error Deleting Asset", error });
                    },
                  }
                );
              }}
              onDrop={(files) => {
                const validFiles = files.filter(
                  (file) => file.size <= 4 * 1024 * 1024
                );
                const invalidFiles = files.filter(
                  (file) => file.size > 4 * 1024 * 1024
                );

                if (invalidFiles.length > 0) {
                  toast.error("File(s) Too Large", {
                    description: `Please upload files smaller than 4MB: ${joinWithAnd(
                      invalidFiles.map((file) => file.name)
                    )}`,
                  });
                }

                if (uppyInstance) {
                  uppyInstance.addFiles(
                    validFiles.map((file) => ({
                      source: "File Input",
                      name: cleanFileName(file.name),
                      type: file.type,
                      data: file,
                    }))
                  );
                }
              }}
              acceptedFileTypes={{
                "image/png": [".png"],
                "image/jpg": [".jpg", ".jpeg"],
                "image/gif": [".gif"],
                "image/svg": [".svg"],
              }}
              onSortEnd={onSortEnd}
              isReadOnly={isReadOnly}
            />
          )}
          {!isReadOnly && (
            <FileDropzone
              onDrop={(files) => {
                const validFiles = files.filter(
                  (file) => file.size <= 4 * 1024 * 1024
                );
                const invalidFiles = files.filter(
                  (file) => file.size > 4 * 1024 * 1024
                );

                if (invalidFiles.length > 0) {
                  toast.error("File(s) Too Large", {
                    description: `Please upload files smaller than 4MB: ${joinWithAnd(
                      invalidFiles.map((file) => file.name)
                    )}`,
                  });
                }

                if (uppyInstance) {
                  uppyInstance.addFiles(
                    validFiles.map((file) => ({
                      source: "File Input",
                      name: cleanFileName(file.name),
                      type: file.type,
                      data: file,
                    }))
                  );
                }
              }}
              maxFiles={25 - (assets?.length || 0)}
              acceptedFileTypes={{
                "image/png": [".png"],
                "image/jpg": [".jpg", ".jpeg"],
                "image/gif": [".gif"],
                "image/svg": [".svg"],
              }}
              uploadProgress={
                uploadingAssets.length > 0
                  ? Math.round(
                      uploadingAssets.reduce(
                        (sum, asset) => sum + (asset.uploadProgress || 0),
                        0
                      ) / uploadingAssets.length
                    )
                  : undefined
              }
            />
          )}
        </div>
      );
    case "File":
    case "Image":
      const asset = assets != null ? assets[0] : null;

      const validations = field.validations;

      return (
        <div className="space-y-4">
          {asset && (
            <Asset
              asset={asset}
              refetchAssets={() => {}}
              onDeleteClick={(asset) => {
                const newAssets = assets.filter((a) => a.id !== asset.id);
                onUpdateAssets(newAssets);

                deleteAssetMutation.mutate(
                  {
                    webflowAssetID: asset.id,
                  },
                  {
                    onError: (error) => {
                      handleTrpcError({ title: "Error Deleting Asset", error });
                    },
                  }
                );
              }}
              isReadOnly={isReadOnly}
            />
          )}
          {!asset && !isReadOnly && (
            <FileDropzone
              onDrop={async (files) => {
                const file = files[0];

                if (files.length > 1) {
                  toast.error("Too Many Files", {
                    description: "Please upload only one file",
                  });
                }

                if (validations) {
                  const {
                    minImageSize,
                    maxImageSize,
                    minImageHeight,
                    maxImageHeight,
                    minImageWidth,
                    maxImageWidth,
                  } = validations;

                  if (file.size < (minImageSize || 0)) {
                    return toast.error("File Size Too Small", {
                      description: `Please upload a file larger than ${minImageSize} bytes`,
                    });
                  } else if (file.size > (maxImageSize || 4 * 1024 * 1024)) {
                    return toast.error("File Size Too Large", {
                      description: `Please upload a file smaller than ${maxImageSize} bytes`,
                    });
                  }

                  const { width, height } = await getImageProperties(file);

                  if (width < (minImageWidth || 0)) {
                    return toast.error("Image Too Narrow", {
                      description: `Please upload an image wider than ${minImageWidth} pixels`,
                    });
                  } else if (width > (maxImageWidth || Infinity)) {
                    return toast.error("Image Too Wide", {
                      description: `Please upload an image narrower than ${maxImageWidth} pixels`,
                    });
                  } else if (height < (minImageHeight || 0)) {
                    return toast.error("Image Too Short", {
                      description: `Please upload an image taller than ${minImageHeight} pixels`,
                    });
                  } else if (height > (maxImageHeight || Infinity)) {
                    return toast.error("Image Too Tall", {
                      description: `Please upload an image shorter than ${maxImageHeight} pixels`,
                    });
                  }
                }

                if (file.size > 4 * 1024 * 1024) {
                  return toast.error("File Too Large", {
                    description: "Please upload a file smaller than 4MB",
                  });
                }

                if (uppyInstance) {
                  uppyInstance.addFile({
                    source: "File Input",
                    name: cleanFileName(file.name),
                    type: file.type,
                    data: file,
                  });
                }
              }}
              acceptedFileTypes={
                field.type === "File"
                  ? {
                      "image/png": [".png"],
                      "image/jpg": [".jpg", ".jpeg"],
                      "image/gif": [".gif"],
                      "image/svg": [".svg"],
                      "application/pdf": [".pdf"],
                    }
                  : {
                      "image/png": [".png"],
                      "image/jpg": [".jpg", ".jpeg"],
                      "image/gif": [".gif"],
                      "image/svg": [".svg"],
                    }
              }
              uploadProgress={
                uploadingAssets.length > 0
                  ? Math.round(
                      uploadingAssets.reduce(
                        (sum, asset) => sum + (asset.uploadProgress || 0),
                        0
                      ) / uploadingAssets.length
                    )
                  : undefined
              }
            />
          )}
        </div>
      );
    case "Color":
      const isValidHexColor = /^#[0-9A-F]{6}$/i.test(currentValue || "");

      return (
        <div className="relative w-32">
          <TextInput
            value={currentValue || ""}
            placeholder="#000000"
            isReadOnly={isReadOnly}
            isInvalid={(currentValue?.length || 0) >= 7 && !isValidHexColor}
            onChange={(value) => {
              if (value.startsWith("#")) {
                setCurrentValue(value);
              } else if (value.length > 0) {
                setCurrentValue(`#${value}`);
              } else {
                setCurrentValue(null);
              }
            }}
            onBlur={() => {
              if (isValidHexColor) {
                setCurrentValue(currentValue);
                onChange(currentValue);
              } else {
                toast.error("Invalid Color", {
                  description: "Please enter a valid hex color",
                });
              }
            }}
            kind="outline"
          />
          <div
            className={classNames(
              "absolute top-1.5 right-1.5 rounded-md transition-opacity",
              {
                "opacity-0": !currentValue || !isValidHexColor,
                "opacity-100": isValidHexColor,
              }
            )}
            style={{
              width: 20,
              height: 20,
              backgroundColor: currentValue || "#000000",
            }}
          />
        </div>
      );
    case "DateTime":
      const hasTimeInput = field.validations?.format === "date-time";
      const isRequired = field.isRequired;

      return (
        <div className="flex w-fit items-center gap-4">
          <div className="w-fit max-w-[70px]">
            <DatePicker
              value={date}
              onChange={(newDate: Date | null) => {
                if (isRequired && !newDate) {
                  return toast.error("Date is Required", {
                    description: "Please enter a date",
                  });
                }

                if (newDate == null) {
                  setDate(null);
                  setTime(null);
                  return;
                } else {
                  if (hasTimeInput) {
                    setDate(newDate);
                  } else {
                    const utcDate = getDateUTC(newDate);
                    setDate(utcDate);
                  }
                }
              }}
              isDisabled={isReadOnly}
              isClearable={!isRequired}
              showBorder={true}
            />
          </div>
          {hasTimeInput && (
            <div className="w-24">
              <TimeInput
                value={time}
                onChange={(newTime) => {
                  if (newTime) {
                    setTime(newTime);
                  } else {
                    if (isRequired) {
                      toast.error("Time is Required", {
                        description: "Please enter a time",
                      });
                    } else {
                      setTime(null);
                    }
                  }
                }}
                disabled={isReadOnly}
                autofocus={timeAutofocus}
                showBorder={true}
              />
            </div>
          )}
        </div>
      );
  }
  return null;
};

export default WebflowFieldInput;
