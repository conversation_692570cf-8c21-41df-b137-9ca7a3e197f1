import { ReactNode } from "react";

import { InformationCircleIcon } from "@heroicons/react/24/outline";

import Tooltip from "./Tooltip";

type Props = {
  value: ReactNode | string;
};

const HelpTooltip = ({ value }: Props) => {
  return (
    <Tooltip value={value} className="p-3">
      <InformationCircleIcon className="text-medium-gray hover:text-secondary h-3.5 w-3.5 transition-colors" />
    </Tooltip>
  );
};

export default HelpTooltip;
