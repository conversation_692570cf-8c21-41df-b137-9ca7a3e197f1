import { CheckIcon, MinusIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { IconButton, TextInput } from "~/components";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/SimpleTable";
import Button from "~/components/ui/Button";
import Popover from "~/components/ui/Popover";
import { handleTrpcError, trpc } from "~/utils";

type Props = {
  companyID: string;
  workspace: {
    id: string;
    subscription: {
      seatLimit: number;
      aiChatLimit: number;
      scheduledPostLimit: number;
      socialProfileLimit: number;
      engagementProfileLimit: number;

      isAnalyticsAllTimeEnabled: boolean;
      isAutoEngagementEnabled: boolean;
      isBlogEnabled: boolean;
      isTwitterAnalyticsEnabled: boolean;
    } | null;
  };
};

const WorkspaceLimitsTable = ({ companyID, workspace }: Props) => {
  const trpcUtils = trpc.useUtils();
  const updateWorkspaceTwitterAnalyticsMutation =
    trpc.admin.company.updateWorkspaceTwitterAnalytics.useMutation();

  const subscription = workspace.subscription || {
    seatLimit: "∞",
    aiChatLimit: "∞",
    scheduledPostLimit: "∞",
    socialProfileLimit: "∞",
    engagementProfileLimit: "∞",

    isAnalyticsAllTimeEnabled: true,
    isAutoEngagementEnabled: true,
    isBlogEnabled: true,
    isTwitterAnalyticsEnabled: false,
  };

  const {
    socialProfileLimit,
    seatLimit,
    aiChatLimit,
    scheduledPostLimit,
    engagementProfileLimit,
    isAnalyticsAllTimeEnabled,
    isAutoEngagementEnabled,
    isBlogEnabled,
    isTwitterAnalyticsEnabled,
  } = subscription;

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Type</TableHead>
          <TableHead>Value</TableHead>
          <TableHead>Options</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          <TableCell className="font-medium">Seats</TableCell>
          <TableCell>{seatLimit}</TableCell>
          <TableCell>
            <AddProductPopover
              companyID={companyID}
              workspaceID={workspace.id}
              productName="Seat"
              lookupKey="free_seat"
              defaultQuantity={1}
            />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">AI Chats</TableCell>
          <TableCell>{aiChatLimit}</TableCell>
          <TableCell>
            <AddProductPopover
              companyID={companyID}
              workspaceID={workspace.id}
              productName="AI Chats"
              lookupKey="free_ai_chat"
              defaultQuantity={10}
            />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">Scheduled Posts</TableCell>
          <TableCell>{scheduledPostLimit}</TableCell>
          <TableCell>
            <AddProductPopover
              companyID={companyID}
              workspaceID={workspace.id}
              productName="Scheduled Posts"
              lookupKey="free_scheduled_post"
              defaultQuantity={10}
            />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">Social Profile Limit</TableCell>
          <TableCell>{socialProfileLimit}</TableCell>
          <TableCell>
            <AddProductPopover
              companyID={companyID}
              workspaceID={workspace.id}
              productName="Social Profile"
              lookupKey="free_social_profile"
              defaultQuantity={1}
            />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">
            Engagement Profile Limit
          </TableCell>
          <TableCell>{engagementProfileLimit}</TableCell>
          <TableCell>
            <MinusIcon className="text-medium-gray h-4 w-4" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">Twitter Analytics</TableCell>
          <TableCell>
            {isTwitterAnalyticsEnabled ? (
              <CheckIcon className="text-basic h-4 w-4" />
            ) : (
              <MinusIcon className="text-medium-gray h-4 w-4" />
            )}
          </TableCell>
          <TableCell>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                updateWorkspaceTwitterAnalyticsMutation.mutate(
                  {
                    workspaceID: workspace.id,
                    isEnabled: !isTwitterAnalyticsEnabled,
                  },
                  {
                    onSuccess: () => {
                      trpcUtils.admin.company.get.refetch({
                        id: companyID,
                      });
                    },
                    onError: (error) => {
                      handleTrpcError({
                        title: "Failed to Update Twitter Analytics",
                        error,
                      });
                    },
                  }
                );
              }}
            >
              {isTwitterAnalyticsEnabled ? "Disable" : "Enable"}
            </Button>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">Analytics Length</TableCell>
          <TableCell>
            {isAnalyticsAllTimeEnabled ? "All Time" : "14 Days"}
          </TableCell>
          <TableCell>
            <MinusIcon className="text-medium-gray h-4 w-4" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">Auto Engagement</TableCell>
          <TableCell>
            {isAutoEngagementEnabled ? (
              <CheckIcon className="text-basic h-4 w-4" />
            ) : (
              <MinusIcon className="text-medium-gray h-4 w-4" />
            )}
          </TableCell>
          <TableCell>
            <MinusIcon className="text-medium-gray h-4 w-4" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">Blog Post Scheduling</TableCell>
          <TableCell>
            {isBlogEnabled ? (
              <CheckIcon className="text-basic h-4 w-4" />
            ) : (
              <MinusIcon className="text-medium-gray h-4 w-4" />
            )}
          </TableCell>
          <TableCell>
            <MinusIcon className="text-medium-gray h-4 w-4" />
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );
};

const AddProductPopover = ({
  companyID,
  workspaceID,
  productName,
  lookupKey,
  defaultQuantity,
}: {
  companyID: string;
  workspaceID: string;
  productName: string;
  lookupKey:
    | "free_ai_chat"
    | "free_seat"
    | "free_scheduled_post"
    | "free_social_profile";
  defaultQuantity: number;
}) => {
  const [quantity, setQuantity] = useState<string>(defaultQuantity.toString());

  const trpcUtils = trpc.useUtils();
  const updateSubscriptionMutation =
    trpc.admin.company.updateSubscription.useMutation();

  return (
    <Popover.Root>
      <Popover.Trigger>
        <IconButton icon="PlusIcon" size="sm" onClick={() => {}} />
      </Popover.Trigger>
      <Popover.Content className="flex items-center gap-2">
        <TextInput type="number" value={quantity} onChange={setQuantity} />
        <Button
          variant="secondary"
          size="sm"
          isLoading={
            updateSubscriptionMutation.isPending &&
            updateSubscriptionMutation.variables?.product.lookupKey ===
              lookupKey
          }
          onClick={() => {
            updateSubscriptionMutation.mutate(
              {
                workspaceID,
                product: {
                  lookupKey,
                  quantity: parseInt(quantity),
                },
              },
              {
                onSuccess: () => {
                  trpcUtils.admin.company.get.refetch({
                    id: companyID,
                  });
                },
                onError: (error) => {
                  handleTrpcError({
                    title: `Failed to Update ${productName}`,
                    error,
                  });
                },
              }
            );
          }}
        >
          Add {productName}
        </Button>
      </Popover.Content>
    </Popover.Root>
  );
};

export default WorkspaceLimitsTable;
