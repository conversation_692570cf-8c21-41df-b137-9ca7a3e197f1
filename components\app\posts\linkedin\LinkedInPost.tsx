import { useEffect, useState } from "react";

import { v4 as uuidv4 } from "uuid";

import { ScheduledPost } from "../SchedulePostAccountSelect";

import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { Channel } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import { arrayMoveImmutable } from "array-move";
import assert from "assert";
import classNames from "classnames";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import posthog from "posthog-js";
import { toast } from "sonner";
import supabasePublic from "~/clients/supabasePublic";
import { AutosizeInput, HelpTooltip, Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import Tabs from "~/components/ui/Tabs";
import {
  MAX_VIDEO_UPLOAD_SIZE_MB,
  ONE_YEAR_IN_SECONDS,
  linkedinMentionRegex,
} from "~/constants";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import tusOptions from "~/constants/tusOptions";
import { useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  charCount,
  findAsync,
  formatPercentage,
  getImageProperties,
  getVideoProperties,
  handleFilesUpload,
  handleTrpcError,
  openIntercomArticle,
  openIntercomChat,
  trpc,
} from "~/utils";
import { EditImageModalProperties } from "~/utils/EditImageModal";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import compressImage from "~/utils/compressImage";
import getAssetsWithProperties from "~/utils/getAssetsWithProperties";
import { getDefaultAccount } from "~/utils/getDefaultAccount";
import getFileProperties from "~/utils/getFileProperties";
import createRoomID from "~/utils/liveblocks/createRoomID";
import SelectModal from "../../SelectModal";
import Assets, { UploadingAsset } from "../../files/Assets";
import LinkedInEditorWrapper from "../../tiptap/LinkedInEditor";
import CoverPhotoModal from "../CoverPhotoModal";
import EditorActionsBar from "../EditorActionsBar";
import PostEngagements from "../PostEngagements";
import { PostOption, PostOptions } from "../PostOptions";
import PostPerformance from "../PostPerformance";
import PostPreviewContainer from "../PostPreviewContainer";
import LinkedInPostPreview from "./LinkedInPostPreview";

export type LinkedInIntegration = {
  id: string;
  account: {
    name: string;
    profileImageUrl: string;
  };
};

export type LinkedInContent = {
  id: string;
  editorState: JSONContent | null;
  copy: string;
  documentTitle: string | null;
};

export type LinkedInAssetsType = {
  assets: AssetType[];
  coverPhoto: AssetType | null;
  captions: AssetType | null;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    scheduledPosts: ScheduledPost[];
    linkedInAssets: LinkedInAssetsType;
    linkedInContent: LinkedInContent | null;
    linkedInIntegration: LinkedInIntegration | null;
    linkedInPost: {
      id: string;
      publishedAt: Date | null;
      stats: {
        likeCount: number;
        commentCount: number;
        shareCount: number;
        impressionCount: number | null;
        impressionStatRecordedAt: Date | null;
      };
    } | null;
    linkedInLink: string | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const LinkedInPost = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const router = useRouter();

  const { currentWorkspace } = useWorkspace();
  const trpcUtils = trpc.useUtils();
  const [isRoomReady, setIsRoomReady] = useState<boolean>(false);

  useEffect(() => {
    if (post.linkedInContent && !isSyncingComplete) {
      setIsRoomReady(true);
    }
  }, [post.linkedInContent, isSyncingComplete]);

  const [draggedOver, setDraggedOver] = useState<boolean>(false);
  const [likeSelectModalOpen, setLikeSelectModalOpen] =
    useState<boolean>(false);
  const [integration, setIntegration] = useState<LinkedInIntegration | null>(
    post.linkedInIntegration || null
  );
  const [linkedInContent, setLinkedInContent] = useState<LinkedInContent>(
    post.linkedInContent || {
      id: uuidv4(),
      editorState: null,
      copy: "",
      documentTitle: "",
    }
  );
  const [assets, setAssets] = useState<AssetType[]>(post.linkedInAssets.assets);
  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);
  const [coverPhotoModalOpen, setCoverPhotoModalOpen] =
    useState<boolean>(false);
  const [coverPhoto, setCoverPhoto] = useState<AssetType | null>(
    post.linkedInAssets.coverPhoto
  );
  const [captions, setCaptions] = useState<AssetType | null>(
    post.linkedInAssets.captions
  );
  const [selectedTab, setSelectedTab] = useState<
    "post_content" | "post_options" | "auto_engagemnents"
  >("post_content");

  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);

  const isPDF = assets?.[0]?.metadata.mimetype === "application/pdf";
  const isVideo = assets?.[0]?.metadata.mimetype.includes("video");

  const updatePostMutation = trpc.post.update.useMutation();
  const engagementsQuery = trpc.linkedInContent.getEngagements.useQuery({
    postID: post.id,
  });
  const engagements = engagementsQuery.data?.engagements || [];
  const createEngagementMutation =
    trpc.linkedInContent.createEngagement.useMutation();
  const updateEngagementMutation =
    trpc.linkedInContent.updateEngagement.useMutation();
  const deleteEngagementsMutation =
    trpc.linkedInContent.deleteEngagements.useMutation();
  const upsertEngagementLikesMutation =
    trpc.linkedInContent.upsertEngagementLikes.useMutation();
  const updateAssetMutation = trpc.linkedInContent.updateAsset.useMutation();

  const linkedInContentQuery = trpc.linkedInContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const linkedInContentData = linkedInContentQuery.data;
  useEffect(() => {
    const setCrossPostData = () => {
      if (linkedInContentData?.linkedInContent != null) {
        setLinkedInContent(linkedInContentData.linkedInContent);
        setAssets(linkedInContentData.assets);
        setCoverPhoto(linkedInContentData.coverPhoto);
        setCaptions(linkedInContentData.captions);
        setIsRoomReady(true);
        clearIsSyncing();
      }
    };

    setCrossPostData();
  }, [linkedInContentData]);

  const upsertLinkedInContentMutation = trpc.linkedInContent.upsert.useMutation(
    {
      onSuccess: () => {
        trpcUtils.post.get.invalidate({
          id: post.id,
        });
      },
    }
  );

  const createAssetsMutation = trpc.linkedInContent.createAssets.useMutation();
  const deleteAssetMutation = trpc.linkedInContent.deleteAsset.useMutation();
  const updateAssetPositionMutation =
    trpc.linkedInContent.updateAssetPositions.useMutation();
  const upsertCaptionsMutation =
    trpc.linkedInContent.upsertCaptions.useMutation();
  const deleteCaptionsMutation =
    trpc.linkedInContent.deleteCaptions.useMutation();
  const upsertCoverPhotoMutation =
    trpc.linkedInContent.upsertCoverPhoto.useMutation();
  const deleteCoverPhotoMutation =
    trpc.linkedInContent.deleteCoverPhoto.useMutation();

  const onSortEnd = (oldIndex: number, newIndex: number) => {
    setAssets((assets) => {
      const newAssets = arrayMoveImmutable(assets, oldIndex, newIndex);

      updateAssetPositionMutation.mutate({
        assets: newAssets.map((asset, index) => ({
          id: asset.id,
          position: index,
        })),
      });

      return newAssets;
    });
  };

  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const compressVideoMutation = trpc.video.compress.useMutation();
  const maybeCompressAssets = async (
    assets: {
      path: string;
      sizeBytes: number;
      sizeMB: number;
      mimetype: string;
      signedUrl: string;
      duration?: number | null;
      width?: number | null;
      height?: number | null;
    }[]
  ) => {
    const videoPromises = assets.map(async (asset) => {
      if (!asset || !asset.mimetype.startsWith("video/")) {
        return Promise.resolve(null);
      } else {
        let duration = asset.duration;
        let width = asset.width;
        let height = asset.height;
        if (width && height && duration) {
          return asset as typeof asset & {
            duration: number;
            width: number;
            height: number;
          };
        } else {
          const properties = await getVideoProperties(asset.signedUrl);
          return {
            ...asset,
            duration: properties.duration,
            width: properties.width,
            height: properties.height,
          };
        }
      }
    });
    const videosWithProperties = await Promise.all(videoPromises);

    const videoToCompress = await findAsync(
      videosWithProperties,
      async (asset) => {
        if (!asset || !asset.mimetype.startsWith("video/")) {
          return false;
        }

        return asset.sizeMB > 125;
      }
    );

    if (videoToCompress != null) {
      const { width, height } = videoToCompress;

      compressVideoMutation.mutate(
        {
          video: {
            path: videoToCompress.path,
            resolution: {
              width,
              height,
            },
            duration: videoToCompress.duration,
            sizeBytes: videoToCompress.sizeBytes,
          },
          channel: "Instagram",
        },
        {
          onError: (error) => {
            handleTrpcError({ title: "Error Compressing Video", error });
          },
          onSuccess: (data) => {
            if (data.jobID != null) {
              toast.info("Optimizing Attachment", {
                description:
                  "We're automatically optimizing your attachment (files >125MB are not accepted on LinkedIn).",
              });
            }
          },
        }
      );
    }
  };

  const connectedIntegrationsQuery =
    trpc.integration.linkedin.getWorkspaceIntegrations.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });
  const integrationsQuery = trpc.post.getIntegrations.useQuery({
    id: post.id,
  });
  const postAccounts = integrationsQuery.data?.accounts ?? [];

  const connectedIntegrationsData = connectedIntegrationsQuery.data;
  const connectedIntegrations = connectedIntegrationsData?.integrations || [];
  const schedulingIntegrations =
    connectedIntegrations.filter(
      (integration) => integration.type === "Scheduling"
    ) || [];
  const engagementIntegrations =
    schedulingIntegrations.length > 0
      ? [
          ...schedulingIntegrations,
          ...(connectedIntegrations.filter(
            (integration) => integration.type === "Engagement"
          ) || []),
        ]
      : [];

  useEffect(() => {
    const setInitialIntegration = () => {
      if (connectedIntegrationsData != null) {
        if (integration == null && schedulingIntegrations.length) {
          const defaultIntegration = getDefaultAccount({
            mainAccount: postAccounts[0],
            accounts: schedulingIntegrations.map((integration) => ({
              id: integration.id,
              account: {
                name: integration.account.name,
                profileImageUrl: integration.account.profileImageUrl,
              },
            })),
          });
          setIntegration(defaultIntegration);
          updateIntegration(defaultIntegration.id);
        }
      }
    };

    setInitialIntegration();
  }, [connectedIntegrationsData]);

  const integrationOptions =
    schedulingIntegrations.map((integration) => ({
      id: integration.id,
      account: {
        name: integration.account.name,
        profileImageUrl: integration.account.profileImageUrl,
      },
    })) || (post.linkedInIntegration ? [post.linkedInIntegration] : []);

  const updateIntegration = (integrationID: string | null) => {
    const newIntegration = integrationOptions.find(
      (workspaceIntegration) => workspaceIntegration.id === integrationID
    );

    if (newIntegration) {
      setIntegration(newIntegration);
      trpcUtils.post.getIntegrations.invalidate();
      updatePostMutation.mutate(
        {
          id: post.id,
          linkedInIntegrationID: newIntegration.id,
        },
        {
          onSuccess: () => {
            trpcUtils.post.getIntegrations.refetch({
              id: post.id,
            });
          },
        }
      );
    }
  };

  const numSetOptions = [
    isVideo && coverPhoto != null,
    isVideo && captions != null,
    isPDF &&
      linkedInContent.documentTitle != null &&
      linkedInContent.documentTitle !== "",
  ].filter((value) => !!value).length;

  const getValidFiles = async (files: File[]) => {
    let currentFiles = [...files];
    const validFiles: File[] = [];

    const hasPDF = files.some((file) => file.type === "application/pdf");
    const hasVideo = files.some((file) => file.type.startsWith("video/"));

    const currentAssetIsPDFVideo = assets.some(
      (asset) =>
        asset.metadata.mimetype === "application/pdf" ||
        asset.metadata.mimetype.startsWith("video/")
    );

    const uploadingAssetIsPDFVideo = uploadingAssets.some(
      (asset) =>
        asset.type.startsWith("video/") || asset.type === "application/pdf"
    );

    const currentAssetIsImage = assets.some(
      (asset) =>
        asset.metadata.mimetype.startsWith("image/") &&
        asset.metadata.mimetype !== "image/gif"
    );

    if (currentAssetIsPDFVideo || uploadingAssetIsPDFVideo) {
      toast.error("Too Many Attachments", {
        description:
          "You can add up to a total of 20 images or GIFs, 1 video, or 1 PDF per post",
      });

      return [];
    }

    if (currentAssetIsImage) {
      currentFiles = files
        .filter((file) => file.type.startsWith("image/"))
        .slice(0, 20 - assets.length);
      if (currentFiles.length !== files.length) {
        toast.error("Too Many Attachments", {
          description:
            "You can add up to a total of 20 images or GIFs, 1 video, or 1 PDF per post",
        });

        if (currentFiles.length === 0) {
          return [];
        }
      }
    }

    if (files.length > 1 && (hasPDF || hasVideo)) {
      toast.error("Too Many Attachments", {
        description:
          "You can add up to a total of 20 images or GIFs, 1 video, or 1 PDF per post",
      });

      currentFiles = [files[0]];
    }

    const assetNames = assets.map((asset) => asset.name);

    for await (const file of currentFiles) {
      const fileType = file.type;
      const fileSizeMB = file.size / 1024 / 1024;

      const fileIsGif = fileType === "image/gif";
      const fileIsVideo = fileType.startsWith("video/");
      const fileIsPDF = fileType === "application/pdf";

      if (assetNames.includes(file.name)) {
        toast.error(`Duplicate Attachment: "${file.name}"`, {
          description: "Each attachment must have a unique name",
        });
        continue;
      }

      if (["image/jpeg", "image/png"].includes(fileType)) {
        const { pixels } = await getImageProperties(file);

        // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/multiimage-post-api?view=li-lms-2023-02&tabs=http#multiimage
        if (pixels >= 36_152_320) {
          toast.error("Image is Too Large", {
            description: "An image can have a maximum of 36,152,319 pixels",
          });
          continue;
        }

        if (fileSizeMB > 5) {
          const compressedFile = await compressImage(file, {
            maxSizeMB: 5,
          });
          toast.info("Attachment Optimized", {
            description: `${file.name} was optimized (files >5MB are not accepted on LinkedIn)`,
          });
          posthog.capture(POSTHOG_EVENTS.image.compressed, {
            originalSizeMB: file.size / 1024 / 1024,
            compressedSizeKB: compressedFile.size / 1024 / 1024,
            channel: "LinkedIn",
          });
          validFiles.push(compressedFile);
        } else {
          validFiles.push(file);
        }
      } else if (fileIsGif) {
        if (fileSizeMB > 15) {
          toast.error("GIF Size is Too Big", {
            description: "GIF size must be smaller than 15MB",
          });
          continue;
        }

        validFiles.push(file);
      } else if (fileIsVideo) {
        const { duration, width, height } = await getVideoProperties(file);

        if (fileSizeMB <= 0.075) {
          toast.error("Video Size is Too Small", {
            description: "Video size must be at least 75KB",
          });
          continue;
        } else if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
          toast.error("Video is too large", {
            description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
            action: {
              label: "Contact Support",
              onClick: () => {
                openIntercomChat(
                  `I need help compressing a ${fileSizeMB.toFixed(1)} MB video.`
                );
              },
            },
          });
          continue;
        } else if (duration < 3) {
          toast.error("Video is too short", {
            description: "Minimum video length is 3 seconds",
          });
          continue;
        } else if (duration > 30 * 60) {
          toast.error("Video is too long", {
            description: "Maximum video length is 30 minutes",
          });
          continue;
        }

        validFiles.push(file);
      } else if (fileIsPDF) {
        if (fileSizeMB > 100) {
          toast.error("PDF Size is Too Big", {
            description: "PDF size must be smaller than 100MB",
          });
          continue;
        }

        validFiles.push(file);
      }
    }

    return validFiles;
  };

  const [uppy, setUppy] = useState<Uppy | null>(null);

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `linkedin-${post.id}`,
        restrictions: { maxNumberOfFiles: 20 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", (file) => {
        assert(currentWorkspace);

        setUploadingAssets((uploadingAssets) => [
          ...uploadingAssets,
          {
            name: file.name!,
            size: file.size!,
            type: file.type,
            uploadProgress: 0,
            updateAssetID: file.meta.updateAssetID as string | undefined,
          },
        ]);

        const objectName = `${currentWorkspace.id}/${post.id}/linkedin/${linkedInContent.id}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (uppyData) => {
        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["LinkedIn"].slug
        }/${linkedInContent.id}`;
        const fileNames = uppyData.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          uppyData.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              if (uppyData.successful![0].meta.updateAssetID) {
                const updateAssetID = uppyData.successful![0].meta
                  .updateAssetID as string;
                const newAsset = newAssetsWithProperties[0];

                await updateAssetMutation.mutateAsync({
                  linkedInAssetID: updateAssetID,
                  newAsset: {
                    id: newAsset.id,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                });

                setAssets((prevAssets) =>
                  prevAssets.map((asset) =>
                    asset.id === updateAssetID
                      ? {
                          id: asset.id,
                          name: newAsset.name,
                          path: newAsset.path,
                          sizeBytes: newAsset.sizeBytes,
                          eTag: newAsset.eTag,
                          md5Hash: newAsset.md5Hash,
                          mimetype: newAsset.mimetype,
                          width: newAsset.width,
                          height: newAsset.height,
                          metadata: newAsset.metadata,
                          url: newAsset.signedUrl,
                          sizeMB: newAsset.sizeMB,
                          sizeKB: newAsset.sizeKB,
                          createdAt: newAsset.createdAt,
                          updatedAt: newAsset.updatedAt,
                          duration: newAsset.duration,
                          signedUrl: newAsset.signedUrl,
                          urlExpiresAt: newAsset.urlExpiresAt,
                        }
                      : asset
                  )
                );
                setUploadingAssets([]);
              } else {
                const nextPosition = assets.length;

                createAssetsMutation.mutate(
                  {
                    id: linkedInContent.id,
                    assets: newAssetsWithProperties.map((asset, index) => ({
                      id: asset.id,
                      assetID: asset.assetID,
                      name: asset.name,
                      path: asset.path,
                      size: asset.sizeBytes,
                      eTag: asset.eTag,
                      md5Hash: asset.md5Hash,
                      mimetype: asset.mimetype,
                      width: asset.width,
                      height: asset.height,
                      duration: asset.duration,
                      url: asset.signedUrl,
                      urlExpiresAt: asset.urlExpiresAt,
                      position: nextPosition + index,
                    })),
                  },
                  {
                    onSuccess: async () => {
                      await maybeCompressAssets(newAssetsWithProperties);
                    },
                    onError: (error) => {
                      handleTrpcError({ title: "Error Creating Asset", error });
                    },
                  }
                );

                setUploadingAssets([]);
                setAssets((assets) => [...assets, ...newAssetsWithProperties]);
              }
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace, linkedInContent.id]);

  const linkedInPost = post.linkedInPost;

  return (
    <div
      onDragOverCapture={() => {
        if (!coverPhotoModalOpen && selectedTab === "post_content") {
          setDraggedOver(true);
        }
      }}
      onDragLeaveCapture={() => {
        setDraggedOver(false);
      }}
    >
      <PostPerformance
        channel="LinkedIn"
        url={post.linkedInLink}
        stats={
          linkedInPost && [
            {
              label: "Likes",
              value: linkedInPost.stats.likeCount,
            },
            {
              label: "Comments",
              value: linkedInPost.stats.commentCount,
            },
            {
              label: "Shares",
              value: linkedInPost.stats.shareCount,
            },
            {
              label: "Impressions",
              value: (
                <div className="flex items-center gap-1">
                  {linkedInPost.stats.impressionCount}
                  {linkedInPost.stats.impressionStatRecordedAt &&
                    linkedInPost.publishedAt &&
                    DateTime.fromJSDate(linkedInPost.publishedAt) >
                      DateTime.now().minus({ month: 1 }) &&
                    DateTime.fromJSDate(
                      linkedInPost.stats.impressionStatRecordedAt
                    ) < DateTime.now().minus({ days: 7 }) && (
                      <Tooltip
                        value={
                          <div className="space-y-1">
                            <div>
                              This impressions / engagement data was last
                              updated on{" "}
                              <span className="text-secondary">
                                {DateTime.fromJSDate(
                                  linkedInPost.stats.impressionStatRecordedAt
                                ).toFormat("M/d/yy")}
                              </span>
                              .
                              <br />
                              <br />
                              Since this is a recent post that is more than a
                              week out of date, we recommend rerunning the
                              bookmarklet to update this stat.
                            </div>
                            <Button
                              variant="secondary"
                              size="sm"
                              onClick={() => {
                                router.push(
                                  "/settings/linkedin#creator-profile-analytics"
                                );
                              }}
                            >
                              Bookmarklet Instructions
                            </Button>
                          </div>
                        }
                      >
                        <ExclamationTriangleIcon className="text-medium-dark-gray h-4 w-4" />
                      </Tooltip>
                    )}
                </div>
              ),
            },

            {
              label: "Engagement",
              value:
                linkedInPost.stats.impressionCount != null &&
                linkedInPost.stats.impressionCount > 0
                  ? formatPercentage(
                      linkedInPost.stats.likeCount +
                        linkedInPost.stats.commentCount +
                        linkedInPost.stats.shareCount,
                      linkedInPost.stats.impressionCount
                    )
                  : null,
            },
          ]
        }
      />
      <PostPreviewContainer
        channel="LinkedIn"
        post={post}
        connectedAccount={
          integration
            ? {
                integrationID: integration.id,
                name: integration.account.name,
              }
            : null
        }
        accountOptions={schedulingIntegrations.map((integration) => ({
          integrationID: integration.id,
          name: integration.account.name,
        }))}
        setConnectedAccountID={(id) => {
          updateIntegration(id);
        }}
        Preview={
          <LinkedInPostPreview
            linkedInContent={{ ...linkedInContent, assets }}
            coverPhotoUrl={coverPhoto?.signedUrl}
            captionsUrl={captions?.signedUrl}
            connectedAccount={integration}
          />
        }
        isReadOnly={isReadOnly}
      />
      <Tabs.Root defaultValue="post_content">
        <Tabs.List>
          <Tabs.Trigger value="post_content">Post Content</Tabs.Trigger>
          <Tabs.Trigger value="post_options">
            {numSetOptions > 0
              ? `Post Options (${numSetOptions})`
              : "Post Options"}
          </Tabs.Trigger>

          <Tabs.Trigger value="auto_engagements">
            {engagements.length
              ? `Auto Engagement (${
                  engagements.filter((engagement) => engagement.type !== "Like")
                    .length +
                  (engagements.find((engagement) => engagement.type === "Like")
                    ? 1
                    : 0)
                })`
              : "Auto Engagement"}
          </Tabs.Trigger>
        </Tabs.List>
        <Tabs.Content value="post_content">
          <div>
            <div className="group">
              {isRoomReady && (
                <LinkedInEditorWrapper
                  key={linkedInContent.id}
                  postID={post.id}
                  initialValue={linkedInContent.editorState}
                  placeholder="Add copy and upload media for the post here"
                  isReadOnly={isReadOnly}
                  onImagePaste={(event) => {
                    if (uppy) {
                      handleFilesUpload({
                        items: event.clipboardData.items,
                        uppy,
                        meta: {
                          contentID: linkedInContent.id,
                        },
                        getValidFiles,
                      });
                    }
                  }}
                  onSave={(editorState) => {
                    setLinkedInContent((linkedInContent) => {
                      assert(linkedInContent);
                      return {
                        ...linkedInContent,
                        editorState,
                      };
                    });

                    upsertLinkedInContentMutation.mutate({
                      id: linkedInContent.id,
                      copy: linkedInContent.copy,
                      editorState: editorState,
                      documentTitle: linkedInContent.documentTitle,
                      postID: post.id,
                    });
                  }}
                  onUpdateContent={(copy) => {
                    if (!linkedInContent) {
                      setLinkedInContent({
                        id: uuidv4(),
                        editorState: null,
                        copy,
                        documentTitle: null,
                      });
                    } else {
                      setLinkedInContent((linkedInContent) => ({
                        ...linkedInContent,
                        copy,
                      }));
                    }
                  }}
                  roomID={createRoomID({
                    workspaceID: post.workspaceID,
                    contentType: "post",
                    postID: post.id,
                    roomContentID: linkedInContent.id,
                  })}
                />
              )}
              <EditorActionsBar
                post={post}
                channel="LinkedIn"
                visible={true}
                isReadOnly={isReadOnly}
                characterCount={{
                  max: 3000,
                  current: charCount(linkedInContent.copy, {
                    regexTransformers: [
                      {
                        from: linkedinMentionRegex,
                      },
                    ],
                  }),
                }}
                uploadAssetButton={{
                  acceptedMimetypes: [
                    "image/jpeg",
                    "image/png",
                    "image/gif",
                    "video/mp4",
                    "video/quicktime",
                    "application/pdf",
                  ],
                  acceptMultiple: true,
                  onUploadFiles: async (files) => {
                    if (uppy) {
                      handleFilesUpload({
                        files,
                        uppy,
                        meta: {
                          contentID: linkedInContent.id,
                        },
                        getValidFiles,
                      });
                    }
                  },
                  isLoading: uploadingAssets.length > 0,
                }}
                openHowToTagModal={() => {
                  openIntercomArticle("linkedin_tagging");
                }}
              />
            </div>
            <div
              className={classNames({
                "mt-2":
                  assets.length > 0 ||
                  uploadingAssets.length > 0 ||
                  draggedOver,
              })}
            >
              <Assets
                assets={assets}
                uploadingAssets={uploadingAssets}
                onDeleteClick={(asset) => {
                  setAssets((prevAssets) =>
                    prevAssets.filter((prevAsset) => prevAsset.id !== asset.id)
                  );
                  deleteAssetMutation.mutate({
                    linkedInAssetID: asset.id,
                  });
                }}
                editImageModalProperties={editImageModalProperties}
                onEditClick={(asset) => {
                  if (asset.metadata.mimetype.startsWith("image/")) {
                    setEditImageModalProperties({
                      title: "Edit Image",
                      channel: "LinkedIn",
                      maxSizeMB: 5,
                      isOpen: true,
                      asset,
                    });
                  }
                }}
                onUpload={async (file, updateAssetID) => {
                  if (uppy) {
                    if (updateAssetID) {
                      uppy.addFile({
                        name: file.name,
                        type: file.type,
                        data: file,
                        meta: {
                          contentID: linkedInContent.id,
                          updateAssetID,
                        },
                      });
                    } else {
                      await handleFilesUpload({
                        files: [file],
                        uppy,
                        meta: {
                          contentID: linkedInContent.id,
                        },
                        getValidFiles,
                      });
                    }
                  }
                }}
                onEditImageModalClose={() => setEditImageModalProperties(null)}
                showNewEditButton={true}
                refetchAssets={() => {}}
                isReadOnly={isReadOnly}
                onDrop={async (files) => {
                  setDraggedOver(false);
                  if (uppy) {
                    await handleFilesUpload({
                      files,
                      uppy,
                      meta: {
                        contentID: linkedInContent.id,
                      },
                      getValidFiles,
                    });
                  }
                }}
                showDropzone={draggedOver}
                acceptedFileTypes={{
                  "image/jpg": [".jpg", ".jpeg"],
                  "image/png": [".png"],
                  "image/webp": [".webp"],
                  "image/gif": [".gif"],
                  "video/mp4": [".mp4"],
                  "video/quicktime": [".mov"],
                  "application/pdf": [".pdf"],
                }}
                onSortEnd={onSortEnd}
              />
            </div>
          </div>
        </Tabs.Content>
        <Tabs.Content value="post_options">
          <PostOptions>
            <PostOption
              title="Customize Cover Photo"
              disabled={!isVideo}
              description="Upload a custom cover photo"
              disabledMessage="Cover photos are only supported by video posts - upload a video to customize."
            >
              <CoverPhotoModal
                open={coverPhotoModalOpen}
                setOpen={setCoverPhotoModalOpen}
                contentID={linkedInContent.id}
                video={assets[0]}
                coverPhoto={coverPhoto}
                getValidFiles={async (files) => {
                  const file = files[0];

                  if (!file) {
                    return [];
                  }

                  const fileSizeMB = file.size / 1024 / 1024;
                  if (fileSizeMB > 5) {
                    const compressedFile = await compressImage(file, {
                      maxSizeMB: 5,
                    });
                    toast.success("Attachment Optimized", {
                      description: `${file.name} was optimized (files >5MB are not accepted on LinkedIn)`,
                    });
                    posthog.capture(POSTHOG_EVENTS.image.compressed, {
                      originalSizeMB: file.size / 1024 / 1024,
                      compressedSizeKB: compressedFile.size / 1024 / 1024,
                      channel: "LinkedIn",
                      channelPostType: "Cover Photo",
                    });
                    return [compressedFile];
                  } else {
                    return files;
                  }
                }}
                onUpload={(fileName, onSave) => {
                  getNewAssetsMutation.mutate(
                    {
                      path: `${currentWorkspace!.id}/${post.id}/${
                        CHANNEL_ICON_MAP["LinkedIn"].slug
                      }/${linkedInContent.id}/cover_photo`,
                      fileNames: [fileName],
                    },
                    {
                      onSuccess: async (data) => {
                        const { assets } = data;
                        const newAssetsWithProperties = (
                          await getAssetsWithProperties(assets)
                        ).map((asset) => ({
                          ...asset,
                          id: uuidv4(),
                          assetID: asset.id,
                        }));

                        const coverPhotoAsset = newAssetsWithProperties[0];

                        setCoverPhoto(coverPhotoAsset);

                        upsertCoverPhotoMutation.mutate({
                          id: linkedInContent.id,
                          asset: {
                            id: coverPhotoAsset.id,
                            name: coverPhotoAsset.name,
                            path: coverPhotoAsset.path,
                            size: coverPhotoAsset.sizeBytes,
                            eTag: coverPhotoAsset.eTag,
                            md5Hash: coverPhotoAsset.md5Hash,
                            mimetype: coverPhotoAsset.mimetype,
                            width: coverPhotoAsset.width,
                            height: coverPhotoAsset.height,
                            url: coverPhotoAsset.signedUrl,
                            urlExpiresAt: DateTime.now()
                              .plus({ seconds: ONE_YEAR_IN_SECONDS })
                              .toJSDate(),
                          },
                        });

                        onSave();
                      },
                      onError: (error) => {
                        handleTrpcError({
                          title: "Failed to upload cover photo",
                          error,
                        });
                      },
                    }
                  );
                }}
                onDelete={(asset) => {
                  setCoverPhoto(null);
                  deleteCoverPhotoMutation.mutate({
                    linkedInContentID: linkedInContent.id,
                    path: asset.path,
                  });
                }}
                storagePath={`${currentWorkspace?.id}/${post.id}/linkedin/${linkedInContent.id}/cover_photo`}
              />
            </PostOption>
            <PostOption
              title="Video Captions"
              disabled={!isVideo}
              description="Upload a custom .srt captions file to override LinkedIn's captions."
              disabledMessage="Captions are only supported by video posts - upload a video to set."
            >
              <Assets
                assets={!!captions ? [captions] : []}
                refetchAssets={() => {}}
                onDeleteClick={() => {
                  setCaptions(null);
                  deleteCaptionsMutation.mutate({
                    linkedInContentID: linkedInContent.id,
                  });
                }}
                showDropzone={!captions}
                acceptedFileTypes={{
                  "text/plain": [".srt"],
                }}
                onDrop={async (files) => {
                  if (files.length > 1) {
                    toast.error("Too Many Files", {
                      description: "You can only upload one .srt file",
                    });
                  }

                  const file = files[0];
                  const fileSizeMB = file.size / 1024 / 1024;

                  if (!file) {
                    return;
                  } else if (!file.name.endsWith(".srt")) {
                    toast.error("Invalid File Type", {
                      description: "File must be in the .srt file format",
                    });
                  } else if (fileSizeMB > 1) {
                    toast.error("File Size is Too Big", {
                      description: "File size must be smaller than 1MB",
                    });
                  }

                  const path = `${currentWorkspace!.id}/${post.id}/linkedin/${
                    linkedInContent.id
                  }/captions`;

                  const response = await supabasePublic.storage
                    .from("assets")
                    .upload(`${path}/${file.name}`, file);

                  if (response.error) {
                    toast.error("Error Uploading File", {
                      description: response.error.message,
                    });
                  } else {
                    getNewAssetsMutation.mutate(
                      {
                        path,
                        fileNames: [file.name],
                      },
                      {
                        onSuccess: async (data) => {
                          const { assets } = data;
                          const newAssetsWithProperties = (
                            await getAssetsWithProperties(assets)
                          ).map((asset) => ({
                            ...asset,
                            id: uuidv4(),
                            assetID: asset.id,
                          }));

                          const captionsAsset = newAssetsWithProperties[0];

                          setCaptions(captionsAsset);

                          upsertCaptionsMutation.mutate({
                            id: linkedInContent.id,
                            asset: {
                              id: captionsAsset.id,
                              name: captionsAsset.name,
                              path: captionsAsset.path,
                              size: captionsAsset.sizeBytes,
                              eTag: captionsAsset.eTag,
                              md5Hash: captionsAsset.md5Hash,
                              mimetype: captionsAsset.mimetype,
                              url: captionsAsset.signedUrl,
                              urlExpiresAt: DateTime.now()
                                .plus({ seconds: ONE_YEAR_IN_SECONDS })
                                .toJSDate(),
                            },
                          });
                        },
                      }
                    );
                  }
                }}
              />
            </PostOption>
            <PostOption
              title="PDF Titles"
              disabled={!isPDF}
              disabledMessage="Custom PDF Titles are only supported by posts with a PDF attachment."
            >
              <div>
                <div className="font-eyebrow flex items-center gap-1">
                  File Name Preview
                  <HelpTooltip value="The file preview name shows up at the top of your PDF attachment on Linkedin. Assembly uses the name of the file as the default." />
                </div>
                <AutosizeInput
                  size="md"
                  placeholder="Enter a custom name for the file preview bar (uses the file name by default)"
                  value={linkedInContent.documentTitle || ""}
                  onChange={(value) => {
                    setLinkedInContent((linkedInContent) => ({
                      ...linkedInContent,
                      documentTitle: value,
                    }));
                  }}
                  onBlur={() => {
                    upsertLinkedInContentMutation.mutate({
                      id: linkedInContent.id,
                      copy: linkedInContent.copy,
                      editorState: linkedInContent.editorState,
                      documentTitle: linkedInContent.documentTitle,
                      postID: post.id,
                    });
                  }}
                  onEnterPress={() => {
                    upsertLinkedInContentMutation.mutate({
                      id: linkedInContent.id,
                      copy: linkedInContent.copy,
                      editorState: linkedInContent.editorState,
                      documentTitle: linkedInContent.documentTitle,
                      postID: post.id,
                    });
                  }}
                />
              </div>
            </PostOption>
          </PostOptions>
        </Tabs.Content>
        <Tabs.Content value="auto_engagements">
          <div>
            <PostEngagements
              engagements={engagements}
              enableLikes={
                currentWorkspace?.subscription == null ||
                currentWorkspace.subscription.isAutoEngagementEnabled
              }
              channel="LinkedIn"
              onEngagementCreate={async (type) => {
                const engagementID = uuidv4();

                await trpcUtils.linkedInContent.getEngagements.cancel();

                if (type === "Like") {
                  setLikeSelectModalOpen(true);
                } else {
                  const defaultEngagementIntegration = integration
                    ? {
                        id: integration.id,
                        account: {
                          name: integration.account.name,
                          profileImageUrl: integration.account.profileImageUrl,
                        },
                      }
                    : engagementIntegrations[0];

                  createEngagementMutation.mutate({
                    engagementID,
                    contentID: linkedInContent.id,
                    type,
                    integrationID: defaultEngagementIntegration!.id,
                  });

                  // Optimistically update the response to the engagementsQuery
                  trpcUtils.linkedInContent.getEngagements.setData(
                    {
                      postID: post.id,
                    },
                    {
                      engagements: [
                        ...engagements,
                        {
                          id: engagementID,
                          type,
                          copy: null,
                          editorState: null,
                          integrationID: defaultEngagementIntegration!.id,
                          integration: {
                            id: defaultEngagementIntegration!.id,
                            account: {
                              name: defaultEngagementIntegration!.account.name,
                              profileImageUrl:
                                defaultEngagementIntegration!.account
                                  .profileImageUrl,
                            },
                          },
                          postDelaySeconds: 0,
                          createdAt: new Date(),
                        },
                      ],
                    }
                  );
                }
              }}
              onEngagementUpdate={(engagementID, engagement) => {
                updateEngagementMutation.mutate(
                  {
                    engagementID,
                    ...engagement,
                  },
                  {
                    onSuccess: () => {
                      trpcUtils.linkedInContent.getEngagements.invalidate();
                    },
                  }
                );
              }}
              onEngagementDelete={(engagementID) => {
                let engagementIDs = [engagementID];
                const engagement = engagements.find(
                  (engagement) => engagement.id === engagementID
                );

                if (engagement?.type === "Like") {
                  engagementIDs = engagements
                    .filter((engagement) => engagement.type === "Like")
                    .map((like) => like.id);
                }

                deleteEngagementsMutation.mutate(
                  {
                    engagementIDs,
                  },
                  {
                    onSuccess: () => {
                      trpcUtils.linkedInContent.getEngagements.invalidate();
                    },
                  }
                );

                // Optimistically update the restponse to the engagementsQuery to remove this engagement
                trpcUtils.linkedInContent.getEngagements.setData(
                  {
                    postID: post.id,
                  },
                  {
                    engagements: engagements.filter(
                      (engagement) => !engagementIDs.includes(engagement.id)
                    ),
                  }
                );
              }}
              accountOptions={
                isReadOnly
                  ? engagements.map((engagement) => ({
                      value: engagement.integrationID,
                      label: engagement.integration.account.name,
                    }))
                  : connectedIntegrations.map((integration) => ({
                      value: integration.id,
                      label: integration.account.name,
                      engagementPermissions: integration.engagementPermissions,
                      disabled:
                        !(
                          currentWorkspace?.subscription == null ||
                          currentWorkspace.subscription.isAutoEngagementEnabled
                        ) && integration.id !== integration?.id,
                      disabledReason: (
                        <div>
                          <div className="font-body-sm mb-1.5">
                            Auto-engagements from accounts other than the
                            posting account is only available on{" "}
                            <span className="font-medium">Standard</span>{" "}
                            workspace plans and above.
                          </div>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => {
                              router.push("/settings/pricing");
                            }}
                          >
                            See Plans
                          </Button>
                        </div>
                      ),
                    }))
              }
              Editor={({
                initialValue,
                onSave,
                onUpdateContent,
                placeholder,
                engagementID,
              }) => (
                <LinkedInEditorWrapper
                  key={linkedInContent.id}
                  postID={post.id}
                  initialValue={initialValue}
                  placeholder={placeholder}
                  onSave={onSave}
                  onUpdateContent={onUpdateContent}
                  size="sm"
                  isReadOnly={isReadOnly}
                  escapeContent={false}
                  roomID={null}
                />
              )}
              isReadOnly={isReadOnly}
            />
            <SelectModal
              open={likeSelectModalOpen}
              setOpen={setLikeSelectModalOpen}
              isMulti={true}
              defaultSelected={
                engagements.filter((engagement) => engagement.type === "Like")
                  .length
                  ? engagements
                      .filter((engagement) => engagement.type === "Like")
                      .map((like) => ({ id: like.integrationID }))
                  : engagementIntegrations
                      .filter(
                        (account) =>
                          account.id !== integration?.id &&
                          account.engagementPermissions.includes("Like")
                      )
                      .map((account) => ({
                        id: account.id,
                      }))
              }
              title="Add Automatic Likes"
              subtitle="Schedule automatic likes on LinkedIn from your connected accounts. Likes will automatically trickle in over a period of 10 minutes - no action needed from the user."
              options={engagementIntegrations
                .filter((integration) =>
                  integration.engagementPermissions.includes("Like")
                )
                .map((integration) => ({
                  id: integration.id,
                  name: integration.account.name,
                  profileImageUrl: integration.account.profileImageUrl,
                }))}
              onSelect={(accounts) => {
                const currentLikes = engagements.filter(
                  (engagement) => engagement.type === "Like"
                );

                const likes = accounts.map((account) => {
                  const currentLike = currentLikes.find(
                    (like) => like.integrationID === account.id
                  );

                  if (currentLike) {
                    return currentLike;
                  } else {
                    return {
                      id: uuidv4(),
                      type: "Like" as const,
                      contentID: linkedInContent.id,
                      integrationID: account.id,
                      createdAt: new Date(),
                    };
                  }
                });

                upsertEngagementLikesMutation.mutate({
                  likes: likes.map((like) => ({
                    id: like.id,
                    contentID: linkedInContent.id,
                    integrationID: like.integrationID,
                  })),
                });

                // Optimistically update the restponse to the engagementsQuery
                trpcUtils.linkedInContent.getEngagements.setData(
                  {
                    postID: post.id,
                  },
                  {
                    engagements: [
                      ...engagements.filter(
                        (engagement) => engagement.type !== "Like"
                      ),
                      ...likes.map((like) => ({
                        id: like.id,
                        type: like.type,
                        copy: null,
                        editorState: null,
                        integrationID: like.integrationID,
                        integration: {
                          id: like.integrationID,
                          account: {
                            name:
                              integrationOptions.find(
                                (integration) =>
                                  integration.id === like.integrationID
                              )?.account.name || "",
                            profileImageUrl:
                              integrationOptions.find(
                                (integration) =>
                                  integration.id === like.integrationID
                              )?.account.profileImageUrl || "",
                          },
                        },
                        postDelaySeconds: 0,
                        createdAt: like.createdAt,
                      })),
                    ],
                  }
                );

                setLikeSelectModalOpen(false);
              }}
            />
          </div>
        </Tabs.Content>
      </Tabs.Root>
    </div>
  );
};

export default LinkedInPost;
