import { Channel, YouTubePrivacyStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import assert from "assert";
import classNames from "classnames";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import Checkbox from "~/components/Checkbox";
import HelpTooltip from "~/components/HelpTooltip";
import Select from "~/components/Select";
import TextLink from "~/components/TextLink";
import Tabs from "~/components/ui/Tabs";
import { MAX_VIDEO_UPLOAD_SIZE_MB, ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import YOUTUBE_CATEGORIES from "~/constants/youtubeCategories";
import { useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  findAsync,
  handleFilesUpload,
  handleTrpcError,
  openIntercomChat,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import { getDefaultAccount } from "~/utils/getDefaultAccount";
import getFileProperties from "~/utils/getFileProperties";
import getVideoProperties from "~/utils/getVideoProperties";
import createRoomID from "~/utils/liveblocks/createRoomID";
import Assets, { UploadingAsset } from "../../files/Assets";
import YouTubeShortsEditorWrapper from "../../tiptap/YouTubeShortsEditor";
import EditorActionsBar from "../EditorActionsBar";
import { PostOption, PostOptions } from "../PostOptions";
import PostPerformance from "../PostPerformance";
import PostPreviewContainer from "../PostPreviewContainer";
import { ScheduledPost } from "../SchedulePostAccountSelect";
import YouTubeShortsPostPreview from "./YouTubeShortsPostPreview";

export type YouTubeShortsContent = {
  id: string;
  title: string | null;
  titleEditorState: JSONContent | null;
  description: string | null;
  descriptionEditorState: JSONContent | null;
  categoryID: string | null;
  privacyStatus: YouTubePrivacyStatus;
  notifySubscribers: boolean;
  isEmbeddable: boolean;
  isSelfDeclaredMadeForKids: boolean;
};

export type YouTubeIntegration = {
  id: string;
  name: string;
  username: string;
  profileImageUrl: string;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    scheduledPosts: ScheduledPost[];
    youTubeShortsAssets: {
      asset: AssetType | null;
    };
    youTubeShortsContent: YouTubeShortsContent | null;
    youTubeIntegration: YouTubeIntegration | null;
    youTubeLink: string | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const YouTubeShortsPost = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const trpcUtils = trpc.useUtils();

  const [contentAsset, setContentAsset] = useState<AssetType | null>(
    post.youTubeShortsAssets.asset
  );
  const [uploadingAsset, setUploadingAsset] = useState<UploadingAsset | null>(
    null
  );
  const { currentWorkspace } = useWorkspace();

  const [draggedOver, setDraggedOver] = useState<boolean>(false);

  const [content, setContent] = useState<YouTubeShortsContent>(
    post.youTubeShortsContent || {
      id: uuidv4(),
      title: null,
      titleEditorState: null,
      description: "",
      descriptionEditorState: null,
      categoryID: "",
      privacyStatus: "public",
      notifySubscribers: true,
      isEmbeddable: false,
      isSelfDeclaredMadeForKids: false,
    }
  );
  const [integration, setIntegration] = useState<{
    id: string;
    integrationID: string;
    name: string;
    profileImageUrl: string;
  } | null>(
    post.youTubeIntegration
      ? {
          id: post.youTubeIntegration.id,
          integrationID: post.youTubeIntegration.id,
          name: `@${post.youTubeIntegration.username}`,
          profileImageUrl: post.youTubeIntegration.profileImageUrl,
        }
      : null
  );

  const [uppy, setUppy] = useState<Uppy | null>(null);

  const updatePostMutation = trpc.post.update.useMutation();
  const connectedAccountsQuery =
    trpc.integration.youtube.getConnectedAccounts.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });
  const integrationsQuery = trpc.post.getIntegrations.useQuery({
    id: post.id,
  });
  const postAccounts = integrationsQuery.data?.accounts ?? [];

  const connectedAccountData = connectedAccountsQuery.data;
  useEffect(() => {
    const setDefaultAccount = () => {
      if (integration == null && connectedAccountData?.accounts.length) {
        const defaultIntegration = getDefaultAccount({
          mainAccount: postAccounts[0],
          accounts: connectedAccountData.accounts,
        });
        setIntegration({
          id: defaultIntegration.id,
          integrationID: defaultIntegration.id,
          name: `@${defaultIntegration.channel.username}`,
          profileImageUrl: defaultIntegration.channel.profileImageUrl,
        });
        updatePostMutation.mutate(
          {
            id: post.id,
            youTubeIntegrationID: defaultIntegration.id,
          },
          {
            onSuccess: () => {
              trpcUtils.post.getIntegrations.refetch();
            },
          }
        );
      }
    };
    setDefaultAccount();
  }, [connectedAccountData]);

  const contentQuery = trpc.youtubeShortsContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const contentData = contentQuery.data;
  useEffect(() => {
    const setContentData = async () => {
      if (contentData && contentData.youTubeShortsContent) {
        setContent(contentData.youTubeShortsContent);
        setContentAsset(contentData.asset);
        if (contentData.asset) {
          await maybeCompressAssets([contentData.asset]);
        }
        clearIsSyncing();
      }
    };
    setContentData();
  }, [contentData]);

  const upsertYouTubeMutation = trpc.youtubeShortsContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
  });

  const createAssetsMutation =
    trpc.youtubeShortsContent.createAsset.useMutation();
  const deleteAssetMutation =
    trpc.youtubeShortsContent.deleteAsset.useMutation();
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const compressVideoMutation = trpc.video.compress.useMutation();
  const maybeCompressAssets = async (
    assets: {
      path: string;
      sizeBytes: number;
      sizeMB: number;
      mimetype: string;
      signedUrl: string;
      duration?: number | null;
      width?: number | null;
      height?: number | null;
    }[]
  ) => {
    const videoPromises = assets.map(async (asset) => {
      if (!asset || !asset.mimetype.startsWith("video/")) {
        return Promise.resolve(null);
      } else {
        let duration = asset.duration;
        let width = asset.width;
        let height = asset.height;
        if (width && height && duration) {
          return asset as typeof asset & {
            duration: number;
            width: number;
            height: number;
          };
        } else {
          const properties = await getVideoProperties(asset.signedUrl);
          return {
            ...asset,
            duration: properties.duration,
            width: properties.width,
            height: properties.height,
          };
        }
      }
    });
    const videosWithProperties = await Promise.all(videoPromises);

    const videoToCompress = await findAsync(
      videosWithProperties,
      async (asset) => {
        if (!asset || !asset.mimetype.startsWith("video/")) {
          return false;
        }

        return asset.sizeMB > 125;
      }
    );

    if (videoToCompress != null) {
      const { width, height } = videoToCompress;

      compressVideoMutation.mutate(
        {
          video: {
            path: videoToCompress.path,
            resolution: {
              width,
              height,
            },
            duration: videoToCompress.duration,
            sizeBytes: videoToCompress.sizeBytes,
          },
          channel: "YouTubeShorts",
        },
        {
          onError: (error) => {
            handleTrpcError({ title: "Error Compressing Video", error });
          },
          onSuccess: (data) => {
            if (data.jobID != null) {
              toast.info("Optimizing Attachment", {
                description:
                  "We're automatically optimizing your attachment (files >125MB are not accepted on YouTube).",
              });
            }
          },
        }
      );
    }
  };

  const accountOptions =
    connectedAccountsQuery.data?.accounts.map((account) => {
      return {
        id: account.id,
        integrationID: account.id,
        name: `@${account.channel.username}`,
        profileImageUrl: account.channel.profileImageUrl,
      };
    }) || (integration ? [integration] : []);

  const numSetOptions = [].filter((option) => option).length;

  const getValidFiles = async (files: File[]) => {
    if (contentAsset != null) {
      toast.error("Too Many Files", {
        description: "Only one file can be uploaded to YouTube Shorts",
      });
      return [];
    }

    const validFiles: File[] = [];
    // https://developers.facebook.com/docs/instagram-api/reference/ig-user/media#image-specifications
    for await (const file of files) {
      const fileType = file.type;
      const fileSizeMB = file.size / 1024 / 1024;

      if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
        toast.error("Video is too large", {
          description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
          action: {
            label: "Contact Support",
            onClick: () => {
              openIntercomChat(
                `I need help compressing a video for YouTube Shorts. I have a ${fileSizeMB.toFixed(
                  1
                )}MB video that I need to compress to under ${MAX_VIDEO_UPLOAD_SIZE_MB}MB`
              );
            },
          },
        });
        continue;
      }

      const { duration, height, width } = await getVideoProperties(file);

      if (duration > 3 * 60) {
        toast.error("Video is Too Long", {
          description:
            "YouTube Shorts requires videos to be a maximum of 3 minutes",
        });
        continue;
      }

      if (width / height !== 9 / 16) {
        toast.error("Invalid Aspect Ratio", {
          description:
            "YouTube Shorts requires videos to be in a 9:16 aspect ratio",
        });
        continue;
      }

      if (height / width < 1) {
        toast.error("Video Must be Taller", {
          description:
            "YouTube Shorts must have a height greater than or equal to it's width (minimum 1:1 aspect ratio)",
        });
        continue;
      }

      validFiles.push(file);
    }

    return validFiles;
  };

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `youtube-shorts-${post.id}`,
        restrictions: { maxNumberOfFiles: 1 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", (file) => {
        assert(currentWorkspace);

        setUploadingAsset({
          name: file.name!,
          size: file.size!,
          type: file.type,
          uploadProgress: 0,
        });

        const objectName = `${currentWorkspace.id}/${post.id}/${CHANNEL_ICON_MAP["YouTubeShorts"].slug}/${content.id}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAsset((prevAsset) => {
          if (prevAsset && prevAsset.name === file.name) {
            return {
              ...prevAsset,
              uploadProgress,
            };
          }
          return prevAsset;
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (data) => {
        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["YouTubeShorts"].slug
        }/${content.id}`;
        const fileNames = data.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          data.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              const newAsset = newAssetsWithProperties[0];

              createAssetsMutation.mutate(
                {
                  id: content.id,
                  asset: {
                    id: newAsset.id,
                    assetID: newAsset.assetID,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                },
                {
                  onSuccess: async () => {
                    await maybeCompressAssets(newAssetsWithProperties);
                  },
                  onError: (error) => {
                    handleTrpcError({ title: "Error Creating Asset", error });
                  },
                }
              );

              setUploadingAsset(null);
              setContentAsset(newAsset);
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace, content.id]);

  return (
    <div
      onDragOverCapture={() => {
        setDraggedOver(true);
      }}
      onDragLeaveCapture={() => {
        setDraggedOver(false);
      }}
    >
      <PostPerformance channel="YouTubeShorts" url={post.youTubeLink} />
      <PostPreviewContainer
        channel="YouTubeShorts"
        post={post}
        connectedAccount={
          integration
            ? {
                integrationID: integration.id,
                name: integration.name,
              }
            : null
        }
        accountOptions={accountOptions.map((account) => ({
          integrationID: account.id,
          name: account.name,
        }))}
        setConnectedAccountID={(youTubeIntegrationID: string) => {
          const newIntegration = accountOptions.find(
            (option) => option.id === youTubeIntegrationID
          );
          setIntegration(newIntegration!);
          updatePostMutation.mutate(
            {
              id: post.id,
              youTubeIntegrationID,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getIntegrations.refetch();
              },
            }
          );
        }}
        Preview={
          <YouTubeShortsPostPreview
            content={{ ...content, asset: contentAsset }}
            connectedAccount={integration}
          />
        }
        isReadOnly={isReadOnly}
      />
      <Tabs.Root defaultValue="post_content">
        <Tabs.List>
          <Tabs.Trigger value="post_content">Post Content</Tabs.Trigger>
          <Tabs.Trigger value="post_options">
            {numSetOptions > 0
              ? `Post Options (${numSetOptions})`
              : "Post Options"}
          </Tabs.Trigger>
        </Tabs.List>
        <Tabs.Content value="post_content">
          <div className="group">
            <YouTubeShortsEditorWrapper
              key={content.id}
              postID={post.id}
              title="Title & Content"
              tooltipContent={
                <div className="font-body-xs">
                  <div className="font-medium">Shorts Title</div>
                  <div>
                    The title for your shorts is the text that appears over your
                    Youtube Short video when being viewed (shown in the blue box
                    below).
                  </div>
                  <img
                    src="/youtube_shorts/title_and_content_info.png"
                    className="my-2 w-full"
                  />
                  <div className="font-medium">Shorts Content</div>
                  <div>
                    Your content is the actual video for Shorts. We support .mp4
                    and .mov video uploads.
                  </div>
                </div>
              }
              initialValue={content.titleEditorState}
              placeholder="Add your post title and video attachment here"
              maxLength={100}
              onImagePaste={(event) =>
                handleFilesUpload({
                  items: event.clipboardData.items,
                  uppy: uppy!,
                  meta: {
                    contentID: content.id,
                  },
                  getValidFiles,
                })
              }
              onSave={(titleEditorState) => {
                setContent((prevContent) => {
                  upsertYouTubeMutation.mutate({
                    id: prevContent.id,
                    title: prevContent.title,
                    titleEditorState,
                    postID: post.id,
                  });

                  return {
                    ...prevContent,
                    titleEditorState,
                  };
                });
              }}
              onUpdateContent={(title) => {
                setContent((prevContent) => ({
                  ...prevContent,
                  title,
                }));
              }}
              roomID={createRoomID({
                workspaceID: post.workspaceID,
                contentType: "post",
                postID: post.id,
                roomContentID: content.id,
                extraInformation: "title",
              })}
            />
            <EditorActionsBar
              post={post}
              channel="YouTubeShorts"
              visible={true}
              isReadOnly={isReadOnly}
              characterCount={{
                current: content.title?.length || 0,
                max: 100,
              }}
              uploadAssetButton={{
                acceptedMimetypes: ["video/mp4", "video/quicktime"],
                onUploadFiles: async (files) => {
                  if (uppy) {
                    handleFilesUpload({
                      files,
                      uppy,
                      meta: {
                        contentID: content.id,
                      },
                      getValidFiles,
                    });
                  }
                },
                isLoading: !!uploadingAsset,
              }}
            />
            <div
              className={classNames({
                "mt-2": contentAsset != null,
              })}
            >
              <Assets
                assets={contentAsset ? [contentAsset] : []}
                uploadingAssets={uploadingAsset ? [uploadingAsset] : []}
                onDeleteClick={(asset) => {
                  setContentAsset(null);
                  deleteAssetMutation.mutate({
                    youTubeShortsAssetID: asset.id,
                  });
                }}
                refetchAssets={() => {}}
                isReadOnly={isReadOnly}
                showDropzone={draggedOver}
                onDrop={async (files) => {
                  setDraggedOver(false);
                  if (uppy) {
                    await handleFilesUpload({
                      files,
                      uppy,
                      meta: {
                        contentID: content.id,
                      },
                      getValidFiles,
                    });
                  }
                }}
                acceptedFileTypes={{
                  "video/mp4": [".mp4"],
                  "video/quicktime": [".mov"],
                }}
              />
            </div>
            <div className="mt-1.5">
              <YouTubeShortsEditorWrapper
                key={content.id}
                postID={post.id}
                title="Description"
                tooltipContent={
                  <div className="font-body-xs">
                    <div className="font-medium">Shorts Desciption</div>
                    <div>
                      The description for your Shorts appears in the more info
                      icon on Desktop. It can be helpful when including linkouts
                      related to your video, or a longer description to make
                      your video relevant for search.
                    </div>
                    <img
                      src="/youtube_shorts/description_info.png"
                      className="my-2 w-full"
                    />
                  </div>
                }
                initialValue={content.descriptionEditorState}
                placeholder="Add your description and video content for the Shorts here"
                maxLength={5000}
                onSave={(descriptionEditorState) => {
                  setContent((oldContent) => ({
                    ...oldContent,
                    descriptionEditorState,
                  }));

                  upsertYouTubeMutation.mutate({
                    id: content.id,
                    description: content.description,
                    descriptionEditorState,
                    postID: post.id,
                  });
                }}
                onUpdateContent={(description) => {
                  setContent((oldContent) => ({
                    ...oldContent,
                    description,
                  }));
                }}
                roomID={createRoomID({
                  workspaceID: post.workspaceID,
                  contentType: "post",
                  postID: post.id,
                  roomContentID: content.id,
                  extraInformation: "description",
                })}
              />
            </div>
          </div>
        </Tabs.Content>
        <Tabs.Content value="post_options">
          <PostOptions>
            <PostOption
              title="Video Settings"
              description="Customize options on your video"
            >
              <div className="flex items-center gap-4 pb-2">
                <Select
                  placeholder="Category"
                  isSearchable={true}
                  value={content.categoryID}
                  options={YOUTUBE_CATEGORIES}
                  onChange={(categoryID) => {
                    setContent((oldContent) => ({
                      ...oldContent,
                      categoryID,
                    }));

                    upsertYouTubeMutation.mutate({
                      id: content.id,
                      categoryID,
                      postID: post.id,
                    });
                  }}
                />
                <Select
                  placeholder="Privacy Status"
                  value={content.privacyStatus}
                  options={[
                    {
                      value: "public",
                      label: "Public",
                    },
                    {
                      value: "private",
                      label: "Private",
                    },
                    {
                      value: "unlisted",
                      label: "Unlisted",
                    },
                  ]}
                  onChange={(privacyStatus) => {
                    setContent((oldContent) => ({
                      ...oldContent,
                      privacyStatus,
                    }));

                    upsertYouTubeMutation.mutate({
                      id: content.id,
                      privacyStatus,
                      postID: post.id,
                    });
                  }}
                />
                <Checkbox
                  label="Allow embedding"
                  value={content.isEmbeddable}
                  onChange={(isEmbeddable) => {
                    setContent((oldContent) => ({
                      ...oldContent,
                      isEmbeddable,
                    }));

                    upsertYouTubeMutation.mutate({
                      id: content.id,
                      isEmbeddable,
                      postID: post.id,
                    });
                  }}
                />
                <Checkbox
                  label={
                    <div className="flex items-center gap-1">
                      Mark as made for kids
                      <HelpTooltip
                        value={
                          <div>
                            YouTube requires you to check this setting if your
                            content is made for kids.{" "}
                            <TextLink
                              value="Click here"
                              size="sm"
                              href="https://support.google.com/youtube/answer/9528076?hl=en"
                            />{" "}
                            for more info.
                          </div>
                        }
                      />
                    </div>
                  }
                  value={content.isSelfDeclaredMadeForKids}
                  onChange={(isSelfDeclaredMadeForKids) => {
                    setContent((oldContent) => ({
                      ...oldContent,
                      isSelfDeclaredMadeForKids,
                    }));

                    upsertYouTubeMutation.mutate({
                      id: content.id,
                      isSelfDeclaredMadeForKids,
                      postID: post.id,
                    });
                  }}
                />
              </div>
            </PostOption>
            <PostOption
              title="Notify Subscribers"
              description="Choose whether your subscribers will be notified when the video posts."
            >
              <Checkbox
                label="Notify subscribers"
                value={content.notifySubscribers}
                onChange={(notifySubscribers) => {
                  setContent((oldContent) => ({
                    ...oldContent,
                    notifySubscribers,
                  }));

                  upsertYouTubeMutation.mutate({
                    id: content.id,
                    notifySubscribers,
                    postID: post.id,
                  });
                }}
              />
            </PostOption>
          </PostOptions>
        </Tabs.Content>
      </Tabs.Root>
    </div>
  );
};

export default YouTubeShortsPost;
