import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForDiscord = async (post: {
  id: string;
  workspaceID: string;
  discordContent: {
    id: string;
  } | null;
}): Promise<AssetType[]> => {
  const discordContent = post.discordContent;

  if (!discordContent) {
    return [];
  }

  const assetsMap = await getAssetsForFilePaths([
    {
      id: discordContent.id,
      path: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Discord"].slug}/${discordContent.id}`,
    },
  ]);

  return assetsMap[discordContent.id];
};

export default getStorageAssetsForDiscord;
