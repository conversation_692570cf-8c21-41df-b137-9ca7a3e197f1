import { useEffect } from "react";

import { JSONContent } from "@tiptap/core";
import { DateTime } from "luxon";
import { Alert } from "~/components";
import { Calendar, CommentsAndActivity } from "~/components/app";
import {
  CampaignHeader,
  CampaignPosts,
  CampaignSidebar,
} from "~/components/app/campaigns";
import FileButton from "~/components/app/files/FileButton";
import Tabs from "~/components/ui/Tabs";
import { useWindowTitle } from "~/hooks";
import { useWorkspace } from "~/providers";
import { handleTrpcError, trpc } from "~/utils";
import createRoomID from "~/utils/liveblocks/createRoomID";
import CampaignEditorWrapper from "../tiptap/CampaignEditor";
import CampaignPageLoader from "./CampaignPageLoader";

type Props = {
  id: string;
};

const CampaignPage = ({ id }: Props) => {
  const { workspaces, currentWorkspace, setCurrentWorkspace } = useWorkspace();

  const campaignQuery = trpc.campaign.get.useQuery({
    id: id,
  });

  const campaignData = campaignQuery.data;
  const campaign = campaignData?.campaign;
  const isReadOnly = campaignData?.isReadOnly;
  useEffect(() => {
    if (campaignData) {
      if (
        currentWorkspace?.id !== campaignData.campaign.workspaceID &&
        !campaignData.campaign.isSharedPublicly
      ) {
        const newWorkspace = workspaces.find(
          (workspaces) => workspaces.id === campaignData.campaign.workspaceID
        );
        if (newWorkspace) {
          setCurrentWorkspace(newWorkspace);
        } else {
          throw new Error("Unauthorized");
        }
      }
    }
  }, [campaignData]);

  const updateCampaignMutation = trpc.campaign.update.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Unable to Save Campaign", error });
    },
  });

  const handleUpdateCampaign = (values: {
    overviewEditorState?: JSONContent;
  }) => {
    updateCampaignMutation.mutate({
      id,
      ...values,
    });
  };

  if (!!campaignQuery.error) {
    return (
      <div className="w-full">
        <div className="mx-auto mt-4 max-w-lg">
          <Alert
            title="Unable to Load Campaign"
            intent="danger"
            message={campaignQuery.error.message}
          />
        </div>
      </div>
    );
  }

  useWindowTitle(campaign ? `${campaign.name}` : "Assembly");

  if (!campaign || isReadOnly == null) {
    return (
      <div className="h-full w-full px-8 py-4">
        <CampaignPageLoader />
      </div>
    );
  }

  return (
    <div className="h-full w-full px-8 py-3">
      <div className="flex h-full w-full gap-10">
        <div className="border-lightest-gray scrollbar-hidden h-full w-full overflow-auto border-r pr-8">
          <CampaignHeader campaign={campaign} isReadOnly={isReadOnly} />
          <Tabs.Root defaultValue="overview">
            <Tabs.List>
              <Tabs.Trigger value="overview">Overview</Tabs.Trigger>
              <Tabs.Trigger value="calendar">Calendar</Tabs.Trigger>
            </Tabs.List>
            <Tabs.Content value="overview">
              <div className="scrollbar-hidden h-full w-full overflow-auto">
                <CampaignEditorWrapper
                  initialValue={campaign.overviewEditorState}
                  onSave={(editorState) =>
                    handleUpdateCampaign({ overviewEditorState: editorState })
                  }
                  placeholder="Include campaign overview, key messaging, goals/KPIs, target 
                audience, asset links, and any additional brief notes here..."
                  isReadOnly={isReadOnly}
                  roomID={createRoomID({
                    workspaceID: campaign.workspaceID,
                    contentType: "campaign",
                    campaignID: campaign.id,
                    roomContentID: "notes",
                  })}
                />
                <FileButton
                  assets={campaign.assets}
                  contentID={campaign.id}
                  isReadOnly={isReadOnly}
                />
                <CampaignPosts
                  campaignID={campaign.id}
                  posts={[...campaign.posts, ...campaign.ideas]}
                  isReadOnly={isReadOnly}
                />
              </div>
            </Tabs.Content>
            <Tabs.Content value="calendar">
              <Calendar
                view="calendar"
                posts={campaign.posts}
                campaigns={[campaign]}
                isLoading={campaignQuery.isLoading}
                campaignStartDate={DateTime.fromJSDate(
                  campaign.startDate
                ).toJSDate()}
                isReadOnly={isReadOnly}
                defaultCampaignID={campaign.id}
              />
            </Tabs.Content>
          </Tabs.Root>
          <CommentsAndActivity
            id={id}
            type="campaign"
            comments={campaign.comments}
            activity={campaign.activity}
            isReadOnly={isReadOnly}
          />
        </div>
        <CampaignSidebar campaign={campaign} isReadOnly={isReadOnly} />
      </div>
    </div>
  );
};

export default CampaignPage;
