// https://regexr.com/79lmo
const fullRegex =
  /(https?:\/\/(?:www\.|(?!www)))[a-zA-Z0-9][a-zA-Z0-9-\.]*[a-zA-Z0-9]\.(?!js)[a-zA-Z]{2,}(?:\/[a-zA-Z0-9@\-_\/]*)*(?:\?[\w\d\=\&\@\-\-\%]*)?|www\.[a-zA-Z0-9][a-zA-Z0-9-\.]*[a-zA-Z0-9]\.(?!js)[a-zA-Z]{2,}(?:\/[a-zA-Z0-9@\-_\/]*)*(?:\?[\w\d\=\&\@\-\-\%]*)?|(https?:\/\/(?:www\.|(?!www)))[a-zA-Z0-9]+\.(?!js)[a-zA-Z]{2,}(?:\/[a-zA-Z0-9@\-_\/]*)*(?:\?[\w\d\=\&\@\-\-\%]*)?|www\.[a-zA-Z0-9]+\.(?!js)[a-zA-Z]{2,}(?:\/[a-zA-Z0-9@\-_\/]*)*(?:\?[\w\d\=\&\@\-\-\%]*)?|(?!www\.|ww\.|w\.)[a-zA-Z0-9]+\.[a-zA-Z0-9]+\.(?!js)[a-zA-Z]{2,}(?:\/[a-zA-Z0-9@\-_\/]*)*(?:\?[\w\d\=\&\@\-\-\%]*)?|(?!www\.|ww\.|w\.)[a-zA-Z0-9]+\.(?!js)[a-zA-Z]{2,}(?:\/[a-zA-Z0-9@\-_\/]*)*(?:\?[\w\d\=\&\@\-\-\%]*)?/g;

// Define each part of the regex as separate strings
const protocol = "(https?:\\/\\/(?:www\\.|(?!www)))";
const domain = "[a-zA-Z0-9][a-zA-Z0-9-\\.]*[a-zA-Z0-9]";
const domainWithoutHyphens = "[a-zA-Z0-9]+";
// https://data.iana.org/TLD/tlds-alpha-by-domain.txt
const topLevelDomain = "\\.(?!js)[a-zA-Z]{2,}";
const pathWithSlash = "(?:\\/[^\\s?#)]*)?(?:\\?[^\\s#)]*)?(?:#[^\\s)]*)?";

const wwwDomain = "www\\." + domain + topLevelDomain + pathWithSlash;
const httpsDomain =
  protocol + domainWithoutHyphens + topLevelDomain + pathWithSlash;
const wwwDomainWithoutHyphens =
  "www\\." + domainWithoutHyphens + topLevelDomain + pathWithSlash;
const nonWwwDomain =
  "(?!www\\.|ww\\.|w\\.)" +
  domainWithoutHyphens +
  "\\." +
  domainWithoutHyphens +
  topLevelDomain +
  pathWithSlash;
const nonWwwDomainSimple =
  "(?!www\\.|ww\\.|w\\.)" +
  domainWithoutHyphens +
  topLevelDomain +
  pathWithSlash;

// Combine the parts into the full regex pattern
const combinedPattern =
  "(?<!@[\\w\\.-]*)(?:" +
  [
    protocol + domain + topLevelDomain + pathWithSlash,
    wwwDomain,
    httpsDomain,
    wwwDomainWithoutHyphens,
    nonWwwDomain,
    nonWwwDomainSimple,
  ].join("|") +
  ")";

// Create the RegExp object
const urlRegex = new RegExp(combinedPattern, "g");

export default urlRegex;
