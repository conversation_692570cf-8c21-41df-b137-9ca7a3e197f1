import completeOnboardingTask from "apiUtils/completeOnboardingTask";
import countSocialProfiles from "apiUtils/countSocialProfiles";
import addSubscriptionItem from "apiUtils/stripe/addSubscriptionItem";
import { StatusCodes } from "http-status-codes";
import type { NextApiRequest, NextApiResponse } from "next";
import getUserWorkspaceWithSocialProfileUsage from "server/routers/utils/getUserWorkspaceWithSocialProfileUsage";
import { slack } from "~/clients";
import prisma, { db } from "~/clients/Prisma";
import createSupabaseServerClient from "~/clients/createSupabaseServerClient";
import posthog from "~/clients/posthog";
import TikTok from "~/clients/tiktok/TikTok";
import { TIKTOK_SCOPES } from "~/components/app/settings/profiles/TikTokConnectPage";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { getCookie } from "~/utils/cookies";

const handleError = (
  res: NextApiResponse,
  message: string,
  referrer: string
) => {
  const params = new URLSearchParams({
    error_message: message,
    channel: "TikTok",
  });

  return res.redirect(StatusCodes.MOVED_TEMPORARILY, `${referrer}?${params}`);
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res
      .status(StatusCodes.METHOD_NOT_ALLOWED)
      .json({ message: "Method not allowed" });
  }

  const { code, state } = req.query;
  const tiktokIntegrationCookie = getCookie(
    "TIKTOK_INTEGRATION",
    req.headers.cookie
  );

  if (!tiktokIntegrationCookie) {
    return handleError(
      res,
      "Session expired, please try again",
      "/settings/profiles"
    );
  }

  const { accountID, referrer } = tiktokIntegrationCookie;

  if (!code) {
    return handleError(res, "No authorization code provided", referrer);
  } else if (state !== tiktokIntegrationCookie.state) {
    return handleError(res, "Session expired, please try again", referrer);
  }

  const supabase = await createSupabaseServerClient(req.headers.cookie);

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const workspace = await getUserWorkspaceWithSocialProfileUsage({
    user,
    currentWorkspaceID: getCookie("CURRENT_WORKSPACE_ID", req.headers.cookie),
  });

  if (!user || !workspace) {
    return handleError(res, "User not logged in", referrer);
  }

  try {
    const accessTokenResponse = await TikTok.getAccessToken(code as string);

    if (accessTokenResponse.errors) {
      return handleError(res, accessTokenResponse.errors.join(","), referrer);
    }

    const { data } = accessTokenResponse;
    const { scope } = data;

    if (!TIKTOK_SCOPES.every((permission) => scope.includes(permission))) {
      return handleError(
        res,
        "Please accept all permissions to connect your TikTok account",
        referrer
      );
    }

    const tiktok = new TikTok(data.accessToken);
    const accountResponse = await tiktok.getMe();

    if (!accountResponse.data) {
      return handleError(res, "Unable to query account information", referrer);
    }

    const { account: tikTokAccount, stats } = accountResponse.data;

    const tikTokIntegration = await db.tikTokIntegration.findFirst({
      where: {
        accountID: tikTokAccount.id,
      },
      include: {
        account: true,
      },
    });

    const accountID = tiktokIntegrationCookie?.accountID;
    if (accountID && tikTokAccount.id !== accountID) {
      const expectedTikTokAccount = await db.tikTokAccount.findFirst({
        where: {
          id: accountID,
        },
      });

      if (expectedTikTokAccount) {
        return handleError(
          res,
          `Switch to @${expectedTikTokAccount.username} on TikTok first and then try again`,
          referrer
        );
      } else {
        return handleError(
          res,
          "Please login with the profile you want to connect first before integrating",
          referrer
        );
      }
    }

    if (tikTokIntegration) {
      if (tikTokIntegration.workspaceID !== workspace.id) {
        return handleError(
          res,
          `@${tikTokIntegration.account.username} already connected to another workspace`,
          referrer
        );
      }

      await db.tikTokIntegration.update({
        where: {
          id: tikTokIntegration.id,
        },
        data: {
          accessToken: data.accessToken,
          expiresAt: data.expiresAt,
          refreshToken: data.refreshToken,
          refreshTokenExpiresAt: data.refreshTokenExpiresAt,
          userID: user.id,
          scope,
          isReintegrationRequired: false,
        },
      });

      await db.tikTokAccount.update({
        where: {
          id: tikTokAccount.id,
        },
        data: {
          username: tikTokAccount.username,
          name: tikTokAccount.name,
          bioDescription: tikTokAccount.bioDescription,
          profileLink: tikTokAccount.profileLink,
          isVerified: tikTokAccount.isVerified,
          profileImageUrl: tikTokAccount.profileImageUrl,
        },
      });
    } else {
      const { subscription } = workspace;

      if (subscription) {
        const numSocialProfiles = countSocialProfiles(workspace);

        if (numSocialProfiles >= subscription.socialProfileLimit) {
          await addSubscriptionItem({
            workspaceID: workspace.id,
            itemName: "ADDITIONAL_SOCIAL_PROFILE",
            quantity: numSocialProfiles - subscription.socialProfileLimit + 1,
          });
        }
      }

      await db.$transaction(async (tx) => {
        const username = tikTokAccount.username;

        const account = await tx.tikTokAccount.upsert({
          where: {
            id: tikTokAccount.id,
          },
          create: {
            ...tikTokAccount,
            stats: {
              create: stats,
            },
          },
          update: {
            ...tikTokAccount,
            stats: {
              create: stats,
            },
          },
        });

        const integration = await tx.tikTokIntegration.create({
          data: {
            accountID: account.id,
            accessToken: data.accessToken,
            expiresAt: data.expiresAt,
            refreshToken: data.refreshToken,
            refreshTokenExpiresAt: data.refreshTokenExpiresAt,
            scope,
            userID: user.id,
            workspaceID: workspace.id,
          },
          include: { user: true },
        });

        const deletedTikTokIntegrations =
          await prisma.tikTokIntegration.findMany({
            where: {
              workspaceID: workspace.id,
              accountID: account.id,
              deletedTimeStamp: {
                not: 0,
              },
            },
          });

        await tx.post.updateMany({
          where: {
            tikTokIntegrationID: {
              in: deletedTikTokIntegrations.map(
                (integration) => integration.id
              ),
            },
          },
          data: {
            tikTokIntegrationID: integration.id,
          },
        });

        posthog.capture({
          distinctId: user.id,
          event: POSTHOG_EVENTS.socialAccount.connected,
          properties: {
            channel: "TikTok",
          },
          groups: {
            workspace: workspace.id,
            company: workspace.company.id,
          },
        });

        await slack.newConnectedAccount({
          username,
          channel: "TikTok",
          url: `https://www.tiktok.com/@${username}/`,
          workspace,
          connectedBy: integration.user,
        });

        if (workspace._count.tikTokIntegrations === 0) {
          await tx.postDefault.upsert({
            where: {
              workspaceID: workspace.id,
            },
            create: {
              workspaceID: workspace.id,
              tikTokIntegrationID: integration.id,
            },
            update: {
              tikTokIntegrationID: integration.id,
            },
          });
        }
      });
    }

    await completeOnboardingTask({
      workspaceID: workspace.id,
      userID: user.id,
      taskType: "CONNECT_ACCOUNT",
      state: "COMPLETED",
    });

    const params = new URLSearchParams({
      account_name: `@${tikTokAccount.username}`,
      channel: "TikTok",
      flow: accountID
        ? "reconnect_account"
        : tikTokIntegration
          ? "existing_account"
          : "new_account",
    });

    if (referrer.includes("/settings/profiles")) {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/profiles?${params}`
      );
    } else {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/tiktok?${params}`
      );
    }
  } catch (error) {
    console.error("TikTok auth error:", error);
    return handleError(res, "Error connecting TikTok account", referrer);
  }
}
