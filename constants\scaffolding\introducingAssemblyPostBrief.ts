const introducingAssemblyPostBrief = {
  root: {
    type: "root",
    format: "",
    indent: 0,
    version: 1,
    children: [
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "Dimensions: 1080x1080 ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [],
        direction: null,
      },
      {
        tag: "ul",
        type: "list",
        start: 1,
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            type: "listitem",
            value: 1,
            format: "",
            indent: 0,
            version: 1,
            children: [
              {
                mode: "normal",
                text: "Product GIF ",
                type: "text",
                style: "",
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
            direction: "ltr",
          },
          {
            type: "listitem",
            value: 2,
            format: "",
            indent: 0,
            version: 1,
            children: [
              {
                mode: "normal",
                text: "CALENDAR Static Asset",
                type: "text",
                style: "",
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
            direction: "ltr",
          },
          {
            type: "listitem",
            value: 3,
            format: "",
            indent: 0,
            version: 1,
            children: [
              {
                mode: "normal",
                text: "APPROVALS Static Asset ",
                type: "text",
                style: "",
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
            direction: "ltr",
          },
        ],
        listType: "bullet",
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [],
        direction: null,
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "References: ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
          {
            rel: null,
            url: "https://twitter.com/jordihays/status/1577332820027985921",
            type: "autolink",
            format: "",
            indent: 0,
            target: null,
            version: 1,
            children: [
              {
                mode: "normal",
                text: "https://twitter.com/jordihays/status/1577332820027985921",
                type: "text",
                style: "",
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
            direction: "ltr",
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [],
        direction: null,
      },
    ],
    direction: "ltr",
  },
};

export default introducingAssemblyPostBrief;
