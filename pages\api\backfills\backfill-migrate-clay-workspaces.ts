import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const clayWorkspaceID = "578e5c7d-533b-4fd4-a544-d3b708a8f6fc";
  const clayExecWorkspaceID = "6801b7f8-a0d3-4456-ab4b-09c2609e79db";

  const clayExecLinkedInProfileUsernames = [
    "kareemamin",
    "karan-parekh-4090455a",
    "vaanand",
    "clay-hq",
  ];

  const linkedInIntegrations = await db.linkedInIntegration.findMany({
    where: {
      account: {
        vanityName: {
          in: clayExecLinkedInProfileUsernames,
        },
      },
    },
  });

  const twitterIntegrations = await db.twitterIntegration.findMany({
    where: {
      account: {
        username: "clay_gtm",
      },
    },
  });

  const posts = await db.post.findMany({
    where: {
      OR: [
        {
          linkedInIntegrationID: {
            in: linkedInIntegrations.map((integration) => integration.id),
          },
        },
        {
          twitterIntegrationID: {
            in: twitterIntegrations.map((integration) => integration.id),
          },
        },
      ],
      workspaceID: clayWorkspaceID,
    },
  });

  await db.post.updateMany({
    where: {
      id: {
        in: posts.map((post) => post.id),
      },
    },
    data: {
      workspaceID: clayExecWorkspaceID,
    },
  });

  await db.scheduledPost.updateMany({
    where: {
      postID: {
        in: posts.map((post) => post.id),
      },
    },
    data: {
      workspaceID: clayExecWorkspaceID,
    },
  });

  await db.linkedInIntegration.updateMany({
    where: {
      workspaceID: clayWorkspaceID,
      account: {
        vanityName: {
          in: clayExecLinkedInProfileUsernames,
        },
      },
    },
    data: {
      workspaceID: clayExecWorkspaceID,
    },
  });

  const remainingLinkedInIntegrations = await db.linkedInIntegration.findMany({
    where: {
      workspaceID: clayWorkspaceID,
    },
  });

  await db.twitterIntegration.updateMany({
    where: {
      id: {
        in: twitterIntegrations.map((integration) => integration.id),
      },
    },
    data: {
      workspaceID: clayExecWorkspaceID,
    },
  });
  const integrationsToAdd = remainingLinkedInIntegrations.map((integration) => {
    const { id, ...rest } = integration;
    return {
      ...rest,
      type: "Engagement",
      workspaceID: clayExecWorkspaceID,
    };
  });

  const createdLinkedInIntegrations = await db.linkedInIntegration.createMany({
    data: remainingLinkedInIntegrations.map((integration) => ({
      accessToken: integration.accessToken,
      accessTokenExpiresAt: integration.accessTokenExpiresAt,

      refreshToken: integration.refreshToken,
      refreshTokenExpiresAt: integration.refreshTokenExpiresAt,

      isReintegrationRequired: integration.isReintegrationRequired,
      type: "Engagement",
      engagementPermissions: integration.engagementPermissions,

      scope: integration.scope,
      role: integration.role,
      state: integration.state,

      integratorAccountID: integration.integratorAccountID,
      accountID: integration.accountID,

      workspaceID: clayExecWorkspaceID,

      createdByID: integration.createdByID,
    })),
    skipDuplicates: true,
  });

  const execWorkspaceLinkedInIntegrations =
    await db.linkedInIntegration.findMany({
      where: {
        workspaceID: clayExecWorkspaceID,
      },
    });

  const integrationMapping = new Map();

  remainingLinkedInIntegrations.forEach((integration) => {
    const execWorkspaceIntegration = execWorkspaceLinkedInIntegrations.find(
      (execIntegration) => execIntegration.accountID === integration.accountID
    );
    if (execWorkspaceIntegration) {
      integrationMapping.set(integration.id, execWorkspaceIntegration.id);
    }
  });

  for await (const oldIntegration of remainingLinkedInIntegrations) {
    const newIntegrationID = integrationMapping.get(oldIntegration.id);
    if (!newIntegrationID) {
      continue;
    }

    await db.linkedInPostEngagement.updateMany({
      where: {
        integrationID: oldIntegration.id,
        content: {
          post: {
            workspaceID: clayExecWorkspaceID,
          },
        },
      },
      data: {
        integrationID: newIntegrationID,
      },
    });
  }

  res.status(200).send({
    success: true,
  });
};

export default handler;
