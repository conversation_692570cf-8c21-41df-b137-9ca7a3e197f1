import {
  ArrowsRightLeftIcon,
  BoltIcon,
  ChartBarSquareIcon,
  Cog6ToothIcon,
  EllipsisVerticalIcon,
  GiftIcon,
  IdentificationIcon,
  KeyIcon,
  LightBulbIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  QuestionMarkCircleIcon,
  Square3Stack3DIcon,
  UserPlusIcon,
} from "@heroicons/react/24/outline";
import { useMounted } from "@mantine/hooks";
import classNames from "classnames";
import { DateTime } from "luxon";
import Link from "next/link";
import { useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import ReactHotkeys from "react-hot-keys";
import { toast } from "sonner";
import { Kbd, Label, TextInput } from "~/components";
import DropdownMenu from "~/components/ui/DropdownMenu";
import Sidebar from "~/components/ui/Sidebar";
import WHITELABELED_AGENCIES from "~/constants/whitelabledAgencies";
import { useFilters, useUser, useWorkspace } from "~/providers";
import { usePosts } from "~/providers/PostProvider";
import { getKeys, openConfirmationModal, trpc } from "~/utils";
import KbdTooltip from "../KbdTooltip";
import { NewPostModal } from "../posts";
import GettingStartedCard from "./GettingStartedCard";
import MainSidebarContent, { Group } from "./MenuSidebarContent";
import TrialCard from "./TrialCard";
import WorkspaceSwitcher from "./WorkspaceSwitcher";

type Props = {
  setCommandPalletteOpen: (open: boolean) => void;
};

export const MainSidebar = ({ setCommandPalletteOpen }: Props) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();

  const { currentWorkspace } = useWorkspace();
  const hasMounted = useMounted();
  const { user } = useUser();

  const companyID = currentWorkspace?.company.id;
  const logoUrl =
    companyID != null
      ? WHITELABELED_AGENCIES[companyID as keyof typeof WHITELABELED_AGENCIES]
          ?.logo || "/logos/logo_mark_and_text.svg"
      : "/logos/logo_mark_and_text.svg";

  const shouldHideAI =
    companyID != null
      ? WHITELABELED_AGENCIES[companyID as keyof typeof WHITELABELED_AGENCIES]
          ?.hideAI || false
      : false;

  const { filters, setFilters } = useFilters();
  const [editingFilter, setEditingFilter] = useState<
    (typeof filters)[0] | null
  >(null);

  const { setNewPost } = usePosts();

  const [newPostModalOpen, setNewPostModalOpen] = useState<boolean>(false);

  const pendingApprovalsQuery = trpc.approval.getPending.useQuery(undefined);
  const notificationsQuery = trpc.knock.getMessages.useQuery();
  const approvalsNeeded = pendingApprovalsQuery.data?.approvalsNeeded || [];

  const filtersQuery = trpc.filter.getAll.useQuery();
  useEffect(() => {
    if (filtersQuery.data) {
      setFilters(filtersQuery.data.filters);
    }
  }, [filtersQuery.data?.filters]);

  const updateFilterMutation = trpc.filter.update.useMutation({
    onSuccess: (data) => {
      setFilters(data.filters);
      toast.success("Filter Updated", {
        description: "The filter name been updated",
      });
      setEditingFilter(null);
    },
  });

  const deleteFitlerMutation = trpc.filter.delete.useMutation();

  const mostRecentlyUsedQuery = trpc.post.getMostRecentlyUsed.useQuery(
    undefined,
    {
      enabled: !!currentWorkspace,
    }
  );

  const numApprovals = approvalsNeeded.length;
  const messages = notificationsQuery.data?.messages;
  const numUnreadNotifications = messages
    ? messages.filter((message) => message.read_at == null).length
    : 0;

  const notificationItems = useMemo(
    () => [
      {
        name: "Inbox",
        icon: (
          <div
            className={classNames(
              "font-eyebrow-sm flex h-4 w-4 items-center justify-center rounded-full text-white",
              {
                "bg-light-gray": numUnreadNotifications === 0,
                "bg-secondary text-secondary-light":
                  numUnreadNotifications !== 0,
              }
            )}
          >
            {numUnreadNotifications}
          </div>
        ),
        href: "/inbox",
      },
      {
        name: "Approvals",
        icon: (
          <div
            className={classNames(
              "font-eyebrow-sm flex h-4 w-4 items-center justify-center rounded-full text-white",
              {
                "bg-light-gray": numApprovals === 0,
                "bg-secondary text-secondary-light": numApprovals !== 0,
              }
            )}
          >
            {numApprovals}
          </div>
        ),
        href: "/approvals",
      },
    ],
    [numUnreadNotifications, numApprovals]
  );

  const postItems: Group = useMemo(() => {
    return {
      name: "Posts",
      items: [
        ...(shouldHideAI
          ? []
          : [
              {
                name: "AI Draft Generator",
                icon: <BoltIcon className="h-4 w-4" />,
                href: "/prompts",
                onMouseEnter: () => {
                  trpcUtils.prompt.getAssemblyPrompts.prefetch();
                },
              },
            ]),
        {
          name: "All Posts",
          icon: <Square3Stack3DIcon className="h-4 w-4" />,
          href: "/",
          isActiveFn: (pathname) =>
            pathname === "/authed" && router.query.id == null,
          onMouseEnter: () => {
            trpcUtils.post.getMany.prefetch();
            trpcUtils.label.getAll.prefetch();
            trpcUtils.campaign.getAll.prefetch();
          },
          subItems: {
            name: "Saved Filters",
            defaultOpen: false,
            items: hasMounted
              ? filters.map((filter) => {
                  const paramFilters: { [key: string]: string } = {};

                  const query = JSON.parse(JSON.stringify(filter.query));

                  getKeys(query).forEach((key) => {
                    if (query[key].length) {
                      paramFilters[key.toString()] = query[key].join(",");
                    }
                  });

                  const params = new URLSearchParams({
                    ...paramFilters,
                    id: filter.id,
                  });

                  const isActive = router.query.id === filter.id;

                  return (
                    <Sidebar.MenuSubItem
                      key={filter.name}
                      onMouseEnter={() => {
                        trpcUtils.post.getMany.prefetch();
                      }}
                    >
                      <Sidebar.MenuSubButton asChild={true}>
                        <div className="group/filter flex items-center justify-between">
                          <Link
                            href={`/?${params}`}
                            className={classNames(
                              "hover:text-basic transition-colors",
                              {
                                "text-basic font-medium": isActive,
                              }
                            )}
                          >
                            <div className="max-w-[120px] overflow-hidden text-ellipsis">
                              {filter.name}
                            </div>
                          </Link>
                          <DropdownMenu.Root>
                            <DropdownMenu.Trigger>
                              <div className="bg-surface hover:bg-lightest-gray aspect-square rounded-full rounded-md p-0.5 text-black opacity-0 transition-all group-hover/filter:opacity-100">
                                <EllipsisVerticalIcon className="h-4 w-4" />
                              </div>
                            </DropdownMenu.Trigger>
                            <DropdownMenu.Content>
                              <DropdownMenu.Group>
                                <DropdownMenu.Sub>
                                  <DropdownMenu.SubTrigger icon="PencilSquareIcon">
                                    Edit Name
                                  </DropdownMenu.SubTrigger>
                                  <DropdownMenu.SubContent>
                                    <Label value="Name">
                                      <TextInput
                                        value={
                                          editingFilter?.name || filter.name
                                        }
                                        autoFocus={true}
                                        onFocus={() => {
                                          setEditingFilter(filter);
                                        }}
                                        onChange={(value) => {
                                          if (!editingFilter) {
                                            setEditingFilter({
                                              ...filter,
                                              name: value,
                                            });
                                          } else {
                                            setEditingFilter({
                                              ...editingFilter,
                                              name: value,
                                            });
                                          }
                                        }}
                                        onBlur={() => {
                                          const newName = editingFilter?.name;
                                          if (
                                            filter.name !== newName &&
                                            newName != null
                                          ) {
                                            updateFilterMutation.mutate({
                                              id: filter.id,
                                              name: newName,
                                            });
                                          }
                                        }}
                                        onEnterPress={() => {
                                          const newName = editingFilter?.name;
                                          if (
                                            filter.name !== newName &&
                                            newName != null
                                          ) {
                                            updateFilterMutation.mutate({
                                              id: filter.id,
                                              name: newName,
                                            });
                                          }
                                        }}
                                      />
                                    </Label>
                                  </DropdownMenu.SubContent>
                                </DropdownMenu.Sub>
                                <DropdownMenu.Item
                                  icon="TrashIcon"
                                  intent="danger"
                                  onClick={() => {
                                    openConfirmationModal({
                                      title: `Delete "${filter.name}" View?`,
                                      body: "This view will be removed from the sidebar. This action cannot be undone.",
                                      primaryButton: {
                                        label: "Delete Filter",
                                        variant: "danger",
                                        onClick: () => {
                                          deleteFitlerMutation.mutate({
                                            id: filter.id,
                                          });
                                        },
                                      },
                                    });
                                  }}
                                >
                                  Delete Saved View
                                </DropdownMenu.Item>
                              </DropdownMenu.Group>
                            </DropdownMenu.Content>
                          </DropdownMenu.Root>
                        </div>
                      </Sidebar.MenuSubButton>
                    </Sidebar.MenuSubItem>
                  );
                })
              : [],
          },
        },
        {
          name: "Ideas",
          icon: <LightBulbIcon className="h-4 w-4" />,
          href: "/ideas",
          onMouseEnter: () => {
            trpcUtils.idea.getAll.prefetch();
          },
        },
      ],
    };
  }, [editingFilter, router.query.id, filters, shouldHideAI]);

  const isTwitterAnalyticsEnabled =
    currentWorkspace?.subscription?.isTwitterAnalyticsEnabled;

  const trialEndsAt = !!currentWorkspace?.company?.trialEndsAt
    ? DateTime.fromJSDate(new Date(currentWorkspace.company.trialEndsAt))
    : null;
  const hoursRemaining = trialEndsAt
    ? trialEndsAt.diffNow("hours").hours
    : null;
  const daysRemaining = hoursRemaining
    ? Math.max(Math.round(hoursRemaining / 24), 1)
    : null;

  const analyticsItems: Group = useMemo(() => {
    return {
      name: "Analytics",
      items: [
        {
          name: "Analytics",
          icon: <ChartBarSquareIcon className="h-4 w-4" />,
          href: "/analytics",
          onMouseEnter: () => {
            trpcUtils.analytics.getAccountStats.prefetch({
              startDate: DateTime.now()
                .minus({ weeks: 1 })
                .startOf("day")
                .toJSDate(),
              endDate: DateTime.now().endOf("day").toJSDate(),
            });
          },
          subItems: {
            name: "Channels",
            defaultOpen: false,
            items: [
              {
                name: "LinkedIn",
                href: "/analytics/linkedin",
                onMouseEnter: () => {
                  trpcUtils.analytics.linkedin.getAccountPosts.prefetch();
                },
              },
              {
                name: "Instagram",
                href: "/analytics/instagram",
                onMouseEnter: () => {
                  trpcUtils.analytics.instagram.getAccountPosts.prefetch();
                },
              },
              {
                name: "Facebook",
                href: "/analytics/facebook",
                onMouseEnter: () => {
                  trpcUtils.analytics.facebook.getAccountPosts.prefetch();
                },
              },
              {
                name: "Twitter",
                href: isTwitterAnalyticsEnabled
                  ? "/analytics/twitter"
                  : "/analytics/twitter-basic",
                onMouseEnter: () => {
                  trpcUtils.analytics.twitter.getStatsAndTweets.prefetch();
                },
              },
            ],
          },
        },
        {
          name: "Leads (LinkedIn)",
          showNewBadge: !currentWorkspace?.isLeadsScrapingEnabled,
          icon: <IdentificationIcon className="h-4 w-4" />,
          href: currentWorkspace?.isLeadsScrapingEnabled
            ? "/linkedin-leads"
            : "/leads",
        },
      ],
    };
  }, [isTwitterAnalyticsEnabled]);

  const footerItems: Group = useMemo(() => {
    return {
      name: "Workspace",
      items: [
        ...(user?.isAdmin
          ? [
              {
                name: "Admin Settings",
                icon: <KeyIcon className="h-4 w-4" />,
                href: "/admin/settings/workspaces",
                onMouseEnter: () => {
                  trpcUtils.user.currentUser.prefetch();
                  trpcUtils.admin.getWorkspaces.prefetch();
                },
              },
            ]
          : []),
        {
          name: "Referrals",
          icon: <GiftIcon className="h-4 w-4" />,
          href: "/referrals",
          onMouseEnter: () => {
            trpcUtils.referral.getAll.prefetch();
          },
        },
        {
          name: "Settings",
          icon: <Cog6ToothIcon className="h-4 w-4" />,
          href: "/settings/members",
          onMouseEnter: () => {
            trpcUtils.workspace.getUsers.prefetch();
            trpcUtils.invite.getAllPending.prefetch();
          },
        },
        {
          name: "Invite",
          icon: <UserPlusIcon className="h-4 w-4" />,
          href: "/settings/members",
          onMouseEnter: () => {
            trpcUtils.invite.getAllPending.prefetch();
          },
        },
        {
          name: "Integrations",
          icon: <ArrowsRightLeftIcon className="h-4 w-4" />,
          href: "/settings/profiles",
          onMouseEnter: () => {
            trpcUtils.workspace.getSchedulingProfiles.prefetch();
          },
        },
        {
          name: "Help",
          icon: <QuestionMarkCircleIcon className="h-4 w-4" />,
          href: "https://help.meetassembly.com",
          target: "_blank",
          rel: "noopener noreferrer",
        },
      ],
    };
  }, [user?.isAdmin]);

  const sidebarGroups: Group[] = useMemo(
    () => [
      {
        name: "Notifications",
        items: notificationItems,
      },
      postItems,
      analyticsItems,
      footerItems,
    ],
    [notificationItems, postItems, analyticsItems]
  );

  return (
    <Sidebar.Root>
      <Sidebar.Header className="px-5 pt-6">
        <div className="flex w-full items-center justify-between">
          <Link href="/" className="my-auto flex items-center gap-2">
            <img src={logoUrl} className="h-5 w-auto" />
          </Link>
          {hasMounted && <WorkspaceSwitcher />}
        </div>
        <div className="flex items-center justify-center gap-2">
          <ReactHotkeys
            keyName="P"
            onKeyDown={(keyName) => {
              if (keyName.toLowerCase() === "p") {
                setNewPostModalOpen(true);
              }
            }}
          >
            <button
              className="text-basic flex w-32 items-center justify-between rounded-md px-1.5 py-1.5 shadow-sm transition-shadow hover:shadow-lg"
              onClick={() => setNewPostModalOpen(true)}
            >
              <div className="flex items-center gap-0.5">
                <PlusIcon className="h-4 w-4" />
                <div className="font-body-sm">New Post</div>
              </div>
              <Kbd>P</Kbd>
            </button>
          </ReactHotkeys>
          <KbdTooltip
            label="Search"
            hotKeyLabel="⌘+K"
            hotKeys={["cmd+k", "ctrl+k"]}
            onKeyPress={() => setCommandPalletteOpen(true)}
          >
            <button
              className="text-basic flex w-fit items-center justify-between rounded-md p-2 shadow-sm transition-shadow hover:shadow-lg"
              onClick={() => setCommandPalletteOpen(true)}
            >
              <MagnifyingGlassIcon className="h-4 w-4" />
            </button>
          </KbdTooltip>
        </div>
      </Sidebar.Header>
      <MainSidebarContent groups={sidebarGroups} />
      <Sidebar.Footer>
        <Sidebar.Group>
          <GettingStartedCard />
        </Sidebar.Group>
        <Sidebar.Group>
          {trialEndsAt && (
            <TrialCard
              trialPlanName={
                currentWorkspace?.subscription
                  ? `${currentWorkspace?.subscription?.tier} Plan trial`
                  : "Free Trial"
              }
              daysRemaining={daysRemaining}
              hoursRemaining={hoursRemaining}
            />
          )}
        </Sidebar.Group>
      </Sidebar.Footer>
      <NewPostModal
        open={newPostModalOpen}
        setOpen={setNewPostModalOpen}
        defaultPublishDate={new Date()}
        defaultChannel={mostRecentlyUsedQuery.data?.channel || undefined}
        onCreateSuccess={(post) => {
          setNewPost({ ...post, approvals: [] });
          toast.success("Post Created", {
            id: post.id,
            description: "Click to view the post",
            action: {
              label: "View Post",
              onClick: () => router.push(`/posts/${post.id}`),
            },
          });
        }}
      />
    </Sidebar.Root>
  );
};
