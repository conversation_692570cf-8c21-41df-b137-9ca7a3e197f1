import { useRouter } from "next/router";
import { useEffect } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { Divider, TextLink } from "~/components";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import { useWindowTitle } from "~/hooks";
import { openIntercomChat } from "~/utils";
import { setCookie } from "~/utils/cookies";

export const INSTAGRAM_SCOPES = [
  "instagram_business_basic",
  "instagram_business_manage_comments",
  "instagram_business_content_publish",
  "instagram_business_manage_insights",
];

type Props = {
  type: "scheduling";
};

const handleInstagramLogin = async (accountID?: string) => {
  const state = uuidv4();

  const params = new URLSearchParams({
    enable_fb_login: "0",
    force_authentication: "1",
    client_id: `${process.env.NEXT_PUBLIC_INSTAGRAM_CLIENT_ID}`,
    redirect_uri: `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/instagram/callback`,
    response_type: "code",
    scope: INSTAGRAM_SCOPES.join(","),
    state,
  });

  setCookie(
    "INSTAGRAM_INTEGRATION",
    {
      state,
      accountID,
      referrer: window.location.pathname,
    },
    {
      path: "/",
      secure: true,
      sameSite: "lax",
      maxAge: 5 * 60, // 5 minutes in seconds
    }
  );

  window.location.href = `https://www.instagram.com/oauth/authorize?${params.toString()}`;
};

const InstagramConnectPage = ({ type }: Props) => {
  useWindowTitle("Instagram Integration");
  const router = useRouter();

  useEffect(() => {
    if (!router.isReady) return;

    const query = router.query;
    router.replace(router.pathname.replace("authed/", ""));

    if (query.error_message) {
      toast.error("Could not connect profile", {
        description: query.error_message as string,
      });
    }
  }, [router.isReady]);

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      <ConnectProfile.Root>
        <ConnectProfile.Header
          channel="Instagram"
          description="Connect your Instagram account to Assembly."
        />
        <ConnectProfile.Content>
          <Button size="sm" onClick={() => handleInstagramLogin()}>
            Connect Instagram
          </Button>
        </ConnectProfile.Content>
        <Divider className="my-2 md:my-4" />
        <ConnectProfile.Footer>
          <div className="space-y-1.5">
            <p className="font-medium">
              Important: Login to the correct Instagram account before
              connecting.
            </p>
            <p className="text-medium-dark-gray">
              Before connecting, login to the account you want to connect on{" "}
              <TextLink
                href="https://www.instagram.com"
                value="instagram.com"
              />
              .
            </p>
          </div>
          <div className="space-y-1.5">
            <p className="font-medium">Need Help?</p>
            <p className="text-medium-dark-gray">
              Check out our help center article for a video walkthrough on how
              to connect. If you're still having issues, please{" "}
              <Button
                size="sm"
                variant="link"
                onClick={() => {
                  openIntercomChat(
                    "I'm having trouble connecting my Instagram profile: {details here}"
                  );
                }}
              >
                chat with us
              </Button>
              .
            </p>
          </div>
        </ConnectProfile.Footer>
      </ConnectProfile.Root>
    </SettingsLayout.Root>
  );
};

export default InstagramConnectPage;
export { handleInstagramLogin };
