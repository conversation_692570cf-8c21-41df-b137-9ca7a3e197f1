
import { useState } from "react";
import { toast } from "sonner";
import { Tooltip } from "~/components";
import MembersList, { User } from "~/components/app/members/MembersList";
import EditWorkspacesModal from "~/components/app/members/EditWorkspacesModal";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import DropdownMenu from "~/components/ui/DropdownMenu";
import Avatar from "~/components/ui/Avatar";
import Skeleton from "~/components/ui/Skeleton";
import { useWindowTitle } from "~/hooks";
import { InformationCircleIcon, PencilIcon, TrashIcon, EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import { openConfirmationModal, handleTrpcError, trpc } from '~/utils';


const AgencyMembers = () => {
  useWindowTitle("Agency Members");

  const [editWorkspacesModalOpen, setEditWorkspacesModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const membersQuery = trpc.agency.getMembers.useQuery(undefined, {
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  const removeUserMutation = trpc.agency.removeUser.useMutation({
    onSuccess: () => {
      toast.success("User removed successfully");
      membersQuery.refetch();
    },
    onError: (error) => {
      handleTrpcError({ title: "Error Removing User", error });
    },
  });

  const handleEditWorkspaceAccess = (user: User) => {
    setSelectedUser(user);
    setEditWorkspacesModalOpen(true);
  };

  const handleRemoveUser = async (user: User) => {
    openConfirmationModal({
      title: `Remove ${user.firstName} ${user.lastName}?`,
      body: `Are you sure you want to remove ${user.firstName} ${user.lastName}?`,
      cancelButton: {
        label: "Cancel, don't remove",
      },
      primaryButton: {
        label: `Remove User`,
        variant: "danger",
        onClick: () => {
          removeUserMutation.mutate({ userID: user.id });
        },
      },
    });

  };

  const agencyUsers = membersQuery.data?.agencyUsers || [];
  const clientUsers = membersQuery.data?.clientUsers || [];
  const pendingUsers = membersQuery.data?.pendingUsers || [];
  const isEmpty = agencyUsers.length === 0 && clientUsers.length === 0 && pendingUsers.length === 0;
  const isLoading = membersQuery.isLoading;

  const LoadingSkeleton = () => (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-center justify-between w-full gap-6">
          <div className="flex items-center gap-3 flex-1">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex flex-col gap-1">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-48" />
            </div>
          </div>
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-5 w-5" />
        </div>
      ))}
    </div>
  );

  return (
    <SettingsLayout.Root>
      <SettingsLayout.Header>
        <SettingsLayout.Title>Agency Members</SettingsLayout.Title>
        <SettingsLayout.Description>
          Select which workspaces each member of the agency has access to
        </SettingsLayout.Description>
      </SettingsLayout.Header>
      <SettingsLayout.Sections>

        <SettingsLayout.Section>
          <div className="space-y-8 pt-5">
            {/* Loading State */}
            {isLoading && (
              <div className="space-y-8">
                <div>
                  <SettingsLayout.SectionTitle>
                    <div className="flex items-center gap-2 font-medium">
                      <div>Agency Users</div>
                      <Tooltip value="Agency users are any users that are part of more than 1 of your Client workspace(s)." side="bottom" align="start">
                        <InformationCircleIcon className="h-4 w-4 text-medium-dark-gray" />
                      </Tooltip>
                    </div>
                  </SettingsLayout.SectionTitle>
                  <LoadingSkeleton />
                </div>
                <div>
                  <SettingsLayout.SectionTitle>
                    Client Users
                  </SettingsLayout.SectionTitle>
                  <LoadingSkeleton />
                </div>
              </div>
            )}

            {/* Agency Users Section */}
            {!isLoading && agencyUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  <div className="flex items-center gap-2 font-medium">
                    <div>Agency Users</div>
                    <Tooltip value="Agency users are any users that are part of more than 1 of your Client workspace(s)." side="bottom" align="start">
                      <InformationCircleIcon className="h-4 w-4 text-medium-dark-gray" />
                    </Tooltip>
                  </div>
                </SettingsLayout.SectionTitle>
                {agencyUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} />
                    <div className="text-medium-dark-gray font-body-sm ml-auto">
                      {user.workspaceCount === 1
                        ? "1 workspace"
                        : `${user.workspaceCount} workspaces`}
                    </div>
                    <MembersList.MemberActions>
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <EllipsisHorizontalIcon
                            className="h-5 w-5 transition-colors font-bold stroke-2 text-medium-dark-gray cursor-pointer"
                          />
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Group>
                            <DropdownMenu.Item
                              onClick={() => handleEditWorkspaceAccess(user)}
                              intent="none"
                            >
                              <div className="flex items-center gap-2">
                                <PencilIcon className="h-4 w-4 stroke-2" />
                                Edit Workspace Access
                              </div>
                            </DropdownMenu.Item>
                            <DropdownMenu.Item
                              onClick={() => handleRemoveUser(user)}
                              intent="danger"
                            >
                              <div className="flex items-center gap-2">
                                <TrashIcon className="h-4 w-4 stroke-2" />
                                Remove User
                              </div>
                            </DropdownMenu.Item>
                          </DropdownMenu.Group>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}

                {/* Pending Users - shown inline with different styling */}
                {pendingUsers.map((user) => (
                  <MembersList.Member key={`pending-${user.id}`}>
                <div className="flex items-center gap-3 flex-1">
                  <Avatar.Root className="h-10 w-10">
                    <Avatar.Fallback className="bg-lightest-gray text-medium-gray">
                      {`${user.firstName?.charAt(0) || ''}${user.lastName?.charAt(0) || ''}`}
                    </Avatar.Fallback>
                  </Avatar.Root>
                  <div className="flex flex-col">
                    <div className="font-medium text-medium-gray">
                      Pending
                    </div>
                    <div className="text-medium-dark-gray text-sm">
                      {user.email}
                    </div>
                  </div>
                </div>
                <div className="text-medium-dark-gray font-body-sm ml-auto">
                  {user.workspaceCount === 1
                    ? "1 workspace"
                    : `${user.workspaceCount} workspaces`}
                </div>
                <MembersList.MemberActions>
                  <DropdownMenu.Root>
                    <DropdownMenu.Trigger>
                      <EllipsisHorizontalIcon
                        className="h-5 w-5 transition-colors font-bold stroke-2 text-medium-dark-gray cursor-pointer"
                      />
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content align="end">
                      <DropdownMenu.Group>
                        <DropdownMenu.Item
                          onClick={() => handleRemoveUser(user)}
                          intent="danger"
                        >
                          <div className="flex items-center gap-2">
                            <TrashIcon className="h-4 w-4 stroke-2" />
                            Remove User
                          </div>
                        </DropdownMenu.Item>
                      </DropdownMenu.Group>
                    </DropdownMenu.Content>
                  </DropdownMenu.Root>
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}

            {/* Client Users Section */}
            {!isLoading && clientUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  Client Users
                </SettingsLayout.SectionTitle>
                {clientUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} />
                    <div className="text-medium-dark-gray font-body-sm ml-auto">
                      {user.workspaceCount === 1
                        ? "1 workspace"
                        : `${user.workspaceCount} workspaces`}
                    </div>
                    <MembersList.MemberActions>
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <EllipsisHorizontalIcon
                            className="h-5 w-5 transition-colors font-bold stroke-2 text-medium-dark-gray cursor-pointer"
                          />
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Group>
                            <DropdownMenu.Item
                              onClick={() => handleEditWorkspaceAccess(user)}
                              intent="none"
                            >
                              <div className="flex items-center gap-2">
                                <PencilIcon className="h-4 w-4 stroke-2" />
                                Edit Workspace Access
                              </div>
                            </DropdownMenu.Item>
                            <DropdownMenu.Item
                              onClick={() => handleRemoveUser(user)}
                              intent="danger"
                            >
                              <div className="flex items-center gap-2">
                                <TrashIcon className="h-4 w-4 stroke-2" />
                                Remove User
                              </div>
                            </DropdownMenu.Item>
                          </DropdownMenu.Group>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}

            {/* Empty State */}
            {!isLoading && isEmpty && (
              <div className="text-center py-12">
                <div className="text-medium-dark-gray">
                  No members found in your agency.
                </div>
              </div>
            )}
          </div>
        </SettingsLayout.Section>
      </SettingsLayout.Sections>

      {/* Edit Workspaces Modal */}
      <EditWorkspacesModal
        open={editWorkspacesModalOpen}
        setOpen={setEditWorkspacesModalOpen}
        user={selectedUser}
        onSuccess={() => {
          membersQuery.refetch();
          setSelectedUser(null);
        }}
      />
    </SettingsLayout.Root>
  );
};

export default AgencyMembers;
