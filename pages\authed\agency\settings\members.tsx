import {
  EllipsisHorizontalIcon,
  InformationCircleIcon,
  PencilIcon,
} from "@heroicons/react/24/outline";
import { useState } from "react";
import { Tooltip } from "~/components";
import EditWorkspacesModal from "~/components/app/members/EditWorkspacesModal";
import MembersList, { User } from "~/components/app/members/MembersList";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import Avatar from "~/components/ui/Avatar";
import DropdownMenu from "~/components/ui/DropdownMenu";
import Skeleton from "~/components/ui/Skeleton";
import { useWindowTitle } from "~/hooks";
import { useUser } from "~/providers";
import { trpc } from "~/utils";

const AgencyMembers = () => {
  useWindowTitle("Agency Members");

  const [editWorkspacesModalOpen, setEditWorkspacesModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const { user } = useUser();

  const membersQuery = trpc.agency.getMembers.useQuery(undefined, {
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  const handleEditWorkspaceAccess = (user: User) => {
    setSelectedUser(user);
    setEditWorkspacesModalOpen(true);
  };

  const agencyUsers = membersQuery.data?.agencyUsers || [];
  console.log('agencyUsers', agencyUsers);
  const clientUsers = membersQuery.data?.clientUsers || [];
  const pendingUsers = membersQuery.data?.pendingUsers || [];
  const sharedWorkspaces = membersQuery.data?.sharedWorkspaces || [];

  const isLoading = membersQuery.isLoading;

  const LoadingSkeleton = () => (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex w-full items-center justify-between gap-6">
          <div className="flex flex-1 items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex flex-col gap-1">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-48" />
            </div>
          </div>
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-5 w-5" />
        </div>
      ))}
    </div>
  );

  return (
    <SettingsLayout.Root>
      <SettingsLayout.Header>
        <SettingsLayout.Title>Agency Members</SettingsLayout.Title>
        <SettingsLayout.Description>
          Select which workspaces each member of the agency has access to
        </SettingsLayout.Description>
      </SettingsLayout.Header>
      <SettingsLayout.Sections>
        <SettingsLayout.Section>
          <div className="space-y-8 pt-5">
            {/* Loading State */}
            {isLoading && (
              <div className="space-y-8">
                <div>
                  <SettingsLayout.SectionTitle>
                    <div className="flex items-center gap-2 font-medium">
                      <div>Agency Users</div>
                      <Tooltip
                        value="Agency users are any users that are part of more than 1 of your Client workspace(s)."
                        side="bottom"
                        align="start"
                      >
                        <InformationCircleIcon className="text-medium-dark-gray h-4 w-4" />
                      </Tooltip>
                    </div>
                  </SettingsLayout.SectionTitle>
                  <LoadingSkeleton />
                </div>
                <div>
                  <SettingsLayout.SectionTitle>
                    Client Users
                  </SettingsLayout.SectionTitle>
                  <LoadingSkeleton />
                </div>
              </div>
            )}

            {/* Agency Users Section */}
            {!isLoading && agencyUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  <div className="flex items-center gap-2 font-medium">
                    <div>Agency Users</div>
                    <Tooltip
                      value="Agency users are any users that are part of more than 1 of your Client workspace(s)."
                      side="bottom"
                      align="start"
                    >
                      <InformationCircleIcon className="text-medium-dark-gray h-4 w-4" />
                    </Tooltip>
                  </div>
                </SettingsLayout.SectionTitle>
                {agencyUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} />
                    <Tooltip
                      value={
                        <div className="space-y-2">
                          <div className="text-sm font-medium text-black">
                            Our Shared Workspaces
                          </div>
                          {user.workspaces.map((workspace) => (
                            <div
                              key={workspace.id}
                              className="text-sm text-gray-600"
                            >
                              {workspace.name}
                            </div>
                          ))}
                        </div>
                      }
                      className="p-3"
                      side="bottom"
                      align="start"
                    >
                      <div className="text-medium-dark-gray font-body-sm ml-auto cursor-pointer">
                        {user.workspaces.length === 1
                          ? "1 workspace"
                          : `${user.workspaces.length} workspaces`}
                      </div>
                    </Tooltip>
                    <MembersList.MemberActions>
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <EllipsisHorizontalIcon className="hover:bg-basic/10 h-5 w-5 cursor-pointer rounded-sm bg-transparent stroke-2 p-0 font-bold text-black transition-colors" />
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Group>
                            <DropdownMenu.Item
                              onClick={() => handleEditWorkspaceAccess(user)}
                              intent="none"
                            >
                              <div className="flex items-center gap-2">
                                <PencilIcon className="h-4 w-4 stroke-2" />
                                Edit Workspace Access
                              </div>
                            </DropdownMenu.Item>
                          </DropdownMenu.Group>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}

                {/* Pending Users - shown inline with different styling */}
                {pendingUsers.map((user) => (
                  <MembersList.Member key={`pending-${user.id}`}>
                    <div className="flex flex-1 items-center gap-3">
                      <Avatar.Root className="h-10 w-10">
                        <Avatar.Fallback className="bg-lightest-gray text-medium-gray">
                          {/* {`${user.firstName?.charAt(0) || ''}${user.lastName?.charAt(0) || ''}`} */}
                          Pending
                        </Avatar.Fallback>
                      </Avatar.Root>
                      <div className="flex flex-col">
                        <div className="text-medium-gray font-medium">
                          Pending
                        </div>
                        <div className="text-medium-dark-gray text-sm">
                          {user.email}
                        </div>
                      </div>
                    </div>
                    <Tooltip
                      value={
                        <div className="space-y-2">
                          <div className="text-sm font-medium text-black">
                            Our Shared Workspaces
                          </div>
                          {user.workspaces.map((workspace) => (
                            <div
                              key={workspace.id}
                              className="text-sm text-gray-600"
                            >
                              {workspace.name}
                            </div>
                          ))}
                        </div>
                      }
                      className="p-3"
                      side="bottom"
                      align="start"
                    >
                      <div className="text-medium-dark-gray font-body-sm ml-auto cursor-pointer">
                        {user.workspaces.length === 1
                          ? "1 workspace"
                          : `${user.workspaces.length} workspaces`}
                      </div>
                    </Tooltip>
                    <MembersList.MemberActions>
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <EllipsisHorizontalIcon className="hover:bg-basic/10 h-5 w-5 cursor-pointer rounded-sm bg-transparent stroke-2 p-0 font-bold text-black transition-colors" />
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Group>
                            <DropdownMenu.Item
                              onClick={() => handleEditWorkspaceAccess(user)}
                              intent="none"
                            >
                              <div className="flex items-center gap-2">
                                <PencilIcon className="h-4 w-4 stroke-2" />
                                Edit Workspace Access
                              </div>
                            </DropdownMenu.Item>
                          </DropdownMenu.Group>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}

            {/* Client Users Section */}
            {!isLoading && clientUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  Client Users
                </SettingsLayout.SectionTitle>
                {clientUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} />
                    <Tooltip
                      value={
                        <div className="space-y-2">
                          <div className="text-sm font-medium text-black">
                            Our Shared Workspaces
                          </div>
                          {user.workspaces.map((workspace) => (
                            <div
                              key={workspace.id}
                              className="text-sm text-gray-600"
                            >
                              {workspace.name}
                            </div>
                          ))}
                        </div>
                      }
                      className="p-3"
                      side="bottom"
                      align="start"
                    >
                      <>
                        <div className="text-medium-dark-gray font-body-sm ml-auto cursor-pointer">
                          {user.workspaces.length === 1
                            ? "1 workspace"
                            : `${user.workspaces.length} workspaces`}
                        </div>
                      </>
                    </Tooltip>
                    <MembersList.MemberActions>
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <EllipsisHorizontalIcon className="hover:bg-basic/10 h-5 w-5 cursor-pointer rounded-sm bg-transparent stroke-2 p-0 font-bold text-black transition-colors" />
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Group>
                            <DropdownMenu.Item
                              onClick={() => handleEditWorkspaceAccess(user)}
                              intent="none"
                            >
                              <div className="flex items-center gap-2">
                                <PencilIcon className="h-4 w-4 stroke-2" />
                                Edit Workspace Access
                              </div>
                            </DropdownMenu.Item>
                          </DropdownMenu.Group>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}
          </div>
        </SettingsLayout.Section>
      </SettingsLayout.Sections>

      {/* Edit Workspaces Modal */}
      <EditWorkspacesModal
        open={editWorkspacesModalOpen}
        setOpen={setEditWorkspacesModalOpen}
        user={selectedUser}
        sharedWorkspaces={sharedWorkspaces}
        companyID={user?.agency?.id || ""}
        onSuccess={() => {
          membersQuery.refetch();
          setSelectedUser(null);
        }}
      />
    </SettingsLayout.Root>
  );
};

export default AgencyMembers;
