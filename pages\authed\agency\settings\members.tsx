
import { toast } from "sonner";
import { Tooltip } from "~/components";
import MembersList, { User } from "~/components/app/members/MembersList";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { useWindowTitle } from "~/hooks";
import { InformationCircleIcon, PencilIcon, TrashIcon, EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import { openConfirmationModal } from '~/utils';

// Mock data for demonstration
const mockAgencyUsers: User[] = [
  {
    id: "1",
    firstName: "<PERSON>",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 5,
  },
  {
    id: "2",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 5,
  },
  {
    id: "3",
    firstName: "<PERSON>",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
];

const mockPendingUsers: User[] = [
  {
    id: "4",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 5,
  },
];

const mockClientUsers: User[] = [
  {
    id: "5",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
  {
    id: "6",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
  {
    id: "7",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
];

const workspaceOptions = [
  { key: "workspace1", value: "workspace1", label: "Workspace 1" },
  { key: "workspace2", value: "workspace2", label: "Workspace 2" },
  { key: "workspace3", value: "workspace3", label: "Workspace 3" },
  { key: "workspace4", value: "workspace4", label: "Workspace 4" },
];





const AgencyMembers = () => {
  useWindowTitle("Agency Members");



  // Handlers for member actions
  const handleEditWorkspaceAccess = (user: User) => {
    toast.info(`Edit workspace access for ${user.firstName} ${user.lastName}`);
    // TODO: Implement edit workspace access functionality
  };

  const handleRemoveUser = async (user: User) => {
    openConfirmationModal({
      title: `Remove ${user.firstName} ${user.lastName}?`,
      body: `Are you sure you want to remove ${user.firstName} ${user.lastName}?`,
      cancelButton: {
        label: "Cancel, don't remove",
      },
      primaryButton: {
        label: `Remove User`,
        variant: "danger",
        onClick: () => {
          // TODO: Implement remove user functionality with toast.promise
          toast.success(`${user.firstName} ${user.lastName} has been removed`);
        },
      },
    });

  };




  return (
    <SettingsLayout.Root>
      <SettingsLayout.Header>
        <SettingsLayout.Title>Agency Members</SettingsLayout.Title>
        <SettingsLayout.Description>
          Select which workspaces each member of the agency has access to
        </SettingsLayout.Description>
      </SettingsLayout.Header>
      <SettingsLayout.Sections>

        <SettingsLayout.Section>
          <div className="space-y-8 pt-5">
            {/* Agency Users Section */}
            {mockAgencyUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  <div className="flex items-center gap-2 font-medium">
                    <div>Agency Users</div>
                    <Tooltip value="Agency users are any users that are part of more than 1 of your Client workspace(s)." side="bottom" align="start">
                      <InformationCircleIcon className="h-4 w-4 text-medium-dark-gray" />
                    </Tooltip>
                  </div>
                </SettingsLayout.SectionTitle>
                {mockAgencyUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
                    <MembersList.MemberActions>
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <EllipsisHorizontalIcon
                            className="h-5 w-5 transition-colors font-bold stroke-2 cursor-pointer"
                            style={{ color: '#0F172A' }}
                          />
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Group>
                            <DropdownMenu.Item
                              onClick={() => handleEditWorkspaceAccess(user)}
                              intent="none"
                            >
                              <div className="flex items-center gap-2">
                                <PencilIcon className="h-4 w-4 stroke-2" />
                                Edit Workspace Access
                              </div>
                            </DropdownMenu.Item>
                            <DropdownMenu.Item
                              onClick={() => handleRemoveUser(user)}
                              intent="danger"
                            >
                              <div className="flex items-center gap-2">
                                <TrashIcon className="h-4 w-4 stroke-2" />
                                Remove User
                              </div>
                            </DropdownMenu.Item>
                          </DropdownMenu.Group>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}

            {/* Pending Users Section */}
            {mockPendingUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  Pending
                </SettingsLayout.SectionTitle>
                {mockPendingUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
                    <MembersList.MemberActions>
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <EllipsisHorizontalIcon
                            className="h-5 w-5 transition-colors font-bold stroke-2 cursor-pointer"
                            style={{ color: '#0F172A' }}
                          />
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Group>
                            <DropdownMenu.Item
                              onClick={() => handleRemoveUser(user)}
                              intent="danger"
                            >
                              <div className="flex items-center gap-2">
                                <TrashIcon className="h-4 w-4 stroke-2" />
                                Remove User
                              </div>
                            </DropdownMenu.Item>
                          </DropdownMenu.Group>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}

            {/* Client Users Section */}
            {mockClientUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  Client Users
                </SettingsLayout.SectionTitle>
                {mockClientUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
                    <MembersList.MemberActions>
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <EllipsisHorizontalIcon
                            className="h-5 w-5 transition-colors font-bold stroke-2 cursor-pointer"
                            style={{ color: '#0F172A' }}
                          />
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Group>
                            <DropdownMenu.Item
                              onClick={() => handleEditWorkspaceAccess(user)}
                              intent="none"
                            >
                              <div className="flex items-center gap-2">
                                <PencilIcon className="h-4 w-4 stroke-2" />
                                Edit Workspace Access
                              </div>
                            </DropdownMenu.Item>
                            <DropdownMenu.Item
                              onClick={() => handleRemoveUser(user)}
                              intent="danger"
                            >
                              <div className="flex items-center gap-2">
                                <TrashIcon className="h-4 w-4 stroke-2" />
                                Remove User
                              </div>
                            </DropdownMenu.Item>
                          </DropdownMenu.Group>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}
          </div>
        </SettingsLayout.Section>
      </SettingsLayout.Sections>
    </SettingsLayout.Root>
  );
};

export default AgencyMembers;
