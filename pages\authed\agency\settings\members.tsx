import { useState } from "react";
import { toast } from "sonner";
import { Combobox, Label, TextInput } from "~/components";
import AgencyMembersList from "~/components/app/members/AgencyMembersList";
import { User } from "~/components/app/members/MembersList";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import Button from "~/components/ui/Button";
import { useWindowTitle } from "~/hooks";

// Mock data for demonstration
const mockAgencyUsers: User[] = [
  {
    id: "1",
    firstName: "<PERSON>",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 5,
  },
  {
    id: "2",
    firstName: "<PERSON>",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 5,
  },
  {
    id: "3",
    firstName: "<PERSON>",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
];

const mockPendingUsers: User[] = [
  {
    id: "4",
    firstName: "<PERSON>",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 5,
  },
];

const mockClientUsers: User[] = [
  {
    id: "5",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
  {
    id: "6",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
  {
    id: "7",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
];

const workspaceOptions = [
  { key: "workspace1", value: "workspace1", label: "Workspace 1" },
  { key: "workspace2", value: "workspace2", label: "Workspace 2" },
  { key: "workspace3", value: "workspace3", label: "Workspace 3" },
  { key: "workspace4", value: "workspace4", label: "Workspace 4" },
];

const AgencyMembers = () => {
  useWindowTitle("Agency Members");

  const [email, setEmail] = useState<string>("");
  const [selectedWorkspaces, setSelectedWorkspaces] = useState<any[]>([]);
  const [isInviting, setIsInviting] = useState(false);

  const handleInviteEmail = async () => {
    if (!email.trim()) {
      toast.error("Please enter an email address");
      return;
    }

    if (selectedWorkspaces.length === 0) {
      toast.error("Please select at least one workspace");
      return;
    }

    setIsInviting(true);
    try {
      // TODO: Implement actual invite functionality
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success(`Invitation sent to ${email}`);
      setEmail("");
      setSelectedWorkspaces([]);
    } catch (error) {
      toast.error("Failed to send invitation");
    } finally {
      setIsInviting(false);
    }
  };

  return (
    <SettingsLayout.Root>
      <SettingsLayout.Header>
        <SettingsLayout.Title>Agency Members</SettingsLayout.Title>
        <SettingsLayout.Description>
          Select which workspaces each member of the agency has access to
        </SettingsLayout.Description>
      </SettingsLayout.Header>
      <SettingsLayout.Sections>
        <SettingsLayout.Section>
          <div className="flex items-end gap-2">
            <div className="w-80">
              <Label value="Email">
                <TextInput
                  kind="outline"
                  value={email}
                  onChange={setEmail}
                  onEnterPress={handleInviteEmail}
                  placeholder="<EMAIL>"
                />
              </Label>
            </div>
            <div className="w-60">
              <Label value="Workspaces">
                <Combobox
                  placeholder="Select Workspaces"
                  options={workspaceOptions}
                  value={selectedWorkspaces}
                  onChange={setSelectedWorkspaces}
                  isMulti={true}
                />
              </Label>
            </div>
            <Button
              size="sm"
              isLoading={isInviting}
              onClick={handleInviteEmail}
            >
              Invite
            </Button>
          </div>
        </SettingsLayout.Section>
        <SettingsLayout.Section>
          <AgencyMembersList
            agencyUsers={mockAgencyUsers}
            clientUsers={mockClientUsers}
            pendingUsers={mockPendingUsers}
          />
        </SettingsLayout.Section>
      </SettingsLayout.Sections>
    </SettingsLayout.Root>
  );
};

export default AgencyMembers;
