import { useState, ReactNode } from "react";
import { toast } from "sonner";
import { Toolt<PERSON> } from "~/components";
import MembersList, { User } from "~/components/app/members/MembersList";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { useWindowTitle } from "~/hooks";
import { InformationCircleIcon, PencilIcon, TrashIcon, EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import { openConfirmationModal } from '~/utils';

// Mock data for demonstration
const mockAgencyUsers: User[] = [
  {
    id: "1",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 5,
  },
  {
    id: "2",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 5,
  },
  {
    id: "3",
    firstName: "<PERSON>",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
];

const mockPendingUsers: User[] = [
  {
    id: "4",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 5,
  },
];

const mockClientUsers: User[] = [
  {
    id: "5",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
  {
    id: "6",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
  {
    id: "7",
    firstName: "John",
    lastName: "Embly",
    email: "<EMAIL>",
    profileImageUrl: undefined,
    workspaceCount: 1,
  },
];

const workspaceOptions = [
  { key: "workspace1", value: "workspace1", label: "Workspace 1" },
  { key: "workspace2", value: "workspace2", label: "Workspace 2" },
  { key: "workspace3", value: "workspace3", label: "Workspace 3" },
  { key: "workspace4", value: "workspace4", label: "Workspace 4" },
];

// Types for member actions
interface MemberAction {
  label: string;
  onClick: (user: User) => void;
  variant?: 'destructive';
  icon?: ReactNode;
}

// Helper function to render dropdown menu for agency members
const showAgencyMemberActions = (user: User, actions: MemberAction[], loadingUserId: string | null) => {
  if (actions.length === 0) return null;

  const isLoading = loadingUserId === user.id;

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger disabled={isLoading}>
        <EllipsisHorizontalIcon className={`h-5 w-5 transition-colors font-bold stroke-2 ${isLoading
          ? "text-medium-gray cursor-not-allowed"
          : "cursor-pointer"
          }`}
          style={{ color: isLoading ? undefined : '#0F172A' }}
        />
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end">
        <DropdownMenu.Group>
          {actions.map((action, actionIndex) => (
            <DropdownMenu.Item
              key={actionIndex}
              onClick={() => action.onClick(user)}
              disabled={isLoading}
              intent={action.variant === 'destructive' ? 'danger' : 'none'}
            >
              <div className="flex items-center gap-2">
                {action.icon && action.icon}
                {action.label}
              </div>
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Group>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};

const AgencyMembers = () => {
  useWindowTitle("Agency Members");

  const [loadingUserId, setLoadingUserId] = useState<string | null>(null);

  // Handlers for member actions
  const handleEditWorkspaceAccess = (user: User) => {
    toast.info(`Edit workspace access for ${user.firstName} ${user.lastName}`);
    // TODO: Implement edit workspace access functionality
  };

  const handleRemoveUser = async (user: User) => {
    // if (window.confirm(`Are you sure you want to remove ${user.firstName} ${user.lastName}?`)) {
    //   setLoadingUserId(user.id);
    //   try {
    //     // TODO: Implement remove user functionality
    //     toast.success(`${user.firstName} ${user.lastName} has been removed`);
    //   } catch (error) {
    //     toast.error("Failed to remove user");
    //   } finally {
    //     setLoadingUserId(null);
    //   }
    // }

    // use openConfirmationModal component

    openConfirmationModal({
      title: `Remove ${user.firstName} ${user.lastName}?`,
      body: `Are you sure you want to remove ${user.firstName} ${user.lastName}?`,
      cancelButton: {
        label: "Cancel, don't remove",
      },
      primaryButton: {
        label: `Remove User`,
        variant: "danger",
        onClick: () => {
          setLoadingUserId(user.id);
          try {
            // TODO: Implement remove user functionality
            toast.success(`${user.firstName} ${user.lastName} has been removed`);
          } catch (error) {
            toast.error("Failed to remove user");
          } finally {
            setLoadingUserId(null);
          }
        },
      },
    });

  };

  // Define actions for different user types
  const agencyUserActions: MemberAction[] = [
    {
      label: "Edit Workspace Access",
      onClick: handleEditWorkspaceAccess,
      icon: <PencilIcon className="h-4 w-4" />,
    },
    {
      label: "Remove User",
      onClick: handleRemoveUser,
      variant: "destructive",
      icon: <TrashIcon className="h-4 w-4" />,
    },
  ];

  const clientUserActions: MemberAction[] = [
    {
      label: "Edit Workspace Access",
      onClick: handleEditWorkspaceAccess,
      icon: <PencilIcon className="h-4 w-4" />,
    },
    {
      label: "Remove User",
      onClick: handleRemoveUser,
      variant: "destructive",
      icon: <TrashIcon className="h-4 w-4" />,
    },
  ];

  const pendingUserActions: MemberAction[] = [
    {
      label: "Remove User",
      onClick: handleRemoveUser,
      variant: "destructive",
      icon: <TrashIcon className="h-4 w-4" />,
    },
  ];


  return (
    <SettingsLayout.Root>
      <SettingsLayout.Header>
        <SettingsLayout.Title>Agency Members</SettingsLayout.Title>
        <SettingsLayout.Description>
          Select which workspaces each member of the agency has access to
        </SettingsLayout.Description>
      </SettingsLayout.Header>
      <SettingsLayout.Sections>

        <SettingsLayout.Section>
          <div className="space-y-8 pt-5">
            {/* Agency Users Section */}
            {mockAgencyUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  <div className="flex items-center gap-2 font-medium">
                    <div>Agency Users</div>
                    <Tooltip value="Agency users are any users that are part of more than 1 of your Client workspace(s)." side="bottom" align="start">
                      <InformationCircleIcon className="h-4 w-4 text-medium-dark-gray" />
                    </Tooltip>
                  </div>
                </SettingsLayout.SectionTitle>
                {mockAgencyUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
                    <MembersList.MemberActions>
                      {showAgencyMemberActions(user, agencyUserActions, loadingUserId)}
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}

            {/* Pending Users Section */}
            {mockPendingUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  Pending
                </SettingsLayout.SectionTitle>
                {mockPendingUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
                    <MembersList.MemberActions>
                      {showAgencyMemberActions(user, pendingUserActions, loadingUserId)}
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}

            {/* Client Users Section */}
            {mockClientUsers.length > 0 && (
              <MembersList.Root isLoading={false} isEmpty={false}>
                <SettingsLayout.SectionTitle>
                  Client Users
                </SettingsLayout.SectionTitle>
                {mockClientUsers.map((user) => (
                  <MembersList.Member key={user.id}>
                    <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
                    <MembersList.MemberActions>
                      {showAgencyMemberActions(user, clientUserActions, loadingUserId)}
                    </MembersList.MemberActions>
                  </MembersList.Member>
                ))}
              </MembersList.Root>
            )}
          </div>
        </SettingsLayout.Section>
      </SettingsLayout.Sections>
    </SettingsLayout.Root>
  );
};

export default AgencyMembers;
