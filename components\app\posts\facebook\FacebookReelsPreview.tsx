import { EllipsisHorizontalIcon, PhotoIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import uniq from "lodash/uniq";
import { useState } from "react";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import { urlRegex } from "~/constants";
import facebookMentionRegex from "~/constants/facebookMentionRegex";
import { useWorkspace } from "~/providers";
import { getDefaultProfilePicture, removeEmptyElems } from "~/utils";

type FacebookAccount = {
  id: string;
  name: string | null;
  profileImageUrl: string;
};

type Props = {
  content: {
    copy?: string;
    assets: {
      id: string;
      signedUrl: string;
      metadata: {
        mimetype: string;
      };
      createdAt: Date;
    }[];
  };
  coverPhotoUrl?: string;
  connectedAccount: FacebookAccount | null | undefined;
};

const FacebookReelsPreview = ({
  content,
  coverPhotoUrl,
  connectedAccount,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const [showMore, setShowMore] = useState<boolean>(false);
  const nonEmpty =
    (content.copy && content.copy != "") || content.assets.length > 0;

  const accountName = connectedAccount?.name || currentWorkspace?.name;
  const profilePictureUrl =
    connectedAccount?.profileImageUrl ?? getDefaultProfilePicture("Facebook");

  const copy = `${accountName ? `**${accountName}** ` : " "}${content?.copy}`;

  const videoUrl = content.assets[0]?.signedUrl;

  if (!nonEmpty) {
    return (
      <div className="font-body text-medium-dark-gray mt-1 flex h-full items-center">
        Preview will show when post content is added below
      </div>
    );
  } else {
    return (
      <div className="scrollbar-hidden flex h-[408px] justify-center overflow-auto">
        <div
          className={classNames(
            "relative w-[230px] rounded-xl text-[8px] text-white",
            {
              "bg-black": !!videoUrl,
              "bg-medium-gray": !videoUrl,
            }
          )}
        >
          {videoUrl ? (
            <div className="flex h-full items-center justify-center overflow-clip rounded-xl">
              <video
                src={content?.assets[0].signedUrl}
                loop={true}
                poster={coverPhotoUrl}
                onClick={(e) => {
                  const video = e.target as HTMLVideoElement;
                  if (video.paused) {
                    video.play();
                  } else {
                    video.pause();
                  }
                }}
              />
              {/* Add an overlay if showMore is true */}
              {showMore && (
                <div
                  className={classNames(
                    "absolute inset-0 rounded-xl bg-black transition-all",
                    {
                      "bg-opacity-0": !showMore,
                      "bg-opacity-40": showMore,
                    }
                  )}
                />
              )}
            </div>
          ) : (
            <div className="flex h-full flex-col items-center justify-center gap-2 text-white">
              <PhotoIcon className="h-6 w-6" />
              <div className="font-body text-center">
                Upload video in the <br />
                editor below
              </div>
            </div>
          )}
          {/* Right Side Icons */}
          <div className="absolute right-2 bottom-6 flex flex-col gap-4">
            <svg
              aria-label="Like"
              color="rgb(255, 255, 255)"
              fill="rgb(255, 255, 255)"
              height="16"
              role="img"
              viewBox="0 0 24 24"
              width="16"
            >
              <title>Like</title>
              <path d="M16.792 3.904A4.989 4.989 0 0 1 21.5 9.122c0 3.072-2.652 4.959-5.197 7.222-2.512 2.243-3.865 3.469-4.303 3.752-.477-.309-2.143-1.823-4.303-3.752C5.141 14.072 2.5 12.167 2.5 9.122a4.989 4.989 0 0 1 4.708-5.218 4.21 4.21 0 0 1 3.675 1.941c.84 1.175.98 1.763 1.12 1.763s.278-.588 1.11-1.766a4.17 4.17 0 0 1 3.679-1.938m0-2a6.04 6.04 0 0 0-4.797 2.127 6.052 6.052 0 0 0-4.787-2.127A6.985 6.985 0 0 0 .5 9.122c0 3.61 2.55 5.827 5.015 7.97.283.246.569.494.853.747l1.027.918a44.998 44.998 0 0 0 3.518 3.018 2 2 0 0 0 2.174 0 45.263 45.263 0 0 0 3.626-3.115l.922-.824c.293-.26.59-.519.885-.774 2.334-2.025 4.98-4.32 4.98-7.94a6.985 6.985 0 0 0-6.708-7.218Z"></path>
            </svg>
            <svg
              aria-label="Comment"
              color="rgb(255, 255, 255)"
              fill="rgb(255, 255, 255)"
              height="16"
              role="img"
              viewBox="0 0 24 24"
              width="16"
            >
              <title>Comment</title>
              <path
                d="M20.656 17.008a9.993 9.993 0 1 0-3.59 3.615L22 22Z"
                fill="none"
                stroke="currentColor"
                strokeLinejoin="round"
                strokeWidth="2"
              ></path>
            </svg>
            <svg
              aria-label="Share Post"
              color="rgb(255, 255, 255)"
              fill="rgb(255, 255, 255)"
              height="16"
              role="img"
              viewBox="0 0 24 24"
              width="16"
            >
              <title>Share Post</title>
              <line
                fill="none"
                stroke="currentColor"
                strokeLinejoin="round"
                strokeWidth="2"
                x1="22"
                x2="9.218"
                y1="3"
                y2="10.083"
              ></line>
              <polygon
                fill="none"
                points="11.698 20.334 22 3.001 2 3.001 9.218 10.084 11.698 20.334"
                stroke="currentColor"
                strokeLinejoin="round"
                strokeWidth="2"
              ></polygon>
            </svg>
            <EllipsisHorizontalIcon className="-mt-2 h-4 w-4" />
          </div>
          {/* Bottom section */}
          <div className="absolute bottom-3 left-3">
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-1">
                <img className="h-4 w-4 rounded-full" src={profilePictureUrl} />
                <div className="font-semibold">
                  {accountName?.replace("@", "")}
                </div>
              </div>
              <div
                className={classNames(
                  "transition-max-height mr-6 h-full leading-tight duration-300",
                  {
                    "max-h-[12px] overflow-clip": !showMore,
                    "scrollbar-hidden max-h-[200px] overflow-auto": showMore,
                  }
                )}
                onClick={() => {
                  if (showMore) {
                    setShowMore(false);
                  }
                }}
              >
                {copy?.split("\n").map((line, index) => {
                  if (line === "") {
                    return <div key={index} className="py-1" />;
                  } else {
                    let parsedLine = line;

                    const websites = uniq(line.match(urlRegex)) || [];

                    websites.forEach((website) => {
                      let httpWebsite = website;
                      if (!website.startsWith("http")) {
                        httpWebsite = `http://${website}`;
                      }
                      const websiteRegex = new RegExp(
                        `${website.replace("?", "\\?")}(?!\\/|[?#])`,
                        "g"
                      );
                      parsedLine = parsedLine.replace(
                        websiteRegex,
                        `[${website}](${httpWebsite})`
                      );
                    });

                    // Returns ["@[pageName](id)", "@[pageName](id)", ...]
                    const mentions = uniq(
                      line.match(facebookMentionRegex) || []
                    );
                    mentions.forEach((mention) => {
                      facebookMentionRegex.lastIndex = 0;
                      const pageMatch = facebookMentionRegex.exec(mention);
                      if (pageMatch) {
                        const pageId = pageMatch[2];
                        const pageLink = `https://www.facebook.com/${pageId}`;
                        const newMention = mention
                          .slice(1)
                          .replace(pageId, pageLink);
                        parsedLine = parsedLine.replaceAll(mention, newMention);
                      }
                    });

                    const hashtags = uniq(line.match(/(#[\w\d_]+)/g)) || [];

                    hashtags.forEach((hashtag, index) => {
                      const tag = hashtag.replace("#", "");

                      const tagRegex = new RegExp(`${hashtag}\\b`, "g");

                      parsedLine = parsedLine.replaceAll(
                        tagRegex,
                        `[${hashtag}](https://www.facebook.com/hashtag/${tag})`
                      );
                    });

                    return (
                      <div key={index} className="flex items-center" dir="auto">
                        <ReactMarkdown
                          className="mr-0 w-fit break-words"
                          linkTarget="_blank"
                          children={parsedLine}
                          components={{
                            h1: ({ node, ...props }) => {
                              return (
                                <span>
                                  # <span {...props} />
                                </span>
                              );
                            },
                            h2: ({ node, ...props }) => {
                              return (
                                <span>
                                  ## <span {...props} />
                                </span>
                              );
                            },
                            h3: ({ node, ...props }) => {
                              return (
                                <span>
                                  ### <span {...props} />
                                </span>
                              );
                            },
                            em: "span",
                            blockquote: ({ node, ...props }) => {
                              const children = removeEmptyElems(
                                props.children.filter((child) => child !== "\n")
                                // @ts-expect-error props is not defined on the child
                              ).map((child) => child.props.children);
                              return (
                                <span>
                                  {">"} {children}
                                </span>
                              );
                            },
                            hr: ({ node, ...props }) => {
                              const length = node.position?.end.offset || 3;
                              return (
                                <span>
                                  {Array.from({ length }).map((_, index) => (
                                    <span key={index}>-</span>
                                  ))}
                                </span>
                              );
                            },
                            ol: ({ node, ...props }) => {
                              return (
                                <ol
                                  {...props}
                                  className="list-inside list-decimal"
                                />
                              );
                            },
                            ul: ({ node, ...props }) => {
                              // @ts-expect-error children is not defined on the node
                              const children = props.children[1].props.children;
                              return (
                                <ol {...props} className="list-inside">
                                  <li>- {children}</li>
                                </ol>
                              );
                            },
                            code: ({ node, ...props }) => {
                              return (
                                <span>
                                  `<span {...props} />`
                                </span>
                              );
                            },
                            strong: ({ node, ...props }) => {
                              return (
                                <span
                                  {...props}
                                  className="font-medium text-white"
                                />
                              );
                            },
                            a: ({ node, ...props }) => {
                              return (
                                <a
                                  onClick={(e) => {
                                    e.stopPropagation();
                                  }}
                                  className="font-semibold text-white"
                                  {...props}
                                />
                              );
                            },
                          }}
                        />
                        {!showMore && index === 0 && (
                          <button
                            className="text-light-gray whitespace-nowrap"
                            onClick={() => setShowMore(true)}
                          >
                            ...
                          </button>
                        )}
                      </div>
                    );
                  }
                })}
              </div>
              <div className="flex items-center gap-1">
                <svg
                  aria-label="Audio image"
                  color="rgb(255, 255, 255)"
                  fill="rgb(255, 255, 255)"
                  height="12"
                  role="img"
                  viewBox="0 0 24 24"
                  width="12"
                >
                  <title>Audio image</title>
                  <path d="M21.002 16.972V2.044a.999.999 0 0 0-.36-.769 1.012 1.012 0 0 0-.823-.214L6.816 3.479A1 1 0 0 0 6 4.462v10.864A3.75 3.75 0 1 0 9 19V9.56l9.003-1.675v5.442A3.75 3.75 0 1 0 21.005 17c0-.01-.003-.02-.003-.029Z"></path>
                </svg>
                <div>Original audio</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default FacebookReelsPreview;
