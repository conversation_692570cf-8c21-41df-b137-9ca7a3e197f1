import getAssetBuffer from "apiUtils/getAssetBuffer";
import getFileHash from "apiUtils/getFileHash";
import getStorageAssetsForTwitter from "apiUtils/twitter/getStorageAssetsForTwitter";
import imageSize from "image-size";
import minBy from "lodash/minBy";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const createdAtDate = req.query.createdAt as string;

  const posts = await db.post.findMany({
    take: 750,
    where: {
      createdAt: {
        lte: new Date(createdAtDate),
      },
      channels: {
        hasSome: ["Twitter"],
      },
      tweets: {
        every: {
          assets: {
            none: {},
          },
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    select: {
      id: true,
      workspaceID: true,
      createdByID: true,
      createdAt: true,
      tweets: {
        select: {
          id: true,
        },
      },
    },
  });

  console.log(`Num posts to backfill ${posts.length}`);

  const postAssetPromises = posts.map(async (post) => {
    const twitterAssets = await getStorageAssetsForTwitter(post);

    return {
      ...post,
      twitterAssets,
    };
  });

  const postsWithAssets = await Promise.all(postAssetPromises);

  console.log("Got all assets for posts");

  const postsToBackfill = postsWithAssets.filter((post) => {
    return Object.values(post.twitterAssets).some(
      (assets) => assets.length > 0
    );
  });

  console.log(`Backfilling ${postsToBackfill.length} posts`);

  const getImageDimensions = async (
    asset: {
      mimetype: string;
      url: string;
    } | null
  ) => {
    try {
      if (asset && asset.mimetype.includes("image/")) {
        const buffer = await getAssetBuffer(asset.url);
        const dimensions = imageSize(buffer);
        const md5Hash = await getFileHash(buffer);
        const width = dimensions.width as number;
        const height = dimensions.height as number;
        return { width, height, md5Hash };
      } else {
        return {
          width: null,
          height: null,
          md5Hash: null,
        };
      }
    } catch (error) {
      return {
        width: null,
        height: null,
        md5Hash: null,
      };
    }
  };

  const allAssets = postsToBackfill.flatMap((post) => {
    const { twitterAssets } = post;

    const assets = Object.values(twitterAssets)
      .flatMap((assets) => assets)
      .map((asset) => ({
        ...asset,
        workspaceID: post.workspaceID,
        userID: post.createdByID,
      }));

    return assets;
  });

  const allAssetsWithDimensions = await Promise.all(
    allAssets.map(async (asset) => {
      const imageDimensions = await getImageDimensions(asset);

      return {
        ...asset,
        ...imageDimensions,
      };
    })
  );

  await db.asset.createMany({
    data: allAssetsWithDimensions.map((asset) => ({
      id: asset.id,
      name: asset.name,
      bucket: "assets",
      path: asset.path,
      eTag: asset.eTag,
      md5Hash: asset.md5Hash,
      mimetype: asset.mimetype,
      size: asset.sizeBytes,
      url: asset.url,
      width: asset.width,
      height: asset.height,
      urlExpiresAt: asset.urlExpiresAt,
      workspaceID: asset.workspaceID,
      userID: asset.userID,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
    })),
    skipDuplicates: true,
  });

  const backfillPromises = postsToBackfill.map(async (post) => {
    const { twitterAssets } = post;

    await db.tweetAsset.createMany({
      data: Object.entries(twitterAssets).flatMap(
        ([tweetContentID, assets]) => {
          return assets.map((asset, index) => ({
            position: index,
            assetID: asset.id,
            tweetContentID,
          }));
        }
      ),
      skipDuplicates: true,
    });
  });

  await Promise.all(backfillPromises);

  res.status(200).send({
    numPostsBackfilled: postsToBackfill.length,
    earliestPostDate: minBy(posts, "createdAt")?.createdAt || "No posts found",
  });
};

export default handler;
