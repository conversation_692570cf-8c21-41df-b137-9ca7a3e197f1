import { Asset } from "@prisma/client";
import { AssetType } from "~/types/Asset";
import enhanceAssetType from "./assets/enhanceAssetType";

const getAssetsForTweets = (post: {
  id: string;
  workspaceID: string;
  tweets: {
    id: string;
    assets: {
      id: string;
      position: number;
      altText: string | null;
      asset: Asset;
    }[];
  }[];
}): {
  [id: string]: (AssetType & {
    width: number | null;
    altText: string | null;
  })[];
} => {
  const assetsForTweet: {
    [id: string]: (AssetType & {
      width: number | null;
      altText: string | null;
    })[];
  } = {};
  post.tweets.forEach((tweet) => {
    const assets = tweet.assets
      .map((asset) => {
        return {
          ...enhanceAssetType(asset.asset),
          id: asset.id,
          position: asset.position,
          altText: asset.altText,
        };
      })
      .sort((a, b) => a.position - b.position);

    assetsForTweet[tweet.id] = assets;
  });

  return assetsForTweet;
};

export default getAssetsForTweets;
