import axios from "axios";

const hackySleep = async (timeoutMs: number = 5000) => {
  try {
    console.log(`Hacky Sleeping ${timeoutMs}ms`);
    const response = await axios.get(
      "https://jsonplaceholder.typicode.com/todos/1",
      {
        timeout: timeoutMs,
      }
    );
    console.log(response.data);
  } catch (error) {
    console.log(error);
  }

  return true;
};

export default hackySleep;
