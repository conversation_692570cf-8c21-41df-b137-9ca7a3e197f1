import { Asset, EngagementType } from "@prisma/client";
import getAssetsForLinkedIn from "apiUtils/getAssetsForLinkedIn";
import { StatusCodes } from "http-status-codes";
import { DateTime } from "luxon";
import { db } from "~/clients";
import { inngest } from "~/clients/inngest/inngest";
import LinkedIn from "~/clients/linkedin/LinkedIn";
import { GENERIC_ERROR_MESSAGE } from "~/constants";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";

type Post = {
  id: string;
  linkedInIntegration: {
    accessToken: string;
    account: {
      urn: string;
    };
  } | null;
  linkedInContent: {
    id: string;
    copy: string;
    documentTitle: string | null;
    assets: {
      id: string;
      position: number;
      asset: Asset;
    }[];
    captions: Asset | null;
    coverPhoto: Asset | null;
    engagements: {
      id: string;
      type: EngagementType;
      postDelaySeconds: number;
    }[];
  } | null;
  title: string;
  workspaceID: string;
};

const publishLinkedInPost = async (
  post: Post
): Promise<ScheduledPostResult> => {
  const { linkedInContent, linkedInIntegration } = post;

  if (!linkedInContent) {
    return {
      status: "NonRetriableError",
      error: "No LinkedIn content to post",
    };
  }

  if (!linkedInIntegration) {
    return {
      status: "NonRetriableError",
      error: "No LinkedIn Integration",
    };
  }

  const client = new LinkedIn(linkedInIntegration.accessToken);

  const authorUrn = linkedInIntegration.account.urn;

  const { assets, coverPhoto, captions } = await getAssetsForLinkedIn(post);

  const response = await client.createPost({
    authorUrn,
    commentary: linkedInContent.copy,
    documentTitle: linkedInContent.documentTitle,
    assets: assets.map((asset) => ({
      name: asset.name,
      signedUrl: asset.signedUrl,
      mimetype: asset.metadata.mimetype,
      fileSizeBytes: asset.metadata.size,
    })),
    coverPhoto,
    captions,
  });

  if (response.status === StatusCodes.CREATED && response.data) {
    const { postUrn } = response.data;
    const postUrl = `https://www.linkedin.com/feed/update/${postUrn}`;

    try {
      const engagements = linkedInContent.engagements;
      if (engagements.length) {
        const likes = engagements.filter(
          (engagement) => engagement.type === "Like"
        );
        const otherEngagements = engagements.filter(
          (engagement) => engagement.type !== "Like"
        );

        const events = otherEngagements.map((engagement) => {
          const delayUntil = DateTime.now()
            .plus({
              seconds: engagement.postDelaySeconds,
            })
            .toJSDate();

          return {
            name: "linkedin.postEngagement" as const,
            data: {
              engagementID: engagement.id,
              externalPostID: postUrn,
              delayUntil,
              permalink: postUrl,
            },
          };
        });

        let index = 0;

        for await (const engagement of likes) {
          // All likes will be delayed by a random amount of time between 0 and 10 minutes
          let randomDelay = Math.floor(Math.random() * 10 * 60);
          // The first like will be delayed by a random amount of time between 0 and 2 minutes
          // to ensure the post has at least one like early on
          if (index === 0) {
            randomDelay = Math.floor(Math.random() * 2 * 60);
          }

          const delayUntil = DateTime.now()
            .plus({
              seconds: randomDelay,
            })
            .toJSDate();

          events.push({
            name: "linkedin.postEngagement" as const,
            data: {
              engagementID: engagement.id,
              externalPostID: postUrn,
              delayUntil,
              permalink: postUrl,
            },
          });

          await db.linkedInPostEngagement.update({
            where: { id: engagement.id },
            data: {
              postDelaySeconds: randomDelay,
            },
          });
          index++;
        }

        if (events.length) {
          await inngest.send(events);
        }
      }
    } catch (error) {
      console.error("Error posting engagements:", error);
    }

    return {
      status: "Success",
      postUrl,
    };
  } else {
    const error = response.errors
      ? response.errors.join(". ")
      : GENERIC_ERROR_MESSAGE;

    return {
      status: "Error",
      error,
      rawError: response.rawError,
    };
  }
};

export default publishLinkedInPost;
