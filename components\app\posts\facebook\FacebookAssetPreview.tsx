import { Player } from "video-react";

type Props = {
  assets: {
    name: string;
    signedUrl: string;
    metadata: {
      mimetype: string;
    };
    createdAt: Date;
  }[];
  coverPhotoUrl?: string;
};

const FacebookAssetPreview = ({ assets, coverPhotoUrl }: Props) => {
  if (assets.length === 0) {
    return null;
  }

  const numAssets = assets.length;

  if (numAssets === 1) {
    const asset = assets[0];
    const isVideo = asset.metadata.mimetype.startsWith("video");

    if (isVideo) {
      return <Player src={asset.signedUrl} poster={coverPhotoUrl} />;
    } else {
      return <img src={assets[0].signedUrl} />;
    }
  } else if (numAssets === 2) {
    return (
      <div className="grid w-full grid-cols-2 gap-0.5 border border-white">
        <div className="flex items-center">
          <img
            className="aspect-square h-full w-full object-cover"
            src={assets[0].signedUrl}
          />
        </div>
        <div className="flex items-center">
          <img
            className="aspect-square h-full w-full object-cover"
            src={assets[1].signedUrl}
          />
        </div>
      </div>
    );
  } else if (numAssets === 3) {
    return (
      <div className="grid h-full grid-cols-1 gap-0.5 border border-white">
        <div className="mt-0! flex w-full items-center">
          <img
            className="h-[250px] w-full object-cover object-top"
            src={assets[0].signedUrl}
          />
        </div>
        <div className="grid grid-cols-2 gap-0.5 space-y-0.5">
          <div className="mt-0! flex aspect-square h-[250px] items-center">
            <img
              className="aspect-square h-full w-full object-cover object-top"
              src={assets[1].signedUrl}
            />
          </div>
          <div className="mt-0! flex aspect-square h-[250px] items-center">
            <img
              className="aspect-square h-full w-full object-cover object-top"
              src={assets[2].signedUrl}
            />
          </div>
        </div>
      </div>
    );
  } else if (numAssets === 4) {
    return (
      <div className="grid h-full grid-cols-1 gap-0.5 border border-white">
        <div className="h-[250px] w-full object-cover">
          <img
            className="h-full w-full object-cover object-top"
            src={assets[0].signedUrl}
          />
        </div>
        <div className="grid grid-cols-3 gap-0.5 space-y-0.5">
          <div className="mt-0! flex aspect-square h-full w-full items-center">
            <img
              className="h-full w-full object-cover object-top"
              src={assets[1].signedUrl}
            />
          </div>
          <div className="mt-0! flex aspect-square h-full w-full items-center">
            <img
              className="h-full w-full object-cover object-top"
              src={assets[2].signedUrl}
            />
          </div>
          <div className="mt-0! flex aspect-square h-full w-full items-center">
            <img
              className="h-full w-full object-cover object-top"
              src={assets[3].signedUrl}
            />
          </div>
        </div>
      </div>
    );
  } else if (numAssets >= 5) {
    return (
      <div className="grid h-[500px] grid-cols-2 gap-0.5 border border-white">
        <div className="grid h-full grid-rows-2 gap-0.5 space-y-0.5">
          <div className="mt-0! flex h-[250px] items-center">
            <img
              className="h-full object-cover object-top"
              src={assets[0].signedUrl}
            />
          </div>
          <div className="mt-0! flex h-[250px] items-center">
            <img
              className="h-full object-cover object-top"
              src={assets[1].signedUrl}
            />
          </div>
        </div>
        <div className="grid h-[500px] grid-rows-3 gap-0.5 space-y-0.5">
          <div className="mt-0! flex h-full items-center">
            <img
              className="h-full w-full object-cover object-top"
              src={assets[2].signedUrl}
            />
          </div>
          <div className="mt-0! flex h-full items-center">
            <img
              className="h-full w-full object-cover object-top"
              src={assets[3].signedUrl}
            />
          </div>
          <div className="relative mt-0! flex h-full items-center">
            <img
              className="h-full w-full object-cover object-top"
              src={assets[4].signedUrl}
            />
            {numAssets > 5 && (
              <div className="absolute flex h-full w-full items-center justify-center bg-black/60 text-[32px] text-white">
                <div>+{numAssets - 5}</div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default FacebookAssetPreview;
