import { Controller, Path } from "react-hook-form";

import { Label } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import camelCaseToWords from "~/utils/camelCaseToWords";
import LabelSelect, { type Label as LabelType } from "../app/LabelSelect";

type FormLabelSelectProps<T> = {
  control: any;
  name: Path<T>;
  label?: string | JSX.Element;
  options: LabelType[];
  isOptional?: boolean;
  tooltipText?: string;
  rules?: Rules;
};

const FormLabelSelect = <T,>({
  control,
  name,
  label,
  options,
  isOptional,
  tooltipText,
  rules = {},
}: FormLabelSelectProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <Label
            value={label}
            isRequired={!!rules.required}
            isOptional={isOptional}
            tooltipText={tooltipText}
          >
            <>
              <LabelSelect
                value={value || []}
                options={options}
                onChange={onChange}
              />
              <Error error={error} />
            </>
          </Label>
        );
      }}
    />
  );
};

export default FormLabelSelect;
