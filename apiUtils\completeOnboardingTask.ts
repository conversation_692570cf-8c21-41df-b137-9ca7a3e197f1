import {
  WorkspaceOnboardingTaskState,
  WorkspaceOnboardingTaskType,
} from "@prisma/client";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const eventsMap: Record<WorkspaceOnboardingTaskState, string | null> = {
  [WorkspaceOnboardingTaskState.COMPLETED]:
    POSTHOG_EVENTS.onboarding.taskCompleted,
  [WorkspaceOnboardingTaskState.MARKED_COMPLETE]:
    POSTHOG_EVENTS.onboarding.taskMarkedComplete,
  [WorkspaceOnboardingTaskState.TIMED_OUT]:
    POSTHOG_EVENTS.onboarding.taskExpired,
  [WorkspaceOnboardingTaskState.ACTIVE]: null,
};

export default async function completeOnboardingTask({
  workspaceID,
  userID,
  taskType,
  state,
}: {
  workspaceID: string;
  userID: string;
  taskType: WorkspaceOnboardingTaskType;
  state: WorkspaceOnboardingTaskState;
}) {
  if (state === WorkspaceOnboardingTaskState.ACTIVE) {
    const task = await db.workspaceOnboardingTask.findFirst({
      where: {
        workspaceID,
        type: taskType,
      },
    });

    if (task && task.state === WorkspaceOnboardingTaskState.MARKED_COMPLETE) {
      await db.workspaceOnboardingTask.update({
        where: { id: task.id },
        data: {
          state: WorkspaceOnboardingTaskState.ACTIVE,
          completedAt: null,
          completedByID: null,
        },
      });

      posthog.capture({
        distinctId: userID,
        event: POSTHOG_EVENTS.onboarding.taskUnmarked,
        properties: { taskType },
        groups: { workspace: workspaceID },
      });

      return;
    }
  }

  const onboardingTask = await db.workspaceOnboardingTask.findFirst({
    where: {
      workspaceID,
      type: taskType,
      state: WorkspaceOnboardingTaskState.ACTIVE,
    },
  });

  const remainingTasks = await db.workspaceOnboardingTask.count({
    where: {
      workspaceID,
      state: WorkspaceOnboardingTaskState.ACTIVE,
    },
  });

  if (remainingTasks === 0) {
    posthog.capture({
      distinctId: userID,
      event: POSTHOG_EVENTS.onboarding.allTasksCompleted,
      groups: { workspace: workspaceID },
    });
  }

  if (onboardingTask) {
    await db.workspaceOnboardingTask.update({
      where: { id: onboardingTask.id },
      data: {
        state,
        completedAt: new Date(),
        completedByID: userID ?? null,
      },
    });

    const event = eventsMap[state];

    if (event) {
      posthog.capture({
        distinctId: userID,
        event,
        properties: { taskType },
        groups: { workspace: workspaceID },
      });
    }
  }
}
