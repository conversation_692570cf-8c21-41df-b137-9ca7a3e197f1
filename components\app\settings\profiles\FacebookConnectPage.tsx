import assert from "assert";
import { useRouter } from "next/router";
import { useState } from "react";
import { toast } from "sonner";
import { Divider } from "~/components";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import Avatar from "~/components/ui/Avatar";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import RadioGroup from "~/components/ui/RadioGroup";
import { useWindowTitle } from "~/hooks";
import { handleTrpcError, openIntercomChat, trpc } from "~/utils";
import { RouterOutput } from "~/utils/trpc";

type Account =
  RouterOutput["integration"]["facebook"]["getAccountOptions"]["accounts"][number];

export const handleFacebookLogin = async (
  callback: (authToken: string) => void
) => {
  try {
    window.FB.login(
      (response) => {
        if (response.status === "connected") {
          const { authResponse } = response;

          assert(authResponse.accessToken, "Access token is required");

          callback(authResponse.accessToken);
        } else {
          toast.error("Unable to Connect to Facebook", {
            description: "User cancelled login or did not fully authorize",
          });
        }
      },
      {
        scope: [
          "email",
          "public_profile",
          "instagram_basic",
          "instagram_manage_insights",
          "instagram_manage_comments",
          "instagram_content_publish",
          "pages_read_engagement",
          "pages_show_list",
          "pages_manage_posts",
          "business_management",
          "pages_read_user_content",
          "pages_manage_engagement",
          "read_insights",
        ].join(","),
        return_scopes: true,
      }
    );
  } catch (error: any) {
    console.log(error.message);
  }
};

type Props = {
  type: "scheduling" | "engagement";
};

const FacebookConnectPage = ({ type }: Props) => {
  useWindowTitle("Facebook Integration");
  const router = useRouter();
  const trpcUtils = trpc.useUtils();

  const [authToken, setAuthToken] = useState<string | null>(null);
  const [accountOptions, setAccountOptions] = useState<Account[] | null>(null);
  const [selectedAccountID, setSelectedAccountID] = useState<string | null>(
    null
  );

  const getAccountOptionsMutation =
    trpc.integration.facebook.getAccountOptions.useMutation();
  const saveAccountMutation = trpc.integration.facebook.saveAccount.useMutation(
    {
      onSuccess: (data) => {
        toast.success("Facebook Profile Connected", {
          description: `Connected ${data.page.name}`,
        });
        trpcUtils.workspace.getSchedulingProfiles.refetch();
        router.push(`/settings/profiles`);
      },
      onError: (error) => {
        handleTrpcError({
          title: "Error Connecting Profile",
          error,
        });
      },
    }
  );

  const facebookLogin = async () => {
    handleFacebookLogin((authToken) => {
      setAuthToken(authToken);

      getAccountOptionsMutation.mutate(
        {
          accessToken: authToken,
        },
        {
          onSuccess: (data) => {
            setAccountOptions(data.accounts);
          },
          onError: (error) => {
            handleTrpcError({
              title: "Error Connecting Profile",
              error,
            });
          },
        }
      );
    });
  };

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      <ConnectProfile.Root>
        {accountOptions == null ? (
          <div>
            <ConnectProfile.Header
              channel="Facebook"
              description="Connect your Facebook account to Assembly."
            />
            <ConnectProfile.Content>
              <Button size="sm" onClick={() => facebookLogin()}>
                Connect Facebook
              </Button>
            </ConnectProfile.Content>
            <Divider className="my-2 md:my-4" />
            <ConnectProfile.Footer>
              <div className="space-y-1.5">
                <p className="font-medium">Need Help?</p>
                <p className="text-medium-dark-gray">
                  Check out our help center article for a video walkthrough on
                  how to connect. If you're still having issues, please{" "}
                  <Button
                    size="sm"
                    variant="link"
                    onClick={() => {
                      openIntercomChat(
                        "I'm having trouble connecting my Facebook page and getting this error: ERROR"
                      );
                    }}
                  >
                    chat with us
                  </Button>
                  .
                </p>
              </div>
            </ConnectProfile.Footer>
          </div>
        ) : (
          <div className="flex flex-col gap-3">
            <ConnectProfile.Header
              channel="Facebook"
              title="Select which profile to connect"
              description="Select a profile you'd like to connect to Assembly"
            />
            <ConnectProfile.Content className="space-y-2">
              {accountOptions.length > 0 ? (
                <RadioGroup.Root
                  className="max-h-[460px] flex-col flex-nowrap gap-0 overflow-y-auto"
                  orientation="vertical"
                  onValueChange={(value) => {
                    setSelectedAccountID(value);
                  }}
                >
                  {accountOptions.map((account) => {
                    const isConnected = account.isConnected;

                    return (
                      <div key={account.id}>
                        <RadioGroup.Item
                          value={account.id}
                          disabled={isConnected}
                          className="w-full"
                        >
                          <div className="flex items-center justify-between">
                            <div className="ml-5 flex w-full items-center gap-x-2 transition-all">
                              <Avatar.Root className="h-8 w-8">
                                <Avatar.Image src={account.profileImageUrl} />
                                <Avatar.Fallback>
                                  {account.name}
                                </Avatar.Fallback>
                              </Avatar.Root>
                              <div className="font-body-sm">
                                <p className="font-medium">{account.name}</p>
                                <p className="text-medium-dark-gray">
                                  {account.username}
                                </p>
                              </div>
                            </div>
                            {isConnected && (
                              <p className="text-dark-gray font-body-sm whitespace-nowrap">
                                Already Connected
                              </p>
                            )}
                          </div>
                        </RadioGroup.Item>
                        <Divider className="my-2 md:my-4" />
                      </div>
                    );
                  })}
                </RadioGroup.Root>
              ) : (
                <div className="font-body-sm space-y-1.5">
                  <p className="font-medium">No Facebook Pages Found</p>
                  <p className="text-medium-dark-gray">
                    We couldn't find any Facebook Pages associated with your
                    Facebook profile. To connect a Facebook Page, you need to:
                  </p>
                  <ul className="text-medium-dark-gray list-inside list-disc">
                    <li>
                      Make sure you're logged into the correct Facebook profile
                    </li>
                    <li>Have admin access to at least one Facebook Page</li>
                    <li>Create a Facebook Page if you don't have one yet</li>
                  </ul>
                  <p className="text-medium-dark-gray">
                    If you need help, please contact our support team.
                  </p>
                </div>
              )}
            </ConnectProfile.Content>
            <div className="mt-2 flex flex-row justify-end md:gap-2.5">
              <Button
                variant="secondary"
                onClick={() => {
                  setAuthToken(null);
                  setAccountOptions(null);
                  setSelectedAccountID(null);
                }}
              >
                Cancel
              </Button>
              {accountOptions.length > 0 ? (
                <Button
                  isLoading={saveAccountMutation.isPending}
                  onClick={() => {
                    if (!selectedAccountID) {
                      toast.error("Please select a profile", {
                        description: "Select a profile to connect",
                      });
                      return;
                    } else {
                      saveAccountMutation.mutate({
                        facebookPageID: selectedAccountID,
                        accessToken: authToken!,
                      });
                    }
                  }}
                >
                  Connect
                </Button>
              ) : (
                <Button
                  onClick={() => {
                    setAuthToken(null);
                    setAccountOptions(null);
                    setSelectedAccountID(null);
                    facebookLogin();
                  }}
                >
                  Try Again
                </Button>
              )}
            </div>
          </div>
        )}
      </ConnectProfile.Root>
    </SettingsLayout.Root>
  );
};

export default FacebookConnectPage;
