import uniq from "lodash/uniq";

import { EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import { useWorkspace } from "~/providers";
import { getDefaultProfilePicture } from "~/utils";
import InstagramFeedAssetPreview from "./InstagramFeedAssetPreview";

type InstagramAccount = {
  id: string;
  name: string | null;
  profileImageUrl: string;
};

type Props = {
  instagramContent: {
    copy?: string;
    location: {
      name: string;
    } | null;
    assets: {
      id: string;
      signedUrl: string;
      metadata: {
        mimetype: string;
      };
      createdAt: Date;
    }[];
  } | null;
  connectedAccount: InstagramAccount | null | undefined;
  maxHeight?: number | "full";
};

const InstagramFeedPreview = ({
  instagramContent,
  connectedAccount,
  maxHeight,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const nonEmpty =
    instagramContent &&
    ((instagramContent.copy && instagramContent.copy != "") ||
      instagramContent.assets.length > 0);

  const accountName =
    connectedAccount?.name?.replace("@", "") || currentWorkspace?.name;

  const copy = `${accountName ? `**${accountName}** ` : " "}${
    instagramContent?.copy
  }`;

  if (!nonEmpty) {
    return (
      <div className="font-body text-medium-dark-gray mt-1 flex h-full items-center">
        Preview will show when post content is added below
      </div>
    );
  } else {
    return (
      <div className="scrollbar-hidden flex max-h-[500px] justify-center overflow-auto">
        <div
          className={classNames(
            "w-[300px] space-y-2 bg-white py-2.5 text-[12px]",
            {
              [`max-h-[${maxHeight}px] h-full`]: typeof maxHeight === "number",
              "max-h-full": maxHeight === "full",
            }
          )}
        >
          <div className="flex items-center justify-between px-2.5">
            <div className="font-smoothing flex items-center gap-2 leading-4">
              <img
                className="h-7 w-7 rounded-full"
                src={
                  connectedAccount?.profileImageUrl ??
                  getDefaultProfilePicture("Instagram")
                }
              />
              <div className="flex flex-col">
                <div className="leading-none font-semibold text-[#262626]">
                  {accountName}
                </div>
                {instagramContent.location && (
                  <div className="text-[10px] text-[#262626]">
                    {instagramContent.location.name}
                  </div>
                )}
              </div>{" "}
            </div>
            <EllipsisHorizontalIcon className="h-5 w-5 text-black" />
          </div>
          <InstagramFeedAssetPreview assets={instagramContent.assets} />
          <div className="bg-white">
            <div className="flex items-center justify-between px-3">
              <div className="flex items-center gap-1.5">
                <svg
                  aria-label="Like"
                  color="rgb(38, 38, 38)"
                  fill="rgb(38, 38, 38)"
                  height="16"
                  role="img"
                  viewBox="0 0 24 24"
                  width="16"
                >
                  <title>Like</title>
                  <path d="M16.792 3.904A4.989 4.989 0 0 1 21.5 9.122c0 3.072-2.652 4.959-5.197 7.222-2.512 2.243-3.865 3.469-4.303 3.752-.477-.309-2.143-1.823-4.303-3.752C5.141 14.072 2.5 12.167 2.5 9.122a4.989 4.989 0 0 1 4.708-5.218 4.21 4.21 0 0 1 3.675 1.941c.84 1.175.98 1.763 1.12 1.763s.278-.588 1.11-1.766a4.17 4.17 0 0 1 3.679-1.938m0-2a6.04 6.04 0 0 0-4.797 2.127 6.052 6.052 0 0 0-4.787-2.127A6.985 6.985 0 0 0 .5 9.122c0 3.61 2.55 5.827 5.015 7.97.283.246.569.494.853.747l1.027.918a44.998 44.998 0 0 0 3.518 3.018 2 2 0 0 0 2.174 0 45.263 45.263 0 0 0 3.626-3.115l.922-.824c.293-.26.59-.519.885-.774 2.334-2.025 4.98-4.32 4.98-7.94a6.985 6.985 0 0 0-6.708-7.218Z"></path>
                </svg>
                <svg
                  aria-label="Comment"
                  color="rgb(0, 0, 0)"
                  fill="rgb(0, 0, 0)"
                  height="16"
                  role="img"
                  viewBox="0 0 24 24"
                  width="16"
                >
                  <title>Comment</title>
                  <path
                    d="M20.656 17.008a9.993 9.993 0 1 0-3.59 3.615L22 22Z"
                    fill="none"
                    stroke="currentColor"
                    strokeLinejoin="round"
                    strokeWidth="2"
                  ></path>
                </svg>
                <svg
                  aria-label="Share Post"
                  color="rgb(0, 0, 0)"
                  fill="rgb(0, 0, 0)"
                  height="16"
                  role="img"
                  viewBox="0 0 24 24"
                  width="16"
                >
                  <title>Share Post</title>
                  <line
                    fill="none"
                    stroke="currentColor"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    x1="22"
                    x2="9.218"
                    y1="3"
                    y2="10.083"
                  ></line>
                  <polygon
                    fill="none"
                    points="11.698 20.334 22 3.001 2 3.001 9.218 10.084 11.698 20.334"
                    stroke="currentColor"
                    strokeLinejoin="round"
                    strokeWidth="2"
                  ></polygon>
                </svg>
              </div>
              <svg
                aria-label="Save"
                color="rgb(0, 0, 0)"
                fill="rgb(0, 0, 0)"
                height="16"
                role="img"
                viewBox="0 0 24 24"
                width="16"
              >
                <title>Save</title>
                <polygon
                  fill="none"
                  points="20 21 12 13.44 4 21 4 3 20 3 20 21"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                ></polygon>
              </svg>
            </div>
            <div className="mt-1 px-3 leading-tight">
              {copy?.split("\n").map((line, index) => {
                if (line === "") {
                  return <div key={index} className="py-1" />;
                } else {
                  let parsedLine = line;

                  const usernameRegex = /(@[A-Za-z0-9_\.]{3,30})/g;
                  const usernameTags = uniq(line.match(usernameRegex) || []);

                  usernameTags.forEach((usernameWithAt) => {
                    const username = usernameWithAt.replace("@", "");

                    parsedLine = parsedLine.replaceAll(
                      usernameWithAt,
                      `[${usernameWithAt}](https://instagram.com/${username})`
                    );
                  });

                  const hashtags = uniq(line.match(/(#[\w\d_]+)/g)) || [];

                  hashtags.forEach((hashtag, index) => {
                    const tag = hashtag.replace("#", "");

                    const tagRegex = new RegExp(`${hashtag}\\b`, "g");

                    parsedLine = parsedLine.replaceAll(
                      tagRegex,
                      `[${hashtag}](https://www.instagram.com/explore/tags/${tag})`
                    );
                  });

                  return (
                    <div key={index} dir="auto">
                      <ReactMarkdown
                        className="w-full break-words text-[#0F1419]"
                        linkTarget="_blank"
                        children={parsedLine}
                        components={{
                          h1: ({ node, ...props }) => {
                            return (
                              <span>
                                # <span {...props} />
                              </span>
                            );
                          },
                          h2: ({ node, ...props }) => {
                            return (
                              <span>
                                ## <span {...props} />
                              </span>
                            );
                          },
                          h3: ({ node, ...props }) => {
                            return (
                              <span>
                                ### <span {...props} />
                              </span>
                            );
                          },
                          em: "span",
                          ol: ({ node, ...props }) => {
                            return (
                              <ol
                                {...props}
                                className="list-inside list-decimal"
                              />
                            );
                          },
                          ul: ({ node, ...props }) => {
                            // @ts-expect-error children is not defined on the node
                            const children = props.children[1].props.children;
                            return (
                              <ol {...props} className="list-inside">
                                <li>- {children}</li>
                              </ol>
                            );
                          },
                          code: ({ node, ...props }) => {
                            return (
                              <span>
                                `<span {...props} />`
                              </span>
                            );
                          },
                          strong: ({ node, ...props }) => {
                            return <span {...props} className="font-medium" />;
                          },
                          a: ({ node, ...props }) => {
                            return <a className="text-[#00376B]" {...props} />;
                          },
                        }}
                      />
                    </div>
                  );
                }
              })}
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default InstagramFeedPreview;
