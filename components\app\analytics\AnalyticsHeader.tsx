import {
  ArrowDownIcon,
  ArrowUpIcon,
  MinusIcon,
} from "@heroicons/react/24/outline";
import classNames from "classnames";
import { ReactElement } from "react";
import { Divider, HelpTooltip } from "~/components";
import { RequireAtLeastOne } from "~/types/RequireAtLeastOne";
import { cn, formatPercentage } from "~/utils";

type StatItem<T> = {
  label: string;
  value: T;
  prevValue?: T;
  formatter?: (value: NonNullable<T>) => string;
  isHidden?: boolean;
};

type Props<T> = {
  account?: RequireAtLeastOne<
    {
      name: string | null;
      username: string | null;
      profileImageUrl: string;
    },
    "name" | "username"
  > | null;
  stats: StatItem<T>[];
  topPerformers?: {
    category: string;
    values: {
      key: string;
      label: string | ReactElement;
      href?: string;
      value: number | string;
    }[];
    emptyMessage?: string;
  }[];
  onClick?: () => void;
  previousPeriod?: string;
};

const AnalyticsHeader = <T extends string | number | null>({
  account,
  stats,
  topPerformers,
  onClick,
  previousPeriod,
}: Props<T>) => {
  const hasPrevValue = stats.some((stat) => stat.prevValue != null);

  return (
    <div
      className={cn(
        "border-lightest-gray mb-4 w-full rounded-lg border p-10 transition-all",
        {
          "hover:border-basic cursor-pointer": onClick,
        }
      )}
      onClick={onClick}
    >
      <div className="flex w-full gap-8">
        {account && (
          <div className="space-y-5">
            <div className="flex w-[250px] items-center gap-3">
              <img
                src={account.profileImageUrl.replace("_normal", "")}
                className="h-9 w-9 rounded-full"
              />
              <div className="font-body-sm tracking-[-0.01em]">
                <div className="font-medium">
                  {account.name ? account.name : `${account.username}`}
                </div>
                {account.name && account.username && (
                  <div className="text-medium-gray">{account.username}</div>
                )}
              </div>
            </div>
            {hasPrevValue && (
              <div className="flex items-center gap-2">
                <div className="font-eyebrow text-medium-dark-gray">
                  Prev. Total
                </div>
                {previousPeriod && (
                  <HelpTooltip
                    value={`Comparing to the previous period: ${previousPeriod}`}
                  />
                )}
              </div>
            )}
          </div>
        )}
        <div className="flex w-full items-start justify-between">
          {stats.map((stat) => {
            if (stat.isHidden) {
              return null;
            }

            return (
              <div key={stat.label}>
                <div className="font-eyebrow text-medium-gray mb-1">
                  {stat.label}
                </div>
                {stat.value == null ? (
                  <MinusIcon className="text-medium-gray my-1 h-4 w-4" />
                ) : (
                  <div className="space-y-3">
                    <h4>
                      {stat.formatter ? stat.formatter(stat.value) : stat.value}
                    </h4>
                    {stat.prevValue != null && (
                      <div className="font-body-sm flex items-center gap-1.5">
                        {stat.formatter
                          ? stat.formatter(stat.prevValue)
                          : stat.prevValue}
                        <PercentChange
                          value={stat.value}
                          prevValue={stat.prevValue}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
      {!!topPerformers && <Divider padding="lg" />}
      {!!topPerformers && (
        <div>
          <div className="font-body-sm mb-5 font-medium">Top Performers</div>
          <div className="divide-lightest-gray grid grid-cols-4 gap-8 divide-x">
            {topPerformers.map((performer, index) => {
              return (
                <div
                  key={performer.category}
                  className={classNames({
                    "pl-8": index !== 0,
                  })}
                >
                  <div className="font-eyebrow text-medium-gray">
                    {performer.category}
                  </div>
                  <div className="font-body-sm mt-1 space-y-2">
                    {performer.values.map((value) => {
                      return (
                        <div
                          key={value.key}
                          className="flex items-center justify-between gap-4"
                        >
                          {value.href ? (
                            <a
                              className="hover:text-basic overflow-hidden text-ellipsis whitespace-nowrap transition-all"
                              rel={
                                value.href ? "noopener noreferrer" : undefined
                              }
                              target={value.href ? "_blank" : undefined}
                              href={value.href}
                            >
                              {value.label}
                            </a>
                          ) : (
                            <div className="overflow-clip text-ellipsis whitespace-nowrap">
                              {value.label}
                            </div>
                          )}
                          <div>{value.value}</div>
                        </div>
                      );
                    })}
                    {performer.values.length === 0 && (
                      <div className="text-medium-gray">
                        {performer.emptyMessage}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

const PercentChange = ({
  value,
  prevValue,
}: {
  value: number | string | null;
  prevValue: number | string | null;
}) => {
  if (typeof value !== "number" || typeof prevValue !== "number") {
    return null;
  }

  return (
    <div
      className={cn("font-eyebrow flex items-center gap-0.5", {
        "text-danger": value < prevValue,
        "text-success": value > prevValue,
        "text-medium-gray": value === prevValue,
      })}
    >
      {value >= prevValue ? (
        <ArrowUpIcon className="h-2.5 w-2.5" />
      ) : (
        <ArrowDownIcon className="h-2.5 w-2.5" />
      )}
      {formatPercentage(Math.abs(value - prevValue), prevValue)}
    </div>
  );
};

export default AnalyticsHeader;
