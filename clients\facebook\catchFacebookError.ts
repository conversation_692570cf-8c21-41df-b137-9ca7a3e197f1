import axios from "axios";
import { StatusCodes } from "http-status-codes";
import { createErrorResponse, Response } from "../Response";

// https://developers.facebook.com/docs/instagram-api/reference/error-codes
export const INSTAGRAM_ERROR_CODE_MESSAGES = {
  "2207003": "Media took too long to download.",
  "2207020":
    "The media you are trying to access has expired. Please try to upload again.",
  "2207001": "Instagram server error. Please try to upload again.",
  "2207032": "Create media fail, please try to re-create media",
  "2207053": "Unknown upload error. Please try to upload again.",
  "2207057":
    "Thumbnail offset must be greater than or equal to 0 and less than video duration",
  "2207051": "The publishing action is suspected to be spam",
  "2207042":
    "You reached maximum number of posts that is allowed to be published by Content Publishing API.",
  "2207006":
    "Possible permission error due to missing permission or expired token. Generate a new container and use it to try again.",
  "2207008":
    "Temporary error publishing a container. Try publishing again or if unsuccessful, generate a new container ID and use it to try again.",
  "2207050":
    "The app user's Instagram Professional account is inactive, checkpointed, or restricted. Please sign in to the Instagram app and complete any actions the app requires to re-enable your account.",
  "2207023":
    "The media type entered is not one of CAROUSEL_ALBUM, IMAGE, or VIDEO.",
  "2207028":
    "Your post won't work as a carousel. Carousels need at least 2 photos/videos and no more than 10 photos/videos.",
  "2207035": "Product tag positions should not be specified for video media.",
  "2207036": "Product tag positions are required for photo media.",
  "2207037":
    "All of your product tags couldn't be added. The product ID may be incorrect, the product may be deleted, or you may not have permission to tag the product.",
  "2207040": "Cannot use more than 20 tags per created media.",
  "2207026":
    "The video format is not supported. Please check spec for supported format",
  "2207052": "The media could not be fetched from this URL",
  "2207027": "The media is not ready for publishing, please wait for a moment",
  "2207004": "The image is too large to download",
  "2207005": "The image format is not supported",
  "2207009":
    "The image's aspect ratio does not fall within 4:5 to 1.91:1 range.",
  "2207010":
    "The caption is too long. A caption can have a maximum 2,200 characters, 30 hashtags, and 20 @ tags.",
};

/**
 * Instagram Errors: https://developers.facebook.com/docs/instagram-api/reference/error-codes
 */
const catchFacebookError = (error: unknown): Response<any> => {
  console.error("Facebook error", error);
  try {
    if (axios.isAxiosError(error)) {
      const response = error.response;
      console.error(error.response?.data);
      const fbError = response?.data.error;
      let message = fbError.message || fbError.debug_info?.message;
      const rawUserTitle = response?.data.error.error_user_title;
      let userTitle = null;
      if (rawUserTitle) {
        userTitle = response?.data.error.error_user_title.replace(
          "Cannot load user with a private account or invalid username",
          "Cannot tag or collaborate with a user with a private account or invalid username"
        );
        message = response?.data.error.error_user_msg || message;
        if (message === "An unknown error occurred") {
          if (fbError.code === 1) {
            message =
              "Possibly a temporary issue due to downtime. Wait and retry the operation";
          }
        }
      }
      const userErrorMessage = userTitle ? `${userTitle}. ${message}` : message;

      let errorType = null;
      let isTransient = true;
      try {
        errorType = response?.data.error.type;
        isTransient = !!response?.data.error.is_transient;
      } catch (error) {
        console.error("Error getting error type", response?.data);
      }

      const isAuthError =
        errorType === "OAuthException" &&
        !isTransient &&
        !userErrorMessage.includes("please try to re-create media");

      return createErrorResponse(
        [userErrorMessage],
        response?.status || StatusCodes.UNPROCESSABLE_ENTITY,
        response?.data,
        isAuthError
      );
    } else {
      return createErrorResponse(
        ["Error making query"],
        StatusCodes.UNPROCESSABLE_ENTITY
      );
    }
  } catch (error) {
    console.error("Error catching facebook error", error);
    return createErrorResponse(
      ["Error making query"],
      StatusCodes.UNPROCESSABLE_ENTITY
    );
  }
};

export default catchFacebookError;
