import { FacebookStatusType } from "@prisma/client";

export type Attachment = {
  description?: string;
  media_type: "photo" | "video";
  media: {
    image?: {
      src: string;
      height: number;
      width: number;
    };
    source?: string;
  };
  subattachments?: {
    data: SubAttachment[];
  };
};

export type SubAttachment = {
  media_type: "photo" | "video";
  media: {
    image?: {
      src: string;
      height: number;
      width: number;
    };
    source?: string;
  };
  target?: {
    id: string;
    url: string;
  };
  type: string;
  url: string;
};

export type FBPost = {
  id: string;
  caption: string | null;
  permalinkUrl: string;
  statusType: FacebookStatusType;
  imageUrls: string[];
  videoUrls: string[];
  messageTags: string[];
  messageTagsCount: number;
  hasEvent: boolean;
  isPopular: boolean;
  isHidden: boolean;
  isPublished: boolean;
  isSpherical: boolean;
  impressionCount: number;
  shareCount: number;
  reactionCount: number;
  commentCount: number;
  publishedAt: Date;
  rawResponse: RawFBPost;
};

export type RawFBPost = {
  id: string;
  message: string;
  message_tags?: {
    id: string;
    name: string;
    type: string;
  }[];
  permalink_url: string;
  status_type: FacebookStatusType;
  event?: {
    id: string;
    name: string;
  };
  is_popular: boolean;
  is_hidden: boolean;
  is_published: boolean;
  is_spherical: boolean;
  shares?: {
    count: number;
  };
  comments?: {
    data: {
      id: string;
      message: string;
    }[];
  };
  attachments?: {
    data: Attachment[];
  };
  insights: {
    data: {
      id: string;
      title: string;
      description: string;
      name: "post_impressions" | "post_reactions_like_total";
      period: "lifetime";
      values: {
        value: number;
      }[];
    }[];
  };
  created_time: string;
};
