import { useCallback, useEffect, useRef, useState } from "react";

import maxBy from "lodash/maxBy";
import sortBy from "lodash/sortBy";

import { BoltIcon, PlusIcon } from "@heroicons/react/24/outline";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/solid";
import { Channel, PostStatus, StartOfWeek } from "@prisma/client";
import classNames from "classnames";
import { useRouter } from "next/router";

import { toast } from "sonner";
import {
  ButtonGroup,
  FilterDropdown,
  IconButton,
  Kbd,
  Loader,
} from "~/components";
import DropdownMenu from "~/components/ui/DropdownMenu";

import { useCalendarView, useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP, FilterType, POST_STATUSES } from "~/types";
import {
  capitalizeWords,
  getDateUTC,
  getFullName,
  getKeys,
  getPostPublishAt,
  getPostPublishAtMoment,
  openConfirmationModal,
  removeEmptyElems,
  trpc,
} from "~/utils";
import { NewCampaignModal } from "../campaigns";
import NewPostModal from "../posts/NewPostModal";
import CalendarDay from "./CalendarDay";

import { DateTime } from "luxon";
import Link from "next/link";
import { parseAsIsoDate, useQueryState } from "nuqs";
import ReactHotkeys from "react-hot-keys";
import WHITELABELED_AGENCIES from "~/constants/whitelabledAgencies";
import { useBreakpoints } from "~/hooks";
import { CalendarView, TimeFrame } from "~/providers/CalendarViewProvider";
import { usePosts } from "~/providers/PostProvider";
import { RouterOutput } from "~/utils/trpc";
import ChannelIcon from "../ChannelIcon";
import KbdTooltip from "../KbdTooltip";
import { Label } from "../LabelSelect";
import CalendarViewToggle from "./CalendarViewToggle";
import PostList from "./PostList";
import SaveFilterButton from "./SaveFilterButton";

type Post = RouterOutput["post"]["getMany"]["posts"][number];
type Campaign = RouterOutput["campaign"]["getAll"]["campaigns"][number];

type Props = {
  view: CalendarView;
  posts?: Post[];
  campaigns?: Campaign[];
  labels?: Label[];
  defaultCampaignID?: string;
  campaignStartDate?: Date;
  isLoading: boolean;
  isReadOnly?: boolean;
  hideHeader?: boolean;
  disableCreatePost?: boolean;
};

const Calendar = ({
  view,
  posts = [],
  campaigns: initialCampaigns = [],
  labels = [],
  defaultCampaignID,
  campaignStartDate,
  isLoading,
  isReadOnly,
  hideHeader = false,
  disableCreatePost = false,
}: Props) => {
  const trpcUtils = trpc.useUtils();
  const { currentWorkspace } = useWorkspace();
  const router = useRouter();
  const userPreferencesQuery = trpc.user.getUserAndPreferences.useQuery();
  const startOfWeekPreference =
    userPreferencesQuery.data?.user.startOfWeek ?? "Sunday";

  const companyID = currentWorkspace?.company.id;
  const shouldHideAI =
    companyID != null
      ? WHITELABELED_AGENCIES[companyID as keyof typeof WHITELABELED_AGENCIES]
          ?.hideAI || false
      : false;

  const { newPost, setNewPost } = usePosts();
  const { isMobile } = useBreakpoints();

  const [startDate, setStartDate] = useQueryState(
    "startDate",
    parseAsIsoDate.withDefault(DateTime.now().startOf("day").toJSDate())
  );

  const getWorkspaceUsersQuery = trpc.workspace.getUsers.useQuery();
  const getWorkspaceSchedulingProfilesQuery =
    trpc.workspace.getSchedulingProfiles.useQuery();

  const updateLabelsMutation = trpc.label.updateLabels.useMutation();

  const updatePostMutation = trpc.post.update.useMutation({
    onSuccess: (data) => {
      trpcUtils.post.get.invalidate({ id: data.post.id });
      trpcUtils.post.getMany.invalidate();
    },
  });

  const mostRecentPost = maxBy(posts, (post) => post.createdAt);
  const mostRecentPostChannel = mostRecentPost
    ? mostRecentPost.channels[0]
    : undefined;

  const deletePostMutation = trpc.post.delete.useMutation({
    onSuccess: (data) => {
      trpcUtils.post.getMany.invalidate();
      trpcUtils.post.get.invalidate({ id: data.post.id });
      if (defaultCampaignID) {
        trpcUtils.campaign.get.refetch({
          id: defaultCampaignID,
        });
      }
    },
  });

  const addNewPost = (post: Post) => {
    const newPosts = sortBy([...posts, post], ["publishDate", "publishAt"]);
    trpcUtils.post.getMany.setData(undefined, {
      posts: newPosts,
    });
  };

  const getStartOfWeek = (date: DateTime, startDay: StartOfWeek): DateTime => {
    if (startDay === "Monday") {
      return date.startOf("week");
    } else {
      // Sunday as start of week
      const daysToSubtract = date.weekday === 7 ? 0 : date.weekday;
      return date.minus({ days: daysToSubtract }).startOf("day");
    }
  };

  const getEndOfWeek = (date: DateTime, startDay: StartOfWeek): DateTime => {
    if (startDay === "Monday") {
      // ISO: Monday start, Sunday end
      return date.endOf("week");
    } else {
      // Sunday start, Saturday end
      // weekday: 1 (Monday) ... 7 (Sunday)
      // To get to Saturday: 6 - (weekday % 7)
      const daysForward = 6 - (date.weekday % 7);
      return date.plus({ days: daysForward }).endOf("day");
    }
  };

  useEffect(() => {
    if (newPost) {
      addNewPost(newPost);
      setNewPost(null);
    }
  }, [newPost]);

  useEffect(() => {
    if (isMobile) {
      setTimeFrame("2 days");
    } else if (timeFrame === "2 days") {
      setTimeFrame("2 weeks");
    }
  }, [isMobile]);

  const deletePost = (post: { id: string; title: string }) => {
    openConfirmationModal({
      title: `Delete ${post.title} Post?`,
      body: "This action cannot be undone. Please confirm you want to delete this post.",
      cancelButton: {
        label: "Cancel, don't delete",
      },
      primaryButton: {
        label: "Delete Post",
        variant: "danger",
        onClick: () => {
          const postIndex = posts.findIndex(
            (prevPost) => prevPost.id == post.id
          );

          const newPosts = [...posts];
          const withPostRemoved = [
            ...newPosts.slice(0, postIndex),
            ...newPosts.slice(postIndex + 1),
          ];

          trpcUtils.post.getMany.setData(undefined, {
            posts: withPostRemoved,
          });

          deletePostMutation.mutate(
            {
              id: post.id,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getMany.invalidate();
              },
            }
          );
        },
      },
    });
  };

  const workspaceUsers = getWorkspaceUsersQuery.data?.users || [];
  const workspaceSchedulingProfiles =
    getWorkspaceSchedulingProfilesQuery.data?.schedulingProfiles || [];

  const [initialCampaignsLoaded, setInitialCampaignsLoaded] =
    useState<boolean>(false);
  const [campaigns, setCampaigns] = useState<Campaign[]>(initialCampaigns);
  useEffect(() => {
    if (
      !initialCampaignsLoaded &&
      initialCampaigns.length !== campaigns.length
    ) {
      setCampaigns(initialCampaigns);
      setInitialCampaignsLoaded(initialCampaignsLoaded);
    }
  }, [initialCampaigns]);

  const headerRef = useRef<HTMLHeadElement>(null);

  const [filters, setFilters] = useState<FilterType>({
    channels: [],
    statuses: [],
    subscriberIDs: [],
    campaignIDs: [],
    labelIDs: [],
    accountIDs: [],
  });
  const [hoveredCampaignID, setHoveredCampaignID] = useState<string | null>(
    null
  );
  const [newCampaignModalOpen, setNewCampaignModalOpen] =
    useState<boolean>(false);
  const [defaultPublishDate, setDefaultPublishDate] = useState<Date>(
    new Date()
  );
  const [newPostModalOpen, setNewPostModalOpen] = useState<boolean>(false);

  const updatePost = useCallback(
    (
      postID: string,
      updatedValues: {
        publishDate?: Date;
        campaign?: Campaign | null;
        labels?: Label[];
        status?: PostStatus;
      }
    ) => {
      const postIndex = posts.findIndex((post) => post.id == postID);

      const { campaign, publishDate, ...rest } = updatedValues;

      const newPosts = [...posts];
      const previousPost = newPosts[postIndex];
      const previousCampaign = campaigns.find(
        (campaign) => campaign.id === previousPost.campaignID
      );
      const post = {
        ...previousPost,
        ...rest,
        campaignID: campaign == null ? previousPost.campaignID : campaign.id,
        campaign: campaign == null ? previousCampaign : campaign,
        publishDate:
          publishDate == null
            ? previousPost.publishDate
            : getDateUTC(publishDate),
        publishAt:
          publishDate == null
            ? previousPost.publishAt
            : getPostPublishAt(publishDate, previousPost.publishAt) || null,
      };

      trpcUtils.post.getMany.cancel();

      if (updatedValues.labels != null) {
        updateLabelsMutation.mutate(
          {
            contentID: postID,
            contentType: "post",
            labels: updatedValues.labels,
          },
          {
            onSuccess: () => {
              trpcUtils.post.getMany.invalidate();
              trpcUtils.label.getAll.refetch();
            },
          }
        );
      } else {
        const campaignID = campaign ? campaign.id : campaign;
        updatePostMutation.mutate(
          {
            id: postID,
            campaignID,
            ...rest,
            publishDate: publishDate == null ? undefined : post.publishDate,
            publishAt: publishDate == null ? undefined : post.publishAt,
          },
          {
            onSuccess: () => {
              trpcUtils.post.getMany.invalidate();
              trpcUtils.post.get.invalidate({ id: postID });
            },
          }
        );
      }

      newPosts[postIndex] = post;

      trpcUtils.post.getMany.setData(undefined, {
        posts: newPosts,
      });

      if (defaultCampaignID) {
        const campaignData = trpcUtils.campaign.get.getData({
          id: defaultCampaignID,
        });
        if (campaignData) {
          const campaignPosts = campaignData.campaign.posts;
          const campaignPost = campaignPosts.find((p) => p.id === postID);
          if (campaignPost) {
            const updatedPosts = campaignPosts.map((p) => {
              if (p.id === postID) {
                return {
                  ...p,
                  publishDate: post.publishDate,
                  publishAt: post.publishAt,
                };
              } else {
                return p;
              }
            });

            trpcUtils.campaign.get.setData(
              { id: defaultCampaignID },
              {
                ...campaignData,
                campaign: {
                  ...campaignData.campaign,
                  posts: updatedPosts,
                },
              }
            );
          }
        }
      }
    },
    [posts]
  );

  useEffect(() => {
    if (router.asPath !== router.route) {
      const query = router.query;

      const initialFilters = {
        // @ts-ignore: always a string
        channels: query.channels?.split(",") || [],
        // @ts-ignore: always a string
        statuses: query.statuses?.split(",") || [],
        subscriberIDs:
          // @ts-ignore: always a string
          query.subscriberIDs?.split(",") ||
          // @ts-ignore: always a string
          query.assigneeIDs?.split(",") ||
          [],
        // @ts-ignore: always a string
        campaignIDs: query.campaignIDs?.split(",") || [],
        // @ts-ignore: always a string
        labelIDs: query.labelIDs?.split(",") || [],
        // @ts-ignore: always a string
        accountIDs: query.accountIDs?.split(",") || [],
      };

      setFilters(initialFilters);
    }
  }, [router]);

  const {
    setView,
    timeFrame: workspaceTimeFrame,
    setTimeFrame,
    showCampaigns,
    setShowCampaigns,
  } = useCalendarView();

  const timeFrame: TimeFrame = hideHeader ? "1 week" : workspaceTimeFrame;

  const getCalendarStart = (date: DateTime): DateTime => {
    if (timeFrame === "2 days") {
      return date.startOf("day");
    } else if (timeFrame === "1 month") {
      return getStartOfWeek(date.startOf("month"), startOfWeekPreference);
    } else {
      return getStartOfWeek(date, startOfWeekPreference);
    }
  };

  const startOfWeek = getCalendarStart(DateTime.fromJSDate(startDate));

  let numDays = 0;
  switch (timeFrame) {
    case "2 days":
      numDays = 2;
      break;
    case "1 week":
      numDays = 7;
      break;
    case "2 weeks":
      numDays = 14;
      break;
    case "1 month":
      const firstOfMonth = DateTime.fromJSDate(startDate).startOf("month");
      const firstDayOfWeek = getStartOfWeek(
        firstOfMonth,
        startOfWeekPreference
      );

      const lastOfMonth = firstOfMonth.endOf("month");
      const lastDayOfWeek = getEndOfWeek(lastOfMonth, startOfWeekPreference);

      numDays = Math.round(lastDayOfWeek.diff(firstDayOfWeek, "days").days);
      break;
  }

  const campaignOffsetMapping: { [id: string]: number } = {};

  const updateFilter = (type: keyof typeof filters, value: string) => {
    const selectedValues = filters[type];

    const index = selectedValues.findIndex((elem) => elem === value);

    let newFilters = filters;

    if (index >= 0) {
      selectedValues.splice(index, 1);

      newFilters = {
        ...filters,
        [type]: selectedValues,
      };
    } else {
      newFilters = {
        ...filters,
        [type]: [...selectedValues, value],
      };
    }

    setFilters(newFilters);

    updateParams(newFilters);
  };

  const updateParams = (filters: FilterType, filterID?: string) => {
    const paramFilters: { [filter: string]: string } = {};

    getKeys(filters).forEach((key) => {
      if (filters[key].length) {
        paramFilters[key] = filters[key].join(",");
      }
    });

    if (filterID) {
      paramFilters["id"] = filterID;
    }
    const currentParams = new URLSearchParams(window.location.search);
    if (currentParams.has("startDate")) {
      paramFilters["startDate"] = currentParams.get("startDate")!;
    }

    const params = new URLSearchParams(paramFilters);
    if (typeof window !== "undefined") {
      const newURL = new URL(window.location.href);
      newURL.search = params.toString();

      router.replace(newURL.href, undefined, { scroll: false });
    }
  };

  const filteredPosts = posts.filter((post) => {
    const passedChannelFilter =
      filters.channels.length === 0 ||
      post.channels.some((channel) => filters.channels.includes(channel));

    const passedStatusFilter =
      filters.statuses.length === 0 || filters.statuses.includes(post.status);

    const hasChannel = (channel: Channel) => post.channels.includes(channel);

    const postAccountIDs = removeEmptyElems([
      hasChannel("Discord") ? post.discordWebhookID : null,
      hasChannel("Threads") ? post.threadsIntegrationID : null,
      hasChannel("Instagram") ? post.instagramIntegrationID : null,
      hasChannel("Facebook") ? post.facebookIntegrationID : null,
      hasChannel("LinkedIn") ? post.linkedInIntegrationID : null,
      hasChannel("TikTok") ? post.tikTokIntegrationID : null,
      hasChannel("YouTubeShorts") ? post.youTubeIntegrationID : null,
      hasChannel("Twitter") ? post.twitterIntegrationID : null,
      hasChannel("Slack") ? post.slackIntegrationID : null,
      hasChannel("Webflow") ? post.webflowIntegrationID : null,
    ]);

    const passedAccountFilter =
      filters.accountIDs.length === 0 ||
      postAccountIDs.some((accountID) =>
        filters.accountIDs.includes(accountID)
      );

    const passedSubscriberFilter =
      filters.subscriberIDs.length === 0 ||
      post.subscribers.some((subscriber) =>
        filters.subscriberIDs.includes(subscriber.userID)
      );

    const passedCampaignFilter =
      filters.campaignIDs.length === 0 ||
      filters.campaignIDs.includes(post.campaignID || "");

    const labelsFilter =
      filters.labelIDs.length === 0 ||
      post.labels.some((label) => filters.labelIDs.includes(label.id));

    // Filter to only the posts visible in the current time frame
    const postPublishAt = getPostPublishAtMoment(post);

    const timeFrameStart = startOfWeek.startOf("day").minus({ day: 1 });
    const timeFrameEnd = startOfWeek
      .plus({ days: numDays })
      .endOf("day")
      .plus({ day: 1 });

    const passedTimeFrameFilter =
      view === "list" ||
      (postPublishAt >= timeFrameStart && postPublishAt <= timeFrameEnd) ||
      postPublishAt.diff(timeFrameStart, "millisecond").milliseconds === 0 ||
      postPublishAt.diff(timeFrameEnd, "millisecond").milliseconds === 0;

    return (
      passedChannelFilter &&
      passedStatusFilter &&
      passedAccountFilter &&
      passedSubscriberFilter &&
      passedCampaignFilter &&
      labelsFilter &&
      passedTimeFrameFilter
    );
  });

  const handleDateChange = (direction: "prev" | "next") => {
    const currentDate = DateTime.fromJSDate(startDate);

    if (timeFrame === "2 days") {
      setStartDate(
        currentDate.plus({ days: direction === "next" ? 2 : -2 }).toJSDate()
      );
    } else if (timeFrame === "1 week") {
      setStartDate(
        currentDate.plus({ days: direction === "next" ? 7 : -7 }).toJSDate()
      );
    } else if (timeFrame === "2 weeks") {
      setStartDate(
        currentDate.plus({ days: direction === "next" ? 14 : -14 }).toJSDate()
      );
    } else if (timeFrame === "1 month") {
      setStartDate(
        currentDate.plus({ months: direction === "next" ? 1 : -1 }).toJSDate()
      );
    }
  };

  const goToStartDate = () => {
    setStartDate(DateTime.now().startOf("day").toJSDate());
  };

  return (
    <div className="z-0 flex h-full flex-col">
      {isLoading ? (
        <div className="flex w-full justify-center pt-8">
          <Loader size="lg" />
        </div>
      ) : (
        <div className="z-0 flex h-full flex-col">
          <header
            ref={headerRef}
            className={classNames(
              "flex flex-none items-center justify-between px-2 pt-2",
              {
                hidden: hideHeader,
              }
            )}
          >
            <div className="flex items-center gap-4">
              <div className="mr-1 w-full">
                <CalendarViewToggle view={view} setView={setView} />
              </div>
              <FilterDropdown
                sections={[
                  {
                    name: "Channel",
                    options: getKeys(CHANNEL_ICON_MAP).map((channel) => {
                      return {
                        label: CHANNEL_ICON_MAP[channel].label,
                        value: channel,
                        selected: filters.channels.includes(channel),
                        onClick: () => updateFilter("channels", channel),
                      };
                    }),
                  },
                  {
                    name: "Status",
                    options: POST_STATUSES.map((status) => {
                      return {
                        label: capitalizeWords(status),
                        value: status,
                        selected: filters.statuses.includes(status),
                        onClick: () => updateFilter("statuses", status),
                      };
                    }),
                  },
                  {
                    name: "Label",
                    options: labels.map((label) => {
                      return {
                        value: label.id,
                        label: label.name,
                        selected: filters.labelIDs.includes(label.id),
                        onClick: () => updateFilter("labelIDs", label.id),
                      };
                    }),
                  },
                  {
                    name: "Social Profile",
                    options: workspaceSchedulingProfiles.map((profile) => {
                      return {
                        value: profile.id,
                        label: `${profile.name} ${
                          profile.detail ? `${profile.detail}` : ""
                        }`,
                        component: (
                          <div className="flex items-center gap-1.5">
                            <ChannelIcon channel={profile.channel} width={16} />
                            {profile.detail || profile.name}
                          </div>
                        ),
                        selected: filters.accountIDs.includes(profile.id),
                        onClick: () => updateFilter("accountIDs", profile.id),
                      };
                    }),
                  },
                  {
                    name: "Subscriber",
                    options: workspaceUsers.map((user) => ({
                      label: getFullName(user),
                      value: user.id,
                      selected: filters.subscriberIDs.includes(user.id),
                      onClick: () => updateFilter("subscriberIDs", user.id),
                    })),
                  },
                  {
                    name: "Campaign",
                    options: campaigns.map((campaign) => {
                      return {
                        value: campaign.id,
                        label: campaign.name,
                        selected: filters.campaignIDs.includes(campaign.id),
                        onClick: () => updateFilter("campaignIDs", campaign.id),
                      };
                    }),
                  },
                ]}
              />
              <SaveFilterButton filters={filters} updateParams={updateParams} />
            </div>
            {!isReadOnly && (
              <div className="flex items-center gap-2">
                <Link
                  href="/prompts"
                  className={classNames(
                    "font-body-sm text-basic-light to-basic bg-opacity-30 relative flex h-7 items-center gap-1 rounded-md bg-linear-to-r from-[#72A184] px-2",
                    {
                      hidden: shouldHideAI,
                    }
                  )}
                >
                  <div className="absolute inset-0 h-full w-full rounded-md bg-linear-to-b from-white/30 to-white/70 opacity-0 transition duration-500 hover:opacity-30" />
                  <BoltIcon className="h-4 w-4" />
                  Assembly AI
                </Link>
                <ReactHotkeys
                  keyName="C"
                  onKeyDown={(keyName) => {
                    if (keyName.toLowerCase() === "c") {
                      setNewCampaignModalOpen(true);
                    }
                  }}
                >
                  <DropdownMenu.Root>
                    <DropdownMenu.Trigger>
                      <div className="bg-basic hover:bg-basic/80 text-basic-light rounded-md px-1 py-1 transition-colors">
                        <PlusIcon className="h-5 w-5" />
                      </div>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content position="bottom-left" align="end">
                      <DropdownMenu.Group>
                        <DropdownMenu.Item
                          icon="PlusIcon"
                          onClick={() => {
                            setDefaultPublishDate(new Date());
                            setNewPostModalOpen(true);
                          }}
                        >
                          <span>Create Post</span>
                          <DropdownMenu.Shortcut>
                            <Kbd>P</Kbd>
                          </DropdownMenu.Shortcut>
                        </DropdownMenu.Item>
                        <DropdownMenu.Item
                          icon="PlusIcon"
                          onClick={() => {
                            setNewCampaignModalOpen(true);
                          }}
                        >
                          <span>Create Campaign</span>
                          <DropdownMenu.Shortcut>
                            <Kbd>C</Kbd>
                          </DropdownMenu.Shortcut>
                        </DropdownMenu.Item>
                      </DropdownMenu.Group>
                    </DropdownMenu.Content>
                  </DropdownMenu.Root>
                </ReactHotkeys>
              </div>
            )}
          </header>
          <div className="relative h-full">
            {view === "calendar" ? (
              <div className="relative h-full">
                <div className="bg-surface h-full overflow-clip pt-4">
                  <div className="flex max-w-full flex-none flex-col sm:max-w-none md:max-w-full">
                    {/* Header just for calendar view */}
                    <div className="mb-4 ml-3 flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="font-body w-32">
                          {timeFrame === "1 month"
                            ? DateTime.fromJSDate(startDate)
                                .startOf("month")
                                .toFormat("MMMM")
                            : startOfWeek.toFormat("MMMM")}{" "}
                          <span className="text-medium-gray">
                            {" "}
                            {timeFrame === "1 month"
                              ? DateTime.now()
                                  .plus({
                                    days: DateTime.fromJSDate(startDate).day,
                                  })
                                  .startOf("month")
                                  .toFormat("yyyy")
                              : startOfWeek.toFormat("yyyy")}
                          </span>
                        </div>
                        <div className="text-basic flex items-center md:items-stretch">
                          <KbdTooltip
                            label="Back"
                            hotKeys={"←"}
                            keyName="left"
                            onKeyPress={() => handleDateChange("prev")}
                          >
                            <button
                              type="button"
                              className="hover:bg-slate flex items-center justify-center rounded-md px-0.5 outline-hidden transition-all"
                              onClick={() => handleDateChange("prev")}
                            >
                              <span className="sr-only">Previous month</span>
                              <ChevronLeftIcon
                                className="h-3 w-3"
                                aria-hidden="true"
                              />
                            </button>
                          </KbdTooltip>
                          <KbdTooltip
                            label="Today"
                            hotKeys="T"
                            onKeyPress={goToStartDate}
                          >
                            <button
                              type="button"
                              className="bg-surface font-body-sm hover:bg-slate rounded-md px-2 transition-all"
                              onClick={goToStartDate}
                            >
                              Today
                            </button>
                          </KbdTooltip>
                          <KbdTooltip
                            label="Forward"
                            hotKeys={"→"}
                            keyName="right"
                            onKeyPress={() => handleDateChange("next")}
                          >
                            <button
                              type="button"
                              className="hover:bg-slate flex items-center justify-center rounded-md px-0.5 outline-hidden transition-all"
                              onClick={() => handleDateChange("next")}
                            >
                              <span className="sr-only">Next month</span>
                              <ChevronRightIcon
                                className="h-3 w-3"
                                aria-hidden="true"
                              />
                            </button>
                          </KbdTooltip>
                        </div>
                      </div>
                      <div
                        className={classNames("mr-2 gap-2", {
                          hidden: hideHeader,
                          "hidden items-center md:flex": !hideHeader,
                        })}
                      >
                        <IconButton
                          icon={showCampaigns ? "EyeSlashIcon" : "EyeIcon"}
                          label={
                            showCampaigns ? "Hide Campaigns" : "Show Campaigns"
                          }
                          onClick={() => {
                            setShowCampaigns(!showCampaigns);
                          }}
                          intent="none"
                        />
                        <ButtonGroup
                          kind="bare"
                          buttons={[
                            {
                              key: "1 week",
                              label: "1 Week",
                              onClick: () => {
                                setTimeFrame("1 week");
                              },
                              selected: timeFrame === "1 week",
                              hotKey: "W",
                            },
                            {
                              key: "2 week",
                              label: "2 Weeks",
                              onClick: () => {
                                setTimeFrame("2 weeks");
                              },
                              selected: timeFrame === "2 weeks",
                              hotKey: "X",
                            },
                            {
                              key: "1 month",
                              label: "1 Month",
                              onClick: () => {
                                setTimeFrame("1 month");
                              },
                              selected: timeFrame === "1 month",
                              hotKey: "M",
                            },
                          ]}
                        />
                      </div>
                    </div>

                    {/* Header with days of week */}
                    <div className="bg-surface sticky top-0 flex-none">
                      <div className="font-eyebrow text-medium-gray ml-1 grid grid-cols-3 text-left md:grid-cols-7">
                        {Array.apply(0, Array(Math.min(numDays, 7))).map(
                          (_, day) => {
                            const currentDay = startOfWeek.plus({ day: day });
                            return (
                              <div key={day} className="pl-2">
                                {currentDay.toFormat("EEE")}
                              </div>
                            );
                          }
                        )}
                      </div>
                    </div>
                  </div>
                  <div
                    style={{
                      height: `calc(100% - 75px)`,
                    }}
                    className={classNames(
                      "mt-2 grid grid-cols-2 overflow-hidden text-left md:grid-cols-7",
                      {
                        "grid-rows-1":
                          timeFrame === "2 days" || timeFrame === "1 week",
                        "grid-rows-2": timeFrame === "2 weeks",
                        "grid-rows-5":
                          timeFrame === "1 month" &&
                          Math.ceil(numDays / 7) === 5,
                        "grid-rows-6":
                          timeFrame === "1 month" &&
                          Math.ceil(numDays / 7) === 6,
                      }
                    )}
                  >
                    {Array.apply(0, Array(numDays)).map((_, day) => {
                      const currentDay = startOfWeek.plus({ day: day });

                      const dayPosts = filteredPosts.filter(
                        (post) =>
                          getPostPublishAtMoment(post).toFormat("M/dd/yyyy") ===
                          currentDay.toFormat("M/dd/yyyy")
                      );

                      const postsWithPublishTime = sortBy(
                        dayPosts.filter((post) => post.publishTime != null),
                        (post) => (post.publishAt ? post.publishAt : null)
                      );
                      const postsWithoutPublishTime = sortBy(
                        dayPosts.filter((post) => post.publishTime == null),
                        (post) => post.createdAt
                      );

                      const sortedPosts = [
                        ...postsWithPublishTime,
                        ...postsWithoutPublishTime,
                      ];

                      return (
                        <div
                          key={`${currentDay.toFormat("yyyy-MM-dd")}`}
                          className="row-span-1"
                          onMouseEnter={() => {
                            setDefaultPublishDate(currentDay.toJSDate());
                          }}
                        >
                          <CalendarDay
                            key={`${dayPosts.length}-${currentDay.toFormat(
                              "yyyy-MM-dd"
                            )}`}
                            day={day}
                            startOfWeek={startOfWeek.toJSDate()}
                            posts={sortedPosts}
                            campaigns={campaigns}
                            timeFrame={timeFrame}
                            filters={filters}
                            showCampaigns={showCampaigns}
                            deletePost={deletePost}
                            addNewPost={addNewPost}
                            updatePost={updatePost}
                            campaignOffsetMapping={campaignOffsetMapping}
                            hoveredCampaignID={hoveredCampaignID}
                            setHoveredCampaignID={setHoveredCampaignID}
                            setNewPostModalOpen={setNewPostModalOpen}
                            setDefaultPublishDate={setDefaultPublishDate}
                            isReadOnly={isReadOnly}
                            disableCreatePost={disableCreatePost}
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            ) : (
              <PostList
                posts={filteredPosts}
                campaigns={campaigns}
                updatePost={updatePost}
              />
            )}
          </div>
        </div>
      )}
      <NewPostModal
        open={newPostModalOpen}
        setOpen={(open: boolean) => {
          setNewPostModalOpen(open);
        }}
        defaultStatus="ToDo"
        defaultPublishDate={defaultPublishDate}
        defaultChannel={mostRecentPostChannel}
        defaultCampaignID={defaultCampaignID}
        onCreateSuccess={(post) => {
          trpcUtils.post.getMany.setData(undefined, {
            posts: [...posts, { ...post, approvals: [] }],
          });

          if (defaultCampaignID) {
            trpcUtils.campaign.get.refetch({
              id: defaultCampaignID,
            });
          }

          toast.success("Post Created", {
            id: post.id,
            description: "Click to view the post",
            action: {
              label: "View Post",
              onClick: () => router.push(`/posts/${post.id}`),
            },
          });
        }}
      />
      <NewCampaignModal
        open={newCampaignModalOpen}
        setOpen={setNewCampaignModalOpen}
      />
    </div>
  );
};

export default Calendar;
