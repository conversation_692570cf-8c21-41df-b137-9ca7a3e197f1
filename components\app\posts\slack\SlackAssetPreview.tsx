import Uppy from "@uppy/core";
import classNames from "classnames";
import { motion } from "framer-motion";
import { useState } from "react";
import AssetUploadContainer from "~/components/ui/AssetUploadContainer";
import { AssetType } from "~/types/Asset";
import { handleFilesUpload, trpc } from "~/utils";
import EditImageModal, {
  EditImageModalProperties,
} from "~/utils/EditImageModal";
import { UploadingAsset } from "../../files/AssetsNewDesign";
import FileDropzone from "../../files/FileDropzone";

const SlackAssetPreview = ({
  contentID,
  assets,
  uploadingAssets,
  setAssets,
  uppy,
  getValidFiles,
  draggedOver,
  setDraggedOver,
  isReadOnly,
}: {
  contentID: string;
  assets: AssetType[];
  uploadingAssets: UploadingAsset[];
  setAssets: (
    value: AssetType[] | ((prev: AssetType[]) => AssetType[])
  ) => void;
  uppy: Uppy | null;
  getValidFiles: (files: File[]) => Promise<File[]>;
  draggedOver: boolean;
  setDraggedOver: (isDragged: boolean) => void;
  isReadOnly: boolean;
}) => {
  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);

  const deleteAssetMutation = trpc.slackContent.deleteAsset.useMutation();

  const asset = assets?.[0];

  const onDeleteClick = (asset: AssetType) => {
    setAssets((prevAssets) =>
      prevAssets.filter((prevAsset) => prevAsset.id !== asset.id)
    );
    deleteAssetMutation.mutate({
      slackAssetID: asset.id,
    });
  };

  const onEditClick = (asset: AssetType) => {
    if (
      asset.metadata.mimetype.startsWith("image/") &&
      asset.metadata.mimetype !== "image/gif"
    ) {
      setEditImageModalProperties({
        title: "Edit Image",
        channel: "Slack",
        isOpen: true,
        asset,
      });
    }
  };

  const renderContent = () => {
    const newUploadingAssets = uploadingAssets.filter(
      (asset) => !asset.updateAssetID
    );

    const hasUploadedVideo = assets[0]?.metadata.mimetype.startsWith("video");
    const uploadingVideo = newUploadingAssets.find((asset) =>
      asset.type.startsWith("video/")
    );
    const isVideo = hasUploadedVideo || uploadingVideo;

    if (isVideo) {
      return (
        <AssetUploadContainer
          asset={!!uploadingVideo ? uploadingVideo : assets[0]}
          onDeleteClick={onDeleteClick}
          isReadOnly={isReadOnly}
        />
      );
    }

    const displayAssets = [...assets, ...newUploadingAssets];
    if (displayAssets.length === 0) {
      return null;
    }

    const asset = displayAssets[0];
    return (
      <div className="w-full">
        <div className="flex w-full items-center gap-1">
          <div className="text-[13px] text-[#1D1C1DB3]">{asset.name}</div>
          <svg
            data-1qi="true"
            aria-hidden="true"
            viewBox="0 0 20 20"
            className="h-4 w-4"
          >
            <path
              fill="currentColor"
              fillRule="evenodd"
              d="M13.75 7.25h-7.5a.75.75 0 0 0-.576 1.23l3.75 4.5a.75.75 0 0 0 1.152 0l3.75-4.5a.75.75 0 0 0-.576-1.23Z"
              clipRule="evenodd"
            ></path>
          </svg>
        </div>
        <AssetUploadContainer
          asset={asset}
          className="mt-1 w-full rounded-[4px]"
          isReadOnly={isReadOnly}
          onEditClick={onEditClick}
          onDeleteClick={onDeleteClick}
        />
      </div>
    );
  };

  return (
    <>
      {draggedOver ? (
        <motion.div
          key="dropzone"
          className="mb-3.5 w-full"
          initial={{ height: 338, opacity: 0 }}
          animate={{
            height: 338,
            opacity: 100,
          }}
        >
          <FileDropzone
            onDrop={async (files) => {
              setDraggedOver(false);
              if (uppy) {
                await handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID,
                  },
                  getValidFiles,
                });
              }
            }}
            acceptedFileTypes={{
              "image/jpg": [".jpg", ".jpeg"],
              "image/png": [".png"],
              "image/gif": [".gif"],
              "image/webp": [".webp"],
              "video/mp4": [".mp4"],
              "video/quicktime": [".mov"],
            }}
          />
        </motion.div>
      ) : (
        <motion.div
          key="assets"
          initial={{ opacity: 0 }}
          animate={{
            opacity: 100,
          }}
          className={classNames({
            hidden: assets.length === 0 && uploadingAssets?.length === 0,
          })}
        >
          <div className="-ml-12 sm:-ml-0">{renderContent()}</div>
        </motion.div>
      )}
      {editImageModalProperties && (
        <EditImageModal
          title={editImageModalProperties.title}
          asset={editImageModalProperties.asset}
          maxSizeMB={editImageModalProperties.maxSizeMB}
          channel={editImageModalProperties.channel}
          isOpen={editImageModalProperties.isOpen}
          onClose={() => setEditImageModalProperties(null)}
          onUpload={async (file) => {
            if (uppy && editImageModalProperties.asset) {
              uppy.addFile({
                name: file.name,
                type: file.type,
                data: file,
                meta: {
                  contentID,
                  updateAssetID: editImageModalProperties.asset.id,
                },
              });
            }
          }}
        />
      )}
    </>
  );
};

export default SlackAssetPreview;
