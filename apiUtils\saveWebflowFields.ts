import assert from "assert";
import { db } from "~/clients";
import Webflow from "~/clients/Webflow";

const saveWebflowFields = async (postID: string) => {
  const post = await db.post.findFirstOrThrow({
    where: {
      id: postID,
    },
    include: {
      webflowIntegration: true,
      webflowContent: {
        include: {
          fields: true,
        },
      },
    },
  });

  const { webflowContent, webflowIntegration } = post;

  assert(webflowContent, "Webflow content not found");
  assert(webflowIntegration, "Webflow integration not found");

  const webflow = new Webflow(webflowIntegration.accessToken);

  const collectionResponse = await webflow.getCollectionDetails(
    webflowIntegration.collectionID
  );

  if (collectionResponse.errors) {
    throw new Error(collectionResponse.errors.join(","));
  }

  const currentFields = webflowContent.fields;

  // We don't want to save the blog content and title fields
  const fields = collectionResponse.data.fields.filter(
    (field) =>
      ![
        webflowIntegration.blogContentFieldID,
        webflowIntegration.blogTitleFieldID,
      ].includes(field.id)
  );

  const missingFields = fields.filter((field) => {
    return !currentFields.some(
      (currentField) => currentField.fieldID === field.id
    );
  });

  const getFieldPosition = (fieldID: string) => {
    const index = fields.findIndex((field) => field.id === fieldID);

    const field = fields[index];
    if (field.slug === "slug") {
      return 0;
    }

    // Add 1 to the index to account for the slug field
    return index > 0 ? index + 1 : 1;
  };

  if (missingFields.length > 0) {
    await db.webflowField.createMany({
      data: missingFields.map((field) => {
        return {
          fieldID: field.id,
          displayName: field.displayName,
          helpText: field.helpText,
          position: getFieldPosition(field.id),
          isRequired: field.isRequired,
          isEditable: field.isEditable,
          type: field.type,
          slug: field.slug,
          contentID: webflowContent.id,
          validations: field.validations || undefined,
        };
      }),
    });
  }

  const deletedFields = currentFields.filter((field) => {
    return !fields.some((currentField) => currentField.id === field.fieldID);
  });

  await db.webflowField.deleteMany({
    where: {
      id: {
        in: deletedFields.map((field) => field.id),
      },
    },
  });

  const existingFields = fields.filter((field) => {
    return currentFields.some(
      (currentField) => currentField.fieldID === field.id
    );
  });

  await db.$transaction(async (tx) => {
    for await (const field of existingFields) {
      const existingField = currentFields.find(
        (currentField) => currentField.fieldID === field.id
      );

      assert(existingField, "Existing field not found");

      const isDifferentType = field.type !== existingField!.type;
      let isInvalidValue = false;
      if (
        field.type === "Option" ||
        field.type === "Reference" ||
        field.type === "MultiReference"
      ) {
        const options = field.validations?.options || [];
        if (existingField.value) {
          isInvalidValue = !options
            .map((option) => option.id)
            .includes(existingField.value);
        }
      }

      await tx.webflowField.update({
        where: {
          id: existingField!.id,
        },
        data: {
          displayName: field.displayName,
          helpText: field.helpText,
          position: getFieldPosition(field.id),
          isRequired: field.isRequired,
          isEditable: field.isEditable,
          type: field.type,
          slug: field.slug,
          validations: field.validations || undefined,
          value: isDifferentType || isInvalidValue ? null : existingField.value,
        },
      });
    }
  });

  const newFields = await db.webflowField.findMany({
    where: {
      contentID: webflowContent.id,
    },
  });

  return newFields;
};

export default saveWebflowFields;
