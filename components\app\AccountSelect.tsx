import { Fragment } from "react";

import { Combobox, Transition } from "@headlessui/react";
import classNames from "classnames";

type Option = {
  value: string;
  label: string;
};

type Props = {
  value: string;
  options: Option[];
  onChange: (option: Option) => void;
};

const AccountSelect = ({ value, options, onChange }: Props) => {
  const optionValue = options.find((option) => option.value === value);

  return (
    <Combobox
      value={optionValue}
      onChange={(selection: Option) => {
        onChange(selection);
      }}
    >
      {({ open }: { open: boolean }) => (
        <div className="relative min-w-fit text-sm text-black">
          <Combobox.Button>{optionValue?.label}</Combobox.Button>
          <Transition
            as={Fragment}
            enterFrom="opacity-0"
            enterTo="opacity-100"
            enter="transition ease-in duration-75"
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Combobox.Options className="bg-surface absolute z-10 mt-2 w-fit translate-y-1 rounded-lg py-1 shadow-xl focus:outline-hidden">
              <div className="max-h-60 overflow-auto">
                {options.map((option) => (
                  <Combobox.Option
                    key={option.value}
                    value={option}
                    disabled={value?.length === 1 && option.value === value[0]}
                    className="px-2"
                  >
                    {({
                      selected,
                      active,
                      disabled,
                    }: {
                      selected: boolean;
                      active: boolean;
                      disabled: boolean;
                    }) => (
                      <div
                        className={classNames(
                          "relative flex cursor-pointer items-center gap-2 rounded-md px-3 py-2 pr-10 pl-4 select-none",
                          {
                            "text-secondary": selected,
                          },
                          {
                            "bg-slate": active,
                          },
                          {
                            "cursor-not-allowed": disabled,
                          }
                        )}
                      >
                        <div className="flex flex-col whitespace-nowrap">
                          <span className="font-eyebrow">{option.label}</span>
                        </div>
                      </div>
                    )}
                  </Combobox.Option>
                ))}
              </div>
            </Combobox.Options>
          </Transition>
        </div>
      )}
    </Combobox>
  );
};

export default AccountSelect;
