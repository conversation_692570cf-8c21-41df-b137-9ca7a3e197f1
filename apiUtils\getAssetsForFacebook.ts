import { Asset } from "@prisma/client";
import sortBy from "lodash/sortBy";
import { AssetType } from "~/types/Asset";
import { removeEmptyElems } from "~/utils";
import enhanceAssetType from "./assets/enhanceAssetType";

const getAssetsForFacebook = async (post: {
  id: string;
  workspaceID: string;
  facebookContent: {
    id: string;
    coverPhoto: Asset | null;
    assets: {
      id: string;
      asset: Asset;
      position: number;
    }[];
  } | null;
}): Promise<{
  assets: (AssetType & {
    position: number;
  })[];
  coverPhoto: AssetType | null;
}> => {
  const { facebookContent } = post;
  if (!facebookContent) {
    return { assets: [], coverPhoto: null };
  }

  return {
    assets: removeEmptyElems(
      sortBy(facebookContent.assets, "position").map((asset) => ({
        ...enhanceAssetType(asset.asset),
        id: asset.id,
        position: asset.position,
      }))
    ),
    coverPhoto: facebookContent.coverPhoto
      ? enhanceAssetType(facebookContent.coverPhoto)
      : null,
  };
};

export default getAssetsForFacebook;
