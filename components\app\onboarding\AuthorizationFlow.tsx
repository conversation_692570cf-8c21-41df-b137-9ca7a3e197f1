import {
  EllipsisHorizontalIcon,
  EnvelopeIcon,
  KeyIcon,
  LockClosedIcon,
} from "@heroicons/react/24/outline";
import { EmailOtpType } from "@supabase/supabase-js";
import assert from "assert";
import classNames from "classnames";
import Link from "next/link";
import { useRouter } from "next/router";
import pluralize from "pluralize";
import posthog from "posthog-js";
import { useCallback, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { toast } from "sonner";
import { Alert } from "~/components";
import { Error } from "~/components/form";
import Avatar from "~/components/ui/Avatar";
import Button from "~/components/ui/Button";
import DropdownMenu from "~/components/ui/DropdownMenu";
import Popover from "~/components/ui/Popover";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { useUser } from "~/providers/UserProvider";
import { getFullName } from "~/utils";
import * as OnboardingCard from "./OnboardingCard";

type FormValues = {
  email: string;
};

type OtpFormValues = {
  token: string;
};

type Props = {
  title: string;
  invite?: {
    email: string;
    workspace: {
      name: string;
    };
    invitedBy: {
      firstName: string | null;
      lastName: string | null;
    };
  } | null;
  referrer?: {
    firstName: string | null;
    lastName: string | null;
  } | null;
  otpType: EmailOtpType;
  otherFlow: "login" | "sign_up";
  showEmailPasswordButton?: boolean;
};

const LastUsedPopover = ({ children }: { children: React.ReactNode }) => {
  return (
    <Popover.Root open={true} defaultOpen={true}>
      <Popover.Anchor>{children}</Popover.Anchor>
      <Popover.Content
        side="left"
        align="center"
        sideOffset={5}
        className="bg-medium-dark-gray/85 font-body-sm z-50 hidden items-center gap-1 rounded-[5px] border-0 px-2 py-1.5 text-white md:flex"
      >
        <KeyIcon className="h-3.5 w-3.5" />
        <span>Last used</span>
        <Popover.Arrow
          className="fill-medium-dark-gray/85"
          width={10}
          height={5}
        />
      </Popover.Content>
    </Popover.Root>
  );
};

const GoogleButton = ({
  onClick,
  isLastUsed,
}: {
  onClick: () => void;
  isLastUsed: boolean;
}) => (
  <button className="w-full focus:outline-hidden" onClick={onClick}>
    <div
      className={classNames(
        "group hover:border-basic group-focus:border-basic mx-auto flex w-full items-center justify-center rounded-[10px] border px-6 py-5 transition-colors",
        {
          "md:border-basic": isLastUsed,
          "border-light-gray": !isLastUsed,
        }
      )}
    >
      <div className="flex items-center gap-2.5">
        <img src="/integrations/google.svg" className="h-6 w-6" />
        <div className="group-hover:text-basic font-body font-medium transition-colors">
          Continue with Google
        </div>
      </div>
    </div>
  </button>
);

const AuthorizationFlow = ({
  title,
  invite,
  referrer,
  otpType,
  otherFlow,
  showEmailPasswordButton = true,
}: Props) => {
  const router = useRouter();

  const [authLink, setAuthLink] = useState<string | null>(null);
  const [direction, setDirection] = useState<-1 | 1>(1);

  useEffect(() => {
    const getAuthLinkFromURL = async () => {
      const { link } = router.query;

      if (link && typeof link === "string" && link.startsWith("http")) {
        setAuthLink(link);
      }
    };

    getAuthLinkFromURL();
  }, [router.query]);

  const [emailSentTo, setEmailSentTo] = useState<string | null>(null);
  const [verifyingOTP, setVerifyingOTP] = useState<boolean>(false);
  const loginError = router.query.error;

  const {
    user,
    lastLoginMethod,
    removeLastLoginMethod,
    signIn,
    signInWithGoogle,
    verifyEmailOTP,
  } = useUser();

  const { control, handleSubmit } = useForm<FormValues>({
    defaultValues: {
      email: invite?.email || "",
    },
  });
  const {
    control: otpControl,
    handleSubmit: handleOtpSubmit,
    setValue: setOtpValue,
  } = useForm<OtpFormValues>();

  useEffect(() => {
    if (user) {
      router.push("/loading-user");
    }
  }, [user]);

  const sendLoginLink = async ({ email }: FormValues) => {
    const error = await signIn(email);

    if (error) {
      toast.error("Error Sending Email", {
        description: error?.message,
      });
    } else {
      if (otpType === "magiclink") {
        posthog.capture(POSTHOG_EVENTS.account.requestedLoginLink, {
          email,
          otpType,
        });
      } else if (otpType === "signup") {
        posthog.capture(POSTHOG_EVENTS.account.signedUp, {
          email,
          otpType,
        });
      }
      setDirection(1);
      setEmailSentTo(email);
    }
  };

  const verifyOTP = async ({ token }: OtpFormValues) => {
    setVerifyingOTP(true);

    assert(emailSentTo);

    const error = await verifyEmailOTP(emailSentTo, token, otpType);

    if (error) {
      const otherOtpType = otpType === "magiclink" ? "signup" : "magiclink";
      const newError = await verifyEmailOTP(emailSentTo, token, otherOtpType);

      if (newError) {
        toast.error("Invalid Code", {
          description: error?.message,
        });
      }
      setVerifyingOTP(false);
    }
  };

  const onSubmitForm = useCallback(
    (formValues: FormValues) => {
      const emailToUse =
        formValues.email ||
        (isLastMethodEmailOrPassword
          ? (lastLoginMethod?.userInfo?.email ?? "")
          : "");

      sendLoginLink({ email: emailToUse });
    },
    [lastLoginMethod]
  );

  const isLastMethodGoogle = lastLoginMethod?.method === "google";

  const isLastMethodEmailOrPassword =
    lastLoginMethod?.method === "email" ||
    lastLoginMethod?.method === "password";

  const getInitials = (firstName?: string, lastName?: string): string => {
    if (!firstName || !lastName) return "";
    return `${firstName[0]}${lastName[0]}`.toUpperCase();
  };

  return (
    <>
      <div className="bg-surface relative flex min-h-full w-screen flex-col justify-center">
        <div className="absolute top-4 flex w-full items-center justify-between px-5 py-1">
          <Link href="https://www.meetassembly.com">
            <img
              src="/logos/assembly_logo_text.svg"
              className="h-auto w-[75px] md:w-[100px]"
            />
          </Link>
          <div className="flex items-center gap-4">
            <span className="font-body text-medium-dark-gray hidden md:block">
              {otherFlow === "login"
                ? "Already have an account?"
                : "Don't have an account?"}
            </span>
            <Button
              onClick={() => {
                router.push(otherFlow === "login" ? "/login" : "/sign-up");
              }}
            >
              {otherFlow === "login" ? "Login" : "Sign Up"}
            </Button>
          </div>
        </div>
        {loginError && (
          <div className="mx-auto mb-4 max-w-md">
            <Alert
              title="Error Logging In"
              intent="danger"
              message={`${loginError}. Try logging in again.`}
            />
          </div>
        )}
        <OnboardingCard.Container>
          {emailSentTo == null && authLink == null ? (
            <OnboardingCard.Content
              title={title}
              showTermsAndConditions={true}
              direction={direction}
            >
              {invite != null && (
                <div className="font-body-sm bg-lightest-gray-30 w-full space-y-0.5 rounded-md p-3 text-black">
                  <div className="text-center font-medium">
                    {invite.invitedBy.firstName} invited you to the workspace{" "}
                    {invite.workspace.name}
                  </div>
                  <div className="text-medium-dark-gray text-center">
                    To accept the invitation, please continue as{" "}
                    <span className="text-black">{invite.email}</span>
                  </div>
                </div>
              )}
              {referrer != null && (
                <div className="font-body bg-lightest-gray-30 w-full space-y-0.5 rounded-md px-6 py-6 text-black">
                  <div className="text-center font-medium">
                    {getFullName(referrer)} has referred you to join Assembly
                  </div>
                  <div className="text-medium-dark-gray text-center">
                    We'll let them know once you've finished signing up.
                  </div>
                </div>
              )}
              <div>
                {isLastMethodGoogle && otpType === "magiclink" ? (
                  <LastUsedPopover>
                    <GoogleButton
                      onClick={signInWithGoogle}
                      isLastUsed={true}
                    />
                  </LastUsedPopover>
                ) : (
                  <GoogleButton onClick={signInWithGoogle} isLastUsed={false} />
                )}

                <div className="relative my-5">
                  <div
                    className="absolute inset-0 flex items-center"
                    aria-hidden="true"
                  >
                    <div className="border-lightest-gray w-full border-t" />
                  </div>
                  <div className="relative flex justify-center">
                    <span className="font-eyebrow text-medium-gray bg-white px-2">
                      OR
                    </span>
                  </div>
                </div>

                <div className="relative mt-6 w-full">
                  <form
                    className="relative mt-6 w-full"
                    onSubmit={handleSubmit(onSubmitForm)}
                  >
                    {isLastMethodEmailOrPassword && otpType === "magiclink" ? (
                      <LastUsedPopover>
                        <div className="mb-[31px] flex w-full items-center justify-between rounded-md">
                          <div className="flex gap-[18px]">
                            <div className="min-h-12 min-w-12 rounded-full">
                              <Avatar.Root className="h-12 w-12">
                                <Avatar.Fallback className="bg-secondary font-sans text-xl leading-none font-medium text-white">
                                  {getInitials(
                                    lastLoginMethod.userInfo?.firstName,
                                    lastLoginMethod.userInfo?.lastName
                                  )}
                                </Avatar.Fallback>
                              </Avatar.Root>
                            </div>
                            <div className="flex flex-col justify-center">
                              <span className="font-medium">
                                {lastLoginMethod.userInfo?.email}
                              </span>
                              <span className="text-medium-dark-gray">
                                {pluralize(
                                  "workspace",
                                  lastLoginMethod.userInfo?.workspaceCount ?? 0,
                                  true
                                )}
                              </span>
                            </div>
                          </div>
                          <DropdownMenu.Root>
                            <DropdownMenu.Trigger className="hover:bg-slate cursor-pointer rounded-full bg-inherit p-0.5 text-black transition-all">
                              <EllipsisHorizontalIcon className="h-6 w-6" />
                            </DropdownMenu.Trigger>
                            <DropdownMenu.Content position="bottom-right">
                              <DropdownMenu.Group>
                                <DropdownMenu.Item
                                  icon="BackspaceIcon"
                                  intent="danger"
                                  onClick={removeLastLoginMethod}
                                >
                                  Remove Account
                                </DropdownMenu.Item>
                              </DropdownMenu.Group>
                            </DropdownMenu.Content>
                          </DropdownMenu.Root>
                        </div>
                      </LastUsedPopover>
                    ) : (
                      <Controller
                        control={control}
                        name="email"
                        rules={{
                          required: {
                            value: true,
                            message: "Email must be filled out",
                          },
                          pattern: {
                            value:
                              /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                            message: "Must be a valid email",
                          },
                        }}
                        render={({
                          field: { onChange, onBlur, value },
                          fieldState: { error },
                        }) => {
                          return (
                            <div>
                              <div className="group text-medium-gray focus:text-basic relative mb-4 rounded-[10px] shadow-xs">
                                <input
                                  type="email"
                                  name="email"
                                  value={value}
                                  onChange={onChange}
                                  className="peer ring-light-gray focus:ring-basic hover:ring-basic placeholder:text-medium-gray block w-full rounded-[10px] border-0 py-5 pl-16 text-black ring-1 outline-0 transition-all ring-inset"
                                  placeholder="Enter your Email Address"
                                />
                                <div className="text-medium-dark-gray peer-focus:text-basic pointer-events-none absolute inset-y-0 left-6 flex items-center transition-colors">
                                  <EnvelopeIcon
                                    className="h-6 w-6"
                                    aria-hidden="true"
                                  />
                                </div>
                              </div>
                              <Error error={error} />
                            </div>
                          );
                        }}
                      />
                    )}
                    <Button type="submit" size="lg" className="w-full">
                      Continue With Email
                    </Button>
                  </form>
                  {otpType === "signup" && (
                    <div className="flex w-full items-center justify-center pt-4">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push("/login")}
                      >
                        Already have an account? Click here.
                      </Button>
                    </div>
                  )}
                  <div
                    className={classNames("flex justify-center pt-4", {
                      "opacity-0": !showEmailPasswordButton,
                    })}
                  >
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      disabled={!showEmailPasswordButton}
                      onClick={() => router.push("/login-email")}
                    >
                      Continue with Email / Password
                    </Button>
                  </div>
                </div>
              </div>
            </OnboardingCard.Content>
          ) : (
            <OnboardingCard.Content
              title={
                authLink == null
                  ? "Check your email"
                  : "Click Continue to Login"
              }
              description={
                authLink == null ? (
                  <div className="font-body text-medium-dark-gray text-center">
                    We've sent a temporary login code to
                    <div className="text-secondary">{emailSentTo}</div>
                  </div>
                ) : (
                  "Thanks for verifying your email. Click the “Continue” button below to sign in."
                )
              }
              direction={direction}
              showTermsAndConditions={true}
            >
              <form className="space-y-4" onSubmit={handleOtpSubmit(verifyOTP)}>
                {authLink == null && (
                  <Controller
                    key="token"
                    control={otpControl}
                    name="token"
                    rules={{
                      required: {
                        value: true,
                        message: "Code must be filled out",
                      },
                      pattern: {
                        value: /\d{6}$/,
                        message: "Code must be 6 digits",
                      },
                    }}
                    render={({
                      field: { onChange, value },
                      fieldState: { error },
                    }) => {
                      return (
                        <div>
                          <div className="group text-medium-gray focus:text-basic relative rounded-[10px] shadow-xs">
                            <input
                              type="number"
                              value={value}
                              onChange={onChange}
                              onPaste={(e) => {
                                const pastedValue =
                                  e.clipboardData.getData("text");
                                setTimeout(() => {
                                  if (pastedValue?.length === 6) {
                                    handleOtpSubmit(verifyOTP)();
                                  }
                                }, 100);
                              }}
                              className="peer ring-light-gray focus:ring-basic hover:ring-basic placeholder:text-medium-gray block w-full rounded-[10px] border-0 py-5 pl-16 text-black ring-1 outline-0 transition-all ring-inset"
                              placeholder="Enter the Login Code"
                            />
                            <div className="text-medium-dark-gray peer-focus:text-basic pointer-events-none absolute inset-y-0 left-6 flex items-center transition-colors">
                              <LockClosedIcon
                                className="h-6 w-6"
                                aria-hidden="true"
                              />
                            </div>
                          </div>
                          <Error error={error} />
                        </div>
                      );
                    }}
                  />
                )}
                {authLink == null ? (
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full"
                    isLoading={verifyingOTP}
                  >
                    Continue
                  </Button>
                ) : (
                  <Button
                    type="button"
                    size="lg"
                    className="w-full"
                    onClick={() => {
                      window.location.href = authLink;
                    }}
                  >
                    Continue
                  </Button>
                )}
                <div className="flex w-full items-center justify-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setDirection(-1);
                      setEmailSentTo(null);
                    }}
                  >
                    Back to Login
                  </Button>
                </div>
              </form>
            </OnboardingCard.Content>
          )}
        </OnboardingCard.Container>
      </div>
    </>
  );
};

export default AuthorizationFlow;
