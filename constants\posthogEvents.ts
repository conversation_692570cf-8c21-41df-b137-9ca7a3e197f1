const POSTHOG_EVENTS = {
  account: {
    signedUp: "Account Signed Up" as const,
    requestedLoginLink: "Account Requested Login Link" as const,
    loggedIn: "Account Logged In" as const,
    created: "Account Created" as const,
  },
  draft: {
    startedGenerating: "Draft Started Generating" as const,
    completedGenerating: "Draft Completed Generating" as const,
    convertedToPost: "Draft Converted to Post" as const,
  },
  post: {
    created: "Post Created" as const,
    deleted: "Post Deleted" as const,
    scheduled: "Post Scheduled" as const,
    bulkDuplicated: "Post Bulk Duplicated" as const,
    updatedPrimaryChannel: "Post Updated Main Channel" as const,
    contentSynced: "Post Content Synced" as const,
    published: "Post Published" as const,
    errored: "Post Errored" as const,
  },
  dub: {
    created: "Short Link Created" as const,
    removed: "Short Link Removed" as const,
  },
  engagement: {
    published: "Engagement Published" as const,
  },
  timeSuggestion: {
    picked: "Time Suggestion Picked" as const,
  },
  idea: {
    created: "Idea Created" as const,
    deleted: "Idea Deleted" as const,
  },
  campaign: {
    created: "Campaign Created" as const,
    deleted: "Campaign Deleted" as const,
  },
  approval: {
    requested: "Approval Requested" as const,
    approved: "Approval Approved" as const,
  },
  comment: {
    created: "Comment Created" as const,
  },
  socialAccount: {
    connected: "Social Account Connected" as const,
    disconnected: "Social Account Disconnected" as const,
  },
  trialBanner: {
    navigated: "Trial Banner Navigated to Pricing" as const,
  },
  invite: {
    accepted: "Invite Accepted" as const,
  },
  onboarding: {
    allTasksCompleted: "All Onboarding Tasks Completed" as const,
    taskCompleted: "Onboarding Task Completed" as const,
    taskExpired: "Onboarding Task Expired" as const,
    taskUnmarked: "Onboarding Task Marked Active" as const,
    allTasksExpired: "All Onboarding Tasks Expired" as const,
    taskMarkedComplete: "Onboarding Task Marked Complete" as const,
  },
  referral: {
    copied: "Referral Link Copied" as const,
    signedUp: "Referral Signed Up" as const,
    expired: "Referral Expired" as const,
    subscribed: "Referral Subscribed" as const,
    paidOut: "Referral Paid Out" as const,
  },
  upgradePlanModal: {
    viewed: "Upgrade Plan Modal Viewed" as const,
    addedSocialProfile: "Added Social Profile" as const,
    navigated: "Upgrade Plan Modal Navigated to Pricing" as const,
  },
  trialExpiredModal: {
    viewed: "Trial Expired Modal Viewed" as const,
    loggedOut: "Trial Expired Modal Logged Out" as const,
    navigated: "Trial Expired Modal Navigated to Pricing" as const,
  },
  twitterStats: {
    refreshModalViewed: "Twitter Stats Refresh Modal Viewed" as const,
    refreshed: "Twitter Stats Refreshed" as const,
    queryLimitReached: "Twitter Stats Query Limit Reached" as const,
  },
  paymentMethodPastDueModal: {
    viewed: "Payment Method Past Due Modal Viewed" as const,
    navigated: "Payment Method Past Due Modal Navigated to Stripe" as const,
  },
  subscriptionCancelledModal: {
    viewed: "Subscription Cancelled Modal Viewed" as const,
    navigated: "Subscription Cancelled Modal Navigated to Pricing" as const,
  },
  editImageModal: {
    viewed: "Edit Image Modal Viewed" as const,
  },
  image: {
    compressed: "Image Compressed" as const,
  },
  workspace: {
    created: "Workspace Created" as const,
  },
  subscription: {
    created: "Subscription Created" as const,
    cancelled: "Subscription Cancelled" as const,
  },
  trial: {
    started: "Trial Started" as const,
  },
  video: {
    compressionStarted: "Video Compression Started" as const,
    compressed: "Video Compressed" as const,
  },
  search: {
    opened: "Search Opened" as const,
    navigated: "Search Navigated to Page" as const,
  },
};

export default POSTHOG_EVENTS;
