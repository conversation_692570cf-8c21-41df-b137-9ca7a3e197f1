import { StatusCodes } from "http-status-codes";
import type { NextApiRequest, NextApiResponse } from "next";
import getUserWorkspaceWithSocialProfileUsage from "server/routers/utils/getUserWorkspaceWithSocialProfileUsage";
import createSupabaseServerClient from "~/clients/createSupabaseServerClient";
import { getCookie } from "~/utils/cookies";

const handleError = (
  res: NextApiResponse,
  message: string,
  referrer: string
) => {
  const params = new URLSearchParams({
    error_message: message,
    channel: "Webflow",
  });

  return res.redirect(StatusCodes.MOVED_TEMPORARILY, `${referrer}?${params}`);
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res
      .status(StatusCodes.METHOD_NOT_ALLOWED)
      .json({ message: "Method not allowed" });
  }

  const { code, state } = req.query;
  const webflowIntegrationCookie = getCookie(
    "WEBFLOW_INTEGRATION",
    req.headers.cookie
  );

  if (!webflowIntegrationCookie) {
    return handleError(
      res,
      "Session expired, please try again",
      "/settings/profiles"
    );
  }

  const { referrer } = webflowIntegrationCookie;

  if (!code) {
    return handleError(res, "No authorization code provided", referrer);
  } else if (!state || state !== webflowIntegrationCookie?.state) {
    return handleError(res, "Session expired, please try again", referrer);
  }

  const supabase = await createSupabaseServerClient(req.headers.cookie);

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const workspace = await getUserWorkspaceWithSocialProfileUsage({
    user,
    currentWorkspaceID: getCookie("CURRENT_WORKSPACE_ID", req.headers.cookie),
  });

  if (!user || !workspace) {
    return handleError(res, "User not logged in", referrer);
  }

  try {
    const params = new URLSearchParams({
      code: code as string,
    });

    // Instead of saving the integration here, redirect back to the frontend with the code
    // The frontend will then use the code to fetch site options and let user select

    if (referrer.startsWith("/settings/webflow")) {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/webflow?${params}`
      );
    } else {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `${referrer}/choose?${params}`
      );
    }
  } catch (error) {
    console.error("Webflow auth error:", error);
    return handleError(res, "Error connecting Webflow account", referrer);
  }
}
