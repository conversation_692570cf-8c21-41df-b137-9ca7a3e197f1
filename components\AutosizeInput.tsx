import { RefCallback, useEffect, useRef, useState } from "react";

import classNames from "classnames";

export type Size = "sm" | "md" | "lg" | "xl";

type Props = {
  inputRef?: RefCallback<HTMLInputElement>;
  name?: string;
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  autoFocus?: boolean;
  isReadOnly?: boolean;
  onEnterPress?: () => void;
  size?: Size;
  weight?: "regular" | "medium";
  showBorder?: boolean;
};

const AutosizeInput = ({
  inputRef,
  name,
  value,
  onChange,
  onBlur,
  placeholder,
  autoFocus,
  isReadOnly,
  onEnterPress,
  size = "sm",
  weight = "regular",
  showBorder = true,
}: Props) => {
  const span = useRef<HTMLSpanElement>(null);
  const [inputWidth, setInputWidth] = useState<number>(0);

  const extraWidth =
    size === "sm"
      ? 20
      : size === "md"
        ? 30
        : size === "lg"
          ? 30
          : size === "xl"
            ? 40
            : 4;

  // Update width when value or placeholder changes
  useEffect(() => {
    if (!span.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      const width = entries[0].contentRect.width;
      setInputWidth(width + extraWidth);
    });

    resizeObserver.observe(span.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [value, placeholder, extraWidth]);

  return (
    <div
      className={classNames({
        "font-body-sm": size === "sm",
        "font-body": size === "md",
        h4: size === "lg",
        h2: size === "xl",
      })}
    >
      <span
        className="w-content absolute -z-10 whitespace-pre opacity-0"
        ref={span}
      >
        {value || placeholder}
      </span>
      <input
        ref={inputRef}
        name={name}
        value={value}
        type="text"
        disabled={isReadOnly}
        placeholder={placeholder}
        autoFocus={autoFocus}
        onKeyUp={(event) => {
          if (event.key === "Enter") {
            if (onEnterPress) {
              onEnterPress();
              event.currentTarget.blur();
            } else if (onBlur) {
              onBlur();
              event.currentTarget.blur();
            }
          }
        }}
        className={classNames(
          "placeholder:text-medium-gray max-w-2xl rounded-md border border-transparent px-1 py-0.5 text-black outline-hidden transition-colors duration-200 disabled:bg-white",
          {
            "placeholder:font-body-sm": size === "sm",
            "font-body placeholder:font-body": size === "md",
            "-translate-x-1": size === "sm" || size === "md",
            "-translate-x-1.5": size === "xl",
            "hover:border-slate focus:border-light-gray":
              !isReadOnly && showBorder,
            "focus:none outline-hidden": isReadOnly,
            "font-medium": weight === "medium",
          }
        )}
        style={{ width: inputWidth || 400 }}
        onChange={(event) => {
          onChange(event.target.value);
        }}
        onBlur={onBlur}
      />
    </div>
  );
};

export default AutosizeInput;
