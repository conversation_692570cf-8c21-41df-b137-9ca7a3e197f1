import { CheckIcon } from "@heroicons/react/24/solid";
import classNames from "classnames";
import { motion } from "framer-motion";
import { ReactElement } from "react";

type Props = {
  id?: string;
  value: boolean;
  onChange: (value: boolean) => void;
  intent?: "basic" | "none";
  label?: string | ReactElement;
  disabled?: boolean;
};

const Checkbox = ({
  id,
  value,
  onChange,
  intent = "basic",
  label,
  disabled,
}: Props) => {
  return (
    <button
      id={id}
      type="button"
      disabled={disabled}
      className={classNames("group flex items-center gap-1.5", {
        "cursor-not-allowed": disabled,
      })}
      onClick={() => {
        onChange(!value);
      }}
    >
      <motion.div
        whileHover={{ scale: disabled ? 1 : 1.1 }}
        whileTap={{ scale: disabled ? 1 : 0.9 }}
        className={classNames("border p-0.5 transition-colors", {
          "rounded-md": intent === "none",
          "opacity-40": disabled,
          "border-medium-gray bg-white": value === false && intent === "none",
          "border-light-gray bg-light-gray":
            value === true && intent === "none",
          "rounded-xs": intent === "basic",
          "border-medium-gray bg-transparent":
            value === false && intent === "basic",
          "group-hover:border-secondary":
            value === false && intent === "basic" && !disabled,
          "border-secondary bg-secondary": value === true && intent === "basic",
        })}
      >
        {intent === "none" ? (
          <CheckIcon className="h-4 w-4 text-white" />
        ) : (
          <div className="h-2 w-2" />
        )}
      </motion.div>
      {!!label && <div className="font-body-sm">{label}</div>}
    </button>
  );
};

export default Checkbox;
