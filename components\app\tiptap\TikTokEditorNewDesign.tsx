import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Document from "@tiptap/extension-document";
import History from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { ClipboardEvent, useEffect, useRef, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import { usePostIntegrations, useThreadComments } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { stripLiveblocksMarks } from "~/utils";
import ThreadCommentsPopover from "../posts/ThreadCommentsPopover";
import {
  AutoHashtag,
  AutoMention,
  CharacterCount,
  CustomCommentThreadMark,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "./extensions";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  title?: string;
  postID?: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  roomID: string;
};

const createBaseExtensions = (
  postID: string | undefined,
  placeholder: string
) => [
  Document,
  Paragraph,
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  Text,
  History,
  Emoji,
  Link.configure({
    isLinkShorteningEnabled: !!postID ? true : false,
    postID,
  }),
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  AutoHashtag.configure({
    urlFn: (hashtag) => `https://www.tiktok.com/tag/${hashtag}`,
  }),
  AutoMention.configure({
    baseUrl: "https://tiktok.com/@",
    regex: /(?<=^|\s)(@[A-Za-z0-9_\.]{1,24})/g,
  }),
  CharacterCount.configure({
    limit: 2200,
  }),
  PasteHandler,
];

type TikTokEditorFallbackProps = {
  content: JSONContent | null;
  postID?: string;
  placeholder: string;
};

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const TikTokEditorFallback = ({
  content,
  postID,
  placeholder,
}: TikTokEditorFallbackProps) => {
  const cleanContent = stripLiveblocksMarks(content);

  const editor = useEditor({
    extensions: createBaseExtensions(postID, placeholder),
    immediatelyRender: false,
    content: cleanContent,
    editable: false,
  });

  if (!editor) return null;

  return <EditorContent editor={editor} />;
};

const TikTokEditor = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  roomID,
}: BaseProps) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );
  const { setEditorForRoom } = useThreadComments();
  const { currentChannel } = usePostIntegrations();

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(postID, placeholder),
    ],
    onUpdate: ({ editor }) => {
      // TipTap doubles newlines for paragraph breaks (e.g., pressing Enter once creates two \n).
      // We halve the newlines to match LinkedIn's expected format while preserving paragraph structure.
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      onUpdateContent(textContent);

      const json = editor.getJSON();
      setEditorState(json);
    },
    immediatelyRender: false,
    editable: !isReadOnly,
  });

  const isEditorReady = useIsEditorReady();

  useEffect(() => {
    if (editor && currentChannel === "TikTok") {
      setEditorForRoom(editor, roomID);
    }

    return () => {
      setEditorForRoom(null, roomID);
    };
  }, [editor, currentChannel, setEditorForRoom, roomID]);

  if (!editor) return null;

  return (
    <div
      className="py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor} shouldHideWithLink={true}>
        <ToolbarActions
          editor={editor}
          features={{
            unicodeFormatting: true,
          }}
        />
      </CustomFloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}

      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["TikTok"].label,
            }}
          />
        </>
      ) : (
        <TikTokEditorFallback
          content={initialValue}
          placeholder={placeholder}
        />
      )}
    </div>
  );
};

const TikTokEditorWrapperNewDesign = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  roomID,
}: BaseProps) => {
  const { currentChannel } = usePostIntegrations();
  const threadCommentsContainerRef = useRef<HTMLDivElement>(null);

  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense
        fallback={
          <div className="py-2">
            <TikTokEditorFallback
              content={initialValue}
              placeholder={placeholder}
            />
          </div>
        }
      >
        <div className="relative">
          <TikTokEditor
            title={title}
            postID={postID}
            initialValue={initialValue}
            placeholder={placeholder}
            onSave={onSave}
            onUpdateContent={onUpdateContent}
            onImagePaste={onImagePaste}
            isReadOnly={isReadOnly}
            roomID={roomID}
          />
          <div ref={threadCommentsContainerRef} />
        </div>
      </ClientSideSuspense>
      {currentChannel === "TikTok" && (
        <ThreadCommentsPopover
          containerRef={threadCommentsContainerRef}
          key={roomID}
          roomID={roomID}
        />
      )}
    </RoomProvider>
  );
};

export default TikTokEditorWrapperNewDesign;
