import ContentLoader from "react-content-loader";
import colors from "~/styles/colors";

const CampaignPageLoader = () => {
  return (
    <div className="flex gap-10">
      <ContentLoader
        speed={2}
        width="full"
        height={600}
        backgroundColor={colors.lightestGray}
        foregroundColor={colors.lightGray}
        radius={10}
      >
        <rect x="0" y="0" rx="6" ry="6" width="50" height="20" />
        <rect x="0" y="30" rx="6" ry="6" width="200" height="45" />
        <rect x="0" y="110" rx="6" ry="6" width="100%" height="40" />
        <rect x="0" y="180" rx="12" ry="12" width="100%" height="400" />
      </ContentLoader>
      <ContentLoader
        speed={2}
        width={350}
        height={1000}
        backgroundColor={colors.lightestGray}
        foregroundColor={colors.lightGray}
        radius={10}
      >
        <rect x="0" y="10" rx="6" ry="6" width="35" height="16.5" />
        <rect x="75" y="10" rx="6" ry="6" width="90" height="16.5" />
        <rect x="170" y="10" rx="6" ry="6" width="50" height="16.5" />
        <rect x="0" y="48" rx="6" ry="6" width="60" height="16.5" />
        <rect x="75" y="43" rx="12" ry="12" width="96" height="25" />
        {/* Divider */}
        <rect x="0" y="91" rx="6" ry="6" width="100%" height="1" />
        {/* Subscribers */}
        <rect x="0" y="116" rx="6" ry="6" width="60" height="16.5" />
        <circle cx="80" cy="123" r="10" />
        <circle cx="16" cy="158" r="16" />
        {/* Approvals */}
        <rect x="0" y="191" rx="6" ry="6" width="60" height="16.5" />
        <circle cx="85" cy="198" r="10" />
        <circle cx="16" cy="236" r="16" />
        <circle cx="55" cy="236" r="16" />
      </ContentLoader>
    </div>
  );
};

export default CampaignPageLoader;
