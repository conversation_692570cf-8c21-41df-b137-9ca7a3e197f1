import { useState } from "react";

import { BookmarkIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { FormTextInput } from "~/components/form";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { useFilters } from "~/providers";
import { FilterType } from "~/types";
import { getKeys, handleTrpcError, trpc } from "~/utils";

type FormValues = {
  name: string;
};

type Props = {
  filters: FilterType;
  updateParams: (filters: FilterType, filterID?: string) => void;
};

const SaveFilterButton = ({ filters, updateParams }: Props) => {
  const router = useRouter();

  const { control, handleSubmit } = useForm<FormValues>();

  const [modalOpen, setModalOpen] = useState<boolean>(false);

  const { setFilters } = useFilters();

  const filterID = router.query.id as string;

  trpc.filter.getAll.useQuery();

  const createFilterMutation = trpc.filter.create.useMutation({
    onSuccess: (data) => {
      setFilters(data.filters);
      updateParams(filters, data.newFilter.id);
      setModalOpen(false);
    },
    onError: (error) => {
      handleTrpcError({ title: "Failed to Save Filter", error });
    },
  });

  const updateFilterMutation = trpc.filter.update.useMutation({
    onSuccess: (data) => {
      setFilters(data.filters);
      updateParams(filters, filterID);
      toast.success("Filter updated", {
        description: "Your filter has been updated successfully",
      });
    },
    onError: (error) => {
      handleTrpcError({ title: "Failed to Update Filter", error });
    },
  });

  const saveNewFilter = ({ name }: FormValues) => {
    createFilterMutation.mutate({
      name,
      query: filters,
    });
  };

  const filterKeys = getKeys(filters);
  const emptyFilter = filterKeys.every((key) => filters[key].length === 0);

  if (emptyFilter) {
    return null;
  }

  return (
    <>
      {filterID == null ? (
        <div>
          <button
            className="text-basic hover:bg-slate focus flex h-7 items-center gap-1 rounded-lg bg-white px-2 shadow-sm ring-0 outline-hidden transition-colors"
            onClick={() => setModalOpen(true)}
          >
            <BookmarkIcon className="h-4 w-4" />
            <div className="font-body-xs font-medium">Save</div>
          </button>
        </div>
      ) : (
        <div className="z-10 w-full max-w-sm">
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              <div className="text-basic hover:bg-slate focus font-body-sm flex h-7 items-center gap-1 rounded-md bg-white px-2 shadow-sm ring-0 outline-hidden transition-colors">
                <BookmarkIcon className="h-4 w-4" />
                <div className="font-body-xs font-medium">Save</div>
              </div>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item
                icon="PencilSquareIcon"
                onClick={() => {
                  updateFilterMutation.mutate({
                    id: filterID,
                    query: filters,
                  });
                }}
              >
                Update Existing View
              </DropdownMenu.Item>
              <DropdownMenu.Item
                icon="PlusIcon"
                onClick={() => setModalOpen(true)}
              >
                Save as New View
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        </div>
      )}
      <Credenza.Root open={modalOpen} onOpenChange={setModalOpen}>
        <Credenza.Header>
          <Credenza.Title>Create New Saved View</Credenza.Title>
          <Credenza.Description className="sr-only">
            Create a new saved view with a name
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <form onSubmit={handleSubmit(saveNewFilter)}>
            <FormTextInput
              control={control}
              name="name"
              placeholder="Enter name here"
              rules={{ required: true }}
            />
            <Credenza.Footer>
              <Button
                variant="secondary"
                disabled={createFilterMutation.isPending}
                onClick={() => setModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" isLoading={createFilterMutation.isPending}>
                Save
              </Button>
            </Credenza.Footer>
          </form>
        </Credenza.Body>
      </Credenza.Root>
    </>
  );
};

export default SaveFilterButton;
