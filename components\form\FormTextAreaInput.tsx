import { Controller } from "react-hook-form";

import { Label, TextareaInput } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import camelCaseToWords from "~/utils/camelCaseToWords";

type FormTextareaInputProps = {
  control: any;
  name: string;
  placeholder?: string;
  disabled?: boolean;
  readOnly?: boolean;
  label?: string;
  isOptional?: boolean;
  tooltipText?: string;
  rows: number;
  rules?: Rules;
};

const FormTextareaInput = ({
  control,
  name,
  placeholder,
  readOnly,
  disabled,
  label,
  isOptional = false,
  tooltipText,
  rows,
  rules = {},
}: FormTextareaInputProps) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  if (typeof rules.min === "number") {
    rules = {
      ...rules,
      min: { value: rules.min, message: `Minimum value is ${rules.min}` },
    };
  }

  if (typeof rules.max === "number") {
    rules = {
      ...rules,
      max: { value: rules.max, message: `Maximum value is ${rules.max}` },
    };
  }

  if (typeof rules.maxLength === "number") {
    rules = {
      ...rules,
      maxLength: {
        value: rules.maxLength,
        message: `Max length is ${rules.maxLength}`,
      },
    };
  }

  if (typeof rules.minLength === "number") {
    rules = {
      ...rules,
      minLength: {
        value: rules.minLength,
        message: `Max length is ${rules.minLength}`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({
        field: { onChange, onBlur, value },
        fieldState: { error },
      }) => {
        return (
          <div>
            <Label
              value={label}
              isRequired={!!rules.required}
              tooltipText={tooltipText}
            >
              <TextareaInput
                value={value as string}
                onChange={onChange}
                onBlur={onBlur}
                placeholder={placeholder}
                disabled={disabled}
                readOnly={readOnly}
                rows={rows}
                isInvalid={!!error}
              />
            </Label>
            <Error error={error} />
          </div>
        );
      }}
    />
  );
};

export default FormTextareaInput;
