import React, { ReactNode } from "react";
import { Divider } from "~/components";
import Avatar from "~/components/ui/Avatar";
import SettingsLayout from "~/components/app/settings/SettingsLayout";

// Types defined inline - TEMPORARY: These will be replaced with tRPC-derived types when integrating with real data
interface User {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  profileImageUrl?: string;
  workspaceCount: number;
}



const Root = ({
  children,
  isLoading,
  isEmpty,
  emptyMessage = "No members found",
}: {
  children: ReactNode;
  isLoading: boolean;
  isEmpty: boolean;
  emptyMessage?: string;
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="text-medium-dark-gray">Loading...</div>
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div className="flex justify-center py-8">
        <div className="text-medium-dark-gray">{emptyMessage}</div>
      </div>
    );
  }

  return (
    <SettingsLayout.Section>
      <div className="flex flex-col">
        {React.Children.toArray(children).map((child, index, array) => (
          <div key={index}>
            {child}
            {index < array.length - 1 && <Divider padding="md" />}
          </div>
        ))}
      </div>
    </SettingsLayout.Section>
  );
};
Root.displayName = "Members";

const Member = ({ children }: { children: ReactNode }) => {
  return (
    <div className="flex items-center justify-between w-full gap-6">
      {children}
    </div>
  );
};
Member.displayName = "MembersMember";

interface MemberDetailsProps {
  user: User;
}

const MemberDetails = ({ user }: MemberDetailsProps) => {
  const initials = `${user.firstName?.charAt(0) || ''}${user.lastName?.charAt(0) || ''}`;

  return (
    <div className="flex items-center gap-3 flex-1">
      <Avatar.Root className="h-10 w-10">
        {user.profileImageUrl && (
          <Avatar.Image
            src={user.profileImageUrl}
            alt={`${user.firstName} ${user.lastName}`}
          />
        )}
        <Avatar.Fallback>
          {initials}
        </Avatar.Fallback>
      </Avatar.Root>
      <div className="flex flex-col">
        <div className=" font-medium">
          {user.firstName} {user.lastName}
        </div>
        <div className="text-medium-dark-gray text-sm">
          {user.email}
        </div>
      </div>
    </div>
  );
};
MemberDetails.displayName = "MembersMemberDetails";

interface MemberActionsProps {
  children: ReactNode;
}

const MemberActions = ({ children }: MemberActionsProps) => {
  return (
    <div className="flex items-center justify-end">
      {children}
    </div>
  );
};
MemberActions.displayName = "MembersMemberActions";

export default {
  Root,
  Member,
  MemberDetails,
  MemberActions,
};

// Types defined inline - TEMPORARY: These will be replaced with tRPC-derived types when integrating with real data
export type { User };
