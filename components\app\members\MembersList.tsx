import { EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import React, { ReactNode } from "react";
import { Divider, UserAvatar } from "~/components";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import DropdownMenu from "~/components/ui/DropdownMenu";

// Types defined inline - TEMPORARY: These will be replaced with tRPC-derived types when integrating with real data
interface User {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  profileImageUrl?: string;
  workspaceCount: number;
}

interface MemberAction {
  label: string;
  onClick: (user: User) => void;
  variant?: 'destructive';
  icon?: ReactNode;
}

// Root component
const Root = ({
  children,
  isLoading,
  isEmpty,
  emptyMessage = "No members found",
}: {
  children: ReactNode;
  isLoading: boolean;
  isEmpty: boolean;
  emptyMessage?: string;
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="text-medium-dark-gray">Loading...</div>
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div className="flex justify-center py-8">
        <div className="text-medium-dark-gray">{emptyMessage}</div>
      </div>
    );
  }

  return (
    <SettingsLayout.Section>
      <div className="flex flex-col">
        {React.Children.toArray(children).map((child, index, array) => (
          <div key={index}>
            {child}
            {index < array.length - 1 && <Divider padding="md" />}
          </div>
        ))}
      </div>
    </SettingsLayout.Section>
  );
};
Root.displayName = "Members";

// Title component
const Title = ({ children }: { children: ReactNode }) => {
  return (
    <SettingsLayout.SectionTitle>
      {children}
    </SettingsLayout.SectionTitle>
  );
};
Title.displayName = "MembersTitle";

// Member component
const Member = ({ children }: { children: ReactNode }) => {
  return (
    <div className="flex items-center justify-between w-full">
      {children}
    </div>
  );
};
Member.displayName = "MembersMember";

// MemberDetails component
interface MemberDetailsProps {
  user: User;
  showWorkspaceCount?: boolean;
}

const MemberDetails = ({ user, showWorkspaceCount = false }: MemberDetailsProps) => {
  return (
    <div className="flex items-center gap-3 flex-1">
      <UserAvatar
        user={{
          firstName: user.firstName,
          lastName: user.lastName,
          detail: user.email,
          profileImageUrl: user.profileImageUrl,
        }}
        showFullName={true}
        showDetail={true}
        backgroundColor="mediumGray"
      />
      {showWorkspaceCount && (
        <div className="text-medium-dark-gray font-body-sm ml-auto">
          {user.workspaceCount === 1
            ? "1 workspace"
            : `${user.workspaceCount} workspaces`}
        </div>
      )}
    </div>
  );
};
MemberDetails.displayName = "MembersMemberDetails";

// MemberActions component
interface MemberActionsProps {
  user: User;
  actions: MemberAction[];
}

const MemberActions = ({ user, actions }: MemberActionsProps) => {
  if (actions.length === 0) return null;

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <EllipsisHorizontalIcon className="h-5 w-5 text-medium-dark-gray hover:text-basic cursor-pointer transition-colors" />
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end">
        <DropdownMenu.Group>
          {actions.map((action, actionIndex) => (
            <DropdownMenu.Item
              key={actionIndex}
              onClick={() => action.onClick(user)}
              className={
                action.variant === 'destructive'
                  ? "text-red-600 hover:text-red-700"
                  : ""
              }
            >
              <div className="flex items-center gap-2">
                {action.icon && action.icon}
                {action.label}
              </div>
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Group>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
MemberActions.displayName = "MembersMemberActions";

export default {
  Root,
  Title,
  Member,
  MemberDetails,
  MemberActions,
};

// Types defined inline - TEMPORARY: These will be replaced with tRPC-derived types when integrating with real data
export type { User, MemberAction };
