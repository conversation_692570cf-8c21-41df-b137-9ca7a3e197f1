import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { ReactRenderer } from "@tiptap/react";
import { SuggestionKeyDownProps, SuggestionProps } from "@tiptap/suggestion";
import classNames from "classnames";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import { useAutosave } from "react-autosave";
import tippy, { Instance } from "tippy.js";
import { Divider, Loader, UserAvatar } from "~/components";
import Button from "~/components/ui/Button";
import { openIntercomArticle, trpc } from "~/utils";
import getLinkedInProfilePermalink from "~/utils/linkedin/getLinkedInProfilePermalink";
import LinkMention from "./LinkMention";

type LinkedInMentionListRef = {
  onKeyDown: (props: SuggestionKeyDownProps) => boolean;
};

type LinkedInMentionListProps = {
  query: string;
  command: ({
    id,
    label,
    href,
  }: {
    id: string;
    label: string;
    href: string;
  }) => void;
};

const LinkedInMentionList = forwardRef(
  (props: LinkedInMentionListProps, ref: React.Ref<LinkedInMentionListRef>) => {
    const [selectedIndex, setSelectedIndex] = useState(0);

    const [query, setQuery] = useState<string>(props.query);
    const [forceSearchUser, setForceSearchUser] = useState<boolean>(false);

    useAutosave({
      data: props.query,
      onSave: (data) => {
        setQuery(data);
        setForceSearchUser(false);
      },
      interval: 250,
    });

    const { data, isLoading } =
      trpc.integration.linkedin.searchByVanityName.useQuery(
        {
          vanityName: query,
          forceSearchUser,
        },
        {
          refetchOnWindowFocus: false,
          refetchInterval: false,
        }
      );

    const recordMentionMutation =
      trpc.integration.linkedin.recordLinkedInMention.useMutation();

    const previouslyTagged =
      data?.previouslyTagged.map((previouslyTagged) => ({
        id: previouslyTagged.id,
        urn: previouslyTagged.urn,
        name: previouslyTagged.name,
        profileImageUrl: previouslyTagged.profileImageUrl,
        vanityName: previouslyTagged.vanityName,
        type: previouslyTagged.type,
        href: `https://www.linkedin.com/${
          previouslyTagged.type === "User" ? "in" : "company"
        }/${previouslyTagged.vanityName}`,
      })) || [];

    const users =
      data?.users.map((user) => ({
        id: user.id,
        urn: user.urn,
        name: user.name,
        profileImageUrl: user.profileImageUrl,
        vanityName: user.vanityName,
        type: "User",
        href: getLinkedInProfilePermalink({
          vanityName: user.vanityName,
          type: "User",
        }),
      })) || [];

    const organizations =
      data?.organizations.map((organization) => ({
        id: organization.id,
        urn: organization.urn,
        name: organization.name,
        profileImageUrl: organization.profileImageUrl,
        vanityName: organization.vanityName,
        type: "Organization",
        href: getLinkedInProfilePermalink({
          vanityName: organization.vanityName,
          type: "Organization",
        }),
      })) || [];

    const allOptions = [...previouslyTagged, ...organizations, ...users];

    const upHandler = () => {
      setSelectedIndex(
        (selectedIndex + allOptions.length - 1) % allOptions.length
      );
    };

    const downHandler = () => {
      setSelectedIndex((selectedIndex + 1) % allOptions.length);
    };

    const enterHandler = () => {
      const item = allOptions[selectedIndex];

      if (item) {
        recordMentionMutation.mutate({
          accountID: item.id,
        });

        props.command({ id: item.urn, label: item.name, href: item.href });
      }
    };

    useEffect(() => {
      setSelectedIndex(selectedIndex % allOptions.length);
    }, [allOptions.length]);

    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }) => {
        if (event.key === "ArrowUp") {
          upHandler();
          return true;
        }

        if (event.key === "ArrowDown") {
          downHandler();
          return true;
        }

        if (event.key === "Enter") {
          enterHandler();
          return true;
        }

        return false;
      },
    }));

    if (isLoading) {
      return (
        <div className="rounded-lg bg-white px-2 py-1 shadow-xl">
          <div className="flex h-10 w-20 items-center justify-center">
            <Loader size="md" />
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-start gap-1 rounded-lg bg-white px-2 py-1 shadow-xl">
        <div className="space-y-4">
          {previouslyTagged.length > 0 && (
            <div>
              <div className="font-eyebrow-sm text-medium-dark-gray ml-2.5">
                Previously Tagged
              </div>
              <div className="divide-tan/25 font-body mb-1">
                {previouslyTagged.map((option, index) => {
                  return (
                    <button
                      key={option.id}
                      className={classNames(
                        "font-eyebrow hover:bg-lightest-gray flex w-full items-center gap-2 rounded-md px-2 py-1 transition-colors",
                        {
                          "bg-lightest-gray": selectedIndex === index,
                        }
                      )}
                      onClick={() => {
                        recordMentionMutation.mutate({
                          accountID: option.id,
                        });
                        props.command({
                          id: option.urn,
                          label: option.name,
                          href: option.href,
                        });
                      }}
                    >
                      <UserAvatar
                        user={{
                          firstName: option.name,
                          detail: `@${option.vanityName}`,
                          profileImageUrl: option.profileImageUrl,
                        }}
                        showFullName={true}
                        showDetail={true}
                      />
                    </button>
                  );
                })}
              </div>
            </div>
          )}
          <div>
            <div className="font-eyebrow-sm text-medium-dark-gray ml-2">
              Companies
            </div>
            <div className="divide-tan/25 mb-1">
              {organizations.map((option, index) => {
                return (
                  <button
                    key={option.id}
                    className={classNames(
                      "font-eyebrow hover:bg-lightest-gray flex w-full items-center gap-2 rounded-md px-2 py-1 transition-colors",
                      {
                        "bg-lightest-gray":
                          selectedIndex === index + previouslyTagged.length,
                      }
                    )}
                    onClick={() => {
                      recordMentionMutation.mutate({
                        accountID: option.id,
                      });
                      props.command({
                        id: option.urn,
                        label: option.name,
                        href: option.href,
                      });
                    }}
                  >
                    <UserAvatar
                      user={{
                        firstName: option.name,
                        detail: `@${option.vanityName}`,
                        profileImageUrl: option.profileImageUrl,
                      }}
                      showFullName={true}
                      showDetail={true}
                    />
                  </button>
                );
              })}
              {organizations.length === 0 && (
                <div className="font-body-sm text-medium-dark-gray ml-2">
                  No results found
                </div>
              )}
            </div>
          </div>
          <div>
            <div className="font-eyebrow-sm text-medium-dark-gray ml-2">
              Users
            </div>
            <div className="divide-tan/25">
              {users.map((option, index) => {
                return (
                  <button
                    key={option.id}
                    className={classNames(
                      "font-eyebrow hover:bg-lightest-gray flex w-full items-center gap-2 rounded-md px-2 py-1 transition-colors",
                      {
                        "bg-lightest-gray":
                          selectedIndex ===
                          index +
                            previouslyTagged.length +
                            organizations.length,
                      }
                    )}
                    onClick={() => {
                      recordMentionMutation.mutate({
                        accountID: option.id,
                      });
                      props.command({
                        id: option.urn,
                        label: option.name,
                        href: option.href,
                      });
                    }}
                  >
                    <UserAvatar
                      user={{
                        firstName: option.name,
                        detail: `@${option.vanityName}`,
                        profileImageUrl: option.profileImageUrl,
                      }}
                      showFullName={true}
                      showDetail={true}
                    />
                  </button>
                );
              })}
              {users.length === 0 && (
                <div className="translate-x-1">
                  {!forceSearchUser ? (
                    <div className="mt-1">
                      <Button
                        onClick={() => {
                          setForceSearchUser(true);
                        }}
                        variant="ghost"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <Loader size="sm" />
                        ) : (
                          <div className="flex items-center justify-center gap-1">
                            <MagnifyingGlassIcon className="text-basic h-4 w-4" />
                            <span className="text-basic font-body-sm font-medium">{`Search users for "${query}"`}</span>
                          </div>
                        )}
                      </Button>
                    </div>
                  ) : (
                    <div className="font-body-sm text-medium-dark-gray mr-2 ml-1 whitespace-nowrap">
                      No results found
                    </div>
                  )}
                </div>
              )}
              <div className="font-body-sm mx-2 mb-2 w-48">
                <Divider padding="sm" />
                <div className="flex flex-col items-start gap-0.5 pt-1">
                  Having trouble with tags?
                  <div className="-translate-x-1.5">
                    <Button
                      onClick={() => openIntercomArticle("linkedin_tagging")}
                      size="sm"
                      variant="ghost"
                    >
                      Click here
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

const LinkedInMention = LinkMention.extend({
  addKeyboardShortcuts() {
    return {
      Backspace: () => {
        try {
          const { selection } = this.editor.state;
          const pos = selection.$from.pos;
          const node = this.editor.state.doc.nodeAt(pos - 1);

          if (!node?.attrs?.label) return false;

          // Check if the label contains multiple words
          const names = node.attrs.label.split(" ");
          if (names.length > 1) {
            // Remove the last name
            const newLabel = names.slice(0, -1).join(" ");

            // Update the node with the new label
            const tr = this.editor.state.tr.setNodeMarkup(pos - 1, undefined, {
              ...node.attrs,
              label: newLabel,
            });

            this.editor.view.dispatch(tr);
            return true;
          }

          return false;
        } catch (error) {
          return false;
        }
      },
    };
  },
}).configure({
  HTMLAttributes: {
    class: "bg-secondary-light text-secondary px-1 py-0.5 rounded-sm",
  },
  renderHTML({ options, node }) {
    return [
      "a",
      {
        ...options.HTMLAttributes,
        href: node.attrs.href,
        target: "_blank",
        rel: "noopener noreferrer",
      },
      node.attrs.label,
    ];
  },
  renderText: ({ node }) => {
    return `@[${node.attrs.label}](${node.attrs.id})`;
  },
  deleteTriggerWithBackspace: true,
  suggestion: {
    items: () => [],
    render: () => {
      let component: ReactRenderer<
        LinkedInMentionListRef,
        LinkedInMentionListProps
      >;
      let popup: Instance;

      return {
        onStart: (props: SuggestionProps<any, any>) => {
          component = new ReactRenderer<
            LinkedInMentionListRef,
            LinkedInMentionListProps
          >(LinkedInMentionList, {
            props,
            editor: props.editor,
          });

          if (!props.clientRect) {
            return;
          }

          popup = tippy(document.body, {
            getReferenceClientRect: () => props.clientRect?.() || new DOMRect(),
            appendTo: () => document.body,
            content: component.element,
            showOnCreate: true,
            interactive: true,
            trigger: "manual",
            placement: "bottom-start",
          });
        },

        onUpdate: (props: SuggestionProps<any, any>) => {
          component.updateProps(props);

          if (!props.clientRect) {
            return;
          }

          popup.setProps({
            getReferenceClientRect: () => props.clientRect?.() || new DOMRect(),
          });
        },

        onKeyDown: (props: SuggestionKeyDownProps) => {
          if (props.event.key === "Escape") {
            popup.hide();

            return true;
          }

          return component.ref?.onKeyDown(props) || false;
        },

        onExit: () => {
          popup.destroy();
          component.destroy();
        },
      };
    },
  },
});

export default LinkedInMention;
