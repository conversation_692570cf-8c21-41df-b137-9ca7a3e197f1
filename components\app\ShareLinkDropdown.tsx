import { Menu, Transition } from "@headlessui/react";
import { LinkIcon } from "@heroicons/react/24/outline";
import capitalize from "lodash/capitalize";
import { useRouter } from "next/router";
import { Fragment, useState } from "react";
import Switch from "~/components/ui/Switch";
import { trpc } from "~/utils";
import CopyButton from "../CopyButton";

type Props = {
  id: string;
  type: "post" | "campaign" | "idea";
  isSharedPublicly: boolean;
  arePostsSharedPublicly?: boolean;
};

const ShareLinkDropdown = ({
  id,
  type,
  isSharedPublicly: initialIsSharedPublicly,
  arePostsSharedPublicly: initialArePostsSharedPublicly,
}: Props) => {
  const [isSharedPublicly, setIsSharedPublicly] = useState<boolean>(
    initialIsSharedPublicly
  );
  const [arePostsSharedPublicly, setArePostsSharedPublicly] = useState<boolean>(
    initialArePostsSharedPublicly || false
  );
  const router = useRouter();

  const updatePostMutation = trpc.post.update.useMutation();
  const updateCampaignPublicVisibilityMutation =
    trpc.campaign.updatePublicVisibility.useMutation();

  const contentLink = `${process.env.NEXT_PUBLIC_DOMAIN}${router.asPath}`;

  return (
    <Menu as="div" className="relative inline-block text-left">
      <Menu.Button className="bg-surface hover:bg-basic/10 text-basic rounded-sm p-1.5 transition-colors">
        <LinkIcon className="h-5 w-5" />
      </Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="font-body-sm box-shadow absolute right-0 z-10 w-fit origin-top-right rounded-md bg-white px-3 py-5">
          <Menu.Item>
            <>
              <div className="mb-1 font-medium">{capitalize(type)} Link</div>
              <CopyButton
                label={`${capitalize(type)} Link`}
                value={contentLink}
                textStyle="font-body-sm"
              />
            </>
          </Menu.Item>
          <Menu.Item>
            <>
              <div className="mt-4 flex items-center justify-between">
                <div>Share public access</div>
                <Switch
                  checked={isSharedPublicly}
                  onCheckedChange={(updatedIsSharedPublicly) => {
                    if (type === "post" || type === "idea") {
                      updatePostMutation.mutate({
                        id,
                        isSharedPublicly: updatedIsSharedPublicly,
                      });
                    } else {
                      updateCampaignPublicVisibilityMutation.mutate({
                        id,
                        isSharedPublicly: updatedIsSharedPublicly,
                      });
                    }
                    setIsSharedPublicly(updatedIsSharedPublicly);
                  }}
                />
              </div>
              <div className="text-medium-dark-gray">
                Anyone with the link can view
              </div>
            </>
          </Menu.Item>
          {type === "campaign" && (
            <Menu.Item>
              <>
                <div className="mt-2 flex items-center justify-between">
                  <div>Share campaign's posts</div>
                  <Switch
                    checked={arePostsSharedPublicly}
                    onCheckedChange={(updatedArePostsSharedPublicly) => {
                      const updatedIsSharedPublicly =
                        updatedArePostsSharedPublicly ? true : isSharedPublicly;

                      updateCampaignPublicVisibilityMutation.mutate({
                        id,
                        isSharedPublicly: updatedIsSharedPublicly,
                        arePostsSharedPublicly: updatedArePostsSharedPublicly,
                      });

                      setIsSharedPublicly(updatedIsSharedPublicly);
                      setArePostsSharedPublicly(updatedArePostsSharedPublicly);
                    }}
                  />
                </div>
                <div className="text-medium-dark-gray">
                  Anyone with the link can view
                </div>
              </>
            </Menu.Item>
          )}
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

export default ShareLinkDropdown;
