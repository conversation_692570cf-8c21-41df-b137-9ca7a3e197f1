import { LinkIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import Link from "next/link";

type LinkProps = {
  /** The display text */
  value: string | number | React.ReactNode;
  color?: "basic" | "secondary" | "inherit";
  size?: "sm" | "md";
  /** The url that the link component will direct to */
  href: string;
  /** Should this link open in a new tab, i.e. `target='_blank'` */
  openInNewTab?: boolean;
  /** If we should prefix the value with the link icon */
  showLinkIcon?: boolean;
  /** If the text should wrap */
  wrapText?: boolean;
  /** If false then we'll add http if necessary */
  ignoreProtocol?: boolean;
};

const TextLink = ({
  value,
  href,
  size,
  color = "basic",
  openInNewTab = true,
  showLinkIcon = false,
  wrapText = false,
  ignoreProtocol = false,
}: LinkProps) => {
  if (
    !href.startsWith("http") &&
    !href.startsWith("/") &&
    !href.startsWith("mailto:") &&
    !ignoreProtocol
  ) {
    href = `http://${href}`;
  }

  return (
    <Link
      className={classNames(
        "w-fit transform font-medium underline underline-offset-4 transition",
        {
          "whitespace-normal": wrapText,
          "text-basic": color === "basic",
          "decoration-basic": !showLinkIcon && color === "basic",

          "text-secondary": color === "secondary",
          "decoration-secondary": !showLinkIcon && color === "secondary",

          "text-inherit": color === "inherit",
          "underline-inherit underline": !showLinkIcon && color === "inherit",
          "flex items-center gap-1": showLinkIcon,
          "font-body": size === "md",
          "font-body-sm": size === "sm",
        }
      )}
      rel={openInNewTab ? "noopener noreferrer" : undefined}
      target={openInNewTab ? "_blank" : undefined}
      href={href}
    >
      {showLinkIcon && <LinkIcon className="h-3 w-3" />}
      {value}
    </Link>
  );
};

export default TextLink;
