import { TRPCError } from "@trpc/server";
import catchZenstackDeleteError from "apiUtils/catchZenstackDeleteError";
import removeSubscriptionItem from "apiUtils/stripe/removeSubscriptionItem";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const disconnectThreadsIntegration = async ({
  integrationID,
  workspaceID,
  userID,
}: {
  integrationID: string;
  workspaceID: string;
  userID: string;
}) => {
  const threadsIntegration = await db.threadsIntegration.findFirst({
    where: {
      id: integrationID,
      workspaceID,
    },
    select: {
      id: true,
      accessToken: true,
      account: {
        select: {
          username: true,
        },
      },
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
    },
  });

  if (!threadsIntegration) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "No integration found",
    });
  } else {
    const workspace = threadsIntegration.workspace;

    const postsToUpdate = await db.post.findMany({
      where: {
        threadsIntegrationID: threadsIntegration.id,
        scheduledPosts: {
          some: {
            status: "Scheduled",
            channel: "Threads",
            publishAt: {
              gt: new Date(),
            },
          },
        },
      },
      select: {
        id: true,
      },
    });
    const postIDs = postsToUpdate.map((post) => post.id);

    // Cancel all scheduled posts for just Threads
    await db.scheduledPost.updateMany({
      where: {
        channel: "Threads",
        status: "Scheduled",
        publishAt: {
          gt: new Date(),
        },
        post: {
          id: {
            in: postIDs,
          },
        },
      },
      data: {
        status: "Cancelled",
      },
    });

    const updatedPosts = await db.post.findMany({
      where: {
        id: {
          in: postIDs,
        },
      },
      select: {
        id: true,
        _count: {
          select: {
            scheduledPosts: {
              where: {
                status: "Scheduled",
              },
            },
          },
        },
      },
    });

    const postsWithoutScheduledPosts = updatedPosts.filter(
      (post) => post._count.scheduledPosts === 0
    );
    await db.post.updateMany({
      where: {
        id: {
          in: postsWithoutScheduledPosts.map((post) => post.id),
        },
      },
      data: {
        status: "Finalized",
      },
    });

    const postDefaults = await db.postDefault.findFirst({
      where: {
        workspaceID: workspaceID,
      },
      select: {
        threadsIntegrationID: true,
        workspace: {
          select: {
            threadsIntegrations: {
              where: {
                id: {
                  not: threadsIntegration.id,
                },
              },
              select: {
                id: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    if (postDefaults?.threadsIntegrationID === threadsIntegration.id) {
      const oldestThreadsIntegration =
        postDefaults.workspace.threadsIntegrations.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
        )[0];

      await db.postDefault.update({
        where: {
          workspaceID: workspaceID,
        },
        data: {
          threadsIntegrationID:
            oldestThreadsIntegration != null
              ? oldestThreadsIntegration.id
              : null,
        },
      });
    }

    await catchZenstackDeleteError(async () => {
      await db.threadsIntegration.update({
        where: {
          id: threadsIntegration.id,
        },
        data: {
          deletedTimeStamp: Math.floor(new Date().getTime() / 1000),
          deletedByID: userID,
        },
      });
    });

    await removeSubscriptionItem({
      workspaceID,
      itemName: "ADDITIONAL_SOCIAL_PROFILE",
      quantity: 1,
    });

    posthog.capture({
      distinctId: userID,
      event: POSTHOG_EVENTS.socialAccount.disconnected,
      properties: {
        channel: "Threads",
      },
      groups: {
        workspace: workspaceID,
        company: workspace.companyID,
      },
    });

    return threadsIntegration.account;
  }
};

export default disconnectThreadsIntegration;
