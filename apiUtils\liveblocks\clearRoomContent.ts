import { withProsemirrorDocument } from "@liveblocks/node-prosemirror";
import { liveblocks } from "server/routers/liveblocks";
import getOrCreateRoom from "./getOrCreateRoom";

// Clears the content of a Liveblocks room with error handling
const clearRoomContent = async ({
  roomID,
  userID,
}: {
  roomID: string;
  userID: string;
}): Promise<void> => {
  try {
    await getOrCreateRoom({
      roomID,
      userID,
    });

    await withProsemirrorDocument(
      {
        client: liveblocks,
        roomId: roomID,
      },
      async (api) => {
        await api.clearContent();
      }
    );
  } catch (error) {
    console.error("Error clearing content for room", error);
    throw error;
  }
};

export default clearRoomContent;
