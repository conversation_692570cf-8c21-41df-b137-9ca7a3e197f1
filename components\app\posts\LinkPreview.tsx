import { Channel } from "@prisma/client";
import classNames from "classnames";
import { useEffect, useState } from "react";
import { useAutosave } from "react-autosave";
import { cleanedUrl, trpc } from "~/utils";

type Props = {
  url: string | null;
  channel: Channel;
};

const LinkPreview = ({ url, channel }: Props) => {
  const [imageWidth, setImageWidth] = useState<null | number>(null);

  const [currentUrl, setCurrentUrl] = useState<string | null>(url);

  useAutosave({
    data: url,
    onSave: (data) => {
      setCurrentUrl(data);
    },
    interval: 500,
  });

  const linkPreviewQuery = trpc.linkPreview.getUrlData.useQuery(
    {
      url: currentUrl,
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
    }
  );

  const metadata = linkPreviewQuery.data?.metadata;

  const image = metadata?.image;

  useEffect(() => {
    if (image) {
      const img = new Image();
      img.src = image;
      img.onload = () => {
        setImageWidth(img.width);
      };
    } else {
      setImageWidth(null);
    }
  }, [image]);

  if (url == null || metadata == null) {
    return null;
  }

  const { hostname } = new URL(cleanedUrl(url));

  if (channel === "Twitter") {
    if (!metadata.image) {
      return null;
    } else {
      return (
        <a
          href={cleanedUrl(url)}
          rel="noopener noreferrer"
          target="_blank"
          className="relative block h-[268px] w-[500px] rounded-[16px] border border-[#CFD9DE] transition-colors hover:bg-[#F7F9F9]"
        >
          <img
            src={metadata.image}
            className="h-full w-full rounded-[15px] object-cover"
          />
          <div className="absolute bottom-3 left-3 rounded-sm bg-black/30 px-1 text-[13px] text-white">
            {metadata.hostname}
          </div>
        </a>
      );
    }
  } else if (channel === "Facebook") {
    return (
      <a
        href={cleanedUrl(url)}
        rel="noopener noreferrer"
        target="_blank"
        className="block w-full"
      >
        {metadata.image && (
          <img src={metadata.image} className="h-full w-full object-cover" />
        )}
        <div className="w-full bg-[#F0F2F5] px-4 py-3 text-[15px]">
          <div className="text-[13px] font-normal text-[#536471] uppercase">
            {metadata.hostname}
          </div>
          <div className="text-[17px] font-semibold text-[#0F1419]">
            {metadata.title}
          </div>
          <div className="truncate text-[15px] text-ellipsis text-[#536471]">
            {metadata.description}
          </div>
        </div>
      </a>
    );
  } else if (channel === "Threads") {
    return (
      <a
        href={cleanedUrl(url)}
        rel="noopener noreferrer"
        target="_blank"
        className="my-2 h-full w-full overflow-clip rounded-[8px] border-[0.5px] border-[#E5E5E5]"
      >
        {metadata.image && (
          <img
            src={metadata.image}
            className="aspect-8/3 w-full object-cover"
          />
        )}
        <div className="w-full space-y-2 border-t border-[#E5E5E5] px-4 py-4">
          <div className="text-[12px] font-normal text-[#999999]">
            {metadata.hostname?.replace("www.", "")}
          </div>
          <div className="text-[15px] text-black">{metadata.title}</div>
        </div>
      </a>
    );
  } else if (channel === "LinkedIn") {
    return (
      <a
        href={cleanedUrl(url)}
        rel="noopener noreferrer"
        target="_blank"
        className="grid w-full grid-cols-10 rounded-xl border border-[#8C8C8C33] p-3"
      >
        {metadata.image && (
          <img
            src={metadata.image}
            className="col-span-3 h-[72px] w-full rounded-lg object-cover"
          />
        )}
        <div
          className={classNames("flex flex-col justify-center gap-1 pl-3", {
            "col-span-7": metadata.image,
            "col-span-10": !metadata.image,
          })}
        >
          <div className="text-[14px] font-semibold text-black">
            {metadata.title}
          </div>
          <div className="text-[12px] text-[#67696B]">{metadata.hostname}</div>
        </div>
      </a>
    );
  } else if (metadata.image == null) {
    return (
      <a
        href={cleanedUrl(url)}
        rel="noopener noreferrer"
        target="_blank"
        className="flex h-[129px] w-[500px] rounded-[16px] transition-colors hover:bg-[#F7F9F9]"
      >
        {metadata.image ? (
          <img
            src={metadata.image}
            className="h-[129px] w-[129px] rounded-l-[16px] border border-[#CFD9DE] object-cover"
          />
        ) : (
          <div className="flex h-[129px] w-[129px] items-center justify-center rounded-l-[16px] border border-[#CFD9DE] bg-[#F7F9F9]">
            <svg viewBox="0 0 24 24" aria-hidden="true" className="h-8 w-8">
              <g>
                <path d="M1.998 5.5c0-1.38 1.119-2.5 2.5-2.5h15c1.381 0 2.5 1.12 2.5 2.5v13c0 1.38-1.119 2.5-2.5 2.5h-15c-1.381 0-2.5-1.12-2.5-2.5v-13zm2.5-.5c-.276 0-.5.22-.5.5v13c0 .**********.5h15c.276 0 .5-.22.5-.5v-13c0-.28-.224-.5-.5-.5h-15zM6 7h6v6H6V7zm2 2v2h2V9H8zm10 0h-4V7h4v2zm0 4h-4v-2h4v2zm-.002 4h-12v-2h12v2z"></path>
              </g>
            </svg>
          </div>
        )}
        <div className="font-chirp w-full rounded-r-[16px] border-y border-r border-[#CFD9DE] pl-4 text-[15px]">
          <div className="mt-4 pr-2">
            <div className="text-[#536471]">{metadata.hostname}</div>
            <div className="text-[#0F1419]">{metadata.title}</div>
            <div className="h-11 overflow-clip text-ellipsis text-[#536471]">
              {metadata.description}
            </div>
          </div>
        </div>
      </a>
    );
  } else {
    return null;
  }
};

export default LinkPreview;
