import { Channel } from "@prisma/client";
import classNames from "classnames";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import Checkbox from "~/components/Checkbox";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { EDITOR_CHANNELS } from "~/constants";
import { CHANNEL_ICON_MAP } from "~/types";
import { getKeys, trpc } from "~/utils";
import ChannelIcon from "../ChannelIcon";
import ChannelSelect from "./ChannelSelect";

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  post: {
    id: string;
    channels: Channel[];
  };
  defaultSourceChannel: Channel;
};

const SyncContentModal = ({
  open,
  setOpen,
  post,
  defaultSourceChannel,
}: Props) => {
  const router = useRouter();
  const [sourceChannel, setSourceChannel] = useState<Channel>(
    EDITOR_CHANNELS.includes(defaultSourceChannel)
      ? defaultSourceChannel
      : (post.channels.find((channel) =>
          EDITOR_CHANNELS.includes(channel)
        ) as Channel)
  );
  const channels = post.channels.filter(
    (channel) => EDITOR_CHANNELS.includes(channel) && channel !== sourceChannel
  );
  const [destinationCopyChannels, setDestinationCopyChannels] =
    useState<Channel[]>(channels);
  const [destinationAssetChannels, setDestinationAssetChannels] =
    useState<Channel[]>(channels);

  useEffect(() => {
    const defaultChannels = post.channels.filter(
      (channel) =>
        EDITOR_CHANNELS.includes(channel) && channel !== sourceChannel
    );
    setDestinationCopyChannels(defaultChannels);
    setDestinationAssetChannels(defaultChannels);
  }, [post.channels, sourceChannel]);

  useEffect(() => {
    if (EDITOR_CHANNELS.includes(defaultSourceChannel)) {
      setSourceChannel(defaultSourceChannel);
    }
  }, [defaultSourceChannel]);

  const syncCopyAndAssetsMutation = trpc.post.syncCopyAndAssets.useMutation({
    onSuccess: () => {
      toast.success("Synced Channels", {
        description: "Copy / assets have been synced",
      });
      setOpen(false);
      router.reload();
    },
  });

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header>
        <Credenza.Title>Sync Copy and Assets</Credenza.Title>
        <Credenza.Description>
          <div className="mb-1 flex items-center gap-1.5 font-medium text-black">
            <span>Sync from</span>
            <ChannelSelect
              value={sourceChannel}
              onChange={(channel) => setSourceChannel(channel)}
              ignoredChannels={getKeys(CHANNEL_ICON_MAP).filter(
                (channel) => !post.channels.includes(channel)
              )}
            />
            <span>to cross-post channels</span>
          </div>
          <div className="text-medium-dark-gray">
            Note that this will overwrite any existing copy or assets on the
            other channel(s). Some assets may not sync if not supported by the
            channel.
          </div>
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <div>
          <div className="font-eyebrow py-2">Copy</div>
          <div className="flex flex-col gap-4">
            {channels.map((channel) => {
              const isSelected = destinationCopyChannels.includes(channel);
              return (
                <div className="w-full" key={channel}>
                  <button
                    className={classNames("flex items-center outline-hidden")}
                    onClick={() => {
                      if (isSelected) {
                        setDestinationCopyChannels(
                          destinationCopyChannels.filter(
                            (selectedChannel) => selectedChannel !== channel
                          )
                        );
                      } else {
                        setDestinationCopyChannels([
                          ...destinationCopyChannels,
                          channel,
                        ]);
                      }
                    }}
                  >
                    <div className="flex w-full items-center gap-2">
                      <Checkbox
                        value={destinationCopyChannels.includes(channel)}
                        onChange={() => {
                          if (isSelected) {
                            setDestinationCopyChannels(
                              destinationCopyChannels.filter(
                                (selectedChannel) => selectedChannel !== channel
                              )
                            );
                          } else {
                            setDestinationCopyChannels([
                              ...destinationCopyChannels,
                              channel,
                            ]);
                          }
                        }}
                      />
                      <ChannelIcon key={channel} channel={channel} width={18} />
                      <div
                        className={classNames(
                          "font-eyebrow transition-colors",
                          {
                            "text-secondary": isSelected,
                          }
                        )}
                      >
                        {CHANNEL_ICON_MAP[channel].label}
                      </div>
                    </div>
                  </button>
                </div>
              );
            })}
          </div>
        </div>
        <div>
          <div className="font-eyebrow pt-4 pb-2">Assets</div>
          <div className="flex flex-col gap-4">
            {channels.map((channel) => {
              const isSelected = destinationAssetChannels.includes(channel);
              return (
                <div className="w-full" key={channel}>
                  <button
                    className={classNames("flex items-center outline-hidden")}
                    onClick={() => {
                      if (isSelected) {
                        setDestinationAssetChannels(
                          destinationAssetChannels.filter(
                            (selectedChannel) => selectedChannel !== channel
                          )
                        );
                      } else {
                        setDestinationAssetChannels([
                          ...destinationAssetChannels,
                          channel,
                        ]);
                      }
                    }}
                  >
                    <div className="flex w-full items-center gap-2">
                      <Checkbox
                        value={destinationAssetChannels.includes(channel)}
                        onChange={() => {
                          if (isSelected) {
                            setDestinationAssetChannels(
                              destinationAssetChannels.filter(
                                (selectedChannel) => selectedChannel !== channel
                              )
                            );
                          } else {
                            setDestinationAssetChannels([
                              ...destinationAssetChannels,
                              channel,
                            ]);
                          }
                        }}
                      />
                      <ChannelIcon key={channel} channel={channel} width={18} />
                      <div
                        className={classNames(
                          "font-eyebrow transition-colors",
                          {
                            "text-secondary": isSelected,
                          }
                        )}
                      >
                        {CHANNEL_ICON_MAP[channel].label}
                      </div>
                    </div>
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      </Credenza.Body>
      <Credenza.Footer>
        <Button variant="secondary" onClick={() => setOpen(false)}>
          Cancel
        </Button>
        <Button
          onClick={() =>
            syncCopyAndAssetsMutation.mutate({
              id: post.id,
              sourceChannel,
              copyChannels: destinationCopyChannels.filter(
                (channel) => channel !== sourceChannel
              ),
              assetChannels: destinationAssetChannels.filter(
                (channel) => channel !== sourceChannel
              ),
            })
          }
          isLoading={syncCopyAndAssetsMutation.isPending}
        >
          Sync
        </Button>
      </Credenza.Footer>
    </Credenza.Root>
  );
};

export default SyncContentModal;
