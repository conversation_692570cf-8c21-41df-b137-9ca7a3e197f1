import { Channel, PostStatus } from "@prisma/client";

import { useDrag } from "react-dnd";

import { motion } from "framer-motion";
import ContextMenu from "~/components/ui/ContextMenu";

import classNames from "classnames";
import Link from "next/link";
import { JSXElementConstructor, ReactElement, useState } from "react";
import { toast } from "sonner";
import { Badge, Divider, Tooltip, UserAvatar } from "~/components";
import { DraggableItemTypes } from "~/constants";
import { POST_STATUSES } from "~/types";
import {
  assertPostOrIdea,
  cn,
  getFullName,
  getPostPublishAtMoment,
  handleTrpcError,
  openConfirmationModal,
  sleep,
  trpc,
} from "~/utils";
import { StatusBadge } from "../posts";
import BulkDuplicatePostModal from "../posts/BulkDuplicatePostModal";

import {
  CheckBadgeIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { useElementSize } from "@mantine/hooks";
import copy from "copy-to-clipboard";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import { RouterOutput } from "~/utils/trpc";
import AccountChannelAvatar from "../AccountChannelAvatar";
import TimezoneTooltip from "../TimezoneTooltip";
import AccountList from "./AccountList";

type FullPost = RouterOutput["post"]["getMany"]["posts"][number];

type Props = {
  post: {
    id: string;
    title: string;
    channels: Channel[];
    status: PostStatus;
    campaignID: string | null;
    publishDate: Date;
    publishTime: Date | null;
    publishAt: Date | null;
    approvals: {
      id: string;
      user: {
        id: string;
        firstName: string | null;
        lastName: string | null;
      };
      isBlocking: boolean;
      approvedAt: Date | null;
    }[];
    accounts: {
      id: string;
      name: string;
      profileImageUrl?: string;
      channel: Channel;
    }[];
    labels: {
      id: string;
      name: string;
      color: string;
      backgroundColor: string;
      createdAt: Date;
    }[];
  };
  size?: "normal" | "compact";
  deletePost: (post: { id: string; title: string }) => void;
  addNewPost: (post: FullPost) => void;
  updatePost: (
    postID: string,
    updatedValues: {
      status: PostStatus;
    }
  ) => void;
  hoveredCampaignID?: string | null;
  isDragDisabled?: boolean;
};

const PostCalendarEvent = ({
  post,
  size = "normal",
  deletePost,
  addNewPost,
  updatePost,
  hoveredCampaignID,
  isDragDisabled = false,
}: Props) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();

  const { ref, width } = useElementSize();

  const [duplicateModalOpen, setDuplicateModalOpen] = useState<boolean>(false);
  const [bulkDuplicateModalOpen, setBulkDuplicateModalOpen] =
    useState<boolean>(false);

  const [{ isDragging }, dragRef] = useDrag(
    () => ({
      type: DraggableItemTypes.PostCalendarEvent,
      item: post,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    []
  );

  const duplicatePostMutation = trpc.post.duplicate.useMutation();

  const convertToIdeaMutation = trpc.post.convertToIdea.useMutation({
    onSuccess: () => {
      const data = trpcUtils.post.getMany.getData();
      if (data) {
        const previousPosts = data.posts;
        const updatedPosts = previousPosts.filter(
          (previousPost) => previousPost.id !== post.id
        );
        trpcUtils.post.getMany.setData(undefined, { posts: updatedPosts });
      }
      toast.success("Post Moved to Ideas", {
        description: "Your post has been moved to the Ideas tab",
        action: {
          label: "Click to View Post",
          onClick: () => {
            router.push(`/ideas/${post.id}`);
          },
        },
      });
    },
    onError: (error) => {
      handleTrpcError({ title: "Error Updating Post", error });
    },
  });

  const { labels, accounts } = post;

  const { status } = post;

  const statusOptions = POST_STATUSES.filter(
    (postStatus) => postStatus !== status
  );

  const allApprovers = post.approvals;
  const approvedApprovals = allApprovers.filter(
    (approval) => approval.approvedAt !== null
  );

  const hasPendingApprovals = approvedApprovals.length !== allApprovers.length;
  const blockingApprovals = allApprovers.filter(
    (approval) => approval.isBlocking
  );
  const pendingBlockingApprovals = blockingApprovals.filter(
    (approval) => approval.approvedAt === null
  );

  const avatarWidth = 24;
  const maxAvatars = Math.floor(width / avatarWidth);
  const areAvatarsOverflowing = post.accounts.length > maxAvatars;

  return (
    <motion.div layoutId={size === "normal" ? `post-${post.id}` : undefined}>
      <ContextMenu.Root>
        <ContextMenu.Trigger>
          <Link
            // @ts-expect-error
            ref={isDragDisabled ? null : dragRef}
            href={`/posts/${post.id}`}
            onClick={async (e) => {
              e.stopPropagation();
              await sleep(250);
              toast.dismiss(post.id);
            }}
            onMouseEnter={() => {
              trpcUtils.post.get.prefetch({ id: post.id });
            }}
            className={classNames(
              "bg-surface hover:bg-lightest-gray-30 block overflow-hidden rounded-lg border-[0.5px] text-ellipsis transition-all hover:shadow-sm",
              {
                "border-light-gray":
                  status !== "Scheduled" && status !== "Posted",
                "border-basic": status === "Scheduled",
                "border-accent-blue": status === "Posted",
                "text-medium-dark-gray": status === "Tentative",
                "opacity-40":
                  (hoveredCampaignID != null &&
                    post.campaignID !== hoveredCampaignID) ||
                  isDragging,
                "px-1.5 py-1.5 md:px-3 md:py-2.5": size === "normal",
                "px-1 py-1 md:px-2 md:py-1": size === "compact",
              }
            )}
          >
            <div key={post.id} className="flex flex-col gap-0.5">
              <div
                className={classNames("flex items-center gap-0.5", {
                  "flex-wrap": size === "normal",
                })}
              >
                <div className="bg-lightest-gray/50 flex h-4 shrink-0 items-center gap-1 rounded-xs px-1">
                  <Tooltip
                    className="px-3.5 py-3"
                    value={
                      size === "compact" ? (
                        <div className="flex items-center gap-2">
                          <StatusBadge kind="label" status={status} />
                          {post.publishAt && (
                            <div className="text-medium-dark-gray font-eyebrow-sm whitespace-nowrap">
                              {getPostPublishAtMoment(post).toFormat("h:mm a")}
                            </div>
                          )}
                        </div>
                      ) : null
                    }
                  >
                    <StatusBadge kind="icon" status={status} />
                  </Tooltip>
                  {post.publishAt && size === "normal" && (
                    <TimezoneTooltip date={new Date(post.publishAt)}>
                      <div
                        className={classNames(
                          "font-eyebrow-sm whitespace-nowrap",
                          {
                            "text-medium-dark-gray": status !== "Scheduled",
                            "text-basic": status === "Scheduled",
                          }
                        )}
                      >
                        {getPostPublishAtMoment(post).toFormat("h:mm a")}
                      </div>
                    </TimezoneTooltip>
                  )}
                </div>
                {hasPendingApprovals && (
                  <Tooltip
                    value={
                      <div className="font-body-sm flex flex-col gap-3">
                        <span className="font-body-sm font-medium">
                          Approvals
                        </span>
                        {allApprovers.map((approval) => (
                          <div
                            key={approval.id}
                            className="flex items-center gap-2"
                          >
                            <div className="relative">
                              <UserAvatar
                                user={approval.user}
                                backgroundColor="secondary"
                                showCheck={!!approval.approvedAt}
                              />
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="font-body-sm">
                                {getFullName(approval.user)}
                              </span>

                              {approval.isBlocking && !approval.approvedAt && (
                                <div className="bg-warning w-fit rounded-xs px-[3.75px] py-[3.19px] text-white">
                                  <ExclamationTriangleIcon className="h-4 w-4" />
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    }
                  >
                    <div
                      className={classNames(
                        "font-eyebrow-sm flex h-4 w-fit items-center gap-0.5 rounded-xs px-1",
                        {
                          "bg-lightest-gray-50 text-medium-dark-gray":
                            pendingBlockingApprovals.length === 0,
                          "bg-warning text-white":
                            pendingBlockingApprovals.length > 0,
                        }
                      )}
                    >
                      {pendingBlockingApprovals.length > 0 ? (
                        <ExclamationTriangleIcon className="-mb-px h-2.5 w-2.5" />
                      ) : (
                        <CheckBadgeIcon className="h-3 w-3" />
                      )}
                      {approvedApprovals.length}/{allApprovers.length}
                    </div>
                  </Tooltip>
                )}
                {size === "compact" && (
                  <PostTooltip post={post}>
                    <div className="flex w-full justify-between gap-1">
                      <div
                        className={cn(
                          "font-body-xs one-line-ellipses max-w-[120px]",
                          {
                            "text-medium-dark-gray": status === "Tentative",
                          }
                        )}
                      >
                        {post.title}
                      </div>
                      <AccountList
                        accounts={accounts}
                        numAccountsBeforeOverflow={3}
                        isIconOnly={true}
                      />
                    </div>
                  </PostTooltip>
                )}
                {size === "normal" &&
                  labels.map((label) => {
                    return (
                      <Badge
                        key={label.id}
                        color={label.color}
                        backgroundColor={label.backgroundColor}
                        kind="solid"
                        showHoverState={false}
                        label={label.name}
                        isUppercase={true}
                        size="sm"
                      />
                    );
                  })}
              </div>
            </div>
            {size === "normal" && (
              <PostTooltip post={post}>
                <div>
                  <div
                    className={cn(
                      "font-body-sm two-line-ellipses pb-1 text-black",
                      {
                        "text-medium-dark-gray": status === "Tentative",
                      }
                    )}
                  >
                    {post.title}
                  </div>
                  <div ref={ref} className="mt-1 flex items-center gap-2">
                    {post.accounts.slice(0, maxAvatars).map((account) => {
                      return (
                        <AccountChannelAvatar
                          key={account.id}
                          account={account}
                        />
                      );
                    })}
                    {areAvatarsOverflowing && (
                      <div className="text-medium-dark-gray font-body-sm">
                        +{post.accounts.length - maxAvatars}
                      </div>
                    )}
                  </div>
                </div>
              </PostTooltip>
            )}
          </Link>
        </ContextMenu.Trigger>
        <ContextMenu.Content>
          <ContextMenu.Sub>
            <ContextMenu.SubTrigger icon="CheckBadgeIcon">
              Change Status
            </ContextMenu.SubTrigger>
            <ContextMenu.Separator />
            <ContextMenu.SubContent className="w-fit">
              <ContextMenu.Item
                key="Idea"
                onClick={(e) => {
                  e.stopPropagation();
                  openConfirmationModal({
                    title: "Move post to Ideas?",
                    body: `Changing the status to "Idea" will move your post from the calendar to the Ideas tab`,
                    primaryButton: {
                      label: "Confirm",
                      onClick: () => {
                        convertToIdeaMutation.mutate({
                          id: post.id,
                        });
                      },
                    },
                  });
                }}
              >
                <StatusBadge kind="label" status="Idea" />
              </ContextMenu.Item>
              <Divider padding="xs" />
              {statusOptions.map((statusOption) => {
                return (
                  <div key={statusOption}>
                    {statusOption === "Scheduled" && <ContextMenu.Separator />}
                    <ContextMenu.Item
                      key={statusOption}
                      onClick={(e) => {
                        e.stopPropagation();
                        updatePost(post.id, { status: statusOption });
                      }}
                    >
                      <StatusBadge kind="label" status={statusOption} />
                    </ContextMenu.Item>
                  </div>
                );
              })}
            </ContextMenu.SubContent>
          </ContextMenu.Sub>
          <ContextMenu.Item
            icon="LinkIcon"
            intent="none"
            onClick={(e) => {
              e.stopPropagation();
              copy(`${process.env.NEXT_PUBLIC_DOMAIN}/posts/${post.id}`);
              toast.success(`Copied Link`, {
                description: "Post link is now saved to your clipboard",
              });
            }}
          >
            Copy Post Link
          </ContextMenu.Item>
          <ContextMenu.Item
            icon="DocumentDuplicateIcon"
            intent="none"
            onClick={async (e) => {
              e.stopPropagation();

              toast.promise(
                duplicatePostMutation.mutateAsync(
                  {
                    sourcePostID: post.id,
                  },
                  {
                    onSuccess: (data) => {
                      const newPost = assertPostOrIdea(data.post);
                      if (newPost.isIdea) {
                        throw new Error(
                          "Idea cannot be duplicated from calendar"
                        );
                      } else {
                        addNewPost({ ...newPost, approvals: [] });
                      }
                    },
                  }
                ),
                {
                  loading: "Duplicating Post",
                  success: () => {
                    return {
                      message: "Post Duplicated",
                      description: "Successfully duplicated post",
                    };
                  },
                  error: (error) => {
                    return {
                      message: "Error Duplicating Post",
                      description: error.message as string,
                    };
                  },
                }
              );
            }}
          >
            Duplicate Post
          </ContextMenu.Item>
          <ContextMenu.Item
            icon="DocumentDuplicateIcon"
            intent="none"
            onClick={(e) => {
              e.stopPropagation();
              setBulkDuplicateModalOpen(true);
            }}
          >
            Batch Duplicate Post
          </ContextMenu.Item>
          <ContextMenu.Item
            icon="TrashIcon"
            intent="danger"
            onClick={(e) => {
              e.stopPropagation();
              deletePost(post);
            }}
          >
            Delete Post
          </ContextMenu.Item>
        </ContextMenu.Content>
      </ContextMenu.Root>
      <BulkDuplicatePostModal
        open={bulkDuplicateModalOpen}
        setOpen={setBulkDuplicateModalOpen}
        onSuccess={(posts) => {
          posts.forEach((post) => {
            addNewPost({ ...post, approvals: [] });
          });
          toast.success(`${posts.length} Posts Created`);
        }}
        post={post}
      />
    </motion.div>
  );
};

const PostTooltip = ({
  post,
  children,
}: {
  post: {
    title: string;
    status: PostStatus;
    publishDate: Date;
    publishTime: Date | null;
    publishAt: Date | null;
    channels: Channel[];
    accounts: {
      id: string;
      name: string;
      profileImageUrl?: string;
      channel: Channel;
      type?: string;
    }[];
    labels: {
      id: string;
      name: string;
      color: string;
      backgroundColor: string;
      createdAt: Date;
    }[];
  };
  children: ReactElement<any, string | JSXElementConstructor<any>>;
}) => {
  return (
    <Tooltip
      value={
        <div>
          <div className="flex gap-1">
            <StatusBadge kind="label" status={post.status} />
            {post.publishAt && (
              <div className="text-medium-dark-gray font-eyebrow-sm whitespace-nowrap">
                {getPostPublishAtMoment(post).toFormat("h:mm a")}{" "}
                {DateTime.now().offsetNameShort}
              </div>
            )}
          </div>
          <div className="font-body-sm">{post.title}</div>
          <div
            className={classNames("flex items-center gap-1.5", {
              "mt-1 pb-1": post.labels.length > 0,
              "mt-0": post.labels.length === 0,
            })}
          >
            {post.labels.map((label) => {
              return (
                <Badge
                  key={label.id}
                  color={label.color}
                  backgroundColor={label.backgroundColor}
                  kind="solid"
                  showHoverState={false}
                  label={label.name}
                  isUppercase={true}
                  size="sm"
                />
              );
            })}
          </div>
          <div className="mt-2 flex flex-col items-start gap-2">
            {post.accounts.map((account) => {
              return (
                <div key={account.id} className="flex items-center gap-2">
                  <AccountChannelAvatar key={account.id} account={account} />
                  <div className="font-body-sm text-medium-dark-gray">
                    {account.name} {account.type ? `(${account.type})` : null}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      }
    >
      {children}
    </Tooltip>
  );
};

export default PostCalendarEvent;
