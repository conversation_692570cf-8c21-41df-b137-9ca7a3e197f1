import Uppy from "@uppy/core";
import { DateTime } from "luxon";
import { ReactNode } from "react";
import { useWorkspace } from "~/providers";
import { SlackIntegration } from "~/providers/PostIntegrationsProvider";
import { AssetType } from "~/types/Asset";
import { UploadingAsset } from "../../files/AssetsNewDesign";
import SlackAssetPreview from "./SlackAssetPreview";

type Props = {
  contentID: string;
  assets: AssetType[];
  setAssets: (
    value: AssetType[] | ((prev: AssetType[]) => AssetType[])
  ) => void;
  uploadingAssets: UploadingAsset[];
  draggedOver: boolean;
  setDraggedOver: (isDragged: boolean) => void;
  uppy: Uppy | null;
  getValidFiles: (files: File[]) => Promise<File[]>;
  isReadOnly: boolean;
  editor: ReactNode;
  connectedIntegration: SlackIntegration | null;
};

const SlackPostPreviewNewDesign = ({
  contentID,
  assets,
  setAssets,
  uploadingAssets,
  draggedOver,
  setDraggedOver,
  uppy,
  getValid<PERSON><PERSON>,
  is<PERSON><PERSON><PERSON><PERSON><PERSON>,
  editor,
  connectedIntegration,
}: Props) => {
  const { currentWorkspace } = useWorkspace();

  return (
    <div className="flex items-center justify-center">
      <div className="font-lato w-full space-y-4 rounded-lg bg-white text-[#1D1C1D]">
        <div className="font-smoothing flex gap-2 leading-4">
          <img
            className="h-10 w-10 rounded-[4px]"
            src={
              connectedIntegration?.account.profileImageUrl ||
              "/integrations/slack/default_user.png"
            }
          />
          <div className="flex w-full flex-col">
            <div className="flex items-baseline gap-1">
              <span className="font-semibold">
                {connectedIntegration?.account.displayName ||
                  currentWorkspace?.name}
              </span>
              <div
                key={DateTime.now().toFormat("h:mm a")}
                className="text-[12px] text-[#616061]"
              >
                {DateTime.now().toFormat("h:mm a")}
              </div>
            </div>
            <div className="leading-[22px]">{editor}</div>
            <SlackAssetPreview
              contentID={contentID}
              assets={assets}
              setAssets={setAssets}
              uploadingAssets={uploadingAssets}
              draggedOver={draggedOver}
              setDraggedOver={setDraggedOver}
              uppy={uppy}
              getValidFiles={getValidFiles}
              isReadOnly={isReadOnly}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SlackPostPreviewNewDesign;
