import { TRPCError } from "@trpc/server";
import catchZenstackDeleteError from "apiUtils/catchZenstackDeleteError";
import removeSubscriptionItem from "apiUtils/stripe/removeSubscriptionItem";
import { db } from "~/clients";
import { inngest } from "~/clients/inngest/inngest";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const disconnectWebflowAccount = async ({
  integrationID,
  workspaceID,
  userID,
}: {
  integrationID: string;
  workspaceID: string;
  userID: string;
}) => {
  const webflowIntegration = await db.webflowIntegration.findFirst({
    where: {
      id: integrationID,
      workspaceID,
    },
    select: {
      id: true,
      siteName: true,
      collectionName: true,
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
    },
  });

  if (!webflowIntegration) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "No integration found",
    });
  } else {
    const workspace = webflowIntegration.workspace;

    const postsToUpdate = await db.post.findMany({
      where: {
        webflowIntegrationID: webflowIntegration.id,
        scheduledPosts: {
          some: {
            status: "Scheduled",
            channel: "Webflow",
            publishAt: {
              gt: new Date(),
            },
          },
        },
      },
      select: {
        id: true,
        scheduledPosts: {
          where: {
            status: "Scheduled",
            channel: "Webflow",
            publishAt: {
              gt: new Date(),
            },
          },
          select: {
            id: true,
          },
        },
      },
    });
    const postIDs = postsToUpdate.map((post) => post.id);

    // Cancel all scheduled posts for just Webflow
    await db.scheduledPost.updateMany({
      where: {
        channel: "Webflow",
        status: "Scheduled",
        publishAt: {
          gt: new Date(),
        },
        post: {
          id: {
            in: postIDs,
          },
        },
      },
      data: {
        status: "Cancelled",
      },
    });

    await inngest.send(
      postsToUpdate.map((post) => ({
        name: "post.unscheduled",
        data: {
          postID: post.id,
          channel: "Webflow",
          scheduledPostID: post.scheduledPosts[0].id,
        },
      }))
    );

    const updatedPosts = await db.post.findMany({
      where: {
        id: {
          in: postIDs,
        },
      },
      select: {
        id: true,
        _count: {
          select: {
            scheduledPosts: {
              where: {
                status: "Scheduled",
              },
            },
          },
        },
      },
    });

    const postsWithoutScheduledPosts = updatedPosts.filter(
      (post) => post._count.scheduledPosts === 0
    );
    await db.post.updateMany({
      where: {
        id: {
          in: postsWithoutScheduledPosts.map((post) => post.id),
        },
      },
      data: {
        status: "Finalized",
      },
    });

    const postDefaults = await db.postDefault.findFirst({
      where: {
        workspaceID: workspaceID,
      },
      select: {
        webflowIntegrationID: true,
        workspace: {
          select: {
            webflowIntegrations: {
              where: {
                id: {
                  not: webflowIntegration.id,
                },
              },
              select: {
                id: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    if (postDefaults?.webflowIntegrationID === webflowIntegration.id) {
      const oldestwebflowIntegration =
        postDefaults.workspace.webflowIntegrations.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
        )[0];

      await db.postDefault.update({
        where: {
          workspaceID: workspaceID,
        },
        data: {
          webflowIntegrationID:
            oldestwebflowIntegration != null
              ? oldestwebflowIntegration.id
              : null,
        },
      });
    }

    await catchZenstackDeleteError(async () => {
      await db.webflowIntegration.update({
        where: {
          id: webflowIntegration.id,
        },
        data: {
          deletedTimeStamp: Math.floor(new Date().getTime() / 1000),
          deletedByID: userID,
        },
      });
    });

    await removeSubscriptionItem({
      workspaceID,
      itemName: "ADDITIONAL_SOCIAL_PROFILE",
      quantity: 1,
    });

    posthog.capture({
      distinctId: userID,
      event: POSTHOG_EVENTS.socialAccount.disconnected,
      properties: {
        channel: "Webflow",
      },
      groups: {
        workspace: workspaceID,
        company: workspace.companyID,
      },
    });

    return webflowIntegration;
  }
};

export default disconnectWebflowAccount;
