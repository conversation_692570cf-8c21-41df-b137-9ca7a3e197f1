import { DateTime } from "luxon";
import { useEffect, useState } from "react";

const DayHoliday = ({ day }: { day: Date }) => {
  const [holiday, setHoliday] = useState<{ name: string } | null>(null);

  useEffect(() => {
    import("date-holidays").then((Holidays) => {
      const USHolidays = new Holidays.default("US");

      // First convert the input day to local midnight in US Eastern time
      const dateInUS = DateTime.fromJSDate(day)
        .setZone("America/New_York", { keepLocalTime: true })
        .startOf("day");

      const isHoliday = USHolidays.isHoliday(dateInUS.toJSDate());
      const holidayResult = isHoliday ? isHoliday[0] : null;

      setHoliday(holidayResult);
    });
  }, [day]);

  if (holiday) {
    return (
      <div className="font-eyebrow-sm mb-0.5 text-ellipsis whitespace-nowrap">
        {holiday.name.replace("(substitute day)", "(substitute)")}
      </div>
    );
  } else {
    return null;
  }
};

export default DayHoliday;
