import {
  CheckCircleIcon,
  ChevronRightIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { usePrevious } from "@mantine/hooks";
import {
  WorkspaceOnboardingTaskState,
  WorkspaceOnboardingTaskType,
} from "@prisma/client";
import classNames from "classnames";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "~/components/HoverCard";
import Kbd from "~/components/Kbd";
import Accordion from "~/components/ui/Accordian";
import Button from "~/components/ui/Button";
import Progress from "~/components/ui/Progress";
import { trpc } from "~/utils";

interface Step {
  id: string;
  type: WorkspaceOnboardingTaskType;
  title: string;
  description: string;
  imageUrl: string;
  button?: {
    label: string;
    href: string;
  };
  state: WorkspaceOnboardingTaskState;
  isCompleted: boolean;
}

const CheckCircle = ({
  onClick,
}: {
  onClick?: (e: React.MouseEvent) => void;
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      stroke="#B9B9B9"
      strokeWidth="1.5"
      className="h-4 w-4 cursor-pointer"
      viewBox="0 0 24 24"
      onClick={onClick}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0z"
      />
    </svg>
  );
};

const StepCard = ({
  step,
  onMarkComplete,
  onUnmarkComplete,
}: {
  step: Step;
  onMarkComplete?: (e: React.MouseEvent) => void;
  onUnmarkComplete?: (e: React.MouseEvent) => void;
}) => {
  const description =
    step.type === WorkspaceOnboardingTaskType.CREATE_POST ? (
      <div className="text-medium-dark-gray flex flex-wrap items-center gap-1">
        Click anywhere on the Calendar or press <Kbd>P</Kbd> to create a post.
      </div>
    ) : (
      <div className="text-medium-dark-gray">{step.description}</div>
    );

  return (
    <>
      <div className="font-body-sm flex flex-col gap-0.5">
        <div className="flex items-center justify-between gap-1.5">
          <span className="font-medium">{step.title}</span>
          {step.isCompleted ? (
            <CheckCircleIcon
              className="text-medium-gray h-4 w-4 cursor-pointer"
              onClick={
                step.state === WorkspaceOnboardingTaskState.MARKED_COMPLETE
                  ? onUnmarkComplete
                  : undefined
              }
            />
          ) : (
            <CheckCircle onClick={onMarkComplete} />
          )}
        </div>
        {description}
      </div>
      <img src={step.imageUrl} alt={step.title} />
      {step.button && (
        <Link href={step.button.href}>
          <Button size="sm">{step.button.label}</Button>
        </Link>
      )}
    </>
  );
};

const GettingStartedCard = () => {
  const [hoveredStepID, setHoveredStepID] = useState<string | null>(null);
  const [selectedStepID, setSelectedStepID] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [isVisible, setIsVisible] = useState<boolean>(true);
  const [showCompletionMessage, setShowCompletionMessage] =
    useState<boolean>(false);
  const [initialDataLoaded, setInitialDataLoaded] = useState<boolean>(false);

  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const contentRef = useRef<HTMLDivElement | null>(null);

  const trpcUtils = trpc.useUtils();
  const { data: tasks, isLoading: tasksLoading } =
    trpc.onboarding.getTasks.useQuery();

  const { data: announcementData, isLoading: announcementLoading } =
    trpc.announcement.hasSeen.useQuery({
      type: "GettingStartedCard",
    });

  const markAsSeenMutation = trpc.announcement.markAsSeen.useMutation();

  const updateTaskMutation = trpc.onboarding.updateTask.useMutation({
    async onMutate({ taskType, state }) {
      await trpcUtils.onboarding.getTasks.cancel();
      const previousTasks = trpcUtils.onboarding.getTasks.getData();

      if (previousTasks) {
        trpcUtils.onboarding.getTasks.setData(
          undefined,
          previousTasks.map((task) =>
            task.type === taskType
              ? {
                  ...task,
                  state,
                  isCompleted:
                    state === WorkspaceOnboardingTaskState.COMPLETED ||
                    state === WorkspaceOnboardingTaskState.MARKED_COMPLETE,
                }
              : task
          )
        );
      }

      return { previousTasks };
    },
    onSettled() {
      trpcUtils.onboarding.getTasks.invalidate();
    },
  });

  const handleMarkComplete = (
    taskType: WorkspaceOnboardingTaskType,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();

    updateTaskMutation.mutate({
      taskType,
      state: WorkspaceOnboardingTaskState.MARKED_COMPLETE,
    });
  };

  const handleUnmarkComplete = (
    taskType: WorkspaceOnboardingTaskType,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();

    updateTaskMutation.mutate({
      taskType,
      state: WorkspaceOnboardingTaskState.ACTIVE,
    });
  };

  const handleStepClick = (stepID: string) => {
    if (selectedStepID === stepID) {
      setSelectedStepID(null);
    } else {
      setSelectedStepID(stepID);
      setHoveredStepID(null);
    }
  };

  const totalSteps = tasks?.length ?? 0;
  const completedSteps = tasks?.filter((step) => step.isCompleted).length ?? 0;
  const progressPercent = totalSteps
    ? Math.round((completedSteps / totalSteps) * 100)
    : 0;
  const isComplete = totalSteps === completedSteps && totalSteps > 0;
  const prevIsComplete = usePrevious(isComplete);
  const justCompleted =
    isComplete && prevIsComplete === false && initialDataLoaded;

  const handleAccordionChange = (value: string) => {
    const newExpandedState = value === "getting-started";
    setIsExpanded(newExpandedState);

    if (!newExpandedState) {
      markAsSeenMutation.mutate({
        type: "GettingStartedCard",
      });
    }
  };

  useEffect(() => {
    if (!tasksLoading && tasks && !initialDataLoaded && !announcementLoading) {
      setInitialDataLoaded(true);

      if (announcementData?.hasSeen) {
        setIsExpanded(false);
      } else {
        setIsExpanded(true);
      }
    }
  }, [tasksLoading, tasks, initialDataLoaded, announcementData]);

  useEffect(() => {
    if (justCompleted) {
      setShowCompletionMessage(true);
      setIsExpanded(false);

      setTimeout(() => {
        setIsVisible(false);
      }, 3000);
    }
  }, [justCompleted]);

  const handleMouseEnter = (stepID: string) => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    setHoveredStepID(stepID);
  };

  const handleMouseLeave = (stepID: string) => {
    // Delay the hover state change to ensure the hover card is visible
    hoverTimeoutRef.current = setTimeout(() => {
      if (hoveredStepID === stepID) {
        setHoveredStepID(null);
      }
    }, 200);
  };

  if (!isVisible || tasks?.length === 0 || tasksLoading || announcementLoading)
    return null;

  if (isComplete && !justCompleted && !showCompletionMessage) return null;

  return (
    <Accordion.Root
      type="single"
      collapsible={true}
      onValueChange={handleAccordionChange}
      className="relative rounded-[5px]"
      value={isExpanded ? "getting-started" : ""}
    >
      <Accordion.Item value="getting-started" className="border-none">
        <div
          className={classNames(
            "border-lightest-gray text-off-black flex flex-col rounded-[5px] border bg-white",
            {
              "w-[225px]": isExpanded,
            }
          )}
        >
          <Accordion.Trigger
            hideIcon={true}
            className={classNames(
              "flex w-full px-3 py-2.5 transition-all duration-300 hover:no-underline hover:shadow-lg",
              {
                "pt-5 pb-1.5 hover:shadow-none": isExpanded,
              }
            )}
          >
            <div className="w-full text-start">
              <div className="flex justify-between">
                <span className="font-body-xs text-medium-gray">
                  {progressPercent}% Completed
                </span>
                <XMarkIcon
                  className={classNames(
                    "text-medium-dark-gray hover:text-basic h-3 w-3 transition-colors",
                    {
                      hidden: !isExpanded,
                    }
                  )}
                />
              </div>
              <span className="font-body-sm">
                {isComplete || showCompletionMessage
                  ? "You've finished onboarding! 🎉"
                  : "Getting Started"}
              </span>
            </div>
          </Accordion.Trigger>
          <Accordion.Content className="px-3 pb-4">
            <Progress value={progressPercent} className="mb-1.5 h-0.5" />
            <ul className="font-body-sm flex w-full flex-col">
              {tasks?.map((step) => {
                const isHovered = hoveredStepID === step.id;
                const isSelected = selectedStepID === step.id;
                const isOpen =
                  isHovered || (isSelected && hoveredStepID === null);

                return (
                  <li
                    className={classNames(
                      "hovercard-container flex w-full items-center",
                      {
                        "text-medium-gray": step.isCompleted,
                        "text-off-black": !step.isCompleted,
                      }
                    )}
                    key={step.id}
                  >
                    <HoverCard open={isOpen}>
                      <div className="relative w-full">
                        <HoverCardTrigger
                          className="flex w-full cursor-pointer items-center justify-between py-[5px]"
                          onClick={() => handleStepClick(step.id)}
                          onMouseEnter={() => handleMouseEnter(step.id)}
                          onMouseLeave={() => handleMouseLeave(step.id)}
                        >
                          <div className="flex w-full items-center gap-1.5">
                            {step.isCompleted ? (
                              <CheckCircleIcon
                                className="h-4 w-4 cursor-pointer"
                                onClick={
                                  step.state ===
                                  WorkspaceOnboardingTaskState.MARKED_COMPLETE
                                    ? (e) => handleUnmarkComplete(step.type, e)
                                    : undefined
                                }
                              />
                            ) : (
                              <CheckCircle
                                onClick={(e) =>
                                  handleMarkComplete(step.type, e)
                                }
                              />
                            )}
                            <span
                              className={classNames({
                                "text-secondary": isOpen || isSelected,
                              })}
                            >
                              {step.title}
                            </span>
                          </div>
                          {(isOpen || isSelected) && (
                            <ChevronRightIcon className="h-4 w-4" />
                          )}

                          {isOpen && (
                            <div
                              className="absolute top-0 right-0 z-10 h-full w-[30px]"
                              style={{ left: "100%" }}
                              onMouseEnter={() => handleMouseEnter(step.id)}
                            />
                          )}
                        </HoverCardTrigger>
                      </div>
                      <HoverCardContent
                        align="start"
                        side="right"
                        sideOffset={20}
                        className="border-lightest-gray text-off-black flex max-w-[315px] flex-col gap-3 rounded-[5px] border bg-white px-4 py-5"
                        onMouseEnter={() => handleMouseEnter(step.id)}
                        onMouseLeave={() => handleMouseLeave(step.id)}
                        ref={contentRef}
                      >
                        <StepCard
                          step={step}
                          onMarkComplete={
                            !step.isCompleted
                              ? (event) => handleMarkComplete(step.type, event)
                              : undefined
                          }
                          onUnmarkComplete={
                            step.isCompleted &&
                            step.state ===
                              WorkspaceOnboardingTaskState.MARKED_COMPLETE
                              ? (event) =>
                                  handleUnmarkComplete(step.type, event)
                              : undefined
                          }
                        />
                      </HoverCardContent>
                    </HoverCard>
                  </li>
                );
              })}
            </ul>
          </Accordion.Content>
        </div>
      </Accordion.Item>
    </Accordion.Root>
  );
};

export default GettingStartedCard;
