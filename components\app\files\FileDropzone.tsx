import { PhotoIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import pluralize from "pluralize";
import { Accept, useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { UploadProgress } from "~/components/ui/AssetUploadContainer";
import { capitalizeWords } from "~/utils";

type Props = {
  onDrop: (files: File[]) => void;
  // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker#examples
  acceptedFileTypes: Accept;
  maxFiles?: number;
  label?: string;
  backgroundImage?: string;
  borderClassName?: string;
  uploadProgress?: number;
};

const FileDropzone = ({
  onDrop,
  acceptedFileTypes,
  maxFiles,
  label,
  backgroundImage,
  borderClassName = "rounded-lg",
  uploadProgress,
}: Props) => {
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptedFileTypes,
    maxFiles,
    onDropAccepted: onDrop,
    onDropRejected: (fileRejections) => {
      fileRejections.forEach((rejection) => {
        const errorCode = rejection.errors[0].code;
        if (errorCode === "too-many-files") {
          toast.error("Too many Files", {
            description: `${
              rejection.file.name
            } not uploaded. You can only upload ${maxFiles} ${pluralize(
              "file",
              maxFiles
            )}`,
          });
        } else if (errorCode === "file-invalid-type") {
          toast.error("Invalid File Type", {
            description: `${rejection.file.name} does not have an accepted file type`,
          });
        } else {
          toast.error("File Rejected", {
            description: `${rejection.file.name} was rejected ${capitalizeWords(
              errorCode
            )}`,
          });
        }
      });

      onDrop([]);
    },
  });

  return (
    <div
      {...getRootProps({
        className: "flex items-center justify-center w-full h-full group",
      })}
    >
      <label
        className={classNames(
          "relative flex h-full w-full cursor-pointer flex-col items-center justify-center border border-dashed transition-colors",
          borderClassName,
          {
            "border-light-gray hover:bg-lightest-gray-30 hover:border-basic bg-transparent":
              !isDragActive,
            "bg-lightest-gray-30 border-basic": isDragActive,
            "bg-slate": uploadProgress !== undefined,
          }
        )}
        style={
          backgroundImage && !isDragActive
            ? {
                backgroundImage: `url(${backgroundImage})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
              }
            : undefined
        }
      >
        {uploadProgress !== undefined && (
          <div className="absolute inset-x-1/2 z-10 h-10 w-10 -translate-x-1/2">
            <UploadProgress progress={uploadProgress} colorClass="text-white" />
          </div>
        )}
        <div
          className={classNames(
            "group-hover:text-basic font-body relative z-10 flex flex-col items-center justify-center gap-2 pt-5 pb-6 transition-colors",
            {
              "text-basic": isDragActive,
              "text-medium-dark-gray": !isDragActive,
              "group-hover:text-lightest-gray text-white": backgroundImage,
              "opacity-0": uploadProgress !== undefined,
            }
          )}
        >
          <PhotoIcon className="h-7 w-7" />
          <p className="font-body max-w-60 text-center font-medium">
            {label || "Drop media here to upload"}
          </p>
        </div>
      </label>
      <input {...getInputProps()} />
    </div>
  );
};

export default FileDropzone;
