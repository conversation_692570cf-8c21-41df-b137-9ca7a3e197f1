import { Channel, Prisma } from "@prisma/client";
import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import { db } from "~/clients";
import { inngest } from "~/clients/inngest/inngest";
import { cleanUnicodeCharacters } from "~/components/app/tiptap/utils/unicodeTextFormattingTools";
import { MAX_VIDEO_UPLOAD_SIZE_MB } from "~/constants";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import { formatTextAsTipTapEditorState } from "~/utils";
import escapeSpecialCharacters from "~/utils/linkedin/escapeSpecialCharacters";
import createRoomID from "~/utils/liveblocks/createRoomID";
import deleteAssetIfUnused from "./assets/deleteAssetIfUnused";
import deleteAssetsIfUnused from "./assets/deleteAssetsIfUnused";
import { copyStorageFiles } from "./copyStorageFiles";
import getStorageAssetsForDiscord from "./discord/getStorageAssetsForDiscord";
import getStorageAssetsForFacebook from "./facebook/getStorageAssetsForFacebook";
import getAssetsForDiscord from "./getAssetsForDiscord";
import getAssetsForFacebook from "./getAssetsForFacebook";
import getAssetsForInstagram from "./getAssetsForInstagram";
import getAssetsForLinkedIn from "./getAssetsForLinkedIn";
import getAssetsForSlack from "./getAssetsForSlack";
import getAssetsForThreads from "./getAssetsForThreads";
import getAssetsForTikTok from "./getAssetsForTikTok";
import getAssetsForTweets from "./getAssetsForTweets";
import getAssetsForYouTubeShorts from "./getAssetsForYouTubeShorts";
import getPostDefaults from "./getPostDefaults";
import getStorageAssetsForInstagram from "./instagram/getStorageAssetsForInstagram";
import getStorageAssetsForLinkedIn from "./linkedin/getStorageAssetsForLinkedIn";
import clearRoomContent from "./liveblocks/clearRoomContent";
import setRoomContent from "./liveblocks/setRoomContent";
import getStorageAssetsForSlack from "./slack/getStorageAssetsForSlack";
import getStorageAssetsForThreads from "./threads/getStorageAssetsForThreads";
import getStorageAssetsForTikTok from "./tiktok/getStorageAssetsForTikTok";
import getStorageAssetsForTwitter from "./twitter/getStorageAssetsForTwitter";
import getCodecs from "./video/getCodecs";
import getStorageAssetsForYouTubeShorts from "./youtube/getStorageAssetsForYouTubeShorts";

const maybeConvertVideoCodecs = async ({
  assets,
  channel,
  userID,
}: {
  assets: {
    id: string;
    mimetype: string;
    signedUrl: string;
  }[];
  channel: "Instagram" | "Threads" | "Twitter";
  userID: string;
}) => {
  const videoAssets = assets.filter((asset) =>
    asset.mimetype.includes("video")
  );

  if (videoAssets.length > 0) {
    const audioCodecPromises = videoAssets.map(async (asset) => {
      return await getCodecs(asset.signedUrl);
    });
    const codecs = await Promise.all(audioCodecPromises);

    let index = 0;
    for (const codec of codecs) {
      const video = videoAssets[index];
      const { audioCodec, videoCodec } = codec;
      if (channel === "Instagram" || channel === "Threads") {
        if (audioCodec != null && audioCodec !== "aac") {
          await inngest.send({
            name: "video.convertCodecs",
            data: {
              assetID: video.id,
              userID,
              audioCodec,
              videoCodec,
              channel,
            },
          });
        }
      } else if (channel === "Twitter") {
        if (videoCodec != null && videoCodec === "hevc") {
          await inngest.send({
            name: "video.convertCodecs",
            data: {
              assetID: video.id,
              userID,
              audioCodec,
              videoCodec,
              channel,
            },
          });
        }
      }
      index++;
    }
  }
};

const syncPostCopyAndAssets = async ({
  postID,
  workspaceID,
  sourceChannel,
  destinationCopyChannels,
  destinationAssetChannels,
  userID,
}: {
  postID: string;
  workspaceID: string;
  sourceChannel?: Channel;
  destinationCopyChannels: Channel[];
  destinationAssetChannels: Channel[];
  userID: string;
}) => {
  if (
    destinationAssetChannels.length === 0 &&
    destinationCopyChannels.length === 0
  ) {
    return;
  }

  const post = await db.post.findFirstOrThrow({
    where: {
      id: postID,
    },
    select: {
      id: true,
      workspaceID: true,
      channels: true,
      createdByID: true,
      tweets: {
        orderBy: {
          threadPosition: "asc",
        },
        select: {
          id: true,
          copy: true,
          threadPosition: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
              altText: true,
            },
          },
        },
      },
      linkedInContent: {
        select: {
          id: true,
          copy: true,
          coverPhoto: true,
          captions: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
      },
      discordContent: {
        select: {
          id: true,
          copy: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
      },
      threadsContent: {
        select: {
          id: true,
          copy: true,
          threadPosition: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
        orderBy: {
          threadPosition: "asc",
        },
      },
      slackContent: {
        select: {
          id: true,
          copy: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
      },
      tikTokContent: {
        select: {
          id: true,
          copy: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
      },
      instagramContent: {
        select: {
          id: true,
          copy: true,
          type: true,
          coverPhoto: true,
          assets: {
            orderBy: {
              position: "asc",
            },
            select: {
              id: true,
              fileName: true,
              asset: true,
              position: true,
              tags: true,
            },
          },
        },
      },
      facebookContent: {
        select: {
          id: true,
          copy: true,
          coverPhoto: true,
          assets: {
            orderBy: {
              position: "asc",
            },
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
      },
      youTubeShortsContent: {
        select: {
          id: true,
          title: true,
          description: true,
          asset: {
            select: {
              id: true,
              asset: true,
            },
          },
        },
      },
    },
  });

  if (post.workspaceID !== workspaceID) {
    throw new Error("You don't have access to this post");
  }

  const postDefaults = await getPostDefaults(workspaceID);

  sourceChannel = sourceChannel || post.channels[0];

  const isFromThreadedChannel = ["Twitter", "Threads"].includes(sourceChannel);

  let copy = "";
  switch (sourceChannel) {
    case "LinkedIn":
      copy = (post.linkedInContent?.copy || "")
        .replaceAll("\\|", "|")
        .replaceAll("\\{", "{")
        .replaceAll("\\}", "}")
        .replaceAll("\\>", ">")
        .replaceAll("\\<", "<")
        .replaceAll("\\*", "*")
        .replaceAll("\\~", "~")
        .replaceAll("\\_", "_")
        // Escape all @ signs that aren't for a tag
        .replaceAll(/\\@([^\[])/g, "@$1")
        // These are below the @ sign, since the regex expects @[ without any \
        .replaceAll("\\(", "(")
        .replaceAll("\\)", ")")
        .replaceAll("\\[", "[")
        .replaceAll("\\]", "]")
        // Since we've replaced all the parentheses, we need to replace the escaped
        // ones with the original if it's for a tag
        .replaceAll(/\@\[(.+?)\]\((.+?)\)/g, "$1");
      break;
    case "Instagram":
      copy = post.instagramContent?.copy || "";
      break;
    case "Facebook":
      copy = (post.facebookContent?.copy || "").replaceAll(
        /\@\[(.+?)\]\((.+?)\)/g,
        "$1"
      );
      break;
    case "Discord":
      copy = post.discordContent?.copy || "";
      break;
    case "Threads":
      copy = post.threadsContent
        .sort((a, b) => a.threadPosition - b.threadPosition)
        .map((content) => content.copy)
        .join("\n\n");
      break;
    case "Twitter":
      copy = post.tweets
        .sort((a, b) => a.threadPosition - b.threadPosition)
        .map((tweet) => tweet.copy)
        .join("\n\n");
      break;
    case "TikTok":
      copy = post.tikTokContent?.copy || "";
      break;
    case "Slack":
      copy = post.slackContent?.copy || "";
      break;
    case "YouTubeShorts":
      copy =
        post.youTubeShortsContent?.description ||
        post.youTubeShortsContent?.title ||
        "";
      break;
  }

  const normalizedCopy = cleanUnicodeCharacters(copy);

  let instagramContent = post.instagramContent;
  let linkedInContent = post.linkedInContent;
  let facebookContent = post.facebookContent;
  let tikTokContent = post.tikTokContent;
  let tweetsContent = post.tweets;
  let threadsContent = post.threadsContent;
  let discordContent = post.discordContent;
  let slackContent = post.slackContent;
  let youTubeShortsContent = post.youTubeShortsContent;

  // Copy copy from source to destination
  for (const channel of destinationCopyChannels) {
    switch (channel) {
      case "LinkedIn":
        const escapedCopy = escapeSpecialCharacters(copy);

        linkedInContent = await db.linkedInContent.upsert({
          where: {
            postID: post.id,
          },
          create: {
            copy: escapedCopy,
            editorState: formatTextAsTipTapEditorState(copy),
            postID: post.id,
          },
          update: {
            copy: escapedCopy,
            editorState: formatTextAsTipTapEditorState(copy),
          },
          select: {
            id: true,
            copy: true,
            coverPhoto: true,
            captions: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
            },
          },
        });

        await setRoomContent({
          roomID: createRoomID({
            workspaceID,
            contentType: "post",
            postID: post.id,
            roomContentID: linkedInContent.id,
          }),
          content: formatTextAsTipTapEditorState(copy),
          userID,
        });
        break;
      case "Instagram":
        const fromVideoChannel =
          sourceChannel === "TikTok" || sourceChannel === "YouTubeShorts";

        instagramContent = await db.instagramContent.upsert({
          where: {
            postID: post.id,
          },
          create: {
            copy,
            editorState: formatTextAsTipTapEditorState(copy),
            type: fromVideoChannel ? "Reel" : "Feed",
            postID: post.id,
            reelShareToFeed: postDefaults.instagramReelShareToFeed,
            locationID: postDefaults.instagramLocation?.id,
          },
          update: {
            copy,
            editorState: formatTextAsTipTapEditorState(copy),
          },
          include: {
            coverPhoto: true,
            assets: {
              include: {
                asset: true,
                tags: true,
              },
            },
          },
        });

        await setRoomContent({
          roomID: createRoomID({
            workspaceID,
            contentType: "post",
            postID: post.id,
            roomContentID: instagramContent.id,
          }),
          content: formatTextAsTipTapEditorState(copy),
          userID,
        });
        break;
      case "Facebook":
        facebookContent = await db.facebookContent.upsert({
          where: {
            postID: post.id,
          },
          create: {
            copy,
            editorState: formatTextAsTipTapEditorState(copy),
            postID: post.id,
          },
          update: {
            copy,
            editorState: formatTextAsTipTapEditorState(copy),
          },
          select: {
            id: true,
            copy: true,
            coverPhoto: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
            },
          },
        });

        await setRoomContent({
          roomID: createRoomID({
            workspaceID,
            contentType: "post",
            postID: post.id,
            roomContentID: facebookContent.id,
          }),
          content: formatTextAsTipTapEditorState(copy),
          userID,
        });
        break;
      case "Discord":
        discordContent = await db.discordContent.upsert({
          where: {
            postID: post.id,
          },
          create: {
            copy: normalizedCopy,
            editorState: formatTextAsTipTapEditorState(normalizedCopy),
            postID: post.id,
          },
          update: {
            copy: normalizedCopy,
            editorState: formatTextAsTipTapEditorState(normalizedCopy),
          },
          select: {
            id: true,
            copy: true,
            editorState: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
            },
          },
        });

        await setRoomContent({
          roomID: createRoomID({
            workspaceID,
            contentType: "post",
            postID: post.id,
            roomContentID: discordContent.id,
          }),
          content: formatTextAsTipTapEditorState(normalizedCopy),
          userID,
        });
        break;
      case "Threads":
        if (isFromThreadedChannel) {
          for await (const tweet of tweetsContent) {
            const thread = post.threadsContent.find(
              (content) => content.threadPosition === tweet.threadPosition
            );

            if (thread) {
              await db.threadsContent.update({
                where: {
                  id: thread.id,
                },
                data: {
                  copy: tweet.copy,
                  editorState: formatTextAsTipTapEditorState(tweet.copy),
                },
              });
            } else {
              await db.threadsContent.create({
                data: {
                  postID: post.id,
                  copy: tweet.copy,
                  editorState: formatTextAsTipTapEditorState(tweet.copy),
                  threadPosition: tweet.threadPosition,
                },
              });
            }
          }

          const highestTweetPosition = Math.max(
            ...tweetsContent.map((content) => content.threadPosition)
          );

          await db.threadsContent.deleteMany({
            where: {
              postID: post.id,
              threadPosition: {
                gte: highestTweetPosition + 1,
              },
            },
          });
        } else {
          const firstThread = post.threadsContent.find(
            (content) => content.threadPosition === 0
          );

          // 4+ new lines will trigger the tweet split and
          // end up with a race condition with the last part duplicated
          copy = copy.replace(/\n{4,}/g, "\n\n\n");

          if (firstThread) {
            await db.threadsContent.update({
              where: {
                id: firstThread.id,
              },
              data: {
                copy,
                editorState: formatTextAsTipTapEditorState(copy),
              },
            });
          } else {
            await db.threadsContent.create({
              data: {
                postID: post.id,
                copy,
                editorState: formatTextAsTipTapEditorState(copy),
                threadPosition: 0,
              },
            });
          }

          await db.threadsContent.deleteMany({
            where: {
              postID: post.id,
              threadPosition: {
                gte: 1,
              },
            },
          });
        }

        threadsContent = await db.threadsContent.findMany({
          where: {
            postID: post.id,
          },
          orderBy: {
            threadPosition: "asc",
          },
          select: {
            id: true,
            copy: true,
            threadPosition: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
              orderBy: {
                position: "asc",
              },
            },
          },
        });
        break;
      case "Twitter":
        if (isFromThreadedChannel) {
          for await (const thread of threadsContent) {
            const tweet = tweetsContent.find(
              (content) => content.threadPosition === thread.threadPosition
            );

            if (tweet) {
              await db.tweetContent.update({
                where: {
                  id: tweet.id,
                },
                data: {
                  copy: thread.copy,
                  editorState: formatTextAsTipTapEditorState(thread.copy),
                },
              });
            } else {
              await db.tweetContent.create({
                data: {
                  postID: post.id,
                  copy: thread.copy,
                  editorState: formatTextAsTipTapEditorState(thread.copy),
                  threadPosition: thread.threadPosition,
                },
              });
            }
          }

          const highestThreadPosition = Math.max(
            ...threadsContent.map((content) => content.threadPosition)
          );

          await db.tweetContent.deleteMany({
            where: {
              postID: post.id,
              threadPosition: {
                gte: highestThreadPosition + 1,
              },
            },
          });
        } else {
          const firstTweet = tweetsContent.find(
            (tweet) => tweet.threadPosition === 0
          );

          // 4+ new lines will trigger the tweet split and
          // end up with a race condition with the last part duplicated
          copy = copy.replace(/\n{4,}/g, "\n\n\n");

          if (firstTweet) {
            await db.tweetContent.update({
              where: {
                id: firstTweet.id,
              },
              data: {
                copy: copy,
                editorState: formatTextAsTipTapEditorState(copy),
              },
            });
          } else {
            await db.tweetContent.create({
              data: {
                postID: post.id,
                copy: copy,
                editorState: formatTextAsTipTapEditorState(copy),
                threadPosition: 0,
              },
            });
          }

          await db.tweetContent.deleteMany({
            where: {
              postID: post.id,
              threadPosition: {
                gte: 1,
              },
            },
          });
        }

        tweetsContent = await db.tweetContent.findMany({
          where: {
            postID: post.id,
          },
          orderBy: {
            threadPosition: "asc",
          },
          select: {
            id: true,
            copy: true,
            threadPosition: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
                altText: true,
              },
            },
          },
        });

        break;
      case "TikTok":
        tikTokContent = await db.tikTokContent.upsert({
          where: {
            postID: post.id,
          },
          create: {
            copy,
            editorState: formatTextAsTipTapEditorState(copy),
            postID: post.id,
          },
          update: {
            copy,
            editorState: formatTextAsTipTapEditorState(copy),
          },
          select: {
            id: true,
            copy: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
            },
          },
        });

        await setRoomContent({
          roomID: createRoomID({
            workspaceID,
            contentType: "post",
            postID: post.id,
            roomContentID: tikTokContent.id,
          }),
          content: formatTextAsTipTapEditorState(copy),
          userID,
        });
        break;
      case "Slack":
        slackContent = await db.slackContent.upsert({
          where: {
            postID: post.id,
          },
          create: {
            copy: normalizedCopy,
            editorState: formatTextAsTipTapEditorState(normalizedCopy),
            postID: post.id,
          },
          update: {
            copy: normalizedCopy,
            editorState: formatTextAsTipTapEditorState(normalizedCopy),
          },
          select: {
            id: true,
            copy: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
            },
          },
        });

        await setRoomContent({
          roomID: createRoomID({
            workspaceID,
            contentType: "post",
            postID: post.id,
            roomContentID: slackContent.id,
          }),
          content: formatTextAsTipTapEditorState(normalizedCopy),
          userID,
        });
        break;
      case "YouTubeShorts":
        const title = copy.length <= 100 ? copy : undefined;
        const description = copy.length > 100 ? copy : undefined;

        youTubeShortsContent = await db.youTubeShortsContent.upsert({
          where: {
            postID: post.id,
          },
          create: {
            title,
            titleEditorState: title
              ? formatTextAsTipTapEditorState(title)
              : Prisma.JsonNull,
            description,
            descriptionEditorState: description
              ? formatTextAsTipTapEditorState(description)
              : Prisma.JsonNull,
            postID: post.id,
            notifySubscribers: postDefaults.youTubeShortsNotifySubscribers,
          },
          update: {
            title,
            titleEditorState: title
              ? formatTextAsTipTapEditorState(title)
              : Prisma.JsonNull,
            description,
            descriptionEditorState: description
              ? formatTextAsTipTapEditorState(description)
              : Prisma.JsonNull,
          },
          select: {
            id: true,
            title: true,
            description: true,
            asset: {
              select: {
                id: true,
                asset: true,
              },
            },
          },
        });

        const roomTitleContent = {
          roomID: createRoomID({
            workspaceID,
            contentType: "post",
            postID: post.id,
            roomContentID: youTubeShortsContent.id,
            extraInformation: "title",
          }),
          content: formatTextAsTipTapEditorState(title),
          userID,
        };

        const roomDescriptionContent = {
          roomID: createRoomID({
            workspaceID,
            contentType: "post",
            postID: post.id,
            roomContentID: youTubeShortsContent.id,
            extraInformation: "description",
          }),
          content: formatTextAsTipTapEditorState(description),
          userID,
        };

        if (title) {
          await setRoomContent(roomTitleContent);
        } else {
          await clearRoomContent(roomTitleContent);
        }

        if (description) {
          await setRoomContent(roomDescriptionContent);
        } else {
          await clearRoomContent(roomDescriptionContent);
        }

        break;
    }
  }

  let sourceBasePath: null | string = null;
  let assets: AssetType[] = [];

  // Only copy assets if there are any channels to copy to
  if (destinationAssetChannels.length > 0) {
    switch (sourceChannel) {
      case "LinkedIn":
        sourceBasePath = `${post.workspaceID}/${post.id}/linkedin/${post.linkedInContent?.id}`;
        const linkedInAssetResponse = await getAssetsForLinkedIn(post);
        assets = linkedInAssetResponse.assets;
        break;
      case "Instagram":
        sourceBasePath = `${post.workspaceID}/${post.id}/instagram/${post.instagramContent?.id}`;
        const instagramAssets = getAssetsForInstagram(post);
        assets = instagramAssets.assets;
        break;
      case "Facebook":
        sourceBasePath = `${post.workspaceID}/${post.id}/facebook/${post.facebookContent?.id}`;
        const facebookAssetResponse = await getAssetsForFacebook(post);
        assets = facebookAssetResponse.assets;
        break;
      case "Discord":
        sourceBasePath = `${post.workspaceID}/${post.id}/discord/${post.discordContent?.id}`;
        assets = await getAssetsForDiscord(post);
        break;
      case "Threads":
        sourceBasePath = `${post.workspaceID}/${post.id}/threads`;
        const threadsAssets = getAssetsForThreads(post);
        sortBy(threadsContent, (content) => content.threadPosition).forEach(
          (content) => {
            assets.push(...threadsAssets[content.id]);
          }
        );
        assets = Object.values(threadsAssets).flat();
        break;
      case "Twitter":
        sourceBasePath = `${post.workspaceID}/${post.id}/tweets`;
        const twitterAssets = getAssetsForTweets(post);
        sortBy(tweetsContent, (tweet) => tweet.threadPosition).forEach(
          (tweet) => {
            assets.push(...twitterAssets[tweet.id]);
          }
        );
        assets = Object.values(twitterAssets).flat();
        break;
      case "TikTok":
        sourceBasePath = `${post.workspaceID}/${post.id}/tiktok/${post.tikTokContent?.id}`;
        assets = getAssetsForTikTok(post);
        break;
      case "Slack":
        sourceBasePath = `${post.workspaceID}/${post.id}/slack/${post.slackContent?.id}`;
        assets = getAssetsForSlack(post);
        break;
      case "YouTubeShorts":
        sourceBasePath = `${post.workspaceID}/${post.id}/youtube_shorts/${post.youTubeShortsContent?.id}`;
        const { asset: youTubeShortsAsset } = getAssetsForYouTubeShorts(post);
        assets = youTubeShortsAsset ? [youTubeShortsAsset] : [];
        break;
      default:
        console.log("No source channel found", sourceChannel);
    }

    const findMatchingAsset = (fileName: string) => {
      return assets.find((asset) => asset.name === fileName);
    };

    if (sourceBasePath && assets.length > 0) {
      const firstAsset = assets[0];
      const firstAssetIsGif = firstAsset.metadata.mimetype === "image/gif";
      const firstAssetIsImage =
        firstAsset.metadata.mimetype.includes("image") && !firstAssetIsGif;
      const firstAssetIsVideo = firstAsset.metadata.mimetype.includes("video");

      // Copy assets from source to destination
      for (const channel of destinationAssetChannels) {
        switch (channel) {
          case "LinkedIn":
            if (linkedInContent) {
              const linkedInAssetIDs = linkedInContent.assets.map(
                (asset) => asset.asset.id
              );
              await db.linkedInAsset.deleteMany({
                where: {
                  linkedInContentID: linkedInContent.id,
                },
              });

              await deleteAssetsIfUnused(linkedInAssetIDs);

              const slug = CHANNEL_ICON_MAP["LinkedIn"].slug;

              if (firstAssetIsVideo || firstAssetIsGif) {
                await copyStorageFiles({
                  sourceBasePath,
                  destinationBasePath: `${post.workspaceID}/${post.id}/${slug}/${linkedInContent.id}`,
                  userID,
                  maxFiles: 1,
                  acceptedMimetypes: {
                    "video/mp4": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    },
                    "video/quicktime": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    },
                    "image/gif": {
                      maxSizeMb: 15,
                    },
                  },
                });
              } else {
                await copyStorageFiles({
                  sourceBasePath,
                  destinationBasePath: `${post.workspaceID}/${post.id}/${slug}/${linkedInContent.id}`,
                  userID,
                  maxFiles: 9,
                  acceptedMimetypes: {
                    "image/jpeg": {
                      maxSizeMb: 5,
                    },
                    "image/png": {
                      maxSizeMb: 5,
                    },
                  },
                });
              }

              const linkedInAssets = await getStorageAssetsForLinkedIn({
                ...post,
                linkedInContent,
              });

              await db.asset.createMany({
                data: linkedInAssets.assets.map((asset) => {
                  const matchingAsset = findMatchingAsset(asset.name);

                  return {
                    id: asset.id,
                    name: asset.name,
                    bucket: "assets",
                    path: asset.path,
                    url: asset.signedUrl,
                    urlExpiresAt: DateTime.now().plus({ year: 1 }).toJSDate(),
                    width: matchingAsset?.width,
                    height: matchingAsset?.height,
                    duration: matchingAsset?.duration,
                    md5Hash: matchingAsset?.md5Hash,
                    mimetype: asset.mimetype,
                    size: asset.sizeBytes,
                    eTag: asset.eTag,
                    workspaceID: post.workspaceID,
                    userID: post.createdByID,
                  };
                }),
                skipDuplicates: true,
              });

              await db.linkedInAsset.createMany({
                data: linkedInAssets.assets.map((asset) => {
                  const originalAssetIndex = assets.findIndex(
                    (originalAsset) => originalAsset.name === asset.name
                  );

                  return {
                    assetID: asset.id,
                    position: originalAssetIndex,
                    linkedInContentID: linkedInContent!.id,
                  };
                }),
              });

              console.log(
                `Created ${linkedInAssets.assets.length} linkedIn assets`
              );
            }
            break;
          case "Instagram":
            if (instagramContent) {
              const instagramAssetIDs = instagramContent.assets.map(
                (asset) => asset.asset.id
              );

              await db.instagramAsset.deleteMany({
                where: {
                  instagramContentID: instagramContent.id,
                },
              });

              await deleteAssetsIfUnused(instagramAssetIDs);

              // If the first asset is a video, we'll only copy one asset and make it a reel
              if (firstAssetIsVideo && assets.length === 1) {
                await copyStorageFiles({
                  sourceBasePath,
                  destinationBasePath: `${post.workspaceID}/${post.id}/instagram/${instagramContent.id}`,
                  userID,
                  acceptedMimetypes: {
                    "video/mp4": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                      minDurationSec: 3,
                      maxDurationSec: 60 * 15,
                    },
                    "video/quicktime": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                      minDurationSec: 3,
                      maxDurationSec: 60 * 15,
                    },
                  },
                });

                if (instagramContent.type !== "Reel") {
                  await db.instagramContent.update({
                    where: {
                      id: instagramContent.id,
                    },
                    data: {
                      type: "Reel",
                    },
                  });
                }
              } else {
                await copyStorageFiles({
                  sourceBasePath,
                  destinationBasePath: `${post.workspaceID}/${post.id}/instagram/${instagramContent.id}`,
                  userID,
                  maxFiles: 10,
                  copySourceFolderStructure: sourceChannel !== "Twitter",
                  acceptedMimetypes: {
                    "image/jpeg": {
                      maxSizeMb: 8,
                    },
                    "image/png": {
                      maxSizeMb: 8,
                    },
                    "video/mp4": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                      minDurationSec: 3,
                      maxDurationSec: 60,
                    },
                    "video/quicktime": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                      minDurationSec: 3,
                      maxDurationSec: 60,
                    },
                  },
                });

                if (instagramContent.type !== "Feed") {
                  await db.instagramContent.update({
                    where: {
                      id: instagramContent.id,
                    },
                    data: {
                      type: "Feed",
                    },
                  });
                }
              }

              const igAssets = await getStorageAssetsForInstagram({
                ...post,
                instagramContent,
              });

              await db.asset.createMany({
                data: igAssets.assets.map((asset) => {
                  const matchingAsset = findMatchingAsset(asset.name);

                  return {
                    id: asset.id,
                    name: asset.name,
                    bucket: "assets",
                    path: asset.path,
                    url: asset.signedUrl,
                    urlExpiresAt: DateTime.now().plus({ year: 1 }).toJSDate(),
                    width: matchingAsset?.width,
                    height: matchingAsset?.height,
                    duration: matchingAsset?.duration,
                    md5Hash: matchingAsset?.md5Hash,
                    mimetype: asset.mimetype,
                    size: asset.sizeBytes,
                    eTag: asset.eTag,
                    workspaceID: post.workspaceID,
                    userID: post.createdByID,
                  };
                }),
                skipDuplicates: true,
              });

              const assetData = igAssets.assets.map((asset, index) => {
                const originalAssetIndex = assets.findIndex(
                  (originalAsset) => originalAsset.name === asset.name
                );
                return {
                  assetID: asset.id,
                  position: originalAssetIndex,
                  fileName: asset.name,
                  mimetype: asset.metadata.mimetype,
                  instagramContentID: instagramContent!.id,
                };
              });

              await db.instagramAsset.createMany({
                data: assetData,
              });

              console.log(`Created ${igAssets.assets.length} instagram assets`);

              await maybeConvertVideoCodecs({
                assets: igAssets.assets,
                channel: "Instagram",
                userID: post.createdByID,
              });
            }

            break;
          case "Discord":
            if (discordContent) {
              const discordAssetIDs = discordContent.assets.map(
                (asset) => asset.asset.id
              );
              await db.discordAsset.deleteMany({
                where: {
                  discordContentID: discordContent.id,
                },
              });

              await deleteAssetsIfUnused(discordAssetIDs);

              await copyStorageFiles({
                sourceBasePath,
                destinationBasePath: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Discord"].slug}/${discordContent.id}`,
                userID,
                maxFiles: 3,
                acceptedMimetypes: {
                  "image/jpeg": {
                    maxSizeMb: 10,
                  },
                  "image/png": {
                    maxSizeMb: 10,
                  },
                  "image/gif": {
                    maxSizeMb: 10,
                  },
                  "video/mp4": {
                    maxSizeMb: 10,
                  },
                  "video/quicktime": {
                    maxSizeMb: 10,
                  },
                },
              });

              const discordAssets = await getStorageAssetsForDiscord({
                ...post,
                discordContent,
              });

              await db.asset.createMany({
                data: discordAssets.map((asset) => {
                  const matchingAsset = findMatchingAsset(asset.name);

                  return {
                    id: asset.id,
                    name: asset.name,
                    bucket: "assets",
                    path: asset.path,
                    url: asset.signedUrl,
                    urlExpiresAt: DateTime.now().plus({ year: 1 }).toJSDate(),
                    width: matchingAsset?.width,
                    height: matchingAsset?.height,
                    duration: matchingAsset?.duration,
                    md5Hash: matchingAsset?.md5Hash,
                    mimetype: asset.mimetype,
                    size: asset.sizeBytes,
                    eTag: asset.eTag,
                    workspaceID: post.workspaceID,
                    userID: post.createdByID,
                  };
                }),
                skipDuplicates: true,
              });

              await db.discordAsset.createMany({
                data: discordAssets.map((asset, index) => {
                  const originalAssetIndex = assets.findIndex(
                    (originalAsset) => originalAsset.name === asset.name
                  );
                  return {
                    assetID: asset.id,
                    position: originalAssetIndex,
                    discordContentID: discordContent!.id,
                  };
                }),
              });
            }

            break;
          case "Facebook":
            if (facebookContent) {
              const facebookAssetIDs = facebookContent.assets.map(
                (asset) => asset.asset.id
              );
              await db.facebookAsset.deleteMany({
                where: {
                  facebookContentID: facebookContent.id,
                },
              });

              await deleteAssetsIfUnused(facebookAssetIDs);

              await copyStorageFiles({
                sourceBasePath,
                destinationBasePath: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Facebook"].slug}/${facebookContent.id}`,
                userID,
                maxFiles: firstAssetIsVideo ? 1 : 10,
                copySourceFolderStructure: sourceChannel !== "Twitter",
                acceptedMimetypes: firstAssetIsVideo
                  ? {
                      "video/mp4": {
                        maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                        maxDurationSec: 90,
                      },
                      "video/quicktime": {
                        maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                        maxDurationSec: 90,
                      },
                    }
                  : {
                      "image/jpeg": {
                        maxSizeMb: 4,
                      },
                      "image/png": {
                        maxSizeMb: 4,
                      },
                    },
              });

              const facebookAssets = await getStorageAssetsForFacebook({
                ...post,
                facebookContent,
              });

              await db.asset.createMany({
                data: facebookAssets.assets.map((asset) => {
                  const matchingAsset = findMatchingAsset(asset.name);

                  return {
                    id: asset.id,
                    name: asset.name,
                    bucket: "assets",
                    path: asset.path,
                    url: asset.signedUrl,
                    urlExpiresAt: DateTime.now().plus({ year: 1 }).toJSDate(),
                    width: matchingAsset?.width,
                    height: matchingAsset?.height,
                    duration: matchingAsset?.duration,
                    md5Hash: matchingAsset?.md5Hash,
                    mimetype: asset.mimetype,
                    size: asset.sizeBytes,
                    eTag: asset.eTag,
                    workspaceID: post.workspaceID,
                    userID: post.createdByID,
                  };
                }),
                skipDuplicates: true,
              });

              await db.facebookAsset.createMany({
                data: facebookAssets.assets.map((asset) => {
                  const originalAssetIndex = assets.findIndex(
                    (originalAsset) => originalAsset.name === asset.name
                  );
                  return {
                    fileName: asset.name,
                    mimetype: asset.mimetype,
                    assetID: asset.id,
                    position: originalAssetIndex,
                    facebookContentID: facebookContent!.id,
                  };
                }),
              });

              const fbCoverPhoto = facebookAssets.coverPhoto;
              if (fbCoverPhoto) {
                await db.facebookContent.update({
                  where: {
                    id: facebookContent.id,
                  },
                  data: {
                    coverPhoto: {
                      create: {
                        id: fbCoverPhoto.id,
                        name: fbCoverPhoto.name,
                        bucket: "assets",
                        path: fbCoverPhoto.path,
                        eTag: fbCoverPhoto.eTag,
                        mimetype: fbCoverPhoto.mimetype,
                        size: fbCoverPhoto.sizeBytes,
                        url: fbCoverPhoto.url,
                        // width: sourcePost.facebookContent.coverPhoto?.width,
                        // height: sourcePost.facebookContent.coverPhoto?.height,
                        urlExpiresAt: fbCoverPhoto.urlExpiresAt,
                        workspaceID: post.workspaceID,
                        userID: post.createdByID,
                      },
                    },
                  },
                });
              }
            }

            break;
          case "Threads":
            const threadsSlug = CHANNEL_ICON_MAP["Threads"].slug;

            const threadsAssetIDs = threadsContent.flatMap((thread) =>
              thread.assets.map((asset) => asset.asset.id)
            );

            await db.threadsAsset.deleteMany({
              where: {
                threadsContentID: {
                  in: threadsContent.map((thread) => thread.id),
                },
              },
            });

            await deleteAssetsIfUnused(threadsAssetIDs);

            if (isFromThreadedChannel) {
              const promises = tweetsContent.map(async (tweet) => {
                const thread = threadsContent.find(
                  (thread) => thread.threadPosition === tweet.threadPosition
                );

                await copyStorageFiles({
                  sourceBasePath: `${sourceBasePath}/${tweet.id}`,
                  destinationBasePath: `${post.workspaceID}/${
                    post.id
                  }/${threadsSlug}/${thread!.id}`,
                  userID,
                  copySubdirectories: false,
                  maxFiles: 20,
                  acceptedMimetypes: {
                    "image/jpeg": {
                      maxSizeMb: 8,
                    },
                    "image/png": {
                      maxSizeMb: 8,
                    },
                    "video/mp4": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    },
                    "video/quicktime": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    },
                  },
                });
              });

              await Promise.all(promises);
            } else {
              await copyStorageFiles({
                sourceBasePath,
                destinationBasePath: `${post.workspaceID}/${post.id}/${threadsSlug}/${threadsContent[0].id}`,
                userID,
                copySubdirectories: false,
                maxFiles: 20,
                acceptedMimetypes: {
                  "image/jpeg": {
                    maxSizeMb: 8,
                  },
                  "image/png": {
                    maxSizeMb: 8,
                  },
                  "video/mp4": {
                    maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                  },
                  "video/quicktime": {
                    maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                  },
                },
              });
            }

            const threadsAssets = await getStorageAssetsForThreads({
              ...post,
              threadsContent,
            });

            const allThreadsAssets = Object.values(threadsAssets).flat();

            await db.asset.createMany({
              data: allThreadsAssets.map((asset) => {
                const matchingAsset = findMatchingAsset(asset.name);

                return {
                  id: asset.id,
                  name: asset.name,
                  bucket: "assets",
                  path: asset.path,
                  url: asset.signedUrl,
                  urlExpiresAt: DateTime.now().plus({ year: 1 }).toJSDate(),
                  width: matchingAsset?.width,
                  height: matchingAsset?.height,
                  duration: matchingAsset?.duration,
                  md5Hash: matchingAsset?.md5Hash,
                  mimetype: asset.mimetype,
                  size: asset.sizeBytes,
                  eTag: asset.eTag,
                  workspaceID: post.workspaceID,
                  userID: post.createdByID,
                };
              }),
              skipDuplicates: true,
            });

            await db.threadsAsset.createMany({
              data: Object.entries(threadsAssets).flatMap(
                ([threadID, assets]) =>
                  assets.map((asset) => {
                    const originalAssetIndex = assets.findIndex(
                      (originalAsset) => originalAsset.name === asset.name
                    );
                    return {
                      assetID: asset.id,
                      position: originalAssetIndex,
                      threadsContentID: threadID,
                    };
                  })
              ),
            });

            // If Instagram is already in the destination it'll convert for both channel
            if (!destinationAssetChannels.includes("Instagram")) {
              await maybeConvertVideoCodecs({
                assets: allThreadsAssets,
                channel: "Threads",
                userID: post.createdByID,
              });
            }

            break;
          case "Twitter":
            const slug = CHANNEL_ICON_MAP["Twitter"].slug;

            const tweetAssetIDs = tweetsContent.flatMap((tweet) =>
              tweet.assets.map((asset) => asset.asset.id)
            );

            await db.tweetAsset.deleteMany({
              where: {
                tweetContentID: {
                  in: tweetsContent.map((tweet) => tweet.id),
                },
              },
            });

            await deleteAssetsIfUnused(tweetAssetIDs);

            if (isFromThreadedChannel) {
              const promises = threadsContent.map(async (thread) => {
                const tweet = tweetsContent.find(
                  (tweet) => tweet.threadPosition === thread.threadPosition
                );

                const firstThreadAsset = thread.assets[0]?.asset;
                const firstThreadAssetIsImage =
                  firstThreadAsset?.mimetype.startsWith("image/") &&
                  firstThreadAsset?.mimetype !== "image/gif";

                await copyStorageFiles({
                  sourceBasePath: `${sourceBasePath}/${thread.id}`,
                  destinationBasePath: `${post.workspaceID}/${
                    post.id
                  }/${slug}/${tweet!.id}`,
                  userID,
                  copySubdirectories: false,
                  maxFiles: firstThreadAssetIsImage ? 4 : 1,
                  acceptedMimetypes: {
                    "image/jpeg": {
                      maxSizeMb: 5,
                    },
                    "image/png": {
                      maxSizeMb: 5,
                    },
                    "image/gif": {
                      maxSizeMb: 15,
                    },
                    "video/mp4": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                      maxDurationSec: 140,
                      minDimensions: {
                        aspectRatio: 1 / 3,
                      },
                      maxDimensions: {
                        aspectRatio: 3 / 1,
                      },
                    },
                    "video/quicktime": {
                      maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                      maxDurationSec: 140,
                      minDimensions: {
                        aspectRatio: 1 / 3,
                      },
                      maxDimensions: {
                        aspectRatio: 3 / 1,
                      },
                    },
                  },
                });
              });

              await Promise.all(promises);
            } else {
              await copyStorageFiles({
                sourceBasePath,
                destinationBasePath: `${post.workspaceID}/${post.id}/${slug}/${tweetsContent[0].id}`,
                userID,
                copySubdirectories: false,
                maxFiles: firstAssetIsImage ? 4 : 1,
                acceptedMimetypes: {
                  "image/jpeg": {
                    maxSizeMb: 5,
                  },
                  "image/png": {
                    maxSizeMb: 5,
                  },
                  "image/gif": {
                    maxSizeMb: 15,
                  },
                  "video/mp4": {
                    maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    maxDurationSec: 60 * 10,
                    minDimensions: {
                      aspectRatio: 1 / 3,
                    },
                    maxDimensions: {
                      aspectRatio: 3 / 1,
                    },
                  },
                  "video/quicktime": {
                    maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    maxDurationSec: 60 * 10,
                    minDimensions: {
                      aspectRatio: 1 / 3,
                    },
                    maxDimensions: {
                      aspectRatio: 3 / 1,
                    },
                  },
                },
              });
            }

            const twitterAssets = await getStorageAssetsForTwitter({
              ...post,
              tweets: tweetsContent,
            });

            const allTwitterAssets = Object.values(twitterAssets).flat();

            await db.asset.createMany({
              data: allTwitterAssets.map((asset) => {
                const matchingAsset = findMatchingAsset(asset.name);

                return {
                  id: asset.id,
                  name: asset.name,
                  bucket: "assets",
                  path: asset.path,
                  url: asset.signedUrl,
                  urlExpiresAt: DateTime.now().plus({ year: 1 }).toJSDate(),
                  width: matchingAsset?.width,
                  height: matchingAsset?.height,
                  duration: matchingAsset?.duration,
                  md5Hash: matchingAsset?.md5Hash,
                  mimetype: asset.mimetype,
                  size: asset.sizeBytes,
                  eTag: asset.eTag,
                  workspaceID: post.workspaceID,
                  userID: post.createdByID,
                };
              }),
              skipDuplicates: true,
            });

            await db.tweetAsset.createMany({
              data: Object.entries(twitterAssets).flatMap(([tweetID, assets]) =>
                assets.map((asset) => {
                  const originalAssetIndex = assets.findIndex(
                    (originalAsset) => originalAsset.name === asset.name
                  );
                  return {
                    assetID: asset.id,
                    position: originalAssetIndex,
                    tweetContentID: tweetID,
                  };
                })
              ),
            });

            await maybeConvertVideoCodecs({
              assets: allTwitterAssets,
              channel: "Twitter",
              userID: post.createdByID,
            });

            break;
          case "TikTok":
            if (tikTokContent) {
              const tikTokAssetIDs = tikTokContent.assets.map(
                (asset) => asset.asset.id
              );
              await db.tikTokAsset.deleteMany({
                where: {
                  tikTokContentID: tikTokContent.id,
                },
              });

              await deleteAssetsIfUnused(tikTokAssetIDs);

              await copyStorageFiles({
                sourceBasePath,
                destinationBasePath: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["TikTok"].slug}/${tikTokContent.id}`,
                userID,
                maxFiles: 1,
                acceptedMimetypes: {
                  "video/mp4": {
                    maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    maxDurationSec: 60 * 3,
                  },
                  "video/quicktime": {
                    maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    maxDurationSec: 60 * 3,
                  },
                },
              });

              const tikTokAssets = await getStorageAssetsForTikTok({
                ...post,
                tikTokContent,
              });

              await db.asset.createMany({
                data: tikTokAssets.map((asset) => {
                  const matchingAsset = findMatchingAsset(asset.name);

                  return {
                    id: asset.id,
                    name: asset.name,
                    bucket: "assets",
                    path: asset.path,
                    url: asset.signedUrl,
                    urlExpiresAt: DateTime.now().plus({ year: 1 }).toJSDate(),
                    width: matchingAsset?.width,
                    height: matchingAsset?.height,
                    duration: matchingAsset?.duration,
                    md5Hash: matchingAsset?.md5Hash,
                    mimetype: asset.mimetype,
                    size: asset.sizeBytes,
                    eTag: asset.eTag,
                    workspaceID: post.workspaceID,
                    userID: post.createdByID,
                  };
                }),
                skipDuplicates: true,
              });

              await db.tikTokAsset.createMany({
                data: tikTokAssets.map((asset, index) => ({
                  assetID: asset.id,
                  position: index,
                  tikTokContentID: tikTokContent!.id,
                })),
              });
            }

            break;
          case "YouTubeShorts":
            if (youTubeShortsContent) {
              const youTubeShortsAssetID = youTubeShortsContent.asset?.asset.id;

              if (youTubeShortsAssetID) {
                await db.youTubeShortsAsset.deleteMany({
                  where: {
                    youTubeShortsContentID: youTubeShortsContent.id,
                  },
                });

                await deleteAssetIfUnused(youTubeShortsAssetID);
              }

              await copyStorageFiles({
                sourceBasePath,
                destinationBasePath: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["YouTubeShorts"].slug}/${youTubeShortsContent.id}`,
                userID,
                maxFiles: 1,
                acceptedMimetypes: {
                  "video/mp4": {
                    maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    maxDurationSec: 3 * 60,
                  },
                  "video/quicktime": {
                    maxSizeMb: MAX_VIDEO_UPLOAD_SIZE_MB,
                    maxDurationSec: 3 * 60,
                  },
                },
              });

              const { asset: youTubeShortsAsset } =
                await getStorageAssetsForYouTubeShorts({
                  ...post,
                  youTubeShortsContent,
                });

              if (youTubeShortsAsset) {
                await db.asset.create({
                  data: {
                    id: youTubeShortsAsset.id,
                    name: youTubeShortsAsset.name,
                    bucket: "assets",
                    path: youTubeShortsAsset.path,
                    url: youTubeShortsAsset.signedUrl,
                    urlExpiresAt: DateTime.now().plus({ year: 1 }).toJSDate(),
                    mimetype: youTubeShortsAsset.mimetype,
                    size: youTubeShortsAsset.sizeBytes,
                    eTag: youTubeShortsAsset.eTag,
                    workspaceID: post.workspaceID,
                    userID: post.createdByID,
                  },
                });

                await db.youTubeShortsAsset.create({
                  data: {
                    assetID: youTubeShortsAsset.id,
                    youTubeShortsContentID: youTubeShortsContent.id,
                  },
                });
              }
            }

            break;
          case "Slack":
            if (slackContent) {
              const slackAssetIDs = slackContent.assets.map(
                (asset) => asset.asset.id
              );
              await db.slackAsset.deleteMany({
                where: {
                  slackContentID: slackContent.id,
                },
              });

              await deleteAssetsIfUnused(slackAssetIDs);

              await copyStorageFiles({
                sourceBasePath,
                destinationBasePath: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Slack"].slug}/${slackContent.id}`,
                userID,
                maxFiles: 1,
                acceptedMimetypes: {
                  "image/jpeg": {
                    maxSizeMb: 8,
                  },
                  "image/png": {
                    maxSizeMb: 8,
                  },
                  "image/gif": {
                    maxSizeMb: 8,
                  },
                  "video/mp4": {
                    maxSizeMb: 100,
                  },
                  "video/quicktime": {
                    maxSizeMb: 100,
                  },
                },
              });

              const slackAssets = await getStorageAssetsForSlack({
                ...post,
                slackContent,
              });

              await db.asset.createMany({
                data: slackAssets.map((asset) => {
                  const matchingAsset = findMatchingAsset(asset.name);

                  return {
                    id: asset.id,
                    name: asset.name,
                    bucket: "assets",
                    path: asset.path,
                    url: asset.signedUrl,
                    urlExpiresAt: DateTime.now().plus({ year: 1 }).toJSDate(),
                    width: matchingAsset?.width,
                    height: matchingAsset?.height,
                    duration: matchingAsset?.duration,
                    md5Hash: matchingAsset?.md5Hash,
                    mimetype: asset.mimetype,
                    size: asset.sizeBytes,
                    eTag: asset.eTag,
                    workspaceID: post.workspaceID,
                    userID: post.createdByID,
                  };
                }),
                skipDuplicates: true,
              });

              await db.slackAsset.createMany({
                data: slackAssets.map((asset, index) => ({
                  assetID: asset.id,
                  position: index,
                  slackContentID: slackContent!.id,
                })),
              });
            }
            break;
        }
      }
    }
  }
};

export default syncPostCopyAndAssets;
