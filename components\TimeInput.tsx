// Based off of https://github.com/dima-bu/react-time-input

import classNames from "classnames";
import { DateTime } from "luxon";
import posthog from "posthog-js";
import { ReactElement, useEffect, useRef, useState } from "react";
import Popover from "~/components/ui/Popover";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

import { getTimezoneName } from "~/utils";

export type SuggestedTimes = {
  label: string;
} & (
  | {
      times: Date[];
      howToComponent?: undefined;
    }
  | {
      times?: [];
      howToComponent: ReactElement;
    }
);

type Props = {
  value: Date | null;
  onChange: (date: Date | null) => void;
  disabled?: boolean;
  mountFocus?: boolean;
  acceptedMinuteMultiple?: number;
  showBorder?: boolean;
  placeholder?: string;
  suggestedTimes?: SuggestedTimes[];
  autofocus?: boolean;
  displayStyle?: "underline" | "border";
};

const TimeInput = ({
  value,
  onChange,
  disabled,
  mountFocus,
  acceptedMinuteMultiple,
  showBorder = true,
  placeholder = "HH:MM",
  suggestedTimes,
  autofocus,
  displayStyle = "underline",
}: Props) => {
  const [showSuggestedTimes, setShowSuggestedTimes] = useState(false);

  const [time, setTime] = useState(
    value ? DateTime.fromJSDate(value).toFormat("hh:mm") : ""
  );

  const [ampm, setAmPm] = useState<"AM" | "PM">(
    value ? (DateTime.fromJSDate(value).toFormat("a") as "AM" | "PM") : "PM"
  );

  useEffect(() => {
    if (value) {
      setTime(DateTime.fromJSDate(value).toFormat("hh:mm"));
      setAmPm(DateTime.fromJSDate(value).toFormat("a") as "AM" | "PM");
    } else {
      setTime("");
    }
  }, [value]);

  const _input = useRef<HTMLInputElement>(null);

  const isValid = (val: string) => {
    const regexp = /^\d{0,2}?\:?\d{0,2}$/;

    const [hoursStr, minutesStr] = val.split(":");

    if (!regexp.test(val)) {
      return false;
    }

    const hours = Number(hoursStr);
    const minutes = Number(minutesStr);

    // Hours valid up to 15 cause we can then split up in to 01:5
    const isValidHour = (hour: number) =>
      Number.isInteger(hour) && hour >= 0 && hour <= 15;
    const isValidMinutes = (minutes: number) =>
      (Number.isInteger(minutes) && hours >= 0 && hours < 24) ||
      Number.isNaN(minutes);

    if (!isValidHour(hours) || !isValidMinutes(minutes)) {
      return false;
    }

    if (minutes < 10 && Number(minutesStr[0]) > 5) {
      return false;
    }

    const valArr = val.indexOf(":") !== -1 ? val.split(":") : [val];

    // check mm and HH
    if (
      valArr[0] &&
      valArr[0].length &&
      (parseInt(valArr[0], 10) < 0 || parseInt(valArr[0], 10) > 23)
    ) {
      return false;
    }

    if (
      valArr[1] &&
      valArr[1].length &&
      (parseInt(valArr[1], 10) < 0 || parseInt(valArr[1], 10) > 59)
    ) {
      return false;
    }

    return true;
  };

  useEffect(() => {
    if (!disabled && mountFocus) {
      setTimeout(() => {
        _input.current?.focus();
      }, 0);
    }
  });

  useEffect(() => {
    if (autofocus) {
      setTimeout(() => {
        _input.current?.focus();
      }, 0);
    }
  }, [autofocus]);

  let lastVal = "";

  const handleChange = (val: string) => {
    if (val == time) {
      return;
    }
    if (isValid(val)) {
      const numVal = Number(val);
      if (val.length === 1 && numVal > 1) {
        val = "0" + val;

        if (numVal >= 1 && numVal <= 6) {
          setAmPm("PM");
        } else if (numVal >= 7 && numVal <= 9) {
          setAmPm("AM");
        }
      }

      if (val.length === 2 && numVal > 12) {
        val = `0${val[0]}:${val[1]}`;
      }

      if (val.length === 2 && lastVal.length !== 3 && val.indexOf(":") === -1) {
        val = val + ":";

        if (numVal >= 10 && numVal < 12) {
          setAmPm("AM");
        }
      }

      if (val.length === 2 && lastVal.length === 3) {
        val = val.slice(0, 1);
      }

      if (acceptedMinuteMultiple && val.length === 5) {
        const [hours, minutes] = val.split(":");

        const isMultiple = Number(minutes) % acceptedMinuteMultiple === 0;
        if (!isMultiple) {
          const multiple =
            Math.round(Number(minutes) / acceptedMinuteMultiple) *
            acceptedMinuteMultiple;

          val = `${hours}:${
            multiple.toString().length === 1 ? `0${multiple}` : multiple
          }`;
        }
      }

      if (val.length > 5) {
        onChange(null);
        return false;
      }

      lastVal = val;

      setTime(val);

      if (val.length === 0) {
        onChange(null);
      }
    }
  };

  const handleSaveTime = (time: string, newAmPm?: "AM" | "PM") => {
    const [hourStr, minuteStr] = time.split(":");
    const hour = Number(hourStr);
    const minute = Number(minuteStr);

    const timeAmPm = newAmPm || ampm;

    const date = new Date();
    date.setHours(
      (timeAmPm === "AM" && hour < 12) || (timeAmPm === "PM" && hour === 12)
        ? hour
        : (12 + hour) % 24,
      minute
    );

    onChange(date);
  };

  const handleInputBlur = () => {
    if (time.length === 1) {
      const numberTime = Number(time);
      if (numberTime !== 0) {
        const newTime = `0${time}:00`;
        setTime(newTime);
        handleSaveTime(newTime);
      }
    } else if (time.length === 2) {
      const numberTime = Number(time);
      if (numberTime > 0 && numberTime <= 12) {
        const newTime = `${time}:00`;
        setTime(newTime);
        handleSaveTime(newTime);
      }
    } else if (time.length === 3) {
      const numberTime = Number(time.slice(0, 2));
      if (numberTime > 0 && numberTime <= 12) {
        const newTime = `${time}00`;
        setTime(newTime);
        handleSaveTime(newTime);
      }
    } else if (time.length === 4) {
      const hourTimeRegex = /\d:\d\d/;
      if (hourTimeRegex.test(time)) {
        const newTime = `0${time}`;
        if (isValid(newTime)) {
          if (acceptedMinuteMultiple) {
            const [hours, minutes] = newTime.split(":");

            let roundedTime = newTime;
            const isMultiple = Number(minutes) % acceptedMinuteMultiple === 0;
            if (!isMultiple) {
              roundedTime = `${hours}:${
                Math.round(Number(minutes) / acceptedMinuteMultiple) *
                acceptedMinuteMultiple
              }`;
            }
            setTime(roundedTime);
            handleSaveTime(roundedTime);
          } else {
            setTime(newTime);
            handleSaveTime(newTime);
          }
        }
      }
    } else if (time.length !== 5 || !isValid(time)) {
      setTime("");
    } else {
      handleSaveTime(time);
    }
  };

  const handleAmPmBlur = (ampm?: "AM" | "PM") => {
    if (!isValid(time)) {
      setTime("");
    } else {
      handleSaveTime(time, ampm);
    }
  };

  return (
    <div
      className={classNames(
        "group w-content font-eyebrow flex justify-between whitespace-nowrap transition-colors",
        {
          "focus-within:border-secondary mt-1 ml-0.5 border-b pb-1":
            displayStyle === "underline",
          "hover:border-basic rounded-full border-[0.25px] px-3 py-0.5":
            displayStyle === "border",
          "border-light-gray": showBorder && displayStyle === "underline",
          "border-surface": !showBorder && displayStyle === "underline",
          "hover:border-light-gray": !disabled && displayStyle === "underline",
        }
      )}
    >
      <div
        onBlur={(e) => {
          const currentTarget = e.currentTarget;

          requestAnimationFrame(() => {
            const activeElementID = document.activeElement?.id;

            // Check if the new focused element is a child of the original container
            if (
              !currentTarget.contains(document.activeElement) &&
              activeElementID !== "radix-:r1n:" &&
              !activeElementID?.includes("time-shortcut")
            ) {
              setShowSuggestedTimes(false);
            }

            if (activeElementID?.includes("time-shortcut")) {
              // Set focus to the input
              _input.current?.focus();
            }
          });
        }}
      >
        <Popover.Root open={showSuggestedTimes}>
          <Popover.Anchor asChild={true}>
            <input
              ref={_input}
              className="peer w-9 outline-hidden disabled:bg-white"
              disabled={disabled}
              placeholder={disabled ? "" : placeholder}
              onFocus={() => {
                if (suggestedTimes) {
                  // If some suggested times are available and the input is focused, show the popover
                  const hasAvailableSuggestedTimes = suggestedTimes.some(
                    (suggestedTime) => (suggestedTime.times?.length || 0) > 0
                  );

                  setShowSuggestedTimes(hasAvailableSuggestedTimes);
                }
              }}
              value={time}
              onKeyUp={(event) => {
                if (event.key === "Enter") {
                  handleInputBlur();
                  event.currentTarget.blur();
                }
              }}
              onBlur={() => {
                handleInputBlur();
              }}
              onChange={(e) => handleChange(e.target.value)}
            />
          </Popover.Anchor>
          <Popover.Content sideOffset={10}>
            <div>
              {suggestedTimes
                ?.filter(
                  (suggestedTime) =>
                    (suggestedTime.times?.length || 0) > 0 ||
                    suggestedTime.howToComponent
                )
                .map((suggestedTime, index) => {
                  if (suggestedTime.howToComponent) {
                    return (
                      <div
                        key="how-to"
                        className={classNames({
                          "border-light-gray mb-2 max-w-[170px] border-b pb-1":
                            index !== suggestedTimes.length - 1,
                        })}
                      >
                        <div className="font-eyebrow-sm text-medium-dark-gray mb-1.5">
                          {suggestedTime.label}
                        </div>
                        {suggestedTime.howToComponent}
                      </div>
                    );
                  } else {
                    return (
                      <div
                        key={suggestedTime.label}
                        className={classNames({
                          "border-light-gray mb-2 border-b pb-1":
                            index !== suggestedTimes.length - 1,
                        })}
                      >
                        <div className="font-eyebrow-sm text-medium-dark-gray mb-1.5">
                          {suggestedTime.label}
                        </div>
                        <div className="font-body-sm flex flex-col items-start gap-1">
                          {suggestedTime.times.map((time) => {
                            const dateTime = DateTime.local().set({
                              hour: time.getHours(),
                              minute: time.getMinutes(),
                              second: time.getSeconds(),
                            });

                            return (
                              <button
                                id={`time-shortcut-${DateTime.fromJSDate(
                                  time
                                ).toFormat("hh:mm a")}`}
                                key={`time-shortcut-${DateTime.fromJSDate(
                                  time
                                ).toFormat("hh:mm a")}`}
                                className="hover:bg-slate rounded-md p-1 transition-colors"
                                onClick={() => {
                                  posthog.capture(
                                    POSTHOG_EVENTS.timeSuggestion.picked,
                                    {
                                      type: suggestedTime.label,
                                      time: dateTime.toLocaleString(
                                        DateTime.TIME_SIMPLE
                                      ),
                                    }
                                  );

                                  const time = dateTime.toFormat("hh:mm");
                                  const ampm = dateTime.toFormat("a") as
                                    | "AM"
                                    | "PM";

                                  setTime(time);
                                  setAmPm(ampm);
                                  handleSaveTime(time, ampm);
                                  setShowSuggestedTimes(false);
                                }}
                              >
                                {dateTime.toFormat("h:mma ZZZZ")}
                              </button>
                            );
                          })}
                        </div>
                      </div>
                    );
                  }
                })}
            </div>
          </Popover.Content>
        </Popover.Root>
        <button
          type="button"
          className={classNames(
            "peer-focus:bg-secondary hover:bg-secondary/80 mr-1 ml-0.5 rounded-md px-1 text-inherit transition-all peer-focus:text-white hover:text-white",
            { "hidden opacity-0": time.length === 0 },
            { "opacity-100": time.length > 0 }
          )}
          onClick={() => {
            const newAmPm = ampm === "AM" ? "PM" : "AM";
            if (time.length === 5 && isValid(time)) {
              handleAmPmBlur(newAmPm);
            }
            setAmPm(newAmPm);
          }}
          onKeyUp={(event) => {
            if (event.key === "Enter") {
              handleAmPmBlur();
              event.currentTarget.blur();
            }
          }}
          onBlur={() => handleAmPmBlur()}
        >
          {ampm}
        </button>
      </div>
      <div
        className={classNames(
          "text-medium-gray whitespace-nowrap opacity-0 transition-opacity",
          {
            "group-hover:opacity-100":
              !disabled && displayStyle === "underline",
            "w-0 group-hover:opacity-0":
              time.length === 0 && displayStyle === "border",
            "opacity-100": time.length > 0,
          }
        )}
      >
        {getTimezoneName()}
      </div>
    </div>
  );
};

export default TimeInput;
