import { StatusCodes } from "http-status-codes";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";
import { removeEmptyElems } from "~/utils";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.findMany({
    where: {
      linkedInLink: {
        not: null,
      },
      linkedInPostID: null,
    },
    select: {
      id: true,
      linkedInLink: true,
      workspaceID: true,
    },
  });

  // https://www.linkedin.com/feed/update/urn:li:ugcPost:7165374615161143296
  const urns = removeEmptyElems(
    posts.map((post) => {
      if (!post.linkedInLink) {
        return null;
      } else {
        return post.linkedInLink.split("/").pop();
      }
    })
  );

  const linkedInPosts = await db.linkedInPost.findMany({
    where: {
      id: {
        in: urns,
      },
    },
    select: {
      id: true,
    },
  });

  const foundPosts = posts.filter((post) => {
    return linkedInPosts.some((linkedInPost) => {
      return (
        post.linkedInLink &&
        linkedInPost.id === post.linkedInLink.split("/").pop()
      );
    });
  });

  for await (const post of foundPosts) {
    const linkedInPostID = post.linkedInLink?.split("/").pop();
    console.log(post.id, linkedInPostID);
    await db.post.update({
      where: {
        id: post.id,
      },
      data: {
        linkedInPostID,
      },
    });
  }

  res.status(StatusCodes.OK).send({
    numPosts: foundPosts.length,
  });
};

export default handler;
