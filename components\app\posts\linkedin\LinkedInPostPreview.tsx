import { useState } from "react";

import { useElementSize } from "@mantine/hooks";
import classNames from "classnames";
import uniq from "lodash/uniq";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import AnimateChangeInHeight from "~/components/AnimateChangeInHeight";
import { linkedinMentionRegex, urlRegex } from "~/constants";
import { useWorkspace } from "~/providers";
import { getDefaultProfilePicture, removeEmptyElems, trpc } from "~/utils";
import LinkPreview from "../LinkPreview";
import LinkedInAssetPreview from "./LinkedInAssetPreview";
import { LinkedInIntegration } from "./LinkedInPost";

type Props = {
  linkedInContent: {
    copy?: string;
    assets: {
      name: string;
      signedUrl: string;
      width?: number | null;
      height?: number | null;
      metadata: {
        mimetype: string;
      };
      createdAt: Date;
    }[];
    documentTitle: string | null;
  } | null;
  connectedAccount: LinkedInIntegration | null | undefined;
  coverPhotoUrl?: string;
  captionsUrl?: string;
  isLoadingAssets?: boolean;
  maxHeight?: number | "full";
  defaultShowMore?: boolean;
};

const LinkedInPostPreview = ({
  linkedInContent,
  connectedAccount,
  coverPhotoUrl,
  captionsUrl,
  maxHeight = 600,
  defaultShowMore = false,
}: Props) => {
  const { ref, height } = useElementSize();

  const urns = linkedInContent?.copy?.match(/urn:li:\w+:[\w-_]+/g) || [];

  const getAccountUrlsQuery =
    trpc.integration.linkedin.getAccountUrlsByUrn.useQuery(
      {
        urns,
      },
      {
        refetchOnWindowFocus: false,
        refetchInterval: false,
      }
    );

  const urnToUrls = getAccountUrlsQuery.data?.urnToUrls || {};
  const [showMore, setShowMore] = useState<boolean>(defaultShowMore);
  const { currentWorkspace } = useWorkspace();
  const copyWithoutMentions = linkedInContent?.copy?.replaceAll(
    linkedinMentionRegex,
    ""
  );
  const websites = uniq(copyWithoutMentions?.match(urlRegex)) || [];

  const firstWebsite = websites.length ? websites[0] : null;

  const asset = linkedInContent?.assets?.[0];

  const nonEmpty =
    (linkedInContent && linkedInContent.copy && linkedInContent.copy != "") ||
    asset;

  const copy =
    linkedInContent?.copy &&
    firstWebsite &&
    asset == null &&
    linkedInContent.copy?.endsWith(firstWebsite)
      ? linkedInContent.copy.slice(0, -firstWebsite.length).trimEnd()
      : linkedInContent?.copy;

  if (!nonEmpty) {
    return (
      <div className="font-body text-medium-dark-gray mt-1">
        Preview will show when post content is added below
      </div>
    );
  } else {
    return (
      <div
        className="flex min-h-[300px] items-center justify-center"
        style={{ fontFamily: "system-ui" }}
      >
        <div
          className={classNames(
            "scrollbar-hidden relative h-fit w-[540px] space-y-4 overflow-auto rounded-lg bg-white py-3",
            {
              [`max-h-[${maxHeight}px]`]: typeof maxHeight === "number",
              "max-h-full": maxHeight === "full",
            }
          )}
        >
          <div className="font-smoothing flex items-start gap-2 px-4 leading-4">
            <img
              className="h-12 w-12 rounded-full"
              src={
                connectedAccount?.account.profileImageUrl ??
                getDefaultProfilePicture("LinkedIn")
              }
            />
            <div className="mt-2 flex flex-col items-start">
              <span className="text-[14px] font-semibold">
                {connectedAccount?.account.name || currentWorkspace?.name}
              </span>
              <span className="text-[12px] text-black/60">Now</span>
            </div>
          </div>
          <div className="space-y-2 text-[14px] leading-5">
            <AnimateChangeInHeight>
              <div
                ref={ref}
                className={classNames("relative overflow-clip px-4", {
                  "max-h-[60px]": !showMore,
                  "max-h-full pb-5": showMore,
                })}
              >
                {copy?.split("\n").map((line, index) => {
                  if (line === "") {
                    return <div key={index} className="py-[10px]" />;
                  } else {
                    let parsedLine = line.replaceAll("\\_", "_");
                    const lineWithoutMentions = parsedLine.replaceAll(
                      linkedinMentionRegex,
                      ""
                    );
                    const websites = (
                      uniq(lineWithoutMentions.match(urlRegex)) || []
                    ).sort((a, b) => b.length - a.length);

                    websites.forEach((website) => {
                      let httpWebsite = website;
                      if (!website.startsWith("http")) {
                        httpWebsite = `http://${website}`;
                      }
                      const escapedWebsite = website.replace(
                        /[.*+?^${}()|[\]\\]/g,
                        "\\$&"
                      );
                      const websiteRegex = new RegExp(
                        `${escapedWebsite}(?!\\/|[?#])`,
                        "g"
                      );
                      parsedLine = parsedLine.replace(
                        websiteRegex,
                        `[${website}](${httpWebsite})`
                      );
                    });

                    parsedLine = parsedLine
                      .replaceAll("\\>", ">")
                      .replaceAll("\\<", "<")
                      .replaceAll("\\{", "{")
                      .replaceAll("\\}", "}")
                      .replaceAll("\\*", "*")
                      .replaceAll("\\~", "~")
                      .replaceAll(">>", "\\>\\>");

                    // Returns ["@[NAME](urn:li:organization:ID)", "@[NAME](urn:li:organization:ID)"]
                    const mentions = uniq(
                      parsedLine.match(linkedinMentionRegex) || []
                    );
                    mentions.forEach((mention) => {
                      let cleanedMention = mention.slice(1);
                      const orgRegex = /(urn:li:organization:\w+)/;
                      const orgUrnMatch = orgRegex.exec(mention);
                      if (orgUrnMatch) {
                        const urn = orgUrnMatch[0];
                        const orgLink = urnToUrls[urn] || "www.linkedin.com";
                        cleanedMention = cleanedMention.replace(
                          orgUrnMatch[0],
                          orgLink
                        );
                        parsedLine = parsedLine.replaceAll(
                          mention,
                          cleanedMention
                        );
                      } else {
                        const memberRegex = /(urn:li:person:[\w-_]+)/;
                        const memberUrnMatch = memberRegex.exec(mention);
                        if (memberUrnMatch) {
                          const urn = memberUrnMatch[0];
                          const personLink =
                            urnToUrls[urn] || "https://www.linkedin.com";
                          cleanedMention = cleanedMention.replace(
                            memberUrnMatch[0],
                            personLink
                          );
                          parsedLine = parsedLine.replaceAll(
                            mention,
                            cleanedMention
                          );
                        }
                      }
                    });

                    const hashtags =
                      uniq(line.match(/(^|\s)(#[\w\d_]+)/g)) || [];

                    hashtags.forEach((hashtag) => {
                      const tag = hashtag.replace(" #", "").replace("#", "");

                      const tagRegex = new RegExp(`${hashtag}\\b`, "g");

                      parsedLine = parsedLine.replaceAll(
                        tagRegex,
                        `[${hashtag}](https://www.linkedin.com/feed/hashtag/?keywords=${tag})`
                      );
                    });

                    return (
                      <div key={index} dir="auto">
                        <ReactMarkdown
                          className="w-[512px] break-words text-[#0F1419]"
                          linkTarget="_blank"
                          children={parsedLine}
                          components={{
                            h1: ({ node, ...props }) => {
                              return (
                                <span>
                                  # <span {...props} />
                                </span>
                              );
                            },
                            h2: ({ node, ...props }) => {
                              return (
                                <span>
                                  ## <span {...props} />
                                </span>
                              );
                            },
                            h3: ({ node, ...props }) => {
                              return (
                                <span>
                                  ### <span {...props} />
                                </span>
                              );
                            },
                            strong: "span",
                            em: ({ node, ...props }) => {
                              const start = node.position?.start.column;
                              const end = node.position?.end.column;

                              if (start != null && end != null) {
                                const copy = parsedLine.slice(
                                  start - 1,
                                  end - 1
                                );
                                return <span>{copy}</span>;
                              } else {
                                return (
                                  <span>
                                    *<span {...props} />*
                                  </span>
                                );
                              }
                            },
                            code: ({ node, ...props }) => {
                              return (
                                <span>
                                  `<span {...props} />`
                                </span>
                              );
                            },
                            blockquote: ({ node, ...props }) => {
                              const children = removeEmptyElems(
                                props.children.filter((child) => child !== "\n")
                                // @ts-expect-error props is not defined on the child
                              ).map((child) => child.props.children);
                              return (
                                <span>
                                  {">"} {children}
                                </span>
                              );
                            },
                            hr: ({ node, ...props }) => {
                              const length = node.position?.end.offset || 3;
                              return (
                                <span>
                                  {Array.from({ length }).map((_, index) => (
                                    <span key={index}>-</span>
                                  ))}
                                </span>
                              );
                            },
                            ol: ({ node, ...props }) => {
                              return (
                                <ol
                                  {...props}
                                  className="list-inside list-decimal"
                                />
                              );
                            },
                            ul: ({ node, ...props }) => {
                              // @ts-expect-error children is not defined on the node
                              const children = props.children[1].props.children;
                              return (
                                <ol {...props} className="list-inside">
                                  <li>- {children}</li>
                                </ol>
                              );
                            },
                            a: ({ node, ...props }) => {
                              return (
                                <a
                                  className="text-linkedin-blue hover:decoration-linkedin-blue font-semibold underline-offset-2 hover:underline"
                                  {...props}
                                />
                              );
                            },
                          }}
                        />
                      </div>
                    );
                  }
                })}
                {!showMore && height >= 60 && (
                  <button
                    className="absolute right-4 bottom-0 bg-white px-0.5 text-black/60 transition-colors hover:text-[#0a66c2] hover:underline hover:decoration-[#0a66c2]"
                    onClick={() => setShowMore(true)}
                  >
                    ...more
                  </button>
                )}
                {showMore && (
                  <button
                    className="absolute right-4 bottom-0 bg-white px-0.5 text-black/60 transition-colors hover:text-[#0a66c2] hover:underline hover:decoration-[#0a66c2]"
                    onClick={() => setShowMore(false)}
                  >
                    ...less
                  </button>
                )}
              </div>
            </AnimateChangeInHeight>
            {linkedInContent.assets.length === 0 && (
              <LinkPreview url={firstWebsite} channel="LinkedIn" />
            )}
            <LinkedInAssetPreview
              documentTitle={linkedInContent.documentTitle}
              assets={linkedInContent.assets}
              coverPhotoUrl={coverPhotoUrl}
              captionsUrl={captionsUrl}
            />
          </div>
          <div className="flex items-center justify-between px-6 text-[14px] font-semibold text-black/60">
            <div className="flex items-center">
              <img src="/integrations/linkedin_like.png" className="h-6 w-6" />
              <span>Like</span>
            </div>
            <div className="flex items-center">
              <img
                src="/integrations/linkedin_comment.png"
                className="h-6 w-6"
              />
              <span>Comment</span>
            </div>
            <div className="flex items-center">
              <img
                src="/integrations/linkedin_repost.png"
                className="h-6 w-6"
              />
              <span>Repost</span>
            </div>
            <div className="flex items-center">
              <img src="/integrations/linkedin_send.png" className="h-6 w-6" />
              <span>Send</span>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default LinkedInPostPreview;
