import { AssetType } from "~/types/Asset";

const enhanceAssetType = (asset: {
  id: string;
  name: string;
  bucket: string;
  path: string;

  eTag: string;
  md5Hash: string | null;

  url: string;
  urlExpiresAt: Date;

  mimetype: string;

  width: number | null;
  height: number | null;
  duration: number | null;

  size: number;

  createdAt: Date;
  updatedAt: Date;
}): AssetType & {
  width: number | null;
  height: number | null;
  duration: number | null;
} => {
  return {
    id: asset.id,
    name: asset.name,
    path: asset.path,
    url: asset.url,
    urlExpiresAt: asset.urlExpiresAt,
    signedUrl: asset.url,
    eTag: asset.eTag,
    md5Hash: asset.md5Hash,
    mimetype: asset.mimetype,
    metadata: {
      size: asset.size,
      mimetype: asset.mimetype,
    },
    width: asset.width,
    height: asset.height,
    duration: asset.duration,
    sizeBytes: asset.size,
    sizeMB: asset.size / 1_000 / 1_000,
    sizeKB: asset.size / 1_000,
    createdAt: asset.createdAt,
    updatedAt: asset.updatedAt,
  };
};

export default enhanceAssetType;
