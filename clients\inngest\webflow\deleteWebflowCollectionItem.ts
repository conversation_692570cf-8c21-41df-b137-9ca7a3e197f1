import { NonRetriableError, slugify } from "inngest";
import { db } from "~/clients";
import Webflow from "~/clients/Webflow";
import { inngest } from "../inngest";

export default inngest.createFunction(
  {
    id: slugify("Webflow Delete Collection Item"),
    name: "Webflow Delete Collection Item",
    cancelOn: [
      {
        event: "post.webflow.previewUnpublished",
        match: "data.itemID",
      },
    ],
    onFailure: async ({ error, event, step }) => {},
  },
  {
    event: "post.webflow.previewed",
  },
  async ({ event, step }) => {
    const { webflowIntegrationID, collectionID, itemID, delayUntil } =
      event.data;

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    await step.run("Delete Webflow Collection Item", async () => {
      console.log(`Webflow Integration:${webflowIntegrationID}: Deleting item`);

      const webflowIntegration = await db.webflowIntegration.findFirstOrThrow({
        where: {
          id: webflowIntegrationID,
        },
      });

      if (!webflowIntegration) {
        throw new NonRetriableError(
          `Webflow Integration:${webflowIntegrationID}: No Webflow integration`
        );
      }

      const webflow = new Webflow(webflowIntegration.accessToken);

      const deleteResponse = await webflow.deleteCollectionItem({
        collectionID,
        itemID,
      });

      return {
        status: deleteResponse.status,
      };
    });
  }
);
