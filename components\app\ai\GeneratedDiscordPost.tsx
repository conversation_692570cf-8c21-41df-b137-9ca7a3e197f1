import { JSONContent } from "@tiptap/core";
import { useEffect, useState } from "react";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { formatTextAsTipTapEditorState } from "~/utils";
import DiscordPostPreview from "../posts/discord/DiscordPostPreview";
import DiscordEditor from "../tiptap/DiscordEditor";
import ContentContainer from "./ContentContainer";

type AccountOption = {
  id: string;
  username: string;
  channel: string;
  profileImageUrl: string;
};

type Props = {
  draftID?: string;
  title: string | null;
  content: string;
  isLoading: boolean;
  accountOptions: AccountOption[];
};

const GeneratedDiscordPost = ({
  draftID,
  title,
  content: rawContent,
  isLoading,
  accountOptions,
}: Props) => {
  const [editModalOpen, setEditModalOpen] = useState<boolean>(false);
  const [content, setContent] = useState<{
    copy: string;
    assets: [];
    editorState: JSONContent | null;
    documentTitle: null;
  }>({
    copy: rawContent,
    assets: [],
    editorState: formatTextAsTipTapEditorState(rawContent),
    documentTitle: null,
  });
  const [editedContent, setEditedContent] = useState<{
    copy: string;
    assets: [];
    editorState: JSONContent | null;
    documentTitle: null;
  }>({
    copy: rawContent,
    assets: [],
    editorState: formatTextAsTipTapEditorState(rawContent),
    documentTitle: null,
  });

  useEffect(() => {
    setContent({
      copy: rawContent,
      assets: [],
      editorState: formatTextAsTipTapEditorState(rawContent),
      documentTitle: null,
    });
  }, [rawContent]);

  useEffect(() => {
    setEditedContent(content);
  }, [content]);

  const [integrationAccountID, setIntegrationAccountID] = useState<
    string | null
  >(accountOptions.length > 0 ? accountOptions[0].id : null);
  const integrationAccount = accountOptions.find(
    (option) => option.id === integrationAccountID
  );

  return (
    <>
      <ContentContainer
        title={title}
        channel="Discord"
        draftID={draftID}
        isLoading={isLoading}
        content={[content]}
        onEditClick={() => {
          setEditModalOpen(true);
        }}
        connectedAccount={
          integrationAccount
            ? {
                integrationID: integrationAccount.id,
                name: integrationAccount.username,
              }
            : null
        }
        accountOptions={accountOptions.map((option) => ({
          value: option.id,
          label: `${option.channel} (${option.username})`,
        }))}
        setConnectedAccountID={setIntegrationAccountID}
      >
        <DiscordPostPreview
          discordContent={content}
          connectedAccount={integrationAccount}
        />
      </ContentContainer>
      <Credenza.Root open={editModalOpen} onOpenChange={setEditModalOpen}>
        <Credenza.Header>
          <Credenza.Title>Edit Copy</Credenza.Title>
          <Credenza.Description className="sr-only">
            Edit the copy for the post here
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <DiscordEditor
            title="Post Content"
            placeholder="Add copy for the post here"
            initialValue={editedContent.editorState}
            onSave={(editorState) => {
              setEditedContent({
                ...editedContent,
                editorState,
              });
            }}
            onUpdateContent={(copy) => {
              setEditedContent({
                ...editedContent,
                copy,
              });
            }}
            roomID={null}
          />
          <Credenza.Footer>
            <Button
              variant="secondary"
              onClick={() => {
                setEditedContent(content);
                setEditModalOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setContent(editedContent);
                setEditModalOpen(false);
              }}
            >
              Save
            </Button>
          </Credenza.Footer>
        </Credenza.Body>
      </Credenza.Root>
    </>
  );
};

export default GeneratedDiscordPost;
