{"[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[zmodel]": {"editor.defaultFormatter": "zenstack.zenstack"}, "deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}