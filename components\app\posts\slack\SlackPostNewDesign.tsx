import { useEffect, useState } from "react";

import { v4 as uuidv4 } from "uuid";

import { Channel, ScheduledPostStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import assert from "assert";
import { toast } from "sonner";
import { ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import {
  usePostIntegrations,
  useThreadComments,
  useWorkspace,
} from "~/providers";
import { SlackIntegration } from "~/providers/PostIntegrationsProvider";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  getImageProperties,
  getVideoProperties,
  handleFilesUpload,
  handleTrpcError,
  openIntercomArticle,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import getFileProperties from "~/utils/getFileProperties";
import createRoomID from "~/utils/liveblocks/createRoomID";
import { UploadingAsset } from "../../files/AssetsNewDesign";
import SlackEditorWrapperNewDesign from "../../tiptap/SlackEditorNewDesign";
import EditorActionsBar from "../EditorActionsBar";
import SlackPostPreviewNewDesign from "./SlackPostPreviewNewDesign";

type ScheduledPost = {
  status: ScheduledPostStatus;
  channel: Channel;
  error: string | null;
  publishAt: Date;
  integrationAccountName: string;
};

export type SlackContent = {
  id: string;
  editorState: JSONContent | null;
  copy: string;
  uploading?: boolean;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    slackAssets: AssetType[];
    slackContent: SlackContent | null;
    scheduledPosts: ScheduledPost[];
    slackIntegration: SlackIntegration | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const SlackPostNewDesign = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const trpcUtils = trpc.useUtils();
  const [isRoomReady, setIsRoomReady] = useState<boolean>(false);

  useEffect(() => {
    if (post.slackContent && !isSyncingComplete) {
      setIsRoomReady(true);
    }
  }, [post.slackContent, isSyncingComplete]);

  const {
    connectedIntegrations,
    selectedIntegrations,
    currentChannel,
    getChannelIntegration,
  } = usePostIntegrations();

  const connectedSlackIntegrations = connectedIntegrations.Slack || [];

  const [integration, setIntegration] = useState<SlackIntegration | null>(
    post.slackIntegration
  );
  const [content, setContent] = useState<SlackContent>(
    post.slackContent || {
      id: uuidv4(),
      editorState: null,
      copy: "",
    }
  );
  const [draggedOver, setDraggedOver] = useState<boolean>(false);
  const [assets, setAssets] = useState<AssetType[]>(post.slackAssets);
  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);

  const [uppy, setUppy] = useState<Uppy | null>(null);

  const updatePostMutation = trpc.post.update.useMutation();

  const slackContentQuery = trpc.slackContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const slackContentData = slackContentQuery.data;
  useEffect(() => {
    const setCrossPostData = () => {
      if (slackContentData?.slackContent != null) {
        setContent(slackContentData.slackContent);
        setAssets(slackContentData.assets);
        setIsRoomReady(true);
        clearIsSyncing();
      }
    };
    setCrossPostData();
  }, [slackContentData]);

  const upsertSlackContent = trpc.slackContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
  });
  const createAssetsMutation = trpc.slackContent.createAssets.useMutation();
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const updateAssetMutation = trpc.slackContent.updateAsset.useMutation();

  useEffect(() => {
    if (selectedIntegrations.Slack && connectedSlackIntegrations.length) {
      const selectedIntegration = connectedSlackIntegrations.find(
        (integration) => integration.id === selectedIntegrations.Slack
      );

      if (
        selectedIntegration &&
        (!integration || integration.id !== selectedIntegration.id)
      ) {
        const slackIntegration = getChannelIntegration(
          "Slack",
          selectedIntegration.id
        );
        if (slackIntegration) {
          setIntegration(slackIntegration);
          updatePostMutation.mutate(
            {
              id: post.id,
              slackIntegrationID: slackIntegration.id,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getIntegrations.refetch({
                  id: post.id,
                });
              },
            }
          );
        }
      }
    }
  }, [selectedIntegrations, connectedSlackIntegrations]);

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `slack-${post.id}`,
        restrictions: { maxNumberOfFiles: 10 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", (file) => {
        assert(currentWorkspace);

        setUploadingAssets((uploadingAssets) => {
          return [
            ...uploadingAssets,
            {
              id: file.id,
              name: file.name!,
              size: file.size!,
              type: file.type,
              uploadProgress: 0,
              updateAssetID: file.meta.updateAssetID as string | undefined,
              width: 0,
              height: 0,
            },
          ];
        });

        const objectName = `${currentWorkspace.id}/${post.id}/${CHANNEL_ICON_MAP["Slack"].slug}/${content.id}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-start", async (data) => {
        for (const file of data) {
          if (
            file.type.startsWith("image/") ||
            file.type.startsWith("video/")
          ) {
            const assetProperties = file.type.startsWith("video/")
              ? await getVideoProperties(file.data)
              : await getImageProperties(file.data);

            setUploadingAssets((uploadingAssets) => {
              const uploadingAsset = uploadingAssets.find(
                (asset) => asset.name === file.name
              );
              if (uploadingAsset) {
                return uploadingAssets.map((asset) =>
                  asset.name === file.name
                    ? {
                        ...asset,
                        width: assetProperties.width,
                        height: assetProperties.height,
                      }
                    : asset
                );
              }
              return uploadingAssets;
            });
          }
        }
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (uppyData) => {
        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["Slack"].slug
        }/${content.id}`;
        const fileNames = uppyData.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          uppyData.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              if (uppyData.successful![0].meta.updateAssetID) {
                const updateAssetID = uppyData.successful![0].meta
                  .updateAssetID as string;
                const newAsset = newAssetsWithProperties[0];

                await updateAssetMutation.mutateAsync({
                  slackAssetID: updateAssetID,
                  newAsset: {
                    id: newAsset.id,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                });

                setAssets((prevAssets) =>
                  prevAssets.map((asset) =>
                    asset.id === updateAssetID
                      ? {
                          id: asset.id,
                          name: newAsset.name,
                          path: newAsset.path,
                          sizeBytes: newAsset.sizeBytes,
                          eTag: newAsset.eTag,
                          md5Hash: newAsset.md5Hash,
                          mimetype: newAsset.mimetype,
                          width: newAsset.width,
                          height: newAsset.height,
                          metadata: newAsset.metadata,
                          url: newAsset.signedUrl,
                          sizeMB: newAsset.sizeMB,
                          sizeKB: newAsset.sizeKB,
                          createdAt: newAsset.createdAt,
                          updatedAt: newAsset.updatedAt,
                          duration: newAsset.duration,
                          signedUrl: newAsset.signedUrl,
                          urlExpiresAt: newAsset.urlExpiresAt,
                        }
                      : asset
                  )
                );
                setUploadingAssets([]);
              } else {
                const nextPosition = assets.length;

                createAssetsMutation.mutate(
                  {
                    id: content.id,
                    assets: newAssetsWithProperties.map((asset, index) => ({
                      id: asset.id,
                      assetID: asset.assetID,
                      name: asset.name,
                      path: asset.path,
                      size: asset.sizeBytes,
                      eTag: asset.eTag,
                      md5Hash: asset.md5Hash,
                      mimetype: asset.mimetype,
                      width: asset.width,
                      height: asset.height,
                      duration: asset.duration,
                      url: asset.signedUrl,
                      urlExpiresAt: asset.urlExpiresAt,
                      position: nextPosition + index,
                    })),
                  },
                  {
                    onError: (error) => {
                      handleTrpcError({ title: "Error Creating Asset", error });
                    },
                  }
                );

                setUploadingAssets([]);
                setAssets((assets) => [...assets, ...newAssetsWithProperties]);
              }
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace, content.id]);

  const getValidFiles = async (files: File[]) => {
    if (assets.length + files.length > 1) {
      toast.error("Too Many Files", {
        description: "You can add 1 image or video per post",
      });
      return [];
    }

    return files;
  };

  const { setRoomIDs } = useThreadComments();

  const currentRoomID = createRoomID({
    workspaceID: post.workspaceID,
    contentType: "post",
    postID: post.id,
    roomContentID: content.id,
  });

  useEffect(() => {
    if (content.id && currentChannel === "Slack") {
      setRoomIDs([currentRoomID]);
    }
  }, [content.id, currentRoomID, setRoomIDs, currentChannel]);

  return (
    <div
      className="mb-12 flex min-h-[289px] w-full flex-col lg:max-w-[622px]"
      // This avoids triggering the dropzone when dragging internal post content like existing images
      onDragOver={(event) => {
        if (
          event.dataTransfer.types.includes("Files") &&
          !event.dataTransfer.types.includes("text/html")
        ) {
          event.preventDefault();
          setDraggedOver(true);
        }
      }}
      onDragLeave={(event) => {
        const currentTarget = event.currentTarget;
        const relatedTarget = event.relatedTarget as Node | null;

        if (!relatedTarget || !currentTarget.contains(relatedTarget)) {
          setDraggedOver(false);
        }
      }}
      onDrop={() => {
        setDraggedOver(false);
      }}
    >
      <SlackPostPreviewNewDesign
        contentID={content.id}
        assets={assets}
        setAssets={setAssets}
        uploadingAssets={uploadingAssets}
        draggedOver={draggedOver}
        setDraggedOver={setDraggedOver}
        uppy={uppy}
        getValidFiles={getValidFiles}
        isReadOnly={isReadOnly}
        connectedIntegration={integration}
        editor={
          <>
            {isRoomReady && (
              <SlackEditorWrapperNewDesign
                key={content?.id || "slack-editor"}
                placeholder="Add copy for the post here"
                initialValue={content?.editorState}
                isReadOnly={isReadOnly}
                onSave={(editorState) => {
                  assert(content);
                  setContent({
                    ...content,
                    editorState,
                  });

                  upsertSlackContent.mutate({
                    id: content.id,
                    copy: content.copy,
                    editorState: editorState,
                    postID: post.id,
                  });
                }}
                onUpdateContent={(copy) => {
                  setContent({
                    ...content,
                    copy,
                  });
                }}
                onImagePaste={(event) => {
                  if (uppy) {
                    handleFilesUpload({
                      items: event.clipboardData.items,
                      uppy,
                      meta: {
                        contentID: content.id,
                      },
                      getValidFiles,
                    });
                  }
                }}
                roomID={createRoomID({
                  workspaceID: post.workspaceID,
                  contentType: "post",
                  postID: post.id,
                  roomContentID: content.id,
                })}
              />
            )}
          </>
        }
      />
      <div className="mt-2">
        <EditorActionsBar
          post={post}
          channel="Slack"
          visible={true}
          isReadOnly={isReadOnly}
          characterCount={{
            current: content.copy.length,
            max: 40000,
          }}
          openHowToTagModal={() => {
            openIntercomArticle("slack_tagging");
          }}
          uploadAssetButton={{
            acceptedMimetypes: [
              "image/jpeg",
              "image/png",
              "image/gif",
              "video/mp4",
              "video/quicktime",
            ],
            onUploadFiles: async (files) => {
              if (uppy) {
                handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID: content.id,
                  },
                  getValidFiles,
                });
              }
            },
            isLoading: content.uploading,
          }}
        />
      </div>
    </div>
  );
};

export default SlackPostNewDesign;
