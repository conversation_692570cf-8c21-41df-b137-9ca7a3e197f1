import { AiPromptInputType, Channel } from "@prisma/client";
import { useForm } from "react-hook-form";
import { Label } from "~/components";
import FormLayout from "~/components/app/settings/FormLayout";
import {
  FormChannelSelect,
  FormTextAreaInput,
  FormTextInput,
} from "~/components/form";
import Button from "~/components/ui/Button";

type FormValues = {
  name: string;
  description: string;
  category: string;
  channels: Channel[];
  inputTypes: AiPromptInputType[];

  inputPlaceholder: string | null;
  exampleInput: string | null;

  style: string;
  structure: string;
  keyFeatures: string;
  additionalContext: string;
  qualityChecks: string;
};

type Props = {
  title: string;
  prompt?: FormValues | null;
  saveButton: {
    label: string;
    onClick: (data: FormValues) => void;
    isLoading: boolean;
  };
};

const PromptForm = ({ title, prompt, saveButton }: Props) => {
  const { control, handleSubmit } = useForm<FormValues>({
    defaultValues: prompt
      ? prompt
      : {
          inputTypes: ["Text", "URL"],
        },
  });

  const onSubmit = (data: FormValues) => {
    saveButton.onClick(data);
  };

  return (
    <FormLayout title={title} backHref="/admin/settings/prompts">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Label value="Name" isUppercase={false}>
          <FormTextInput
            name="name"
            kind="outline"
            control={control}
            placeholder="Enter your prompt name"
          />
        </Label>
        <Label value="Description" isUppercase={false}>
          <FormTextInput
            name="description"
            kind="outline"
            control={control}
            placeholder="Add a short description for what this prompt is about"
          />
        </Label>
        <Label value="Category" isUppercase={false}>
          <FormTextInput
            name="category"
            kind="outline"
            control={control}
            placeholder="Enter the category for this prompt"
          />
        </Label>
        <Label value="Placeholder" isUppercase={false} isOptional={true}>
          <FormTextInput
            name="inputPlaceholder"
            kind="outline"
            control={control}
            placeholder="Placeholder instructions for the prompt input"
          />
        </Label>
        <Label
          value="Example Input"
          description="Text input for users to test the prompt with"
          isUppercase={false}
          isOptional={true}
        >
          <FormTextAreaInput
            name="exampleInput"
            control={control}
            placeholder="Enter an example input for the prompt"
            rows={3}
          />
        </Label>
        <Label
          value="Output"
          description="What channel(s) are you outputting content on?"
          isUppercase={false}
        >
          <FormChannelSelect
            name="channels"
            placeholder="Channels"
            control={control}
            isMulti={true}
          />
        </Label>
        <Label
          value="Style"
          description="The instructions Assembly AI will use to set the style"
          isUppercase={false}
        >
          <FormTextAreaInput name="style" control={control} rows={3} />
        </Label>
        <Label
          value="Structure"
          description="The instructions Assembly AI will use to set the structure"
          isUppercase={false}
        >
          <FormTextAreaInput name="structure" control={control} rows={3} />
        </Label>
        <Label
          value="Key Features"
          description="The instructions Assembly AI will use to set the key features"
          isUppercase={false}
        >
          <FormTextAreaInput name="keyFeatures" control={control} rows={3} />
        </Label>
        <Label
          value="Additional Context"
          description="The instructions Assembly AI will use to set the additional context"
          isUppercase={false}
        >
          <FormTextAreaInput
            name="additionalContext"
            control={control}
            rows={3}
          />
        </Label>
        <Label
          value="Quality Checks"
          description="The instructions Assembly AI will use to set the quality checks"
          isUppercase={false}
        >
          <FormTextAreaInput name="qualityChecks" control={control} rows={3} />
        </Label>
        <div className="flex justify-end">
          <Button isLoading={saveButton.isLoading} type="submit">
            {saveButton.label}
          </Button>
        </div>
      </form>
    </FormLayout>
  );
};

export default PromptForm;
