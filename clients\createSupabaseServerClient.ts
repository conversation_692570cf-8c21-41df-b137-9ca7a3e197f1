import { createServerClient } from "@supabase/ssr";
import Cookies from "universal-cookie";

const createSupabaseServerClient = async (cookie: string | undefined) => {
  const cookiesInstance = new Cookies(cookie);

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        async getAll() {
          const allCookies = cookiesInstance.getAll();
          return Object.entries(allCookies).map(([name, value]) => ({
            name,
            value: String(value),
          }));
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookiesInstance.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
};

export default createSupabaseServerClient;
