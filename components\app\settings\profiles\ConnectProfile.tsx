import { Channel } from "@prisma/client";
import { ReactNode } from "react";
import AnimateChangeInHeight from "~/components/AnimateChangeInHeight";
import { CHANNEL_ICON_MAP } from "~/types";
import { cn } from "~/utils";
import ChannelIcon from "../../ChannelIcon";

type RootProps = {
  children: ReactNode;
};

const Root = ({ children }: RootProps) => {
  return (
    <AnimateChangeInHeight>
      <div className="border-light-gray overflow-clip rounded-lg border p-6 md:px-10 md:py-8">
        {children}
      </div>
    </AnimateChangeInHeight>
  );
};
Root.displayName = "ConnectProfile";

type HeaderProps = {
  channel: Channel;
  title?: string;
  description: string;
};

const Header = ({ channel, title, description }: HeaderProps) => {
  return (
    <div className="space-y-1">
      <div className="flex items-center gap-2">
        <ChannelIcon channel={channel} width={20} />
        <p className="font-body font-medium">
          {title ? title : `Connect ${CHANNEL_ICON_MAP[channel].label}`}
        </p>
      </div>
      <p className="font-body-sm text-medium-dark-gray">{description}</p>
    </div>
  );
};
Header.displayName = "ConnectProfileHeader";

type ContentProps = {
  children: ReactNode;
  className?: string;
};

const Content = ({ children, className }: ContentProps) => {
  return <div className={cn("mt-3 space-y-4", className)}>{children}</div>;
};
Content.displayName = "ConnectProfileContent";

type FooterProps = {
  children: ReactNode;
  className?: string;
};

const Footer = ({ children, className }: FooterProps) => {
  return (
    <div className={cn("font-body-sm flex flex-col gap-4 md:gap-6", className)}>
      {children}
    </div>
  );
};
Footer.displayName = "ConnectProfileFooter";

export default {
  Root,
  Header,
  Content,
  Footer,
};
