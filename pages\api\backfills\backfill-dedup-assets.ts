import uniq from "lodash/uniq";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.findMany({
    where: {
      channels: {
        has: "LinkedIn",
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    select: {
      id: true,
      workspaceID: true,
      createdByID: true,
      createdAt: true,
      linkedInContent: {
        select: {
          id: true,
          coverPhoto: true,
          captions: true,
          assets: {
            select: {
              id: true,
              position: true,
              assetID: true,
            },
          },
        },
      },
    },
  });

  const postsWithDuplicateAssetIDs = posts.filter((post) => {
    if (!post.linkedInContent) {
      return false;
    }

    const assetIDs = post.linkedInContent.assets.map((asset) => asset.assetID);
    return new Set(assetIDs).size !== assetIDs.length;
  });

  const promises = postsWithDuplicateAssetIDs.map(async (post) => {
    const assetIDs = post.linkedInContent!.assets.map((asset) => asset.assetID);
    const uniqueAssetIDs = uniq(assetIDs);
    const linkedInAssetIDsToKeep = uniqueAssetIDs.map((assetID) => {
      const asset = post.linkedInContent!.assets.find(
        (asset) => asset.assetID === assetID
      );
      return asset!.id;
    });

    await db.linkedInAsset.deleteMany({
      where: {
        linkedInContentID: post.linkedInContent!.id,
        id: {
          notIn: linkedInAssetIDsToKeep,
        },
      },
    });

    await db.$transaction(async (tx) => {
      let index = 0;
      for await (const linkedInAssetID of linkedInAssetIDsToKeep) {
        await tx.linkedInAsset.update({
          where: {
            id: linkedInAssetID,
          },
          data: {
            position: index,
          },
        });
        index++;
      }
    });
  });

  await Promise.all(promises);

  res.status(200).send({
    numDuplicatePosts: postsWithDuplicateAssetIDs.length,
    duplicatePostIDs: postsWithDuplicateAssetIDs.map((post) => post.id),
    duplicatePosts: postsWithDuplicateAssetIDs.map((post) => post),
  });
};

export default handler;
