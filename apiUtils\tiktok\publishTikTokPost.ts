import { Asset, TikTokPrivacyLevel } from "@prisma/client";
import getAssetsForTikTok from "apiUtils/getAssetsForTikTok";
import TikTok from "~/clients/tiktok/TikTok";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";

type Post = {
  id: string;
  tikTokIntegration: {
    accessToken: string;
  } | null;
  tikTokContent: {
    id: string;
    copy: string;
    privacyLevel: TikTokPrivacyLevel | null;
    isCommentDisabled: boolean;
    isDuetDisabled: boolean;
    isStitchDisabled: boolean;
    isPromotingBusiness: boolean;
    isPaidPartnership: boolean | null;
    assets: {
      id: string;
      position: number;
      asset: Asset;
    }[];
  } | null;
  title: string;
  workspaceID: string;
};

const publishTikTokPost = async (post: Post): Promise<ScheduledPostResult> => {
  const { tikTokContent, tikTokIntegration } = post;

  if (!tikTokContent) {
    return {
      status: "NonRetriableError",
      error: "No TikTok content to post",
    };
  }

  if (!tikTokIntegration) {
    return {
      status: "NonRetriableError",
      error: "No TikTok integration",
    };
  }

  const tiktok = new TikTok(tikTokIntegration.accessToken);

  const assets = getAssetsForTikTok(post);

  if (assets.length == 0 || !assets[0].metadata.mimetype.includes("video")) {
    return {
      status: "NonRetriableError",
      error: "No video to post",
    };
  }

  const asset = assets[0];

  if (tikTokContent.privacyLevel === null) {
    return {
      status: "NonRetriableError",
      error: "Privacy level not set",
    };
  }

  const response = await tiktok.publishVideo({
    caption: tikTokContent.copy,
    video: {
      url: asset.signedUrl,
      mimetype: asset.metadata.mimetype,
    },
    privacyLevel: tikTokContent.privacyLevel,
    isCommentDisabled: tikTokContent.isCommentDisabled,
    isDuetDisabled: tikTokContent.isDuetDisabled,
    isStitchDisabled: tikTokContent.isStitchDisabled,
    isPromotingBusiness: tikTokContent.isPromotingBusiness,
    isPaidPartnership: tikTokContent.isPaidPartnership,
  });

  if (response.success === false) {
    const error = response.errors.join(". ");

    const isAuthError = response.isAuthError;
    return {
      status: isAuthError ? "NonRetriableError" : "Error",
      error,
      rawError: response.rawError,
      isAuthError,
    };
  } else {
    return {
      status: "Success",
      postUrl: null,
    };
  }
};

export default publishTikTokPost;
