import {
  Bars3BottomLeftIcon,
  ChevronDownIcon,
  H1Icon,
  H2Icon,
  H3Icon,
  ListBulletIcon,
  NumberedListIcon,
} from "@heroicons/react/24/outline";
import { Level } from "@tiptap/extension-heading";
import { Editor } from "@tiptap/react";
import classNames from "classnames";
import Image from "next/image";
import { useEffect, useState } from "react";
import ToolbarButton from "./ToolbarButton";

type TextFormatButtonsProps = {
  editor: Editor;
  features?: {
    headings?: Level[];
    bulletList?: boolean;
    orderedList?: boolean;
  };
  onHide: (callback: () => void) => void;
};

const isHeadingLevelDisabled = (editor: Editor, level: Level) => {
  try {
    return (
      !editor.schema.nodes.heading ||
      !editor.extensionManager.extensions.find(
        (ext) => ext.name === "heading" && ext.options?.levels?.includes(level)
      )
    );
  } catch (error) {
    return false;
  }
};

const getHeadingIcon = (level: Level) => {
  switch (level) {
    case 1:
      return <H1Icon className="h-3 w-3" />;
    case 2:
      return <H2Icon className="h-3 w-3" />;
    case 3:
      return <H3Icon className="h-3 w-3" />;
    default:
      return (
        <Image
          src={`/editor/heading${level}.png`}
          alt={`Heading ${level}`}
          width={12}
          height={12}
        />
      );
  }
};

const TextFormatButtons = ({
  editor,
  features = {},
  onHide,
}: TextFormatButtonsProps) => {
  const {
    headings = [1, 2, 3, 4, 5, 6],
    bulletList = true,
    orderedList = true,
  } = features;

  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const cleanup = onHide(() => setIsOpen(false));
    return cleanup;
  }, [onHide]);

  const options = [
    {
      id: "paragraph",
      icon: <Bars3BottomLeftIcon className="h-3 w-3" />,
      onClick: () => {
        editor
          .chain()
          .focus()
          .command(({ commands }) => {
            if (
              editor.isActive("bulletList") ||
              editor.isActive("orderedList")
            ) {
              commands.liftListItem("listItem");
            }
            if (editor.isActive("taskList")) {
              commands.lift("taskItem");
            }
            return true;
          })
          .setParagraph()
          .run();
      },
      isDisabled: () => !editor.can().setParagraph(),
      isActive: () =>
        editor.isActive("paragraph") &&
        !editor.isActive("orderedList") &&
        !editor.isActive("bulletList") &&
        !editor.isActive("taskList"),
      label: "Text",
    },
    ...(headings.length > 0
      ? headings.map((level) => ({
          id: `heading${level}`,
          icon: getHeadingIcon(level),
          onClick: () =>
            editor
              .chain()
              .focus()
              .command(({ commands }) => {
                if (
                  editor.isActive("bulletList") ||
                  editor.isActive("orderedList")
                ) {
                  commands.liftListItem("listItem");
                }
                if (editor.isActive("taskList")) {
                  commands.lift("taskItem");
                }
                return true;
              })
              .setHeading({ level })
              .run(),
          isDisabled: () => isHeadingLevelDisabled(editor, level),
          isActive: () => editor.isActive("heading", { level }),
          label: `Heading ${level}`,
        }))
      : []),
    ...(bulletList
      ? [
          {
            id: "bulletList",
            icon: <ListBulletIcon className="h-3 w-3" />,
            onClick: () => editor.chain().focus().toggleBulletList().run(),
            isDisabled: () => !editor.can().toggleBulletList(),
            isActive: () => editor.isActive("bulletList"),
            label: "Bullet List",
          },
        ]
      : []),
    ...(orderedList
      ? [
          {
            id: "orderedList",
            icon: <NumberedListIcon className="h-3 w-3" />,
            onClick: () => editor.chain().focus().toggleOrderedList().run(),
            isDisabled: () => !editor.can().toggleOrderedList(),
            isActive: () => editor.isActive("orderedList"),
            label: "Ordered List",
          },
        ]
      : []),
  ];

  const currentActiveOption = options.find((option) => option.isActive());
  const enabledOptions = options.filter((option) => !option.isActive());

  if (enabledOptions.length === 0) {
    return null;
  }

  return (
    <div className="relative">
      <ToolbarButton
        onClick={(e) => {
          e.preventDefault();
          setIsOpen(!isOpen);
        }}
        className="flex items-center gap-0.5 px-1"
      >
        <span className="font-body-sm">{currentActiveOption?.label}</span>
        <ChevronDownIcon
          className={classNames("h-3 w-3 transition-transform", {
            "-rotate-180": isOpen,
          })}
        />
      </ToolbarButton>
      {isOpen && (
        <div className="animate-in fade-in zoom-in-95 absolute top-full left-0 mt-2.5 w-48 origin-top-left transform rounded-md bg-white p-1 shadow-xl transition-all duration-100 ease-out">
          {enabledOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => {
                option.onClick();
                setIsOpen(false);
              }}
              className={classNames(
                "font-body-sm hover:bg-slate flex w-full items-center gap-2 rounded-md px-2 py-1 text-left whitespace-nowrap transition-colors",
                { "bg-light-gray": option.isActive() }
              )}
            >
              <div className="border-light-gray rounded-xs border p-1">
                {option.icon}
              </div>
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default TextFormatButtons;
