import { StatusCodes } from "http-status-codes";
import { NonRetriableError, slugify } from "inngest";
import { DateTime } from "luxon";
import { db, slack } from "~/clients";
import LinkedIn from "~/clients/linkedin/LinkedIn";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import escapeSpecialCharacters from "~/utils/linkedin/escapeSpecialCharacters";
import getLinkedInProfilePermalink from "~/utils/linkedin/getLinkedInProfilePermalink";
import { inngest } from "../inngest";

export default inngest.createFunction(
  {
    id: slugify("LinkedIn Post Engagement"),
    name: "LinkedIn Post Engagement",
    onFailure: async ({ error, event }) => {
      const data = event.data.event.data;
      const runID = event.data.run_id;
      const { engagementID, permalink } = data;
      console.log(
        `LinkedIn Engagement:${engagementID}: Final Error ${error.message}`
      );

      const engagement = await db.linkedInPostEngagement.findFirst({
        where: {
          id: engagementID,
        },
        select: {
          integration: {
            select: {
              account: {
                select: {
                  name: true,
                  type: true,
                  vanityName: true,
                },
              },
            },
          },
          content: {
            select: {
              post: {
                select: {
                  id: true,
                  title: true,
                  linkedInIntegration: {
                    select: {
                      accessToken: true,
                      account: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                  workspace: {
                    select: {
                      name: true,
                      company: {
                        select: { name: true },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!engagement) {
        console.error(
          `LinkedIn Engagement:${engagementID}: Engagement deleted`
        );
        return;
      }

      const { linkedInIntegration } = engagement.content.post;

      if (linkedInIntegration != null) {
        const linkedin = new LinkedIn(linkedInIntegration.accessToken);

        const urn = permalink.replace(
          "https://www.linkedin.com/feed/update/",
          ""
        );

        const { data, errors } = await linkedin.getPosts({
          urns: [urn],
        });

        if (data) {
          const statuses = data.statuses;
          if (statuses[urn] === 404) {
            console.log(`LinkedIn Engagement:${engagementID}: Post deleted`);
            return;
          }
        }

        const profile = {
          name: engagement.integration.account.name,
          url: getLinkedInProfilePermalink(engagement.integration.account),
        };

        await slack.uncaughtFailedEngagement({
          channel: "LinkedIn",
          post: engagement.content.post,
          postUrl: permalink,
          workspace: engagement.content.post.workspace,
          engagementID,
          error: error.message,
          profile,
          runID,
        });
      }
    },
  },
  { event: "linkedin.postEngagement" },
  async ({ event, step }) => {
    const { delayUntil, engagementID, externalPostID: postUrn } = event.data;

    // If delayUntil is in the past then we'll wait 30 seconds just in case
    // since LinkedIn posts sometimes aren't ready immediately
    const minDelay = DateTime.now().plus({ seconds: 30 }).toJSDate();
    const delay = delayUntil < minDelay ? minDelay : delayUntil;
    await step.sleepUntil("Wait for Scheduled Time", delay);

    await step.run("Post LinkedIn Post Engagement", async () => {
      console.log(`LinkedIn Engagement:${engagementID}: Finding record`);
      const engagement = await db.linkedInPostEngagement.findFirst({
        where: {
          id: engagementID,
        },
        select: {
          id: true,
          type: true,
          copy: true,
          url: true,
          postDelaySeconds: true,
          content: {
            select: {
              post: {
                select: {
                  id: true,
                  createdByID: true,
                  workspace: {
                    select: {
                      companyID: true,
                      id: true,
                    },
                  },
                },
              },
            },
          },
          integration: {
            select: {
              accessToken: true,
              account: {
                select: {
                  name: true,
                  urn: true,
                },
              },
            },
          },
        },
      });
      if (!engagement) {
        return {
          skipped: true,
          reason: "Engagement deleted",
        };
      }

      console.log(`LinkedIn Engagement:${engagementID}: Found record`);

      if (engagement.url != null) {
        console.error(`LinkedIn Engagement:${engagementID}: Already engaged`);
        throw new NonRetriableError("Already engaged");
      }

      const { account, accessToken } = engagement.integration;
      const engagementClient = new LinkedIn(accessToken);

      const { type, copy } = engagement;

      let url = null;

      if (type === "Comment") {
        if (copy == null || copy.length === 0) {
          console.log(
            `LinkedIn Engagement:${engagementID}: No copy to comment with`
          );
          return {
            skipped: true,
            reason: "No copy to comment with",
          };
        }

        console.log(`LinkedIn Engagement:${engagementID}: Creating comment`);
        const { data, errors } = await engagementClient.createComment({
          authorUrn: account.urn,
          postUrn,
          commentary: copy,
        });

        if (data) {
          console.log(
            `LinkedIn Engagement:${engagementID}: Posted to ${data.commentUrl}`
          );
          url = data.commentUrl;
        } else {
          const error = errors.join(", ");
          console.error(
            `LinkedIn Engagement:${engagementID} Errored: ${error}`
          );
          throw new Error(error);
        }
      } else if (type === "Like") {
        console.log(`LinkedIn Engagement:${engagementID}: Liking post`);
        const { status, data, errors } = await engagementClient.likePost({
          actorUrn: account.urn,
          postUrn,
        });

        if (data) {
          console.log(`LinkedIn Engagement:${engagementID}: Liked post`);

          url = `https://www.linkedin.com/feed/update/${postUrn}?likeUrn=${data.id}`;
        } else {
          const error = errors.join(", ");
          console.error(
            `LinkedIn Engagement:${engagementID}: Errored ${error}`
          );

          if (status === StatusCodes.CONFLICT) {
            return {
              skipped: true,
              reason: "Already liked",
            };
          } else {
            throw new Error(error);
          }
        }
      } else {
        console.log(`LinkedIn Engagement:${engagementID}: Creating Reshare`);
        const { data, errors } = await engagementClient.createPost({
          authorUrn: account.urn,
          commentary: escapeSpecialCharacters(copy),
          parentUrn: postUrn,
        });

        if (data) {
          url = `https://www.linkedin.com/feed/update/${data.postUrn}`;
          console.log(`LinkedIn Engagement:${engagementID}: Posted to ${url}`);
        } else {
          const error = errors.join(", ");
          console.error(
            `LinkedIn Engagement:${engagementID}: Errored ${error}`
          );
          throw new Error(error);
        }
      }

      if (url) {
        await db.linkedInPostEngagement.update({
          where: {
            id: engagement.id,
          },
          data: {
            url,
          },
        });

        posthog.capture({
          distinctId: engagement.content.post.createdByID,
          event: POSTHOG_EVENTS.engagement.published,
          properties: {
            channel: "LinkedIn",
            engagementType: engagement.type,
            postID: engagement.content.post.id,
            delaySeconds: engagement.postDelaySeconds,
          },
          groups: {
            workspace: engagement.content.post.workspace.id,
            company: engagement.content.post.workspace.companyID,
          },
        });

        return {
          url,
        };
      }
    });
  }
);
