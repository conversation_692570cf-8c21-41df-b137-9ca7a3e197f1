import { RoomData } from "@liveblocks/node";
import { TRPCError } from "@trpc/server";
import { liveblocks } from "server/routers/liveblocks";
import { parseRoomID } from "~/utils";

interface GetOrCreateRoomProps {
  roomID: string;
  userID: string;
}

const getOrCreateRoom = async ({
  roomID,
  userID,
}: GetOrCreateRoomProps): Promise<{ room: RoomData }> => {
  const { workspaceID } = parseRoomID(roomID);

  try {
    const room = await liveblocks.getOrCreateRoom(roomID, {
      defaultAccesses: [],
      usersAccesses: {
        [userID]: ["room:write"],
      },
      groupsAccesses: {
        [workspaceID]: ["room:write"],
      },
    });

    return { room };
  } catch (error) {
    console.error("Error getting or creating Liveblocks room:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to get or create Liveblocks room",
    });
  }
};

export default getOrCreateRoom;
