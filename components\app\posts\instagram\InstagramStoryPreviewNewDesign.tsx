import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import Uppy from "@uppy/core";
import { motion } from "framer-motion";
import { useState } from "react";
import { Accept } from "react-dropzone";
import { Carousel } from "react-responsive-carousel";
import { UploadProgress } from "~/components/ui/AssetUploadContainer";
import VideoPlayer from "~/components/ui/VideoPlayer";
import { useWorkspace } from "~/providers";
import { AssetType } from "~/types/Asset";
import { getDefaultProfilePicture, handleFilesUpload } from "~/utils";
import {
  AssetControls,
  AssetsUploadReorderControls,
} from "../../files/AssetControls";
import { UploadingAsset } from "../../files/AssetsNewDesign";
import FileDropzone from "../../files/FileDropzone";

type InstagramAccount = {
  id: string;
  account: {
    name: string | null;
    profileImageUrl: string;
  };
};

type Props = {
  assets: AssetType[];
  connectedIntegration: InstagramAccount | null;
  uploadingAssets: UploadingAsset[];
  setIsEditingAssets: (isEditingAssets: boolean) => void;
  uppy?: Uppy | null;
  getValidFiles: (files: File[]) => Promise<File[]>;
  acceptedFileTypes: Accept;
  contentID?: string;
  onDrop: (files: File[]) => void;
  handleUploadButtonClick: () => void;
  onDeleteClick: (asset: AssetType) => void;
  isDraggedOver: boolean;
  isReadOnly: boolean;
};

const InstagramStoryPreviewNewDesign = ({
  assets,
  connectedIntegration,
  uploadingAssets,
  setIsEditingAssets,
  uppy,
  getValidFiles,
  acceptedFileTypes,
  contentID,
  onDrop,
  handleUploadButtonClick,
  onDeleteClick,
  isDraggedOver,
  isReadOnly,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const [isHovering, setIsHovering] = useState<boolean>(false);
  const nonEmpty = assets.length > 0;

  const accountName =
    connectedIntegration?.account.name || currentWorkspace?.name;
  const profilePictureUrl =
    connectedIntegration?.account.profileImageUrl ??
    getDefaultProfilePicture("Instagram");

  const averageUploadProgress =
    uploadingAssets.length > 0
      ? Math.round(
          uploadingAssets.reduce(
            (sum, asset) => sum + (asset.uploadProgress || 0),
            0
          ) / uploadingAssets.length
        )
      : undefined;

  if (!nonEmpty) {
    return (
      <motion.div
        key="dropzone"
        className="h-[532px] w-60 sm:w-[300px]"
        initial={{ height: 532, opacity: 0 }}
        animate={{
          height: 532,
          opacity: 100,
        }}
      >
        <FileDropzone
          onDrop={async (files) => {
            if (uppy) {
              await handleFilesUpload({
                files,
                uppy,
                meta: {
                  contentID,
                },
                getValidFiles,
              });
            }
            onDrop(files);
          }}
          acceptedFileTypes={acceptedFileTypes}
          borderClassName={`rounded-3xl ${
            !isDraggedOver ? "border-none" : undefined
          }`}
          backgroundImage={
            !isDraggedOver ? "/instagram/placeholder.png" : undefined
          }
          label={
            !isDraggedOver
              ? "Click or drag and drop to upload media"
              : undefined
          }
          uploadProgress={averageUploadProgress}
        />
      </motion.div>
    );
  } else {
    return (
      <div className="relative flex h-full w-full items-center justify-center">
        <div
          className="group/asset-controls relative h-[532px] w-60 overflow-clip rounded-md bg-black sm:w-[300px]"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
        >
          <div className="absolute top-1.5 w-full px-1">
            <div className="flex items-center gap-px">
              {assets.map((asset, index) => {
                return (
                  <div
                    key={asset.id}
                    className="h-px w-full rounded-xs bg-white/50"
                  />
                );
              })}
            </div>
            <div className="mt-2 ml-1 flex items-center gap-2">
              <img className="h-6 w-6 rounded-full" src={profilePictureUrl} />
              <div className="font-body-sm font-medium text-white">
                {accountName?.replace("@", "")}
              </div>
            </div>
          </div>
          <Carousel
            showThumbs={false}
            showStatus={false}
            showIndicators={false}
            renderArrowNext={(onClickHandler, hasNext, label) =>
              hasNext && (
                <button
                  type="button"
                  onClick={onClickHandler}
                  title={label}
                  className="bg-light-gray absolute top-1/2 right-2 z-10 -translate-y-1/2 transform rounded-full p-0.5 opacity-70"
                >
                  <ChevronRightIcon className="h-4 w-4" />
                </button>
              )
            }
            renderArrowPrev={(onClickHandler, hasPrev, label) =>
              hasPrev && (
                <button
                  type="button"
                  onClick={onClickHandler}
                  title={label}
                  className="bg-light-gray absolute top-1/2 left-2 z-10 -translate-y-1/2 transform rounded-full p-0.5 opacity-70"
                >
                  <ChevronLeftIcon className="h-4 w-4" />
                </button>
              )
            }
          >
            {assets.map((asset) => {
              if (asset.metadata.mimetype.startsWith("video")) {
                return (
                  <div
                    key={asset.signedUrl}
                    className="group/asset relative flex h-[532px] items-center justify-center"
                  >
                    <VideoPlayer.Root className="h-full w-full">
                      <VideoPlayer.Content
                        src={asset.signedUrl}
                        slot="media"
                        preload="auto"
                      />
                      {isHovering && (
                        <VideoPlayer.ControlBar>
                          <VideoPlayer.PlayButton />
                          <VideoPlayer.TimeRange />
                          <VideoPlayer.TimeDisplay showDuration />
                          <VideoPlayer.VolumeRange />
                        </VideoPlayer.ControlBar>
                      )}
                    </VideoPlayer.Root>
                    <AssetControls
                      asset={asset}
                      isReadOnly={isReadOnly}
                      onDeleteClick={onDeleteClick}
                    />
                  </div>
                );
              } else {
                return (
                  <div
                    key={asset.signedUrl}
                    className="group/asset relative flex h-[532px] w-full items-center justify-center"
                  >
                    <img
                      className="max-h-[532px] object-cover"
                      src={asset.signedUrl}
                    />
                    <AssetControls
                      asset={asset}
                      isReadOnly={isReadOnly}
                      onDeleteClick={onDeleteClick}
                    />
                  </div>
                );
              }
            })}
          </Carousel>
          {assets.length > 0 && (
            <AssetsUploadReorderControls
              onUploadClick={handleUploadButtonClick}
              onReorderClick={() => setIsEditingAssets(true)}
              isReadOnly={isReadOnly}
            />
          )}
        </div>
        {uploadingAssets && uploadingAssets.length > 0 && (
          <motion.div
            className="bg-slate absolute inset-0 z-30 flex items-center justify-center backdrop-blur-xs"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <UploadProgress progress={averageUploadProgress || 0} />
          </motion.div>
        )}
      </div>
    );
  }
};

export default InstagramStoryPreviewNewDesign;
