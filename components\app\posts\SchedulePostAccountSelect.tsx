import { useEffect, useState } from "react";

import {
  ChevronDownIcon,
  ClockIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import { Channel, ScheduledPostStatus } from "@prisma/client";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { Badge, Select } from "~/components";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import Skeleton from "~/components/ui/Skeleton";
import { useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import {
  dateFromUTC,
  getPostPublishAtMoment,
  getTimezoneName,
  openIntercomChat,
  removeEmptyElems,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";

export type ScheduledPost = {
  id: string;
  status: ScheduledPostStatus;
  channel: Channel;
  error: string | null;
  publishAt: Date;
  integrationAccountName: string;
};

export type ConnectedAccount = {
  integrationID: string;
  name: string;
};

type Props = {
  post: {
    id: string;
    channels: Channel[];
    title: string;
    labels: {
      id: string;
      name: string;
      color: string;
      backgroundColor: string;
      createdAt: Date;
    }[];
  } & PostOrIdea;
  channel: Channel;
  connectedAccount?: ConnectedAccount | null;
  setConnectedAccountID?: (integrationID: string) => void;
  accountOptions?: ConnectedAccount[];
  isReadOnly?: boolean;
};

type FormValues = {
  postDate: Date;
  postTime: Date;
};

const SchedulePostAccountSelect = ({
  post,
  channel,
  connectedAccount,
  accountOptions,
  setConnectedAccountID,
  isReadOnly,
}: Props) => {
  const { currentWorkspace } = useWorkspace();

  const router = useRouter();
  const trpcUtils = trpc.useUtils();

  const [selectedAccounts, setSelectedAccounts] = useState<
    {
      id: string;
      channel: Channel;
    }[]
  >([]);
  const [connectAccountModalOpen, setConnectAccountModalOpen] =
    useState<boolean>(false);
  const [errorModalOpen, setErrorModalOpen] = useState<boolean>(false);
  const { control, handleSubmit, watch, reset, setValue } =
    useForm<FormValues>();

  const publishDate = watch("postDate");
  const publishTime = watch("postTime");

  const integrationsQuery = trpc.post.getIntegrations.useQuery(
    {
      id: post.id,
    },
    {
      refetchOnWindowFocus: false,
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    }
  );

  const initialAccounts = integrationsQuery.data?.accounts ?? [];

  useEffect(() => {
    if (initialAccounts.length > 0) {
      const accounts = removeEmptyElems(
        initialAccounts.map((account) => {
          if (account.id == null) {
            return null;
          }

          return {
            id: account.id,
            channel: account.channel,
          };
        })
      );
      setSelectedAccounts(accounts);
    }
  }, [initialAccounts]);

  const accounts = integrationsQuery.data?.accounts ?? [];
  const scheduledAccounts = accounts.filter(
    (account) => !!account.scheduledPost
  );

  const scheduledPosts = scheduledAccounts.map(
    (account) => account.scheduledPost
  );
  const scheduledPost = scheduledPosts.find(
    (scheduledPost) => scheduledPost?.channel === channel
  );
  const cancellationReason = scheduledPost?.cancellationReason;

  useEffect(() => {
    if (scheduledPost && scheduledPost.status === "Scheduled") {
      setValue("postDate", new Date(scheduledPost.publishAt));
      setValue("postTime", new Date(scheduledPost.publishAt));
    }
  }, [scheduledPost]);

  useEffect(() => {
    if (post.publishAt) {
      setValue("postDate", new Date(post.publishAt));
      setValue("postTime", new Date(post.publishAt));
    }
  }, [post.publishAt]);

  useEffect(() => {
    // TODO(francisco): change this to use publishAt instead of publishDate and publishTime
    if (!scheduledPost && !post.isIdea) {
      const publishDate = post.publishDate;
      const postDate = post.publishAt
        ? new Date(post.publishAt)
        : dateFromUTC(publishDate);

      const postTime = post.publishAt
        ? getPostPublishAtMoment({ ...post, publishDate }).toJSDate()
        : undefined;

      setValue("postDate", postDate);
      if (postTime) {
        setValue("postTime", postTime);
      }
    }
  }, [post]);

  if (integrationsQuery.isLoading) {
    return (
      <div className="mt-1 mr-2 flex h-4 items-center gap-2">
        <Skeleton className="h-full w-24" />
        <Skeleton className="h-full w-4 rounded-full" />
        <Skeleton className="h-full w-44" />
      </div>
    );
  }

  return (
    <div className="flex flex-col-reverse items-end gap-2 md:flex-row md:items-center">
      {cancellationReason && (
        <div className="font-body-sm text-medium-dark-gray font-medium">
          {cancellationReason}
        </div>
      )}
      {(scheduledPost == null || scheduledPost.status === "Cancelled") &&
        accountOptions != null &&
        setConnectedAccountID &&
        accountOptions.length > 0 && (
          <Select
            value={connectedAccount?.integrationID}
            placeholder="Choose Account"
            disabled={isReadOnly}
            onChange={setConnectedAccountID}
            options={accountOptions.map((option) => ({
              value: option.integrationID,
              label: option.name,
              component: (
                <Badge intent="none">
                  <div className="font-body-sm text-secondary font-medium">
                    {option.name}
                  </div>
                  {!isReadOnly && (
                    <ChevronDownIcon className="text-medium-gray h-3 w-3" />
                  )}
                </Badge>
              ),
            }))}
            actionButton={{
              icon: "PlusIcon",
              label: "Connect another account",
              onClick: () => {
                router.push("/settings/profiles");
              },
            }}
          />
        )}
      {scheduledPost != null && scheduledPost.status != "Cancelled" ? (
        <div className="group font-body-sm flex items-center gap-1">
          {scheduledPost.status === "Scheduled" ? (
            <div className="text-medium-dark-gray flex items-center gap-1.5">
              <ClockIcon className="h-4 w-4" />
              <span>
                Scheduled for{" "}
                <span className="text-secondary font-medium">
                  {scheduledPost.integrationAccountName}
                </span>
              </span>
            </div>
          ) : scheduledPost.status === "Completed" ? (
            <div className="text-medium-dark-gray flex items-center gap-1">
              <ClockIcon className="h-4 w-4" />
              <span>
                Posted on{" "}
                {DateTime.fromJSDate(scheduledPost.publishAt).toFormat(
                  "M/d/yyyy 'at' h:mm a"
                )}{" "}
                {getTimezoneName()}
              </span>
            </div>
          ) : (
            <div className="text-medium-dark-gray flex items-center gap-1">
              <XCircleIcon className="h-4 w-4" />
              <div>Post Failed</div>
              <Button
                variant="warning"
                size="sm"
                onClick={() => {
                  setErrorModalOpen(true);
                }}
              >
                See Details
              </Button>
            </div>
          )}
        </div>
      ) : (
        <>
          {!isReadOnly &&
            accountOptions != null &&
            accountOptions.length === 0 && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  setConnectAccountModalOpen(true);
                }}
              >
                Connect {CHANNEL_ICON_MAP[channel].label} to Schedule
              </Button>
            )}
        </>
      )}
      <Credenza.Root open={errorModalOpen} onOpenChange={setErrorModalOpen}>
        <Credenza.Header>
          <Credenza.Title>Something went wrong</Credenza.Title>
          <Credenza.Description>
            {scheduledPost?.error || "Unknown error, contact Assembly support"}
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Footer>
          <Button
            onClick={() => {
              openIntercomChat(
                `I'm having trouble with post ${post.title} on ${CHANNEL_ICON_MAP[channel].label}. The post URL is ${process.env.NEXT_PUBLIC_DOMAIN}/posts/${post.id} if you'd be able to help me get it posted.`
              );
              setErrorModalOpen(false);
            }}
          >
            Contact Support
          </Button>
        </Credenza.Footer>
      </Credenza.Root>
      <Credenza.Root
        open={connectAccountModalOpen}
        onOpenChange={setConnectAccountModalOpen}
      >
        <Credenza.Header>
          <Credenza.Title>
            Connect {CHANNEL_ICON_MAP[channel].label} to Schedule
          </Credenza.Title>
          <Credenza.Description>
            Connect an account so you can publish directly from Assembly.
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Footer>
          <Button
            variant="secondary"
            onClick={() => setConnectAccountModalOpen(false)}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={() => router.push("/settings/profiles")}
          >
            Connect
          </Button>
        </Credenza.Footer>
      </Credenza.Root>
    </div>
  );
};

export default SchedulePostAccountSelect;
