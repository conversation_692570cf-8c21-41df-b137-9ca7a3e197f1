import { StatusCodes } from "http-status-codes";
import chunk from "lodash/chunk";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const posts = await db.post.findMany({
      where: {
        scheduledPosts: {
          // Find all posts where none of the related scheduledPosts have status = "Completed".
          none: {
            status: "Completed",
          },
        },
      },
      select: {
        id: true,
        workspace: {
          select: {
            userWorkspaces: {
              select: {
                userID: true,
              },
            },
          },
        },
      },
    });

    const campaigns = await db.campaign.findMany({
      include: {
        workspace: {
          select: {
            userWorkspaces: {
              select: {
                userID: true,
              },
            },
          },
        },
      },
    });

    // Add workspace users as subscribers to posts if they aren't already
    const postSubscriberData = posts.flatMap((post) => {
      const userIDs = post.workspace.userWorkspaces.map(
        (userWorkspace) => userWorkspace.userID
      );
      return userIDs.map((userID) => ({
        postID: post.id,
        userID,
        createdByID: userID,
      }));
    });

    if (postSubscriberData.length > 0) {
      const postSubscribersBatches = chunk(postSubscriberData, 5000);

      for (const batch of postSubscribersBatches) {
        await db.subscriber.createMany({
          data: batch,
          skipDuplicates: true,
        });
      }
    }

    // Add workspace users as subscribers to campaigns if they aren't already
    const campaignSubscriberData = campaigns.flatMap((campaign) => {
      const userIDs = campaign.workspace.userWorkspaces.map(
        (userWorkspace) => userWorkspace.userID
      );
      return userIDs.map((userID) => ({
        campaignID: campaign.id,
        userID,
        createdByID: userID,
      }));
    });

    if (campaignSubscriberData.length > 0) {
      const campaignSubscriberBatches = chunk(campaignSubscriberData, 5000);

      for (const batch of campaignSubscriberBatches) {
        await db.subscriber.createMany({
          data: batch,
          skipDuplicates: true,
        });
      }
    }

    res.status(StatusCodes.OK).json({
      message: "Backfill complete",
    });
  } catch (error) {
    console.error("Error in backfill endpoint:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: "An error occurred",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

export default handler;
