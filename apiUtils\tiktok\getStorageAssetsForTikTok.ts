import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForTikTok = async (post: {
  id: string;
  workspaceID: string;
  tikTokContent: {
    id: string;
  } | null;
}): Promise<AssetType[]> => {
  const tikTokContent = post.tikTokContent;

  if (!tikTokContent) {
    return [];
  }

  const assetsMap = await getAssetsForFilePaths([
    {
      id: tikTokContent.id,
      path: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["TikTok"].slug}/${tikTokContent.id}`,
    },
  ]);

  return assetsMap[tikTokContent.id];
};

export default getStorageAssetsForTikTok;
