import { CropPresetOption, OptionGroup } from "@pqina/pintura";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { DateTime } from "luxon";
import { useEffect, useRef, useState } from "react";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import { Accept } from "react-dropzone";
import SortableList, { SortableItem } from "react-easy-sort";
import { Tooltip } from "~/components";
import colors from "~/styles/colors";
import { AssetType } from "~/types/Asset";
import { getTimezoneName } from "~/utils";
import EditImageModal, {
  EditImageModalProperties,
  ValidationRules,
} from "~/utils/EditImageModal";
import Asset from "./Asset";
import FileDropzone from "./FileDropzone";

export type UploadingAsset = {
  name: string;
  uploadProgress: number;
  size: number;
  type: string;
  updateAssetID?: string;
};

type Props = {
  assets: AssetType[];
  uploadingAssets?: UploadingAsset[] | null;
  onDeleteClick?: (asset: { id: string; path: string }) => void;
  onEditClick?: (asset: AssetType) => void;
  refetchAssets: () => void;
  isReadOnly?: boolean;
  showDropzone?: boolean;
  onDrop: (acceptedFiles: File[]) => void;
  acceptedFileTypes?: Accept;
  onSortEnd?: (oldIndex: number, newIndex: number) => void;
  editImageModalProperties?: EditImageModalProperties | null;
  validationRules?: ValidationRules;
  cropSelectPresetOptions?: OptionGroup[] | CropPresetOption[];
  onEditImageModalClose?: () => void;
  onUpload?: (file: File, assetID?: string) => Promise<void>;
  showNewEditButton?: boolean;
};

const Assets = ({
  assets,
  uploadingAssets = [],
  onDeleteClick,
  onEditClick,
  refetchAssets,
  isReadOnly = false,
  showDropzone = false,
  onDrop,
  acceptedFileTypes,
  onSortEnd,
  editImageModalProperties,
  onEditImageModalClose,
  validationRules,
  cropSelectPresetOptions,
  onUpload,
  showNewEditButton = false,
}: Props) => {
  const [height, setHeight] = useState<number>(100);
  const assetsRef = useRef<HTMLDivElement>(null);

  const isSortable = !!onSortEnd;

  useEffect(() => {
    if (
      assetsRef.current &&
      assets.length > 0 &&
      assetsRef.current.clientHeight > 0
    ) {
      // Set height to the height of the assets container
      setHeight(assetsRef.current.clientHeight || 100);
    }
  }, [assetsRef.current?.clientHeight, assets]);

  return (
    <AnimatePresence>
      {showDropzone ? (
        <motion.div
          key="dropzone"
          className="w-full"
          initial={{ height: assets.length > 0 ? height : 0, opacity: 0 }}
          animate={{
            height,
            opacity: 100,
          }}
          exit={assets.length > 0 ? undefined : { height: 0, opacity: 0 }}
        >
          <FileDropzone
            onDrop={onDrop}
            acceptedFileTypes={acceptedFileTypes || {}}
          />
        </motion.div>
      ) : (
        <motion.div
          key="assets"
          ref={assetsRef}
          initial={{ opacity: 0 }}
          animate={{
            opacity: 100,
          }}
          className={classNames({
            hidden: assets.length === 0 && uploadingAssets?.length === 0,
          })}
        >
          {isSortable ? (
            <SortableList
              onSortEnd={onSortEnd}
              className="list grid grid-cols-3 gap-4"
              draggedItemClassName="dragged"
            >
              {assets.map((asset) => {
                const isEditing = uploadingAssets?.some(
                  (uploadingAsset) => uploadingAsset.updateAssetID === asset.id
                );
                return (
                  <SortableItem key={asset.id}>
                    <div className="item cursor-grab">
                      <Asset
                        asset={asset}
                        onDeleteClick={onDeleteClick}
                        onEditClick={() => onEditClick?.(asset)}
                        refetchAssets={refetchAssets}
                        isReadOnly={isReadOnly}
                        showNewEditButton={showNewEditButton}
                        isEditing={isEditing}
                      />
                    </div>
                  </SortableItem>
                );
              })}
              {(uploadingAssets || [])
                .filter((asset) => !asset.updateAssetID)
                .map((uploadingAsset) => {
                  return (
                    <UploadingAsset
                      key={uploadingAsset.name}
                      uploadingAsset={uploadingAsset}
                    />
                  );
                })}
            </SortableList>
          ) : (
            <div className="grid grid-cols-3 gap-4">
              {assets?.map((asset) => {
                const isEditing = uploadingAssets?.some(
                  (uploadingAsset) => uploadingAsset.updateAssetID === asset.id
                );
                return (
                  <Asset
                    asset={asset}
                    key={asset.id}
                    onDeleteClick={onDeleteClick}
                    onEditClick={() => onEditClick?.(asset)}
                    refetchAssets={refetchAssets}
                    isReadOnly={isReadOnly}
                    showNewEditButton={showNewEditButton}
                    isEditing={isEditing}
                  />
                );
              })}
              {(uploadingAssets || [])
                .filter((asset) => !asset.updateAssetID)
                .map((uploadingAsset) => {
                  return (
                    <UploadingAsset
                      key={uploadingAsset.name}
                      uploadingAsset={uploadingAsset}
                    />
                  );
                })}
            </div>
          )}
        </motion.div>
      )}
      {editImageModalProperties && (
        <EditImageModal
          title={editImageModalProperties.title}
          asset={editImageModalProperties.asset}
          maxSizeMB={editImageModalProperties.maxSizeMB}
          channel={editImageModalProperties.channel}
          isOpen={editImageModalProperties.isOpen}
          error={editImageModalProperties.error}
          validationRules={validationRules}
          cropSelectPresetOptions={cropSelectPresetOptions}
          onClose={onEditImageModalClose!}
          onUpload={async (file) =>
            onUpload?.(file, editImageModalProperties.asset.id)
          }
        />
      )}
    </AnimatePresence>
  );
};

const UploadingAsset = ({
  uploadingAsset,
}: {
  uploadingAsset?: UploadingAsset | null;
}) => {
  if (!uploadingAsset) {
    return null;
  }

  return (
    <div className="bg-lightest-gray-30 border-medium-gray/30 relative h-auto w-auto max-w-[400px] rounded-xl border">
      <div className="flex aspect-square w-full max-w-[400px] items-center justify-center">
        <div className="h-8 w-8">
          <CircularProgressbar
            value={uploadingAsset.uploadProgress}
            strokeWidth={5}
            styles={buildStyles({
              pathColor: colors.accentBlue,
            })}
          />
        </div>
      </div>
      <div className="px-3 py-2">
        <Tooltip value={uploadingAsset.name}>
          <div className="font-body-xs overflow-clip text-ellipsis whitespace-nowrap">
            {uploadingAsset.name}
          </div>
        </Tooltip>
        <div className="font-eyebrow-sm text-medium-gray">
          {Math.round((uploadingAsset.size / 1_024 / 1_024) * 10) / 10} MB |{" "}
          {DateTime.now().toFormat("MM/dd/yy h:mm a")} {getTimezoneName()}
        </div>
      </div>
    </div>
  );
};

export default Assets;
