import { useState } from "react";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import Card from "~/components/ui/Card";
import Switch from "~/components/ui/Switch";

const SwitchPage = () => {
  const [checked, setChecked] = useState<boolean>(false);
  console.log(checked);

  return (
    <SettingsLayout.Root>
      <SettingsLayout.Header>
        <SettingsLayout.Title>Switch</SettingsLayout.Title>
        <SettingsLayout.Description>
          If you need to represent the switching between two states or on-off
          state.
          <br />
          <br />
          The difference between <code>Switch</code> and <code>Checkbox</code>{" "}
          is that <code>Switch</code> will trigger a state change directly when
          you toggle it, while <code>Checkbox</code> is generally used for state
          marking, which should work in conjunction with submit operation.
        </SettingsLayout.Description>
      </SettingsLayout.Header>
      <div className="grid grid-cols-3 gap-4">
        <Card.Root>
          <Card.Header>
            <Card.Title>Default</Card.Title>
          </Card.Header>
          <Card.Content className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <Switch
                checked={checked}
                onCheckedChange={() => setChecked(!checked)}
              />
            </div>
          </Card.Content>
        </Card.Root>
      </div>
    </SettingsLayout.Root>
  );
};

export default SwitchPage;
