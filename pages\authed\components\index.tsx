import Link from "next/link";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import Button from "~/components/ui/Button";
import Card from "~/components/ui/Card";
import Switch from "~/components/ui/Switch";

const Components = () => {
  return (
    <SettingsLayout.Root isFullWidth={true}>
      <SettingsLayout.Header>
        <SettingsLayout.Title>Component Library</SettingsLayout.Title>
      </SettingsLayout.Header>
      <SettingsLayout.Section>
        <div className="grid grid-cols-2 gap-4">
          <Link href="/components/button">
            <Card.Root>
              <Card.Header>
                <Card.Title>Button</Card.Title>
              </Card.Header>
              <Card.Content className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Button variant="default">||||||||||</Button>
                  <Button variant="secondary" size="sm">
                    ||||||||||
                  </Button>
                </div>
              </Card.Content>
            </Card.Root>
          </Link>
          <Link href="/components/switch">
            <Card.Root>
              <Card.Header>
                <Card.Title>Switch</Card.Title>
              </Card.Header>
              <Card.Content className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Switch checked={false} onChange={() => {}}>
                    Default
                  </Switch>
                </div>
              </Card.Content>
            </Card.Root>
          </Link>
        </div>
      </SettingsLayout.Section>
    </SettingsLayout.Root>
  );
};

export default Components;
