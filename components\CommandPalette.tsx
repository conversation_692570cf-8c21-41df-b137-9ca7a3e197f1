import { Fragment, useState } from "react";

import classNames from "classnames";

import { Combobox, Dialog, Transition } from "@headlessui/react";
import {
  ChevronRightIcon,
  LifebuoyIcon,
  MagnifyingGlassIcon,
  UsersIcon,
} from "@heroicons/react/24/outline";

import { Kbd, Loader } from "~/components";

import { DateTime } from "luxon";
import { posthog } from "posthog-js";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { dateFromUTC, formatPostDate, trpc } from "~/utils";
import AnimateChangeInHeight from "./AnimateChangeInHeight";
import ChannelIcon from "./app/ChannelIcon";
import Button from "./ui/Button";

type Campaign = {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
};

type Props = {
  open: boolean;
  setOpen: (value: boolean) => void;
};

const CommandPalette = ({ open, setOpen }: Props) => {
  const [rawQuery, setRawQuery] = useState<string>("");

  const query = rawQuery.toLowerCase().replace(/^[#>]/, "");

  // Disabled so we only query when the user opens the modal
  const postQuery = trpc.post.getMany.useQuery(undefined, {
    enabled: open,
  });
  const campaignQuery = trpc.campaign.getAll.useQuery(undefined, {
    enabled: open,
  });
  const ideasQuery = trpc.idea.getAll.useQuery(undefined, {
    enabled: open,
  });

  const campaigns: Campaign[] =
    campaignQuery.data != null ? campaignQuery.data.campaigns : [];
  const filteredCampaigns =
    rawQuery === "#"
      ? campaigns
      : query === "" ||
          rawQuery.startsWith(">") ||
          rawQuery.startsWith("?") ||
          rawQuery.startsWith(":")
        ? []
        : campaigns.filter((campaign) => {
            return (
              campaign.name.toLowerCase().includes(query) ||
              campaign.id === rawQuery
            );
          });

  const posts = postQuery.data?.posts || [];
  const filteredPosts =
    rawQuery === ">"
      ? posts
      : query === "" ||
          rawQuery.startsWith("#") ||
          rawQuery.startsWith("?") ||
          rawQuery.startsWith(":")
        ? []
        : posts.filter((post) => {
            return (
              post.title.toLowerCase().includes(query) || post.id === rawQuery
            );
          });

  const ideas = ideasQuery.data?.ideas || [];
  const filteredIdeas =
    rawQuery === ":"
      ? ideas
      : query === "" ||
          rawQuery.startsWith("#") ||
          rawQuery.startsWith("?") ||
          rawQuery.startsWith(">")
        ? []
        : ideas.filter((idea) => {
            return (
              idea.title.toLowerCase().includes(query) || idea.id === rawQuery
            );
          });

  return (
    <Transition.Root
      show={open}
      as={Fragment}
      afterLeave={() => setRawQuery("")}
      appear
    >
      <Dialog as="div" className="relative z-30" onClose={setOpen}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="bg-lightest-gray/30 fixed inset-0 backdrop-blur-xs transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto p-4 sm:p-6 md:p-20">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 scale-95"
            enterTo="opacity-100 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 scale-100"
            leaveTo="opacity-0 scale-95"
          >
            <Dialog.Panel className="divide-light-gray bg-surface box-shadow mx-auto max-w-3xl transform divide-y overflow-hidden rounded-md transition-all">
              <AnimateChangeInHeight>
                <Combobox>
                  {({ activeOption }) => {
                    return (
                      <>
                        <div className="relative">
                          <MagnifyingGlassIcon
                            className="pointer-events-none absolute top-3.5 left-4 h-5 w-5"
                            aria-hidden="true"
                          />
                          <Combobox.Input
                            className="placeholder-light-gray sm:font-body-sm sticky top-0 h-12 w-full border-0 bg-transparent pr-4 pl-11 outline-hidden"
                            placeholder="Search for campaigns and posts"
                            onChange={(event) =>
                              setRawQuery(event.target.value || "")
                            }
                          />
                        </div>

                        {(campaignQuery.isLoading ||
                          postQuery.isLoading ||
                          ideasQuery.isLoading) &&
                        rawQuery.length > 0 ? (
                          <div className="flex w-full justify-center py-4">
                            <Loader size="lg" />
                          </div>
                        ) : (
                          <>
                            {filteredPosts.length > 0 && (
                              <Combobox.Options
                                as="div"
                                static
                                hold
                                className="divide-light-gray flex h-fit divide-x"
                              >
                                <>
                                  <div
                                    className={classNames(
                                      "scrollbar-hidden max-h-80 min-w-0 flex-auto scroll-py-4 overflow-y-auto",
                                      { "sm:h-fit": activeOption }
                                    )}
                                  >
                                    <div className="font-eyebrow bg-lightest-gray-30 sticky top-0 w-full p-1 pl-5">
                                      Posts
                                    </div>
                                    <div className="font-body-sm">
                                      {filteredPosts.map((post) => {
                                        return (
                                          <Combobox.Option
                                            as="a"
                                            key={post.id}
                                            value={post}
                                            href={`/posts/${post.id}`}
                                            className={({ active }) =>
                                              classNames(
                                                "flex cursor-pointer items-center p-2 transition-colors select-none",
                                                {
                                                  "bg-lightest-gray-30": active,
                                                }
                                              )
                                            }
                                            onClick={() => {
                                              posthog.capture(
                                                POSTHOG_EVENTS.search.navigated,
                                                {
                                                  type: "Post",
                                                }
                                              );
                                              setOpen(false);
                                            }}
                                          >
                                            {({ active }) => (
                                              <>
                                                <span className="ml-3 flex-auto truncate">
                                                  <div className="flex flex-col">
                                                    <div className="flex items-center gap-1.5">
                                                      {post.title}
                                                      {post.channels.map(
                                                        (channel) => {
                                                          return (
                                                            <ChannelIcon
                                                              key={channel}
                                                              channel={channel}
                                                              width={16}
                                                            />
                                                          );
                                                        }
                                                      )}
                                                    </div>
                                                    <div className="font-body-xs flex gap-2 font-light">
                                                      <div>
                                                        Date:{" "}
                                                        {formatPostDate(post)}
                                                      </div>
                                                    </div>
                                                  </div>
                                                </span>
                                                {active && (
                                                  <ChevronRightIcon
                                                    className="ml-3 h-5 w-5 flex-none text-white"
                                                    aria-hidden="true"
                                                  />
                                                )}
                                              </>
                                            )}
                                          </Combobox.Option>
                                        );
                                      })}
                                    </div>
                                  </div>
                                </>
                              </Combobox.Options>
                            )}
                            {filteredIdeas.length > 0 && (
                              <Combobox.Options
                                as="div"
                                static
                                hold
                                className="divide-light-gray flex h-fit divide-x"
                              >
                                <>
                                  <div
                                    className={classNames(
                                      "scrollbar-hidden max-h-80 min-w-0 flex-auto scroll-py-4 overflow-y-auto",
                                      { "sm:h-fit": activeOption }
                                    )}
                                  >
                                    <div className="font-eyebrow bg-lightest-gray-30 sticky top-0 w-full p-1 pl-5">
                                      Ideas
                                    </div>
                                    <div className="font-body-sm">
                                      {filteredIdeas.map((idea) => {
                                        return (
                                          <Combobox.Option
                                            as="a"
                                            key={idea.id}
                                            value={idea}
                                            href={`/ideas/${idea.id}`}
                                            className={({ active }) =>
                                              classNames(
                                                "flex cursor-pointer items-center rounded-xs p-2 transition-colors select-none",
                                                {
                                                  "bg-lightest-gray-30": active,
                                                }
                                              )
                                            }
                                            onClick={() => {
                                              posthog.capture(
                                                POSTHOG_EVENTS.search.navigated,
                                                {
                                                  type: "Idea",
                                                }
                                              );
                                              setOpen(false);
                                            }}
                                          >
                                            {({ active }) => (
                                              <>
                                                <span className="ml-3 flex-auto truncate">
                                                  <div className="flex flex-col">
                                                    <div className="flex items-center gap-1">
                                                      {idea.title}
                                                      {idea.channels.map(
                                                        (channel) => {
                                                          return (
                                                            <ChannelIcon
                                                              key={channel}
                                                              channel={channel}
                                                              width={16}
                                                            />
                                                          );
                                                        }
                                                      )}
                                                    </div>
                                                    <div className="font-body-xs flex gap-2 font-light">
                                                      <div>
                                                        Created At:{" "}
                                                        {DateTime.fromJSDate(
                                                          idea.createdAt
                                                        ).toLocaleString(
                                                          DateTime.DATETIME_MED
                                                        )}
                                                      </div>
                                                    </div>
                                                  </div>
                                                </span>
                                                {active && (
                                                  <ChevronRightIcon
                                                    className="ml-3 h-5 w-5 flex-none text-white"
                                                    aria-hidden="true"
                                                  />
                                                )}
                                              </>
                                            )}
                                          </Combobox.Option>
                                        );
                                      })}
                                    </div>
                                  </div>
                                  {/* Disabled this for now in case we want it again */}
                                  {activeOption && false && (
                                    <div className="divide-light-gray hidden h-96 w-1/2 flex-none flex-col divide-y overflow-y-auto sm:flex">
                                      <div className="flex flex-col items-center p-6">
                                        {/* <UserAvatar user={activeOption} /> */}
                                        <h2 className="mt-3 font-semibold">
                                          {/* {activeOption.title} */}
                                        </h2>
                                        <p className="mb-1 text-sm leading-6">
                                          <div className="font-body-xs flex gap-2">
                                            <div>Post Info 1</div>•
                                            <div>Post Info 2</div>
                                          </div>
                                        </p>
                                      </div>
                                      <div className="flex flex-auto flex-col justify-between p-6">
                                        <dl className="grid grid-cols-1 gap-x-6 gap-y-3 text-sm">
                                          <dt className="col-end-1 font-semibold">
                                            UserID
                                          </dt>
                                          <dd className="truncate text-sm">
                                            More Info
                                          </dd>
                                          <dt className="col-end-1 font-semibold">
                                            Created At
                                          </dt>
                                          <dd className="truncate text-sm">
                                            {/* {moment(activeOption.created).format(
                                          "MMMM Do, yyyy h:mm a"
                                        )} */}
                                            Created At
                                          </dd>
                                        </dl>
                                        <Button
                                          className="w-full"
                                          onClick={() => {
                                            setOpen(false);
                                          }}
                                        >
                                          View Profile
                                        </Button>
                                      </div>
                                    </div>
                                  )}
                                </>
                              </Combobox.Options>
                            )}
                            {filteredCampaigns.length > 0 && (
                              <Combobox.Options
                                as="div"
                                static
                                hold
                                className="divide-light-gray flex divide-x"
                              >
                                <>
                                  <div
                                    className={classNames(
                                      "scrollbar-hidden max-h-80 min-w-0 flex-auto scroll-py-4 overflow-y-auto",
                                      { "sm:h-fit": activeOption }
                                    )}
                                  >
                                    <div className="font-eyebrow bg-lightest-gray-30 sticky top-0 w-full p-1 pl-5">
                                      Campaigns
                                    </div>
                                    <div className="font-body-sm">
                                      {filteredCampaigns.map((campaign) => {
                                        return (
                                          <Combobox.Option
                                            as="a"
                                            key={campaign.id}
                                            href={`/campaigns/${campaign.id}`}
                                            value={campaign}
                                            className={({ active }) =>
                                              classNames(
                                                "flex cursor-pointer items-center rounded-xs p-2 transition-colors select-none",
                                                { "bg-slate": active }
                                              )
                                            }
                                            onClick={() => {
                                              posthog.capture(
                                                POSTHOG_EVENTS.search.navigated,
                                                {
                                                  type: "Campaign",
                                                }
                                              );
                                              setOpen(false);
                                            }}
                                          >
                                            {({ active }) => (
                                              <>
                                                <span className="ml-3 flex-auto truncate">
                                                  <div className="flex flex-col">
                                                    <div className="flex items-center gap-2">
                                                      {campaign.name}
                                                    </div>
                                                    <div className="font-body-xs flex gap-2">
                                                      <div>
                                                        {`${DateTime.fromJSDate(
                                                          dateFromUTC(
                                                            campaign.startDate
                                                          )
                                                        ).toFormat(
                                                          "M/d/y"
                                                        )} - ${DateTime.fromJSDate(
                                                          dateFromUTC(
                                                            campaign.endDate
                                                          )
                                                        ).toFormat("M/d/y")}`}
                                                      </div>
                                                    </div>
                                                  </div>
                                                </span>
                                                {active && (
                                                  <ChevronRightIcon
                                                    className="ml-3 h-5 w-5 flex-none text-white"
                                                    aria-hidden="true"
                                                  />
                                                )}
                                              </>
                                            )}
                                          </Combobox.Option>
                                        );
                                      })}
                                    </div>
                                  </div>
                                </>
                              </Combobox.Options>
                            )}
                            {rawQuery.startsWith("?") && (
                              <div className="px-6 py-14 text-center text-sm sm:px-14">
                                <LifebuoyIcon
                                  className="text-medium-gray mx-auto h-6 w-6"
                                  aria-hidden="true"
                                />
                                <p className="font-body mt-4">
                                  Help with searching
                                </p>
                                <p className="text-medium-dark-gray mt-2">
                                  Use this tool to quickly search for campaigns
                                  and posts across our entire platform. You can
                                  also use the search modifiers found in the
                                  footer below to limit the results to just
                                  campaigns or posts.
                                </p>
                              </div>
                            )}

                            <div className="bg-lightest-gray-30 font-body-xs flex flex-wrap items-center gap-1 px-4 py-2.5">
                              Type <Kbd>&gt;</Kbd>
                              <span className="sm:hidden">
                                to search posts,
                              </span>
                              <span className="hidden sm:inline">
                                to search posts,
                              </span>{" "}
                              <Kbd>#</Kbd> for campaigns,
                              <Kbd>:</Kbd> for ideas, <Kbd>?</Kbd> for help.
                            </div>
                          </>
                        )}

                        {query !== "" &&
                          !postQuery.isLoading &&
                          posts.length === 0 && (
                            <div className="px-6 py-14 text-center text-sm sm:px-14">
                              <UsersIcon
                                className="mx-auto h-6 w-6 text-gray-400"
                                aria-hidden="true"
                              />
                              <p className="mt-4 font-semibold text-gray-900">
                                No records found
                              </p>
                              <p className="mt-2 text-gray-500">
                                We couldn't find anything with that term. Please
                                try again.
                              </p>
                            </div>
                          )}
                      </>
                    );
                  }}
                </Combobox>
              </AnimateChangeInHeight>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default CommandPalette;
