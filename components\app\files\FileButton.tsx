import { useEffect, useState } from "react";

import { trpc } from "~/utils";

import { toast } from "sonner";

import Uppy from "@uppy/core";
import { FileInput } from "@uppy/react";
import assert from "assert";
import supabasePublic from "~/clients/supabasePublic";
import { ONE_YEAR_IN_SECONDS } from "~/constants";
import { useWorkspace } from "~/providers";
import Asset from "./Asset";

type Asset = {
  id: string;
  name: string;
  path: string;
  signedUrl: string;
  sizeMB: number;
  sizeKB: number;
  metadata: {
    mimetype: string;
  };
  createdAt: Date;
};

type Props = {
  assets: Asset[];
  contentID: string;
  isReadOnly?: boolean;
};

const FileButton = ({
  assets: initialAssets,
  contentID,
  isReadOnly = false,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const [assets, setAssets] = useState<Asset[]>(initialAssets);
  const [uploading, setUploading] = useState<boolean>(false);
  const [refetching, setRefetching] = useState<boolean>(false);
  const assetQuery = trpc.asset.getAll.useQuery({
    contentID,
  });

  const assetData = assetQuery.data;
  useEffect(() => {
    if (assetData) {
      if (refetching) {
        setRefetching(false);
        setAssets(assetData.assets);
      }

      if (
        !isReadOnly &&
        uploading &&
        assetData.assets.length !== assets.length
      ) {
        setUploading(false);
        setAssets(assetData.assets);
      }
    }
  }, [assetData]);

  const uppy = new Uppy({
    restrictions: { maxNumberOfFiles: 4 },
    autoProceed: true,
  });

  uppy.on("file-added", async (file) => {
    assert(currentWorkspace);

    setUploading(true);
    const response = await supabasePublic.storage
      .from("assets")
      .upload(`${currentWorkspace.id}/${contentID}/${file.name}`, file.data, {
        cacheControl: `${ONE_YEAR_IN_SECONDS}`,
      });
    if (response.error) {
      toast.error("Error Uploading File", {
        description: response.error.message,
      });
    } else {
      assetQuery.refetch();
    }
  });

  return (
    <div className="mt-4">
      {!isReadOnly && (
        <FileInput
          uppy={uppy}
          locale={{
            strings: {
              chooseFiles: uploading ? "Uploading..." : "Choose Files",
            },
          }}
        />
      )}
      <div className="grid grid-cols-3 gap-4">
        {assets.map((asset) => {
          return (
            <Asset
              key={asset.id}
              asset={asset}
              isReadOnly={isReadOnly}
              refetchAssets={() => {
                setRefetching(true);
                assetQuery.refetch();
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export default FileButton;
