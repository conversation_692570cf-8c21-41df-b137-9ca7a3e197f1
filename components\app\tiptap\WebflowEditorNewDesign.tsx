import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Blockquote from "@tiptap/extension-blockquote";
import Bold from "@tiptap/extension-bold";
import BulletList from "@tiptap/extension-bullet-list";
import Code from "@tiptap/extension-code";
import CodeBlock from "@tiptap/extension-code-block";
import Document from "@tiptap/extension-document";
import Heading, { Level } from "@tiptap/extension-heading";
import History from "@tiptap/extension-history";
import Image from "@tiptap/extension-image";
import Italic from "@tiptap/extension-italic";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import Underline from "@tiptap/extension-underline";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import { DateTime } from "luxon";
import { useEffect, useRef, useState } from "react";
import { useAutosave } from "react-autosave";
import { toast } from "sonner";
import TextDirection from "tiptap-text-direction";
import Turndown from "turndown";
import {
  CustomCommentThreadMark,
  Emoji,
  ImageBlock,
  ImageUpload,
  Link,
  PasteHandler,
} from "~/components/app/tiptap/extensions";
import SlashCommand from "~/components/app/tiptap/extensions/SlashCommand";
import { ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import {
  usePostIntegrations,
  useThreadComments,
  useWorkspace,
} from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import {
  handleFilesUpload,
  handleTrpcError,
  stripLiveblocksMarks,
  trpc,
} from "~/utils";
import getFileProperties from "~/utils/getFileProperties";
import ThreadCommentsPopover from "../posts/ThreadCommentsPopover";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  contentID: string;
  title: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string, htmlContent: string) => void;
  isReadOnly?: boolean;
  roomID: string;
};

const createBaseExtensions = (placeholder: string) => [
  Document,
  Paragraph,
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  Text,
  Heading.configure({
    levels: headingLevels,
  }),
  History,
  BulletList,
  OrderedList,
  ListItem,
  Bold,
  Italic,
  Code,
  CodeBlock,
  Underline,
  Blockquote,
  Image,
  ImageBlock,
  Emoji,
  Link.configure({
    openOnClick: true,
  }),
  Placeholder.configure({
    includeChildren: true,
    showOnlyCurrent: true,
    showOnlyWhenEditable: false,
    placeholder: ({ editor }) => {
      if (!editor.isFocused) {
        return placeholder;
      } else {
        return "Type  /  to browse options";
      }
    },
  }),
  SlashCommand,
  PasteHandler,
];

type WebflowEditorFallbackProps = {
  content: JSONContent | null;
  placeholder: string;
};

const headingLevels: Level[] = [1, 2, 3, 4, 5, 6];

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const WebflowEditorFallback = ({
  content,
  placeholder,
}: WebflowEditorFallbackProps) => {
  const cleanContent = stripLiveblocksMarks(content);

  const editor = useEditor({
    extensions: createBaseExtensions(placeholder),
    immediatelyRender: false,
    content: cleanContent,
    editable: false,
  });

  if (!editor) return null;

  return <EditorContent editor={editor} />;
};

const WebflowEditor = ({
  contentID,
  title,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  isReadOnly,
  roomID,
}: BaseProps) => {
  const { currentWorkspace } = useWorkspace();
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );
  const { setEditorForRoom } = useThreadComments();
  const { currentChannel } = usePostIntegrations();

  const [uppyInstance, setUppyInstance] = useState<Uppy | null>(null);

  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const createInlineAssetMutation =
    trpc.webflowContent.createInlineAsset.useMutation();

  const uploadPromiseCallbacksRef = useRef<{
    resolve: (url: string) => void;
    reject: (error: Error) => void;
  } | null>(null);

  const handleImageUpload = async (file: File) => {
    if (!uppyInstance) {
      throw new Error("Upload instance not ready");
    }

    const uploadPromise = new Promise<string>((resolve, reject) => {
      uploadPromiseCallbacksRef.current = { resolve, reject };
    });

    await handleFilesUpload({
      files: [file],
      uppy: uppyInstance,
      meta: {
        contentID,
      },
      getValidFiles: async (files) => files,
    });

    const url = await uploadPromise;
    uploadPromiseCallbacksRef.current = null;
    return url;
  };

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppy = new Uppy({
        restrictions: {
          maxNumberOfFiles: 1,
        },
        autoProceed: true,
      }).use(Tus, options);

      uppy.on("file-added", async (file) => {
        const storagePath = `${
          currentWorkspace!.id
        }/inline-images/webflow/${DateTime.now().toFormat(
          "yyyy-MM-dd/'T'HH-mm-ss-SSS"
        )}`;
        const objectName = `${storagePath}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          storagePath,
          contentType: file.type,
          cacheControl: ONE_YEAR_IN_SECONDS,
        };

        return file;
      });

      uppy.on("complete", async (data) => {
        const fileNames = data.successful!.map((file) => file.name!);
        const filesWithProperties = await getFileProperties(
          data.successful!.map((file) => ({
            name: file.name!,
            data: file.data!,
            mimetype: file.meta.type as string,
          }))
        );
        const storagePath = data.successful![0].meta.storagePath as string;
        getNewAssetsMutation.mutate(
          {
            path: storagePath,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const newAssetWithProperties = data.assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );
                if (!assetProperties) {
                  return {
                    ...asset,
                    md5Hash: "",
                    width: null,
                    height: null,
                    duration: null,
                    size: asset.sizeBytes,
                  };
                } else {
                  return {
                    ...asset,
                    md5Hash: assetProperties.md5Hash,
                    width: assetProperties.width,
                    height: assetProperties.height,
                    duration: assetProperties.duration,
                    size: asset.sizeBytes,
                  };
                }
              });

              createInlineAssetMutation.mutate(
                {
                  asset: newAssetWithProperties[0],
                  contentID,
                },
                {
                  onSuccess: (data) => {
                    const assetUrl = `${process.env.NEXT_PUBLIC_DOMAIN}/api/assets/${data.asset.id}`;
                    if (uploadPromiseCallbacksRef.current) {
                      uploadPromiseCallbacksRef.current.resolve(assetUrl);
                    }
                  },
                  onError: (error) => {
                    handleTrpcError({
                      title: "Error Creating Asset",
                      error,
                    });
                  },
                }
              );
            },
            onError: (error) => {
              if (uploadPromiseCallbacksRef.current) {
                uploadPromiseCallbacksRef.current.reject(
                  new Error("Failed to get asset URL")
                );
              }
            },
          }
        );
      });

      uppy.on("error", (error) => {
        if (uploadPromiseCallbacksRef.current) {
          uploadPromiseCallbacksRef.current.reject(error);
        }
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      setUppyInstance(uppy);
    };

    initUppy();

    return () => {
      if (uppyInstance) {
        uppyInstance.destroy();
      }
    };
  }, []);

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const turndown = new Turndown({
    headingStyle: "atx",
    emDelimiter: "_",
    bulletListMarker: "-",
  });
  turndown.addRule("underline", {
    filter: ["u"],
    replacement: (content: string) => {
      return `__${content}__`;
    },
  });

  const editor = useEditor(
    {
      extensions: [
        liveblocksExtension,
        CustomCommentThreadMark,
        ...createBaseExtensions(placeholder),
        ImageUpload.configure({
          onUpload: (file) => handleImageUpload(file),
        }),
      ],
      immediatelyRender: false,
      editable: !isReadOnly,
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        const markdown = turndown.turndown(html);
        onUpdateContent(markdown, html);

        const json = editor.getJSON();
        setEditorState(json);
      },
    },
    [uppyInstance]
  );

  const isEditorReady = useIsEditorReady();

  useEffect(() => {
    if (editor && currentChannel === "Webflow") {
      setEditorForRoom(editor, roomID);
    }

    return () => {
      setEditorForRoom(null, roomID);
    };
  }, [editor, currentChannel, setEditorForRoom, roomID]);

  if (!editor) return null;

  return (
    <div className="py-2">
      <CustomFloatingToolbar editor={editor}>
        <ToolbarActions
          editor={editor}
          features={{
            headings: headingLevels,
            bulletList: true,
            orderedList: true,
            link: true,
          }}
        />
      </CustomFloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}

      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["Webflow"].label,
            }}
          />
        </>
      ) : (
        <WebflowEditorFallback
          content={initialValue}
          placeholder={placeholder}
        />
      )}
    </div>
  );
};

const WebflowEditorWrapperNewDesign = ({
  contentID,
  title,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  isReadOnly,
  roomID,
}: BaseProps) => {
  const { currentChannel } = usePostIntegrations();
  const threadCommentsContainerRef = useRef<HTMLDivElement>(null);

  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense
        fallback={
          <div className="py-2">
            {title && (
              <div className="font-eyebrow text-dark-gray">{title}</div>
            )}
            <WebflowEditorFallback
              content={initialValue}
              placeholder={placeholder}
            />
          </div>
        }
      >
        <div className="relative">
          <WebflowEditor
            contentID={contentID}
            title={title}
            initialValue={initialValue}
            placeholder={placeholder}
            onSave={onSave}
            onUpdateContent={onUpdateContent}
            isReadOnly={isReadOnly}
            roomID={roomID}
          />
          <div ref={threadCommentsContainerRef} />
        </div>
      </ClientSideSuspense>
      {currentChannel === "Webflow" && (
        <ThreadCommentsPopover
          key={roomID}
          roomID={roomID}
          containerRef={threadCommentsContainerRef}
        />
      )}
    </RoomProvider>
  );
};

export default WebflowEditorWrapperNewDesign;
