import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import { db } from "~/clients";
import knock from "~/clients/Knock";
import { getFullName } from "~/utils";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const email = req.query.email as string;

  const user = await db.user.findFirstOrThrow({
    where: {
      email,
    },
  });

  await knock.users.identify(user.id, {
    name: getFullN<PERSON>(user),
    email: user.email.toLowerCase(),
  });
  await knock.users.merge(user.id, user.email.toLowerCase());

  res.status(StatusCodes.OK).send({ user });
};

export default handler;
