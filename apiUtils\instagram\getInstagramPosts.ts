import { InstagramIntegrationSource } from "@prisma/client";
import getAssetBuffer from "apiUtils/getAssetBuffer";
import { decode } from "base64-arraybuffer";

import { prisma } from "~/clients";
import Instagram from "~/clients/facebook/instagram/Instagram";
import supabaseAdminClient from "~/clients/supabaseAdminClient";

const getInstagramPosts = async ({
  integration,
  since,
}: {
  integration: {
    account: {
      id: string;
      externalID: string;
      username: string;
    };
    accessToken: string;
    integrationSource: InstagramIntegrationSource;
  };
  since: number;
}) => {
  try {
    const instagram = new Instagram(integration.accessToken, {
      platform: integration.integrationSource,
    });

    const postResponse = await instagram.posts.getForAccount(
      integration.account.externalID,
      {
        since,
      }
    );

    if (postResponse.errors) {
      try {
        console.error(
          `Error getting posts ${
            integration.account.username
          }: ${postResponse.errors.join(",")}`
        );
        console.error(
          `Error getting posts ${
            integration.account.username
          }: ${JSON.stringify(postResponse.errors)}`
        );
      } catch (error) {
        console.error(error);
      }
      return 0;
    }

    // Download the thumbnailUrl image to our storage
    const thumbnailUrlPromises = [...postResponse.data]
      .reverse()
      .map(async (post) => {
        try {
          const assetBuffer = await getAssetBuffer(post.thumbnailUrl);
          const uploadResponse = await supabaseAdminClient.storage
            .from("instagram_grids")
            .upload(
              `${integration.account.externalID}/${post.shortcode}.jpg`,
              decode(assetBuffer.toString("base64")),
              {
                cacheControl: "max-age=********",
                upsert: true,
                contentType: "image/jpeg",
              }
            );

          if (uploadResponse.data) {
            const { path } = uploadResponse.data;
            const {
              data: { publicUrl },
            } = await supabaseAdminClient.storage
              .from("instagram_grids")
              .getPublicUrl(path);

            return publicUrl;
          } else {
            return post.thumbnailUrl;
          }
        } catch (error) {
          throw new Error(
            `@${integration.account.username} Unable to download thumbnailUrl from ${post.thumbnailUrl}`
          );
        }
      });

    const thumbnailUrls = await Promise.all(thumbnailUrlPromises);
    const postData = postResponse.data.map((post, index) => {
      // Need to remove data that's not saved in the database
      const { username, ...rest } = post;

      return {
        ...rest,
        thumbnailUrl: thumbnailUrls[postResponse.data.length - index - 1],
      };
    });

    await prisma.instagramAccount.update({
      where: {
        id: integration.account.id,
      },
      data: {
        posts: {
          upsert: postData.map((post) => ({
            where: {
              id: post.id,
            },
            create: {
              id: post.id,

              shortcode: post.shortcode,
              permalink: post.permalink,

              mediaType: post.mediaType,
              mediaProductType: post.mediaProductType,

              caption: post.caption,
              hashtags: post.hashtags,
              hashtagCount: post.hashtagCount,
              captionMentions: post.captionMentions,
              captionMentionCount: post.captionMentionCount,
              reelSharedToFeed: post.reelSharedToFeed,

              thumbnailUrl: post.thumbnailUrl,
              imageUrls: post.imageUrls,
              videoUrls: post.videoUrls,

              impressionCount: post.impressionCount,
              reachCount: post.reachCount,
              savedCount: post.savedCount,
              likeCount: post.likeCount,
              commentCount: post.commentCount,
              profileVisitCount: post.profileVisitCount,
              shareCount: post.shareCount,
              followCount: post.followCount,

              publishedAt: post.publishedAt,
              rawResponse: post.rawResponse,
            },
            update: {
              caption: post.caption,
              thumbnailUrl: post.thumbnailUrl,
              impressionCount: post.impressionCount,
              reachCount: post.reachCount,
              savedCount: post.savedCount,
              likeCount: post.likeCount,
              commentCount: post.commentCount,
              profileVisitCount: post.profileVisitCount,
              shareCount: post.shareCount,
              followCount: post.followCount,
              rawResponse: post.rawResponse,
            },
          })),
        },
      },
    });

    // Delete any missing posts
    // await db.instagramPost.deleteMany({
    //   where: {
    //     accountID: integration.instagramAccountID,
    //     NOT: {
    //       id: {
    //         in: postData.map((post) => post.id),
    //       },
    //     },
    //     publishedAt: {
    //       gte: new Date(since),
    //     },
    //   },
    // });

    const posts = await prisma.post.findMany({
      where: {
        OR: [
          {
            link: {
              in: postData.map((post) => post.permalink),
            },
          },
          {
            instagramLink: {
              in: postData.map((post) => post.permalink),
            },
          },
        ],
      },
    });

    const promises = posts.map(async (post) => {
      const shortcode = post.link
        ?.replace("https://www.instagram.com/p/", "")
        .replace("https://www.instagram.com/reel/", "")
        .split("/")[0];

      if (!shortcode) {
        return;
      } else {
        await prisma.post.update({
          where: {
            id: post.id,
          },
          data: {
            instagramPost: {
              connect: {
                shortcode,
              },
            },
          },
        });
      }
    });

    await Promise.all(promises);

    return postResponse.data.length;
  } catch (error) {
    console.error("getInstagramPosts errored", error);
    return 0;
  }
};

export default getInstagramPosts;
