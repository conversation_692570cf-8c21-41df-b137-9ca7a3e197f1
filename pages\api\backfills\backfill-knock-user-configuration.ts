import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import { db } from "~/clients";
import knock from "~/clients/Knock";
import { SLACK_CHANNEL_ID } from "~/constants";

const sleep = (milliseconds: number) => {
  return new Promise((resolve) => setTimeout(resolve, milliseconds));
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const users = await db.user.findFirstOrThrow({
    where: {
      email: "<EMAIL>",
    },
  });

  const response = await knock.users.unsetChannelData(
    users.id,
    SLACK_CHANNEL_ID
  );

  res.status(StatusCodes.OK).send({ response });
};

export default handler;
