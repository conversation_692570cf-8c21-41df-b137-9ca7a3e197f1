import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { Carousel } from "react-responsive-carousel";
import { useWorkspace } from "~/providers";
import { getDefaultProfilePicture } from "~/utils";

type InstagramAccount = {
  id: string;
  name: string | null;
  profileImageUrl: string;
};

type Props = {
  instagramContent: {
    assets: {
      id: string;
      signedUrl: string;
      metadata: {
        mimetype: string;
      };
      createdAt: Date;
    }[];
  };
  connectedAccount: InstagramAccount | null | undefined;
};

const InstagramStoryPreview = ({
  instagramContent,
  connectedAccount,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const [isHovering, setIsHovering] = useState<boolean>(false);
  const { assets } = instagramContent;
  const nonEmpty = assets.length > 0;

  const accountName = connectedAccount?.name || currentWorkspace?.name;
  const profilePictureUrl =
    connectedAccount?.profileImageUrl ?? getDefaultProfilePicture("Instagram");

  if (!nonEmpty) {
    return (
      <div className="font-body text-medium-dark-gray mt-1 flex h-full items-center">
        Preview will show when post content is added below
      </div>
    );
  } else {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div
          className="relative h-[408px] w-[230px] overflow-clip rounded-md bg-black"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
        >
          <div className="absolute top-1.5 z-10 w-full px-1">
            <div className="flex items-center gap-px">
              {assets.map((asset, index) => {
                return (
                  <div
                    key={asset.id}
                    className="h-px w-full rounded-xs bg-white/50"
                  />
                );
              })}
            </div>
            <div className="mt-1 flex items-center gap-1">
              <img className="h-4 w-4 rounded-full" src={profilePictureUrl} />
              <div className="text-[8px] font-medium text-white">
                {accountName?.replace("@", "")}
              </div>
            </div>
          </div>
          <Carousel
            showThumbs={false}
            showStatus={false}
            showIndicators={false}
            renderArrowNext={(onClickHandler, hasNext, label) =>
              hasNext && (
                <button
                  type="button"
                  onClick={onClickHandler}
                  title={label}
                  className="bg-light-gray absolute top-1/2 right-2 z-10 -translate-y-1/2 transform rounded-full p-0.5 opacity-70"
                >
                  <ChevronRightIcon className="h-2 w-2" />
                </button>
              )
            }
            renderArrowPrev={(onClickHandler, hasPrev, label) =>
              hasPrev && (
                <button
                  type="button"
                  onClick={onClickHandler}
                  title={label}
                  className="bg-light-gray absolute top-1/2 left-2 z-10 -translate-y-1/2 transform rounded-full p-0.5 opacity-70"
                >
                  <ChevronLeftIcon className="h-2 w-2" />
                </button>
              )
            }
          >
            {assets.map((asset) => {
              if (asset.metadata.mimetype.startsWith("video")) {
                return (
                  <div
                    key={asset.signedUrl}
                    className="flex h-[408px] items-center justify-center"
                  >
                    <video
                      controls={isHovering}
                      className="max-h-[408px] w-full"
                      playsInline={true}
                    >
                      <source src={asset.signedUrl} />
                    </video>
                  </div>
                );
              } else {
                return (
                  <div
                    key={asset.signedUrl}
                    className="flex h-[408px] w-full items-center justify-center"
                  >
                    <img
                      className="max-h-[408px] object-cover"
                      src={asset.signedUrl}
                    />
                  </div>
                );
              }
            })}
          </Carousel>
        </div>
      </div>
    );
  }
};

export default InstagramStoryPreview;
