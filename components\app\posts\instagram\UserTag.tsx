import { motion } from "framer-motion";
import IconButton from "~/components/IconButton";
import TextLink from "~/components/TextLink";

type Props = {
  tag: {
    id: string;
    username: string;
  };
  onDeleteTagClick: () => void;
};

const UserTag = ({ tag, onDeleteTagClick }: Props) => {
  return (
    <motion.div
      key={tag.id}
      className="group relative flex w-fit items-center gap-1 rounded-lg bg-white px-2 py-1 shadow-sm"
      whileHover={{
        paddingRight: "25px",
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <TextLink
        size="sm"
        color="secondary"
        href={`https://instagram.com/${tag.username}`}
        value={tag.username}
      />
      <div className="absolute top-0.5 right-0.5 hidden translate-y-0.5 opacity-0 group-hover:block group-hover:opacity-100">
        <IconButton size="sm" icon="XMarkIcon" onClick={onDeleteTagClick} />
      </div>
    </motion.div>
  );
};

export default UserTag;
