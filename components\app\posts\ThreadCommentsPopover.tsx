import { ChatBubbleOvalLeftIcon } from "@heroicons/react/24/outline";
import { AnchoredThreads, FloatingThreads } from "@liveblocks/react-tiptap";
import { ClientSideSuspense, useThreads } from "@liveblocks/react/suspense";
import { useMemo } from "react";
import { createPortal } from "react-dom";
import { useBreakpoints } from "~/hooks";
import { useThreadComments } from "~/providers";

type ThreadCommentsPopoverProps = {
  containerRef: React.RefObject<HTMLDivElement>;
  roomID: string;
};

const ThreadCommentsContent = ({
  containerRef,
  roomID,
}: ThreadCommentsPopoverProps) => {
  const { getEditorForRoom } = useThreadComments();
  const currentEditor = getEditorForRoom(roomID);
  const { threads } = useThreads({ query: { resolved: false } });
  const { isDesktop } = useBreakpoints();

  // Memoizing portal to prevent unnecessary re-renders on editor onUpdate
  const memoizedPortal = useMemo(() => {
    if (
      !currentEditor ||
      !containerRef.current ||
      !threads ||
      threads.length === 0
    ) {
      return null;
    }

    if (!isDesktop) {
      return <FloatingThreads editor={currentEditor} threads={threads} />;
    }

    return createPortal(
      <div className="absolute right-0 w-4">
        <AnchoredThreads
          editor={currentEditor}
          threads={threads}
          className="ml-2 h-full w-10 xl:ml-5"
          components={{
            Thread: (props) => {
              const nonDeletedCommentsCount = props.thread.comments.filter(
                (comment) => !comment.deletedAt
              ).length;

              return (
                <div
                  {...props}
                  className="text-medium-dark-gray -mt-0.5 flex cursor-pointer items-center justify-center gap-1 transition-colors hover:text-black"
                >
                  <ChatBubbleOvalLeftIcon className="h-[18px] w-[18px]" />
                  {nonDeletedCommentsCount}
                </div>
              );
            },
          }}
        />
        <FloatingThreads
          editor={currentEditor}
          threads={threads}
          className="w-96"
        />
      </div>,
      containerRef.current
    );
  }, [currentEditor, containerRef.current, threads, isDesktop]);

  return memoizedPortal;
};

const ThreadCommentsPopover = (props: ThreadCommentsPopoverProps) => {
  if (!props.containerRef.current) {
    return null;
  }

  return createPortal(
    <ClientSideSuspense fallback={null}>
      <ThreadCommentsContent {...props} />
    </ClientSideSuspense>,
    props.containerRef.current
  );
};

export default ThreadCommentsPopover;
