import { v4 as uuidv4 } from "uuid";
import { Divider, TextLink } from "~/components";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import { useWindowTitle } from "~/hooks";
import { setCookie } from "~/utils/cookies";

export const SLACK_BOT_SCOPES = [
  "chat:write",
  "incoming-webhook",
  "chat:write.customize",
  "files:write",
];

export const SLACK_USER_SCOPES = [
  "chat:write",
  "users:read",
  "files:write",
  "files:read",
  "channels:read",
];

type Props = {
  type: "scheduling";
};

const handleSlackLogin = (accountID?: string) => {
  const state = uuidv4();

  setCookie(
    "SLACK_INTEGRATION",
    {
      state,
      accountID: undefined,
      referrer: window.location.pathname,
    },
    {
      path: "/",
      secure: true,
      sameSite: "lax",
      maxAge: 5 * 60, // 5 minutes in seconds
    }
  );

  const params = new URLSearchParams({
    client_id: `${process.env.NEXT_PUBLIC_SLACK_CLIENT_ID}`,
    scope: SLACK_BOT_SCOPES.join(","),
    user_scope: SLACK_USER_SCOPES.join(","),
    state,
    redirect_uri: `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/slack/callback`,
  });

  window.location.href = `https://slack.com/oauth/v2/authorize?${params.toString()}`;
};

const SlackConnectPage = ({ type }: Props) => {
  useWindowTitle("Slack Integration");

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      <ConnectProfile.Root>
        <ConnectProfile.Header
          channel="Slack"
          description="Connect your Slack profile to Assembly."
        />
        <ConnectProfile.Content>
          <Button size="sm" onClick={() => handleSlackLogin()}>
            Connect Slack
          </Button>
        </ConnectProfile.Content>
        <Divider className="my-2 md:my-4" />
        <ConnectProfile.Footer>
          <div className="space-y-1.5">
            <p className="font-medium">
              Looking to integrate Slack Notifications?
            </p>
            <p className="text-medium-dark-gray">
              This Slack scheduling integration allows you to connect a Slack
              community channel to schedule messages and announcements to.
              <br />
              <br />
              If you're looking instead to integrate Slack for notifications,
              check out the{" "}
              <TextLink
                href="/settings/integrations/slack"
                value="Slack Integrations"
              />{" "}
              page instead.
            </p>
          </div>
        </ConnectProfile.Footer>
      </ConnectProfile.Root>
    </SettingsLayout.Root>
  );
};

export default SlackConnectPage;
export { handleSlackLogin };
