import { useRouter } from "next/router";

import { useForm } from "react-hook-form";
import { FormAutosizeInput } from "~/components/form";
import FormDatePicker from "~/components/form/FormDatePicker";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { getDateUTC, handleTrpcError, trpc } from "~/utils";

type FormValues = {
  name: string;
  startDate: Date;
  endDate: Date;
};

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

const NewCampaignModal = ({ open, setOpen }: Props) => {
  const router = useRouter();
  const { control, handleSubmit } = useForm<FormValues>();

  const createCampaignMutation = trpc.campaign.create.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Error Creating Campaign", error });
    },
    onSuccess: (data) => {
      router.push(`/campaigns/${data.campaign.id}`);
    },
  });

  const createNewCampaign = (values: FormValues) => {
    const { name, startDate, endDate } = values;

    const startDateUTC = getDateUTC(startDate);
    const endDateUTC = getDateUTC(endDate);

    createCampaignMutation.mutate({
      name,
      startDate: startDateUTC,
      endDate: endDateUTC,
    });
  };

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header className="sr-only">
        <Credenza.Title>Create Campaign</Credenza.Title>
        <Credenza.Description>
          Create a new campaign with name, start and end date
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <form
          className="grid grid-cols-12 gap-x-4 gap-y-6"
          onSubmit={handleSubmit(createNewCampaign)}
        >
          <div className="col-span-12">
            <FormAutosizeInput
              control={control}
              name="name"
              placeholder="Enter Campaign Title"
              size="lg"
              rules={{ required: true }}
            />
          </div>
          <div className="col-span-6">
            <FormDatePicker
              control={control}
              name="startDate"
              label="Start Date"
              placeholder="M/DD/YYYY"
              rules={{ required: true }}
            />
          </div>
          <div className="col-span-6">
            <FormDatePicker
              control={control}
              name="endDate"
              label="End Date"
              placeholder="M/DD/YYYY"
              rules={{ required: true }}
            />
          </div>
          <Credenza.Footer className="col-span-12">
            <Button type="submit" isLoading={createCampaignMutation.isPending}>
              Create Campaign
            </Button>
          </Credenza.Footer>
        </form>
      </Credenza.Body>
    </Credenza.Root>
  );
};

export default NewCampaignModal;
