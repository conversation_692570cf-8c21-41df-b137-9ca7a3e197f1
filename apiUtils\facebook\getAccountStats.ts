import { DateTime } from "luxon";
import { prisma } from "~/clients";
import Graph from "~/clients/facebook/Graph";

type Integration = {
  id: string;
  accessToken: string;
  page: {
    id: string;
    name: string;
    externalID: string;
    stats: {
      followerCount: number | null;
      followCount: number | null;
      recordedAt: Date;
    }[];
  };
};

const getAccountStats = async (facebookIntegration: Integration) => {
  console.log(`${facebookIntegration.page.name} querying stats`);
  const FB = new Graph(facebookIntegration.accessToken).FB;
  let timeframeDays = 29;
  const stats = facebookIntegration.page.stats;

  // Get the highest count from all of the stats
  const followerCount = stats
    .map((stat) => stat.followerCount ?? 0)
    .sort((a, b) => b - a)[0];

  // Get the oldest stat that has a null follow count
  const oldestNullFollowCount = stats
    .filter((stat) => stat.followCount == null)
    .sort((a, b) => a.recordedAt.getTime() - b.recordedAt.getTime())[0];

  // If there is an old null follow count, then we need to backfill to that date
  if (oldestNullFollowCount != null) {
    timeframeDays = Math.ceil(
      Math.abs(
        DateTime.fromJSDate(oldestNullFollowCount.recordedAt).diffNow("days")
          .days
      )
    );
  }

  if (followerCount < 100) {
    timeframeDays = 1;
  }

  const [followsResponse, impressionsResponse] = await Promise.all([
    FB.getFollowsAndUnfollows({
      facebookPageID: facebookIntegration.page.externalID,
      timeframeDays,
    }),
    FB.getPageLikesAndImpressions({
      facebookPageID: facebookIntegration.page.externalID,
      timeframeDays,
    }),
  ]);

  if (followsResponse.data && impressionsResponse.data) {
    const followCount =
      followsResponse.data.followsAndUnfollows[
        followsResponse.data.followsAndUnfollows.length - 1
      ].followCount;
    const unfollowCount =
      followsResponse.data.followsAndUnfollows[
        followsResponse.data.followsAndUnfollows.length - 1
      ].unfollowCount;
    const followerCount = followsResponse.data.followerCount;

    console.log(
      `${facebookIntegration.page.name} followCount: ${followCount}, unfollowCount: ${unfollowCount}, followerCount: ${followerCount}`
    );

    const { count: numCreated } = await prisma.facebookPageStats.createMany({
      data: followsResponse.data.followsAndUnfollows.map((stat, index) => ({
        pageID: facebookIntegration.page.id,
        followCount: stat.followCount,
        unfollowCount: stat.unfollowCount,
        followerCount: followsResponse.data.followerCount,
        likeCount: impressionsResponse.data.likeCount,
        pageImpressions: impressionsResponse.data.pageImpressions,
        recordedAt: stat.recordedAt,
      })),
      skipDuplicates: true,
    });

    let index = 0;
    for await (const stat of followsResponse.data.followsAndUnfollows) {
      await prisma.facebookPageStats.upsert({
        where: {
          pageID_recordedAt: {
            pageID: facebookIntegration.page.id,
            recordedAt: stat.recordedAt,
          },
        },
        update: {
          pageID: facebookIntegration.page.id,
          followCount: stat.followCount,
          unfollowCount: stat.unfollowCount,
          followerCount: followsResponse.data.followerCount,
          likeCount: impressionsResponse.data.likeCount,
          pageImpressions: impressionsResponse.data.pageImpressions,
        },
        create: {
          pageID: facebookIntegration.page.id,
          followCount: stat.followCount,
          unfollowCount: stat.unfollowCount,
          followerCount: followsResponse.data.followerCount,
          likeCount: impressionsResponse.data.likeCount,
          pageImpressions: impressionsResponse.data.pageImpressions,
          recordedAt: stat.recordedAt,
        },
      });
      index++;
    }

    return {
      name: facebookIntegration.page.name,
      error: null,
      count: numCreated,
    };
  } else {
    console.log(`${facebookIntegration.page.name} error getting account stats`);
    console.log(followsResponse.errors || impressionsResponse.errors);

    if (followsResponse.isAuthError) {
      await prisma.facebookIntegration.update({
        where: {
          id: facebookIntegration.id,
        },
        data: {
          isReintegrationRequired: true,
        },
      });
    }
    return {
      name: facebookIntegration.page.name,
      error: followsResponse.errors || impressionsResponse.errors,
      count: 0,
    };
  }
};

export default getAccountStats;
