import { ChatPromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";
import { WorkspaceOnboardingTaskState } from "@prisma/client";
import { slugify } from "inngest";
import { DateTime } from "luxon";
import { z } from "zod";
import { db, slack } from "~/clients";
import completeOnboardingTask from "../../apiUtils/completeOnboardingTask";
import getTextFromUrl from "../../apiUtils/getTextFromUrl";
import { inngest } from "./inngest";
// @ts-ignore
import extractor from "unfluff";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const RETRIES = 4;

export default inngest.createFunction(
  {
    id: slugify("Notify of New Workspace"),
    retries: RETRIES,
    name: "Notify of New Workspace",
  },
  [
    {
      event: "workspace.created",
    },
  ],
  async ({ event, step, attempt }) => {
    const { workspaceID } = event.data;

    const result = await step.run("Notify of New Workspace", async () => {
      const workspace = await db.workspace.findFirst({
        where: {
          id: workspaceID,
        },
        select: {
          id: true,
          name: true,
          companyID: true,
          createdBy: {
            select: {
              id: true,
              email: true,
            },
          },
        },
      });

      if (workspace == null) {
        return {
          ignore: true,
          reason: "No workspace found",
        };
      }

      const emailsToIgnore = [
        "gmail.com",
        "yahoo.com",
        "hotmail.com",
        "outlook.com",
        "aol.com",
        "icloud.com",
        "mac.com",
        "me.com",
      ];

      const { name, createdBy } = workspace;
      const email = createdBy.email;
      if (
        emailsToIgnore.some((emailToIgnore) =>
          email.toLocaleLowerCase().endsWith(emailToIgnore)
        )
      ) {
        await slack.notifyOfNewWorkspace({
          workspace,
          result: null,
          url: null,
          userEmail: email,
        });
        return {
          ignore: true,
          email,
          reason: "Email is in the ignore list",
        };
      }

      const url = `https://www.${email.split("@")[1]}`;
      let text = null;
      try {
        text = await getTextFromUrl(url);

        if (text == null || text == "") {
          const response = await fetch(url);

          let html = await response.text();
          const data = extractor(html);
          text = data.text;
        }
      } catch (error) {
        try {
          const response = await fetch(url);

          let html = await response.text();
          const data = extractor(html);
          text = data.text;
        } catch (error) {
          console.error(error);
        }
      }

      if (text == null || text == "") {
        return await slack.notifyOfNewWorkspace({
          workspace,
          url,
          result: null,
          userEmail: email,
        });
      }

      function escapeTemplateVariables(text: string): string;
      function escapeTemplateVariables(text: null | undefined): null;
      function escapeTemplateVariables(
        text: string | null | undefined
      ): string | null;
      function escapeTemplateVariables(
        text: string | null | undefined
      ): string | null {
        if (text == null) {
          return null;
        }
        return text.replace(/\{(\w+)\}/g, "($1)");
      }

      const promptTemplate = ChatPromptTemplate.fromMessages([
        [
          "system",
          "You are an assistant that determines the type of business based on the scraped text from the webiste. Categories are Agency, B2B SaaS, B2C SaaS, E-Commerce or Other. Use the text I pass in along with the name of the company to make an informed decision on the type and a short description of the business type.",
        ],
        [
          "user",
          `The company name is "${name}" and the text is "${escapeTemplateVariables(
            text
          )}". Determine their business type.`,
        ],
      ]);

      const llm = new ChatOpenAI({
        modelName: "gpt-4o",
        temperature: 0.3,
        cache: false,
        streaming: false,
      });

      const structuredLlm = llm.withStructuredOutput(
        z.object({
          businessType: z.string().describe("The business type of the company"),
          description: z
            .string()
            .describe("A short description for the business type"),
        }),
        {
          name: "businessType",
        }
      );

      const chain = promptTemplate.pipe(structuredLlm);

      const result = await chain.invoke({});

      await slack.notifyOfNewWorkspace({
        workspace,
        result,
        url,
        userEmail: email,
      });

      return {
        result,
      };
    });

    await step.sleepUntil(
      "Wait for Onboarding Timeout",
      DateTime.now().plus({ months: 2 }).toJSDate()
    );
    const timeoutResult = await step.run(
      "Mark Incomplete Tasks as Timed Out",
      async () => {
        const workspace = await db.workspace.findFirstOrThrow({
          where: {
            id: workspaceID,
          },
          select: {
            id: true,
            createdBy: true,
          },
        });

        const activeTasks = await db.workspaceOnboardingTask.findMany({
          where: {
            workspaceID: workspaceID,
            state: WorkspaceOnboardingTaskState.ACTIVE,
          },
          select: {
            type: true,
          },
        });

        const taskCompletionPromises = activeTasks.map(async (task) =>
          completeOnboardingTask({
            userID: workspace.createdBy.id,
            workspaceID,
            taskType: task.type,
            state: WorkspaceOnboardingTaskState.TIMED_OUT,
          })
        );

        await Promise.all(taskCompletionPromises);

        posthog.capture({
          distinctId: workspace.createdBy.id,
          event: POSTHOG_EVENTS.onboarding.allTasksExpired,
          groups: {
            workspace: workspaceID,
          },
        });

        return {
          tasksUpdated: activeTasks.length,
          workspaceID,
        };
      }
    );

    return {
      notification: result,
      timeout: timeoutResult,
    };
  }
);
