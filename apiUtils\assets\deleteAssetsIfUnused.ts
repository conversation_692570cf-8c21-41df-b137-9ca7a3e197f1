import { db } from "~/clients";
import supabaseAdminClient from "~/clients/supabaseAdminClient";

// If the asset is no longer used we can delete from the database & from storage
const deleteAssetsIfUnused = async (assetIDs: string[]) => {
  const assets = await db.asset.findMany({
    where: {
      id: {
        in: assetIDs,
      },
    },
    include: {
      _count: {
        select: {
          discordAssets: true,
          facebookAssets: true,
          facebookCoverPhotos: true,
          instagramAssets: true,
          instagramCoverPhotos: true,
          linkedInAssets: true,
          linkedInCaptions: true,
          linkedInCoverPhotos: true,
          slackAssets: true,
          threadsAssets: true,
          tweetAssets: true,
          tikTokAsset: true,
          webflowAssets: true,
          youTubeShortsAssets: true,
          postAssets: true,
        },
      },
    },
  });

  if (assets.length === 0) {
    // Most likely the assets were already deleted
    return;
  }

  const unusedAssets = assets.filter((asset) => {
    const isAssetUsed = Object.values(asset._count).some((count) => count > 0);
    return !isAssetUsed;
  });

  if (unusedAssets.length === 0) {
    return;
  } else {
    // Delete from storage
    await supabaseAdminClient.storage
      .from("assets")
      .remove(unusedAssets.map((asset) => asset.path));

    // Delete from database
    await db.asset.deleteMany({
      where: {
        id: {
          in: unusedAssets.map((asset) => asset.id),
        },
      },
    });
  }
};

export default deleteAssetsIfUnused;
