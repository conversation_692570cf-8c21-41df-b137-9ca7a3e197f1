import assert from "assert";
import { StatusCodes } from "http-status-codes";
import uniq from "lodash/uniq";
import { NextApiRequest, NextApiResponse } from "next";
import { db, prisma } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const contents = await db.linkedInContent.findMany({
    where: {
      copy: {
        contains: "urn:li",
      },
      post: {
        publishAt: {
          not: null,
        },
      },
    },
    select: {
      id: true,
      postID: true,
      copy: true,
      post: {
        select: {
          workspaceID: true,
          createdByID: true,
          publishAt: true,
        },
      },
    },
    take: 2000,
    orderBy: {
      createdAt: "desc",
    },
  });

  const contentWithMentions = contents.map((content) => {
    const mentions = uniq(content.copy.match(/urn:li:\w+:\w+/g) || []);
    return {
      ...content,
      mentions,
    };
  });

  const linkedInAccounts = await db.linkedInAccount.findMany({
    where: {
      urn: {
        in: contentWithMentions.map((content) => content.mentions).flat(),
      },
    },
    select: {
      id: true,
      urn: true,
    },
  });

  const data = contentWithMentions
    .map((content) => {
      const mentions = linkedInAccounts.filter((account) =>
        content.mentions.includes(account.urn)
      );

      const publishAt = content.post.publishAt;
      assert(publishAt != null, "publishAt is null");

      return mentions.map((mention) => ({
        accountID: mention.id,
        workspaceID: content.post.workspaceID,
        userID: content.post.createdByID,
        mentionedAt: publishAt,
      }));
    })
    .flat();

  await prisma.linkedInAccountWorkspaceMentions.createMany({
    data,
  });

  res.status(StatusCodes.OK).send({ numMentions: data.length });
};

export default handler;
