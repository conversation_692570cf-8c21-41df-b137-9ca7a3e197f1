import { Extension } from "@tiptap/core";

export interface BackspaceOnEmptyOptions {
  onBackspace: () => void;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    backspaceOnEmpty: {
      /**
       * Handle backspace on empty editor
       */
      handleBackspace: () => ReturnType;
    };
  }
}

export const BackspaceOnEmpty = Extension.create<BackspaceOnEmptyOptions>({
  name: "backspaceOnEmpty",

  addOptions() {
    return {
      onBackspace: () => {},
    };
  },

  addKeyboardShortcuts() {
    return {
      Backspace: ({ editor }) => {
        if (editor.state.doc.textContent.length === 0) {
          this.options.onBackspace();
          return true;
        }
        return false;
      },
    };
  },
});

export default BackspaceOnEmpty;
