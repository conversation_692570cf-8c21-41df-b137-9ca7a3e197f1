import { google } from "googleapis";
import { db } from "~/clients";
import { capitalizeWords } from "~/utils";

type Integration = {
  id: string;
  accessToken: string;
  refreshToken: string;
  idToken: string;
  channel: {
    username: string;
  };
};

const refreshAccessToken = async (
  integration: Integration
): Promise<
  | {
      status: "Success";
      youTubeIntegration: Integration & {
        expiresAt: Date;
      };
      isAuthError?: never;
    }
  | {
      status: "Error";
      error: string;
      isAuthError: boolean;
    }
> => {
  try {
    console.log(
      `Refreshing YouTube auth token for ${integration.channel.username}`
    );
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXT_PUBLIC_DOMAIN}/settings/youtube/callback`
    );

    oauth2Client.setCredentials({
      access_token: integration.accessToken,
      refresh_token: integration.refreshToken,
      id_token: integration.idToken,
    });

    google.options({
      auth: oauth2Client,
    });

    const response = await oauth2Client.refreshAccessToken();
    const { credentials } = response;

    const youTubeIntegration = await db.youTubeIntegration.update({
      where: {
        id: integration.id,
      },
      data: {
        accessToken: credentials.access_token as string,
        expiresAt: new Date(credentials.expiry_date as number),
        refreshToken: credentials.refresh_token as string,
        idToken: credentials.id_token as string,
        scope: (credentials.scope as string).split(" "),
      },
      select: {
        id: true,
        accessToken: true,
        refreshToken: true,
        idToken: true,
        expiresAt: true,
        scope: true,
        channel: {
          select: {
            username: true,
          },
        },
      },
    });

    return {
      status: "Success",
      youTubeIntegration,
    };
  } catch (error) {
    const youtubeAxiosError = error as {
      response: {
        data: {
          error: string;
          error_description: string;
        };
      };
    };

    try {
      console.log("youtubeAxiosError", youtubeAxiosError.response.data);
      // @ts-ignore
      const errorMessage = youtubeAxiosError.response.data.error;

      if (errorMessage === "invalid_grant") {
        return {
          status: "Error",
          error: `Your social profile's connection to Assembly is expired. To fix this, select "Reconnect" next to the profile on the YouTube Shorts integrations page.`,
          isAuthError: true,
        };
      }

      return {
        status: "Error",
        error: capitalizeWords(errorMessage),
        isAuthError: false,
      };
    } catch (error) {
      return {
        status: "Error",
        error: "Error refreshing YouTube access token",
        isAuthError: false,
      };
    }
  }
};

export default refreshAccessToken;
