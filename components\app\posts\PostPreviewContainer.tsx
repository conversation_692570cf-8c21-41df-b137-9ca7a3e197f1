import { ReactNode } from "react";

import { Channel, ScheduledPostStatus } from "@prisma/client";
import { CHANNEL_ICON_MAP } from "~/types";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import ChannelIcon from "../ChannelIcon";
import SchedulePostAccountSelect, {
  ConnectedAccount,
} from "./SchedulePostAccountSelect";

export type ScheduledPost = {
  id: string;
  status: ScheduledPostStatus;
  channel: Channel;
  error: string | null;
  publishAt: Date;
  integrationAccountName: string;
};

type Props = {
  post: {
    id: string;
    channels: Channel[];
    title: string;
    campaignID: string | null;
    labels: {
      id: string;
      name: string;
      color: string;
      backgroundColor: string;
      createdAt: Date;
    }[];
  } & PostOrIdea;
  channel: Channel;
  additionalContent?: ReactNode;
  connectedAccount?: ConnectedAccount | null;
  setConnectedAccountID?: (integrationID: string) => void;
  accountOptions?: ConnectedAccount[];
  Preview?: ReactNode;
  isReadOnly?: boolean;
  hideSchedulePostButton?: boolean;
};

const PostPreviewContainer = ({
  post,
  channel,
  additionalContent,
  connectedAccount,
  setConnectedAccountID,
  accountOptions,
  Preview,
  isReadOnly,
  hideSchedulePostButton = false,
}: Props) => {
  return (
    <div>
      <div className="bg-lightest-gray-30 border-medium-gray/30 mb-4 rounded-xl border p-4 md:p-6">
        <div className="flex items-start justify-between md:items-center">
          <div className="flex items-center gap-2.5">
            <ChannelIcon key={channel} channel={channel} width={16} />
            <div className="font-body-sm mt-0.5 hidden font-medium md:block">
              {CHANNEL_ICON_MAP[channel].label} Post{" "}
              {channel !== "Webflow" && "Preview"}
            </div>
            {additionalContent}
          </div>
          {!hideSchedulePostButton && (
            <SchedulePostAccountSelect
              post={post}
              channel={channel}
              connectedAccount={connectedAccount ? connectedAccount : null}
              accountOptions={accountOptions}
              setConnectedAccountID={setConnectedAccountID}
              isReadOnly={isReadOnly}
            />
          )}
        </div>
        {Preview != null && <div className="mt-4 pt-2">{Preview}</div>}
      </div>
    </div>
  );
};

export default PostPreviewContainer;
