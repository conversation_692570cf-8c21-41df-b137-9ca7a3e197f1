import { useEffect } from "react";

import { Channel } from "@prisma/client";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { Badge, Divider } from "~/components";
import { FormAutosizeInput, FormChannelSelect } from "~/components/form";
import FormLabelSelect from "~/components/form/FormLabelSelect";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import Switch from "~/components/ui/Switch";
import { useCalendarView } from "~/providers";
import { handleTrpcError, trpc } from "~/utils";
import { Account } from "../calendar/AccountList";

type Campaign = {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
};

type Idea = {
  id: string;
  title: string;
  channels: Channel[];
  campaignID: string | null;
  campaign: Campaign | null;
  subscribers: {
    id: string;
    userID: string;
    user: {
      firstName: string | null;
      lastName: string | null;
    };
  }[];
  accounts: Account[];
  isIdea: boolean;
  labels: {
    id: string;
    name: string;
    color: string;
    backgroundColor: string;
  }[];
  createdAt: Date;
  createdBy: {
    id: string;
    firstName: string | null;
    lastName: string | null;
  };
};

type FormValues = {
  title: string;
  channels: Channel[];
  labels: {
    id: string;
    name: string;
    color: string;
    backgroundColor: string;
  }[];
};

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  onCreateSuccess: (idea: Idea) => void;
};

const NewIdeaModal = ({ open, setOpen, onCreateSuccess }: Props) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();
  const { openIdeaPage, setOpenIdeaPage } = useCalendarView();

  const labels = trpc.label.getAll.useQuery().data?.labels || [];

  const mostRecentlyUsedQuery = trpc.post.getMostRecentlyUsed.useQuery({
    isIdea: true,
  });

  const { control, handleSubmit, watch, reset, setFocus } =
    useForm<FormValues>();

  const createIdeaMutation = trpc.idea.create.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Error Creating Idea", error });
    },
    onSuccess: (data) => {
      trpcUtils.idea.getAll.invalidate();
      reset({
        title: "",
        channels: [],
        labels: [],
      });
      setOpen(false);
      if (openIdeaPage) {
        router.push(`/ideas/${data.idea.id}`);
      } else {
        onCreateSuccess({
          ...data.idea,
        });
        toast.success("Idea Created", {
          action: {
            label: "Click to View Idea",
            onClick: () => router.push(`/ideas/${data.idea.id}`),
          },
        });
      }
    },
  });

  const createNewPost = (values: FormValues) => {
    const { title, channels, labels } = values;

    const id = uuidv4();

    createIdeaMutation.mutate({
      id,
      title,
      channels: Array.isArray(channels) ? channels : [channels],
      labels,
    });
  };

  useEffect(() => {
    if (open) {
      setTimeout(() => {
        setFocus("title");
      }, 10);
    }
  }, [setFocus, open]);

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header className="sr-only">
        <Credenza.Title>Create New Idea</Credenza.Title>
        <Credenza.Description>
          Create a new idea with a title, channels, and labels
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <form
          className="grid grid-cols-12 gap-x-2 gap-y-5"
          onSubmit={handleSubmit(createNewPost)}
        >
          <div className="col-span-12">
            <FormAutosizeInput
              control={control}
              name="title"
              autoFocus={true}
              placeholder="Enter Idea Name"
              size="lg"
              rules={{ required: true }}
              showBorder={false}
            />
          </div>
          <div className="col-span-12 flex items-start gap-2">
            <FormChannelSelect
              control={control}
              name="channels"
              recentlyUsedChannels={
                mostRecentlyUsedQuery.data?.recentlyUsedChannels
              }
              rules={{ required: true }}
            />
            <Badge
              intent="none"
              size="md"
              kind="solid"
              tooltipText="Ideas cannot change status until they're added to the calendar."
              label="Idea"
              isUppercase={true}
              icon={
                <div className="bg-light-gray h-1.5 w-1.5 rounded-full"></div>
              }
            />
            <FormLabelSelect
              control={control}
              name="labels"
              options={labels}
              isOptional={true}
            />
          </div>
          <div className="col-span-12">
            <Divider padding="none" />
          </div>
          <Credenza.Footer className="col-span-12">
            <div className="flex items-center gap-2">
              <Switch
                checked={openIdeaPage}
                onCheckedChange={() => {
                  setOpenIdeaPage(!openIdeaPage);
                }}
              />
              <div className="font-body-sm text-medium-dark-gray">
                Open Idea Page
              </div>
            </div>
            <Button type="submit" isLoading={createIdeaMutation.isPending}>
              Create Idea
            </Button>
          </Credenza.Footer>
        </form>
      </Credenza.Body>
    </Credenza.Root>
  );
};

export default NewIdeaModal;
