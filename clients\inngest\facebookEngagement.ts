import { NonRetriableError, slugify } from "inngest";
import { db, slack } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import Graph from "../facebook/Graph";
import { inngest } from "./inngest";

export default inngest.createFunction(
  {
    id: slugify("Facebook Post Engagement"),
    name: "Facebook Post Engagement",
    onFailure: async ({ error, event }) => {
      const data = event.data.event.data;
      const runID = event.data.run_id;
      const { engagementID, permalink } = data;
      console.log(
        `Facebook Engagement:${engagementID}: Final Error ${error.message}`
      );

      const engagement = await db.facebookPostEngagement.findFirst({
        where: {
          id: engagementID,
        },
        select: {
          integration: {
            select: {
              page: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          content: {
            select: {
              post: {
                select: {
                  id: true,
                  title: true,
                  workspace: {
                    select: {
                      name: true,
                      company: {
                        select: { name: true },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!engagement) {
        console.error(
          `Facebook Engagement:${engagementID}: Engagement deleted`
        );
        return;
      } else {
        const page = engagement.integration.page;

        const profile = {
          name: page.name,
          url: `https://www.facebook.com/${page.id}`,
        };

        await slack.uncaughtFailedEngagement({
          channel: "Facebook",
          post: engagement.content.post,
          postUrl: permalink,
          workspace: engagement.content.post.workspace,
          engagementID,
          error: error.message,
          profile,
          runID,
        });
      }
    },
  },
  { event: "facebook.postEngagement" },
  async ({ event, step }) => {
    const {
      delayUntil,
      engagementID,
      externalPostID: facebookPagePostID,
      permalink,
    } = event.data;

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    await step.run("Post Facebook Post Engagement", async () => {
      console.log(`Facebook Engagement:${engagementID}: Finding record`);
      const engagement = await db.facebookPostEngagement.findFirst({
        where: {
          id: engagementID,
        },
        select: {
          id: true,
          type: true,
          copy: true,
          url: true,
          postDelaySeconds: true,
          content: {
            select: {
              post: {
                select: {
                  id: true,
                  createdByID: true,
                  workspace: {
                    select: { id: true, companyID: true },
                  },
                },
              },
            },
          },
          integration: {
            select: {
              accessToken: true,
            },
          },
        },
      });
      if (!engagement) {
        return {
          skipped: true,
          reason: "Engagement deleted",
        };
      }

      console.log(`Facebook Engagement:${engagementID}: Found record`);

      if (engagement.url != null) {
        console.log(
          `Facebook Engagement:${engagementID}: Already commented on post, skipping`
        );
        throw new NonRetriableError("Already commented on post");
      }

      const engagementClient = new Graph(engagement.integration.accessToken);

      const { copy } = engagement;

      if (!copy || copy.length === 0 || engagement.type === "Repost") {
        console.log(
          `Facebook Engagement:${engagementID}: Nothing to post, skipping`
        );
        return {
          skipped: true,
          reason: "Nothing to post",
        };
      } else if (engagement.type === "Like") {
        return {
          skipped: true,
          reason: "Like engagement not supported",
        };
      } else {
        console.log(`Facebook Engagement:${engagementID}: Creating comment`);

        const cleanedCopy = copy.replace(/@\[(.+?)\]\((\d+?)\)/g, (match) => {
          const pageID = match.match(/\((\d+?)\)/)?.[1];

          return `@[${pageID}]`;
        });

        const response = await engagementClient.FB.publishComment({
          pagePostID: facebookPagePostID,
          message: cleanedCopy,
        });

        if (response.errors) {
          const error = response.errors.join(", ");
          console.log(`Facebook Engagement:${engagementID}: Errored: ${error}`);
          throw new Error(error);
        } else {
          console.log(
            `Facebook Engagement:${engagementID}: Posted to ${permalink}`
          );
          await db.facebookPostEngagement.update({
            where: {
              id: engagement.id,
            },
            data: {
              url: permalink,
            },
          });
          posthog.capture({
            distinctId: engagement.content.post.createdByID,
            event: POSTHOG_EVENTS.engagement.published,
            properties: {
              channel: "Facebook",
              engagementType: engagement.type,
              postID: engagement.content.post.id,
              delaySeconds: engagement.postDelaySeconds,
            },
            groups: {
              workspace: engagement.content.post.workspace.id,
              company: engagement.content.post.workspace.companyID,
            },
          });
          return {
            url: permalink,
          };
        }
      }
    });
  }
);
