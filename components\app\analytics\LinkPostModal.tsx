import { Channel } from "@prisma/client";
import { DateTime } from "luxon";
import { useEffect, useState } from "react";
import { Table, TextLink } from "~/components";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { CHANNEL_ICON_MAP } from "~/types";
import { dateFromUTC, handleTrpcError, trpc } from "~/utils";

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  post: {
    id: string;
    instagramIntegrationID?: string;
    facebookIntegrationID?: string;
    linkedInIntegrationID?: string;
    channel: Channel;
    publishedAt: Date;
    username: string;
    permalink: string;
    copy: string;
  };
  refetchPosts: () => void;
};

type Post = {
  id: string;
  title: string;
  publishAt: Date;
  publishDate: Date;
};

const columns = [
  {
    id: "date",
    name: "Date",
    width: "75px",
    selector: (row: Post) =>
      DateTime.fromJSDate(dateFromUTC(row.publishDate)).toFormat("M/dd"),
  },
  {
    id: "title",
    name: "Title",
    selector: (row: Post) => row.title,
  },
  {
    id: "see_post",
    name: "See Post",
    width: "125px",
    selector: (row: Post) =>
      `${process.env.NEXT_PUBLIC_DOMAIN}/posts/${row.id}}`,
    cell: (row: Post) => (
      <TextLink
        href={`${process.env.NEXT_PUBLIC_DOMAIN}/posts/${row.id}`}
        value="See Post"
        size="sm"
        showLinkIcon={true}
      />
    ),
  },
];

const LinkPostModal = ({ open, setOpen, post, refetchPosts }: Props) => {
  const [error, setError] = useState<string | null>(null);
  const [selectedPostID, setSelectedPostID] = useState<string | null>(null);

  const postsQuery = trpc.post.getMany.useQuery(
    {
      channel: post.channel,
      publishDate: DateTime.fromJSDate(post.publishedAt)
        .startOf("day")
        .toJSDate(),
      instagramIntegrationID: post.instagramIntegrationID,
      facebookIntegrationID: post.facebookIntegrationID,
      linkedInIntegrationID: post.linkedInIntegrationID,
    },
    {
      enabled: open,
    }
  );

  const linkSocialPostMutation = trpc.post.linkSocialPost.useMutation({
    onSuccess: () => {
      refetchPosts();
      setSelectedPostID(null);
      setOpen(false);
    },
    onError: (error) => {
      handleTrpcError({
        title: `Unable to Link ${CHANNEL_ICON_MAP[post.channel].label} Post`,
        error,
      });
    },
  });

  useEffect(() => {
    setSelectedPostID(postsQuery.data?.posts[0]?.id || null);
  }, [postsQuery.data]);

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header>
        <Credenza.Title>Link to Assembly</Credenza.Title>
        <Credenza.Description>
          Linking to an Assembly post will automatically update the analytics
          view and the post's recap tab
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <div className="flex flex-col gap-4">
          <div className="space-y-2">
            <div className="mr-2 flex items-center justify-between">
              <div className="font-body">
                {CHANNEL_ICON_MAP[post.channel].label} Post
              </div>
              <TextLink
                href={post.permalink}
                value={`${CHANNEL_ICON_MAP[post.channel].label} Post`}
                size="sm"
                color="secondary"
                showLinkIcon={true}
              />
            </div>
            <div className="font-body-sm">{post.copy}</div>
          </div>
          <div
            style={{
              width: "106%",
            }}
            className="-translate-x-3.5"
          >
            <div className="font-body translate-x-3.5">Assembly Posts</div>
            <Table
              noTableHead={true}
              data={postsQuery.data?.posts}
              selectableRowsSingle={true}
              onSelectedRowsChange={({ selectedRows }) => {
                if (selectedRows.length > 0) {
                  setError(null);
                }
                setSelectedPostID(selectedRows[0]?.id || null);
              }}
              selectableRowSelected={(row) =>
                row.id === postsQuery.data?.posts[0]?.id
              }
              noDataComponent={
                <div className="px-10 text-center">
                  <div className="mb-2.5">No Assembly posts were found</div>
                  <div className="font-body text-medium-dark-gray">
                    Please create a new {CHANNEL_ICON_MAP[post.channel].label}{" "}
                    Assembly post on{" "}
                    {DateTime.fromJSDate(post.publishedAt).toFormat("M/dd")} for{" "}
                    {post.username} or modify an existing one in order to be
                    able to link this
                  </div>
                </div>
              }
              noDataComponentOffsetCenter={false}
              columns={columns}
            />
          </div>
        </div>
        {error != null && (
          <div className="font-body-sm text-danger">{error}</div>
        )}
        <Credenza.Footer>
          <Button
            variant="secondary"
            disabled={linkSocialPostMutation.isPending}
            onClick={() => {
              setSelectedPostID(null);
              setError(null);
              setOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            isLoading={linkSocialPostMutation.isPending}
            onClick={() => {
              if (selectedPostID != null) {
                linkSocialPostMutation.mutate({
                  id: selectedPostID,
                  socialPostID: post.id,
                  channel: post.channel,
                });
              } else {
                setError("Please select a post to link");
              }
            }}
          >
            Link Post
          </Button>
        </Credenza.Footer>
      </Credenza.Body>
    </Credenza.Root>
  );
};

export default LinkPostModal;
