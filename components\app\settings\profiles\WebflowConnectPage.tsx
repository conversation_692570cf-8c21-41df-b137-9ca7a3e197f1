import { v4 as uuidv4 } from "uuid";
import { Divider } from "~/components";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import { useWindowTitle } from "~/hooks";
import { openIntercomArticle } from "~/utils";
import { setCookie } from "~/utils/cookies";

type Props = {
  type: "scheduling";
};

const handleConnectWebflowCMS = () => {
  const state = uuidv4();

  setCookie(
    "WEBFLOW_INTEGRATION",
    {
      state,
      referrer: window.location.pathname,
    },
    {
      path: "/",
      secure: true,
      sameSite: "lax",
      maxAge: 5 * 60, // 5 minutes in seconds
    }
  );

  const params = new URLSearchParams({
    response_type: "code",
    client_id: `${process.env.NEXT_PUBLIC_WEBFLOW_CLIENT_ID}`,
    scope: [
      "assets:read",
      "assets:write",
      "authorized_user:read",
      "cms:read",
      "cms:write",
      "sites:read",
      "pages:read",
    ].join(" "),
    redirect_uri: `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/webflow/callback`,
    state,
  });

  window.location.href = `https://webflow.com/oauth/authorize?${params}`;
};

const WebflowConnectPage = ({ type }: Props) => {
  useWindowTitle("Webflow Integration");

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      <ConnectProfile.Root>
        <ConnectProfile.Header
          channel="Webflow"
          description="Connect your Webflow CMS to Assembly to start scheduling blog posts."
        />
        <ConnectProfile.Content>
          <Button size="sm" onClick={() => handleConnectWebflowCMS()}>
            Connect Webflow CMS
          </Button>
        </ConnectProfile.Content>
        <Divider className="my-2 md:my-4" />
        <ConnectProfile.Footer>
          <div className="space-y-1.5">
            <p className="font-medium">How does Webflow scheduling work?</p>
            <p className="text-medium-dark-gray">
              Our Webflow integration allows you to schedule and auto-publish
              updates to your Webflow CMS (whether it's a blog, directory, etc.)
              <br />
              <br />
              For more information on the details of how this works, check out
              our{" "}
              <Button
                size="sm"
                variant="link"
                onClick={() => {
                  openIntercomArticle("webflow_scheduling");
                }}
              >
                guide to Webflow scheduling
              </Button>
              .
            </p>
          </div>
        </ConnectProfile.Footer>
      </ConnectProfile.Root>
    </SettingsLayout.Root>
  );
};

export default WebflowConnectPage;
export { handleConnectWebflowCMS };
