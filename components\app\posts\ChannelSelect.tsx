import { AddNew<PERSON>utton, <PERSON>ge, Combobox } from "~/components";
import { getKeys } from "~/utils";

import { Channel } from "@prisma/client";
import { ReactElement, useMemo } from "react";
import { EDITOR_CHANNELS } from "~/constants";
import { CHANNEL_ICON_MAP } from "~/types";
import ChannelIcon from "../ChannelIcon";

type SingleProps = {
  value: Channel;
  onChange: (channel: Channel) => void;
  isMulti?: false;
};

type MultiProps = {
  value: Channel[];
  onChange: (channels: Channel[]) => void;
  isMulti: true;
};

type Props = {
  triggerButton?: ReactElement;
  isReadOnly?: boolean;
  ignoredChannels?: Channel[];
  recentlyUsedChannels?: Channel[];
  disabledChannel?: Channel | null;
  placeholder?: string;
} & (SingleProps | MultiProps);

const ChannelSelect = ({
  triggerButton,
  value,
  ignoredChannels = [],
  recentlyUsedChannels = [],
  onChange,
  isReadOnly = false,
  isMulti = false,
  disabledChannel,
  placeholder,
}: Props) => {
  let options = getKeys(CHANNEL_ICON_MAP)
    .filter((channel) => !ignoredChannels.includes(channel))
    .map((channel) => ({
      key: channel as string,
      value: channel,
      label: <ChannelIcon channel={channel} width={14} showLabel={true} />,
      disabled: channel === disabledChannel,
      disabledReason:
        channel === disabledChannel
          ? "You must have at least one channel for a post. To remove this, add another cross-post channel first."
          : undefined,
      component: (
        <Badge
          key={channel}
          icon={<ChannelIcon key={channel} channel={channel} width={14} />}
          isUppercase={true}
          intent="none"
          showHoverState={!isReadOnly}
          kind="solid"
          label={CHANNEL_ICON_MAP[channel].label}
        />
      ),
    }));

  const schedulableOptions = useMemo(() => {
    return EDITOR_CHANNELS.filter(
      (channel) =>
        !ignoredChannels.includes(channel) &&
        !recentlyUsedChannels.includes(channel)
    ).map((channel) => ({
      key: `${channel}-schedulable`,
      value: channel,
      label: <ChannelIcon channel={channel} width={14} showLabel={true} />,
      sectionName: "Schedulable Channels",
      disabled: channel === disabledChannel,
      disabledReason:
        channel === disabledChannel
          ? "You must have at least one channel for a post. To remove this, add another cross-post channel first."
          : undefined,
      component: (
        <Badge
          key={`${channel}-schedulable`}
          icon={<ChannelIcon key={channel} channel={channel} width={14} />}
          isUppercase={true}
          intent="none"
          showHoverState={!isReadOnly}
          kind="solid"
          label={CHANNEL_ICON_MAP[channel].label}
        />
      ),
    }));
  }, [ignoredChannels, recentlyUsedChannels, disabledChannel]);

  recentlyUsedChannels = recentlyUsedChannels.filter(
    (channel) => !ignoredChannels.includes(channel)
  );
  // If recenltlyUsedChannels is not empty, reorder the options so that the
  // recenlty used channels are at the top of the list and the rest have a sectionName of other channels
  const recentlyUsedOptions = recentlyUsedChannels.map((channel) => ({
    key: `${channel}-recently-used`,
    value: channel,
    label: <ChannelIcon channel={channel} width={14} showLabel={true} />,
    sectionName: "Recently Used",
    disabled: channel === disabledChannel,
    disabledReason:
      channel === disabledChannel
        ? "You must have at least one channel for a post. To remove this, add another cross-post channel first."
        : undefined,
    component: (
      <Badge
        key={`${channel}-recently-used`}
        icon={<ChannelIcon key={channel} channel={channel} width={14} />}
        isUppercase={true}
        intent="none"
        showHoverState={!isReadOnly}
        kind="solid"
        label={CHANNEL_ICON_MAP[channel].label}
      />
    ),
  }));
  const otherOptions = options
    .filter(
      (option) =>
        !recentlyUsedChannels.includes(option.value) &&
        !schedulableOptions.some(
          (schedulableOption) => schedulableOption.value === option.value
        )
    )
    .map((option) => ({
      ...option,
      sectionName: "Other Channels",
    }));

  options = [...recentlyUsedOptions, ...schedulableOptions, ...otherOptions];

  return (
    <Combobox
      placeholder={
        <AddNewButton
          label={placeholder ? placeholder : isMulti ? "Cross-Post" : "Channel"}
          isUppercase={true}
        />
      }
      triggerButton={triggerButton}
      disabled={isReadOnly}
      value={value}
      onChange={(channel) => onChange(channel)}
      isClearable={false}
      isUppercase={true}
      isMulti={isMulti}
      options={options}
    />
  );
};

export default ChannelSelect;
