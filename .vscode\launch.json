{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "bun start",
      "runtimeExecutable": "bun",
      "runtimeArgs": ["run", "start"],
      "cwd": "${workspaceRoot}",
      "timeout": 10000,
      "env": {
        "PATH": "${env:PATH}:${env:HOME}/.nvm/versions/node/v21.7.3/bin"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "bun dev",
      "runtimeExecutable": "bun",
      "runtimeArgs": ["run", "dev"],
      "cwd": "${workspaceRoot}",
      "console": "integratedTerminal",
      "timeout": 10000
      // "env": {
      //   "PATH": "${env:PATH}:${env:HOME}/.nvm/versions/node/v23.6.3/bin"
      // }
    }
  ]
}
