import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Divider } from "~/components";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import { FormTextInput } from "~/components/form";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import { useWindowTitle } from "~/hooks";
import { handleTrpcError, openIntercomArticle, trpc } from "~/utils";

export const SLACK_BOT_SCOPES = [
  "chat:write",
  "incoming-webhook",
  "chat:write.customize",
  "files:write",
];

export const SLACK_USER_SCOPES = [
  "chat:write",
  "users:read",
  "files:write",
  "files:read",
  "channels:read",
];

type Props = {
  type: "scheduling";
};

type FormValues = {
  channel: string;
  url: string;
};

const DiscordConnectPage = ({ type }: Props) => {
  useWindowTitle("Discord Integration");

  const router = useRouter();
  const { control, handleSubmit, reset } = useForm<FormValues>();

  const createWebhookMutation =
    trpc.integration.discord.createWebhook.useMutation({
      onSuccess: (data) => {
        reset({
          channel: "",
          url: "",
        });
        toast.success("Discord connected successfully", {
          description: `Connected ${data.webhook.channel}`,
        });
        router.push(`/settings/profiles`);
      },
      onError: (error, variables) => {
        handleTrpcError({
          title: "Error Saving Webhook",
          error,
        });
      },
    });

  const handleSaveWebhook = (values: FormValues) => {
    createWebhookMutation.mutate({
      channel: values.channel,
      url: values.url,
    });
  };

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      <ConnectProfile.Root>
        <ConnectProfile.Header
          channel="Discord"
          description="Connect your Discord webhook to Assembly."
        />
        <ConnectProfile.Content>
          <form onSubmit={handleSubmit(handleSaveWebhook)}>
            <div className="flex flex-col gap-4">
              <FormTextInput
                control={control}
                name="channel"
                label="Channel"
                placeholder="#announcements"
                kind="outline"
                rules={{ required: true }}
              />
              <FormTextInput
                control={control}
                name="url"
                label="Webhook URL"
                placeholder="Paste in webhook from Discord"
                kind="outline"
                rules={{
                  required: true,
                  pattern: {
                    value: /https:\/\/discord.com\/api\/webhooks\/\d+\/.*/,
                    message:
                      "URL must look like https://discord.com/api/webhooks/12345678/...",
                  },
                }}
              />
              <Button
                type="submit"
                size="sm"
                isLoading={createWebhookMutation.isPending}
              >
                Connect Discord
              </Button>
            </div>
          </form>
        </ConnectProfile.Content>
        <Divider className="my-2 md:my-4" />
        <ConnectProfile.Footer>
          <div className="space-y-1.5">
            <p className="font-medium">How to Generate a Webhook URL</p>
            <p className="text-medium-dark-gray">
              Click below for step-by-step instructions on how to generate a
              webhook URL.
            </p>
            <Button
              size="sm"
              variant="secondary"
              onClick={() => openIntercomArticle("discord_webhook_setup")}
            >
              View Instructions
            </Button>
          </div>
        </ConnectProfile.Footer>
      </ConnectProfile.Root>
    </SettingsLayout.Root>
  );
};

export default DiscordConnectPage;
