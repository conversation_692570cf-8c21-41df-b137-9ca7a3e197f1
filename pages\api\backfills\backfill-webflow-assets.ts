import getAssetBuffer from "apiUtils/getAssetBuffer";
import getFileHash from "apiUtils/getFileHash";
import getStorageAssetsForWebflow from "apiUtils/webflow/getStorageAssetsForWebflow";
import imageSize from "image-size";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.findMany({
    take: 500,
    where: {
      channels: {
        hasSome: ["Webflow"],
      },
    },
    select: {
      id: true,
      workspaceID: true,
      createdByID: true,
      webflowContent: {
        select: {
          id: true,
          fields: {
            select: {
              id: true,
            },
          },
        },
      },
    },
  });

  console.log(`Num posts to backfill ${posts.length}`);

  const postAssetPromises = posts.map(async (post) => {
    const webflowAssets = await getStorageAssetsForWebflow(post);

    return {
      ...post,
      webflowAssets,
    };
  });

  const postsWithAssets = await Promise.all(postAssetPromises);

  console.log("Got all assets for posts");

  const postsToBackfill = postsWithAssets.filter((post) => {
    return Object.values(post.webflowAssets).flat().length !== 0;
  });

  console.log(`Backfilling ${postsToBackfill.length} posts`);

  const getImageDimensions = async (
    asset: {
      mimetype: string;
      url: string;
    } | null
  ) => {
    try {
      if (asset && asset.mimetype.includes("image/")) {
        const buffer = await getAssetBuffer(asset.url);
        const dimensions = imageSize(buffer);
        const width = dimensions.width as number;
        const height = dimensions.height as number;
        const md5Hash = await getFileHash(buffer);
        return { width, height, md5Hash };
      } else {
        return {
          width: null,
          height: null,
          md5Hash: null,
        };
      }
    } catch (error) {
      return {
        width: null,
        height: null,
        md5Hash: null,
      };
    }
  };

  const allAssets = postsToBackfill.flatMap((post) => {
    const { webflowAssets } = post;

    return Object.values(webflowAssets)
      .flat()
      .map((asset) => ({
        ...asset,
        workspaceID: post.workspaceID,
        userID: post.createdByID,
      }));
  });

  const allAssetsWithDimensions = await Promise.all(
    allAssets.map(async (asset) => {
      const imageDimensions = await getImageDimensions(asset);

      return {
        ...asset,
        ...imageDimensions,
      };
    })
  );

  await db.asset.createMany({
    data: allAssetsWithDimensions.map((asset) => ({
      id: asset.id,
      name: asset.name,
      bucket: "assets",
      path: asset.path,
      eTag: asset.eTag,
      md5Hash: asset.md5Hash,
      mimetype: asset.mimetype,
      size: asset.sizeBytes,
      url: asset.url,
      width: asset.width,
      height: asset.height,
      urlExpiresAt: asset.urlExpiresAt,
      workspaceID: asset.workspaceID,
      userID: asset.userID,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
    })),
    skipDuplicates: true,
  });

  const backfillPromises = postsToBackfill.map(async (post) => {
    const { webflowAssets } = post;

    const updateInstagramAssetIDPromises = Object.keys(webflowAssets).map(
      async (fieldID) => {
        if (webflowAssets[fieldID].length > 0) {
          await db.webflowAsset.deleteMany({
            where: {
              webflowFieldID: fieldID,
            },
          });
          await db.webflowAsset.createMany({
            data: webflowAssets[fieldID].map((asset, index) => ({
              assetID: asset.id,
              webflowFieldID: fieldID,
              position: index,
            })),
          });
        } else {
          return null;
        }
      }
    );

    if (updateInstagramAssetIDPromises?.length) {
      await Promise.all(updateInstagramAssetIDPromises);
    }
  });

  await Promise.all(backfillPromises);

  res.status(200).send({
    numPostsBackfilled: postsToBackfill.length,
  });
};

export default handler;
