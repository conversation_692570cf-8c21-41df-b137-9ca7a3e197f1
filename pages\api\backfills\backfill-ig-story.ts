import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.findMany({
    where: {
      instagramLink: {
        contains: "https://www.instagram/stories",
      },
    },
    select: {
      id: true,
      instagramLink: true,
    },
  });

  const promises = posts.map(async (post) => {
    const { instagramLink } = post;

    if (instagramLink) {
      await db.post.update({
        where: { id: post.id },
        data: {
          instagramLink: instagramLink.replace(
            "www.instagram",
            "www.instagram.com"
          ),
        },
      });
    }
  });

  await Promise.all(promises);

  res.status(StatusCodes.OK).send({
    posts,
  });
};

export default handler;
