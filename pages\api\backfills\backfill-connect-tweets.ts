import { StatusCodes } from "http-status-codes";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";
import { removeEmptyElems } from "~/utils";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.findMany({
    where: {
      twitterLink: {
        not: null,
      },
      tweet: null,
    },
    select: {
      id: true,
      twitterLink: true,
    },
  });

  // https://twitter.com/startupbites/status/1742893699334357308
  const tweetIDs = removeEmptyElems(
    posts.map((post) => {
      if (!post.twitterLink) {
        return null;
      } else {
        return post.twitterLink.split("/").pop();
      }
    })
  );

  const tweets = await db.tweet.findMany({
    where: {
      id: {
        in: tweetIDs,
      },
    },
    select: {
      id: true,
    },
  });

  const foundPosts = posts.filter((post) => {
    return tweets.some((tweet) => {
      return post.twitterLink && tweet.id === post.twitterLink.split("/").pop();
    });
  });

  for await (const post of foundPosts) {
    const tweetID = post.twitterLink?.split("/").pop();
    console.log(post.id, tweetID);
    await db.post.update({
      where: {
        id: post.id,
      },
      data: {
        tweetID,
      },
    });
  }

  res.status(StatusCodes.OK).send({
    numPosts: foundPosts.length,
  });
};

export default handler;
