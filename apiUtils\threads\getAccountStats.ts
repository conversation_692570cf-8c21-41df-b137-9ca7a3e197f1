import { prisma } from "~/clients";
import Threads from "~/clients/facebook/Threads";

type ThreadsIntegration = {
  accessToken: string;
  account: {
    username: string;
    threadsUserID: string;
  };
};

const updateAccountStatsAndProfilePicture = async (
  integration: ThreadsIntegration
) => {
  const threads = new Threads({
    accessToken: integration.accessToken,
    threadsUserID: integration.account.threadsUserID,
  });

  const response = await threads.getMe();

  if (response.errors) {
    console.log(
      `Threads error: @${integration.account.username} account stats `,
      response.errors
    );
    return {
      response,
    };
  }

  await prisma.threadsAccount.update({
    where: {
      threadsUserID: integration.account.threadsUserID,
    },
    data: {
      profileImageUrl: response.data.profileImageUrl,
      profileImageExpiresAt: response.data.profileImageExpiresAt,
      name: response.data.name,
      biography: response.data.biography,
      stats: {
        create: {
          followerCount: response.data.followerCount,
        },
      },
    },
  });

  return {
    response,
  };
};

export default updateAccountStatsAndProfilePicture;
