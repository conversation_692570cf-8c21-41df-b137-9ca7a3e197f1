import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

import { useRouter } from "next/router";

import posthog from "posthog-js";

import { shutdown } from "@intercom/messenger-js-sdk";
import { ReferralSource } from "@prisma/client";
import { AuthError, EmailOtpType } from "@supabase/supabase-js";
import axios from "axios";
import { DateTime } from "luxon";
import Cookies from "universal-cookie";
import supabasePublic from "~/clients/supabasePublic";
import { getFullName, isAdmin } from "~/utils";
import { removeCookie } from "~/utils/cookies";

type User = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  isAdmin: boolean;
  agency: {
    id: string;
    name: string;
  } | null;
  referralSource: ReferralSource | null;
  intercomUserJwt: string;
  updatedAt: string;
  createdAt: Date;
};

type Props = {
  children: ReactNode;
};

type Context = {
  user: null | User;
  signIn: (email: string) => Promise<AuthError | null>;
  signInWithPassword: (
    email: string,
    password: string
  ) => Promise<AuthError | null>;
  signInWithGoogle: () => Promise<AuthError | null>;
  resetPasswordForEmail: (email: string) => Promise<AuthError | null>;
  updatePassword: (password: string) => Promise<AuthError | null>;
  verifyEmailOTP: (
    email: string,
    token: string,
    type: EmailOtpType
  ) => Promise<AuthError | null>;
  signOut: () => Promise<void>;
  lastLoginMethod: LoginMethod | null;
  saveLastLoginMethod: (loginMethod: LoginMethod) => void;
  removeLastLoginMethod: () => void;
};

export type LoginMethod = {
  method: "google" | "email" | "password";
  userInfo?: {
    firstName: string;
    lastName: string;
    email: string;
    workspaceCount: number;
  };
};

const UserContext = createContext<Context>({
  user: null,
  signIn: async (email: string) => {
    return null;
  },
  signInWithPassword: async (email: string, password: string) => {
    return null;
  },
  signInWithGoogle: async () => {
    return null;
  },
  resetPasswordForEmail: async (email: string) => {
    return null;
  },
  updatePassword: async (password: string) => {
    return null;
  },
  verifyEmailOTP: async (email: string, token: string, type: EmailOtpType) => {
    return null;
  },
  signOut: async () => {},
  lastLoginMethod: null,
  saveLastLoginMethod: () => {},
  removeLastLoginMethod: () => {},
});

const UserProvider = ({ children }: Props) => {
  const [user, setUser] = useState<User | null>(null);
  const [lastLoginMethod, setLastLoginMethod] = useState<LoginMethod | null>(
    null
  );
  const router = useRouter();
  const cookies = new Cookies();

  const buildUser = async () => {
    const {
      data: { session },
    } = await supabasePublic.auth.getSession();

    if (!session?.user) {
      import("react-facebook-pixel")
        .then((x) => x.default)
        .then((ReactPixel) => {
          ReactPixel.init(
            process.env.NEXT_PUBLIC_FACEBOOK_ADS_PIXEL_ID as string,
            undefined,
            {
              autoConfig: true,
              debug: false,
            }
          );
          ReactPixel.pageView();

          const handleRouteChange = () => {
            ReactPixel.pageView();
          };

          router.events.on("routeChangeComplete", handleRouteChange);

          return () => {
            router.events.off("routeChangeComplete", handleRouteChange);
          };
        });

      return setUser(null);
    }

    try {
      const { data } = await axios.get("/api/users/me");

      const user = data.data as {
        id: string;
        email: string;
        firstName: string | null;
        lastName: string | null;
        updatedAt: string;
        createdAt: Date;
        referralSource: ReferralSource | null;
        intercomUserJwt: string;
        agency: {
          id: string;
          name: string;
        } | null;
      };

      posthog.identify(user.id, {
        email: user.email,
        name: getFullName(user),
        createdAt: user.createdAt,
      });

      const { firstName, lastName, email } = user;

      if (
        firstName &&
        lastName &&
        email &&
        process.env.NODE_ENV === "production"
      ) {
        import("react-facebook-pixel")
          .then((x) => x.default)
          .then((ReactPixel) => {
            ReactPixel.init(
              process.env.NEXT_PUBLIC_FACEBOOK_ADS_PIXEL_ID as string,
              {
                em: email,
                fn: firstName.toLocaleLowerCase(),
                ln: lastName.toLocaleLowerCase(),
                // @ts-expect-error it actually is a field https://developers.facebook.com/docs/meta-pixel/advanced/advanced-matching#reference
                external_id: user.id,
              },
              {
                autoConfig: true,
                debug: false,
              }
            );

            ReactPixel.pageView();

            const handleRouteChange = () => {
              ReactPixel.pageView();
            };

            router.events.on("routeChangeComplete", handleRouteChange);

            return () => {
              router.events.off("routeChangeComplete", handleRouteChange);
            };
          });
      }

      const userIsAdmin = isAdmin(user);

      setUser({
        ...user,
        isAdmin: userIsAdmin,
      });
    } catch (error) {
      setUser(null);
    }
  };

  useEffect(() => {
    buildUser();
  }, []);

  useEffect(() => {
    const {
      data: { subscription },
    } = supabasePublic.auth.onAuthStateChange((event) => {
      if (event === "SIGNED_OUT") {
        const cookies = new Cookies();

        const cookieOptions = { path: "/", domain: ".meetassembly.com" };

        removeCookie("CURRENT_USER_ID", cookieOptions);
        removeCookie("CURRENT_WORKSPACE_ID", cookieOptions);

        localStorage.clear();

        posthog.reset();

        cookies.remove("supabase-auth-token", cookieOptions);
        cookies.remove("sb-mletdxmpsjlbfpnymbcu-auth-token.0", cookieOptions);
        cookies.remove("sb-mletdxmpsjlbfpnymbcu-auth-token.1", cookieOptions);
        cookies.remove("sb-localhost-auth-token.0", cookieOptions);
        cookies.remove("sb-localhost-auth-token.1", cookieOptions);
        router.push("https://www.meetassembly.com", undefined, {
          shallow: true,
        });
      }
      // Rebuild user on auth state changes
      buildUser();
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const loadLastLoginMethod = () => {
    const storedLoginMethod = cookies.get(
      "lastLoginMethod"
    ) as LoginMethod | null;
    if (storedLoginMethod?.method) {
      setLastLoginMethod(storedLoginMethod);
    } else {
      setLastLoginMethod(null);
    }
  };

  const saveLastLoginMethod = (loginMethod: LoginMethod) => {
    cookies.set("lastLoginMethod", loginMethod, {
      path: "/",
      expires: DateTime.now().plus({ days: 90 }).toJSDate(),
    });

    if (loginMethod.userInfo) {
      setLastLoginMethod(loginMethod);
    }
  };

  const removeLastLoginMethod = () => {
    cookies.remove("lastLoginMethod", { path: "/" });
    setLastLoginMethod(null);
  };

  useEffect(() => {
    loadLastLoginMethod();
  }, []);
  // user?.agency?.name
  console.log('user agency', user?.agency?.name);
  const value = {
    signIn: async (email: string) => {
      const response = await supabasePublic.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${process.env.NEXT_PUBLIC_DOMAIN}/loading-user`,
        },
      });

      return response.error;
    },
    signOut: async () => {
      shutdown();
      const response = await supabasePublic.auth.signOut({
        scope: "local",
      });
      console.log("logout response", response);
    },
    signInWithPassword: async (email: string, password: string) => {
      const response = await supabasePublic.auth.signInWithPassword({
        email,
        password,
      });

      if (!response.error) {
        saveLastLoginMethod({ method: "password" });
      }

      return response.error;
    },
    signInWithGoogle: async () => {
      const response = await supabasePublic.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${process.env.NEXT_PUBLIC_DOMAIN}/loading-user`,
          queryParams: {
            access_type: "offline",
            prompt: "consent",
          },
        },
      });

      if (!response.error) {
        saveLastLoginMethod({ method: "google" });
      }

      return response.error;
    },
    resetPasswordForEmail: async (email: string) => {
      const response = await supabasePublic.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_DOMAIN}/new-password`,
      });

      return response.error;
    },
    verifyEmailOTP: async (
      email: string,
      token: string,
      type: EmailOtpType
    ) => {
      const response = await supabasePublic.auth.verifyOtp({
        email,
        token,
        type,
        options: {
          redirectTo: `${process.env.NEXT_PUBLIC_DOMAIN}/loading-user`,
        },
      });

      if (!response.error) {
        saveLastLoginMethod({ method: "email" });
      }

      return response.error;
    },
    updatePassword: async (password: string) => {
      const response = await supabasePublic.auth.updateUser({
        password,
      });

      return response.error;
    },
    user,
    lastLoginMethod,
    saveLastLoginMethod,
    removeLastLoginMethod,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

const useUser = () => {
  return useContext(UserContext);
};

export { UserProvider, useUser };
