import getAuthedTwitterClient from "apiUtils/getAuthedTwitterClient";
import minBy from "lodash/minBy";
import { db } from "~/clients/Prisma";
import getTimeline from "./getTimeline";
import normalizeTwitterUser from "./normalizeTwitterUser";

const saveTimeline = async ({
  integration,
  maxQueries,
  sinceDate,
}: {
  integration: {
    accessToken: string;
    accessSecret: string;
    accountID: string;
    account: {
      username: string;
    };
  };
  maxQueries: number;
  sinceDate?: Date;
}) => {
  const tweetIDs = [];
  let queryNumber = 0;
  let untilID: string | undefined;
  while (true) {
    try {
      const { timeline, tweets } = await getTimeline({
        integration,
        untilID,
        startTime: sinceDate,
      });

      if (tweets.length === 0) {
        break;
      }

      tweetIDs.push(...tweets.map((tweet) => tweet.id));

      const includedUsers = timeline.includes.users;
      const existingTwitterAccounts = await db.twitterAccount.findMany({
        where: {
          username: {
            in: includedUsers.map((user) => user.username),
          },
        },
        select: {
          id: true,
          username: true,
        },
      });

      const misMatchedAccounts = existingTwitterAccounts.filter((account) => {
        const includedUser = includedUsers.find(
          (user) => user.username === account.username
        );
        return includedUser && includedUser.id !== account.id;
      });

      if (misMatchedAccounts.length > 0) {
        const twitter = getAuthedTwitterClient(integration);
        const result = await twitter.v2.users(
          misMatchedAccounts.map((account) => account.id),
          {
            "user.fields": [
              "id",
              "name",
              "username",
              "created_at",
              "description",
              "entities",
              "location",
              "pinned_tweet_id",
              "profile_image_url",
              "protected",
              "public_metrics",
              "url",
              "verified",
              "verified_type",
              "withheld",
            ],
          }
        );
        const misMatchedUsers = result.data;
        for await (const user of misMatchedUsers) {
          const normalizedUser = normalizeTwitterUser(user);

          await db.twitterAccount.update({
            where: { id: user.id },
            data: {
              id: normalizedUser.id,
              username: normalizedUser.username,
              name: normalizedUser.name,
              url: normalizedUser.url,
              description: normalizedUser.description,
              location: normalizedUser.location,
              profileImageUrl: normalizedUser.profileImageUrl,
              verified: normalizedUser.verified,
              verifiedType: normalizedUser.verifiedType,
              accountCreatedAt: normalizedUser.accountCreatedAt,
            },
          });
        }
      }

      for await (const user of includedUsers) {
        const normalizedUser = normalizeTwitterUser(user);

        await db.twitterAccount.upsert({
          where: { id: user.id },
          create: {
            id: normalizedUser.id,
            username: normalizedUser.username,
            name: normalizedUser.name,
            url: normalizedUser.url,
            description: normalizedUser.description,
            location: normalizedUser.location,
            profileImageUrl: normalizedUser.profileImageUrl,
            verified: normalizedUser.verified,
            verifiedType: normalizedUser.verifiedType,
            accountCreatedAt: normalizedUser.accountCreatedAt,
            stats: {
              create: {
                ...normalizedUser.stats,
              },
            },
          },
          update: {
            username: normalizedUser.username,
            name: normalizedUser.name,
            url: normalizedUser.url,
            description: normalizedUser.description,
            location: normalizedUser.location,
            profileImageUrl: normalizedUser.profileImageUrl,
            verified: normalizedUser.verified,
            verifiedType: normalizedUser.verifiedType,
            accountCreatedAt: normalizedUser.accountCreatedAt,
          },
        });
      }

      await db.tweet.createMany({
        data: tweets.map((tweet) => {
          return {
            id: tweet.id,
            text: tweet.text,
            accountID: tweet.accountID,
            type: tweet.type,
            permalink: tweet.permalink,
            imageUrls: tweet.imageUrls,
            videoUrls: tweet.videoUrls,
            gifUrls: tweet.gifUrls,
            publishedAt: tweet.publishedAt,
            rawResponse: tweet.rawResponse,
          };
        }),
        skipDuplicates: true,
      });

      const assemblyPosts = await db.post.findMany({
        where: {
          twitterLink: {
            in: tweets.map((tweet) => tweet.permalink),
          },
        },
      });

      const promises = assemblyPosts.map((post) => {
        if (!post.twitterLink) {
          return;
        }
        const tweetID = post.twitterLink.split("/").pop()!;

        return db.post.update({
          where: {
            id: post.id,
          },
          data: {
            tweetID,
          },
        });
      });
      await Promise.all(promises);

      await db.tweetMention.createMany({
        data: tweets.flatMap((tweet) => {
          return tweet.mentionedAccountIDs.map((mentionID) => ({
            tweetID: tweet.id,
            accountID: mentionID,
          }));
        }),
        skipDuplicates: true,
      });

      await db.tweetStat.createMany({
        data: tweets.map((tweet) => {
          const { metrics } = tweet;

          return {
            tweetID: tweet.id,

            retweetCount: metrics.retweetCount,
            replyCount: metrics.replyCount,
            likeCount: metrics.likeCount,
            quoteCount: metrics.quoteCount,
            bookmarkCount: metrics.bookmarkCount,
            impressionCount: metrics.impressionCount,
          };
        }),
      });

      untilID = minBy(tweets, (tweet) => tweet.id)?.id;
      queryNumber += 1;

      if (queryNumber >= maxQueries) {
        break;
      }

      const oldestTweet = minBy(tweets, (tweet) => tweet.publishedAt);
      if (sinceDate && oldestTweet && oldestTweet.publishedAt <= sinceDate) {
        break;
      }
    } catch (error) {
      console.log(error);
      try {
        const twitterError = error as {
          code: number;
          data: {
            title: string;
            detail: string;
          };
          rateLimit: {
            limit: number;
            remaining: number;
            reset: number;
          };
        };
        if (twitterError.rateLimit?.remaining === 0) {
          console.log(`Rate limit reached @${integration.account.username}`);
        }
        break;
      } catch (error) {
        console.log("Non-twitter error", error);
        break;
      }
    }
  }

  return tweetIDs;
};

export default saveTimeline;
