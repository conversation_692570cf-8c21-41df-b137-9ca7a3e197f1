import { CheckBadgeIcon } from "@heroicons/react/24/solid";
import { <PERSON>act<PERSON>enderer } from "@tiptap/react";
import { SuggestionKeyDownProps, SuggestionProps } from "@tiptap/suggestion";
import classNames from "classnames";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import { useAutosave } from "react-autosave";
import tippy, { Instance } from "tippy.js";
import { Loader } from "~/components";
import { trpc } from "~/utils";
import LinkMention from "./LinkMention";

type FacebookMentionListRef = {
  onKeyDown: (props: SuggestionKeyDownProps) => boolean;
};

type FacebookMentionListProps = {
  query: string;
  command: ({
    id,
    label,
    href,
  }: {
    id: string;
    label: string;
    href: string;
  }) => void;
};

const FacebookMentionList = forwardRef(
  (props: FacebookMentionListProps, ref: React.Ref<FacebookMentionListRef>) => {
    const [selectedIndex, setSelectedIndex] = useState<number>(0);

    const [query, setQuery] = useState<string>("");

    useAutosave({
      data: props.query,
      onSave: (data) => {
        setQuery(data);
      },
      interval: 250,
    });

    const { data, isLoading } = trpc.integration.facebook.searchPages.useQuery(
      {
        query,
        mustHaveLocation: false,
      },
      {
        refetchOnWindowFocus: false,
        refetchInterval: false,
      }
    );

    const pages =
      data?.pages.map((page) => ({
        ...page,
        href: `https://www.facebook.com/${page.id}`,
      })) || [];

    const upHandler = () => {
      setSelectedIndex((selectedIndex + pages.length - 1) % pages.length);
    };

    const downHandler = () => {
      setSelectedIndex((selectedIndex + 1) % pages.length);
    };

    const enterHandler = () => {
      const item = pages[selectedIndex];

      if (item) {
        props.command({ id: item.id, label: item.name, href: item.href });
      }
    };

    useEffect(() => {
      setSelectedIndex(selectedIndex % pages.length);
    }, [pages.length]);

    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }) => {
        if (event.key === "ArrowUp") {
          upHandler();
          return true;
        }

        if (event.key === "ArrowDown") {
          downHandler();
          return true;
        }

        if (event.key === "Enter") {
          enterHandler();
          return true;
        }

        return false;
      },
    }));

    if (isLoading) {
      return (
        <div className="rounded-lg bg-white px-2 py-1 shadow-xl">
          <div className="flex h-10 w-20 items-center justify-center">
            <Loader size="md" />
          </div>
        </div>
      );
    }

    return (
      <div className="scrollbar-hidden flex max-h-[300px] flex-col items-start gap-1 overflow-y-auto rounded-lg bg-white px-2 py-1 shadow-xl">
        <div className="space-y-4">
          <div className="divide-tan/25 font-body mb-1">
            {pages.map((option, index) => {
              return (
                <button
                  key={option.id}
                  className={classNames(
                    "font-eyebrow hover:bg-lightest-gray flex w-full items-center gap-2 rounded-md px-2 py-1 transition-colors",
                    {
                      "bg-lightest-gray": selectedIndex === index,
                    }
                  )}
                  onClick={() => {
                    props.command({
                      id: option.id,
                      label: option.name,
                      href: option.href,
                    });
                  }}
                >
                  <img
                    className="h-8 w-8 rounded-full object-contain"
                    src={
                      option.profileImageUrl || "/channel_icons/facebook.png"
                    }
                  />
                  <div className="flex flex-col items-start pr-4">
                    <div className="flex items-center gap-1">
                      <div className="font-body-sm">{option.name}</div>
                      {option.isVerified && (
                        <CheckBadgeIcon className="text-secondary h-4 w-4" />
                      )}
                    </div>
                    {option.address && (
                      <div className="font-body-xs text-medium-dark-gray mr-2 text-left leading-4">
                        {option.address}
                      </div>
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>
    );
  }
);

const FacebookMention = LinkMention.configure({
  HTMLAttributes: {
    class: "text-secondary px-1 py-0.5 rounded-sm",
  },
  renderHTML({ options, node }) {
    return [
      "a",
      {
        ...options.HTMLAttributes,
        href: node.attrs.href,
        target: "_blank",
        rel: "noopener noreferrer",
      },
      node.attrs.label,
    ];
  },
  renderText: ({ node }) => {
    return `@[${node.attrs.label}](${node.attrs.id})`;
  },
  deleteTriggerWithBackspace: true,
  suggestion: {
    items: () => [],
    render: () => {
      let component: ReactRenderer<
        FacebookMentionListRef,
        FacebookMentionListProps
      >;
      let popup: Instance;

      return {
        onStart: (props: SuggestionProps<any, any>) => {
          component = new ReactRenderer<
            FacebookMentionListRef,
            FacebookMentionListProps
          >(FacebookMentionList, {
            props,
            editor: props.editor,
          });

          if (!props.clientRect) {
            return;
          }

          popup = tippy(document.body, {
            getReferenceClientRect: () => props.clientRect?.() || new DOMRect(),
            appendTo: () => document.body,
            content: component.element,
            showOnCreate: true,
            interactive: true,
            trigger: "manual",
            placement: "bottom-start",
          });
        },

        onUpdate: (props: SuggestionProps<any, any>) => {
          component.updateProps(props);

          if (!props.clientRect) {
            return;
          }

          popup.setProps({
            getReferenceClientRect: () => props.clientRect?.() || new DOMRect(),
          });
        },

        onKeyDown: (props: SuggestionKeyDownProps) => {
          if (props.event.key === "Escape") {
            popup.hide();

            return true;
          }

          return component.ref?.onKeyDown(props) || false;
        },

        onExit: () => {
          popup.destroy();
          component.destroy();
        },
      };
    },
  },
});

export default FacebookMention;
