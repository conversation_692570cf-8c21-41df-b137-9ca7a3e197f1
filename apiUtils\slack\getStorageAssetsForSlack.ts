import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForSlack = async (post: {
  id: string;
  workspaceID: string;
  slackContent: {
    id: string;
  } | null;
}): Promise<AssetType[]> => {
  const slackContent = post.slackContent;

  if (!slackContent) {
    return [];
  }

  const assetsMap = await getAssetsForFilePaths([
    {
      id: slackContent.id,
      path: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Slack"].slug}/${slackContent.id}`,
    },
  ]);

  return assetsMap[slackContent.id];
};

export default getStorageAssetsForSlack;
