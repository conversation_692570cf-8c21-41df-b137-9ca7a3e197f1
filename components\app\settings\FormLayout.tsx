import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import Link from "next/link";
import { ReactNode } from "react";
import { Divider } from "~/components";

type Props = {
  title: string | ReactNode;
  description?: string | ReactNode;
  backHref?: string;
  children: ReactNode;
};

const FormLayout = ({ title, description, backHref, children }: Props) => {
  return (
    <div className="scrollbar-hidden h-full overflow-auto px-5 py-4 md:px-20 md:pt-8">
      <div className="mx-auto max-w-lg">
        {backHref && (
          <div>
            <Link
              href={backHref}
              className="text-basic font-body-sm flex items-center gap-1 font-medium"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              Back
            </Link>
            <Divider padding="md" />
          </div>
        )}
        <h4
          className={classNames({
            "mb-2": !!description,
            "mb-4": !description,
          })}
        >
          {title}
        </h4>
        {description && (
          <div className="font-body-sm text-medium-dark-gray mb-8">
            {description}
          </div>
        )}
        {children}
      </div>
    </div>
  );
};

export default FormLayout;
