import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { StatusCodes } from "http-status-codes";
import { NextApiHandler, NextApiRequest, NextApiResponse } from "next";
import { isAdmin } from "~/utils";

const adminAuthorized = (handler: NextApiHandler) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return req.cookies[name];
          },
          set(name: string, value: string, options: CookieOptions) {
            res.setHeader(
              "Set-Cookie",
              `${name}=${value}; Path=${options.path || "/"}`
            );
          },
          remove(name: string, options: CookieOptions) {
            res.setHeader(
              "Set-Cookie",
              `${name}=; Path=${options.path || "/"}; Max-Age=0`
            );
          },
        },
      }
    );

    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!isAdmin(session?.user)) {
      return res.status(StatusCodes.UNAUTHORIZED).send({
        errors: ["Insufficient permissions"],
        query: req.query,
      });
    }

    return handler(req, res);
  };
};

export default adminAuthorized;
