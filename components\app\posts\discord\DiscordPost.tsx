import { useEffect, useState } from "react";

import { v4 as uuidv4 } from "uuid";

import { Channel, ScheduledPostStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import { arrayMoveImmutable } from "array-move";
import assert from "assert";
import classNames from "classnames";
import { toast } from "sonner";
import DiscordEditorWrapper from "~/components/app/tiptap/DiscordEditor";
import { ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import { useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  handleFilesUpload,
  handleTrpcError,
  openIntercomArticle,
  trpc,
} from "~/utils";
import { EditImageModalProperties } from "~/utils/EditImageModal";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import { getDefaultAccount } from "~/utils/getDefaultAccount";
import getFileProperties from "~/utils/getFileProperties";
import createRoomID from "~/utils/liveblocks/createRoomID";
import Assets, { UploadingAsset } from "../../files/Assets";
import EditorActionsBar from "../EditorActionsBar";
import PostPreviewContainer from "../PostPreviewContainer";
import DiscordPostPreview from "./DiscordPostPreview";

type ScheduledPost = {
  status: ScheduledPostStatus;
  channel: Channel;
  error: string | null;
  publishAt: Date;
  integrationAccountName: string;
};

export type DiscordContent = {
  id: string;
  editorState: JSONContent | null;
  copy: string;
  uploading?: boolean;
};

export type DiscordWebhook = {
  id: string;
  channel: string;
  username: string;
  profileImageUrl: string;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    discordAssets: AssetType[];
    discordContent: DiscordContent | null;
    discordWebhook: DiscordWebhook | null;
    scheduledPosts: ScheduledPost[];
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const DiscordPost = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const trpcUtils = trpc.useUtils();

  const [isRoomReady, setIsRoomReady] = useState<boolean>(false);

  useEffect(() => {
    if (post.discordContent && !isSyncingComplete) {
      setIsRoomReady(true);
    }
  }, [post.discordContent, isSyncingComplete]);

  const [discordWebhook, setDiscordWebhook] = useState<DiscordWebhook | null>(
    post.discordWebhook
  );
  const [content, setContent] = useState<DiscordContent>(
    post.discordContent || {
      id: uuidv4(),
      editorState: null,
      copy: "",
    }
  );
  const [draggedOver, setDraggedOver] = useState<boolean>(false);
  const [assets, setAssets] = useState<AssetType[]>(post.discordAssets);
  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);
  const [uppy, setUppy] = useState<Uppy | null>(null);
  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);

  const updatePostMutation = trpc.post.update.useMutation();

  const discordContentQuery = trpc.discordContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const discordContentData = discordContentQuery.data;
  useEffect(() => {
    const setCrossPostData = () => {
      if (discordContentData?.discordContent != null) {
        setContent(discordContentData.discordContent);
        setAssets(discordContentData.assets);
        setIsRoomReady(true);
        clearIsSyncing();
      }
    };
    setCrossPostData();
  }, [discordContentData]);

  const upsertDiscordContentMutation = trpc.discordContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
  });
  const createAssetsMutation = trpc.discordContent.createAssets.useMutation();
  const deleteAssetMutation = trpc.discordContent.deleteAsset.useMutation();
  const updateAssetPositionsMutation =
    trpc.discordContent.updateAssetPositions.useMutation();
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const updateAssetMutation = trpc.discordContent.updateAsset.useMutation();

  const onSortEnd = (oldIndex: number, newIndex: number) => {
    setAssets((assets) => {
      const newAssets = arrayMoveImmutable(assets, oldIndex, newIndex);

      updateAssetPositionsMutation.mutate({
        assets: newAssets.map((asset, index) => ({
          id: asset.id,
          position: index,
        })),
      });

      return newAssets;
    });
  };

  const connectedWebhooksQuery = trpc.integration.discord.getWebhooks.useQuery(
    undefined,
    {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );
  const integrationsQuery = trpc.post.getIntegrations.useQuery({
    id: post.id,
  });
  const postAccounts = integrationsQuery.data?.accounts ?? [];

  const connectedWebhooksData = connectedWebhooksQuery.data;
  useEffect(() => {
    const setInitialIntegration = () => {
      if (discordWebhook == null && connectedWebhooksData?.webhooks.length) {
        const defaultWebhook = getDefaultAccount({
          mainAccount: postAccounts[0],
          accounts: connectedWebhooksData.webhooks,
        });
        updateDiscordWebhook(defaultWebhook.id);
      }
    };
    setInitialIntegration();
  }, [connectedWebhooksData]);

  const workspaceWebhooks =
    connectedWebhooksData?.webhooks ||
    (post.discordWebhook ? [post.discordWebhook] : []);

  const updateDiscordWebhook = (webhookID: string) => {
    const newWebhook = workspaceWebhooks.find(
      (webhook) => webhook.id === webhookID
    );
    if (newWebhook) {
      setDiscordWebhook(newWebhook);
      updatePostMutation.mutate(
        {
          id: post.id,
          discordWebhookID: newWebhook.id,
        },
        {
          onSuccess: () => {
            trpcUtils.post.getIntegrations.refetch({
              id: post.id,
            });
          },
        }
      );
    }
  };

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        restrictions: { maxNumberOfFiles: 3 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", async (file) => {
        assert(currentWorkspace);

        const contentID = file.meta.contentID;
        assert(contentID);

        setUploadingAssets((uploadingAssets) => {
          return [
            ...uploadingAssets,
            {
              name: file.name!,
              size: file.size!,
              type: file.type,
              uploadProgress: 0,
              updateAssetID: file.meta.updateAssetID as string | undefined,
            },
          ];
        });

        const objectName = `${currentWorkspace.id}/${post.id}/discord/${contentID}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (uppyData) => {
        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["Discord"].slug
        }/${content.id}`;
        const fileNames = uppyData.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          uppyData.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              if (uppyData.successful![0].meta.updateAssetID) {
                const updateAssetID = uppyData.successful![0].meta
                  .updateAssetID as string;
                const newAsset = newAssetsWithProperties[0];

                await updateAssetMutation.mutateAsync({
                  discordAssetID: updateAssetID,
                  newAsset: {
                    id: newAsset.id,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                });

                setAssets((prevAssets) =>
                  prevAssets.map((asset) =>
                    asset.id === updateAssetID
                      ? {
                          id: asset.id,
                          name: newAsset.name,
                          path: newAsset.path,
                          sizeBytes: newAsset.sizeBytes,
                          eTag: newAsset.eTag,
                          md5Hash: newAsset.md5Hash,
                          mimetype: newAsset.mimetype,
                          width: newAsset.width,
                          height: newAsset.height,
                          metadata: newAsset.metadata,
                          url: newAsset.signedUrl,
                          sizeMB: newAsset.sizeMB,
                          sizeKB: newAsset.sizeKB,
                          createdAt: newAsset.createdAt,
                          updatedAt: newAsset.updatedAt,
                          duration: newAsset.duration,
                          signedUrl: newAsset.signedUrl,
                          urlExpiresAt: newAsset.urlExpiresAt,
                        }
                      : asset
                  )
                );
                setUploadingAssets([]);
              } else {
                const nextPosition = assets.length;

                createAssetsMutation.mutate(
                  {
                    id: content.id,
                    assets: newAssetsWithProperties.map((asset, index) => ({
                      id: asset.id,
                      assetID: asset.assetID,
                      name: asset.name,
                      path: asset.path,
                      size: asset.sizeBytes,
                      eTag: asset.eTag,
                      md5Hash: asset.md5Hash,
                      mimetype: asset.mimetype,
                      width: asset.width,
                      height: asset.height,
                      duration: asset.duration,
                      url: asset.signedUrl,
                      urlExpiresAt: asset.urlExpiresAt,
                      position: nextPosition + index,
                    })),
                  },
                  {
                    onError: (error) => {
                      handleTrpcError({ title: "Error Creating Asset", error });
                    },
                  }
                );

                setUploadingAssets([]);
                setAssets((assets) => [...assets, ...newAssetsWithProperties]);
              }
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace, content.id]);

  const getValidFiles = async (files: File[]) => {
    const assetNames = assets.map((asset) => asset.name);

    const validFiles = files.filter((file) => {
      if (assetNames.includes(file.name)) {
        toast.error(`Duplicate Attachment: "${file.name}"`, {
          description: "Each attachment must have a unique name",
        });
        return false;
      }
      // Max size is 10MB
      if (file.size > 10_000_000) {
        toast.error(`File too large: "${file.name}"`, {
          description: `Max file size is 10MB.`,
        });
        return false;
      } else {
        return true;
      }
    });

    if (assets.length + validFiles.length > 3) {
      toast.error("You can add 3 images or videos per post");
      return [];
    }

    return validFiles;
  };

  return (
    <div
      onDragOverCapture={() => {
        setDraggedOver(true);
      }}
      onDragLeaveCapture={() => {
        setDraggedOver(false);
      }}
    >
      <PostPreviewContainer
        channel="Discord"
        post={post}
        connectedAccount={
          discordWebhook && {
            integrationID: discordWebhook.id,
            name: discordWebhook.username,
          }
        }
        accountOptions={workspaceWebhooks.map((workspaceWebhook) => ({
          integrationID: workspaceWebhook.id,
          name: `${workspaceWebhook.channel} (${workspaceWebhook.username})`,
        }))}
        setConnectedAccountID={updateDiscordWebhook}
        Preview={
          <DiscordPostPreview
            discordContent={{ ...content, assets }}
            connectedAccount={discordWebhook}
          />
        }
        isReadOnly={isReadOnly}
      />
      <div className="group">
        {isRoomReady && (
          <DiscordEditorWrapper
            key={content?.id || "discord-editor"}
            title="Post Content"
            placeholder="Add copy for the post here"
            initialValue={content?.editorState}
            isReadOnly={isReadOnly}
            onImagePaste={(event) => {
              if (uppy) {
                handleFilesUpload({
                  items: event.clipboardData.items,
                  uppy,
                  meta: {
                    contentID: content.id,
                  },
                  getValidFiles,
                });
              }
            }}
            onSave={(editorState) => {
              assert(content);
              setContent({
                ...content,
                editorState,
              });

              upsertDiscordContentMutation.mutate({
                id: content.id,
                copy: content.copy,
                editorState: editorState,
                postID: post.id,
              });
            }}
            onUpdateContent={(copy) => {
              setContent({
                ...content,
                copy,
              });
            }}
            roomID={createRoomID({
              workspaceID: post.workspaceID,
              contentType: "post",
              postID: post.id,
              roomContentID: content.id,
            })}
          />
        )}
        <EditorActionsBar
          post={post}
          channel="Discord"
          visible={true}
          isReadOnly={isReadOnly}
          characterCount={{
            current: content.copy.length,
            max: 2000,
          }}
          uploadAssetButton={{
            acceptedMimetypes: [
              "image/jpeg",
              "image/png",
              "image/gif",
              "video/mp4",
              "video/quicktime",
            ],
            acceptMultiple: true,
            onUploadFiles: async (files) => {
              if (uppy) {
                handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID: content.id,
                  },
                  getValidFiles,
                });
              }
            },
            isLoading: content.uploading,
          }}
          openHowToTagModal={() => {
            openIntercomArticle("discord_tagging");
          }}
        />
      </div>
      <div
        className={classNames({
          "mt-2":
            assets.length > 0 || uploadingAssets.length > 0 || draggedOver,
        })}
      >
        <Assets
          assets={assets}
          uploadingAssets={uploadingAssets}
          onDeleteClick={(asset) => {
            setAssets((prevAssets) =>
              prevAssets.filter((prevAsset) => prevAsset.id !== asset.id)
            );
            deleteAssetMutation.mutate({
              discordAssetID: asset.id,
            });
          }}
          editImageModalProperties={editImageModalProperties}
          onEditClick={(asset) => {
            if (asset.metadata.mimetype.startsWith("image/")) {
              setEditImageModalProperties({
                title: "Edit Image",
                channel: "Discord",
                isOpen: true,
                asset,
              });
            }
          }}
          onUpload={async (file, updateAssetID) => {
            if (uppy) {
              if (updateAssetID) {
                uppy.addFile({
                  name: file.name,
                  type: file.type,
                  data: file,
                  meta: {
                    contentID: content.id,
                    updateAssetID,
                  },
                });
              } else {
                await handleFilesUpload({
                  files: [file],
                  uppy,
                  meta: {
                    contentID: content.id,
                  },
                  getValidFiles,
                });
              }
            }
          }}
          onEditImageModalClose={() => setEditImageModalProperties(null)}
          refetchAssets={() => {}}
          isReadOnly={isReadOnly}
          onDrop={async (files) => {
            setDraggedOver(false);
            if (uppy) {
              await handleFilesUpload({
                files,
                uppy,
                meta: {
                  contentID: content.id,
                },
                getValidFiles,
              });
            }
          }}
          showDropzone={draggedOver}
          acceptedFileTypes={{
            "image/jpg": [".jpg", ".jpeg"],
            "image/png": [".png"],
            "image/gif": [".gif"],
            "video/mp4": [".mp4"],
            "video/quicktime": [".mov"],
          }}
          onSortEnd={onSortEnd}
          showNewEditButton={true}
        />
      </div>
    </div>
  );
};

export default DiscordPost;
