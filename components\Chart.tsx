import { ReactElement } from "react";

import dynamic from "next/dynamic";

import { ApexOptions } from "apexcharts";
import { DateTime } from "luxon";
import colors, { chartColors } from "~/styles/colors";

const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

type TooltipType = {
  series: any[][];
  seriesIndex: number;
  dataPointIndex: number;
  w: any;
};

export type Series = {
  name: string;
  data: {
    x: number | string | Date;
    y: number | string;
    fillColor?: string | undefined;
    strokeColor?: string | undefined;
    meta?: any;
  }[];
};

type xAxisType = "category" | "numeric" | "datetime";

type Props = {
  /** Top Level Stats */
  stats?: ReactElement;
  /** The data that will be displayed */
  series: Series[];
  /** The optional filters that will be displayed on the top left */
  filters?: ReactElement;
  /** The type of data that will be on the x-axis */
  xAxisType: xAxisType;
  chartType:
    | "area"
    | "line"
    | "bar"
    | "pie"
    | "donut"
    | "radialBar"
    | "scatter"
    | "bubble"
    | "heatmap"
    | "treemap"
    | "boxPlot"
    | "candlestick"
    | "radar"
    | "polarArea"
    | "rangeBar";
  /** For line charts, the type of line  */
  curve?: "straight" | "smooth" | "stepline";
  /** The height of the chart in pixels */
  height?: number;
  /** Enables stacked option for axis charts */
  stacked?: boolean;
  // Optionally label data, length must be = to data slice
  dataLabels?: object;
  /** The format of the dates on the x-axis */
  dateFormat?: string;
};

const AreaChart = ({
  series,
  stats,
  filters,
  xAxisType = "numeric",
  chartType,
  curve = "straight",
  height,
  stacked = false,
  dataLabels = {},
  dateFormat = "MM dd, yyyy",
}: Props): JSX.Element => {
  const firstSeries = series[0];

  const options: ApexOptions = {
    chart: {
      stacked,
      redrawOnWindowResize: true,
      toolbar: {
        show: false,
      },
    },
    stroke: {
      curve,
      width: 1,
    },
    colors: chartColors,
    xaxis: {
      type:
        xAxisType === "datetime" && firstSeries.data.length < 30
          ? xAxisType
          : "numeric",
      tickAmount:
        xAxisType === "datetime" && firstSeries.data.length < 30
          ? "dataPoints"
          : 6,
      tickPlacement: "on",
      labels: {
        format: dateFormat,
        formatter: (val: any, _: number) => {
          if (xAxisType === "datetime") {
            if (typeof val === "string") {
              return DateTime.fromISO(val).toFormat(dateFormat);
            } else if (val instanceof Date) {
              return `${DateTime.fromJSDate(val).toFormat(dateFormat)}`;
            } else if (typeof val === "number") {
              return `${DateTime.fromMillis(val).toFormat(dateFormat)}`;
            } else {
              return val;
            }
          } else {
            return val;
          }
        },
        style: {
          fontSize: "11px",
          fontFamily: "Inconsolata",
          fontWeight: 500,
          colors: colors.mediumDarkGray,
        },
      },
      tooltip: {
        enabled: false,
      },
    },
    fill: {
      type: "gradient",
      gradient: {
        opacityFrom: 0.7,
        opacityTo: 0,
      },
    },
    grid: {
      show: false,
    },
    yaxis: {
      axisBorder: {
        show: true,
      },
      tickAmount: 4,
      labels: {
        style: {
          fontSize: "11px",
          fontFamily: "Inconsolata",
          fontWeight: 500,
          colors: [colors.mediumDarkGray],
        },
        formatter: (val: number, _: number) => {
          if (val > 1000) {
            return `${(val / 1000).toFixed(1)}k`;
          } else {
            return val.toString();
          }
        },
      },
    },
    dataLabels: {
      enabled: false,
      ...dataLabels,
    },
    legend: {
      show: false,
    },
    markers: {
      size: 3,
      hover: {
        size: 5,
      },
      strokeWidth: 0,
    },
    series,
    // tooltip: {
    //   marker: {
    //     show: false,
    //   },
    //   y: {
    //     formatter: (val: number) => {
    //       return val.toLocaleString();
    //     },
    //   },
    //   style: {
    //     fontSize: "11px",
    //     fontFamily: "Inconsolata",
    //   },
    // },
    tooltip: tooltip(series, xAxisType, chartColors, dateFormat),
  };

  return (
    <div className="w-full">
      {series.length > 1 ? (
        <div className="flex items-center justify-between pb-4">
          {filters}
          <div className="flex flex-col items-start gap-1">
            {series.map((group, i) => {
              return (
                <div key={group.name} className="flex items-center text-sm">
                  <div
                    style={{ backgroundColor: chartColors[i] }}
                    className="mr-1 h-3 w-3 rounded-full"
                  />
                  {group.name}
                </div>
              );
            })}
          </div>
        </div>
      ) : null}
      <div
        className="border-lightest-gray rounded-xl border bg-white px-2"
        style={{ height }}
      >
        {stats ? <div className="w-full px-4 pt-4">{stats}</div> : null}
        <Chart
          options={options}
          series={series}
          type={chartType}
          width="100%"
          height={300}
        />
      </div>
    </div>
  );
};

const tooltip = (
  series: Series[],
  xAxisType: xAxisType,
  colors: string[],
  dateFormat: string
) => ({
  enabled: true,
  custom: ({
    series: _series,
    seriesIndex,
    dataPointIndex,
    w,
  }: TooltipType) => {
    let category = series[seriesIndex].data[dataPointIndex].x;
    if (xAxisType === "datetime") {
      const x = series[seriesIndex].data[dataPointIndex].x;
      if (x instanceof Date) {
        category = DateTime.fromJSDate(x).toFormat(dateFormat);
      } else if (typeof x === "string") {
        category = DateTime.fromISO(x).toFormat(dateFormat);
      } else if (typeof x === "number") {
        category = DateTime.fromMillis(x).toFormat(dateFormat);
      } else {
        category = x;
      }
    }

    const reversedSeries = [...series].reverse();

    const legend =
      '<div class="flex flex-col gap-1 text-secondary">' +
      reversedSeries
        .map((group, i) => {
          const value = group.data[dataPointIndex].y;

          return `
          <div class="flex flex-col">
              <div class="flex gap-1">
                <div>
                  ${group.name}:
                </div>
                <span class="">${value.toLocaleString()}</span>
              
              </div>
              ${
                group.data[dataPointIndex].meta
                  ? group.data[dataPointIndex].meta
                  : ""
              }
            </div>
          `;
        })
        .join(``) +
      "</div>";

    return `
      <div class="font-normal! font-eyebrow-sm p-3 bg-white drop-shadow-sm w-fit">
        <div class="flex flex-col">
          <div class="text-medium-dark-gray">
            ${category}
          </div>
          ${legend}
        </div>
      </div>
      `;
  },
});

export default AreaChart;
