import later from "@breejs/later";
import { Channel, PostStatus } from "@prisma/client";
import { AnimatePresence, motion } from "framer-motion";
import { DateTime } from "luxon";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { FormAutosizeInput, FormSelect } from "~/components/form";
import FormDatePicker from "~/components/form/FormDatePicker";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import {
  addOrdinalSuffix,
  dateFromUTC,
  getDateUTC,
  getPostPublishAt,
  handleTrpcError,
  trpc,
} from "~/utils";

type Post = {
  id: string;
  title: string;
  channels: Channel[];
  campaignID: string | null;
  campaign?: {
    id: string;
    name: string;
    startDate: Date;
    endDate: Date;
  };
  publishDate: Date;
  publishTime: Date | null;
  publishAt: Date | null;
  status: PostStatus;
  isIdea: false;
  discordWebhookID: string | null;
  threadsIntegrationID: string | null;
  instagramIntegrationID: string | null;
  facebookIntegrationID: string | null;
  linkedInIntegrationID: string | null;
  tikTokIntegrationID: string | null;
  youTubeIntegrationID: string | null;
  twitterIntegrationID: string | null;
  slackIntegrationID: string | null;
  webflowIntegrationID: string | null;
  subscribers: {
    id: string;
    userID: string;
    user: {
      firstName: string | null;
      lastName: string | null;
    };
  }[];
  accounts: {
    id: string;
    name: string;
    profileImageUrl?: string;
    channel: Channel;
  }[];
  labels: {
    id: string;
    name: string;
    color: string;
    backgroundColor: string;
    createdAt: Date;
  }[];
  createdAt: Date;
};

type FormValues = {
  title: string;
  startDate: Date;
  frequency: string;
  timeUnit: "day" | "week" | "month";
  monthlySetting?: "day_of_week" | "day_of_month";
  occurrences: string;
};

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSuccess: (posts: Post[]) => void;
  post: {
    id: string;
    title: string;
    publishDate: Date;
    publishAt: Date | null;
  };
};

const BulkDuplicatePostModal = ({ open, setOpen, onSuccess, post }: Props) => {
  const trpcUtils = trpc.useUtils();

  const { control, handleSubmit, watch, setValue, reset } = useForm<FormValues>(
    {
      defaultValues: {
        title: post.title,
        startDate: new Date(),
        frequency: "1",
        timeUnit: "week",
        monthlySetting: "day_of_month",
        occurrences: "3",
      },
    }
  );

  useEffect(() => {
    reset({
      title: post.title,
      startDate: new Date(),
      frequency: "1",
      timeUnit: "week",
      monthlySetting: "day_of_month",
      occurrences: "3",
    });
  }, [post]);

  useEffect(() => {
    if (open) {
      // Set the start date to the next viable date which is
      // The first day that's the same day of the week as this post that's after both today and the post's publish date
      const postsNextStartDate = DateTime.fromJSDate(
        new Date(post.publishAt || dateFromUTC(post.publishDate))
      ).plus({ week: 1 });
      const nextWeekStartDate = DateTime.now()
        .startOf("week")
        .plus({
          week: postsNextStartDate.weekday > DateTime.now().weekday ? 0 : 1,
        })
        .plus({
          days: postsNextStartDate.weekday - 1,
        });

      setValue(
        "startDate",
        nextWeekStartDate > postsNextStartDate
          ? nextWeekStartDate.toJSDate()
          : postsNextStartDate.toJSDate()
      );
    }
  }, [post.publishAt, post.publishDate, open]);

  const timeUnit = watch("timeUnit");
  const startDate = watch("startDate");

  const bulkDuplicatePostMutation = trpc.post.bulkDuplicate.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Error Duplicating Post", error });
    },
    onSuccess: (data) => {
      trpcUtils.post.getMany.invalidate();

      onSuccess(data.posts);

      setOpen(false);

      reset({
        title: post.title,
        startDate: new Date(),
        frequency: "1",
        timeUnit: "week",
        monthlySetting: "day_of_month",
        occurrences: "3",
      });
    },
  });

  const duplicatePost = (values: FormValues) => {
    const {
      title,
      startDate: rawStartDate,
      frequency: stringFrequency,
      timeUnit,
      occurrences: stringOccurrences,
      monthlySetting,
    } = values;

    const frequency = Number(stringFrequency);
    const occurrences = Number(stringOccurrences) - 1;

    const startDateStartOfDay = DateTime.fromJSDate(rawStartDate)
      .startOf("day")
      .toJSDate();

    const startDate =
      getPostPublishAt(rawStartDate, post.publishAt) || startDateStartOfDay;

    let days: Date[] = [startDate];
    if (timeUnit !== "month" || monthlySetting === "day_of_month") {
      // For each occurance of the post, add the next date to the array
      for (let i = 1; i <= occurrences; i++) {
        const newDay = DateTime.fromJSDate(startDate).plus({
          [timeUnit]: i * frequency,
        });
        days.push(newDay.toJSDate());
      }
    } else {
      if (occurrences === 0) {
        return days;
      }

      const dayOfWeekCount =
        later.dayOfWeekCount.val(startDate) === 5
          ? 0
          : later.dayOfWeekCount.val(startDate);

      const sched = later.parse
        .recur()
        .on(dayOfWeekCount)
        .dayOfWeekCount()
        .on(later.dayOfWeek.val(startDate) + 1)
        .dayOfWeek();

      const nextDates = later.schedule(sched).next(occurrences, startDate);
      if (Array.isArray(nextDates)) {
        days = [...days, ...nextDates];
      } else {
        days.push(nextDates);
      }
    }

    const dates = days.map((day) => ({
      publishDate: getDateUTC(day),
      publishTime: post.publishAt,
      publishAt: getPostPublishAt(day, post.publishAt),
    }));

    bulkDuplicatePostMutation.mutate({
      id: post.id,
      title,
      dates,
    });
  };

  const dayOfMonth = DateTime.fromJSDate(startDate).day;
  const dayOfWeekCount =
    later.dayOfWeekCount.val(startDate) === 5
      ? "last"
      : addOrdinalSuffix(later.dayOfWeekCount.val(startDate));
  const dayOfWeekName = DateTime.fromJSDate(startDate).toFormat("cccc");

  const monthOptions = [
    {
      value: "day_of_month",
      label: addOrdinalSuffix(dayOfMonth),
    },
    {
      value: "day_of_week_count",
      label: `${dayOfWeekCount} ${dayOfWeekName}`,
    },
  ];

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header className="sr-only">
        <Credenza.Title>Bulk Duplicate Post</Credenza.Title>
        <Credenza.Description>
          Duplicate a post with a title, start date, frequency, and occurrences
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <form className="gap-y-6" onSubmit={handleSubmit(duplicatePost)}>
          <FormAutosizeInput
            control={control}
            name="title"
            autoFocus={true}
            placeholder="Enter Post Title"
            size="lg"
            rules={{ required: true }}
            showBorder={false}
          />
          <div className="pt-4">
            <div className="font-eyebrow text-medium-dark-gray">Recurrence</div>
            <div className="font-body-sm flex w-full items-center gap-2">
              <div>Starting </div>
              <FormDatePicker
                control={control}
                name="startDate"
                placeholder="MM/DD/YYYY"
                minDate={new Date()}
                rules={{ required: true }}
              />
              repeat this post every{" "}
              <FormSelect
                control={control}
                name="frequency"
                options={[
                  { value: "1", label: "1" },
                  { value: "2", label: "2" },
                  { value: "3", label: "3" },
                  { value: "4", label: "4" },
                ]}
              />
              <FormSelect
                control={control}
                name="timeUnit"
                options={[
                  { value: "day", label: "day(s)" },
                  { value: "week", label: "week(s)" },
                  { value: "month", label: "month(s)" },
                ]}
              />
              <AnimatePresence>
                {timeUnit === "month" && (
                  <motion.div
                    key="monthly-setting"
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                    className="flex items-center gap-1 overflow-clip whitespace-nowrap"
                  >
                    <div>on the</div>
                    <FormSelect
                      control={control}
                      name="monthlySetting"
                      options={monthOptions}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
          <div className="pt-4">
            <div className="font-eyebrow text-medium-dark-gray">ends</div>
            <div className="font-body-sm flex w-full items-center gap-2">
              <div>After </div>
              <FormSelect
                control={control}
                name="occurrences"
                options={[
                  { value: "1", label: "1" },
                  { value: "2", label: "2" },
                  { value: "3", label: "3" },
                  { value: "4", label: "4" },
                  { value: "5", label: "5" },
                  { value: "6", label: "6" },
                  { value: "7", label: "7" },
                  { value: "8", label: "8" },
                  { value: "9", label: "9" },
                  { value: "10", label: "10" },
                ]}
              />
              occurrences
            </div>
          </div>

          <Credenza.Footer>
            <Button
              type="submit"
              isLoading={bulkDuplicatePostMutation.isPending}
              autoFocus={true}
            >
              Duplicate Post
            </Button>
          </Credenza.Footer>
        </form>
      </Credenza.Body>
    </Credenza.Root>
  );
};

export default BulkDuplicatePostModal;
