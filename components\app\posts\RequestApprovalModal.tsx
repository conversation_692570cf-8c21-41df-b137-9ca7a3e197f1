import { useState } from "react";

import { UserPlusIcon } from "@heroicons/react/24/outline";
import { DateTime } from "luxon";
import { toast } from "sonner";
import {
  Checkbox,
  DatePicker,
  OptionsDropdown,
  TextareaInput,
  UserAvatar,
} from "~/components";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { dateFromUTC, getFullName, trpc } from "~/utils";
import { Approval } from "../Approvals";

type RequestedApproval = {
  user: User;
  dueDate?: Date;
};

type User = {
  id: string;
  firstName: string | null;
  lastName: string | null;
};

type Props = {
  id: string;
  type: "post" | "campaign";
  open: boolean;
  setOpen: (open: boolean) => void;
  users: User[];
  onSuccess: (approvals: Approval[]) => void;
  publishDate?: Date;
  publishAt?: Date | null;
};

const RequestApprovalModal = ({
  id,
  type,
  publishDate,
  publishAt,
  open,
  setOpen,
  users,
  onSuccess,
}: Props) => {
  const trpcUtils = trpc.useUtils();
  const [requestedApprovals, setRequestedApprovals] = useState<
    RequestedApproval[]
  >([]);
  const [message, setMessage] = useState<string>("");
  const [isBlocking, setIsBlocking] = useState<boolean>(false);

  const requestApprovalMutation = trpc.approval.request.useMutation({
    onSuccess: (data) => {
      toast.success("Approval Successfully Requested");
      onSuccess(data.approvals);
      setRequestedApprovals([]);
      setOpen(false);
      trpcUtils.subscriber.getForContent.refetch({ id, type });
    },
  });

  const handleRequestApprovals = () => {
    requestApprovalMutation.mutate({
      id,
      type,
      message,
      approvals: requestedApprovals.map((approval) => {
        return {
          userID: approval.user.id,
          dueDate: approval.dueDate,
          isBlocking,
        };
      }),
    });
  };

  const publishDateUTC = publishDate ? dateFromUTC(publishDate) : null;
  const publishAtString =
    publishAt != null
      ? DateTime.fromJSDate(publishAt).toFormat("M/d/yyyy 'at' h:mm a ZZZZ")
      : publishDateUTC != null
        ? DateTime.fromJSDate(publishDateUTC).toFormat("M/d/yyyy")
        : null;

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header>
        <Credenza.Title>Add Approvers</Credenza.Title>
        <Credenza.Description>
          {publishAtString ? (
            <div className="font-body-sm text-medium-dark-gray mb-5">
              Post Date: {publishAtString}
            </div>
          ) : (
            "Add approvers to the post"
          )}
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        {requestedApprovals.length > 0 && (
          <div className="mb-3.5 flex flex-col gap-4">
            {requestedApprovals.map((requestedApproval) => {
              const { user } = requestedApproval;

              return (
                <div key={user.id} className="grid w-full grid-cols-4">
                  <div className="group col-span-2 flex items-center gap-2">
                    <UserAvatar
                      user={user}
                      showFullName={true}
                      onXClick={() => {
                        const index = requestedApprovals.findIndex(
                          (approval) => approval.user.id === user.id
                        );

                        const newRequestedApprovals = [...requestedApprovals];
                        newRequestedApprovals.splice(index, 1);

                        setRequestedApprovals(newRequestedApprovals);
                      }}
                    />
                  </div>
                  <DatePicker
                    value={requestedApproval.dueDate}
                    placeholder="Due Date"
                    minDate={new Date()}
                    onChange={(date) => {
                      const index = requestedApprovals.findIndex(
                        (approval) => approval.user.id === user.id
                      );
                      const newRequestedApprovals = [...requestedApprovals];
                      newRequestedApprovals[index] = {
                        ...requestedApproval,
                        dueDate: date,
                      };
                      setRequestedApprovals(newRequestedApprovals);
                    }}
                  />
                </div>
              );
            })}
          </div>
        )}
        <OptionsDropdown
          showSearch={true}
          options={users
            .filter(
              (user) =>
                !requestedApprovals
                  .map((approval) => approval.user.id)
                  .includes(user.id)
            )
            .map((user) => {
              return {
                key: user.id,
                label: getFullName(user),
                onClick: () => {
                  setRequestedApprovals([
                    ...requestedApprovals,
                    {
                      user: user,
                    },
                  ]);
                },
              };
            })}
          button={
            <Button variant="secondary" size="sm" onClick={() => {}}>
              <UserPlusIcon className="h-4 w-4" />
              Add Approver
            </Button>
          }
        />
        {requestedApprovals.length > 0 && (
          <div className="mt-[30px] mb-3 space-y-2">
            <TextareaInput
              rows={4}
              value={message}
              onChange={setMessage}
              placeholder="(Optional) Message to send along with approval request(s)"
            />
            <button
              className="flex items-start gap-2"
              onClick={() => {
                setIsBlocking(!isBlocking);
              }}
            >
              <Checkbox value={isBlocking} onChange={() => {}} />
              <div className="font-body-sm flex -translate-y-0.5 flex-col items-start">
                <div className="font-medium">
                  {requestedApprovals.length === 1
                    ? "Is this a "
                    : "Are these "}
                  blocking approval{requestedApprovals.length === 1 ? "" : "s"}?
                </div>
                <div className="medium-dark-gray">
                  Blocking approvals will prevent a schedule post from posting
                  if not approved.
                </div>
              </div>
            </button>
          </div>
        )}
        <Credenza.Footer>
          <Button variant="secondary" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            disabled={requestedApprovals.length === 0}
            isLoading={requestApprovalMutation.isPending}
            onClick={handleRequestApprovals}
          >
            Submit
          </Button>
        </Credenza.Footer>
      </Credenza.Body>
    </Credenza.Root>
  );
};

export default RequestApprovalModal;
