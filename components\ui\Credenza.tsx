"use client";

import * as React from "react";

import Dialog from "~/components/ui/Dialog";
import Drawer from "~/components/ui/Drawer";
import { cn } from "~/utils";

interface BaseProps {
  children: React.ReactNode;
}

interface RootCredenzaProps extends BaseProps {
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface CredenzaProps extends BaseProps {
  className?: string;
  asChild?: true;
}

const CredenzaContext = React.createContext<{ isMobile: boolean }>({
  isMobile: false,
});

const useCredenzaContext = () => {
  const context = React.useContext(CredenzaContext);
  if (!context) {
    throw new Error(
      "Credenza components cannot be rendered outside the Credenza Context"
    );
  }
  return context;
};

const Root = ({ children, ...props }: RootCredenzaProps) => {
  const isMobile = false;
  const Credenza = isMobile ? Drawer.Root : Dialog.Root;

  // Check if children contain a Trigger component
  const childrenArray = React.Children.toArray(children);
  const triggerIndex = childrenArray.findIndex(
    (child) => React.isValidElement(child) && child.type === Trigger
  );

  // Extract trigger (or null if not found) and content children since
  // they should be siblings
  const trigger = triggerIndex !== -1 ? childrenArray[triggerIndex] : null;
  const contentChildren =
    triggerIndex !== -1
      ? childrenArray.filter((_, index) => index !== triggerIndex)
      : childrenArray;

  return (
    <CredenzaContext.Provider value={{ isMobile }}>
      <Credenza {...props} {...(!isMobile && { autoFocus: true })}>
        {trigger}
        <Content>{contentChildren}</Content>
      </Credenza>
    </CredenzaContext.Provider>
  );
};

const Content = ({ className, children, ...props }: CredenzaProps) => {
  const { isMobile } = useCredenzaContext();
  const CredenzaContent = isMobile ? Drawer.Content : Dialog.Content;

  return (
    <CredenzaContent className={className} {...props}>
      {children}
    </CredenzaContent>
  );
};

const Trigger = ({ className, children, ...props }: CredenzaProps) => {
  const { isMobile } = useCredenzaContext();
  const CredenzaTrigger = isMobile ? Drawer.Trigger : Dialog.Trigger;

  return (
    <CredenzaTrigger className={className} {...props}>
      {children}
    </CredenzaTrigger>
  );
};

const Close = ({ className, children, ...props }: CredenzaProps) => {
  const { isMobile } = useCredenzaContext();
  const CredenzaClose = isMobile ? Drawer.Close : Dialog.Close;

  return (
    <CredenzaClose className={className} {...props}>
      {children}
    </CredenzaClose>
  );
};

const Description = ({ className, children, ...props }: CredenzaProps) => {
  const { isMobile } = useCredenzaContext();
  const CredenzaDescription = isMobile
    ? Drawer.Description
    : Dialog.Description;

  return (
    <CredenzaDescription className={className} {...props}>
      {children}
    </CredenzaDescription>
  );
};

const Header = ({ className, children, ...props }: CredenzaProps) => {
  const { isMobile } = useCredenzaContext();
  const CredenzaHeader = isMobile ? Drawer.Header : Dialog.Header;

  return (
    <CredenzaHeader className={className} {...props}>
      {children}
    </CredenzaHeader>
  );
};

const Title = ({ className, children, ...props }: CredenzaProps) => {
  const { isMobile } = useCredenzaContext();
  const CredenzaTitle = isMobile ? Drawer.Title : Dialog.Title;

  return (
    <CredenzaTitle className={className} {...props}>
      {children}
    </CredenzaTitle>
  );
};

const Body = ({ className, children, ...props }: CredenzaProps) => {
  const { isMobile } = useCredenzaContext();

  if (isMobile) {
    return (
      <div
        className={cn("max-h-[80vh] overflow-y-auto px-4 md:px-0", className)}
        {...props}
      >
        {children}
      </div>
    );
  } else {
    return (
      <Dialog.Body className={className} {...props}>
        {children}
      </Dialog.Body>
    );
  }
};

const Footer = ({ className, children, ...props }: CredenzaProps) => {
  const { isMobile } = useCredenzaContext();
  const CredenzaFooter = isMobile ? Drawer.Footer : Dialog.Footer;

  return (
    <CredenzaFooter className={className} {...props}>
      {children}
    </CredenzaFooter>
  );
};

export default {
  Root,
  Trigger,
  Close,
  Description,
  Header,
  Title,
  Body,
  Footer,
};
