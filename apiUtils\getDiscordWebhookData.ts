import axios from "axios";

const getDiscordWebhookData = async (webhookUrl: string) => {
  try {
    const response = await axios.get(webhookUrl);

    const data: {
      id: string;
      avatar: string;
      channel_id: string;
      guild_id: string;
      name: string;
    } = response.data;

    const profileImageUrl =
      data.avatar != null
        ? `https://cdn.discordapp.com/avatars/${data.id}/${data.avatar}.png`
        : null;

    return {
      urlLast4: webhookUrl.slice(webhookUrl.length - 4),
      username: data.name,
      profileImageUrl,
    };
  } catch (error) {
    console.error(`Discord Webhook ${webhookUrl} deleted`, error);
    throw new Error(`Discord Webhook ${webhookUrl} deleted`, { cause: error });
  }
};

export default getDiscordWebhookData;
