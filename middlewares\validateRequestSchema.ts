import { StatusCodes } from "http-status-codes";
import { NextApiHandler, NextApiRequest, NextApiResponse } from "next";
import { ZodIssue, ZodType } from "zod";

const zodIssueToErrors = (issues: ZodIssue[]) => {
  return issues.map((issue) => {
    const code = issue.code === "invalid_type" ? "Invalid Type" : issue.code;

    if (issue.message) {
      return `${issue.path[0]}: ${issue.message}`;
    } else {
      // @ts-ignore
      return `${issue.path[0]} ${code}: Expected ${issue.expected} & received ${issue.received}`;
    }
  });
};

const validateRequestSchema = (
  handler: NextApiHandler,
  schema: {
    body?: ZodType;
    query?: ZodType;
  }
) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const { body, query } = req;

    if (schema.body) {
      let requestBody = body;
      if (typeof body === "string") {
        requestBody = await JSON.parse(body);
      }

      const parseResult = schema.body.safeParse(requestBody);

      if (!parseResult.success) {
        return res.status(StatusCodes.UNPROCESSABLE_ENTITY).send({
          errors: [{ message: zodIssueToErrors(parseResult.error.issues) }],
        });
      }
    }

    if (schema.query) {
      const parseResult = schema.query.safeParse(query);

      if (!parseResult.success) {
        return res.status(StatusCodes.UNPROCESSABLE_ENTITY).send({
          errors: [{ message: zodIssueToErrors(parseResult.error.issues) }],
        });
      }
    }

    return handler(req, res);
  };
};

export default validateRequestSchema;
