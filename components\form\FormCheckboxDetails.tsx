import { ReactElement } from "react";

import { Controller, Path } from "react-hook-form";

import { CheckboxDetails } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import camelCaseToWords from "~/utils/camelCaseToWords";

type FormCheckboxDetailsProps<T> = {
  control: any;
  name: Path<T>;
  disabled?: boolean;
  label: string;
  detail?: string | ReactElement;
  count: number;
  totalCount: number;
  percentageLabel: string;
  rules?: Rules;
};

const FormToggle = <T,>({
  control,
  name,
  label,
  detail,
  count,
  totalCount,
  percentageLabel,
  rules = {},
}: FormCheckboxDetailsProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <div>
            <CheckboxDetails
              value={value}
              onChange={onChange}
              label={label}
              detail={detail}
              count={count}
              totalCount={totalCount}
              percentageLabel={percentageLabel}
            />
            <Error error={error} />
          </div>
        );
      }}
    />
  );
};

export default FormToggle;
