import { slugify } from "inngest";
import { DateTime } from "luxon";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { inngest } from "../inngest";

export default inngest.createFunction(
  {
    id: slugify("Referral Expired"),
    name: "Referral Expired",
    cancelOn: [
      {
        event: "referral.subscribed",
        match: "data.referralID",
      },
    ],
  },
  { event: "referral.created" },

  async ({ event, step }) => {
    const { referralID } = event.data;

    await step.sleepUntil(
      "wait-until-date",
      DateTime.now().plus({ days: 90 }).toJSDate()
    );

    await step.run("Mark Referral As Expired", async () => {
      const referral = await db.referral.findFirst({
        where: {
          id: referralID,
        },
        include: {
          workspace: {
            select: {
              id: true,
              companyID: true,
            },
          },
        },
      });

      if (referral?.status === "SignedUp") {
        await db.referral.update({
          where: {
            id: referralID,
          },
          data: {
            status: "Expired",
          },
        });

        posthog.capture({
          distinctId: referral.referredUserID,
          event: POSTHOG_EVENTS.referral.expired,
          properties: {
            referralID: referral.id,
            referringUserID: referral.referringUserID,
            referralCreatedAt: referral.createdAt,
          },
          groups: {
            workspace: referral.workspaceID,
            company: referral.workspace.companyID,
          },
        });

        return {
          status: "Expired",
        };
      } else {
        return {
          status: "Ignored",
        };
      }
    });
  }
);
