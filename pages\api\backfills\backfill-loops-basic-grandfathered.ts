import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import { LoopsClient } from "loops";
import { db } from "~/clients";
import { getFullName } from "~/utils";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const companies = await db.company.findMany({
    where: {
      subscription: {
        tier: "Basic",
        isGrandfathered: true,
        status: "active",
      },
    },
    select: {
      id: true,
      name: true,
      stripeCustomerID: true,
      subscription: {
        select: {
          stripeSubscriptionID: true,
        },
      },
      workspaces: {
        select: {
          userWorkspaces: {
            orderBy: {
              createdAt: "asc",
            },
            take: 1,
            select: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      },
    },
  });

  const loops = new LoopsClient(process.env.LOOPS_API_KEY as string);
  const BASIC_GRANDFATHERED_LIST_ID = "cm2xjnmxo013x0ll4ae5v6rxf";

  const finalComapnies = companies.filter(
    (company) => company.workspaces.length > 0
  );

  const cleanedCompanies: any[] = [];

  for await (const company of finalComapnies) {
    const user = company.workspaces[0].userWorkspaces[0]?.user;
    if (!user) {
      console.log("No user found for company", company.name);
      return;
    }
    const response = await loops.updateContact(
      user.email,
      {
        firstName: user.firstName ?? "",
        lastName: user.lastName ?? "",
      },
      {
        [BASIC_GRANDFATHERED_LIST_ID]: true,
      }
    );

    console.log(response);

    cleanedCompanies.push({
      id: company.id,
      name: company.name,
      stripeSubscriptionID: company.subscription?.stripeSubscriptionID,
      user: {
        name: getFullName(company.workspaces[0].userWorkspaces[0]?.user),
        email: company.workspaces[0].userWorkspaces[0]?.user.email,
      },
    });
  }

  res.status(StatusCodes.OK).send({
    companies: cleanedCompanies,
  });
};

export default handler;
