import { useEffect } from "react";

import { useRouter } from "next/router";
import { <PERSON><PERSON>, Loader } from "~/components";
import { CommentsAndActivity } from "~/components/app";
import { useBreakpoints, useWindowTitle } from "~/hooks";
import { useThreadComments, useWorkspace } from "~/providers";
import { trpc } from "~/utils";
import CrossPostBar from "./CrossPostBar";
import PostHeaderNewDesign from "./PostHeaderNewDesign";
import PostMainBar from "./PostMainBar";
import PostSidebarNewDesign from "./PostSidebarNewDesign";
import ThreadCommentsSidebar from "./ThreadCommentsSidebar";
import { WebflowField } from "./webflow/WebflowFieldInput";

type Props = {
  id: string;
};

const PostPageNewDesign = ({ id }: Props) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();
  const { isDesktop } = useBreakpoints();

  const { workspaces, currentWorkspace, setCurrentWorkspace } = useWorkspace();
  const { isThreadCommentsSidebarVisible } = useThreadComments();

  const postQuery = trpc.post.get.useQuery(
    {
      id,
    },
    {
      refetchInterval: false,
      refetchIntervalInBackground: false,
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    }
  );

  const data = postQuery.data;
  const post = data?.post || null;
  const isReadOnly = data?.isReadOnly;

  useEffect(() => {
    if (data) {
      if (
        currentWorkspace?.id !== data.post.workspaceID &&
        !data.post.isSharedPublicly
      ) {
        const newWorkspace = workspaces.find(
          (workspace) => workspace.id === data.post.workspaceID
        );
        if (newWorkspace) {
          setCurrentWorkspace(newWorkspace);
        } else {
          throw new Error("Unauthorized");
        }
      }

      trpcUtils.dub.getPostLinks.prefetch({
        postID: data.post.id,
      });

      // Need to check if this is needed after converting to an idea/post after redesign
      const path = router.asPath;
      if (path.includes("/posts") && data.post.isIdea) {
        router.push(
          path.replace("/posts", "/ideas"),
          path.replace("/posts", "/ideas"),
          {
            shallow: true,
          }
        );
      } else if (path.includes("/ideas") && !data.post.isIdea) {
        router.push(
          path.replace("/ideas", "/posts"),
          path.replace("/ideas", "/posts"),
          {
            shallow: true,
          }
        );
      }
    }
  }, [data]);

  useEffect(() => {
    if (id && post && id !== post.id) {
      router.reload();
    }
  }, [id]);

  useWindowTitle(post ? `${post.title}` : "Assembly");

  if (!!postQuery.error) {
    if (postQuery.error.data?.code === "UNAUTHORIZED") {
      router.push("/login");
    }

    return (
      <div className="w-full">
        <div className="mx-auto mt-4 max-w-lg">
          <Alert
            title="Unable to Load Post Data"
            intent="danger"
            message={postQuery.error.message}
          />
        </div>
      </div>
    );
  }

  if (!post || id == null || isReadOnly == null) {
    return (
      <div className="flex h-full w-full items-center justify-center px-10 py-4">
        <Loader size="lg" />
      </div>
    );
  }

  return (
    <div className="h-full w-full pb-3">
      <PostHeaderNewDesign post={post} isReadOnly={isReadOnly} />
      <div className="mt-[116px] flex flex-col-reverse md:mt-[60px] lg:flex-row">
        <div className="relative w-full lg:pr-[320px]">
          <div className="h-full w-full pr-7 pl-8 lg:mt-[30px] xl:mx-auto xl:w-[622px] xl:pr-0 xl:pl-0">
            <CrossPostBar post={post} isReadOnly={isReadOnly} />
            <PostMainBar
              post={{
                ...post,
                webflowContent: post.webflowContent && {
                  ...post.webflowContent,
                  fields: post.webflowContent.fields.map((field) => {
                    return field as WebflowField;
                  }),
                },
              }}
              isReadOnly={isReadOnly}
            />
            <CommentsAndActivity
              id={id}
              type="post"
              comments={post.comments}
              activity={post.activity}
              isReadOnly={isReadOnly}
            />
          </div>
        </div>
        <div className="border-lightest-gray top-[60px] right-0 w-full overflow-auto border-l lg:fixed lg:h-[calc(100vh-60px)] lg:w-[320px]">
          {isThreadCommentsSidebarVisible && isDesktop ? (
            <ThreadCommentsSidebar />
          ) : (
            <PostSidebarNewDesign post={post} isReadOnly={isReadOnly} />
          )}
        </div>
      </div>
    </div>
  );
};

export default PostPageNewDesign;
