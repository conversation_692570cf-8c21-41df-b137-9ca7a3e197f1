import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import assert from "assert";
import { db } from "~/clients";
import Intercom from "~/clients/Intercom";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const companies = await db.company.findMany({
    where: {
      subscription: {
        isNot: null,
      },
    },
    select: {
      id: true,
      name: true,
      subscription: {
        select: {
          isGrandfathered: true,
          tier: true,
        },
      },
      _count: {
        select: {
          workspaces: true,
        },
      },
    },
  });
  const promises = companies.map(async (company) => {
    const { id, name, subscription, _count } = company;

    assert(subscription);

    const numWorkspaces = _count.workspaces;
    const isAgency = numWorkspaces > 1;

    let plan = subscription.isGrandfathered
      ? `Grandfathered - ${subscription.tier}`
      : subscription.tier;

    const theraCompanyID = "2463d9e8-8744-4404-87c1-9869deb004a8";

    if (isAgency && id !== theraCompanyID) {
      plan = "Agency";
    }

    await Intercom.companies.create({
      companyId: id,
      name,
      plan,
    });
  });

  await Promise.all(promises);

  res.status(StatusCodes.OK).send({
    companies,
  });
};

export default handler;
