import axios from "axios";
import { slugify } from "inngest";
import { inngest } from "../inngest";

const RETRIES = 4;

const EXPANDI_WEBHOOK_URLS = {
  // https://app.expandi.io/campaign/665583/settings?tab=tab-integration&instanceType=CampaignsInstance
  JEFFREY_CONNECT_AND_FOLLOW_ASSEMBLY:
    "https://api.liaufa.com/api/v1/open-api/campaign-instance/665583/assign/?key=4cdd7896-f874-4971-b45a-2a7fdf984f49&secret=02cc1041-80fd-4903-83de-9de57617c276",
  // https://app.expandi.io/campaign/669387/settings?tab=tab-integration&instanceType=CampaignsInstance
  FRANCISCO_CONNECT:
    "https://api.liaufa.com/api/v1/open-api/campaign-instance/669387/assign/?key=7ce4ca72-9a79-4ff3-be90-d114c3ba99c0&secret=d8371142-87c4-4778-b81b-fb67b7ed3fe0",
};

export default inngest.createFunction(
  {
    id: slugify("Expandi Connect with LinkedIn Account"),
    retries: RETRIES,
    name: "Expandi Connect with LinkedIn Account",
  },
  [
    {
      event: "linkedin.account.connected",
      if: `event.data.accountType == "User"`,
    },
  ],
  async ({ event, step, attempt }) => {
    const { vanityName } = event.data;

    const connectWithJeffreyResponse = await step.run(
      "Expandi Connect with LinkedIn Account",
      async () => {
        try {
          const responseJeffrey = await axios.post(
            EXPANDI_WEBHOOK_URLS.JEFFREY_CONNECT_AND_FOLLOW_ASSEMBLY,
            {
              profile_link: `http://www.linkedin.com/in/${vanityName}`,
            }
          );

          const responseFrancisco = await axios.post(
            EXPANDI_WEBHOOK_URLS.FRANCISCO_CONNECT,
            {
              profile_link: `http://www.linkedin.com/in/${vanityName}`,
            }
          );

          const dataJeffrey = responseJeffrey.data;
          const dataFrancisco = responseFrancisco.data;

          return {
            dataJeffrey,
            dataFrancisco,
          };
        } catch (error) {
          console.error(error);
          throw error;
        }
      }
    );

    return {
      connectWithJeffreyResponse,
    };
  }
);
