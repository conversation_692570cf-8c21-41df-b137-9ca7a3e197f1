import { DateTime } from "luxon";
import { ReactElement } from "react";
import { Tooltip } from "~/components";
import { useWorkspace } from "~/providers";

type Props = {
  date: Date | null;
  children: ReactElement;
};

const TimezoneTooltip = ({ date, children }: Props) => {
  const { currentWorkspace } = useWorkspace();

  const workspaceTimezone = currentWorkspace?.timezone;

  if (!date || !workspaceTimezone) {
    return children;
  } else {
    const workspaceOffset =
      DateTime.now().setZone(workspaceTimezone).offsetNameShort;
    const localOffset = DateTime.now().offsetNameShort;

    if (workspaceOffset === localOffset) {
      return children;
    }

    return (
      <Tooltip
        delayDuration={500}
        value={
          <div className="font-body-sm">
            <div className="border-lightest-gray mb-1.5 border-b pb-1">
              Time Conversion
            </div>
            <div className="flex items-center gap-2">
              <div className="text-medium-dark-gray flex flex-col items-start">
                <div>{DateTime.now().offsetNameShort}</div>
                <div>
                  {DateTime.now().setZone(workspaceTimezone).offsetNameShort}
                </div>
              </div>
              <div className="flex flex-col items-start">
                <div>{DateTime.fromJSDate(date).toFormat("M/dd")}</div>
                <div>
                  {DateTime.fromJSDate(date)
                    .setZone(workspaceTimezone)
                    .toFormat("M/dd")}
                </div>
              </div>
              <div className="flex flex-col items-start">
                <div>{DateTime.fromJSDate(date).toFormat("h:mm a")}</div>
                <div>
                  {DateTime.fromJSDate(date)
                    .setZone(workspaceTimezone)
                    .toFormat("h:mm a")}
                </div>
              </div>
            </div>
          </div>
        }
      >
        {children}
      </Tooltip>
    );
  }
};

export default TimezoneTooltip;
