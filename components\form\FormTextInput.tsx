import { Controller, Path } from "react-hook-form";

import { Label, TextInput } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import camelCaseToWords from "~/utils/camelCaseToWords";
import { Kind, Size } from "../TextInput";

type FormTextInputProps<T> = {
  control: any;
  name: Path<T>;
  placeholder?: string;
  disabled?: boolean;
  isReadOnly?: boolean;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  kind?: Kind;
  size?: Size;
  label?: string;
  isOptional?: boolean;
  tooltipText?: string;
  type?: "text" | "password" | "email" | "number";
  rules?: Rules;
  suffix?: string;
};

const FormTextInput = <T,>({
  control,
  name,
  placeholder,
  isReadOnly,
  onBlur,
  kind,
  size,
  disabled,
  label,
  tooltipText,
  type = "text",
  rules = {},
  suffix,
}: FormTextInputProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  if (typeof rules.min === "number") {
    rules = {
      ...rules,
      min: { value: rules.min, message: `Minimum value is ${rules.min}` },
    };
  }

  if (typeof rules.max === "number") {
    rules = {
      ...rules,
      max: { value: rules.max, message: `Maximum value is ${rules.max}` },
    };
  }

  if (typeof rules.maxLength === "number") {
    rules = {
      ...rules,
      maxLength: {
        value: rules.maxLength,
        message: `Max length is ${rules.maxLength}`,
      },
    };
  }

  if (typeof rules.minLength === "number") {
    rules = {
      ...rules,
      minLength: {
        value: rules.minLength,
        message: `Max length is ${rules.minLength}`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({
        field: { onChange, onBlur: controllerOnBlur, value },
        fieldState: { error },
      }) => {
        return (
          <Label
            value={label}
            isRequired={!!rules.required}
            tooltipText={tooltipText}
          >
            <TextInput
              value={value as string}
              onChange={onChange}
              type={type}
              kind={kind}
              size={size}
              onBlur={(e) => {
                controllerOnBlur();
                onBlur?.(e);
              }}
              placeholder={placeholder}
              disabled={disabled}
              isReadOnly={isReadOnly}
              isInvalid={!!error}
              suffix={suffix}
            />
            <Error error={error} />
          </Label>
        );
      }}
    />
  );
};

export default FormTextInput;
