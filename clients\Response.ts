import { StatusCodes } from "http-status-codes";

export const createSuccessResponse = <T>(
  data: T,
  status: StatusCodes
): Response<T> => ({
  status,
  data,
  errors: null,
  isAuthError: false,
  success: true,
});

const authErrorStatuses = [StatusCodes.UNAUTHORIZED, StatusCodes.FORBIDDEN];

export const createErrorResponse = <T>(
  errors: string[],
  status: StatusCodes,
  rawError?: {} | null,
  isAuthError?: boolean
): Response<T> => ({
  status,
  data: null,
  errors,
  rawError,
  isAuthError: isAuthError ?? authErrorStatuses.includes(status),
  success: false,
});

export type Response<T> =
  | {
      status: StatusCodes;
      data: T;
      rawError?: null;
      errors: null;
      isAuthError: false;
      success: true;
    }
  | {
      status: StatusCodes;
      data: null;
      rawError?: {} | null;
      errors: string[];
      isAuthError: boolean;
      success: false;
    };
