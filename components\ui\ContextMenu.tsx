// Adapted from https://ui.shadcn.com/docs/primitives/context-menu
import * as ContextMenuPrimitive from "@radix-ui/react-context-menu";
import * as React from "react";

import * as Icons from "@heroicons/react/24/outline";

import { CheckIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";

const Root = ContextMenuPrimitive.Root;

const Trigger = ContextMenuPrimitive.Trigger;

const Group = ContextMenuPrimitive.Group;

const Portal = ContextMenuPrimitive.Portal;

const Sub = ContextMenuPrimitive.Sub;

const RadioGroup = ContextMenuPrimitive.RadioGroup;

const SubTrigger = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubTrigger> & {
    icon?: keyof typeof Icons;
  }
>(({ className, icon, children, ...props }, ref) => {
  const Icon = icon ? Icons[icon] : null;

  return (
    <ContextMenuPrimitive.SubTrigger
      ref={ref}
      className={classNames(
        "hover:bg-slate active:bg-slate font-body-sm data-[state=open]:bg-slate flex cursor-default items-center gap-1 rounded-md p-1.5 outline-hidden transition-colors select-none",
        className
      )}
      {...props}
    >
      {Icon ? <Icon className="h-4 w-4" /> : null}
      {children}
      <ChevronRightIcon className="ml-auto h-4 w-4" />
    </ContextMenuPrimitive.SubTrigger>
  );
});
SubTrigger.displayName = ContextMenuPrimitive.SubTrigger.displayName;

const SubContent = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubContent>
>(({ className, ...props }, ref) => (
  <ContextMenuPrimitive.SubContent
    ref={ref}
    className={classNames(
      "animate-in slide-in-from-left-1 z-50 min-w-32 translate-x-2 overflow-hidden rounded-[10px] bg-white p-2 shadow-md",
      className
    )}
    {...props}
  />
));
SubContent.displayName = ContextMenuPrimitive.SubContent.displayName;

const Content = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Content>
>(({ className, ...props }, ref) => (
  <ContextMenuPrimitive.Portal>
    <ContextMenuPrimitive.Content
      ref={ref}
      className={classNames(
        "animate-in fade-in-80 z-50 min-w-56 overflow-hidden rounded-[10px] bg-white p-3 shadow-md",
        className
      )}
      {...props}
    />
  </ContextMenuPrimitive.Portal>
));
Content.displayName = ContextMenuPrimitive.Content.displayName;

const Item = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Item> & {
    icon?: keyof typeof Icons;
    intent?: "danger" | "none" | "success";
    inset?: boolean;
  }
>(({ className, icon, intent, children, inset, ...props }, ref) => {
  const Icon = icon ? Icons[icon] : null;

  return (
    <ContextMenuPrimitive.Item
      ref={ref}
      className={classNames(
        "font-body-sm hover:bg-slate flex w-full cursor-pointer items-center gap-1 rounded-md px-1.5 py-1 text-left whitespace-nowrap outline-hidden transition-colors",
        {
          "text-success": intent === "success",
          "text-danger": intent === "danger",
          "text-off-black": intent === "none" || intent == null,
        }
      )}
      {...props}
    >
      {Icon ? <Icon className="h-4 w-4" /> : null}
      {children}
    </ContextMenuPrimitive.Item>
  );
});
Item.displayName = ContextMenuPrimitive.Item.displayName;

const CheckboxItem = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.CheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <ContextMenuPrimitive.CheckboxItem
    ref={ref}
    className={classNames(
      "relative flex cursor-default items-center rounded-xs py-1.5 pr-2 pl-8 text-sm font-medium outline-hidden select-none focus:bg-slate-100 data-disabled:pointer-events-none data-disabled:opacity-50 dark:focus:bg-slate-700",
      className
    )}
    checked={checked}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <ContextMenuPrimitive.ItemIndicator>
        <CheckIcon className="h-4 w-4" />
      </ContextMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </ContextMenuPrimitive.CheckboxItem>
));
CheckboxItem.displayName = ContextMenuPrimitive.CheckboxItem.displayName;

const RadioItem = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioItem>
>(({ className, children, ...props }, ref) => (
  <ContextMenuPrimitive.RadioItem
    ref={ref}
    className={classNames(
      "relative flex cursor-default items-center rounded-xs py-1.5 pr-2 pl-8 text-sm font-medium outline-hidden select-none focus:bg-slate-100 data-disabled:pointer-events-none data-disabled:opacity-50 dark:focus:bg-slate-700",
      className
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <ContextMenuPrimitive.ItemIndicator>
        <div className="border-light-gray h-2 w-2 rounded-full border fill-current" />
      </ContextMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </ContextMenuPrimitive.RadioItem>
));
RadioItem.displayName = ContextMenuPrimitive.RadioItem.displayName;

const Label = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Label> & {
    inset?: boolean;
  }
>(({ className, inset, ...props }, ref) => (
  <ContextMenuPrimitive.Label
    ref={ref}
    className={classNames(
      "p-1.5 text-sm font-semibold text-slate-900 dark:text-slate-300",
      inset && "pl-8",
      className
    )}
    {...props}
  />
));
Label.displayName = ContextMenuPrimitive.Label.displayName;

const Separator = React.forwardRef<
  React.ElementRef<typeof ContextMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <ContextMenuPrimitive.Separator
    ref={ref}
    className={classNames("bg-light-gray my-1 h-px", className)}
    {...props}
  />
));
Separator.displayName = ContextMenuPrimitive.Separator.displayName;

const Shortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={classNames(
        "ml-auto text-xs tracking-widest text-slate-500",
        className
      )}
      {...props}
    />
  );
};
Shortcut.displayName = "ContextMenuShortcut";

export default {
  CheckboxItem,
  Content,
  Group,
  Item,
  Label,
  Sub,
  Portal,
  RadioGroup,
  RadioItem,
  Root,
  Separator,
  Shortcut,
  SubContent,
  SubTrigger,
  Trigger,
};
