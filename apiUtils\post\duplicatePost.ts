import { Channel } from "@prisma/client";
import { J<PERSON><PERSON>ontent } from "@tiptap/core";
import { TRPCError } from "@trpc/server";
import { copyStorageFiles } from "apiUtils/copyStorageFiles";
import getStorageAssetsForDiscord from "apiUtils/discord/getStorageAssetsForDiscord";
import getStorageAssetsForFacebook from "apiUtils/facebook/getStorageAssetsForFacebook";
import getStorageAssetsForInstagram from "apiUtils/instagram/getStorageAssetsForInstagram";
import getStorageAssetsForLinkedIn from "apiUtils/linkedin/getStorageAssetsForLinkedIn";
import getStorageAssetsForSlack from "apiUtils/slack/getStorageAssetsForSlack";
import getStorageAssetsForThreads from "apiUtils/threads/getStorageAssetsForThreads";
import getStorageAssetsForTikTok from "apiUtils/tiktok/getStorageAssetsForTikTok";
import getStorageAssetsForTwitter from "apiUtils/twitter/getStorageAssetsForTwitter";
import getStorageAssetsForYouTubeShorts from "apiUtils/youtube/getStorageAssetsForYouTubeShorts";
import assert from "assert";
import { Dub } from "dub";
import { PostHog } from "posthog-node";
import { db } from "~/clients";
import { API_CREATED_FOLDER_ID } from "~/constants/dub";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { assertPostOrIdea, removeEmptyElems } from "~/utils";
import { getPostProfiles } from "~/utils/posts/getPostProfiles";

const duplicatePost = async ({
  sourcePostID,
  updatedFields,
  userID,
  workspaceID,
  posthog,
}: {
  sourcePostID: string;
  updatedFields?: {
    title: string;
    publishDate: Date;
    publishTime?: Date | null;
    publishAt?: Date | null;
  }[];
  userID: string;
  workspaceID: string;
  posthog: PostHog;
}) => {
  const sourcePost = await db.post.findFirstOrThrow({
    where: {
      id: sourcePostID,
    },
    include: {
      postLabels: true,
      subscribers: true,
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
      dubLinks: {
        select: {
          id: true,
          url: true,
          shortLink: true,
          domainID: true,
          domain: {
            select: {
              slug: true,
            },
          },
        },
      },
      instagramContent: {
        include: {
          coverPhoto: true,
          location: {
            select: {
              id: true,
            },
          },
          assets: {
            include: {
              asset: true,
              tags: true,
            },
          },
          engagements: true,
        },
      },
      facebookContent: {
        select: {
          id: true,
          copy: true,
          editorState: true,
          coverPhoto: true,
          type: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
              fileName: true,
            },
          },
        },
      },
      tweets: {
        select: {
          id: true,
          copy: true,
          threadPosition: true,
          editorState: true,
          mediaTaggedUsernames: true,
          engagements: {
            select: {
              type: true,
              copy: true,
              editorState: true,
              contentID: true,
              postDelaySeconds: true,
              integrationID: true,
              createdByID: true,
            },
          },
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
      },
      discordContent: {
        select: {
          id: true,
          copy: true,
          editorState: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
      },
      linkedInContent: {
        select: {
          id: true,
          copy: true,
          editorState: true,
          documentTitle: true,
          coverPhoto: true,
          captions: true,
          assets: {
            select: {
              id: true,
              position: true,
              asset: true,
            },
          },
          engagements: true,
        },
      },
      threadsContent: {
        select: {
          id: true,
          copy: true,
          editorState: true,
          threadPosition: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
            orderBy: {
              position: "asc",
            },
          },
        },
        orderBy: {
          threadPosition: "asc",
        },
      },
      tikTokContent: {
        select: {
          id: true,
          copy: true,
          editorState: true,
          privacyLevel: true,
          isCommentDisabled: true,
          isDuetDisabled: true,
          isStitchDisabled: true,
          isPromotingBusiness: true,
          isPaidPartnership: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
      },
      slackContent: {
        select: {
          id: true,
          copy: true,
          editorState: true,
          assets: {
            select: {
              id: true,
              asset: true,
              position: true,
            },
          },
        },
      },
      youTubeShortsContent: {
        include: {
          asset: {
            include: {
              asset: true,
            },
          },
        },
      },
    },
  });

  if (sourcePost.workspaceID !== workspaceID) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have access to this post",
    });
  }

  const newPostFields =
    !!updatedFields && updatedFields.length > 0
      ? updatedFields
      : [
          {
            title: `Copy of ${sourcePost.title}`,
            publishDate: sourcePost.publishDate,
            publishTime: sourcePost.publishTime,
            publishAt: sourcePost.publishAt,
          },
        ];

  const dub = new Dub({
    token: process.env.DUB_API_KEY,
  });

  const promises = newPostFields.map(async (postFields) => {
    const linkPromises = sourcePost.dubLinks.map(async (link) => {
      const dubLink = await dub.links.create({
        url: link.url,
        domain: link.domain.slug,
        tenantId: workspaceID,
        folderId: API_CREATED_FOLDER_ID,
      });

      return {
        ...dubLink,
        shortLink: dubLink.shortLink.replace("https://", ""),
        oldShortLink: link.shortLink,
        domainID: link.domainID,
      };
    });

    const dubLinks = await Promise.all(linkPromises);
    const dubLinksMapping = Object.fromEntries(
      dubLinks.map((dubLink) => [dubLink.oldShortLink, dubLink.shortLink])
    );

    const replaceDubLinks = <T extends JSONContent | string | null>(
      content: T
    ): T extends null ? undefined : T => {
      if (content === null) {
        return undefined as T extends null ? undefined : T;
      }

      let contentString =
        typeof content === "string"
          ? content
          : typeof content === "object" && content !== null
            ? JSON.stringify(content)
            : null;

      for (const [oldLink, newLink] of Object.entries(dubLinksMapping)) {
        contentString = contentString!.replaceAll(oldLink, newLink);
      }

      if (typeof content === "string") {
        return contentString as T extends null ? undefined : T;
      } else if (typeof content === "object" && content !== null) {
        return JSON.parse(contentString!) as T extends null ? undefined : T;
      } else {
        return undefined as T extends null ? undefined : T;
      }
    };

    const post = await db.post.create({
      data: {
        title: postFields.title,
        channels: sourcePost.channels,
        campaignID: sourcePost.campaignID,
        workspaceID: sourcePost.workspaceID,
        publishDate: postFields.publishDate,
        publishTime: postFields.publishTime,
        publishAt: postFields.publishAt,
        status: ["Scheduled", "Posted"].includes(sourcePost.status)
          ? "Finalized"
          : sourcePost.status,
        isIdea: sourcePost.isIdea,
        createdByID: userID,

        // Short Links
        dubLinks: {
          createMany: {
            data: dubLinks.map((dubLink) => ({
              linkID: dubLink.id,
              key: dubLink.key,
              url: dubLink.url,
              shortLink: dubLink.shortLink,
              domainID: dubLink.domainID,
            })),
          },
        },

        // Integrations
        twitterIntegrationID: sourcePost.twitterIntegrationID,
        linkedInIntegrationID: sourcePost.linkedInIntegrationID,
        discordWebhookID: sourcePost.discordWebhookID,
        instagramIntegrationID: sourcePost.instagramIntegrationID,
        facebookIntegrationID: sourcePost.facebookIntegrationID,
        tikTokIntegrationID: sourcePost.tikTokIntegrationID,
        slackIntegrationID: sourcePost.slackIntegrationID,
        youTubeIntegrationID: sourcePost.youTubeIntegrationID,
        threadsIntegrationID: sourcePost.threadsIntegrationID,

        otherChannelEditorState:
          sourcePost.otherChannelEditorState || undefined,
        notesEditorState: sourcePost.notesEditorState || undefined,

        // Labels and Subscribers
        postLabels: {
          createMany: {
            data: sourcePost.postLabels.map((postLabel) => {
              return {
                labelID: postLabel.labelID,
              };
            }),
          },
        },
        subscribers: {
          createMany: {
            data: sourcePost.subscribers.map((subscriber) => {
              return {
                userID: subscriber.userID,
                createdByID: userID,
              };
            }),
          },
        },

        // Duplicates of content
        tweets: {
          createMany: {
            data: sourcePost.tweets.map((tweet) => {
              return {
                copy: replaceDubLinks(tweet.copy),
                editorState: replaceDubLinks(tweet.editorState),
                threadPosition: tweet.threadPosition,
                mediaTaggedUsernames: tweet.mediaTaggedUsernames,
              };
            }),
          },
        },
        // Create the assets & tags later when we have the new IDs
        instagramContent: {
          create:
            (sourcePost.instagramContent && {
              copy: replaceDubLinks(sourcePost.instagramContent.copy),
              editorState: replaceDubLinks(
                sourcePost.instagramContent.editorState
              ),
              type: sourcePost.instagramContent.type,
              locationID: sourcePost.instagramContent.locationID,
            }) ||
            undefined,
        },
        facebookContent: {
          create:
            (sourcePost.facebookContent && {
              copy: replaceDubLinks(sourcePost.facebookContent.copy),
              editorState: replaceDubLinks(
                sourcePost.facebookContent.editorState
              ),
              type: sourcePost.facebookContent.type,
            }) ||
            undefined,
        },
        discordContent: {
          create:
            (sourcePost.discordContent && {
              copy: replaceDubLinks(sourcePost.discordContent.copy),
              editorState: replaceDubLinks(
                sourcePost.discordContent.editorState
              ),
            }) ||
            undefined,
        },
        linkedInContent: {
          create:
            (sourcePost.linkedInContent && {
              copy: replaceDubLinks(sourcePost.linkedInContent.copy),
              editorState: replaceDubLinks(
                sourcePost.linkedInContent.editorState
              ),
              documentTitle: sourcePost.linkedInContent.documentTitle,
            }) ||
            undefined,
        },
        threadsContent: {
          createMany: {
            data: sourcePost.threadsContent.map((content) => {
              return {
                copy: replaceDubLinks(content.copy),
                editorState: replaceDubLinks(content.editorState),
                threadPosition: content.threadPosition,
              };
            }),
          },
        },
        tikTokContent: {
          create:
            (sourcePost.tikTokContent && {
              copy: replaceDubLinks(sourcePost.tikTokContent.copy),
              editorState: replaceDubLinks(
                sourcePost.tikTokContent.editorState
              ),
            }) ||
            undefined,
        },
        slackContent: {
          create:
            (sourcePost.slackContent && {
              copy: replaceDubLinks(sourcePost.slackContent.copy),
              editorState: replaceDubLinks(sourcePost.slackContent.editorState),
            }) ||
            undefined,
        },
        youTubeShortsContent: {
          create:
            (sourcePost.youTubeShortsContent && {
              title: replaceDubLinks(sourcePost.youTubeShortsContent?.title),
              titleEditorState: replaceDubLinks(
                sourcePost.youTubeShortsContent?.titleEditorState
              ),
              description: replaceDubLinks(
                sourcePost.youTubeShortsContent?.description
              ),
              descriptionEditorState: replaceDubLinks(
                sourcePost.youTubeShortsContent?.descriptionEditorState
              ),
              categoryID: sourcePost.youTubeShortsContent?.categoryID,
              notifySubscribers:
                sourcePost.youTubeShortsContent?.notifySubscribers,
              isEmbeddable: sourcePost.youTubeShortsContent?.isEmbeddable,
              isSelfDeclaredMadeForKids:
                sourcePost.youTubeShortsContent?.isSelfDeclaredMadeForKids,
            }) ||
            undefined,
        },
      },
      include: {
        subscribers: {
          include: {
            user: true,
          },
        },
        tweets: true,
        instagramContent: {
          include: {
            coverPhoto: true,
            assets: {
              include: {
                asset: true,
                tags: true,
              },
            },
          },
        },
        facebookContent: {
          select: {
            id: true,
            coverPhoto: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
                fileName: true,
              },
            },
          },
        },
        linkedInContent: true,
        discordContent: {
          select: {
            id: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
            },
          },
        },
        threadsContent: {
          select: {
            id: true,
            copy: true,
            editorState: true,
            threadPosition: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
              orderBy: {
                position: "asc",
              },
            },
          },
          orderBy: {
            threadPosition: "asc",
          },
        },
        tikTokContent: {
          select: {
            id: true,
            copy: true,
            editorState: true,
            privacyLevel: true,
            isCommentDisabled: true,
            isDuetDisabled: true,
            isStitchDisabled: true,
            isPromotingBusiness: true,
            isPaidPartnership: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
            },
          },
        },
        slackContent: {
          select: {
            id: true,
            assets: {
              select: {
                id: true,
                asset: true,
                position: true,
              },
            },
          },
        },
        youTubeShortsContent: {
          select: {
            id: true,
            asset: {
              select: {
                id: true,
                asset: true,
              },
            },
          },
        },
        instagramIntegration: {
          select: {
            id: true,
            account: {
              select: {
                username: true,
                profileImageUrl: true,
              },
            },
          },
        },
        threadsIntegration: {
          select: {
            id: true,
            account: {
              select: {
                username: true,
                profileImageUrl: true,
              },
            },
          },
        },
        facebookIntegration: {
          select: {
            id: true,
            page: {
              select: {
                name: true,
                profileImageUrl: true,
              },
            },
          },
        },
        linkedInIntegration: {
          select: {
            id: true,
            account: {
              select: {
                name: true,
                profileImageUrl: true,
              },
            },
          },
        },
        tikTokIntegration: {
          select: {
            id: true,
            account: {
              select: {
                username: true,
                profileImageUrl: true,
              },
            },
          },
        },
        youTubeIntegration: {
          select: {
            id: true,
            channel: {
              select: {
                username: true,
                profileImageUrl: true,
              },
            },
          },
        },
        twitterIntegration: {
          select: {
            id: true,
            account: {
              select: {
                username: true,
                profileImageUrl: true,
              },
            },
          },
        },
        discordWebhook: {
          select: {
            id: true,
            channel: true,
            username: true,
            profileImageUrl: true,
          },
        },
        slackIntegration: {
          select: {
            id: true,
            displayName: true,
            profileImageUrl: true,
          },
        },
        webflowIntegration: {
          select: {
            id: true,
            siteName: true,
            collectionName: true,
          },
        },
        postLabels: {
          include: {
            label: true,
          },
        },
      },
    });

    let source: string;
    if (sourcePost.isIdea) {
      source = "Duplicate Idea";
    } else {
      if (newPostFields.length > 1) {
        source = "Bulk Duplicate Post";
      } else {
        source = "Duplicate Post";
      }
    }

    posthog.capture({
      distinctId: userID,
      event: POSTHOG_EVENTS.post.created,
      groups: {
        workspace: workspaceID,
        company: sourcePost.workspace.companyID,
      },
      properties: {
        postID: post.id,
        sourceChannel: sourcePost.channels[0],
        sourceChannels: sourcePost.channels,
        channel: post.channels[0],
        channels: post.channels,
        status: post.status,
        campaignID: post.campaignID,
        workspaceID,
        source,
        createdByID: post.createdByID,
        createdAt: new Date(post.createdAt),
      },
    });

    await db.activity.create({
      data: {
        type: "PostCreated",
        userID,
        message: `**{{userName}}** created the post`,
        postID: post.id,
      },
    });

    // Map from old content IDs to new content IDs
    const folderRenames: { [fromName: string]: string } = {};

    const duplicateChannel = (channel: Channel) => {
      return sourcePost.channels.includes(channel);
    };

    if (duplicateChannel("Twitter")) {
      sourcePost.tweets.forEach((tweet) => {
        const newTweet = post.tweets.find((newTweet) => {
          return newTweet.threadPosition === tweet.threadPosition;
        });
        assert(newTweet);
        folderRenames[tweet.id] = newTweet.id;
      });

      const twitterEngagements = sourcePost.tweets.flatMap((tweet) => {
        return tweet.engagements.map((engagement) => {
          const {
            type,
            copy,
            editorState,
            postDelaySeconds,
            contentID,
            integrationID,
            createdByID,
          } = engagement;

          return {
            type,
            copy,
            editorState: editorState || undefined,
            postDelaySeconds,
            contentID: folderRenames[contentID],
            integrationID,
            createdByID,
          };
        });
      });

      await db.twitterPostEngagement.createMany({
        data: twitterEngagements,
      });
    }
    if (duplicateChannel("Discord")) {
      folderRenames[sourcePost.discordContent!.id] = post.discordContent!.id;
    }
    if (duplicateChannel("LinkedIn")) {
      folderRenames[sourcePost.linkedInContent!.id] = post.linkedInContent!.id;

      const linkedInEngagements = sourcePost.linkedInContent!.engagements.map(
        (engagement) => {
          const {
            type,
            copy,
            editorState,
            postDelaySeconds,
            contentID,
            integrationID,
            createdByID,
          } = engagement;

          return {
            type,
            copy,
            editorState: editorState || undefined,
            postDelaySeconds,
            contentID: folderRenames[contentID],
            integrationID,
            createdByID,
          };
        }
      );

      await db.linkedInPostEngagement.createMany({
        data: linkedInEngagements,
      });
    }
    if (duplicateChannel("Instagram")) {
      folderRenames[sourcePost.instagramContent!.id] =
        post.instagramContent!.id;

      const instagramEngagements = sourcePost.instagramContent!.engagements.map(
        (engagement) => {
          const {
            type,
            copy,
            editorState,
            postDelaySeconds,
            contentID,
            integrationID,
            createdByID,
          } = engagement;

          return {
            type,
            copy,
            editorState: editorState || undefined,
            postDelaySeconds,
            contentID: folderRenames[contentID],
            integrationID,
            createdByID,
          };
        }
      );

      await db.instagramPostEngagement.createMany({
        data: instagramEngagements,
      });
    }
    if (duplicateChannel("Facebook")) {
      folderRenames[sourcePost.facebookContent!.id] = post.facebookContent!.id;
    }
    if (duplicateChannel("Threads")) {
      sourcePost.threadsContent.forEach((content) => {
        folderRenames[content.id] = post.threadsContent.find(
          (newContent) => newContent.threadPosition === content.threadPosition
        )!.id;
      });
    }
    if (duplicateChannel("TikTok")) {
      folderRenames[sourcePost.tikTokContent!.id] = post.tikTokContent!.id;
    }
    if (duplicateChannel("Slack")) {
      folderRenames[sourcePost.slackContent!.id] = post.slackContent!.id;
    }
    if (duplicateChannel("YouTubeShorts")) {
      folderRenames[sourcePost.youTubeShortsContent!.id] =
        post.youTubeShortsContent!.id;
    }

    await copyStorageFiles({
      sourceBasePath: `${workspaceID}/${sourcePostID}`,
      destinationBasePath: `${workspaceID}/${post.id}`,
      copySourceFolderStructure: true,
      folderRenames,
      userID,
    });

    // Now that the files have been copied, we can create the assets and tags
    if (sourcePost.instagramContent) {
      const igAssets = await getStorageAssetsForInstagram(post);
      const originalAssets = sourcePost.instagramContent.assets;

      await db.asset.createMany({
        data: igAssets.assets.map((asset) => {
          const originalAsset = sourcePost.instagramContent?.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.instagramAsset.createMany({
        data: igAssets.assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            fileName: asset.name,
            mimetype: asset.mimetype,
            instagramContentID: post.instagramContent!.id,
          };
        }),
      });

      if (igAssets.coverPhoto) {
        await db.instagramContent.update({
          where: {
            id: post.instagramContent!.id,
          },
          data: {
            coverPhoto: {
              create: {
                id: igAssets.coverPhoto.id,
                name: igAssets.coverPhoto.name,
                bucket: "assets",
                path: igAssets.coverPhoto.path,
                eTag: igAssets.coverPhoto.eTag,
                mimetype: igAssets.coverPhoto.mimetype,
                size: igAssets.coverPhoto.sizeBytes,
                url: igAssets.coverPhoto.url,
                width: sourcePost.instagramContent.coverPhoto?.width,
                height: sourcePost.instagramContent.coverPhoto?.height,
                md5Hash: sourcePost.instagramContent.coverPhoto?.md5Hash,
                urlExpiresAt: igAssets.coverPhoto.urlExpiresAt,
                workspaceID: post.workspaceID,
                userID: post.createdByID,
              },
            },
          },
        });
      }

      const newInstagramAssets = await db.instagramAsset.findMany({
        where: {
          instagramContentID: post.instagramContent!.id,
        },
        select: {
          id: true,
          asset: {
            select: {
              name: true,
            },
          },
        },
      });

      await db.instagramAssetTag.createMany({
        data: igAssets.assets.flatMap((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return originalAsset.tags.map((tag) => {
            const newAsset = newInstagramAssets.find(
              (newAsset) => newAsset.asset.name === asset.name
            );

            return {
              assetID: newAsset!.id,
              username: tag.username,
              x: tag.x,
              y: tag.y,
            };
          });
        }),
      });
    }

    if (sourcePost.tweets.length > 0) {
      const twitterAssets = await getStorageAssetsForTwitter(post);

      await db.asset.createMany({
        data: Object.entries(twitterAssets).flatMap(([id, assets]) => {
          const tweetPosition = post.tweets.find(
            (tweet) => tweet.id === id
          )!.threadPosition;

          const originalTweet = sourcePost.tweets.find(
            (tweet) => tweet.threadPosition === tweetPosition
          );

          return assets.map((asset) => {
            const originalAsset = originalTweet!.assets.find(
              (originalAsset) => originalAsset.asset.name === asset.name
            );
            return {
              id: asset.id,
              name: asset.name,
              bucket: "assets",
              path: asset.path,
              eTag: asset.eTag,
              mimetype: asset.mimetype,
              size: asset.sizeBytes,
              url: asset.url,
              width: originalAsset?.asset.width,
              height: originalAsset?.asset.height,
              duration: originalAsset?.asset.duration,
              md5Hash: originalAsset?.asset.md5Hash,
              urlExpiresAt: asset.urlExpiresAt,
              workspaceID: post.workspaceID,
              userID: post.createdByID,
            };
          });
        }),
      });

      await db.tweetAsset.createMany({
        data: Object.entries(twitterAssets).flatMap(([id, assets]) => {
          const tweetPosition = post.tweets.find(
            (tweet) => tweet.id === id
          )!.threadPosition;

          const originalTweet = sourcePost.tweets.find(
            (tweet) => tweet.threadPosition === tweetPosition
          );

          return removeEmptyElems(
            assets.map((asset) => {
              const originalAsset = originalTweet!.assets.find(
                (originalAsset) => originalAsset.asset.name === asset.name
              );
              if (originalAsset) {
                return {
                  assetID: asset.id,
                  position: originalAsset!.position,
                  tweetContentID: id,
                };
              } else {
                console.error(
                  `Original asset ${asset.name} not found in tweet ${id}`
                );
                return null;
              }
            })
          );
        }),
      });
    }

    if (sourcePost.threadsContent.length > 0) {
      const threadsAssets = await getStorageAssetsForThreads(post);

      await db.asset.createMany({
        data: Object.entries(threadsAssets).flatMap(([id, assets]) => {
          const threadPosition = post.threadsContent.find(
            (thread) => thread.id === id
          )!.threadPosition;

          const originalThread = sourcePost.threadsContent.find(
            (thread) => thread.threadPosition === threadPosition
          );

          return assets.map((asset) => {
            const originalAsset = originalThread!.assets.find(
              (originalAsset) => originalAsset.asset.eTag === asset.eTag
            );
            return {
              id: asset.id,
              name: asset.name,
              bucket: "assets",
              path: asset.path,
              eTag: asset.eTag,
              mimetype: asset.mimetype,
              size: asset.sizeBytes,
              url: asset.url,
              width: originalAsset?.asset.width,
              height: originalAsset?.asset.height,
              duration: originalAsset?.asset.duration,
              md5Hash: originalAsset?.asset.md5Hash,
              urlExpiresAt: asset.urlExpiresAt,
              workspaceID: post.workspaceID,
              userID: post.createdByID,
            };
          });
        }),
      });

      await db.threadsAsset.createMany({
        data: Object.entries(threadsAssets).flatMap(([id, assets]) => {
          const threadPosition = post.threadsContent.find(
            (thread) => thread.id === id
          )!.threadPosition;

          const originalThread = sourcePost.threadsContent.find(
            (thread) => thread.threadPosition === threadPosition
          );

          return assets.map((asset) => {
            const originalAsset = originalThread!.assets.find(
              (originalAsset) => originalAsset.asset.name === asset.name
            );
            return {
              assetID: asset.id,
              position: originalAsset!.position,
              threadsContentID: id,
            };
          });
        }),
      });
    }

    if (sourcePost.linkedInContent) {
      const linkedInAssets = await getStorageAssetsForLinkedIn(post);
      const originalAssets = sourcePost.linkedInContent.assets;

      await db.asset.createMany({
        data: linkedInAssets.assets.map((asset) => {
          const originalAsset = sourcePost.linkedInContent?.assets.find(
            (originalAsset) => originalAsset.asset.eTag === asset.eTag
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.linkedInAsset.createMany({
        data: linkedInAssets.assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset, "original asset must exist");

          return {
            assetID: asset.id,
            position: originalAsset.position,
            linkedInContentID: post.linkedInContent!.id,
          };
        }),
      });

      if (linkedInAssets.coverPhoto) {
        await db.linkedInContent.update({
          where: {
            id: post.linkedInContent!.id,
          },
          data: {
            coverPhoto: {
              create: {
                id: linkedInAssets.coverPhoto.id,
                name: linkedInAssets.coverPhoto.name,
                bucket: "assets",
                path: linkedInAssets.coverPhoto.path,
                eTag: linkedInAssets.coverPhoto.eTag,
                mimetype: linkedInAssets.coverPhoto.mimetype,
                size: linkedInAssets.coverPhoto.sizeBytes,
                url: linkedInAssets.coverPhoto.url,
                width: sourcePost.linkedInContent.coverPhoto?.width,
                height: sourcePost.linkedInContent.coverPhoto?.height,
                md5Hash: sourcePost.linkedInContent.coverPhoto?.md5Hash,
                urlExpiresAt: linkedInAssets.coverPhoto.urlExpiresAt,
                workspaceID: post.workspaceID,
                userID: post.createdByID,
              },
            },
          },
        });
      }

      if (linkedInAssets.captions) {
        await db.linkedInContent.update({
          where: {
            id: post.linkedInContent!.id,
          },
          data: {
            captions: {
              create: {
                id: linkedInAssets.captions.id,
                name: linkedInAssets.captions.name,
                bucket: "assets",
                path: linkedInAssets.captions.path,
                eTag: linkedInAssets.captions.eTag,
                mimetype: linkedInAssets.captions.mimetype,
                size: linkedInAssets.captions.sizeBytes,
                url: linkedInAssets.captions.url,
                width: sourcePost.linkedInContent.captions?.width,
                height: sourcePost.linkedInContent.captions?.height,
                md5Hash: sourcePost.linkedInContent.captions?.md5Hash,
                urlExpiresAt: linkedInAssets.captions.urlExpiresAt,
                workspaceID: post.workspaceID,
                userID: post.createdByID,
              },
            },
          },
        });
      }
    }

    if (sourcePost.facebookContent) {
      const { assets, coverPhoto } = await getStorageAssetsForFacebook(post);
      const originalAssets = sourcePost.facebookContent.assets;

      await db.asset.createMany({
        data: assets.map((asset) => {
          const originalAsset = sourcePost.facebookContent?.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.facebookAsset.createMany({
        data: assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            fileName: asset.name,
            mimetype: asset.metadata.mimetype,
            facebookContentID: post.facebookContent!.id,
          };
        }),
      });

      if (coverPhoto) {
        await db.facebookContent.update({
          where: {
            id: post.facebookContent!.id,
          },
          data: {
            coverPhoto: {
              create: {
                id: coverPhoto.id,
                name: coverPhoto.name,
                bucket: "assets",
                path: coverPhoto.path,
                eTag: coverPhoto.eTag,
                mimetype: coverPhoto.mimetype,
                size: coverPhoto.sizeBytes,
                url: coverPhoto.url,
                width: sourcePost.facebookContent.coverPhoto?.width,
                height: sourcePost.facebookContent.coverPhoto?.height,
                md5Hash: sourcePost.facebookContent.coverPhoto?.md5Hash,
                urlExpiresAt: coverPhoto.urlExpiresAt,
                workspaceID: post.workspaceID,
                userID: post.createdByID,
              },
            },
          },
        });
      }
    }

    if (sourcePost.discordContent) {
      const assets = await getStorageAssetsForDiscord(post);
      const originalAssets = sourcePost.discordContent.assets;

      await db.asset.createMany({
        data: assets.map((asset) => {
          const originalAsset = sourcePost.discordContent!.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.discordAsset.createMany({
        data: assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            discordContentID: post.discordContent!.id,
          };
        }),
      });
    }

    if (sourcePost.tikTokContent) {
      const assets = await getStorageAssetsForTikTok(post);
      const originalAssets = sourcePost.tikTokContent.assets;

      await db.asset.createMany({
        data: assets.map((asset) => {
          const originalAsset = sourcePost.tikTokContent!.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.tikTokAsset.createMany({
        data: assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            tikTokContentID: post.tikTokContent!.id,
          };
        }),
      });
    }

    if (sourcePost.slackContent) {
      const assets = await getStorageAssetsForSlack(post);
      const originalAssets = sourcePost.slackContent.assets;

      await db.asset.createMany({
        data: assets.map((asset) => {
          const originalAsset = sourcePost.slackContent!.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.slackAsset.createMany({
        data: assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            slackContentID: post.slackContent!.id,
          };
        }),
      });
    }

    if (sourcePost.youTubeShortsContent) {
      const { asset } = await getStorageAssetsForYouTubeShorts(post);
      const originalAsset = sourcePost.youTubeShortsContent.asset;

      if (asset && originalAsset) {
        await db.asset.create({
          data: {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset.asset.width,
            height: originalAsset.asset.height,
            duration: originalAsset.asset.duration,
            md5Hash: originalAsset.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          },
        });
        await db.youTubeShortsAsset.create({
          data: {
            assetID: asset.id,
            youTubeShortsContentID: post.youTubeShortsContent!.id,
          },
        });
      }
    }

    const accounts = getPostProfiles(post);

    return assertPostOrIdea({
      ...post,
      subscribers: post.subscribers.map((subscriber) => {
        return {
          ...subscriber,
          user: subscriber.user,
        };
      }),
      accounts,
      labels: post.postLabels.map((postLabel) => {
        return {
          id: postLabel.label.id,
          name: postLabel.label.name,
          color: postLabel.label.color,
          backgroundColor: postLabel.label.backgroundColor,
          createdAt: postLabel.label.createdAt,
        };
      }),
    });
  });

  const posts = await Promise.all(promises);

  return posts;
};

export default duplicatePost;
