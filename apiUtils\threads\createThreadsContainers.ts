import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import Threads from "~/clients/facebook/Threads";
import { removeEmptyElems } from "~/utils";

const createThreadsContainers = async ({
  post,
  thread,
  replyToID,
}: {
  post: {
    id: string;
    threadsIntegration: {
      accessToken: string;
      account: {
        threadsUserID: string;
      };
    };
  };
  thread: {
    id: string;
    copy: string;
    threadPosition: number;
    assets: {
      asset: {
        url: string;
        mimetype: string;
      };
      containerID: string | null;
      containerExpiresAt: Date | null;
    }[];
  };
  replyToID: string | null;
}): Promise<
  | {
      status: "Success";
      containerID: string;
    }
  | {
      status: "Error" | "NonRetriableError";
      error: string;
      isAuthError: boolean;
    }
> => {
  const { threadsIntegration } = post;

  const threadNumber = thread.threadPosition + 1;

  if (!threadsIntegration) {
    console.error(
      `Threads Container Error post:${post.id}: No Threads integration`
    );
    return {
      status: "NonRetriableError",
      error: "No Threads integration",
      isAuthError: false,
    };
  }

  const threads = new Threads({
    accessToken: threadsIntegration.accessToken,
    threadsUserID: threadsIntegration.account.threadsUserID,
  });

  const threadsAssets = sortBy(thread.assets, "position");

  if (threadsAssets.length < 2) {
    const asset = threadsAssets[0]?.asset;
    const threadContainerResponse = await threads.createThreadContainer({
      asset: asset
        ? {
            url: asset.url,
            mimetype: asset.mimetype,
          }
        : null,
      text: thread.copy,
      replyToID,
    });

    if (threadContainerResponse.errors) {
      const isAuthError = threadContainerResponse.isAuthError;
      console.error(
        `Threads Container Error post:${post.id}: Failed to upload Thread:${thread.id}`
      );
      return {
        status: isAuthError ? "NonRetriableError" : "Error",
        error: `Post #${threadNumber} in Thread: ${threadContainerResponse.errors.join(
          ", "
        )}`,
        isAuthError,
      };
    }

    return {
      status: "Success",
      containerID: threadContainerResponse.data.containerID,
    };
  } else {
    let containerIDs = threadsAssets.map((asset) => asset.containerID);

    if (
      threadsAssets.every(
        (asset) =>
          asset.containerID &&
          asset.containerExpiresAt &&
          DateTime.fromJSDate(asset.containerExpiresAt)
            .diffNow()
            .as("minutes") > 10
      )
    ) {
      const carouselContainerResponse = await threads.createCarouselContainer({
        carouselItemContainerIDs: removeEmptyElems(containerIDs),
        text: thread.copy,
        replyToID,
      });

      if (carouselContainerResponse.errors) {
        console.error(
          `Threads Container Error post:${post.id}: Failed to upload Thread:${thread.id}`
        );
        const isAuthError = carouselContainerResponse.isAuthError;
        return {
          status: isAuthError ? "NonRetriableError" : "Error",
          error: `Post #${threadNumber} in Thread: ${carouselContainerResponse.errors.join(
            ", "
          )}`,
          isAuthError,
        };
      }

      return {
        status: "Success",
        containerID: carouselContainerResponse.data.containerID,
      };
    } else {
      console.log(
        `Threads Container Error post:${post.id}: Thread:${thread.id} asset containers are not present or expired`
      );
      return {
        status: "NonRetriableError",
        error: `Post #${threadNumber} asset containers are not present or expired`,
        isAuthError: false,
      };
    }
  }
};

export default createThreadsContainers;
