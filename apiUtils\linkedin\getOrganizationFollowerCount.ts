import { db } from "~/clients/Prisma";

import { LinkedInAccountType, LinkedInOrganizationRole } from "@prisma/client";
import LinkedIn from "~/clients/linkedin/LinkedIn";
import { analyticsRoles } from "./getLinkedInPosts";

const getOrganizationFollowerCount = async (integration: {
  id: string;
  accessToken: string;
  account: {
    id: string;
    urn: string;
    type: LinkedInAccountType;
    name: string;
  };
  role: LinkedInOrganizationRole;
}) => {
  const linkedin = new LinkedIn(integration.accessToken);

  const { account, role } = integration;

  if (account.type === "User" || !analyticsRoles.includes(role)) {
    return {
      integrationID: integration.id,
      erroredUrns: [],
    };
  }

  const newStats: {
    accountID: string;
    followerCount: number;
    recordedAt: Date;
  }[] = [];

  const erroredUrns: string[] = [];

  console.log(`${account.name}: BEGIN querying followers ${account.urn}`);

  const { data, errors } = await linkedin.getFollowerCount(account.urn);

  if (errors != null) {
    console.log(`${account.name}: ERROR querying account follower`, errors);
    erroredUrns.push(account.urn);
    return {
      integrationID: integration.id,
      erroredUrns,
    };
  }

  const { followerCount } = data;

  newStats.push({
    accountID: account.id,
    followerCount,
    recordedAt: new Date(),
  });

  console.log(`${account.name}: FINISHED querying account followers`);

  await db.linkedInAccountStats.createMany({
    data: newStats,
  });

  return {
    integrationID: integration.id,
    erroredUrns,
  };
};

export default getOrganizationFollowerCount;
