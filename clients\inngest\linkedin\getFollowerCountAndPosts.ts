import { slugify } from "inngest";
import chunk from "lodash/chunk";
import { db } from "~/clients";
import LinkedIn from "~/clients/linkedin/LinkedIn";
import LinkedInScraper from "~/clients/rapid_api/LinkedInScraper";
import { inngest } from "../inngest";

const BATCH_SIZE = 20;

const cron = (cron: string): { cron: string } => {
  if (process.env.NODE_ENV === "development") {
    return { cron: "0 0 31 2 *" }; // February 31st (never happens)
  }

  return { cron };
};

export const linkedInUpdateDataCron = inngest.createFunction(
  {
    id: slugify("LinkedIn Update Data Cron"),
    name: "LinkedIn Update Data Cron",
  },
  cron("0 6 * * *"), // Run at 1 AM CST (6 AM UTC)
  async ({ event, step }) => {
    const profiles = await step.run("Fetch LinkedIn profiles", async () => {
      const accounts = await db.linkedInAccount.findMany({
        select: {
          id: true,
          vanityName: true,
          integrations: {
            select: {
              id: true,
              accessToken: true,
              workspace: {
                select: {
                  company: {
                    select: {
                      trialEndsAt: true,
                      subscription: {
                        select: {
                          status: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        where: {
          type: "User",
          integrations: {
            some: {
              accessTokenExpiresAt: {
                gt: new Date(),
              },
            },
          },
        },
      });

      // We only want to sync followers for accounts that have a subscription
      const accountsWithSubscription = accounts.filter((account) => {
        return account.integrations.some((integration) => {
          const company = integration.workspace.company;

          const { trialEndsAt } = company;

          if (trialEndsAt && trialEndsAt < new Date()) {
            return false;
          }

          return (
            company.subscription == null ||
            company.subscription.status === "active"
          );
        });
      });

      return accountsWithSubscription;
    });

    console.log(
      `LinkedIn Follower Sync: Found ${profiles.length} profiles to update`
    );

    if (profiles.length === 0) {
      return {
        status: "Skipped",
        reason: "No profiles to update",
      };
    }

    const batches = chunk(profiles, BATCH_SIZE);

    console.log(
      `LinkedIn Follower Sync: Found ${batches.length} batches to process`
    );

    let batchID = 1;
    for await (const batch of batches) {
      await step.sendEvent("linkedin-get-follower-counts", {
        name: "linkedin.getFollowerCountAndPosts",
        data: {
          batchID,
          vanityNames: batch.map((profile) => profile.vanityName),
        },
      });

      batchID++;
    }

    return {
      status: "Scheduled",
      totalProfiles: profiles.length,
      totalBatches: batches.length,
      scheduledAt: new Date().toISOString(),
    };
  }
);

// Process a single batch of LinkedIn profiles
export const linkedInGetFollowerCountAndPosts = inngest.createFunction(
  {
    id: slugify("LinkedIn Get Follower Count and Posts"),
    name: "LinkedIn Get Follower Count and Posts",
    retries: 0,
    concurrency: {
      limit: 4,
    },
  },
  { event: "linkedin.getFollowerCountAndPosts" },
  async ({ event, step, attempt }) => {
    const { batchID, vanityNames } = event.data;

    console.log(
      `LinkedIn Get Follower Count and Posts: Batch ${batchID} - Processing ${vanityNames.length} profiles`
    );

    const results = await step.run("Process profiles", async () => {
      const scraper = new LinkedInScraper();

      const failures: { vanityName: string; error: string }[] = [];

      let index = 1;
      for await (const vanityName of vanityNames) {
        console.log(`Searching ${vanityName} (${index}/${vanityNames.length})`);
        const result = await scraper.profile.getProfileAndPosts(vanityName);

        if (result.success) {
          console.log(
            `Batch ${batchID} - Found ${vanityName}: ${result.data.profile.followerCount} followers`
          );

          const stat = await db.linkedInAccountStats.create({
            data: {
              account: {
                connect: {
                  vanityName_type: {
                    vanityName: vanityName,
                    type: "User",
                  },
                },
              },
              followerCount: result.data.profile.followerCount,
              recordedAt: new Date(),
            },
            select: {
              id: true,
              account: {
                select: {
                  id: true,
                  urn: true,
                  integrations: {
                    select: {
                      id: true,
                      accessToken: true,
                    },
                    where: {
                      accessTokenExpiresAt: {
                        gt: new Date(),
                      },
                    },
                  },
                },
              },
            },
          });

          const account = stat.account;
          const integration = account.integrations[0];

          const linkedIn = new LinkedIn(integration.accessToken);

          const nonRepostedPosts = result.data.posts.filter(
            (post) => post.type !== "Repost"
          );

          const postActivityUrns = nonRepostedPosts.map(
            (post) => post.activityUrn
          );

          const postUrnPostResponse =
            await linkedIn.getPostUrnsByActivityUrns(postActivityUrns);

          if (postUrnPostResponse.success == false) {
            console.log(
              `Batch ${batchID} - Error fetching post urns for ${vanityName}: ${postUrnPostResponse.errors.join(
                ", "
              )}`
            );

            failures.push({
              vanityName: vanityName,
              error: postUrnPostResponse.errors.join(", "),
            });

            continue;
          }

          const postUrnMap = postUrnPostResponse.data;

          const foundPosts = nonRepostedPosts.filter(
            (post) => postUrnMap[post.activityUrn]
          );

          const postPromises = foundPosts.map(async (post) => {
            const id = postUrnMap[post.activityUrn];

            await db.linkedInPost.upsert({
              where: {
                id,
              },
              create: {
                id,
                activityUrn: post.activityUrn,
                type: post.type,
                authorUrn: account.urn,
                commentary: post.commentary,
                personTagCount: post.personTagCount,
                organizationTagCount: post.organizationTagCount,
                hashtagCount: post.hashtagCount,
                publishedAt: post.publishedAt,
                postCreatedAt: post.publishedAt,
                rawResponse: post,
                isScrapedData: true,
                visibility: "PUBLIC",
                isAdvertisement: false,
              },
              update: {
                activityUrn: post.activityUrn,
                commentary: post.commentary,
              },
            });
          });

          await Promise.all(postPromises);

          console.log(
            `Batch ${batchID} - Created ${foundPosts.length} posts for ${vanityName}`
          );

          await db.linkedInPostStats.createMany({
            data: foundPosts.map((post) => {
              return {
                postID: postUrnMap[post.activityUrn],
                commentCount: post.commentCount,
                likeCount: post.likeCount,
                shareCount: post.shareCount,
              };
            }),
          });

          const assemblyPosts = await db.post.findMany({
            where: {
              OR: [
                {
                  link: {
                    in: foundPosts.map(
                      (post) =>
                        `https://www.linkedin.com/feed/update/${
                          postUrnMap[post.activityUrn]
                        }`
                    ),
                  },
                },
                {
                  linkedInLink: {
                    in: foundPosts.map(
                      (post) =>
                        `https://www.linkedin.com/feed/update/${
                          postUrnMap[post.activityUrn]
                        }`
                    ),
                  },
                },
              ],
              linkedInPostID: null,
            },
          });

          const postLinkPromises = assemblyPosts.map(async (post) => {
            const { link, linkedInLink } = post;

            const postLink = linkedInLink || link;

            if (!postLink) {
              return null;
            }

            const urn = postLink.split("/").pop();

            await db.post.update({
              where: {
                id: post.id,
              },
              data: {
                linkedInPostID: urn,
              },
            });
          });

          await Promise.all(postLinkPromises);
        } else {
          console.log(
            `Batch ${batchID} - Error fetching ${vanityName}: ${result.errors.join(
              ", "
            )}`
          );
          failures.push({
            vanityName: vanityName,
            error: result.errors.join(", "),
          });
        }

        index++;
      }

      return {
        total: vanityNames.length,
        successful: vanityNames.length - failures.length,
        failures,
      };
    });

    return {
      total: results.total,
      successful: results.successful,
      failures: results.failures,
      completedAt: new Date().toISOString(),
    };
  }
);
