import { StatusCodes } from "http-status-codes";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.updateMany({
    where: {
      workspaceID: "5daa8f1d-1425-4856-b1ee-3cb3f9a556d5",
      isIdea: false,
      postLabels: {
        some: {
          label: {
            name: {
              equals: "4Labs",
              mode: "insensitive",
            },
          },
        },
      },
      publishDate: {
        gt: new Date(),
      },
    },
    data: {
      isIdea: true,
      publishAt: null,
      publishDate: null,
      publishTime: null,
    },
  });

  res.status(StatusCodes.OK).send({
    posts: posts.count,
  });
};

export default handler;
