import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import minBy from "lodash/minBy";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const users = await db.user.findMany({
    include: {
      receivedInvites: true,
      userWorkspaces: {
        include: {
          workspace: {
            include: {
              company: true,
            },
          },
        },
      },
    },
  });

  for (const user of users) {
    // find the first workspace based on createdAt
    const firstWorkspace = minBy(user.userWorkspaces, "createdAt");
    if (!firstWorkspace) {
      console.log(user.email, "no workspace");
      continue;
    }

    // Find invite that matches the workspace ID
    const invite = user.receivedInvites.find(
      (invite) => invite.workspaceID === firstWorkspace.workspaceID
    );

    console.log(user.email, firstWorkspace.workspace.name, invite?.id);

    posthog.capture({
      distinctId: user.id,
      event: POSTHOG_EVENTS.account.created,
      properties: {
        inviteID: invite?.id || null,
      },
      groups: {
        workspace: firstWorkspace.workspaceID,
        company: firstWorkspace.workspace.companyID,
      },
      timestamp: new Date(user.createdAt),
    });
  }

  res.status(StatusCodes.OK).send({ users });
};

export default handler;
