import { Controller, Path } from "react-hook-form";

import { Label } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import { CampaignSelect } from "~/components/app";
import camelCaseToWords from "~/utils/camelCaseToWords";

type FormCampaignSelectProps<T> = {
  control: any;
  name: Path<T>;
  label?: string | JSX.Element;
  isOptional?: boolean;
  tooltipText?: string;
  rules?: Rules;
};

const FormCampaignSelect = <T,>({
  control,
  name,
  label,
  isOptional,
  tooltipText,
  rules = {},
}: FormCampaignSelectProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <Label
            value={label}
            isRequired={!!rules.required}
            isOptional={isOptional}
            tooltipText={tooltipText}
          >
            <>
              <CampaignSelect value={(value as any)?.id} onChange={onChange} />
              <Error error={error} />
            </>
          </Label>
        );
      }}
    />
  );
};

export default FormCampaignSelect;
