import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  RowData,
  useReactTable,
} from "@tanstack/react-table";
import classNames from "classnames";
import { Checkbox } from "~/components";
import { KnockWorkflow } from "~/types";
import { NotificationPreference } from "../../../pages/authed/settings/notifications";

declare module "@tanstack/react-table" {
  interface TableMeta<TData extends RowData> {
    updateData: (rowIndex: number, columnId: string, value: unknown) => void;
  }
}

const columnHelper = createColumnHelper<NotificationPreference>();

type Props = {
  data: NotificationPreference[];
  setData: (
    workflow: KnockWorkflow,
    settings: {
      in_app_feed: boolean;
      email: boolean;
      chat: boolean;
    }
  ) => void;
  header: string;
  hideHeader: boolean;
};

const NotificationsTable = ({ data, setData, header, hideHeader }: Props) => {
  const columns = [
    columnHelper.accessor("name", {
      cell: (info) => (
        <div>
          <div className="font-body-sm">{info.getValue()}</div>
          <div className="font-body-sm text-medium-dark-gray mt-1">
            {info.row.original.description}
          </div>
        </div>
      ),
      header: header,
      size: 600,
    }),
    columnHelper.accessor("in_app_feed", {
      cell: (info) => (
        <Checkbox
          value={info.getValue()}
          disabled={info.row.original.disabledChannels?.in_app_feed}
          onChange={() => {
            table.options.meta?.updateData(
              info.row.index,
              info.column.id,
              !info.getValue()
            );
          }}
        />
      ),
      header: hideHeader ? undefined : "In-App",
      maxSize: 100,
    }),
    columnHelper.accessor("email", {
      cell: (info) => {
        return (
          <Checkbox
            value={info.getValue()}
            disabled={info.row.original.disabledChannels?.email}
            onChange={() => {
              table.options.meta?.updateData(
                info.row.index,
                info.column.id,
                !info.getValue()
              );
            }}
          />
        );
      },
      header: hideHeader ? undefined : "Email",
      maxSize: 100,
    }),
    columnHelper.accessor("chat", {
      cell: (info) => (
        <Checkbox
          value={info.getValue()}
          disabled={info.row.original.disabledChannels?.chat}
          onChange={() => {
            table.options.meta?.updateData(
              info.row.index,
              info.column.id,
              !info.getValue()
            );
          }}
        />
      ),
      header: hideHeader ? undefined : "Slack",
      maxSize: 100,
    }),
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    meta: {
      updateData: (rowIndex, columnId, value) => {
        const row = data[rowIndex];
        setData(row.workflow, {
          ...row,
          [columnId]: value,
        });
      },
    },
  });

  return (
    <table className="w-full">
      <thead className="font-body-sm w-full">
        {table.getHeaderGroups().map((headerGroup) => (
          <tr key={headerGroup.id} className="border-lightest-gray border-b">
            {headerGroup.headers.map((header, index) => (
              <th
                key={header.id}
                style={{ width: header.column.getSize() }}
                className={classNames("py-2 font-medium", {
                  "text-left": index === 0,
                  "text-center": index > 0,
                })}
              >
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
              </th>
            ))}
          </tr>
        ))}
      </thead>
      <tbody className="font-body text-dark-gray">
        {table.getRowModel().rows.map((row, rowIndex) => (
          <tr
            key={row.id}
            className={classNames("border-lightest-gray border-b", {
              "border-b-0": rowIndex === table.getRowModel().rows.length - 1,
            })}
          >
            {row.getVisibleCells().map((cell, index) => (
              <td key={cell.id} className="py-3">
                <div style={{ width: cell.column.getSize() }}>
                  <div className={classNames({ "mx-auto w-fit": index > 0 })}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </div>
                </div>
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default NotificationsTable;
