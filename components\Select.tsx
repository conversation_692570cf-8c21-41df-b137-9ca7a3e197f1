import { ChevronDownIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import type { ActionButton, Option } from "./Combobox";
import Combobox from "./Combobox";

type SelectProps<T> = {
  /** The currently selected value of the input */
  value: T | null;
  /** Called when the value of the select input is changed */
  onChange: (value: T) => void;
  /** The options that the select input contains */
  options: Option<T>[];
  /** The placeholder text that will be displayed when the input is empty */
  placeholder?: string;
  isSearchable?: boolean;
  /** When true, this allows the user to remove the last element */
  isClearable?: boolean;
  hideSelectedOption?: boolean;
  /** Whether the input is disabled */
  disabled?: boolean;
  dropdownPosition?: "left" | "right";
  /** An optional buton that appears after all of the options */
  actionButton?: ActionButton;
};

const Select = ({
  value,
  onChange,
  options,
  placeholder = "Select an option",
  isSearchable = false,
  isClearable = false,
  hideSelectedOption,
  disabled,
  dropdownPosition,
  actionButton,
}: SelectProps<any>) => {
  return (
    <Combobox
      value={value}
      onChange={onChange}
      isSearchable={isSearchable}
      hideSelectedOption={hideSelectedOption}
      dropdownPosition={dropdownPosition}
      actionButton={actionButton}
      options={options?.map((option) => ({
        ...option,
        component: (
          <div
            className={classNames(
              "w-content bg-surface border-light-gray text-medium-gray hover:text-secondary inline-flex h-6 items-center justify-center gap-1.5 rounded-full border-[0.25px] px-2 whitespace-nowrap transition-colors",
              {
                "hover:border-secondary": !disabled,
              }
            )}
          >
            <span className="font-body-sm text-secondary">{option.label}</span>
            {!disabled && <ChevronDownIcon className="h-4 w-4 text-inherit" />}
          </div>
        ),
      }))}
      placeholder={
        <div
          className={classNames(
            "w-content bg-surface border-light-gray text-medium-gray hover:text-secondary inline-flex h-6 items-center justify-center gap-1.5 rounded-full border-[0.25px] px-2 whitespace-nowrap transition-colors",
            {
              "hover:border-secondary": !disabled,
            }
          )}
        >
          <span className="font-body-sm text-medium-dark-gray">
            {placeholder}
          </span>
          {!disabled && <ChevronDownIcon className="h-4 w-4 text-inherit" />}
        </div>
      }
      disabled={disabled}
      isCreateable={false}
      isClearable={isClearable}
    />
  );
};

export default Select;
