import LinkExtension from "@tiptap/extension-link";
import { Editor, ReactRenderer } from "@tiptap/react";
import { Plugin } from "prosemirror-state";
import tippy, { Instance as TippyInstance } from "tippy.js";
import { urlRegex } from "~/constants";
import LinkShortenPopover from "../ui/LinkShortenPopover";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    textLink: {
      toggleLinkMenu: () => ReturnType;
    };
  }
}

// Extend the options interface to include custom handlers
declare module "@tiptap/extension-link" {
  interface LinkOptions {
    isLinkShorteningEnabled?: boolean;
    postID?: string;
  }
}

const Link = LinkExtension.extend({
  addCommands() {
    const parentCommands = this.parent?.() ?? {};

    return {
      ...parentCommands,
      toggleLinkMenu:
        () =>
        ({ editor }: { editor: Editor }) => {
          editor.chain().focus().setMeta("toggleLinkMenu", {}).run();
          return true;
        },
    };
  },
  addKeyboardShortcuts() {
    return {
      "Mod-k": ({ editor }) => {
        return editor.commands.toggleLinkMenu();
      },
    };
  },

  addOptions() {
    return {
      ...this.parent?.(),
      autolink: true,
      HTMLAttributes: {
        rel: "noopener noreferrer nofollow",
        target: "_blank",
      },
      linkOnPaste: true,
      openOnClick: false,
      isAllowedUri: (url, ctx) => {
        try {
          // construct URL
          const parsedUrl = url.includes(":")
            ? new URL(url)
            : new URL(`${ctx.defaultProtocol}://${url}`);

          // disallowed protocols
          const disallowedProtocols = ["ftp", "file", "mailto"];
          const protocol = parsedUrl.protocol.replace(":", "");

          if (disallowedProtocols.includes(protocol)) {
            return false;
          }

          // only allow protocols specified in ctx.protocols
          const allowedProtocols = ctx.protocols.map((p) =>
            typeof p === "string" ? p : p.scheme
          );

          if (!allowedProtocols.includes(protocol)) {
            return false;
          }

          // all checks have passed
          return true;
        } catch (error) {
          console.log("failed error", error);
          return false;
        }
      },
      defaultProtocol: "https",
      protocols: ["http", "https"],
    };
  },

  addStorage() {
    return {
      popover: null as TippyInstance | null,
    };
  },

  addProseMirrorPlugins() {
    const plugins = this.parent?.() || [];

    // Add a plugin to handle spaces within links
    const linkBreakPlugin = new Plugin({
      appendTransaction: (transactions, oldState, newState) => {
        // Only proceed if there were actual changes
        if (!transactions.some((tr) => tr.docChanged)) return null;

        const tr = newState.tr;
        let modified = false;

        // Find all link marks in the document
        newState.doc.descendants((node, pos) => {
          if (!node.isText) return true;

          const marks = node.marks.filter((mark) => mark.type.name === "link");
          if (marks.length === 0) return true;

          // Check if the text contains spaces
          const text = node.text || "";
          if (text.includes(" ")) {
            // Split the text at spaces
            const parts = text.split(" ");
            let currentPos = pos;

            // Remove the entire node with its link mark
            tr.delete(pos, pos + node.nodeSize);

            // Re-insert each part with appropriate marks
            parts.forEach((part, index) => {
              if (part.length > 0) {
                // Keep the link mark for valid parts
                tr.insert(currentPos, newState.schema.text(part, marks));
                currentPos += part.length;
              }

              // Add space between parts (without link mark)
              if (index < parts.length - 1) {
                tr.insert(currentPos, newState.schema.text(" "));
                currentPos += 1;
              }
            });

            modified = true;
          }

          return true;
        });

        return modified ? tr : null;
      },
    });

    return [...plugins, linkBreakPlugin];
  },

  onCreate() {
    const { editor } = this;

    // Add autolink on create functionality
    if (editor && this.options.autolink) {
      // Get the current document content
      const { doc } = editor.state;

      // Find text nodes that contain URLs and convert them to links
      doc.descendants((node, pos) => {
        const text = node.text;
        if (node.isText && text) {
          let match;
          const matches = [];
          const regex = new RegExp(urlRegex);

          while ((match = regex.exec(text)) !== null) {
            // Only use the actual URL match, not any trailing spaces
            const url = match[0].trim();
            matches.push({
              text: url,
              index: match.index,
              length: url.length,
            });
          }

          if (matches.length > 0) {
            // Process matches in reverse to avoid position shifts
            for (let i = matches.length - 1; i >= 0; i--) {
              const match = matches[i];
              const from = pos + match.index;
              const to = from + match.length;

              // Replace the text with a link
              editor.commands.setTextSelection({ from, to });
              editor.commands.setLink({ href: match.text });
            }
          }
        }
        return true;
      });
    }

    // Existing link shortening functionality
    if (editor && this.options.isLinkShorteningEnabled) {
      if (!this.options.postID) {
        console.error("postID is required for link shortening");
        return;
      }

      // Add a click event listener to the editor DOM element
      editor.view.dom.addEventListener("click", (event) => {
        // Check if the clicked element is a link
        const target = event.target as HTMLElement;
        const linkElement = target.closest("a");

        if (
          linkElement &&
          !linkElement.getAttribute("data-type")?.includes("mention")
        ) {
          event.preventDefault();

          const href = linkElement.getAttribute("href") || "";

          if (this.storage.popover) {
            this.storage.popover.destroy();
          }

          const popover = tippy(linkElement, {
            content: document.createElement("div"),
            showOnCreate: true,
            interactive: true,
            trigger: "manual",
            placement: "bottom",
            arrow: true,
            theme: "light",
            appendTo: () => document.body,
          });

          this.storage.popover = popover;

          const component = new ReactRenderer(LinkShortenPopover, {
            props: {
              postID: this.options.postID,
              href,
              linkElement,
              linkNode: editor.view.state.doc.nodeAt(
                editor.view.posAtDOM(linkElement, 0)
              ),
              linkPos: editor.view.posAtDOM(linkElement, 0),

              onShortenLink: (shortUrl: string) => {
                // Replace the link text with the shortened URL
                const pos = editor.view.posAtDOM(linkElement, 0);
                const node = editor.view.state.doc.nodeAt(pos);

                if (node) {
                  const from = pos;
                  const to = pos + node.nodeSize;

                  editor
                    .chain()
                    .focus()
                    .deleteRange({ from, to })
                    .insertContent(
                      `<a href="${shortUrl}" class="custom-link" rel="noopener noreferrer nofollow" target="_blank">${shortUrl}</a>`
                    )
                    .run();
                }

                popover.destroy();
                this.storage.popover = null;
              },
            },
            editor,
          });

          // Set the content of the popover to the React component
          popover.setContent(component.element);
        } else {
          // If we click outside a link, destroy any existing popover
          if (this.storage.popover) {
            this.storage.popover.destroy();
            this.storage.popover = null;
          }
        }
      });
    }
  },

  onDestroy() {
    // Clean up the popover when the extension is destroyed
    if (this.storage.popover) {
      this.storage.popover.destroy();
      this.storage.popover = null;
    }
  },
});

export default Link;
