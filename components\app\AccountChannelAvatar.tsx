import { Channel } from "@prisma/client";
import Avatar from "~/components/ui/Avatar";
import ChannelIcon from "./ChannelIcon";

const AccountChannelAvatar = ({
  account,
  size = 20,
}: {
  account: {
    profileImageUrl?: string;
    channel: Channel;
  };
  size?: number;
}) => {
  let fallbackProfilePicture = "/instagram/default_user.png";
  if (account.channel === "LinkedIn") {
    fallbackProfilePicture = "/linkedin/default_user.png";
  } else if (account.channel === "TikTok") {
    fallbackProfilePicture = "/tiktok/default_user.jpeg";
  } else if (account.channel === "Discord") {
    fallbackProfilePicture = "/discord/default_user.png";
  } else if (account.channel === "Twitter") {
    fallbackProfilePicture = "/integrations/twitter/default_user.png";
  } else if (account.channel === "Slack") {
    fallbackProfilePicture = "/integrations/slack/default_user.png";
  } else if (account.channel === "Instagram") {
    fallbackProfilePicture = "/instagram/default_user.png";
  } else if (account.channel === "Threads") {
    fallbackProfilePicture = "/instagram/default_user.png";
  }

  return (
    <div className="border-lightest-gray relative flex shrink-0 items-center gap-1 rounded-full border-[0.5px]">
      <Avatar.Root
        style={{
          width: `${size}px`,
          height: `${size}px`,
        }}
      >
        <Avatar.Image src={account.profileImageUrl} />
        <Avatar.Fallback className="font-eyebrow-sm text-basic">
          <div className="h-full w-full">
            <img
              src={fallbackProfilePicture}
              className="h-full w-full object-cover"
            />
          </div>
        </Avatar.Fallback>
      </Avatar.Root>
      <div
        className="absolute"
        style={{
          top: `${0.167 * size - 9.34}px`,
          right: `${0.167 * size - 9.34}px`,
        }}
      >
        <ChannelIcon channel={account.channel} width={14} />
      </div>
    </div>
  );
};

export default AccountChannelAvatar;
