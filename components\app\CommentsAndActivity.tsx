import { useState } from "react";

import classNames from "classnames";
import { Controller, useForm } from "react-hook-form";
import ReactMarkdown from "react-markdown";
import { Mention, MentionsInput } from "react-mentions";
import remarkBreaks from "remark-breaks";
import remarkGfm from "remark-gfm";
import Button from "~/components/ui/Button";
import DropdownMenu from "~/components/ui/DropdownMenu";

import { EllipsisVerticalIcon } from "@heroicons/react/24/outline";
import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import { Divider, Tooltip, UserAvatar } from "~/components";
import { useUser } from "~/providers";
import mentionInputStyles from "~/styles/mentionInputStyles";
import mentionStyles from "~/styles/mentionStyles";
import { getFullName, openConfirmationModal, trpc } from "~/utils";
import Subscribers from "./posts/Subscribers";

export type Comment = {
  id: string;
  message: string;
  user: {
    id: string;
    firstName: string | null;
    lastName: string | null;
  };
  createdAt: Date;
};

export type Activity = {
  id: string;
  message: string;
  user: {
    id: string;
    firstName: string | null;
    lastName: string | null;
  };
  createdAt: Date;
};

type FormValues = {
  message: string;
};

type Props = {
  id: string;
  type: "post" | "campaign";
  comments: Comment[];
  activity: Activity[];
  isReadOnly: boolean;
};

const CommentsAndActivity = ({
  id,
  type,
  comments: initialComments,
  activity,
  isReadOnly,
}: Props) => {
  const { user } = useUser();
  const trpcUtils = trpc.useUtils();
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const { control, handleSubmit, watch, reset } = useForm<FormValues>();

  const [previousKeyPressIsMeta, setPreviousKeyPressIsMeta] =
    useState<boolean>(false);

  const getWorkspaceUsersQuery = trpc.workspace.getUsers.useQuery();
  const workspaceUsers = (getWorkspaceUsersQuery.data?.users || []).map(
    (user) => ({
      id: `/users/${user.id}`,
      display: getFullName(user),
    })
  );

  const createCommentMutation = trpc.comment.createComment.useMutation();
  const deleteCommentMutation = trpc.comment.deleteComment.useMutation();

  const createComment = ({ message }: FormValues) => {
    createCommentMutation.mutate(
      {
        id,
        type,
        message,
      },
      {
        onSuccess: (data) => {
          setComments(data.comments);
          reset({
            message: undefined,
          });
          trpcUtils.subscriber.getForContent.refetch({ id, type });
        },
      }
    );
  };

  const handleDeleteComment = (id: string) => {
    openConfirmationModal({
      title: "Delete this comment?",
      body: "This comment will be removed. This action cannot be undone.",
      cancelButton: {
        label: "Cancel, don't delete",
      },
      primaryButton: {
        variant: "danger",
        label: "Delete Comment",
        onClick: () => {
          const commentIndex = comments.findIndex(
            (comment) => comment.id === id
          );
          const newComments = [...comments];
          newComments.splice(commentIndex, 1);
          setComments(newComments);
          deleteCommentMutation.mutate({
            id,
          });
        },
      },
    });
  };

  const watchMessageInput = watch("message");

  const allActivity = sortBy([...comments, ...activity], "createdAt");

  return (
    <div className="w-full pb-20">
      <div className="flex items-center justify-between">
        <h4>Comments & Activity</h4>
        <Subscribers
          id={id}
          type={type}
          isReadOnly={isReadOnly}
          workspaceUsers={getWorkspaceUsersQuery.data?.users || []}
        />
      </div>
      <Divider padding="sm" />
      <div className="mt-1 flex flex-col gap-4 px-1.5 pt-4">
        {allActivity.map((activity) => {
          const message = activity.message
            .replaceAll("\n", "&nbsp; \n")
            .replaceAll("@[", "[");

          const variables = {
            userName: getFullName(activity.user, { shortenedLastName: true }),
          };

          const parsedMessage = message.replace(
            /\{{(.+?)}}/g,
            (_, variable: string) => {
              if (Object.hasOwn(variables, variable)) {
                // @ts-ignore
                return variables[variable];
              } else if (variable.startsWith("date:")) {
                const value = variable.split("date:")[1];
                return DateTime.fromISO(value).toFormat("M/d/yyyy h:mm a");
              } else {
                return variable;
              }
            }
          );

          // This means it's an activity feed
          if (Object.hasOwn(activity, "type")) {
            return (
              <div
                key={activity.id}
                className="font-body-xs flex items-center gap-1"
              >
                <ReactMarkdown
                  className="text-dark-gray font-light break-words"
                  components={{
                    a: ({ node, ...props }) => {
                      if (
                        node.properties?.href?.toString().startsWith("/users")
                      ) {
                        return (
                          <a
                            className="bg-secondary-light rounded-md p-1"
                            {...props}
                          />
                        );
                      } else {
                        return (
                          <a
                            className="decoration-secondary text-secondary underline"
                            {...props}
                          />
                        );
                      }
                    },
                  }}
                  linkTarget="_blank"
                  children={parsedMessage}
                  remarkPlugins={[remarkBreaks, remarkGfm]}
                />
                <Tooltip
                  value={DateTime.fromJSDate(activity.createdAt).toFormat(
                    "M/d/yyyy h:mm a"
                  )}
                >
                  <span className="text-medium-dark-gray whitespace-nowrap">
                    {DateTime.fromJSDate(activity.createdAt).toRelative()}
                  </span>
                </Tooltip>
              </div>
            );
          } else {
            return (
              <div
                key={activity.id}
                className="group font-body-sm bg-surface box-shadow w-full rounded-lg p-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2">
                      <UserAvatar user={activity.user} size="md" />
                      <span className="font-medium">
                        {getFullName(activity.user)}
                      </span>
                    </div>
                    <Tooltip
                      value={DateTime.fromJSDate(activity.createdAt).toFormat(
                        "M/d/yyyy h:mm a"
                      )}
                    >
                      <span className="text-medium-dark-gray">
                        {DateTime.fromJSDate(activity.createdAt).toRelative()}
                      </span>
                    </Tooltip>
                  </div>
                  <div
                    className={classNames("opacity-0 transition-opacity", {
                      "group-hover:opacity-100": activity.user.id === user?.id,
                    })}
                  >
                    <DropdownMenu.Root>
                      <DropdownMenu.Trigger asChild className="cursor-pointer">
                        <div className="hover:bg-slate rounded-full bg-inherit p-0.5 text-black transition-all">
                          <EllipsisVerticalIcon className="h-5 w-5" />
                        </div>
                      </DropdownMenu.Trigger>
                      <DropdownMenu.Content>
                        <DropdownMenu.Group>
                          <DropdownMenu.Item
                            icon="TrashIcon"
                            intent="danger"
                            onClick={() => handleDeleteComment(activity.id)}
                          >
                            Delete Comment
                          </DropdownMenu.Item>
                        </DropdownMenu.Group>
                      </DropdownMenu.Content>
                    </DropdownMenu.Root>
                  </div>
                </div>
                <div className="mt-3 ml-9">
                  <ReactMarkdown
                    className="font-body-sm overflow-clip"
                    components={{
                      a: ({ node, ...props }) => {
                        if (
                          node.properties?.href?.toString().startsWith("/users")
                        ) {
                          return (
                            <span
                              className="bg-secondary-light rounded-md p-1"
                              {...props}
                            />
                          );
                        } else {
                          return (
                            <a
                              className="decoration-secondary text-secondary w-[50px] whitespace-pre-line underline"
                              {...props}
                            />
                          );
                        }
                      },
                    }}
                    linkTarget="_blank"
                    children={message}
                    remarkPlugins={[remarkBreaks, remarkGfm]}
                  />
                </div>
              </div>
            );
          }
        })}
      </div>
      {!isReadOnly && (
        <form onSubmit={handleSubmit(createComment)} className="mt-4">
          <Controller
            control={control}
            name="message"
            render={({ field: { onChange, value } }) => {
              return (
                <MentionsInput
                  value={value || ""}
                  onChange={(event) => onChange(event.target.value)}
                  onKeyDown={(event) => {
                    if (
                      previousKeyPressIsMeta &&
                      event.key === "Enter" &&
                      value
                    ) {
                      createComment({ message: value });
                    }

                    setPreviousKeyPressIsMeta(event.metaKey);
                  }}
                  style={mentionInputStyles}
                  placeholder="Write your comment here"
                >
                  <Mention
                    trigger="@"
                    style={mentionStyles}
                    data={workspaceUsers}
                  />
                </MentionsInput>
              );
            }}
          />
          <div
            className={classNames(
              "mt-1 flex cursor-default justify-end opacity-0 transition-opacity",
              {
                "block opacity-100": watchMessageInput,
              }
            )}
          >
            <Button
              type="submit"
              isLoading={createCommentMutation.isPending}
              disabled={!watchMessageInput}
            >
              Comment
            </Button>
          </div>
        </form>
      )}
    </div>
  );
};

export default CommentsAndActivity;
