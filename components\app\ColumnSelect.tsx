import { ChartBarIcon } from "@heroicons/react/24/outline";
import { Combobox } from "~/components";

type Props = {
  columns: {
    id: string;
    label: string;
    value: boolean;
    sectionName?: string;
  }[];
  onChange: (
    columns: {
      id: string;
      label: string;
      value: boolean;
    }[]
  ) => void;
};

const ColumnSelect = ({ columns, onChange }: Props) => {
  return (
    <Combobox
      triggerButton={
        <div className="font-body-sm bg-slate text-basic hover:bg-light-gray disabled:text-medium-dark-gray inline-flex h-8 w-fit transform items-center gap-1 rounded-[5px] px-2 py-1 font-medium transition-all">
          <ChartBarIcon className="h-4 w-4" />
          Change Columns
        </div>
      }
      isUppercase={true}
      value={columns
        .filter((column) => column.value)
        .map((column) => column.id)}
      isMulti={true}
      options={columns.map((column) => ({
        value: column.id,
        label: column.label,
        sectionName: column.sectionName,
      }))}
      onChange={(selectedColumns) => {
        onChange(
          columns.map((column) => {
            return {
              ...column,
              value: selectedColumns.includes(column.id),
            };
          })
        );
      }}
    />
  );
};

export default ColumnSelect;
