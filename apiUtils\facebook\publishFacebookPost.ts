import { Asset, FacebookContentType } from "@prisma/client";
import getAssetsForFacebook from "apiUtils/getAssetsForFacebook";
import assert from "assert";
import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import Graph from "~/clients/facebook/Graph";
import { inngest } from "~/clients/inngest/inngest";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";
import { removeEmptyElems } from "~/utils";
import uploadFacebookAssets from "./uploadFacebookAssets";

type Post = {
  id: string;
  facebookIntegration: {
    page: {
      externalID: string;
    };
    accessToken: string;
  } | null;
  facebookContent: {
    id: string;
    copy: string;
    type: FacebookContentType;
    coverPhoto: Asset | null;
    engagements: {
      id: string;
      postDelaySeconds: number;
    }[];
    assets: {
      id: string;
      position: number;
      asset: Asset;
      facebookMediaID: string | null;
      facebookMediaExpiresAt: Date | null;
    }[];
  } | null;
  workspace: {
    name: string;
  };
  title: string;
  workspaceID: string;
};

const triggerPostEngagements = async (
  engagements: {
    id: string;
    postDelaySeconds: number;
  }[],
  pagePostID: string,
  permalink: string
) => {
  try {
    const events = engagements.map((engagement) => {
      return {
        name: "facebook.postEngagement" as const,
        data: {
          externalPostID: pagePostID,
          engagementID: engagement.id,
          delayUntil: DateTime.now()
            .plus({ seconds: engagement.postDelaySeconds })
            .toJSDate(),
          permalink,
        },
      };
    });

    if (events.length > 0) {
      await inngest.send(events);
    }
  } catch (error) {
    console.error(`Error triggering post engagements: ${error}`);
  }
};

const publishFacebookPost = async (
  post: Post
): Promise<ScheduledPostResult> => {
  const { facebookContent, facebookIntegration } = post;

  if (!facebookContent) {
    return {
      status: "NonRetriableError",
      error: "No Facebook content to post",
    };
  }

  if (!facebookIntegration) {
    return {
      status: "NonRetriableError",
      error: "No Facebook integration",
    };
  }

  const graph = new Graph(facebookIntegration.accessToken);
  const facebookPageID = facebookIntegration.page.externalID;

  // Replace the tags that are @[pageName](id) with just @[id]
  const cleanedCopy = facebookContent.copy.replace(
    /@\[(.+?)\]\((\d+?)\)/g,
    (match) => {
      const pageID = match.match(/\((\d+?)\)/)?.[1];

      return `@[${pageID}]`;
    }
  );

  const { assets, coverPhoto } = await getAssetsForFacebook(post);
  const firstAsset = assets.find((asset) => asset.position === 0);
  if (firstAsset == null) {
    const response = await graph.FB.publishMessage({
      accountID: facebookPageID,
      caption: cleanedCopy,
    });

    if (response.errors) {
      const isAuthError = response.isAuthError;

      return {
        status: isAuthError ? "NonRetriableError" : "Error",
        error: response.errors.join(". "),
        isAuthError,
      };
    } else {
      await triggerPostEngagements(
        facebookContent.engagements,
        response.data.id,
        response.data.url
      );

      return {
        status: "Success",
        postUrl: response.data.url,
      };
    }
  }
  const assetType = firstAsset.metadata.mimetype.includes("video")
    ? "video"
    : "photo";

  // If it's a video we just upload and publish
  if (assetType === "video") {
    if (facebookContent.type === "Feed") {
      const videoResponse = await graph.FB.publishVideo({
        accountID: facebookPageID,
        url: firstAsset.signedUrl,
        caption: cleanedCopy,
        coverPhotoUrl: coverPhoto?.signedUrl,
      });

      if (videoResponse.errors != null) {
        const error = videoResponse.errors.join(". ");
        console.error(
          `FB Upload Error post:${post.id}: ${videoResponse.errors.join(". ")}`
        );
        const isAuthError = videoResponse.isAuthError;

        return {
          status: isAuthError ? "NonRetriableError" : "Error",
          error,
          isAuthError,
        };
      } else {
        await triggerPostEngagements(
          facebookContent.engagements,
          videoResponse.data.id,
          videoResponse.data.url
        );

        return {
          status: "Success",
          postUrl: videoResponse.data.url,
        };
      }
    } else if (facebookContent.type === "Reel") {
      const videoResponse = await graph.FB.publishReel({
        accountID: facebookPageID,
        videoUrl: firstAsset.signedUrl,
        caption: cleanedCopy,
        coverPhotoUrl: coverPhoto?.signedUrl,
      });

      if (videoResponse.errors != null) {
        const error = videoResponse.errors.join(". ");
        console.error(
          `FB Upload Error post:${post.id}: ${videoResponse.errors.join(". ")}`
        );
        const isAuthError = videoResponse.isAuthError;

        return {
          status: isAuthError ? "NonRetriableError" : "Error",
          error,
          isAuthError,
        };
      } else {
        await triggerPostEngagements(
          facebookContent.engagements,
          videoResponse.data.id,
          videoResponse.data.url
        );

        return {
          status: "Success",
          postUrl: videoResponse.data.url,
        };
      }
    }
  }

  // If all of the images are already uploaded them we can just publish them
  if (
    facebookContent.assets.every(
      (asset) =>
        asset.facebookMediaID &&
        asset.facebookMediaExpiresAt &&
        DateTime.fromJSDate(asset.facebookMediaExpiresAt)
          .diffNow()
          .as("minutes") > 1
    )
  ) {
    const publishResponse = await graph.FB.publishPhotos({
      accountID: facebookPageID,
      mediaIDs: removeEmptyElems(
        sortBy(facebookContent.assets, "position").map(
          (asset) => asset.facebookMediaID
        )
      ),
      caption: cleanedCopy,
    });

    if (publishResponse.errors) {
      const error = publishResponse.errors.join(". ");
      console.error(
        `FB Upload Error post:${post.id}: ${publishResponse.errors.join(". ")}`
      );
      const isAuthError = publishResponse.isAuthError;

      return {
        status: isAuthError ? "NonRetriableError" : "Error",
        error,
        isAuthError,
      };
    } else {
      await triggerPostEngagements(
        facebookContent.engagements,
        publishResponse.data.id,
        publishResponse.data.url
      );

      return {
        status: "Success",
        postUrl: publishResponse.data.url,
      };
    }
  } else {
    // Otherwise we need to upload the images first and then publish
    const uploadResponse = await uploadFacebookAssets(post);

    if (
      uploadResponse.status === "Error" ||
      uploadResponse.status === "NonRetriableError" ||
      uploadResponse.status === "Skipped"
    ) {
      assert(uploadResponse.status !== "Skipped");

      const isAuthError = uploadResponse.isAuthError;

      return {
        status: isAuthError ? "NonRetriableError" : "Error",
        error: uploadResponse.error,
        isAuthError,
      };
    } else {
      const { assetIDToMediaID } = uploadResponse;

      const publishResponse = await graph.FB.publishPhotos({
        accountID: facebookPageID,
        mediaIDs: removeEmptyElems(
          assets.map((asset) => assetIDToMediaID[asset.id])
        ),
        caption: cleanedCopy,
      });

      if (publishResponse.errors) {
        const error = publishResponse.errors.join(". ");
        console.error(
          `FB Publish Error post:${post.id}: ${publishResponse.errors.join(
            ". "
          )}`
        );

        const isAuthError = publishResponse.isAuthError;

        return {
          status: isAuthError ? "NonRetriableError" : "Error",
          error,
          rawError: publishResponse.rawError,
          isAuthError,
        };
      } else {
        console.log(`FB Publish Success post:${post.id}`);

        await triggerPostEngagements(
          facebookContent.engagements,
          publishResponse.data.id,
          publishResponse.data.url
        );

        return {
          status: "Success",
          postUrl: publishResponse.data.url,
        };
      }
    }
  }
};

export default publishFacebookPost;
