import notifyOfScheduledPost from "apiUtils/notifyOfScheduledPost";
import publishYouTubeVideo from "apiUtils/youtube/publishYouTubeVideo";
import { NonRetriableError, RetryAfterError, slugify } from "inngest";
import { db } from "~/clients";
import { AuthError } from "~/types/ScheduledPostResult";
import { inngest } from "../inngest";

const RETRIES = 4;

export default inngest.createFunction(
  {
    id: slugify("YouTube Shorts Publish Post"),
    retries: RETRIES,
    name: "YouTube Shorts Publish Post",
    concurrency: {
      limit: 1,
      key: "event.data.postID",
    },
    cancelOn: [
      {
        event: "post.rescheduled",
        if: `event.data.channel == "YouTubeShorts" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.unscheduled",
        if: `event.data.channel == "YouTubeShorts" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.deleted",
        match: "data.postID",
      },
    ],
    onFailure: async ({ error, event, step }) => {
      const data = event.data.event.data;
      const { postID } = data;
      console.log(`Post:${postID}: Final Error ${error.message}`);

      const isAuthError = error.name === "AuthError";

      const post = await db.post.findFirstOrThrow({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          youTubeIntegration: {
            select: {
              channel: {
                select: {
                  username: true,
                },
              },
            },
          },
          scheduledPosts: {
            where: {
              channel: "YouTubeShorts",
              status: "Scheduled",
            },
          },
        },
      });

      const scheduledPost = post.scheduledPosts[0];

      if (scheduledPost == null) {
        console.log(`Post:${postID}: No longer scheduled`);
        return;
      }

      await notifyOfScheduledPost({
        post,
        scheduledPost,
        status: "Error",
        account: post.youTubeIntegration
          ? {
              name: `@${post.youTubeIntegration.channel.username}`,
              url: `https://www.youtube.com/@${post.youTubeIntegration.channel.username}`,
            }
          : undefined,
        error: error.message,
        isAuthError,
      });
    },
  },
  [
    {
      event: "post.scheduled",
      if: `event.data.channel == "YouTubeShorts"`,
    },
    {
      event: "post.rescheduled",
      if: `event.data.channel == "YouTubeShorts"`,
    },
  ],
  async ({ event, step, attempt }) => {
    const { delayUntil, postID } = event.data;

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    const response = await step.run("Publish YouTube Shorts Post", async () => {
      console.log(
        `Post:${postID}: Publishing YouTube Shorts Post Attempt ${
          attempt + 1
        }/${RETRIES + 1}`
      );

      const post = await db.post.findFirst({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          workspaceID: true,
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
              slackWebhooks: {
                where: {
                  type: "SuccessfulPost",
                },
                select: {
                  type: true,
                  url: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          createdByID: true,
          youTubeIntegration: {
            select: {
              id: true,
              accessToken: true,
              refreshToken: true,
              idToken: true,
              expiresAt: true,
              channel: {
                select: {
                  username: true,
                },
              },
            },
          },
          youTubeShortsContent: {
            select: {
              id: true,
              title: true,
              description: true,
              categoryID: true,
              privacyStatus: true,
              notifySubscribers: true,
              isEmbeddable: true,
              isSelfDeclaredMadeForKids: true,
              asset: {
                select: {
                  id: true,
                  asset: true,
                },
              },
            },
          },
          scheduledPosts: {
            where: {
              channel: "YouTubeShorts",
              status: "Scheduled",
            },
          },
          approvals: {
            where: {
              isBlocking: true,
              approvedAt: null,
            },
            select: {
              id: true,
            },
          },
        },
      });

      if (post == null) {
        console.log(`Post:${postID}: Post not found`);
        return {
          status: "Skipped",
          comment: "Post has been deleted",
        };
      }

      const scheduledPost = post.scheduledPosts[0];

      if (scheduledPost == null) {
        console.log(`Post:${postID}: No longer scheduled`);
        return {
          status: "Skipped",
          comment: "No scheduled posts",
        };
      }

      if (post.approvals.length > 0) {
        console.log(`Post:${postID}: Blocked by approval`);
        return {
          status: "Skipped",
          comment: "Post has been blocked by approval",
        };
      }

      const publishResponse = await publishYouTubeVideo(post);

      if (publishResponse.status === "NonRetriableError") {
        console.log(
          `Post:${postID}: NonRetriableError ${publishResponse.error}`
        );

        if (publishResponse.isAuthError) {
          throw new AuthError(publishResponse.error);
        } else {
          throw new NonRetriableError(publishResponse.error);
        }
      } else if (publishResponse.status === "Error") {
        console.log(
          `Post:${postID}: Publish Attempt ${attempt + 1}/${
            RETRIES + 1
          } Error ${publishResponse.error}`
        );

        throw new RetryAfterError(publishResponse.error, 10_000);
      }

      console.log(`Post:${postID}: Published YouTube Shorts Post`);

      await notifyOfScheduledPost({
        post,
        scheduledPost,
        account: {
          name: `@${post.youTubeIntegration!.channel.username}`,
          url: `https://www.youtube.com/@${
            post.youTubeIntegration!.channel.username
          }`,
        },
        ...publishResponse,
      });

      return publishResponse;
    });

    return response;
  }
);
