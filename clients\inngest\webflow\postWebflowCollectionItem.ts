import notifyOfScheduledPost from "apiUtils/notifyOfScheduledPost";
import publishWebflowItem from "apiUtils/webflow/publishWebflowItem";
import { NonRetriableError, slugify } from "inngest";
import { db } from "~/clients";
import { inngest } from "../inngest";

export default inngest.createFunction(
  {
    id: slugify("Webflow Post Collection Item"),
    name: "Webflow Post Collection Item",
    concurrency: {
      limit: 1,
      key: "event.data.postID",
    },
    cancelOn: [
      {
        event: "post.rescheduled",
        if: `event.data.channel == "Webflow" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.unscheduled",
        if: `event.data.channel == "Webflow" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.deleted",
        match: "data.postID",
      },
    ],
    onFailure: async ({ error, event, step }) => {
      const data = event.data.event.data;
      const { postID } = data;
      console.log(`Webflow Post:${postID}: Final Error ${error.message}`);

      const post = await db.post.findFirstOrThrow({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          webflowIntegration: {
            select: {
              blogBaseUrl: true,
              siteName: true,
              collectionName: true,
            },
          },
          scheduledPosts: {
            where: {
              channel: "Webflow",
              status: "Scheduled",
            },
          },
        },
      });

      const scheduledPost = post.scheduledPosts[0];

      if (scheduledPost == null) {
        console.log(`Post:${postID}: No longer scheduled`);
        return;
      }

      await notifyOfScheduledPost({
        post,
        scheduledPost,
        status: "Error",
        account: {
          name: `${post.webflowIntegration?.siteName}/${post.webflowIntegration?.collectionName}`,
          url: post.webflowIntegration?.blogBaseUrl || "",
        },
        error: error.message,
      });
    },
  },
  [
    {
      event: "post.scheduled",
      if: `event.data.channel == "Webflow"`,
    },
    {
      event: "post.rescheduled",
      if: `event.data.channel == "Webflow"`,
    },
  ],
  async ({ event, step }) => {
    const { delayUntil, postID } = event.data;

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    await step.run("Post Webflow Collection Item", async () => {
      console.log(`Webflow Post:${postID}: Publishing item`);

      const post = await db.post.findFirstOrThrow({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          webflowIntegration: {
            select: {
              blogBaseUrl: true,
              siteName: true,
              collectionName: true,
            },
          },
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
              slackWebhooks: {
                where: {
                  type: "SuccessfulPost",
                },
                select: {
                  type: true,
                  url: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          scheduledPosts: {
            take: 1,
            where: {
              channel: "Webflow",
              status: "Scheduled",
            },
          },
          approvals: {
            where: {
              isBlocking: true,
              approvedAt: null,
            },
            select: {
              id: true,
            },
          },
        },
      });

      if (post.approvals.length > 0) {
        console.log(`Webflow Post:${postID}: Blocked by approval`);
        return {
          skipped: true,
        };
      }

      const postResponse = await publishWebflowItem({ postID });

      const scheduledPost = post.scheduledPosts[0];

      if (postResponse.status === "Error") {
        console.log(`Webflow Post:${postID}: Errored ${postResponse.error}`);
        if (postResponse.rawError) {
          console.log(
            `Webflow Post:${postID}: Raw Error ${postResponse.rawError}`
          );
        }

        throw new NonRetriableError(
          postResponse.error || "Failed to Publish Webflow Item"
        );
      } else if (postResponse.status === "Success") {
        console.log(
          `Webflow Post:${postID}: Published ${postResponse.postUrl}`
        );

        await notifyOfScheduledPost({
          post,
          scheduledPost,
          account: {
            name: `${post.webflowIntegration!.siteName} - ${
              post.webflowIntegration!.collectionName
            }`,
            url: post.webflowIntegration!.blogBaseUrl || "",
          },
          ...postResponse,
        });

        return {
          url: postResponse.postUrl,
        };
      }
    });
  }
);
