import SupabaseAdmin from "~/clients/supabaseAdminClient";
import { ONE_YEAR_IN_SECONDS } from "~/constants";

const uploadFile = async (
  bucket: "assets",
  fileName: string,
  file: string | Blob | ArrayBuffer | ArrayBufferView | Buffer | File | FormData
) => {
  const { data, error } = await SupabaseAdmin.storage
    .from(bucket)
    .upload(fileName, file, {
      cacheControl: `${ONE_YEAR_IN_SECONDS}`,
    });

  return { data, error };
};

export default uploadFile;
