import { ReactElement } from "react";

import { Checkbox } from "~/components";
import { percentage } from "~/utils";

type Props = {
  value: boolean;
  onChange: (value: boolean) => void;
  label: string;
  detail?: string | ReactElement;
  count: number;
  totalCount: number;
  percentageLabel: string;
};

const CheckboxDetails = ({
  value,
  onChange,
  label,
  detail,
  count,
  totalCount,
  percentageLabel,
}: Props) => {
  return (
    <button
      className="border-ui-300 hover:bg-basic-light hover:border-basic flex w-full flex-col gap-1 rounded-md border px-2 py-2.5"
      onClick={() => onChange(!value)}
    >
      <div className="flex w-full justify-between">
        <Checkbox value={value} label={label} onChange={onChange} />
        <div>
          {count}/<span className="text-ui-500">{totalCount}</span>
        </div>
      </div>
      <div className="flex w-full justify-between">
        {/* Empty div to hold this spot even if there's no detail */}
        {detail || <div />}
        <div className="text-basic text-xs">
          {percentage(count, totalCount, 2)} {percentageLabel}
        </div>
      </div>
    </button>
  );
};

export default CheckboxDetails;
