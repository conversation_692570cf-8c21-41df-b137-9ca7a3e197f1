import { Channel, PostStatus } from "@prisma/client";
import classNames from "classnames";

import dynamic from "next/dynamic";

import { AnimatePresence, motion } from "framer-motion";
import min from "lodash/min";
import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import { useDrop } from "react-dnd";
import { toast } from "sonner";
import ContextMenu from "~/components/ui/ContextMenu";
import { DraggableItemTypes } from "~/constants";
import { TimeFrame } from "~/providers/CalendarViewProvider";
import { FilterType } from "~/types";
import { getPostPublishAt } from "~/utils";
import { RouterOutput } from "~/utils/trpc";
import Campaign from "./Campaign";
import PostCalendarEvent from "./PostCalendarEvent";
const DayHoliday = dynamic(() => import("./DayHoliday"));

type Post = {
  id: string;
  title: string;
  channels: Channel[];
  status: PostStatus;
  campaignID: string | null;
  publishDate: Date;
  publishTime: Date | null;
  publishAt: Date | null;
  approvals: {
    id: string;
    user: {
      id: string;
      firstName: string | null;
      lastName: string | null;
    };
    isBlocking: boolean;
    approvedAt: Date | null;
  }[];
  accounts: {
    id: string;
    name: string;
    profileImageUrl?: string;
    channel: Channel;
  }[];
  labels: {
    id: string;
    name: string;
    color: string;
    backgroundColor: string;
    createdAt: Date;
  }[];
};

type Campaign = {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
};

type FullPost = RouterOutput["post"]["getMany"]["posts"][number];

type Props = {
  day: number;
  startOfWeek: Date;
  posts: Post[];
  campaigns: Campaign[];
  timeFrame: TimeFrame;
  showCampaigns: boolean;
  deletePost: (post: { id: string; title: string }) => void;
  addNewPost: (post: FullPost) => void;
  updatePost: (
    postID: string,
    updatedValues: {
      status?: PostStatus;
      publishDate?: Date;
    }
  ) => void;
  campaignOffsetMapping: { [id: string]: number };
  hoveredCampaignID: string | null;
  setHoveredCampaignID: (hoveredCampaignID: string | null) => void;
  setNewPostModalOpen: (open: boolean) => void;
  setDefaultPublishDate: (date: Date) => void;
  filters: FilterType;
  isReadOnly?: boolean;
  disableCreatePost?: boolean;
};

const CalendarDay = ({
  day,
  startOfWeek,
  posts,
  campaigns,
  timeFrame,
  filters,
  showCampaigns,
  deletePost,
  addNewPost,
  updatePost,
  campaignOffsetMapping,
  hoveredCampaignID,
  setHoveredCampaignID,
  setNewPostModalOpen,
  setDefaultPublishDate,
  isReadOnly,
  disableCreatePost,
}: Props) => {
  const router = useRouter();

  const momentDay = DateTime.fromJSDate(startOfWeek).plus({ day: day });

  const [{ isEventHovering }, dropRef] = useDrop(
    () => ({
      accept: DraggableItemTypes.PostCalendarEvent,

      drop: (item: {
        id: string;
        status: PostStatus;
        publishDate: Date;
        publishTime: Date | null;
        publishAt: Date | null;
      }) => {
        const newDay = momentDay.toJSDate();

        if (
          momentDay.diff(DateTime.fromJSDate(item.publishDate), "days").days !==
          0
        ) {
          if (item.status === "Posted") {
            toast.error("Cannot Move Published Post", {
              description: "This post has been published already.",
              action: {
                label: "View Post",
                onClick: () => router.push(`/posts/${item.id}`),
              },
            });
          } else {
            const now = new Date();
            const publishAt = getPostPublishAt(newDay, item.publishAt);

            if (publishAt && publishAt < now && item.status === "Scheduled") {
              const time =
                DateTime.fromJSDate(publishAt).toFormat("h:mm a ZZZZ");
              const date = DateTime.fromJSDate(publishAt).toFormat("L/d");
              toast.error("Cannot Move to the Past", {
                description: `${time} on ${date} has already passed. Scheduled posts cannot be moved to the past`,
              });
              return;
            }

            updatePost(item.id, {
              publishDate: newDay,
            });

            if (item.status === "Scheduled" && publishAt) {
              toast.success("Post Rescheduled", {
                description: `Rescheduled to ${DateTime.fromJSDate(
                  publishAt
                ).toFormat("L/d 'at' h:mm a ZZZZ")}`,
              });
            }
          }
        }
      },

      collect: (monitor) => ({
        isEventHovering: monitor.isOver(),
      }),
    }),
    [posts, updatePost]
  );

  const viewableCampaigns = showCampaigns
    ? sortBy(campaigns, "startDate").filter((campaign) => {
        const yearDay = momentDay.toFormat("yyyy-MM-dd");
        const startDateYearDay = DateTime.fromJSDate(campaign.startDate, {
          zone: "utc",
        }).toFormat("yyyy-MM-dd");
        const endDateYearDay = DateTime.fromJSDate(campaign.endDate, {
          zone: "utc",
        }).toFormat("yyyy-MM-dd");

        return yearDay >= startDateYearDay && yearDay <= endDateYearDay;
      })
    : [];

  const initialOffset =
    min(
      viewableCampaigns
        .filter((campaign) => campaignOffsetMapping[campaign.id] != null)
        .map((campaign) => campaignOffsetMapping[campaign.id])
    ) || 0;

  let offset = 0;

  const filteredCampaigns =
    filters.campaignIDs.length > 0
      ? viewableCampaigns.filter((campaign) =>
          filters.campaignIDs.includes(campaign.id)
        )
      : viewableCampaigns;

  return (
    <ContextMenu.Root>
      <ContextMenu.Trigger>
        <div
          key={day}
          // @ts-expect-error
          ref={dropRef}
          className={classNames("group relative h-full overflow-clip", {
            "bg-lightest-gray-30": isEventHovering,
            "border-l": day % 7 !== 0,
            "border-t": day > 6,
            "border-l-secondary":
              momentDay
                .startOf("day")
                .diff(DateTime.now().startOf("day"), "days").days === 0,
            "border-lightest-gray":
              momentDay
                .startOf("day")
                .diff(DateTime.now().startOf("day"), "days").days !== 0,
          })}
        >
          <div className="ml-3 flex items-end gap-2">
            <div
              className={classNames(
                "flex items-center justify-center rounded-xs",
                {
                  "bg-secondary":
                    momentDay
                      .startOf("day")
                      .diff(DateTime.now().startOf("day"), "days").days === 0,
                }
              )}
            >
              <div
                className={classNames("font-eyebrow", {
                  "px-1 text-white":
                    momentDay
                      .startOf("day")
                      .diff(DateTime.now().startOf("day"), "days").days === 0,
                  "text-dark-gray":
                    momentDay
                      .startOf("day")
                      .diff(DateTime.now().startOf("day"), "days").days !== 0,
                  "mt-1": day > 6,
                })}
              >
                {momentDay.toFormat("d") === "1" && (
                  <span className="mr-1 underline underline-offset-4">
                    {momentDay.toFormat("MMM")}
                  </span>
                )}
                {momentDay.toFormat("dd")}
              </div>
            </div>
            <DayHoliday day={momentDay.toJSDate()} />
          </div>
          <AnimatePresence initial={false}>
            <div
              className="space-y-1 pt-1"
              style={{ marginTop: 28 * initialOffset }}
            >
              {filteredCampaigns.map((campaign, index) => {
                let previousOffset = null;
                if (index > 0) {
                  previousOffset =
                    campaignOffsetMapping[filteredCampaigns[index - 1].id] ||
                    index - 1;
                }

                return (
                  <motion.div
                    key={momentDay.toFormat("yyyy-MM-dd") + campaign.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  >
                    <Campaign
                      key={campaign.id}
                      campaign={campaign}
                      day={momentDay.toJSDate()}
                      hoveredCampaignID={hoveredCampaignID}
                      setHoveredCampaignID={setHoveredCampaignID}
                      campaignOffsetMapping={campaignOffsetMapping}
                      previousOffset={previousOffset}
                    />
                  </motion.div>
                );
              })}
            </div>
          </AnimatePresence>
          <div
            id="calendar-day"
            className={classNames(
              "scrollbar-hidden flex h-full w-full flex-col justify-start overflow-auto px-2"
            )}
            style={{
              marginTop: "0px !important",
              paddingBottom: `${
                60 + 36 * (offset + viewableCampaigns.length)
              }px`,
            }}
            onClick={(e) => {
              // @ts-expect-error id is defined
              if (e.target.id === "calendar-day" && !disableCreatePost) {
                setNewPostModalOpen(true);
                setDefaultPublishDate(momentDay.toJSDate());
              }
            }}
          >
            {posts.map((post, index) => {
              return (
                <div key={post.id} className="mt-1">
                  <PostCalendarEvent
                    size={timeFrame === "1 month" ? "compact" : "normal"}
                    deletePost={deletePost}
                    post={post}
                    addNewPost={addNewPost}
                    updatePost={updatePost}
                    hoveredCampaignID={hoveredCampaignID}
                    isDragDisabled={isReadOnly}
                  />
                </div>
              );
            })}
          </div>
          <div>
            <div className="absolute bottom-0 h-5 w-full bg-linear-to-t from-white to-white/50 group-hover:h-6" />
            <button
              onClick={() => {
                setNewPostModalOpen(true);
                setDefaultPublishDate(momentDay.toJSDate());
              }}
              className={classNames(
                "text-basic font-body-xs font-body-xs absolute inset-x-1/2 bottom-2.5 -translate-x-12 font-medium whitespace-nowrap opacity-0 transition-all group-hover:opacity-100",
                {
                  hidden: isReadOnly || disableCreatePost,
                }
              )}
            >
              + Create new post
            </button>
          </div>
        </div>
      </ContextMenu.Trigger>
      <ContextMenu.Content>
        <ContextMenu.Item
          icon="PlusIcon"
          intent="none"
          onClick={() => {
            setNewPostModalOpen(true);
            setDefaultPublishDate(momentDay.toJSDate());
          }}
        >
          Create New Post
        </ContextMenu.Item>
      </ContextMenu.Content>
    </ContextMenu.Root>
  );
};

export default CalendarDay;
