import { H1Icon, H2Icon, H3Icon } from "@heroicons/react/24/outline";
import { Toolbar } from "@liveblocks/react-tiptap";
import { Level } from "@tiptap/extension-heading";
import { Editor } from "@tiptap/react";
import Image from "next/image";
import BoldUnicodeButton from "./BoldUnicodeButton";
import ItalicsUnicodeButton from "./ItalicsUnicodeButton";
import LinkMenu from "./LinkMenu";

type ToolbarActionsProps = {
  editor: Editor;
  features?: {
    headings?: Level[];
    bulletList?: boolean;
    orderedList?: boolean;
    link?: boolean;
    unicodeFormatting?: boolean;
  };
};

const getHeadingIcon = (level: Level) => {
  switch (level) {
    case 1:
      return <H1Icon className="h-3 w-3" />;
    case 2:
      return <H2Icon className="h-3 w-3" />;
    case 3:
      return <H3Icon className="h-3 w-3" />;
    default:
      return (
        <Image
          src={`/editor/heading${level}.png`}
          alt={`Heading ${level}`}
          width={14}
          height={14}
        />
      );
  }
};

const ToolbarActions = ({ editor, features = {} }: ToolbarActionsProps) => {
  const {
    headings = [],
    bulletList = false,
    orderedList = false,
    link = false,
    unicodeFormatting = false,
  } = features;

  return (
    <>
      {unicodeFormatting ? (
        <>
          <BoldUnicodeButton editor={editor} />
          <ItalicsUnicodeButton editor={editor} />
        </>
      ) : (
        <Toolbar.BlockSelector
          items={(defaultItems) => {
            const headingItems = defaultItems.filter(
              (item) => item.name.startsWith("Heading") || item.name === "Text"
            );
            const otherItems = defaultItems.filter(
              (item) => !item.name.startsWith("Heading") && item.name !== "Text"
            );

            const allHeadingItems = [
              ...headingItems,
              ...(headings.includes(4)
                ? [
                    {
                      name: "Heading 4",
                      icon: getHeadingIcon(4),
                      isActive: (editor: Editor) =>
                        editor.isActive("heading", { level: 4 }),
                      setActive: (editor: Editor) =>
                        editor
                          .chain()
                          .focus()
                          .toggleHeading({ level: 4 })
                          .run(),
                    },
                  ]
                : []),
              ...(headings.includes(5)
                ? [
                    {
                      name: "Heading 5",
                      icon: getHeadingIcon(5),
                      isActive: (editor: Editor) =>
                        editor.isActive("heading", { level: 5 }),
                      setActive: (editor: Editor) =>
                        editor
                          .chain()
                          .focus()
                          .toggleHeading({ level: 5 })
                          .run(),
                    },
                  ]
                : []),
              ...(headings.includes(6)
                ? [
                    {
                      name: "Heading 6",
                      icon: getHeadingIcon(6),
                      isActive: (editor: Editor) =>
                        editor.isActive("heading", { level: 6 }),
                      setActive: (editor: Editor) =>
                        editor
                          .chain()
                          .focus()
                          .toggleHeading({ level: 6 })
                          .run(),
                    },
                  ]
                : []),
            ];

            const filteredHeadingItems = allHeadingItems.filter((item) => {
              if (item.name === "Text") return true;
              if (item.name.startsWith("Heading")) {
                const level = parseInt(item.name.split(" ")[1]) as Level;
                return headings.includes(level);
              }
              return true;
            });

            const filteredOtherItems = otherItems.filter((item) => {
              if (item.name === "Bullet list" && !bulletList) return false;
              if (item.name === "Numbered list" && !orderedList) return false;
              return true;
            });

            return [...filteredHeadingItems, ...filteredOtherItems];
          }}
        />
      )}
      <Toolbar.SectionInline />
      {link && <LinkMenu editor={editor} />}
      <Toolbar.SectionCollaboration />
    </>
  );
};

export default ToolbarActions;
