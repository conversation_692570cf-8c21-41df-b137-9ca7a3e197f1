import { SubscriptionStatus } from "@prisma/client";
import { DateTime } from "luxon";
import Badge from "~/components/Badge";
import { capitalizeWords } from "~/utils";

const SubscriptionBadge = ({
  company,
}: {
  company: {
    trialEndsAt: Date | null;
    subscription: {
      status: SubscriptionStatus;
    } | null;
  };
}) => {
  const { subscription } = company;
  if (company.trialEndsAt) {
    const trialEndsAt = DateTime.fromJSDate(company.trialEndsAt);
    const now = DateTime.now();
    const hoursRemaining = trialEndsAt.diff(now, "hours").hours;
    const daysRemaining = Math.max(Math.round(hoursRemaining / 24), 1);

    if (hoursRemaining > 0) {
      return (
        <Badge
          intent="info"
          isUppercase={true}
          kind="light"
          size="sm"
          label={`Trial: ${
            hoursRemaining >= 12
              ? `${daysRemaining} days`
              : `${hoursRemaining} hours`
          }`}
        />
      );
    } else {
      return (
        <Badge
          intent="warning"
          isUppercase={true}
          kind="light"
          size="sm"
          label="Trial Expired"
        />
      );
    }
  }

  if (subscription === null) {
    return null;
  }

  const { status } = subscription;

  return (
    <Badge
      intent={
        status === "active"
          ? "success"
          : status === "trialing"
            ? "info"
            : "danger"
      }
      isUppercase={true}
      kind="light"
      size="sm"
      label={capitalizeWords(status)}
    />
  );
};

export default SubscriptionBadge;
