type Company = {
  workspaces: {
    _count: {
      userWorkspaces: number;
      twitterIntegrations: number;
      instagramIntegrations: number;
      threadsIntegrations: number;
      linkedInIntegrations: number;
      discordWebhooks: number;
      tikTokIntegrations: number;
      slackIntegrations: number;
      facebookIntegrations: number;
      youTubeIntegrations: number;
      webflowIntegrations: number;
    };
  }[];
};

// Take the highest number of connections across all social platforms
const countMaxSocialProfiles = (company: Company) => {
  return Math.max(
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.twitterIntegrations
          : 0),
      0
    ),
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.instagramIntegrations
          : 0),
      0
    ),
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.threadsIntegrations
          : 0),
      0
    ),
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.linkedInIntegrations
          : 0),
      0
    ),
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.discordWebhooks
          : 0),
      0
    ),
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.tikTokIntegrations
          : 0),
      0
    ),
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.facebookIntegrations
          : 0),
      0
    ),
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.youTubeIntegrations
          : 0),
      0
    ),
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.slackIntegrations
          : 0),
      0
    ),
    company.workspaces.reduce(
      (acc, workspace) =>
        acc +
        (workspace._count.userWorkspaces > 0
          ? workspace._count.webflowIntegrations
          : 0),
      0
    )
  );
};

export default countMaxSocialProfiles;
