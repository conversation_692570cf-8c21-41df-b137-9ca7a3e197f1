import axios from "axios";
import { StatusCodes } from "http-status-codes";
import { createErrorResponse, Response } from "../Response";

const catchLinkedInError = (
  error: unknown,
  defaultErrorMessage: string
): Response<any> => {
  console.error(`LinkedIn Error ${defaultErrorMessage}`, error);

  if (axios.isAxiosError(error)) {
    const response = error.response;

    const message = response?.data.message || response?.data.error_description;

    console.log("LinkedIn Error Response", response?.data);

    let parsedMessage: string = message;
    // Example Error: "com.linkedin.content.common.exception.BadRequestResponseException: ShareCommentary text length (3160 characters) exceeded the maximum allowed (3000 characters)"
    if (message && message.startsWith("com.linkedin")) {
      const errorRegex = /.*?:(.*)/;
      const maybeMatch = errorRegex.exec(parsedMessage);
      parsedMessage = maybeMatch ? maybeMatch[1].trim() : message;
    }

    // 'Unable to retrieve access token: appid/redirect uri/code verifier does not match authorization code. Or authorization code expired. Or external member binding exists'
    if (message?.includes("authorization code expired")) {
      parsedMessage = "Authorization code expired, please try again";
    }

    return createErrorResponse(
      [parsedMessage],
      response?.status || StatusCodes.UNPROCESSABLE_ENTITY,
      response?.data
    );
  } else {
    return createErrorResponse(
      [defaultErrorMessage],
      StatusCodes.UNPROCESSABLE_ENTITY
    );
  }
};

export default catchLinkedInError;
