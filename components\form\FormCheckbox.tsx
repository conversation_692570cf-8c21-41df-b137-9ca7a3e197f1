import { Controller, Path } from "react-hook-form";

import { Checkbox } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import camelCaseToWords from "~/utils/camelCaseToWords";

type FormCheckboxProps<T> = {
  control: any;
  name: Path<T>;
  disabled?: boolean;
  label?: string | JSX.Element;
  isOptional?: boolean;
  tooltipText?: string;
  rules?: Rules;
};

const FormCheckbox = <T,>({
  control,
  name,
  label,
  rules = {},
}: FormCheckboxProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <div>
            <Checkbox value={value} onChange={onChange} label={label} />
            <Error error={error} />
          </div>
        );
      }}
    />
  );
};

export default FormCheckbox;
