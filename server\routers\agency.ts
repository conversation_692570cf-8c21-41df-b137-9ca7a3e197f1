import { assert } from "console";
import omit from "lodash/omit";
import { db } from "~/clients";
import { filterPosts } from "~/utils";
import { getPostProfiles } from "~/utils/posts/getPostProfiles";
import { protectedProcedure, router } from "../trpc";
import z from 'zod';
import { TRPCError } from '@trpc/server';

export const agencyRouter = router({
  getWorkspaces: protectedProcedure.query(async ({ ctx }) => {
    const userCompanies = await db.userCompany.findMany({
      where: {
        userID: ctx.user.id,
      },
      take: 1,
      select: {
        company: {
          select: {
            id: true,
            name: true,
            workspaces: {
              where: {
                userWorkspaces: {
                  some: {
                    userID: ctx.user.id,
                  },
                },
              },
              select: {
                id: true,
                name: true,
                posts: {
                  where: {
                    isIdea: false,
                  },
                  select: {
                    id: true,
                    title: true,
                    channels: true,
                    campaignID: true,
                    publishAt: true,
                    publishDate: true,
                    publishTime: true,
                    status: true,
                    isIdea: true,
                    instagramIntegrationID: true,
                    instagramIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    instagramContent: {
                      select: {
                        type: true,
                      },
                    },
                    threadsIntegrationID: true,
                    threadsIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    facebookIntegrationID: true,
                    facebookIntegration: {
                      select: {
                        id: true,
                        page: {
                          select: {
                            name: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    linkedInIntegrationID: true,
                    linkedInIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            name: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    tikTokIntegrationID: true,
                    tikTokIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    youTubeIntegrationID: true,
                    youTubeIntegration: {
                      select: {
                        id: true,
                        channel: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    twitterIntegrationID: true,
                    twitterIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    discordWebhookID: true,
                    discordWebhook: {
                      select: {
                        id: true,
                        channel: true,
                        username: true,
                        profileImageUrl: true,
                      },
                    },
                    slackIntegrationID: true,
                    slackIntegration: {
                      select: {
                        id: true,
                        displayName: true,
                        profileImageUrl: true,
                      },
                    },
                    webflowIntegrationID: true,
                    webflowIntegration: {
                      select: {
                        id: true,
                        siteName: true,
                        collectionName: true,
                      },
                    },
                    subscribers: {
                      select: {
                        id: true,
                        userID: true,
                        user: {
                          select: {
                            firstName: true,
                            lastName: true,
                          },
                        },
                      },
                    },
                    postLabels: {
                      select: {
                        label: {
                          select: {
                            id: true,
                            name: true,
                            color: true,
                            backgroundColor: true,
                            createdAt: true,
                          },
                        },
                      },
                    },
                    approvals: {
                      select: {
                        id: true,
                        user: {
                          select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                          },
                        },
                        status: true,
                        isBlocking: true,
                        approvedAt: true,
                      },
                    },
                    createdAt: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      company: {
        ...userCompanies[0].company,
        workspaces: userCompanies[0].company.workspaces.map((workspace) => {
          return {
            ...workspace,
            posts: filterPosts(
              workspace.posts.map((post) => {
                const publishDate = post.publishDate;
                assert(publishDate != null);

                const { postLabels, ...rest } = omit(post, [
                  "linkedInIntegration",
                  "twitterIntegration",
                  "instagramIntegration",
                  "threadsIntegration",
                  "facebookIntegration",
                  "tikTokIntegration",
                  "youTubeIntegration",
                  "discordWebhook",
                  "slackIntegration",
                  "webflowIntegration",
                ]);

                const accounts = getPostProfiles(post);

                return {
                  ...rest,
                  publishDate,
                  subscribers: post.subscribers.map((subscriber) => {
                    return {
                      ...subscriber,
                      user: subscriber.user,
                    };
                  }),
                  accounts,
                  labels: postLabels.map((postLabel) => {
                    return {
                      id: postLabel.label.id,
                      name: postLabel.label.name,
                      color: postLabel.label.color,
                      backgroundColor: postLabel.label.backgroundColor,
                      createdAt: postLabel.label.createdAt,
                    };
                  }),
                };
              })
            ),
          };
        }),
      },
    };
  }),

  getMembers: protectedProcedure.query(async ({ ctx }) => {
    const userCompanies = await db.userCompany.findMany({
      where: {
        userID: ctx.user.id,
      },
      take: 1,
      select: {
        company: {
          select: {
            id: true,
            workspaces: {
              select: {
                id: true,
                name: true,
                userWorkspaces: {
                  select: {
                    userID: true,
                    user: {
                      select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!userCompanies[0]) {
      return {
        agencyUsers: [] as Array<{
          id: string;
          firstName: string | null;
          lastName: string | null;
          email: string;
          workspaceCount: number;
        }>,
        clientUsers: [] as Array<{
          id: string;
          firstName: string | null;
          lastName: string | null;
          email: string;
          workspaceCount: number;
        }>,
        pendingUsers: [] as Array<{
          id: string;
          firstName: string | null;
          lastName: string | null;
          email: string;
          workspaceCount: number;
        }>,
      };
    }

    type UserWithWorkspaceCount = {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
      workspaceCount: number;
    };

    const userWorkspaceCount = new Map<string, number>();
    const allUsers = new Map<string, {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    }>();

    userCompanies[0].company.workspaces.forEach((workspace) => {
      workspace.userWorkspaces.forEach((userWorkspace) => {
        const userId = userWorkspace.userID;
        const user = userWorkspace.user;

        userWorkspaceCount.set(userId, (userWorkspaceCount.get(userId) || 0) + 1);

        if (!allUsers.has(userId)) {
          allUsers.set(userId, {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          });
        }
      });
    });

    const agencyUsers: UserWithWorkspaceCount[] = [];
    const clientUsers: UserWithWorkspaceCount[] = [];
    const pendingUsers: UserWithWorkspaceCount[] = [];

    allUsers.forEach((user, userId) => {
      const workspaceCount = userWorkspaceCount.get(userId) || 0;
      const userWithCount: UserWithWorkspaceCount = {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        workspaceCount,
      };

      if (workspaceCount > 1) {
        agencyUsers.push(userWithCount);
      } else {
        clientUsers.push(userWithCount);
      }
    });

    return {
      agencyUsers,
      clientUsers,
      pendingUsers,
    };
  }),
  removeUser: protectedProcedure
    .input(
      z.object({
        userID: z.string().uuid(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { userID } = input;

      try {
        const userCompanies = await db.userCompany.findMany({
          where: {
            userID: ctx.user.id,
          },
          take: 1,
          select: {
            company: {
              select: {
                id: true,
                workspaces: {
                  select: {
                    id: true,
                    userWorkspaces: {
                      where: {
                        userID: userID,
                      },
                      select: {
                        id: true,
                        userID: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        if (!userCompanies[0]) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Company not found",
          });
        }

        const userWorkspacesToRemove: string[] = [];
        userCompanies[0].company.workspaces.forEach((workspace) => {
          workspace.userWorkspaces.forEach((userWorkspace) => {
            userWorkspacesToRemove.push(userWorkspace.id);
          });
        });

        if (userWorkspacesToRemove.length === 0) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found in any workspace",
          });
        }

        await db.userWorkspace.deleteMany({
          where: {
            id: {
              in: userWorkspacesToRemove,
            },
          },
        });

        return {
          success: true,
          message: "User removed successfully",
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to remove user",
        });
      }
    }),

  getAllWorkspaces: protectedProcedure.query(async ({ ctx }) => {
    try {
      const userCompanies = await db.userCompany.findMany({
        where: {
          userID: ctx.user.id,
        },
        take: 1,
        select: {
          company: {
            select: {
              id: true,
              workspaces: {
                select: {
                  id: true,
                  name: true,
                  _count: {
                    select: {
                      userWorkspaces: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!userCompanies[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No company found for user",
        });
      }

      return {
        workspaces: userCompanies[0].company.workspaces,
      };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch workspaces",
      });
    }
  }),

  getUserWorkspaces: protectedProcedure
    .input(
      z.object({
        userID: z.string().uuid(),
      })
    )
    .query(async ({ input, ctx }) => {
      const { userID } = input;

      try {
        const userCompanies = await db.userCompany.findMany({
          where: {
            userID: ctx.user.id,
          },
          take: 1,
          select: {
            company: {
              select: {
                id: true,
                workspaces: {
                  select: {
                    id: true,
                    name: true,
                    userWorkspaces: {
                      where: {
                        userID: userID,
                      },
                      select: {
                        id: true,
                        userID: true,
                      },
                    },
                    _count: {
                      select: {
                        userWorkspaces: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        if (!userCompanies[0]) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Company not found",
          });
        }

        const workspacesWithMembership = userCompanies[0].company.workspaces.map((workspace) => ({
          id: workspace.id,
          name: workspace.name,
          memberCount: workspace._count.userWorkspaces,
          isMember: workspace.userWorkspaces.length > 0,
        }));

        return {
          workspaces: workspacesWithMembership,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch user workspaces",
        });
      }
    }),

  updateUserWorkspaces: protectedProcedure
    .input(
      z.object({
        userID: z.string().uuid(),
        workspaceIDs: z.array(z.string().uuid()),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { userID, workspaceIDs } = input;

      try {
        const userCompanies = await db.userCompany.findMany({
          where: {
            userID: ctx.user.id,
          },
          take: 1,
          select: {
            company: {
              select: {
                id: true,
                workspaces: {
                  select: {
                    id: true,
                    userWorkspaces: {
                      where: {
                        userID: userID,
                      },
                      select: {
                        id: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        if (!userCompanies[0]) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Company not found",
          });
        }

        const companyWorkspaceIDs = userCompanies[0].company.workspaces.map(w => w.id);

        const invalidWorkspaceIDs = workspaceIDs.filter(id => !companyWorkspaceIDs.includes(id));
        if (invalidWorkspaceIDs.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Some workspace IDs do not belong to your company",
          });
        }

        const currentUserWorkspaces = userCompanies[0].company.workspaces
          .filter(w => w.userWorkspaces.length > 0)
          .map(w => ({ workspaceID: w.id, userWorkspaceID: w.userWorkspaces[0].id }));

        const currentWorkspaceIDs = currentUserWorkspaces.map(uw => uw.workspaceID);

        const workspacesToAdd = workspaceIDs.filter(id => !currentWorkspaceIDs.includes(id));
        const workspacesToRemove = currentUserWorkspaces.filter(uw => !workspaceIDs.includes(uw.workspaceID));

        if (workspacesToRemove.length > 0) {
          await db.userWorkspace.deleteMany({
            where: {
              id: {
                in: workspacesToRemove.map(uw => uw.userWorkspaceID),
              },
            },
          });
        }

        if (workspacesToAdd.length > 0) {
          await db.userWorkspace.createMany({
            data: workspacesToAdd.map(workspaceID => ({
              userID: userID,
              workspaceID: workspaceID,
            })),
          });
        }

        return {
          success: true,
          message: "User workspace access updated successfully",
          added: workspacesToAdd.length,
          removed: workspacesToRemove.length,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user workspace access",
        });
      }
    }),
});

