import { Channel as PrismaChannel } from "@prisma/client";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import { CHANNEL_ICON_MAP } from "~/types";

const ChannelIcon = ({
  channel,
  width,
  showLabel = false,
}: {
  channel: PrismaChannel;
  width: number;
  showLabel?: boolean;
}) => {
  const minWidth = width < 20 && channel === "Youtube" ? 20 : width;
  const minHeight = width < 20 && channel === "Youtube" ? 24 : width;

  return (
    <div className="flex items-center">
      <div
        className="relative resize-none"
        style={{
          height: minHeight,
          width: minWidth,
        }}
      >
        <Image
          src={CHANNEL_ICON_MAP[channel].icon}
          alt={channel}
          fill={true}
          sizes={`${minHeight}px`}
          className="object-contain"
        />
      </div>
      <AnimatePresence>
        {showLabel && (
          <motion.div
            initial={false}
            animate={{ opacity: 1, paddingLeft: 4 }}
            exit={{ opacity: 0, width: 0, paddingLeft: 0 }}
          >
            {CHANNEL_ICON_MAP[channel].label}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ChannelIcon;
