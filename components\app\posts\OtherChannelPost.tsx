import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import assert from "assert";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import { useWorkspace } from "~/providers";
import type { AssetType } from "~/types/Asset";
import {
  cleanFileName,
  handleFilesUpload,
  handleTrpcError,
  trpc,
} from "~/utils";
import createRoomID from "~/utils/liveblocks/createRoomID";
import Assets, { UploadingAsset } from "../files/Assets";
import OtherChannelEditorWrapper from "../tiptap/OtherChannelEditor";
import EditorActionsBar from "./EditorActionsBar";

type Post = {
  id: string;
  workspaceID: string;
  otherChannelEditorState: JSONContent | null;
  assets: AssetType[];
};

type Props = {
  post: Post;
  isReadOnly?: boolean;
};

const OtherChannelPost = ({ post, isReadOnly }: Props) => {
  const trpcUtils = trpc.useUtils();
  const { currentWorkspace } = useWorkspace();
  const [editorState, setEditorState] = useState<JSONContent | null>(
    post.otherChannelEditorState
  );
  const [assets, setAssets] = useState<AssetType[]>(post.assets);
  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);
  const [isRefetching, setIsRefetching] = useState<boolean>(false);
  const [draggedOver, setDraggedOver] = useState<boolean>(false);
  const [uppy, setUppy] = useState<Uppy | null>(null);

  const updatePostMutation = trpc.post.update.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Error Saving Post", error });
    },
  });

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        restrictions: { maxNumberOfFiles: 4 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", async (file) => {
        assert(currentWorkspace);
        setUploadingAssets((uploadingAssets) => {
          return [
            ...uploadingAssets,
            {
              name: file.name!,
              size: file.size!,
              type: file.type,
              uploadProgress: 0,
            },
          ];
        });
        const objectName = `${currentWorkspace.id}/${post.id}/${file.name}`;
        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", () => {
        assetQuery.refetch();
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace]);

  const assetQuery = trpc.asset.getAll.useQuery({
    contentID: post.id,
  });

  const assetData = assetQuery.data;
  useEffect(() => {
    if (assetData) {
      if (isRefetching) {
        setIsRefetching(false);
        setAssets(assetData.assets);
      }

      if (
        !isReadOnly &&
        uploadingAssets.length &&
        assetData.assets.length !== assets.length
      ) {
        const stillUploadingAssets = uploadingAssets.filter((asset) => {
          return !assetData.assets.some((a) => a.name === asset.name);
        });
        setUploadingAssets(stillUploadingAssets);
        setAssets(assetData.assets);
      }
    }
  }, [assetData]);

  return (
    <div
      className="group"
      onDragOverCapture={() => {
        setDraggedOver(true);
      }}
      onDragLeaveCapture={() => {
        setDraggedOver(false);
      }}
    >
      <OtherChannelEditorWrapper
        title="Notes"
        initialValue={editorState}
        isReadOnly={isReadOnly}
        onImagePaste={async (event) => {
          handleFilesUpload({
            items: event.clipboardData.items,
            uppy: uppy!,
            meta: {
              contentID: post.id,
            },
            getValidFiles: async (files) => files,
          });
        }}
        onSave={(editorState) => {
          updatePostMutation.mutate({
            id: post.id,
            otherChannelEditorState: editorState,
          });
          const postData = trpcUtils.post.get.getData({
            id: post.id,
          });
          if (postData) {
            trpcUtils.post.get.setData(
              {
                id: post.id,
              },
              {
                post: {
                  ...postData.post,
                  otherChannelEditorState: editorState as {},
                },
                isReadOnly: postData.isReadOnly,
              }
            );
          }
          setEditorState(editorState);
        }}
        placeholder="Add any copy, attachments, links, or additional notes for the post"
        roomID={createRoomID({
          workspaceID: post.workspaceID,
          contentType: "post",
          postID: post.id,
          roomContentID: post.id,
        })}
      />
      <EditorActionsBar
        visible={true}
        isReadOnly={isReadOnly}
        uploadAssetButton={{
          isLoading: uploadingAssets.length > 0,
          acceptedMimetypes: [
            "image/*",
            "video/*",
            "audio/*",
            "application/pdf",
          ],
          acceptMultiple: true,
          hideTooltip: true,
          onUploadFiles: async (files) => {
            files.forEach((file) => {
              uppy!.addFile({
                source: "File Input",
                meta: {
                  contentID: post.id,
                },
                name: cleanFileName(file.name),
                type: file.type,
                data: file,
              });
            });
          },
        }}
      />
      <div className="pt-1">
        <Assets
          assets={assets}
          refetchAssets={() => {
            setIsRefetching(true);
            assetQuery.refetch();
          }}
          uploadingAssets={uploadingAssets}
          onDrop={async (files) => {
            setDraggedOver(false);
            await handleFilesUpload({
              files,
              uppy: uppy!,
              meta: {
                contentID: post.id,
              },
              getValidFiles: async (files) => files,
            });
          }}
          showDropzone={draggedOver}
          isReadOnly={isReadOnly}
        />
      </div>
    </div>
  );
};

export default OtherChannelPost;
