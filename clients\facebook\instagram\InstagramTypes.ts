import { InstagramMediaProductType, InstagramMediaType } from "@prisma/client";

export type IGPost = {
  id: string;

  shortcode: string;
  permalink: string;

  username: string;

  mediaType: InstagramMediaType;
  mediaProductType: InstagramMediaProductType;

  caption: string | null;
  hashtags: string[];
  hashtagCount: number;
  captionMentions: string[];
  captionMentionCount: number;

  reelSharedToFeed: boolean | null;

  imageUrls: string[];
  videoUrls: string[];
  thumbnailUrl: string;

  impressionCount: number;
  reachCount: number;
  savedCount: number;
  likeCount: number;
  commentCount: number;
  profileVisitCount: number;
  shareCount: number;
  followCount: number;

  publishedAt: Date;

  rawResponse: {};
};

export type RawIGPost = {
  id: string;
  username: string;
  shortcode: string;
  permalink: string;
  caption?: string;
  media_url: string;
  media_type: InstagramMediaType;
  thumbnail_url?: string;
  timestamp: string;
  media_product_type: InstagramMediaProductType;
  like_count: number;
  comments_count: number;
  children?: {
    data: {
      id: string;
      media_url: string;
      media_type: InstagramMediaType;
    }[];
  };
  is_shared_to_feed?: boolean;
  insights: {
    data: {
      name:
        | "carousel_album_engagement"
        | "carousel_album_impressions"
        | "carousel_album_reach"
        | "carousel_album_saved"
        | "carousel_album_video_views"
        | "ig_reels_avg_watch_time"
        | "ig_reels_video_view_total_time"
        | "reach"
        | "likes"
        | "reach"
        | "views"
        | "saved"
        | "shares"
        | "total_interactions"
        | "follows"
        | "profile_visits"
        | "comments";
      period: "lifetime";
      values: [
        {
          value: number;
        },
      ];
      title: string;
      description: string;
    }[];
  };
};
