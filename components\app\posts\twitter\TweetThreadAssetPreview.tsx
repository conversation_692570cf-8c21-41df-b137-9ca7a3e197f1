import { ArrowLeftIcon, ArrowRightIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import { useEffect, useRef, useState } from "react";
import { Player } from "video-react";
import { useBreakpoints } from "~/hooks";
import { getVideoProperties } from "~/utils";

type Asset = {
  signedUrl: string;
  metadata: {
    mimetype: string;
  };
};

type Props = {
  assets:
    | {
        signedUrl: string;
        metadata: {
          mimetype: string;
        };
      }[]
    | null;
};

type CarouselProps = {
  assets: Asset[];
};

const TweetThreadAssetPreview = ({ assets }: Props) => {
  const numAssets = assets?.length ?? 0;

  const [videoDimensions, setVideoDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);

  useEffect(() => {
    const getVideoDimensions = async () => {
      if (assets) {
        const videoAsset = assets[0];
        if (videoAsset && videoAsset.metadata.mimetype.startsWith("video")) {
          const { width, height } = await getVideoProperties(
            videoAsset.signedUrl
          );

          setVideoDimensions({
            width,
            height,
          });
        }
      }
    };

    getVideoDimensions();
  }, [numAssets]);

  if (assets == null) {
    return null;
  }

  if (numAssets === 0) {
    return null;
  } else if (numAssets === 1) {
    const asset = assets[0];
    const isVideo = asset.metadata.mimetype.startsWith("video");
    if (isVideo) {
      if (
        videoDimensions == null ||
        videoDimensions.width / videoDimensions.height > 0.8
      ) {
        return (
          <div className="w-full overflow-clip rounded-[16px] border border-[#CFD9DE]">
            <Player src={asset.signedUrl} />
          </div>
        );
      } else {
        return (
          <div className="relative aspect-square h-full w-full overflow-clip rounded-[16px] border border-[#CFD9DE]">
            <div className="absolute h-full w-full bg-black" />
            <div className="mx-auto w-fit">
              <Player src={asset.signedUrl} height={500} fluid={false} />
            </div>
          </div>
        );
      }
    } else {
      return (
        <img
          className="rounded-[15px] border border-[#D1D9DD]"
          src={assets[0].signedUrl}
        />
      );
    }
  } else {
    return <TwitterAssetCarousel assets={assets} />;
  }
};

function TwitterAssetCarousel({ assets }: Readonly<CarouselProps>) {
  const { isMobile } = useBreakpoints();
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [currentOffset, setCurrentOffset] = useState<number>(0);
  const [totalWidth, setTotalWidth] = useState<number>(0);
  const [imagesLoaded, setImagesLoaded] = useState<number>(0);

  const containerWidth = isMobile ? 252 : 512;
  const itemGap = 8;

  useEffect(() => {
    const calculatedWidth = itemRefs.current.reduce((sum, item) => {
      if (item) {
        return sum + item.offsetWidth + itemGap;
      }
      return sum;
    }, 0);

    const newTotalWidth = Math.max(0, calculatedWidth - itemGap);
    setTotalWidth(newTotalWidth);

    // offset within bounds
    const maxOffset = Math.max(0, newTotalWidth - containerWidth);
    if (-currentOffset > maxOffset) {
      setCurrentOffset(-maxOffset);
    }
  }, [imagesLoaded, assets, itemGap, containerWidth, currentOffset]);

  const handlePrev = () => {
    if (isMobile) {
      const prevOffset =
        currentOffset + (itemRefs.current[0]?.offsetWidth || 0) + itemGap;
      setCurrentOffset(Math.min(prevOffset, 0));
    } else {
      setCurrentOffset(0);
    }
  };

  const handleNext = () => {
    if (isMobile) {
      const nextOffset =
        currentOffset - (itemRefs.current[0]?.offsetWidth ?? 0) - itemGap;
      const maxOffset = -(totalWidth - containerWidth);
      setCurrentOffset(Math.max(nextOffset, maxOffset));
    } else {
      setCurrentOffset(-(totalWidth - containerWidth));
    }
  };

  const handleMediaLoad = () => {
    setImagesLoaded((prev) => prev + 1);
  };

  return (
    <div className="relative overflow-hidden" style={{ width: containerWidth }}>
      {currentOffset < 0 && (
        <button
          onClick={handlePrev}
          className="bg-dark-gray hover:bg-medium-dark-gray absolute top-1/2 left-2 z-10 -translate-y-1/2 rounded-full p-2"
        >
          <ArrowLeftIcon className="h-4 w-4 text-white" strokeWidth={3} />
        </button>
      )}

      {currentOffset > -(totalWidth - containerWidth) && (
        <button
          onClick={handleNext}
          className="bg-dark-gray hover:bg-medium-dark-gray absolute top-1/2 right-2 z-10 -translate-y-1/2 rounded-full p-2"
        >
          <ArrowRightIcon className="h-4 w-4 text-white" strokeWidth={3} />
        </button>
      )}

      <div
        className="flex transition-transform duration-500"
        style={{
          transform: `translateX(${currentOffset}px)`,
        }}
      >
        {assets.map((asset, index) => (
          <div
            key={index}
            ref={(el) => {
              itemRefs.current[index] = el;
            }}
            className={classNames(
              "shrink-0 overflow-hidden rounded-2xl border border-[#D1D9DD]",
              { "mr-2": index !== assets.length - 1 },
              { "w-fit": isMobile }
            )}
          >
            {asset.metadata.mimetype.startsWith("video") ? (
              <video
                controls
                playsInline
                className="h-[273px] object-contain"
                src={asset.signedUrl}
                onLoadedMetadata={handleMediaLoad}
              />
            ) : (
              <img
                src={asset.signedUrl}
                alt=""
                className="h-[273px] w-auto object-cover md:object-contain"
                onLoad={handleMediaLoad}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

export default TweetThreadAssetPreview;
