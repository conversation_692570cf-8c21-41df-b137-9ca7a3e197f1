import urlRegex from "../urlRegex";

describe("urlRegex", () => {
  const testCases = [
    {
      input: "Visit https://www.example.com for more info.",
      expected: ["https://www.example.com"],
    },
    {
      input: "Check out http://subdomain.example.co.uk/page?param=value",
      expected: ["http://subdomain.example.co.uk/page?param=value"],
    },
    {
      input: "www.example.org is a website without protocol.",
      expected: ["www.example.org"],
    },
    { input: "example.com is a bare domain.", expected: ["example.com"] },
    { input: "sub.example.net is a subdomain.", expected: ["sub.example.net"] },
    {
      input: "Visit https://example.com/path/to/page#section for details.",
      expected: ["https://example.com/path/to/page#section"],
    },
    {
      input: "Multiple URLs: https://one.com and http://two.org",
      expected: ["https://one.com", "http://two.org"],
    },
    { input: "Invalid: www.invalid", expected: null },
    { input: "No URLs here!", expected: null },
    { input: "Email address: <EMAIL> is not a URL.", expected: null },
    {
      input: "URL in parentheses (https://example.com) should work.",
      expected: ["https://example.com"],
    },
    {
      input: "URL in parentheses (https://example.com/) should work.",
      expected: ["https://example.com/"],
    },
    {
      input: "URL with underscore: http://example.com/path_with_underscore",
      expected: ["http://example.com/path_with_underscore"],
    },
    {
      input: "URL ending with period: https://www.example.com.",
      expected: ["https://www.example.com"],
    },
    {
      input: "URL with hyphen: https://www.example-domain.com",
      expected: ["https://www.example-domain.com"],
    },
  ];

  test.each(testCases)(
    "should correctly match URLs in: $input",
    ({ input, expected }) => {
      const matches = input.match(urlRegex);
      expect(matches).toEqual(expected);
    }
  );

  const validUrls = [
    "http://www.foufos.gr",
    "https://www.foufos.gr",
    "http://foufos.gr",
    "http://www.foufos.gr/kino",
    "http://werer.gr",
    "www.foufos.gr",
    "www.mp3.com",
    "www.t.co",
    "t.co",
    "http://t.co",
    "http://www.t.co",
    "https://www.t.co",
    "www.aa.com",
    "http://aa.com",
    "http://www.aa.com",
    "https://www.aa.com",
    "www.google.com/123-456/7890",
    "google.com/123-456/7890",
    "https://www.google.com/123-456/7890",
    "http://www.google.com/123-456/7890",
    "https://app.assembly.marketing",
    "https://app.lol",
    "https://app.lol/123-45623",
    "http://app.assembly.marketing",
    "app.assembly.marketing",
    "app.assembly.marketing/123-43543",
    "https://www.youtube.com/watch?v=Fuli5P5cK5s&t=1s",
  ];

  test.each(validUrls)("should match valid URL: %s", (url) => {
    const matches = url.match(urlRegex);
    expect(matches).toEqual([url]);
  });

  const invalidUrls = [
    "www.foufos",
    "next.js",
    "Next.js",
    "www.foufos-.gr",
    "http://www.foufos",
    "http://foufos",
    "www.mp3#.com",
  ];

  test.each(invalidUrls)("should not match invalid URL: %s", (url) => {
    const matches = url.match(urlRegex);
    expect(matches).toBeNull();
  });

  test('should not match "javascript:" URLs', () => {
    const input = 'Malicious URL: javascript:alert("XSS")';
    const matches = input.match(urlRegex);
    expect(matches).toBeNull();
  });

  test("should not match URLs with invalid TLDs", () => {
    const input = "Invalid TLD: http://example.js";
    const matches = input.match(urlRegex);
    expect(matches).toBeNull();
  });

  test("should not match domains in username mentions", () => {
    const inputs = [
      "@user.name is not a URL",
      "Follow @salty.peaks.collective on social media",
      "Contact @company.name.domain for support",
      "Mentioned @first.last.special-123.com in the post",
    ];

    inputs.forEach((input) => {
      const matches = input.match(urlRegex);
      expect(matches).toBeNull();
    });
  });
});
