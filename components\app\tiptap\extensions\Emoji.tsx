import TiptapEmoji from "@tiptap-pro/extension-emoji";
import { ReactRenderer } from "@tiptap/react";
import { SuggestionKeyDownProps, SuggestionProps } from "@tiptap/suggestion";
import classNames from "classnames";
import Fuse from "fuse.js";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import tippy, { Instance } from "tippy.js";

import emojisData from "emojibase-data/en/data.json";

type EmojiListRef = {
  onKeyDown: (props: SuggestionKeyDownProps) => boolean;
};

type EmojiListProps = {
  items: {
    fallbackImage: string | null;
    emoji: string;
    name: string;
  }[];
  command: (emoji: {
    fallbackImage: string | null;
    emoji: string;
    name: string;
  }) => void;
};

const EmojiList = forwardRef(
  (props: EmojiListProps, ref: React.Ref<EmojiListRef>) => {
    const { items: emojis, command } = props;
    const [selectedIndex, setSelectedIndex] = useState<number>(0);

    const upHandler = () => {
      setSelectedIndex((selectedIndex + emojis.length - 1) % emojis.length);
    };

    const downHandler = () => {
      setSelectedIndex((selectedIndex + 1) % emojis.length);
    };

    const enterHandler = () => {
      const emoji = emojis[selectedIndex];
      if (emoji) {
        console.log(emoji);
        command(emoji);
      }
    };

    useEffect(() => {
      if (emojis.length > 0) {
        setSelectedIndex(selectedIndex % emojis.length);
      }
    }, [emojis.length]);

    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }) => {
        if (event.key === "ArrowUp") {
          upHandler();
          return true;
        }

        if (event.key === "ArrowDown") {
          downHandler();
          return true;
        }

        if (event.key === "Enter") {
          enterHandler();
          return true;
        }

        return false;
      },
    }));

    return (
      <div className="scrollbar-hidden flex max-h-[300px] flex-col items-start gap-1 overflow-y-auto rounded-lg bg-white px-2 py-1 shadow-xl">
        <div className="space-y-4">
          <div className="divide-tan/25 font-body mb-1">
            {emojis.map((emoji, index) => {
              return (
                <button
                  key={emoji.name}
                  className={classNames(
                    "font-body-sm hover:bg-slate flex w-full items-center gap-2 rounded-md px-2 py-1 transition-colors",
                    {
                      "bg-slate": selectedIndex === index,
                    }
                  )}
                  onClick={() => {
                    console.log(emoji);
                    props.command(emoji);
                  }}
                >
                  {emoji.fallbackImage ? (
                    <img
                      className="h-4 w-4"
                      src={emoji.fallbackImage}
                      alt={emoji.name}
                    />
                  ) : (
                    <div className="h-4 w-4">{emoji.emoji}</div>
                  )}
                  :{emoji.name}:
                </button>
              );
            })}
          </div>
        </div>
      </div>
    );
  }
);

const emojis = emojisData.map((emoji) => ({
  ...emoji,
  name: emoji.label.replaceAll(" ", "_"),
  shortcodes: emoji.shortcodes || [emoji.label.replaceAll(" ", "_")],
  emoji: emoji.emoji?.replace(/(\d)([\u20E3])/g, "$1\uFE0F$2").normalize("NFC"),
}));

const fuse = new Fuse(emojis, {
  includeScore: true,
  keys: ["label", "tags"],
  ignoreLocation: true,
});

export const Emoji = TiptapEmoji.configure({
  // Have to force the type since it claims emojis can be undefined
  emojis: emojis as [],
  enableEmoticons: false,
  suggestion: {
    items: ({ editor, query }) => {
      const emoticons = [")", "(", "D", "P", "O", "|", "/"];
      if (emoticons.includes(query)) {
        return [];
      }

      const result = fuse.search(query, { limit: 20 });
      return result.map((result) => result.item);
    },

    allowSpaces: false,

    render: () => {
      let component: ReactRenderer<EmojiListRef, EmojiListProps>;
      let popup: Instance;

      return {
        onStart: (props: SuggestionProps<any, any>) => {
          component = new ReactRenderer<EmojiListRef, EmojiListProps>(
            EmojiList,
            {
              props,
              editor: props.editor,
            }
          );

          if (!props.clientRect) {
            return;
          }

          popup = tippy(document.body, {
            getReferenceClientRect: () => props.clientRect?.() || new DOMRect(),
            appendTo: () => document.body,
            content: component.element,
            showOnCreate: true,
            interactive: true,
            trigger: "manual",
            placement: "bottom-start",
          });
        },

        onUpdate: (props: SuggestionProps<any, any>) => {
          component.updateProps(props);

          if (!props.clientRect) {
            return;
          }

          popup.setProps({
            getReferenceClientRect: () => props.clientRect?.() || new DOMRect(),
          });
        },

        onKeyDown: (props: SuggestionKeyDownProps) => {
          if (props.event.key === "Escape") {
            popup.hide();

            return true;
          }

          return component.ref?.onKeyDown(props) || false;
        },

        onExit: () => {
          popup.destroy();
          component.destroy();
        },
      };
    },
  },
});
