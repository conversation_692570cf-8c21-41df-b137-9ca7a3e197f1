import { Asset } from "@prisma/client";
import getAssetsForDiscord from "apiUtils/getAssetsForDiscord";
import { WebhookClient } from "discord.js";
import { GENERIC_ERROR_MESSAGE } from "~/constants";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";

type Post = {
  id: string;
  discordWebhook: {
    url: string;
  } | null;
  discordContent: {
    id: string;
    copy: string;
    assets: {
      id: string;
      position: number;
      asset: Asset;
    }[];
  } | null;
  title: string;
  workspaceID: string;
};

const publishDiscordPost = async (post: Post): Promise<ScheduledPostResult> => {
  const { discordContent, discordWebhook } = post;

  if (!discordContent) {
    return {
      status: "NonRetriableError",
      error: "No Discord content to post",
    };
  } else if (!discordWebhook) {
    return {
      status: "NonRetriableError",
      error: "No Discord integration",
    };
  }

  const assets = await getAssetsForDiscord(post);

  try {
    const webhookClient = new WebhookClient({ url: discordWebhook.url });

    webhookClient.editMessage;

    const response = await webhookClient.send({
      content: discordContent.copy,
      files: assets.map((asset) => asset.signedUrl),
    });
    return {
      status: "Success",
      postUrl: null,
    };
  } catch (error) {
    console.log(`Discord posting error post:${post.id}: ${error}`);

    let errorMessage = GENERIC_ERROR_MESSAGE;

    try {
      // @ts-expect-error
      errorMessage = error.rawError.message;
    } catch (error) {
      errorMessage = GENERIC_ERROR_MESSAGE;
    }

    return {
      status: "Error",
      error: errorMessage,
    };
  }
};

export default publishDiscordPost;
