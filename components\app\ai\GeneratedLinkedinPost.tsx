import { JSONContent } from "@tiptap/core";
import { useEffect, useState } from "react";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { formatTextAsTipTapEditorState } from "~/utils";
import LinkedInPostPreview from "../posts/linkedin/LinkedInPostPreview";
import LinkedInEditor from "../tiptap/LinkedInEditor";
import ContentContainer from "./ContentContainer";

type AccountOption = {
  id: string;
  name: string;
  profileImageUrl: string;
};

type Props = {
  draftID?: string;
  title: string | null;
  content: string;
  isLoading: boolean;
  navigateOnSuccess: boolean;
  accountOptions: AccountOption[];
};

const GeneratedLinkedInPost = ({
  draftID,
  title,
  content: rawContent,
  isLoading,
  navigateOnSuccess,
  accountOptions,
}: Props) => {
  const [editModalOpen, setEditModalOpen] = useState<boolean>(false);
  const [content, setContent] = useState<{
    copy: string;
    assets: [];
    editorState: JSONContent | null;
    documentTitle: null;
  }>({
    copy: rawContent,
    assets: [],
    editorState: formatTextAsTipTapEditorState(rawContent),
    documentTitle: null,
  });
  const [editedContent, setEditedContent] = useState<{
    copy: string;
    assets: [];
    editorState: JSONContent | null;
    documentTitle: null;
  }>({
    copy: rawContent,
    assets: [],
    editorState: formatTextAsTipTapEditorState(rawContent),
    documentTitle: null,
  });

  useEffect(() => {
    setContent({
      copy: rawContent,
      assets: [],
      editorState: formatTextAsTipTapEditorState(rawContent),
      documentTitle: null,
    });
  }, [rawContent]);

  useEffect(() => {
    setEditedContent(content);
  }, [content]);

  const [integrationID, setIntegrationID] = useState<string | null>(
    accountOptions.length > 0 ? accountOptions[0].id : null
  );
  const integration = accountOptions.find(
    (option) => option.id === integrationID
  );

  return (
    <div>
      <ContentContainer
        title={title}
        channel="LinkedIn"
        draftID={draftID}
        isLoading={isLoading}
        content={[content]}
        navigateOnSuccess={navigateOnSuccess}
        onEditClick={() => {
          setEditModalOpen(true);
        }}
        connectedAccount={
          integration
            ? {
                integrationID: integration.id,
                name: integration.name,
              }
            : null
        }
        accountOptions={accountOptions.map((option) => ({
          value: option.id,
          label: option.name,
        }))}
        setConnectedAccountID={setIntegrationID}
      >
        <LinkedInPostPreview
          linkedInContent={content}
          connectedAccount={
            integration
              ? {
                  id: integration.id,
                  account: {
                    name: integration.name,
                    profileImageUrl: integration.profileImageUrl,
                  },
                }
              : null
          }
          defaultShowMore={true}
        />
      </ContentContainer>
      <Credenza.Root open={editModalOpen} onOpenChange={setEditModalOpen}>
        <Credenza.Header>
          <Credenza.Title>Edit Copy</Credenza.Title>
          <Credenza.Description className="sr-only">
            Edit the copy for the post here
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <LinkedInEditor
            title="Post Content"
            placeholder="Add copy for the post here"
            initialValue={content.editorState}
            onSave={(editorState) => {
              setEditedContent({
                ...editedContent,
                editorState,
              });
            }}
            onUpdateContent={(copy) => {
              setEditedContent({
                ...editedContent,
                copy,
              });
            }}
            roomID={null}
          />
          <Credenza.Footer>
            <Button
              variant="secondary"
              onClick={() => {
                setEditedContent(content);
                setEditModalOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setContent(editedContent);
                setEditModalOpen(false);
              }}
            >
              Save
            </Button>
          </Credenza.Footer>
        </Credenza.Body>
      </Credenza.Root>
    </div>
  );
};

export default GeneratedLinkedInPost;
