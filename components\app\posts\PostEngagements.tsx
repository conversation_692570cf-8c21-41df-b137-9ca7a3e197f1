import { EllipsisVerticalIcon, PlusIcon } from "@heroicons/react/24/outline";
import { Channel, EngagementType } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import { AnimatePresence, motion } from "framer-motion";
import sortBy from "lodash/sortBy";
import { useRouter } from "next/router";
import { ReactNode, useState } from "react";
import { Alert, Select, TextLink, Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { CHANNEL_ICON_MAP } from "~/types";
import { openConfirmationModal } from "~/utils";

type LikesCardProps = {
  likes: Engagement[];
  accountOptions: {
    value: string;
    label: string;
    engagementPermissions?: EngagementType[];
    disabled?: boolean;
    disabledReason?: string | ReactNode;
  }[];
  onLikesEdit: () => void;
  onLikesDelete: () => void;
  isReadOnly?: boolean;
};

const LikesCard = ({
  likes,
  accountOptions,
  onLikesEdit,
  onLikesDelete,
  isReadOnly,
}: LikesCardProps) => {
  return (
    <div className="group border-lightest-gray font-body-sm space-y-1 rounded-md border p-6 pt-5">
      <div className="flex items-center gap-1">
        <div className="font-medium">Likes</div>
        {!isReadOnly && (
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              <div className="hover:bg-lightest-gray/50 text-basic rounded-full bg-inherit p-1 opacity-0 transition-all group-hover:opacity-100">
                <EllipsisVerticalIcon className="h-4 w-4" />
              </div>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Group>
                <DropdownMenu.Item
                  icon="PencilIcon"
                  intent="none"
                  onClick={() => onLikesEdit()}
                >
                  Edit Likes
                </DropdownMenu.Item>
                <DropdownMenu.Item
                  icon="TrashIcon"
                  intent="danger"
                  onClick={() =>
                    openConfirmationModal({
                      title: `Delete Likes?`,
                      body: `This action cannot be undone. Please confirm you want to delete these likes.`,
                      cancelButton: {
                        label: "Cancel, don't delete",
                      },
                      primaryButton: {
                        label: `Delete Likes`,
                        variant: "danger",
                        onClick: () => {
                          onLikesDelete();
                        },
                      },
                    })
                  }
                >
                  Delete Likes
                </DropdownMenu.Item>
              </DropdownMenu.Group>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        )}
      </div>
      <div>
        Auto-like this post from the following accounts within 10 minutes of the
        post time.
      </div>
      <div className="flex items-center gap-1.5">
        <div className="font-body-sm">
          {likes.map((like, index) => {
            return (
              <span key={like.id}>
                <span className="text-secondary">
                  {
                    accountOptions.find(
                      (option) => option.value === like.integrationID
                    )?.label
                  }
                </span>
                {index !== likes.length - 1 && ", "}
              </span>
            );
          })}
        </div>
      </div>
    </div>
  );
};

type EngagementCardProps = {
  engagement: Engagement;
  onEngagementUpdate: (
    engagementID: string,
    engagement:
      | {
          copy: string | null;
          editorState: JSONContent;
        }
      | {
          integrationID: string;
        }
      | {
          postDelaySeconds: number;
        }
  ) => void;
  onEngagementDelete: (engagementID: string) => void;
  accountOptions: {
    value: string;
    label: string;
    engagementPermissions?: EngagementType[];
    disabled?: boolean;
    disabledReason?: string | ReactNode;
  }[];
  Editor: ({
    engagementID,
    initialValue,
    onSave,
    onUpdateContent,
    placeholder,
  }: {
    engagementID: string;
    initialValue: JSONContent | null;
    onSave: (editorState: JSONContent) => void;
    onUpdateContent: (textContent: string) => void;
    placeholder: string;
  }) => JSX.Element;
  accountChooserDisabled?: boolean;
  isReadOnly?: boolean;
};

const EngagementCard = ({
  engagement,
  onEngagementUpdate,
  onEngagementDelete,
  accountOptions,
  Editor,
  accountChooserDisabled = false,
  isReadOnly,
}: EngagementCardProps) => {
  const router = useRouter();
  const [integrationID, setIntegrationID] = useState<string>(
    engagement.integrationID
  );
  const [copy, setCopy] = useState<string | null>(engagement.copy);
  const [postDelaySeconds, setPostDelaySeconds] = useState<number>(
    engagement.postDelaySeconds
  );
  const isComment = engagement.type === "Comment";

  return (
    <div className="group border-lightest-gray font-body-sm space-y-1 rounded-md border p-6 pt-5">
      <div className="flex items-center gap-1">
        <div className="font-medium">
          {isComment ? "First Comment" : "Auto-Repost"}
        </div>
        {!isReadOnly && (
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              <div className="hover:bg-lightest-gray/50 text-basic rounded-full bg-inherit p-1 opacity-0 transition-all group-hover:opacity-100">
                <EllipsisVerticalIcon className="h-4 w-4" />
              </div>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Group>
                <DropdownMenu.Item
                  icon="TrashIcon"
                  intent="danger"
                  onClick={() =>
                    openConfirmationModal({
                      title: `Delete ${isComment ? "Comment" : "Repost"}?`,
                      body: `This action cannot be undone. Please confirm you want to delete this ${
                        isComment ? "comment" : "repost"
                      }.`,
                      cancelButton: {
                        label: "Cancel, don't delete",
                      },
                      primaryButton: {
                        label: `Delete ${isComment ? "Comment" : "Repost"}`,
                        variant: "danger",
                        onClick: () => {
                          onEngagementDelete(engagement.id);
                        },
                      },
                    })
                  }
                >
                  {isComment ? "Delete Comment" : "Delete Repost"}
                </DropdownMenu.Item>
              </DropdownMenu.Group>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        )}
      </div>
      <div className="flex items-center gap-1.5">
        {isComment ? "Auto-post a comment by" : "Auto-repost from"}
        {isReadOnly || accountChooserDisabled ? (
          <div className="font-body-sm text-secondary">
            {
              accountOptions.find((option) => option.value === integrationID)
                ?.label
            }
          </div>
        ) : (
          <Select
            value={integrationID}
            options={accountOptions.filter(
              (option) =>
                option.engagementPermissions == null ||
                option.engagementPermissions.includes(engagement.type)
            )}
            disabled={isReadOnly || accountChooserDisabled}
            onChange={(newIntegrationID) => {
              onEngagementUpdate(engagement.id, {
                integrationID: newIntegrationID,
              });
              setIntegrationID(newIntegrationID);
            }}
            actionButton={{
              icon: "PlusIcon",
              label: "Connect another account",
              onClick: () => {
                router.push("/settings/profiles");
              },
            }}
          />
        )}
        <Select
          value={postDelaySeconds}
          disabled={isReadOnly}
          hideSelectedOption={true}
          options={[
            {
              value: 0,
              label: "immediately",
            },
            {
              value: 5 * 60,
              label: "5 minutes",
            },
            {
              value: 10 * 60,
              label: "10 minutes",
            },
            {
              value: 30 * 60,
              label: "30 minutes",
            },
            {
              value: 60 * 60,
              label: "1 hour",
            },
            {
              value: 4 * 60 * 60,
              label: "4 hours",
            },
            {
              value: 8 * 60 * 60,
              label: "8 hours",
            },
            {
              value: 12 * 60 * 60,
              label: "12 hours",
            },
            {
              value: 24 * 60 * 60,
              label: "24 hours",
            },
          ]}
          onChange={(delay) => {
            onEngagementUpdate(engagement.id, {
              postDelaySeconds: delay,
            });
            setPostDelaySeconds(delay);
          }}
        />
        <span>after the post</span>
      </div>
      <div className="border-lightest-gray border-l-2 pl-2">
        {Editor({
          engagementID: engagement.id,
          initialValue: engagement.editorState,
          onSave: (editorState) => {
            onEngagementUpdate(engagement.id, {
              copy,
              editorState,
            });
          },
          onUpdateContent: (textContent) => {
            setCopy(textContent);
          },
          placeholder: isComment
            ? "Write the content of your comment here..."
            : "(Optional) Write a caption for your repost here...",
        })}
      </div>
    </div>
  );
};

type Engagement = {
  id: string;
  type: EngagementType;
  copy: string | null;
  editorState: JSONContent | null;
  integrationID: string;
  postDelaySeconds: number;
  createdAt: Date;
};

type Props = {
  engagements?: Engagement[];
  onEngagementCreate: (engagementType: EngagementType) => void;
  onEngagementUpdate: (
    engagementID: string,
    engagement:
      | {
          copy: string | null;
          editorState: JSONContent;
        }
      | {
          integrationID: string;
        }
      | {
          postDelaySeconds: number;
        }
  ) => void;
  onEngagementDelete: (engagementID: string) => void;
  disableReposts?: boolean;
  enableLikes?: boolean;
  accountOptions: {
    value: string;
    label: string;
    engagementPermissions?: EngagementType[];
  }[];
  Editor: ({
    engagementID,
    initialValue,
    onSave,
    onUpdateContent,
    placeholder,
  }: {
    engagementID: string;
    initialValue: JSONContent | null;
    onSave: (editorState: JSONContent) => void;
    onUpdateContent: (textContent: string) => void;
    placeholder: string;
  }) => JSX.Element;
  channel: Channel;
  accountChooserDisabled?: boolean;
  isReadOnly?: boolean;
  infoBanner?: {
    title: string;
    message: string | ReactNode;
  } | null;
};

const PostEngagements = ({
  engagements = [],
  onEngagementCreate,
  onEngagementUpdate,
  onEngagementDelete,
  disableReposts = false,
  enableLikes,
  accountOptions,
  Editor,
  channel,
  accountChooserDisabled,
  isReadOnly,
  infoBanner,
}: Props) => {
  const router = useRouter();

  const likes = engagements.filter((engagement) => engagement.type === "Like");
  const otherEngagements = engagements.filter(
    (engagement) => engagement.type !== "Like"
  );

  const hasSchedulingProfile = accountOptions.some(
    (option) =>
      option.engagementPermissions == null ||
      (option.engagementPermissions.includes("Like") &&
        option.engagementPermissions.includes("Comment") &&
        option.engagementPermissions.includes("Repost"))
  );

  return (
    <div>
      <div className="font-body pb-1 font-medium">Post Engagement</div>
      <div className="flex flex-col gap-2">
        <AnimatePresence initial={false}>
          {engagements.length === 0 && (
            <motion.div
              key="engagemnt-description"
              className="font-body-sm text-medium-dark-gray"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 100 }}
              exit={{ height: 0, opacity: 0 }}
            >
              Boost your post engagement by scheduling a comment or reposting
              from another account.
            </motion.div>
          )}
          {!hasSchedulingProfile ? (
            <motion.div
              key="no-accounts"
              className="font-body-sm text-medium-dark-gray"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 100 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <TextLink
                href="/settings/profiles"
                value={`Connect a ${CHANNEL_ICON_MAP[channel].label} scheduling profile`}
              />{" "}
              to Assembly in order to use auto-engagement features.
            </motion.div>
          ) : (
            <>
              {sortBy(otherEngagements, "createdAt").map((engagement) => {
                return (
                  <motion.div
                    key={engagement.id}
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 100 }}
                    exit={{ height: 0, opacity: 0 }}
                  >
                    <EngagementCard
                      engagement={engagement}
                      onEngagementUpdate={onEngagementUpdate}
                      onEngagementDelete={onEngagementDelete}
                      accountOptions={accountOptions}
                      Editor={Editor}
                      accountChooserDisabled={accountChooserDisabled}
                      isReadOnly={isReadOnly}
                    />
                  </motion.div>
                );
              })}
              {likes.length > 0 && (
                <motion.div
                  key="likes-card"
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 100 }}
                  exit={{ height: 0, opacity: 0 }}
                >
                  <LikesCard
                    likes={likes}
                    accountOptions={accountOptions.filter(
                      (option) =>
                        option.engagementPermissions == null ||
                        option.engagementPermissions.includes("Like")
                    )}
                    onLikesEdit={() => {
                      onEngagementCreate("Like");
                    }}
                    onLikesDelete={() => {
                      onEngagementDelete(likes[0].id);
                    }}
                  />
                </motion.div>
              )}
              {!isReadOnly && (
                <>
                  {!!infoBanner ? (
                    <Alert
                      intent="none"
                      title={infoBanner.title}
                      message={infoBanner.message}
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => {
                          onEngagementCreate("Comment");
                        }}
                        variant="ghost"
                        size="sm"
                      >
                        <PlusIcon className="h-4 w-4" />
                        First Comment
                      </Button>
                      {!disableReposts && (
                        <Button
                          onClick={() => {
                            onEngagementCreate("Repost");
                          }}
                          variant="ghost"
                          size="sm"
                        >
                          <PlusIcon className="h-4 w-4" />
                          Auto-Repost
                        </Button>
                      )}
                      {enableLikes != null && (
                        <Tooltip
                          value={
                            <>
                              {enableLikes === false ? (
                                <div>
                                  <div className="font-body-sm mb-1.5">
                                    Auto-likes are only available on{" "}
                                    <span className="font-medium">
                                      Standard
                                    </span>{" "}
                                    workspace plans and above.
                                  </div>
                                  <Button
                                    variant="secondary"
                                    size="sm"
                                    onClick={() => {
                                      router.push("/settings/pricing");
                                    }}
                                  >
                                    See Plans
                                  </Button>
                                </div>
                              ) : likes.length > 0 ? (
                                "You can only add one set of auto-likes per post. Please edit the existing “Likes” section to make any changes."
                              ) : null}
                            </>
                          }
                        >
                          <Button
                            onClick={() => {
                              onEngagementCreate("Like");
                            }}
                            disabled={likes.length > 0 || enableLikes === false}
                            variant="ghost"
                            size="sm"
                          >
                            <PlusIcon className="h-4 w-4" />
                            Likes
                          </Button>
                        </Tooltip>
                      )}
                    </div>
                  )}
                </>
              )}
            </>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default PostEngagements;
