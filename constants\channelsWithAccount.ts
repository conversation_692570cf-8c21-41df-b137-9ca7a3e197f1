import { Channel } from "@prisma/client";

type AccountConfig = {
  integrationField: string;
};

const CHANNELS_WITH_ACCOUNT: Partial<Record<Channel, AccountConfig>> = {
  Facebook: {
    integrationField: "facebookIntegrationID",
  },
  Instagram: {
    integrationField: "instagramIntegrationID",
  },
  LinkedIn: {
    integrationField: "linkedInIntegrationID",
  },
  Twitter: {
    integrationField: "twitterIntegrationID",
  },
  TikTok: {
    integrationField: "tikTokIntegrationID",
  },
  Threads: {
    integrationField: "threadsIntegrationID",
  },
  YouTubeShorts: {
    integrationField: "youTubeIntegrationID",
  },
  Slack: {
    integrationField: "slackIntegrationID",
  },
  Discord: {
    integrationField: "discordWebhookID",
  },
  Webflow: {
    integrationField: "webflowIntegrationID",
  },
};

export default CHANNELS_WITH_ACCOUNT;
