import { AnimatePresence, MotionConfig, motion } from "framer-motion";
import Image from "next/image";
import { ReactNode } from "react";
import TextLink from "~/components/TextLink";

const OnboardingCardContainer = ({ children }: { children: ReactNode }) => {
  return (
    <MotionConfig transition={{ duration: 0.5, type: "spring", bounce: 0 }}>
      <div className="relative mx-auto h-5/6 overflow-clip bg-white px-4 md:min-h-[725px] md:w-[525px] md:rounded-2xl md:px-[60px] md:py-14 md:drop-shadow-sm">
        <AnimatePresence mode="popLayout" initial={false}>
          {children}
        </AnimatePresence>
      </div>
    </MotionConfig>
  );
};

type OnboardingCardProps = {
  title: string;
  description?: string | ReactNode;
  children: ReactNode;
  showTermsAndConditions?: boolean;
  direction?: -1 | 1;
};

const OnboardingCard = ({
  title,
  description,
  children,
  showTermsAndConditions = false,
  direction = 1,
}: OnboardingCardProps) => {
  const variants = {
    initial: (direction: -1 | 1) => {
      return { x: `${110 * direction}%`, opacity: 0 };
    },
    active: { x: "0%", opacity: 1 },
    exit: (direction: -1 | 1) => {
      return { x: `${-110 * direction}%`, opacity: 0 };
    },
  };

  return (
    <div className="h-full w-full">
      <motion.div
        key={title}
        variants={variants}
        initial="initial"
        animate="active"
        exit="exit"
        custom={direction}
        className="h-full w-full"
      >
        <div className="h-full w-full space-y-8">
          <Image
            src="/logos/assembly_logo.svg"
            alt="Assembly Logo"
            width={65}
            height={73}
            className="mx-auto"
          />
          <div>
            <div className="h3 md:h1 mt-6 px-2 text-center md:px-0">
              {title}
            </div>
            {description && (
              <div className="text-medium-dark-gray mt-2 text-center">
                {description}
              </div>
            )}
          </div>
          {children}
        </div>
      </motion.div>
      {showTermsAndConditions && (
        <div className="font-body-sm text-medium-gray w-full text-center md:absolute md:bottom-7 md:left-1/2 md:-translate-x-1/2">
          You acknowledge that you read, and agree to our{" "}
          <div>
            <TextLink
              href="https://www.meetassembly.com/terms-of-service"
              value="Terms of Service"
            />{" "}
            and{" "}
            <TextLink
              href="https://www.meetassembly.com/privacy-policy"
              value="Privacy Policy"
            />
            .
          </div>
        </div>
      )}
    </div>
  );
};

const Container = OnboardingCardContainer;
const Content = OnboardingCard;

export { Container, Content };
