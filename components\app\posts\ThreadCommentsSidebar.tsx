import { XMarkIcon } from "@heroicons/react/24/outline";
import { CommentData, ThreadData } from "@liveblocks/node";
import { RoomProvider, useThreads } from "@liveblocks/react";
import { Comment, Composer } from "@liveblocks/react-ui";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { useState } from "react";
import Separator from "~/components/ui/Separator";
import Switch from "~/components/ui/Switch";
import { useThreadComments } from "~/providers";
import getThreadCommentsOriginalText from "~/utils/liveblocks/getThreadCommentsOriginalText";

type ProcessedThread = ThreadData & {
  originalText: string | null;
  comments: CommentData[];
};

const SingleThreadComments = ({ thread }: { thread: ProcessedThread }) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [composer<PERSON><PERSON>, setComposerOpen] = useState<boolean>(false);

  const comments = thread.comments;
  const MIN_COMMENTS_TO_SHOW = 5;
  const LAST_COMMENTS_TO_SHOW = 2;

  const renderComments = () => {
    if (comments.length <= MIN_COMMENTS_TO_SHOW || isExpanded) {
      return comments.map((comment, index) => (
        <div key={comment.id} className="relative">
          <Comment comment={comment} />
          {index < comments.length - 1 && (
            <div
              className="bg-lightest-gray absolute top-6 left-2.5 z-10 w-px"
              style={{
                height: "calc(100% - 12px)",
              }}
            />
          )}
        </div>
      ));
    }

    const firstComment = comments[0];
    const lastComments = comments.slice(
      comments.length - LAST_COMMENTS_TO_SHOW
    );
    const hiddenCount = comments.length - 1 - LAST_COMMENTS_TO_SHOW;

    return (
      <>
        <div key={firstComment.id} className="relative">
          <Comment comment={firstComment} />
          <div
            className="bg-lightest-gray absolute top-6 left-2.5 z-10 w-px"
            style={{
              height: "calc(100% + 36px)",
            }}
          />
        </div>

        <div className="py-2">
          <button
            onClick={() => setIsExpanded(true)}
            className="text-medium-dark-gray hover:text-basic font-body-sm ml-7 transition-colors"
          >
            Show {hiddenCount} replies
          </button>
        </div>

        {lastComments.map((comment, index) => (
          <div key={comment.id} className="relative">
            <Comment comment={comment} />
            {index < lastComments.length - 1 && (
              <div
                className="bg-lightest-gray absolute top-6 left-2.5 z-10 w-px"
                style={{
                  height: "calc(100% - 12px)",
                }}
              />
            )}
          </div>
        ))}
      </>
    );
  };

  return (
    <div className="font-body-sm">
      {thread.originalText ? (
        <div className="border-marigold mb-3 max-w-[247px] border-l pl-2.5">
          <span className="text-medium-dark-gray two-line-ellipses italic">
            {thread.originalText}
          </span>
        </div>
      ) : (
        <div className="border-medium-gray mb-3 border-l pl-2.5">
          <span className="text-medium-gray italic">Original text deleted</span>
        </div>
      )}

      <div className="flex flex-col gap-2.5">{renderComments()}</div>

      <Separator
        className={classNames("mt-3", {
          "mb-2": !composerOpen,
          "mb-3": composerOpen,
        })}
      />

      <Composer
        threadId={thread.id}
        collapsed={!composerOpen}
        onCollapsedChange={(collapsed) => setComposerOpen(!collapsed)}
        onComposerSubmit={() => {
          setComposerOpen(false);
        }}
        overrides={{ COMPOSER_PLACEHOLDER: "Write a reply..." }}
      />
    </div>
  );
};

const ThreadCommentsList = ({
  roomID,
  showResolved,
}: {
  roomID: string;
  showResolved: boolean;
}) => {
  const { getEditorForRoom } = useThreadComments();
  const currentEditor = getEditorForRoom(roomID);

  const { threads, isLoading } = useThreads({
    query: { resolved: showResolved },
  });

  if (isLoading) {
    return null;
  }

  if (!threads || !currentEditor || threads.length === 0) {
    return (
      <div className="w-full">
        <p className="font-body-sm text-medium-dark-gray">
          {showResolved ? "No resolved comments." : "No comments found."}
        </p>
      </div>
    );
  }

  const processedThreads = threads.map((thread) => ({
    ...thread,
    originalText: getThreadCommentsOriginalText(currentEditor, thread.id),
    comments: thread.comments.filter((comment) => !comment.deletedAt),
  }));

  return (
    <div className="flex w-full flex-col gap-7">
      {processedThreads.map((thread) => (
        <SingleThreadComments key={thread.id} thread={thread} />
      ))}
    </div>
  );
};

const ThreadCommentsSidebar = () => {
  const { roomIDs, toggleThreadCommentsSidebar } = useThreadComments();
  const [showResolved, setShowResolved] = useState<boolean>(false);

  if (!roomIDs || roomIDs.length === 0) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{
          height: 0,
          opacity: 0,
        }}
        animate={{
          height: "auto",
          opacity: 1,
        }}
        exit={{
          height: 0,
          opacity: 0,
        }}
        className="threads-sidebar w-full px-6 py-6"
      >
        <div className="font-body-sm flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="font-medium">Comments & Suggestions</span>
            <XMarkIcon
              className="text-medium-dark-gray hover:text-basic h-[18px] w-[18px] cursor-pointer transition-colors"
              onClick={() => {
                toggleThreadCommentsSidebar();
              }}
            />
          </div>
          <div className="flex items-center gap-2">
            <Switch checked={showResolved} onCheckedChange={setShowResolved} />
            <span className="text-medium-dark-gray">
              Show resolved comments
            </span>
          </div>
        </div>
        <div className="flex w-full flex-col gap-7 py-9">
          {roomIDs.map((roomID) => (
            <RoomProvider key={roomID} id={roomID}>
              <ThreadCommentsList roomID={roomID} showResolved={showResolved} />
            </RoomProvider>
          ))}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ThreadCommentsSidebar;
