import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Bold from "@tiptap/extension-bold";
import BulletList from "@tiptap/extension-bullet-list";
import Code from "@tiptap/extension-code";
import Document from "@tiptap/extension-document";
import History from "@tiptap/extension-history";
import Italic from "@tiptap/extension-italic";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { ClipboardEvent, useEffect, useRef, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import Turndown from "turndown";
import { Divider } from "~/components";
import { usePostIntegrations, useThreadComments } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { stripLiveblocksMarks } from "~/utils";
import ThreadCommentsPopover from "../posts/ThreadCommentsPopover";
import {
  AutoMention,
  CharacterCount,
  CustomCommentThreadMark,
  Emoji,
  Link,
  PasteHandler,
} from "./extensions";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import FloatingToolbar from "./ui/FloatingToolbar";
import TextFormatButtons from "./ui/TextFormatButtons";
import TextStyleButtons from "./ui/TextStyleButtons";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  isEditable?: boolean;
};

const createBaseExtensions = (placeholder: string) => [
  Document,
  Paragraph,
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  Text,
  History,
  BulletList,
  OrderedList,
  ListItem,
  Bold,
  Italic,
  Code,
  Link.configure({
    openOnClick: true,
  }),
  Emoji,
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  CharacterCount.configure({
    limit: 2000,
  }),
  // User, channel, and here mentions
  AutoMention.configure({
    baseUrl: null,
    regex: /(?:<@[A-Z0-9]+>|<#[A-Z0-9]+>|(?<=^|\s)@(?:here|everyone|channel))/g,
  }),
  PasteHandler,
];

const createTurndownInstance = () => {
  /** Markdown Rules - https://api.slack.com/reference/surfaces/formatting#visual-styles
   * 1. Bold: *bold*
   * 2. Italic: _italic_
   * 3. Strikethrough: ~strikethrough~
   * 4. Code: `code`
   * 5. Link: <https://www.example.com|link text>
   * 6. Blockquote: > blockquote
   * 7. List: - list item (not really supported)
   */

  const turndown = new Turndown({
    emDelimiter: "_",
    // @ts-expect-error - I do actually want a single star
    strongDelimiter: "*",
    bulletListMarker: "-",
    blankReplacement: (content, node) => {
      if (node.nodeName === "P") {
        return "[NEWLINE]\n";
      } else {
        return "[LISTITEM]";
      }
    },
  });

  turndown.addRule("paragraph", {
    filter: "p",
    replacement: (content) => {
      return content + "\n";
    },
  });

  turndown.addRule("list", {
    filter: ["ul", "ol"],
    replacement: function (content, node) {
      return content + "\n"; // Remove extra newlines from the front
    },
  });

  turndown.addRule("listItem", {
    filter: "li",
    replacement: function (content, node, options) {
      content = content
        .replace(/^\n+/, "") // Remove leading newlines
        .replace(/\n+$/, ""); // Remove trailing newlines

      const prefix = options.bulletListMarker + " ";
      const parent = node.parentNode;
      if (!parent) {
        return content;
      }
      const index = Array.prototype.indexOf.call(parent.children, node);
      const prefix_actual =
        parent.nodeName === "OL" ? `${index + 1}. ` : prefix;

      return prefix_actual + content + (node.nextSibling ? "\n" : "");
    },
  });

  return turndown;
};

const processMarkdown = (html: string, turndown: Turndown) => {
  return turndown
    .turndown(html)
    .replaceAll("[NEWLINE]\n", "\n")
    .replaceAll("[LISTITEM]", "")
    .replaceAll("@here", "<!here>")
    .replaceAll("@everyone", "<!everyone>")
    .replaceAll("@channel", "<!channel>")
    .replace(/(?<=^|\n)\s*- /gm, (match) => match.replace("- ", "• "))
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, "<$2|$1>"); // Convert markdown links to Slack format
};

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const SlackEditorFallback = ({
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  isEditable = false,
  isReadOnly = false,
}: BaseProps) => {
  const cleanContent = stripLiveblocksMarks(initialValue);

  const [editorState, setEditorState] = useState<JSONContent | null>(
    cleanContent
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(cleanContent) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const turndown = createTurndownInstance();

  const editor = useEditor({
    extensions: createBaseExtensions(placeholder),
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const markdown = processMarkdown(html, turndown);

      onUpdateContent(markdown);

      const json = editor.getJSON();
      setEditorState(json);
    },
    immediatelyRender: false,
    content: cleanContent,
    editable: isEditable && !isReadOnly,
  });

  if (!editor) return null;

  return isEditable && !isReadOnly ? (
    <>
      <FloatingToolbar editor={editor}>
        {({ onHide }) => (
          <>
            <TextFormatButtons
              editor={editor}
              onHide={onHide}
              features={{ headings: [] }}
            />
            <Divider orientation="vertical" />
            <TextStyleButtons editor={editor} features={{ underline: false }} />
          </>
        )}
      </FloatingToolbar>
      <EditorContent editor={editor} />
    </>
  ) : (
    <EditorContent editor={editor} />
  );
};

const SlackEditor = ({
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  roomID,
}: BaseProps & { roomID: string }) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );
  const { setEditorForRoom } = useThreadComments();
  const { currentChannel } = usePostIntegrations();

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const turndown = createTurndownInstance();

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(placeholder),
    ],
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const markdown = processMarkdown(html, turndown);

      onUpdateContent(markdown);

      const json = editor.getJSON();
      setEditorState(json);
    },
    immediatelyRender: false,
    editable: !isReadOnly,
  });

  const isEditorReady = useIsEditorReady();

  useEffect(() => {
    if (editor && currentChannel === "Slack") {
      setEditorForRoom(editor, roomID);
    }

    return () => {
      setEditorForRoom(null, roomID);
    };
  }, [editor, currentChannel, setEditorForRoom, roomID]);

  if (!editor) return null;

  return (
    <div
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor}>
        <ToolbarActions
          editor={editor}
          features={{
            bulletList: true,
            orderedList: true,
            link: true,
          }}
        />
      </CustomFloatingToolbar>
      {isEditorReady ? (
        <div className="flex w-full">
          <EditorContent editor={editor} className="w-full" />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["Slack"].label,
            }}
          />
        </div>
      ) : (
        <SlackEditorFallback
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
        />
      )}
    </div>
  );
};

const SlackEditorWrapperNewDesign = ({
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  roomID,
}: BaseProps & {
  roomID: string | null;
}) => {
  const { currentChannel } = usePostIntegrations();
  const threadCommentsContainerRef = useRef<HTMLDivElement>(null);

  if (!roomID) {
    return (
      <SlackEditorFallback
        initialValue={initialValue}
        placeholder={placeholder}
        onSave={onSave}
        onUpdateContent={onUpdateContent}
        isReadOnly={isReadOnly}
        isEditable={!isReadOnly}
      />
    );
  }

  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense fallback={null}>
        <div className="relative w-full">
          <SlackEditor
            initialValue={initialValue}
            placeholder={placeholder}
            onSave={onSave}
            onUpdateContent={onUpdateContent}
            onImagePaste={onImagePaste}
            isReadOnly={isReadOnly}
            roomID={roomID}
          />
          <div ref={threadCommentsContainerRef} />
        </div>
      </ClientSideSuspense>
      {currentChannel === "Slack" && (
        <ThreadCommentsPopover
          key={roomID}
          roomID={roomID}
          containerRef={threadCommentsContainerRef}
        />
      )}
    </RoomProvider>
  );
};

export default SlackEditorWrapperNewDesign;
