import axios, { AxiosInstance } from "axios";
import { DateTime } from "luxon";
import { createSuccessResponse, Response } from "../Response";
import catchFacebookError from "./catchFacebookError";
import Facebook from "./Facebook";

class Graph {
  BASE_URL = "https://graph.facebook.com/v20.0";

  accessToken: string;

  client: AxiosInstance;

  protected _facebook?: Facebook;

  constructor(accessToken: string) {
    this.client = axios.create({
      baseURL: this.BASE_URL,
    });
    this.accessToken = accessToken;
  }

  public get FB() {
    if (this._facebook) {
      return this._facebook;
    } else {
      return (this._facebook = new Facebook(this.accessToken));
    }
  }

  // Takes a short-lived access token and returns a long-lived access token
  // https://developers.facebook.com/docs/facebook-login/guides/access-tokens/get-long-lived#get-a-long-lived-user-access-token
  getOauthToken = async (): Promise<
    Response<{
      accessToken: string;
      tokenType: string;
    }>
  > => {
    const params = new URLSearchParams({
      grant_type: "fb_exchange_token",
      client_id: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID as string,
      client_secret: process.env.FACEBOOK_APP_SECRET as string,
      fb_exchange_token: this.accessToken,
    });

    try {
      const { status, data } = await this.client.get(
        `/oauth/access_token?${params}`
      );

      const { access_token, token_type } = data;

      return createSuccessResponse(
        {
          accessToken: access_token,
          tokenType: token_type,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/tools/explorer/?method=GET&path=me%3Ffields%3Did%2Cname%2Cpicture%2Cpermissions%2Caccounts%7Bid%2Cname%2Caccess_token%2Ccategory%2Cpicture%2Cinstagram_business_account%7Bid%2Cname%2Cusername%2Cprofile_picture_url%7D%7D&version=v17.0
  getCurrentUser = async (): Promise<
    Response<{
      id: string;
      name: string;
      profileImageUrl: string;
      scope: string[];
      accounts: {
        id: string;
        name: string;
        username: string | null;
        category: string | null;
        accessToken: string;
        profileImageUrl: string | null;
        profileImageExpiresAt: Date | null;
        instagramAccount: {
          id: string;
          facebookPageID: string;
          name: string | null;
          username: string;
          profileImageUrl: string | null;
          profileImageExpiresAt: Date | null;
        } | null;
      }[];
    }>
  > => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      fields: [
        "id",
        "name",
        "picture",
        "permissions",
        `accounts.limit(100){${[
          "id",
          "name",
          "access_token",
          "category",
          "username",
          "picture",
          `instagram_business_account{${[
            "id",
            "name",
            "username",
            "profile_picture_url",
          ].join(",")}}`,
        ].join(",")}}`,
      ].join(","),
    });

    try {
      const { status, data } = await this.client.get(`/me?${params}`);

      const { id, name, picture } = data;
      const permissions: {
        data: {
          permission: string;
          status: "granted" | "declined";
        }[];
      } = data.permissions;

      const profileImageUrl = picture.data.url;
      const scope = permissions.data
        .filter((permission) => permission.status === "granted")
        .map((permission) => permission.permission);

      const rawAccounts = (data.accounts?.data || []) as {
        id: string;
        name: string;
        username?: string;
        category: string;
        access_token: string;
        picture: {
          data: {
            url: string;
          };
        };
        instagram_business_account?: {
          id: string;
          name: string | null;
          username: string;
          profile_picture_url: string | null;
        };
      }[];

      const accounts = rawAccounts.map((account) => ({
        id: account.id,
        name: account.name,
        username: account.username || null,
        category: account.category || null,
        profileImageUrl: account.picture.data.url || null,
        profileImageExpiresAt: data.picture.data.url
          ? DateTime.now().plus({ days: 3 }).toJSDate()
          : null,
        accessToken: account.access_token,
        instagramAccount: account.instagram_business_account
          ? {
              id: account.instagram_business_account.id,
              facebookPageID: account.id,
              name: account.instagram_business_account.name || null,
              username: account.instagram_business_account.username,
              profileImageUrl:
                account.instagram_business_account.profile_picture_url || null,
              profileImageExpiresAt: account.instagram_business_account
                .profile_picture_url
                ? DateTime.now().plus({ days: 3 }).toJSDate()
                : null,
            }
          : null,
      }));
      const meData = {
        id,
        name,
        profileImageUrl,
        scope,
        accounts,
      };

      return createSuccessResponse(meData, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  getAccounts = async (): Promise<
    Response<
      {
        id: string;
        name: string;
        accessToken: string;
        category: string;
        instagramAccountID: string | null;
      }[]
    >
  > => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      fields: [
        "id",
        "name",
        "category",
        "picture",
        "access_token",
        "instagram_business_account",
      ].join(","),
    });

    try {
      const { status, data } = await this.client.get(`/me/accounts?${params}`);

      const rawAccounts = data.data as {
        id: string;
        name: string;
        category: string;
        access_token: string;
        picture: {
          data: {
            url: string;
          };
        };
        instagram_business_account?: {
          id: string;
        };
      }[];
      const accounts = rawAccounts.map(
        (account: {
          id: string;
          name: string;
          category: string;
          access_token: string;
          picture: {
            data: {
              url: string;
            };
          };
          instagram_business_account?: {
            id: string;
          };
        }) => ({
          id: account.id,
          name: account.name,
          category: account.category,
          accessToken: account.access_token,
          profileImageUrl: account.picture.data.url,
          instagramAccountID: account.instagram_business_account?.id || null,
        })
      );

      return createSuccessResponse(accounts, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };
}

export default Graph;
