import {
  CheckCircleIcon,
  EllipsisHorizontalIcon,
  PlusIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { FC, useEffect, useState } from "react";
import { UseFormReturn, useForm } from "react-hook-form";
import { toast } from "sonner";
import Label from "~/components/Label";
import TextLink from "~/components/TextLink";
import { FormTextInput } from "~/components/form";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { useWindowTitle } from "~/hooks";
import { handleTrpcError, trpc } from "~/utils";
import openConfirmationModal from "~/utils/openConfirmationModal";
import SettingsLayout from "../settings/SettingsLayout";
import SocialProfiles from "../settings/profiles/SocialProfiles";

const LinkedInLeadsIntegrations: FC = () => {
  useWindowTitle("LinkedIn Leads Integrations");

  const [selectedIntegration, setSelectedIntegration] =
    useState<Integration | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [modalType, setModalType] = useState<"clay" | "custom" | null>(null);

  const connectedIntegrationsQuery =
    trpc.leads.linkedIn.getWorkspaceIntegrations.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });

  type Integration = NonNullable<
    typeof connectedIntegrationsQuery.data
  >["integrations"][number];

  const scrapingIntegrations: Integration[] =
    connectedIntegrationsQuery.data?.integrations.filter(
      (
        integration: Integration
      ): integration is Integration & {
        isLeadsScrapingEnabled: true;
      } => integration.isLeadsScrapingEnabled
    ) || [];

  const customEnabledIntegrations: Integration[] =
    connectedIntegrationsQuery.data?.integrations.filter(
      (
        integration: Integration
      ): integration is Integration & {
        isLeadsScrapingEnabled: true;
      } => integration.isLeadsScrapingEnabled
    ) || [];

  const trpcUtils = trpc.useUtils();

  const deleteClayWebhookMutation =
    trpc.leads.linkedIn.deleteClayWebhook.useMutation({
      onSuccess: () => {
        trpcUtils.leads.linkedIn.getWorkspaceIntegrations.refetch();
      },
    });

  const deleteCustomWebhookMutation =
    trpc.leads.linkedIn.deleteCustomWebhook.useMutation({
      onSuccess: () => {
        trpcUtils.leads.linkedIn.getWorkspaceIntegrations.refetch();
      },
    });

  const sendTestWebhookMutation =
    trpc.leads.linkedIn.sendWebhookTestEvent.useMutation();

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/linkedin-leads">
              LinkedIn Leads
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Integrations</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      <SettingsLayout.Header>
        <SettingsLayout.Title>LinkedIn Leads Integrations</SettingsLayout.Title>
        <SettingsLayout.Description>
          Connect your LinkedIn leads data to external sources.
        </SettingsLayout.Description>
      </SettingsLayout.Header>
      <SettingsLayout.Sections>
        <SettingsLayout.Section>
          <SettingsLayout.SectionTitle>Clay.com</SettingsLayout.SectionTitle>
          <SettingsLayout.SectionDescription>
            Automatically send LinkedIn leads from Assembly to Clay.com. View
            instructions for generating and adding the webhook{" "}
            <TextLink
              href="https://www.clay.com/university/guide/webhook-integration-guide"
              value="here"
            />
            .
          </SettingsLayout.SectionDescription>
          <SocialProfiles.Root
            isLoading={connectedIntegrationsQuery.isLoading}
            isEmpty={scrapingIntegrations.length === 0}
          >
            {scrapingIntegrations.map((integration) => {
              const { clayWebhook } = integration;
              const hasWebhook = clayWebhook != null;

              return (
                <SocialProfiles.Profile key={integration.id}>
                  <SocialProfiles.ProfileDetails
                    profile={{
                      id: integration.id,
                      name: integration.account.name,
                      detail: integration.account.vanityName,
                      channel: "LinkedIn",
                      profileImageUrl: integration.account.profileImageUrl,
                    }}
                  />
                  <div className="flex items-center gap-4">
                    {hasWebhook && (
                      <div className="text-success font-body-sm flex items-center gap-0.5">
                        <CheckCircleIcon className="h-4 w-4" />
                        <div className="font-medium">Connected</div>
                      </div>
                    )}
                    {hasWebhook ? (
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <div className="hover:bg-slate rounded-full p-0.5 transition-colors">
                            <EllipsisHorizontalIcon className="h-5 w-5" />
                          </div>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content>
                          <DropdownMenu.Item
                            icon="PencilSquareIcon"
                            onClick={() => {
                              setSelectedIntegration(integration);
                              setIsModalOpen(true);
                              setModalType("clay");
                            }}
                          >
                            Edit Webhook
                          </DropdownMenu.Item>
                          <DropdownMenu.Item
                            icon="PaperAirplaneIcon"
                            onClick={() => {
                              toast.promise(
                                sendTestWebhookMutation.mutateAsync({
                                  webhookID: clayWebhook!.id,
                                }),
                                {
                                  loading: "Sending test event",
                                  success: (data) => {
                                    return {
                                      message:
                                        "Test event received successfully",
                                      description:
                                        "Successfully received test event",
                                    };
                                  },
                                  error: (error: unknown) => ({
                                    message: "Error sending test event",
                                    description: (error as Error).message,
                                  }),
                                }
                              );
                            }}
                          >
                            Send Test Event
                          </DropdownMenu.Item>
                          <DropdownMenu.Item
                            icon="TrashIcon"
                            intent="danger"
                            onClick={() => {
                              openConfirmationModal({
                                title: `Delete Clay webhook for ${integration.account.name}?`,
                                body: "Leads will no longer be sent to Clay from this integration. This action cannot be undone.",
                                primaryButton: {
                                  label: "Delete Webhook",
                                  variant: "danger",
                                  onClick: () => {
                                    toast.promise(
                                      deleteClayWebhookMutation.mutateAsync({
                                        integrationID: integration.id,
                                      }),
                                      {
                                        loading: "Deleting webhook",
                                        success: {
                                          message: "Webhook deleted",
                                          description:
                                            "Successfully deleted Clay webhook",
                                        },
                                        error: (error: unknown) => ({
                                          message: "Failed to delete webhook",
                                          description: (error as Error).message,
                                        }),
                                      }
                                    );
                                  },
                                },
                              });
                            }}
                          >
                            Delete Webhook
                          </DropdownMenu.Item>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    ) : (
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => {
                          setSelectedIntegration(integration);
                          setIsModalOpen(true);
                          setModalType("clay");
                        }}
                      >
                        Add Webhook
                      </Button>
                    )}
                  </div>
                </SocialProfiles.Profile>
              );
            })}
          </SocialProfiles.Root>
        </SettingsLayout.Section>
        <SettingsLayout.Section>
          <SettingsLayout.SectionTitle>
            Custom Webhooks
          </SettingsLayout.SectionTitle>
          <SettingsLayout.SectionDescription>
            Send LinkedIn leads to your own webhook endpoint for further
            processing.
          </SettingsLayout.SectionDescription>
          <SocialProfiles.Root
            isLoading={connectedIntegrationsQuery.isLoading}
            isEmpty={customEnabledIntegrations.length === 0}
          >
            {customEnabledIntegrations.map((integration) => {
              const { customWebhook } = integration;
              const hasCustom = !!customWebhook;

              return (
                <SocialProfiles.Profile key={integration.id + "-custom"}>
                  <SocialProfiles.ProfileDetails
                    profile={{
                      id: integration.id,
                      name: integration.account.name,
                      detail: integration.account.vanityName,
                      channel: "LinkedIn",
                      profileImageUrl: integration.account.profileImageUrl,
                    }}
                  />
                  <div className="flex items-center gap-4">
                    {hasCustom && (
                      <div className="text-success font-body-sm flex items-center gap-0.5">
                        <CheckCircleIcon className="h-4 w-4" />
                        <div className="font-medium">Connected</div>
                      </div>
                    )}
                    {hasCustom ? (
                      <DropdownMenu.Root>
                        <DropdownMenu.Trigger>
                          <div className="hover:bg-slate rounded-full p-0.5 transition-colors">
                            <EllipsisHorizontalIcon className="h-5 w-5" />
                          </div>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content>
                          <DropdownMenu.Item
                            icon="PencilSquareIcon"
                            onClick={() => {
                              setSelectedIntegration(integration);
                              setIsModalOpen(true);
                              setModalType("custom");
                            }}
                          >
                            Edit Webhook
                          </DropdownMenu.Item>
                          <DropdownMenu.Item
                            icon="PaperAirplaneIcon"
                            onClick={() => {
                              toast.promise(
                                sendTestWebhookMutation.mutateAsync({
                                  webhookID: customWebhook.id,
                                }),
                                {
                                  loading: "Sending test event",
                                  success: (data) => {
                                    return {
                                      message:
                                        "Test event received successfully",
                                      description:
                                        "Successfully received test event",
                                    };
                                  },
                                  error: (error: unknown) => ({
                                    message: "Error sending test event",
                                    description: (error as Error).message,
                                  }),
                                }
                              );
                            }}
                          >
                            Send Test Event
                          </DropdownMenu.Item>
                          <DropdownMenu.Item
                            icon="TrashIcon"
                            intent="danger"
                            onClick={() => {
                              openConfirmationModal({
                                title: `Delete custom webhook for ${integration.account.name}?`,
                                body: "Leads will no longer be sent to this webhook from this integration. This action cannot be undone.",
                                primaryButton: {
                                  label: "Delete Webhook",
                                  variant: "danger",
                                  onClick: () => {
                                    toast.promise(
                                      deleteCustomWebhookMutation.mutateAsync({
                                        integrationID: integration.id,
                                      }),
                                      {
                                        loading: "Deleting webhook",
                                        success: {
                                          message: "Webhook deleted",
                                          description:
                                            "Successfully deleted custom webhook",
                                        },
                                        error: (error: unknown) => ({
                                          message: "Failed to delete webhook",
                                          description: (error as Error).message,
                                        }),
                                      }
                                    );
                                  },
                                },
                              });
                            }}
                          >
                            Delete Webhook
                          </DropdownMenu.Item>
                        </DropdownMenu.Content>
                      </DropdownMenu.Root>
                    ) : (
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => {
                          setSelectedIntegration(integration);
                          setIsModalOpen(true);
                          setModalType("custom");
                        }}
                      >
                        Add Webhook
                      </Button>
                    )}
                  </div>
                </SocialProfiles.Profile>
              );
            })}
          </SocialProfiles.Root>
        </SettingsLayout.Section>
      </SettingsLayout.Sections>
      <AddWebhookModal
        open={isModalOpen && modalType === "clay"}
        setOpen={setIsModalOpen}
        integration={selectedIntegration}
      />
      <AddCustomWebhookModal
        open={isModalOpen && modalType === "custom"}
        setOpen={setIsModalOpen}
        integration={selectedIntegration}
      />
    </SettingsLayout.Root>
  );
};

type AddWebhookModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  integration: {
    id: string;
    clayWebhook: {
      url: string;
      authTokenLast4: string | null;
    } | null;
  } | null;
};

type FormValues = {
  webhookURL: string;
  authToken: string | null;
};

const AddWebhookModal = ({
  open,
  setOpen,
  integration,
}: AddWebhookModalProps) => {
  const { control, handleSubmit, reset, setValue }: UseFormReturn<FormValues> =
    useForm<FormValues>({
      defaultValues: {
        webhookURL: integration?.clayWebhook?.url || "",
      },
    });

  const [isAuthTokenEditable, setIsAuthTokenEditable] =
    useState<boolean>(false);

  const trpcUtils = trpc.useUtils();

  useEffect(() => {
    const setDefaultValues = () => {
      setValue("webhookURL", integration?.clayWebhook?.url || "");
      setIsAuthTokenEditable(!integration?.clayWebhook?.authTokenLast4);
    };

    setDefaultValues();
  }, [integration]);

  const upsertWebhookMutation =
    trpc.leads.linkedIn.upsertClayWebhook.useMutation({
      onSuccess: () => {
        setOpen(false);
        reset();
        toast.success(
          `Webhook ${
            integration?.clayWebhook ? "updated" : "created"
          } successfully`,
          {
            description: "Leads will now automatically be sent to Clay",
          }
        );
        trpcUtils.leads.linkedIn.getWorkspaceIntegrations.refetch();
      },
      onError: (error) => {
        handleTrpcError({
          title: "Failed to create webhook",
          error,
        });
      },
    });

  const onSubmit = (data: FormValues) => {
    const shouldNullifyAuthToken =
      !data.authToken &&
      integration?.clayWebhook?.authTokenLast4 &&
      isAuthTokenEditable;

    upsertWebhookMutation.mutate({
      integrationID: integration!.id,
      url: data.webhookURL,
      authToken: shouldNullifyAuthToken ? null : data.authToken,
    });
  };

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header>
        <Credenza.Title>
          {integration?.clayWebhook
            ? "Edit Clay Webhook URL"
            : "Add Clay Webhook URL"}
        </Credenza.Title>
        <Credenza.Description>
          For instructions on how to generate a webhook URL for Clay, click{" "}
          <TextLink
            href="https://www.clay.com/university/guide/webhook-integration-guide"
            value="here"
            openInNewTab
          />
          .
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mt-4 flex flex-col gap-4">
            <Label value="Webhook URL">
              <FormTextInput
                kind="outline"
                control={control}
                name="webhookURL"
                placeholder="https://api.clay.com/v3/sources/webhook/pull-in-data-from-a-webhook"
                rules={{ required: true }}
              />
            </Label>
            <Label value="Auth Token" isOptional={true}>
              {isAuthTokenEditable ? (
                <FormTextInput
                  kind="outline"
                  control={control}
                  name="authToken"
                  placeholder="Enter auth token"
                />
              ) : (
                <div className="flex items-center gap-2">
                  <div className="text-medium-dark-gray">
                    •••••{integration?.clayWebhook?.authTokenLast4}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsAuthTokenEditable(true)}
                  >
                    Edit
                  </Button>
                </div>
              )}
            </Label>
          </div>
          <Credenza.Footer>
            <Button
              variant="secondary"
              onClick={() => setOpen(false)}
              disabled={upsertWebhookMutation.isPending}
            >
              Cancel
            </Button>
            <Button type="submit" isLoading={upsertWebhookMutation.isPending}>
              Save
            </Button>
          </Credenza.Footer>
        </form>
      </Credenza.Body>
    </Credenza.Root>
  );
};

type AddCustomWebhookModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  integration: {
    id: string;
    customWebhook: {
      name: string;
      url: string;
      headerKey: string | null;
      headerValue: string | null;
    } | null;
  } | null;
};

type CustomFormValues = {
  name: string;
  webhookURL: string;
  headerKey: string | null;
  headerValue: string | null;
};

const AddCustomWebhookModal = ({
  open,
  setOpen,
  integration,
}: AddCustomWebhookModalProps) => {
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
  }: UseFormReturn<CustomFormValues> = useForm<CustomFormValues>({
    defaultValues: {
      name: integration?.customWebhook?.name || "",
      webhookURL: integration?.customWebhook?.url || "",
      headerKey: integration?.customWebhook?.headerKey || null,
      headerValue: integration?.customWebhook?.headerValue || null,
    },
  });

  const trpcUtils = trpc.useUtils();

  useEffect(() => {
    setValue("name", integration?.customWebhook?.name || "");
    setValue("webhookURL", integration?.customWebhook?.url || "");
    setValue("headerKey", integration?.customWebhook?.headerKey || null);
    setValue("headerValue", integration?.customWebhook?.headerValue || null);
  }, [integration]);

  const upsertMutation = trpc.leads.linkedIn.upsertCustomWebhook.useMutation({
    onSuccess: () => {
      setOpen(false);
      reset();
      toast.success(
        `Webhook ${
          integration?.customWebhook ? "updated" : "created"
        } successfully`,
        {
          description: "Leads will now automatically be sent to your webhook",
        }
      );
      trpcUtils.leads.linkedIn.getWorkspaceIntegrations.refetch();
    },
    onError: (error) => {
      handleTrpcError({
        title: "Failed to create webhook",
        error,
      });
    },
  });

  const headerKey = watch("headerKey");
  const headerValue = watch("headerValue");

  const addHeader = () => {
    setValue("headerKey", "");
    setValue("headerValue", "");
  };

  const removeHeader = () => {
    setValue("headerKey", null);
    setValue("headerValue", null);
  };

  const onSubmit = (data: CustomFormValues) => {
    upsertMutation.mutate({
      integrationID: integration!.id,
      name: data.name,
      url: data.webhookURL,
      headerKey: data.headerKey,
      headerValue: data.headerValue,
    });
  };

  const isHeaderVisible = headerKey != null && headerValue != null;

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header>
        <Credenza.Title>
          {integration?.customWebhook
            ? "Edit Custom Webhook"
            : "Add Custom Webhook"}
        </Credenza.Title>
        <Credenza.Description className="sr-only">
          Add a custom webhook to send LinkedIn leads to your own endpoint.
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-4">
            <Label value="Name">
              <FormTextInput
                kind="outline"
                control={control}
                name="name"
                placeholder="Enter webhook name (e.g. Zapier)"
              />
            </Label>
            <Label value="Webhook URL">
              <FormTextInput
                kind="outline"
                control={control}
                name="webhookURL"
                placeholder="https://example.com/webhook"
                rules={{ required: true }}
              />
            </Label>
            {isHeaderVisible && (
              <Label value="Headers">
                <div className="flex items-center gap-2">
                  <FormTextInput
                    kind="outline"
                    control={control}
                    name="headerKey"
                    placeholder="Key"
                  />
                  <FormTextInput
                    kind="outline"
                    control={control}
                    name="headerValue"
                    placeholder="Value"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeHeader()}
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </Button>
                </div>
              </Label>
            )}
            {!isHeaderVisible && (
              <Button variant="ghost" size="sm" onClick={addHeader}>
                <PlusIcon className="h-4 w-4" />
                Add Header
              </Button>
            )}
          </div>
          <Credenza.Footer>
            <Button
              variant="secondary"
              onClick={() => setOpen(false)}
              disabled={upsertMutation.isPending}
            >
              Cancel
            </Button>
            <Button type="submit" isLoading={upsertMutation.isPending}>
              Save
            </Button>
          </Credenza.Footer>
        </form>
      </Credenza.Body>
    </Credenza.Root>
  );
};

export default LinkedInLeadsIntegrations;
