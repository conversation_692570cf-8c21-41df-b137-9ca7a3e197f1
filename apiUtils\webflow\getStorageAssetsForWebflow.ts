import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForWebflow = async (post: {
  id: string;
  workspaceID: string;
  webflowContent: {
    id: string;
    fields: {
      id: string;
    }[];
  } | null;
}): Promise<{ [fieldID: string]: AssetType[] }> => {
  const webflowContent = post.webflowContent;

  if (!webflowContent) {
    return {};
  }

  const assetsMap = await getAssetsForFilePaths(
    webflowContent.fields.map((field) => ({
      id: field.id,
      path: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Webflow"].slug}/${webflowContent.id}/${field.id}`,
    }))
  );

  return assetsMap;
};

export default getStorageAssetsForWebflow;
