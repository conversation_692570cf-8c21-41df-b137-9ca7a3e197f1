import * as SeparatorPrimitive from "@radix-ui/react-separator";
import * as React from "react";
import { cn } from "~/utils";

const Separator = React.forwardRef<
  React.ElementRef<typeof SeparatorPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root> & {
    padding?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  }
>(
  (
    {
      padding = "none",
      orientation = "horizontal",
      className,
      decorative = true,
      ...props
    },
    ref
  ) => (
    <SeparatorPrimitive.Root
      ref={ref}
      decorative={decorative}
      orientation={orientation}
      className={cn(
        "bg-light-gray shrink-0",
        {
          "h-full w-px": orientation === "vertical",
          "mx-0": padding === "none" && orientation === "vertical",
          "mx-0.5": padding === "xs" && orientation === "vertical",
          "mx-1.5": padding === "sm" && orientation === "vertical",
          "mx-4": padding === "md" && orientation === "vertical",
          "mx-6": padding === "lg" && orientation === "vertical",
          "mx-10": padding === "xl" && orientation === "vertical",
          "h-px w-full": orientation === "horizontal",
          "my-0": padding === "none" && orientation === "horizontal",
          "my-0.5": padding === "xs" && orientation === "horizontal",
          "my-1.5": padding === "sm" && orientation === "horizontal",
          "my-4": padding === "md" && orientation === "horizontal",
          "my-6": padding === "lg" && orientation === "horizontal",
          "my-8": padding === "xl" && orientation === "horizontal",
        },
        className
      )}
      {...props}
    />
  )
);
Separator.displayName = SeparatorPrimitive.Root.displayName;

export default Separator;
