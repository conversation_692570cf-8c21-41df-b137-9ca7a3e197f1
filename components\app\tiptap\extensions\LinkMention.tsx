import Mention from "@tiptap/extension-mention";

const LinkMention = Mention.extend({
  inclusive: false,
  addAttributes() {
    const parentAttributes = this.parent?.() || {};

    return {
      ...parentAttributes,
      href: {
        default: null,
        parseHTML: (element) => element.getAttribute("href"),
        renderHTML: (attributes) => ({
          href: attributes.href,
        }),
      },
    };
  },
});

export default LinkMention;
