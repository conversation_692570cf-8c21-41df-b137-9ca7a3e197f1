import { WebflowFieldType } from "@prisma/client";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Divider } from "~/components";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import { FormSelect, FormTextInput } from "~/components/form";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import RadioGroup from "~/components/ui/RadioGroup";
import Skeleton from "~/components/ui/Skeleton";
import { urlRegex } from "~/constants";
import { useWindowTitle } from "~/hooks";
import { handleTrpcError, trpc } from "~/utils";

type Site = {
  id: string;
  displayName: string;
  shortName: string;
  customDomains: {
    id: string;
    url: string;
  }[];
  scope: string[];
  previewUrl: string | null;
  collections: {
    id: string;
    displayName: string;
    isConnected: boolean;
  }[];
  accessToken: string;
};

type Collection = {
  id: string;
  displayName: string;
  fields: Field[];
  blogBaseUrl?: string | null;
  blogTitleFieldID?: string | null;
  blogContentFieldID?: string | null;
};

type Field = {
  id: string;
  slug: string;
  displayName: string;
  type: WebflowFieldType;
};

type Props = {
  type: "scheduling";
};

const WebflowChooseProfilePage = ({ type }: Props) => {
  useWindowTitle("Webflow Integration");
  const router = useRouter();

  const [siteOptions, setSiteOptions] = useState<Site[] | null>(null);
  const [selectedSite, setSelectedSite] = useState<Site | null>(null);
  const [selectedCollection, setSelectedCollection] =
    useState<Collection | null>(null);

  const getSiteOptionsMutation =
    trpc.integration.webflow.getSiteOptions.useMutation();
  const getCollectionDetailsMutation =
    trpc.integration.webflow.getCollectionDetails.useMutation();

  useEffect(() => {
    const getOauthToken = async () => {
      if (!router.isReady) return;

      const query = router.query;

      if (query.error_message) {
        router.replace(window.location.pathname);

        toast.error("Error Connecting Webflow Account", {
          description: query.error_message as string,
        });
      } else if (query.code) {
        router.replace(window.location.pathname);

        const code = query.code as string;

        getSiteOptionsMutation.mutate(
          { code },
          {
            onSuccess: (response) => {
              setSiteOptions(response.sites);
              if (response.sites.length === 1) {
                setSelectedSite(response.sites[0]);
              }
            },
            onError: (error) => {
              handleTrpcError({
                title: "Error Connecting Webflow Site",
                error,
              });
            },
          }
        );
      } else if (query.collectionID) {
        const collectionID = query.collectionID as string;
        getCollectionDetailsMutation.mutate(
          { collectionID },
          {
            onSuccess: (data) => {
              setSelectedCollection({
                ...data.collection,
                blogBaseUrl: data.blogBaseUrl,
                blogTitleFieldID: data.blogTitleFieldID,
                blogContentFieldID: data.blogContentFieldID,
              });
            },
            onError: (error) => {
              handleTrpcError({
                title: "Error Getting Collection Details",
                error,
              });
            },
          }
        );
      } else {
        router.push(`/settings/profiles/scheduling/webflow`);
      }
    };
    getOauthToken();
  }, [router.isReady]);

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      {selectedCollection == null ? (
        <>
          {selectedSite == null ? (
            <ChooseSite siteOptions={siteOptions} setSite={setSelectedSite} />
          ) : (
            <ChooseCollection
              site={selectedSite}
              setCollection={setSelectedCollection}
              clearSelectedSite={() => setSelectedSite(null)}
            />
          )}
        </>
      ) : (
        <SetCMSSettings
          site={selectedSite}
          collection={selectedCollection}
          clearSelectedCollection={() => setSelectedCollection(null)}
        />
      )}
    </SettingsLayout.Root>
  );
};

type ChooseSiteProps = {
  siteOptions: Site[] | null;
  setSite: (site: Site) => void;
};

const ChooseSite = ({ siteOptions, setSite }: ChooseSiteProps) => {
  const router = useRouter();
  const [selectedSite, setSelectedSite] = useState<Site | null>(null);

  return (
    <ConnectProfile.Root>
      <ConnectProfile.Header
        channel="Webflow"
        title="Select a Webflow Site"
        description="Select a site you'd like to connect to Assembly"
      />
      <ConnectProfile.Content className="mt-4 space-y-2">
        {siteOptions === null ? (
          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div key={i}>
                <div className="flex w-full items-center justify-between p-2">
                  <div className="flex items-center">
                    <Skeleton className="border-medium-gray h-3 w-3 rounded-full border bg-white" />
                    <div className="ml-5 flex items-center gap-2">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                  </div>
                </div>
                <Divider className="my-2 md:my-4" />
              </div>
            ))}
          </div>
        ) : (
          <RadioGroup.Root
            className="max-h-[460px] flex-col flex-nowrap gap-0 overflow-y-auto"
            orientation="vertical"
            onValueChange={(value) => {
              const site = siteOptions?.find((site) => site.id === value);
              if (site) {
                setSelectedSite(site);
              }
            }}
          >
            {siteOptions.map((site) => {
              return (
                <div key={site.id}>
                  <RadioGroup.Item value={site.id} className="w-full">
                    <div className="flex items-center justify-between">
                      <div className="ml-5 flex w-full items-center gap-x-2 transition-all">
                        <div className="font-body-sm">
                          <p className="font-medium">{site.displayName}</p>
                          <p className="text-medium-dark-gray">
                            {site.customDomains[0]?.url || site.shortName}
                          </p>
                        </div>
                      </div>
                    </div>
                  </RadioGroup.Item>
                  <Divider className="my-2 md:my-4" />
                </div>
              );
            })}
          </RadioGroup.Root>
        )}
      </ConnectProfile.Content>
      <div className="mt-2 flex flex-row justify-end md:gap-2.5">
        <Button
          variant="secondary"
          onClick={() => {
            router.push(`/settings/profiles/scheduling/webflow`);
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={() => {
            if (selectedSite == null) {
              toast.error("Please select a site", {
                description: "Select a site to connect",
              });
            } else {
              setSite(selectedSite);
            }
          }}
        >
          Continue
        </Button>
      </div>
    </ConnectProfile.Root>
  );
};

type ChooseCollectionProps = {
  site: {
    id: string;
    displayName: string;
    collections: {
      id: string;
      displayName: string;
      isConnected: boolean;
    }[];
    accessToken: string;
  };
  setCollection: (collection: Collection) => void;
  clearSelectedSite: () => void;
};

const ChooseCollection = ({
  site,
  setCollection,
  clearSelectedSite,
}: ChooseCollectionProps) => {
  const getCollectionDetailsMutation =
    trpc.integration.webflow.getCollectionDetails.useMutation();

  const [selectedCollection, setSelectedCollection] = useState<{
    id: string;
    displayName: string;
  } | null>(null);

  const { collections } = site;

  return (
    <ConnectProfile.Root>
      <ConnectProfile.Header
        channel="Webflow"
        title="Select a CMS Collection"
        description="Select which CMS collection you'd like to connect to Assembly"
      />
      <ConnectProfile.Content className="mt-4 space-y-2">
        <RadioGroup.Root
          className="max-h-[400px] flex-col flex-nowrap gap-0 overflow-y-auto"
          orientation="vertical"
          onValueChange={(value) => {
            const collection = collections?.find(
              (collection) => collection.id === value
            );
            if (collection) {
              setSelectedCollection(collection);
            }
          }}
        >
          {collections.map((collection) => {
            const { isConnected } = collection;

            return (
              <div key={collection.id}>
                <RadioGroup.Item
                  value={collection.id}
                  disabled={isConnected}
                  className="w-full"
                >
                  <div className="flex items-center justify-between">
                    <div className="ml-5 flex w-full items-center gap-x-2 transition-all">
                      <div className="font-body-sm font-medium">
                        {collection.displayName}
                      </div>
                    </div>
                    {isConnected && (
                      <p className="text-dark-gray font-body-sm whitespace-nowrap">
                        Already Connected
                      </p>
                    )}
                  </div>
                </RadioGroup.Item>
                <Divider className="my-2 md:my-4" />
              </div>
            );
          })}
          {collections.length === 0 && (
            <div className="font-body-sm">
              <div className="font-medium">No CMS found</div>
              <div className="text-medium-dark-gray mt-2">
                Looks like there's no CMS collections associated with this site
                - please select a different site, or add a CMS collection on
                Webflow and try the integration again.
              </div>
            </div>
          )}
        </RadioGroup.Root>
      </ConnectProfile.Content>
      <div className="mt-2 flex flex-row justify-end md:gap-2.5">
        <Button
          variant="secondary"
          onClick={() => {
            clearSelectedSite();
          }}
        >
          Back
        </Button>
        <Button
          isLoading={getCollectionDetailsMutation.isPending}
          onClick={() => {
            if (selectedCollection == null) {
              toast.error("Please select a collection", {
                description: "Select a collection to connect",
              });
            } else {
              getCollectionDetailsMutation.mutate(
                {
                  collectionID: selectedCollection.id,
                  encryptedAccessToken: site.accessToken,
                },
                {
                  onSuccess: (data) => {
                    setCollection(data.collection);
                  },
                  onError: (error) => {
                    handleTrpcError({
                      title: "Error Getting Collection Details",
                      error,
                    });
                  },
                }
              );
            }
          }}
        >
          Continue
        </Button>
      </div>
    </ConnectProfile.Root>
  );
};

type SetCMSSettingsProps = {
  site: {
    id: string;
    displayName: string;
    scope: string[];
    accessToken: string;
  } | null;
  collection: Collection;
  clearSelectedCollection: () => void;
};

type FormValues = {
  blogTitleFieldID: string;
  blogContentFieldID: string;
  blogBaseUrl: string;
};

const SetCMSSettings = ({
  site,
  collection,
  clearSelectedCollection,
}: SetCMSSettingsProps) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();

  const { control, handleSubmit, setValue } = useForm<FormValues>();

  useEffect(() => {
    const setDefaultBlogFields = () => {
      if (collection.blogBaseUrl) {
        setValue("blogBaseUrl", collection.blogBaseUrl);
      }

      const defaultBlogTitleField = collection.fields.find(
        (field) => field.slug === "name"
      );
      if (defaultBlogTitleField) {
        setValue(
          "blogTitleFieldID",
          collection.blogTitleFieldID || defaultBlogTitleField?.id
        );
      }

      const defaultBlogContentField = collection.fields.find(
        (field) => field.type === "RichText"
      );
      if (defaultBlogContentField) {
        setValue(
          "blogContentFieldID",
          collection.blogContentFieldID || defaultBlogContentField?.id
        );
      }
    };

    setDefaultBlogFields();
  }, [collection.fields]);

  const saveIntegrationMutation =
    trpc.integration.webflow.saveIntegration.useMutation();
  const updateIntegrationFieldsMutation =
    trpc.integration.webflow.updateIntegrationFields.useMutation();

  const handleSaveIntegration = (values: FormValues) => {
    const blogBaseUrl = values.blogBaseUrl.startsWith("http")
      ? values.blogBaseUrl
      : `https://${values.blogBaseUrl}`;

    if (site == null) {
      updateIntegrationFieldsMutation.mutate(
        {
          collectionID: collection.id,
          blogBaseUrl,
          blogTitleFieldID: values.blogTitleFieldID,
          blogContentFieldID: values.blogContentFieldID,
        },
        {
          onSuccess: () => {
            toast.success("Webflow CMS Fields Updated", {
              description: `Updated ${collection.displayName}`,
            });
            trpcUtils.workspace.getSchedulingProfiles.refetch();
            router.push(`/settings/profiles`);
          },
          onError: (error) => {
            handleTrpcError({
              title: "Error Updating Webflow CMS Fields",
              error,
            });
          },
        }
      );
    } else {
      saveIntegrationMutation.mutate(
        {
          siteID: site.id,
          siteName: site.displayName,
          collectionID: collection.id,
          collectionName: collection.displayName,
          scope: site.scope,
          accessToken: site.accessToken,
          blogBaseUrl,
          blogTitleFieldID: values.blogTitleFieldID,
          blogContentFieldID: values.blogContentFieldID,
        },
        {
          onSuccess: () => {
            toast.success("Webflow CMS Connected", {
              description: `Connected ${site.displayName}`,
            });
            trpcUtils.workspace.getSchedulingProfiles.refetch();
            router.push(`/settings/profiles`);
          },
          onError: (error) => {
            handleTrpcError({
              title: "Error Saving Webflow Integration",
              error,
            });
          },
        }
      );
    }
  };

  return (
    <ConnectProfile.Root>
      <ConnectProfile.Header
        channel="Webflow"
        title="Edit CMS Settings"
        description="Map your CMS fields to Assembly and set your blog URL."
      />
      <ConnectProfile.Content className="mt-4 space-y-2">
        <form onSubmit={handleSubmit(handleSaveIntegration)}>
          <div>
            <span className="font-body font-medium">Map your fields</span>
            <div className="font-body-sm">
              <span className="text-medium-dark-gray">
                Let us know which CMS fields should correspond with our Assembly
                editor.
              </span>
              <div className="my-8 flex gap-14">
                <div className="flex flex-col gap-[18px]">
                  <div className="font-eyebrow">Assembly Editor</div>
                  <div>Blog Title</div>
                  <div>Blog Content</div>
                </div>
                <div className="flex flex-col gap-4">
                  <div className="font-eyebrow">Assembly Editor</div>
                  <FormSelect
                    name="blogTitleFieldID"
                    control={control}
                    options={
                      collection.fields
                        .filter((field) => field.type === "PlainText")
                        .map((field) => ({
                          value: field.id,
                          label: field.displayName,
                        })) || []
                    }
                  />
                  <FormSelect
                    name="blogContentFieldID"
                    control={control}
                    options={
                      collection.fields
                        .filter((field) => field.type === "RichText")
                        .map((field) => ({
                          value: field.id,
                          label: field.displayName,
                        })) || []
                    }
                  />
                </div>
              </div>
              <span className="text-medium-dark-gray">
                Note: Assembly will automatically pull in all the other custom
                fields from your CMS. You can edit them on the Post Options tab
                on any blog post.
              </span>
            </div>
          </div>
          <div className="mt-10">
            <span className="font-body font-medium">Set your blog URL</span>
            <div className="font-body-sm">
              <div className="text-medium-dark-gray pb-2.5">
                Let us know the URL of your blog (without the post name slug).
                For example, if your blog posts live on
                www.apple.com/blog/this-is-a-post-name, the URL you would paste
                in would be{" "}
                <span className="text-secondary">www.apple.com/blog</span>
              </div>
              <FormTextInput
                control={control}
                name="blogBaseUrl"
                placeholder="www.your-domain.com/blog"
                kind="outline"
                label="Blog URL"
                rules={{
                  required: true,
                  pattern: {
                    value: urlRegex,
                    message: "Invalid URL",
                  },
                }}
              />
            </div>
          </div>
          <div className="mt-2 flex flex-row justify-end md:gap-2.5">
            <Button
              variant="secondary"
              onClick={() => {
                if (site == null) {
                  router.push(`/settings/profiles`);
                } else {
                  clearSelectedCollection();
                }
              }}
            >
              Back
            </Button>
            <Button
              isLoading={
                saveIntegrationMutation.isPending ||
                updateIntegrationFieldsMutation.isPending
              }
              type="submit"
            >
              Save
            </Button>
          </div>
        </form>
      </ConnectProfile.Content>
    </ConnectProfile.Root>
  );
};

export default WebflowChooseProfilePage;
