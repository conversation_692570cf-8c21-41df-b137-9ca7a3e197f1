import * as Icons from "@heroicons/react/24/outline";
import classNames from "classnames";
import KbdTooltip from "./app/KbdTooltip";

type BorderRadius = "md" | "full";

type Props = {
  /** The Icon to display, full list https://heroicons.com */
  icon: keyof typeof Icons;
  /** Triggered when the button is clicked */
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  /** The text of the tooltip that shows on hover */
  tooltipText?: string;
  /** The optional label of the button */
  label?: string;
  intent?: "basic" | "none" | "danger";
  /** The border raidus of the IconButton
   *  - `"full", the default and will have full roundedness e.g. if you want a circle around the icon
   *  - `"md"`, a less rounded radius at 6px */
  borderRadius?: BorderRadius;
  /** The side that the icon is on relative to the label */
  iconSide?: "left" | "right";
  size?: "sm" | "md";
  /** Whether the button should be focused on mount */
  autoFocus?: boolean;
  hotKeyOptions?: {
    /** The hotkeys that open the combobox */
    keys: string | string[];
    /** The label for the hotkeys */
    label: string;
  };
};

const IconButton = ({
  icon,
  onClick,
  label,
  intent = "none",
  tooltipText,
  borderRadius = "full",
  iconSide = "left",
  size = "md",
  autoFocus,
  hotKeyOptions,
}: Props) => {
  const Icon = Icons[icon];

  const className = classNames(
    "font-body-sm transition-colors outline-hidden",
    {
      /** Intent Styles */
      "bg-basic text-basic-light hover:bg-basic/80": intent === "basic",
      "bg-surface hover:bg-lightest-gray/50 text-black": intent === "none",
      "border border-light-gray": intent === "none" && !!label,
      "bg-surface hover:bg-lightest-gray text-danger": intent === "danger",
      /** Label Styles */
      "flex items-center justify-center gap-1 pr-2": !!label,
      /** Border Radius Styles */
      "rounded-full p-1.5": borderRadius === "full" && size === "md",
      "rounded-full p-0.5": borderRadius === "full" && size === "sm",
      "rounded-md p-1.5": borderRadius === "md",
    }
  );

  return (
    <KbdTooltip
      hotKeys={hotKeyOptions?.keys}
      label={hotKeyOptions?.label}
      // @ts-ignore we're passing the event to stop propagation
      onKeyPress={() => onClick()}
    >
      {iconSide === "left" ? (
        <button
          className={className}
          onClick={onClick}
          type="button"
          autoFocus={autoFocus}
        >
          <Icon
            className={classNames({
              "h-2 w-2": !!label && size === "sm",
              "h-4 w-4":
                (!!label && size === "md") || (!label && size === "sm"),
              "h-5 w-5": !label && size === "md",
            })}
          />
          <span className="whitespace-nowrap">{label}</span>
        </button>
      ) : (
        <button
          className={className}
          onClick={onClick}
          type="button"
          autoFocus={autoFocus}
        >
          <span>{label}</span>
          <Icon className="h-5 w-5" />
        </button>
      )}
    </KbdTooltip>
  );
};

export default IconButton;
