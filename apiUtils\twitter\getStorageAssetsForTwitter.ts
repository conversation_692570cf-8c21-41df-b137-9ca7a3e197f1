import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForTwitter = async (post: {
  id: string;
  workspaceID: string;
  tweets: {
    id: string;
  }[];
}): Promise<{ [id: string]: AssetType[] }> => {
  const filePaths = post.tweets.map((tweet) => {
    return {
      id: tweet.id,
      path: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Twitter"].slug}/${tweet.id}`,
    };
  });

  return await getAssetsForFilePaths(filePaths);
};

export default getStorageAssetsForTwitter;
