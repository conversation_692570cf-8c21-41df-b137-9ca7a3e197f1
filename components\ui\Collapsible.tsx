import * as CollapsiblePrimitive from "@radix-ui/react-collapsible";
import React from "react";
import { cn } from "~/utils";

const Root = CollapsiblePrimitive.Root;

const Trigger = CollapsiblePrimitive.CollapsibleTrigger;

const Content = React.forwardRef<
  React.ElementRef<typeof CollapsiblePrimitive.CollapsibleContent>,
  React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.CollapsibleContent>
>(({ className, ...props }, ref) => (
  <CollapsiblePrimitive.CollapsibleContent
    ref={ref}
    className={cn(
      "data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up overflow-hidden",
      className
    )}
    {...props}
  />
));
Content.displayName = "CollapsibleContent";

export default { Root, Trigger, Content };
