import { Channel, YouTubePrivacyStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import assert from "assert";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import HelpTooltip from "~/components/HelpTooltip";
import Select from "~/components/Select";
import TextLink from "~/components/TextLink";
import { MAX_VIDEO_UPLOAD_SIZE_MB, ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import YOUTUBE_CATEGORIES from "~/constants/youtubeCategories";
import {
  usePostIntegrations,
  useThreadComments,
  useWorkspace,
} from "~/providers";
import { YouTubeIntegration } from "~/providers/PostIntegrationsProvider";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  findAsync,
  handleFilesUpload,
  handleTrpcError,
  openIntercomChat,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import getFileProperties from "~/utils/getFileProperties";
import getVideoProperties from "~/utils/getVideoProperties";
import createRoomID from "~/utils/liveblocks/createRoomID";
import { UploadingAsset } from "../../files/Assets";
import YouTubeShortsEditorWrapperNewDesign from "../../tiptap/YoutubeShortsEditorNewDesign";
import EditorActionsBar from "../EditorActionsBar";
import {
  PostOptionNewDesign,
  PostOptionsNewDesign,
} from "../PostOptionsNewDesign";
import PostPerformance from "../PostPerformance";
import { ScheduledPost } from "../SchedulePostAccountSelect";
import YouTubeShortsPostPreviewNewDesign from "./YouTubeShortsPostPreviewNewDesign";

export type YouTubeShortsContent = {
  id: string;
  title: string | null;
  titleEditorState: JSONContent | null;
  description: string | null;
  descriptionEditorState: JSONContent | null;
  categoryID: string | null;
  privacyStatus: YouTubePrivacyStatus;
  notifySubscribers: boolean;
  isEmbeddable: boolean;
  isSelfDeclaredMadeForKids: boolean;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    scheduledPosts: ScheduledPost[];
    youTubeShortsAssets: {
      asset: AssetType | null;
    };
    youTubeShortsContent: YouTubeShortsContent | null;
    youTubeIntegration: YouTubeIntegration | null;
    youTubeLink: string | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const YouTubeShortsPostNewDesign = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const trpcUtils = trpc.useUtils();
  const [isRoomReady, setIsRoomReady] = useState<boolean>(false);
  const {
    connectedIntegrations,
    selectedIntegrations,
    currentChannel,
    getChannelIntegration,
  } = usePostIntegrations();

  const connectedYouTubeShortsIntegrations =
    connectedIntegrations.YouTubeShorts || [];

  useEffect(() => {
    if (post.youTubeShortsContent && !isSyncingComplete) {
      setIsRoomReady(true);
    }
  }, [post.youTubeShortsContent, isSyncingComplete]);

  const [contentAsset, setContentAsset] = useState<AssetType | null>(
    post.youTubeShortsAssets.asset
  );
  const [uploadingAsset, setUploadingAsset] = useState<UploadingAsset | null>(
    null
  );
  const { currentWorkspace } = useWorkspace();

  const [draggedOver, setDraggedOver] = useState<boolean>(false);

  const [content, setContent] = useState<YouTubeShortsContent>(
    post.youTubeShortsContent || {
      id: uuidv4(),
      title: null,
      titleEditorState: null,
      description: "",
      descriptionEditorState: null,
      categoryID: "",
      privacyStatus: "public",
      notifySubscribers: true,
      isEmbeddable: false,
      isSelfDeclaredMadeForKids: false,
    }
  );
  const [integration, setIntegration] = useState<YouTubeIntegration | null>(
    post.youTubeIntegration || null
  );

  const [uppy, setUppy] = useState<Uppy | null>(null);

  const updatePostMutation = trpc.post.update.useMutation();

  useEffect(() => {
    if (
      selectedIntegrations.YouTubeShorts &&
      connectedYouTubeShortsIntegrations.length
    ) {
      const selectedIntegration = connectedYouTubeShortsIntegrations.find(
        (integration) => integration.id === selectedIntegrations.YouTubeShorts
      );

      if (
        selectedIntegration &&
        (!integration || integration.id !== selectedIntegration.id)
      ) {
        const youTubeIntegration = getChannelIntegration(
          "YouTubeShorts",
          selectedIntegrations.YouTubeShorts
        );
        if (youTubeIntegration) {
          setIntegration(youTubeIntegration);
          updatePostMutation.mutate(
            {
              id: post.id,
              youTubeIntegrationID: youTubeIntegration.id,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getIntegrations.refetch();
              },
            }
          );
        }
      }
    }
  }, [selectedIntegrations, connectedYouTubeShortsIntegrations]);

  const contentQuery = trpc.youtubeShortsContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const contentData = contentQuery.data;
  useEffect(() => {
    const setContentData = async () => {
      if (contentData && contentData.youTubeShortsContent) {
        setContent(contentData.youTubeShortsContent);
        setContentAsset(contentData.asset);
        if (contentData.asset) {
          await maybeCompressAssets([contentData.asset]);
        }
        setIsRoomReady(true);
        clearIsSyncing();
      }
    };
    setContentData();
  }, [contentData]);

  const upsertYouTubeMutation = trpc.youtubeShortsContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
  });

  const createAssetsMutation =
    trpc.youtubeShortsContent.createAsset.useMutation();
  const deleteAssetMutation =
    trpc.youtubeShortsContent.deleteAsset.useMutation();
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const compressVideoMutation = trpc.video.compress.useMutation();
  const maybeCompressAssets = async (
    assets: {
      path: string;
      sizeBytes: number;
      sizeMB: number;
      mimetype: string;
      signedUrl: string;
      duration?: number | null;
      width?: number | null;
      height?: number | null;
    }[]
  ) => {
    const videoPromises = assets.map(async (asset) => {
      if (!asset || !asset.mimetype.startsWith("video/")) {
        return Promise.resolve(null);
      } else {
        let duration = asset.duration;
        let width = asset.width;
        let height = asset.height;
        if (width && height && duration) {
          return asset as typeof asset & {
            duration: number;
            width: number;
            height: number;
          };
        } else {
          const properties = await getVideoProperties(asset.signedUrl);
          return {
            ...asset,
            duration: properties.duration,
            width: properties.width,
            height: properties.height,
          };
        }
      }
    });
    const videosWithProperties = await Promise.all(videoPromises);

    const videoToCompress = await findAsync(
      videosWithProperties,
      async (asset) => {
        if (!asset || !asset.mimetype.startsWith("video/")) {
          return false;
        }

        return asset.sizeMB > 125;
      }
    );

    if (videoToCompress != null) {
      const { width, height } = videoToCompress;

      compressVideoMutation.mutate(
        {
          video: {
            path: videoToCompress.path,
            resolution: {
              width,
              height,
            },
            duration: videoToCompress.duration,
            sizeBytes: videoToCompress.sizeBytes,
          },
          channel: "YouTubeShorts",
        },
        {
          onError: (error) => {
            handleTrpcError({ title: "Error Compressing Video", error });
          },
          onSuccess: (data) => {
            if (data.jobID != null) {
              toast.info("Optimizing Attachment", {
                description:
                  "We're automatically optimizing your attachment (files >125MB are not accepted on YouTube).",
              });
            }
          },
        }
      );
    }
  };

  const getValidFiles = async (files: File[]) => {
    if (contentAsset != null) {
      toast.error("Too Many Files", {
        description: "Only one file can be uploaded to YouTube Shorts",
      });
      return [];
    }

    const validFiles: File[] = [];
    // https://developers.facebook.com/docs/instagram-api/reference/ig-user/media#image-specifications
    for await (const file of files) {
      const fileType = file.type;
      const fileSizeMB = file.size / 1024 / 1024;

      if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
        toast.error("Video is too large", {
          description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
          action: {
            label: "Contact Support",
            onClick: () => {
              openIntercomChat(
                `I need help compressing a video for YouTube Shorts. I have a ${fileSizeMB.toFixed(
                  1
                )}MB video that I need to compress to under ${MAX_VIDEO_UPLOAD_SIZE_MB}MB`
              );
            },
          },
        });
        continue;
      }

      const { duration, height, width } = await getVideoProperties(file);

      if (duration > 3 * 60) {
        toast.error("Video is Too Long", {
          description:
            "YouTube Shorts requires videos to be a maximum of 3 minutes",
        });
        continue;
      }

      if (width / height !== 9 / 16) {
        toast.error("Invalid Aspect Ratio", {
          description:
            "YouTube Shorts requires videos to be in a 9:16 aspect ratio",
        });
        continue;
      }

      if (height / width < 1) {
        toast.error("Video Must be Taller", {
          description:
            "YouTube Shorts must have a height greater than or equal to it's width (minimum 1:1 aspect ratio)",
        });
        continue;
      }

      validFiles.push(file);
    }

    return validFiles;
  };

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `youtube-shorts-${post.id}`,
        restrictions: { maxNumberOfFiles: 1 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", (file) => {
        assert(currentWorkspace);

        setUploadingAsset({
          name: file.name!,
          size: file.size!,
          type: file.type,
          uploadProgress: 0,
        });

        const objectName = `${currentWorkspace.id}/${post.id}/${CHANNEL_ICON_MAP["YouTubeShorts"].slug}/${content.id}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAsset((prevAsset) => {
          if (prevAsset && prevAsset.name === file.name) {
            return {
              ...prevAsset,
              uploadProgress,
            };
          }
          return prevAsset;
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (data) => {
        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["YouTubeShorts"].slug
        }/${content.id}`;
        const fileNames = data.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          data.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              const newAsset = newAssetsWithProperties[0];

              createAssetsMutation.mutate(
                {
                  id: content.id,
                  asset: {
                    id: newAsset.id,
                    assetID: newAsset.assetID,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                },
                {
                  onSuccess: async () => {
                    await maybeCompressAssets(newAssetsWithProperties);
                  },
                  onError: (error) => {
                    handleTrpcError({ title: "Error Creating Asset", error });
                  },
                }
              );

              setUploadingAsset(null);
              setContentAsset(newAsset);
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace, content.id]);

  const { setRoomIDs } = useThreadComments();

  const titleRoomID = createRoomID({
    workspaceID: post.workspaceID,
    contentType: "post",
    postID: post.id,
    roomContentID: content.id,
    extraInformation: "title",
  });

  const descriptionRoomID = createRoomID({
    workspaceID: post.workspaceID,
    contentType: "post",
    postID: post.id,
    roomContentID: content.id,
    extraInformation: "description",
  });

  useEffect(() => {
    if (content.id && currentChannel === "YouTubeShorts") {
      setRoomIDs([titleRoomID, descriptionRoomID]);
    }
  }, [content.id, titleRoomID, descriptionRoomID, setRoomIDs, currentChannel]);

  return (
    <div
      className="flex max-w-[622px] flex-col items-center"
      // This avoids triggering the dropzone when dragging internal post content like existing images
      onDragOver={(event) => {
        if (
          event.dataTransfer.types.includes("Files") &&
          !event.dataTransfer.types.includes("text/html")
        ) {
          event.preventDefault();
          setDraggedOver(true);
        }
      }}
      onDragLeave={(event) => {
        const currentTarget = event.currentTarget;
        const relatedTarget = event.relatedTarget as Node | null;

        if (!relatedTarget || !currentTarget.contains(relatedTarget)) {
          setDraggedOver(false);
        }
      }}
      onDrop={() => {
        setDraggedOver(false);
      }}
    >
      <PostPerformance channel="YouTubeShorts" url={post.youTubeLink} />
      <YouTubeShortsPostPreviewNewDesign
        content={content}
        asset={contentAsset}
        connectedIntegration={integration}
        isReadOnly={isReadOnly}
        uppy={uppy}
        uploadingAssets={uploadingAsset ? [uploadingAsset] : []}
        getValidFiles={getValidFiles}
        onDrop={() => setDraggedOver(false)}
        onDeleteClick={(asset) => {
          setContentAsset(null);
          deleteAssetMutation.mutate({
            youTubeShortsAssetID: asset.id,
          });
        }}
        isDraggedOver={draggedOver}
      />
      {isRoomReady && (
        <div className="group mt-12 w-full">
          <div className="flex items-center gap-1">
            <span className="font-body-sm font-medium">Video Title</span>
            <HelpTooltip
              value={
                <div className="font-body-xs">
                  <div className="font-medium">Shorts Title</div>
                  <div>
                    The title for your shorts is the text that appears over your
                    Youtube Short video when being viewed (shown in the blue box
                    below).
                  </div>
                  <img
                    src="/youtube_shorts/title_and_content_info.png"
                    className="my-2 w-full"
                  />
                  <div className="font-medium">Shorts Content</div>
                  <div>
                    Your content is the actual video for Shorts. We support .mp4
                    and .mov video uploads.
                  </div>
                </div>
              }
            />
          </div>
          <YouTubeShortsEditorWrapperNewDesign
            key={content.id}
            postID={post.id}
            initialValue={content.titleEditorState}
            placeholder="Add your YouTube Shorts video title here"
            maxLength={100}
            onImagePaste={(event) =>
              handleFilesUpload({
                items: event.clipboardData.items,
                uppy: uppy!,
                meta: {
                  contentID: content.id,
                },
                getValidFiles,
              })
            }
            onSave={(titleEditorState) => {
              setContent((prevContent) => {
                upsertYouTubeMutation.mutate({
                  id: prevContent.id,
                  title: prevContent.title,
                  titleEditorState,
                  postID: post.id,
                });

                return {
                  ...prevContent,
                  titleEditorState,
                };
              });
            }}
            onUpdateContent={(title) => {
              setContent((prevContent) => ({
                ...prevContent,
                title,
              }));
            }}
            roomID={titleRoomID}
          />
          <EditorActionsBar
            post={post}
            channel="YouTubeShorts"
            visible={true}
            isReadOnly={isReadOnly}
            characterCount={{
              current: content.title?.length || 0,
              max: 100,
            }}
            uploadAssetButton={{
              acceptedMimetypes: ["video/mp4", "video/quicktime"],
              onUploadFiles: async (files) => {
                if (uppy) {
                  handleFilesUpload({
                    files,
                    uppy,
                    meta: {
                      contentID: content.id,
                    },
                    getValidFiles,
                  });
                }
              },
              isLoading: !!uploadingAsset,
            }}
          />
          <div className="mt-12">
            <div className="flex items-center gap-1">
              <span className="font-body-sm font-medium">
                Video Description
              </span>
              <HelpTooltip
                value={
                  <div className="font-body-xs">
                    <div className="font-medium">Shorts Desciption</div>
                    <div>
                      The description for your Shorts appears in the more info
                      icon on Desktop. It can be helpful when including linkouts
                      related to your video, or a longer description to make
                      your video relevant for search.
                    </div>
                    <img
                      src="/youtube_shorts/description_info.png"
                      className="my-2 w-full"
                    />
                  </div>
                }
              />
            </div>
            <YouTubeShortsEditorWrapperNewDesign
              key={content.id}
              postID={post.id}
              initialValue={content.descriptionEditorState}
              placeholder="Add your YouTube Shorts video description here"
              maxLength={5000}
              onSave={(descriptionEditorState) => {
                setContent((oldContent) => ({
                  ...oldContent,
                  descriptionEditorState,
                }));

                upsertYouTubeMutation.mutate({
                  id: content.id,
                  description: content.description,
                  descriptionEditorState,
                  postID: post.id,
                });
              }}
              onUpdateContent={(description) => {
                setContent((oldContent) => ({
                  ...oldContent,
                  description,
                }));
              }}
              roomID={descriptionRoomID}
            />
          </div>
        </div>
      )}
      <PostOptionsNewDesign>
        <PostOptionNewDesign title="Notify Subs?">
          <Select
            value={content.notifySubscribers}
            options={[
              {
                value: true,
                label: "Yes, notify",
              },
              {
                value: false,
                label: "No, don't notify",
              },
            ]}
            onChange={(notifySubscribers) => {
              setContent((oldContent) => ({
                ...oldContent,
                notifySubscribers,
              }));

              upsertYouTubeMutation.mutate({
                id: content.id,
                notifySubscribers,
                postID: post.id,
              });
            }}
          />
        </PostOptionNewDesign>
        <PostOptionNewDesign title="Category">
          <Select
            placeholder="Category"
            isSearchable={true}
            value={content.categoryID}
            options={YOUTUBE_CATEGORIES}
            onChange={(categoryID) => {
              setContent((oldContent) => ({
                ...oldContent,
                categoryID,
              }));

              upsertYouTubeMutation.mutate({
                id: content.id,
                categoryID,
                postID: post.id,
              });
            }}
          />
        </PostOptionNewDesign>
        <PostOptionNewDesign title="Visibility">
          <Select
            placeholder="Privacy Status"
            value={content.privacyStatus}
            options={[
              {
                value: "public",
                label: "Public",
              },
              {
                value: "private",
                label: "Private",
              },
              {
                value: "unlisted",
                label: "Unlisted",
              },
            ]}
            onChange={(privacyStatus) => {
              setContent((oldContent) => ({
                ...oldContent,
                privacyStatus,
              }));

              upsertYouTubeMutation.mutate({
                id: content.id,
                privacyStatus,
                postID: post.id,
              });
            }}
          />
        </PostOptionNewDesign>
        <PostOptionNewDesign title="Embedding?">
          <Select
            value={content.isEmbeddable}
            options={[
              {
                value: true,
                label: "Allow",
              },
              {
                value: false,
                label: "Don't allow",
              },
            ]}
            onChange={(isEmbeddable) => {
              setContent((oldContent) => ({
                ...oldContent,
                isEmbeddable,
              }));

              upsertYouTubeMutation.mutate({
                id: content.id,
                isEmbeddable,
                postID: post.id,
              });
            }}
          />
        </PostOptionNewDesign>
        <PostOptionNewDesign
          title={
            <div className="flex items-center gap-1">
              Made for Kids?
              <HelpTooltip
                value={
                  <div>
                    YouTube requires you to check this setting if your content
                    is made for kids.
                    <TextLink
                      value="Click here"
                      size="sm"
                      href="https://support.google.com/youtube/answer/9528076?hl=en"
                    />
                    for more info.
                  </div>
                }
              />
            </div>
          }
        >
          <Select
            placeholder="Made for kids?"
            value={content.isSelfDeclaredMadeForKids}
            options={[
              {
                value: true,
                label: "True",
              },
              {
                value: false,
                label: "False",
              },
            ]}
            onChange={(isSelfDeclaredMadeForKids) => {
              setContent((oldContent) => ({
                ...oldContent,
                isSelfDeclaredMadeForKids,
              }));

              upsertYouTubeMutation.mutate({
                id: content.id,
                isSelfDeclaredMadeForKids,
                postID: post.id,
              });
            }}
          />
        </PostOptionNewDesign>
      </PostOptionsNewDesign>
    </div>
  );
};

export default YouTubeShortsPostNewDesign;
