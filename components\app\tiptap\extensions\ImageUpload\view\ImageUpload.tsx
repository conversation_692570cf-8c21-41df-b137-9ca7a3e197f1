import { <PERSON>, NodeViewWrapper } from "@tiptap/react";
import { useCallback } from "react";

import { ImageUploader } from "./ImageUploader";

export const ImageUpload = ({
  getPos,
  editor,
}: {
  getPos: () => number;
  editor: Editor;
}) => {
  const handleUpload = useCallback(
    (url: string) => {
      if (url) {
        editor
          .chain()
          .setImageBlock({ src: url })
          .deleteRange({ from: getPos(), to: getPos() })
          .focus()
          .run();
      }
    },
    [getPos, editor]
  );

  const onUpload = editor.extensionManager.extensions.find(
    (ext) => ext.name === "imageUpload"
  )?.options.onUpload;

  return (
    <NodeViewWrapper>
      <div className="m-0 p-0" data-drag-handle>
        <ImageUploader onUpload={handleUpload} customUpload={onUpload} />
      </div>
    </NodeViewWrapper>
  );
};

export default ImageUpload;
