import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";
import { ClipboardEvent, useState } from "react";

import Bold from "@tiptap/extension-bold";
import BulletList from "@tiptap/extension-bullet-list";
import Code from "@tiptap/extension-code";
import Document from "@tiptap/extension-document";
import Heading, { Level } from "@tiptap/extension-heading";
import { History } from "@tiptap/extension-history";
import Italic from "@tiptap/extension-italic";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import Underline from "@tiptap/extension-underline";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import Turndown from "turndown";
import { Divider } from "~/components";
import { CHANNEL_ICON_MAP } from "~/types";
import { stripLiveblocksMarks } from "~/utils";
import { Threads } from "./Threads";
import {
  AutoMention,
  CharacterCount,
  CustomCommentThreadMark,
  Emoji,
  Link,
  PasteHandler,
} from "./extensions";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import FloatingToolbar from "./ui/FloatingToolbar";
import TextFormatButtons from "./ui/TextFormatButtons";
import TextStyleButtons from "./ui/TextStyleButtons";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  title: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  isEditable?: boolean;
  roomID: string | null;
};

type DiscordEditorProps = Omit<BaseProps, "roomID">;

const headingLevels: Level[] = [1, 2];

const createBaseExtensions = (placeholder: string) => [
  Document,
  Paragraph,
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  Text,
  Heading.configure({
    levels: headingLevels,
  }),
  History,
  Bold,
  Italic,
  Code,
  Underline,
  Emoji,
  BulletList,
  OrderedList,
  ListItem,
  Link.configure({
    openOnClick: true,
  }),
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  CharacterCount.configure({
    limit: 2000,
  }),
  AutoMention.configure({
    baseUrl: null,
    regex:
      /<[@#][!&]?\d{17,20}>|<t:\d{10}:[tTdDfFR]>|(?<=^|\s)(@(?:here|everyone))/g,
  }),
  PasteHandler,
];

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const DiscordEditorFallback = ({
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  isEditable = false,
  isReadOnly = false,
}: DiscordEditorProps) => {
  const cleanContent = stripLiveblocksMarks(initialValue);

  const [editorState, setEditorState] = useState<JSONContent | null>(
    cleanContent
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(cleanContent) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const turndown = new Turndown({
    headingStyle: "atx",
    emDelimiter: "*",
    blankReplacement: () => {
      return "[NEWLINE]\n";
    },
  });

  const originalEscape = turndown.escape;

  turndown.escape = function (text) {
    if (text.match(/^https?:\/\//)) {
      return text;
    }

    return originalEscape(text);
  };

  turndown.addRule("link", {
    filter: "a",
    replacement: (content, node) => {
      const href = (node as HTMLAnchorElement).getAttribute("href");

      if (content.trim() === href) {
        return href;
      }

      return `[${content}](${href})`;
    },
  });

  turndown.addRule("underline", {
    filter: ["u"],
    replacement: (content: string) => {
      return `__${content}__`;
    },
  });

  turndown.addRule("paragraph", {
    filter: "p",
    replacement: (content) => {
      return content + "\n";
    },
  });

  const editor = useEditor({
    extensions: createBaseExtensions(placeholder),
    immediatelyRender: false,
    content: cleanContent,
    editable: isEditable && !isReadOnly,
    onUpdate: ({ editor }) => {
      const markdown = turndown
        .turndown(editor.getHTML())
        .replaceAll("[NEWLINE]\n", "\n");
      onUpdateContent(markdown);

      const json = editor.getJSON();
      setEditorState(json);
    },
  });

  if (!editor) return null;

  return isEditable && !isReadOnly ? (
    <div className="py-2">
      <FloatingToolbar editor={editor} shouldHideWithLink={true}>
        {({ onHide }) => (
          <>
            <TextFormatButtons
              editor={editor}
              onHide={onHide}
              features={{ headings: headingLevels }}
            />
            <Divider orientation="vertical" />
            <TextStyleButtons editor={editor} />
          </>
        )}
      </FloatingToolbar>
      <EditorContent editor={editor} />
    </div>
  ) : (
    <EditorContent editor={editor} />
  );
};

const DiscordEditor = ({
  title,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
}: DiscordEditorProps) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const turndown = new Turndown({
    headingStyle: "atx",
    emDelimiter: "*",
    blankReplacement: () => {
      return "[NEWLINE]\n";
    },
  });

  const originalEscape = turndown.escape;

  turndown.escape = function (text) {
    if (text.match(/^https?:\/\//)) {
      return text;
    }

    return originalEscape(text);
  };

  turndown.addRule("link", {
    filter: "a",
    replacement: (content, node) => {
      const href = (node as HTMLAnchorElement).getAttribute("href");

      if (content.trim() === href) {
        return href;
      }

      return `[${content}](${href})`;
    },
  });

  turndown.addRule("underline", {
    filter: ["u"],
    replacement: (content: string) => {
      return `__${content}__`;
    },
  });

  turndown.addRule("paragraph", {
    filter: "p",
    replacement: (content) => {
      return content + "\n";
    },
  });

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(placeholder),
    ],
    immediatelyRender: false,
    editable: !isReadOnly,
    onUpdate: ({ editor }) => {
      const markdown = turndown
        .turndown(editor.getHTML())
        .replaceAll("[NEWLINE]\n", "\n");
      onUpdateContent(markdown);

      const json = editor.getJSON();
      setEditorState(json);
    },
  });

  const isEditorReady = useIsEditorReady();

  if (!editor) return null;

  return (
    <div
      className="py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor}>
        <ToolbarActions
          editor={editor}
          features={{
            headings: headingLevels,
            bulletList: true,
            orderedList: true,
            link: true,
          }}
        />
      </CustomFloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}

      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["Discord"].label,
            }}
          />
          <ClientSideSuspense fallback={null}>
            <Threads editor={editor} />
          </ClientSideSuspense>
        </>
      ) : (
        <DiscordEditorFallback
          title={title}
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
        />
      )}
    </div>
  );
};

const DiscordEditorWrapper = ({
  title,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  roomID,
}: BaseProps) => {
  if (!roomID) {
    return (
      <div className="py-2">
        {title && <div className="font-eyebrow text-dark-gray">{title}</div>}
        <DiscordEditorFallback
          title={title}
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
          isReadOnly={isReadOnly}
          isEditable={!isReadOnly}
        />
      </div>
    );
  }

  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense
        fallback={
          <div className="py-2">
            {title && (
              <div className="font-eyebrow text-dark-gray">{title}</div>
            )}
            <DiscordEditorFallback
              title={title}
              initialValue={initialValue}
              placeholder={placeholder}
              onSave={onSave}
              onUpdateContent={onUpdateContent}
            />
          </div>
        }
      >
        <DiscordEditor
          title={title}
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
          onImagePaste={onImagePaste}
          isReadOnly={isReadOnly}
        />
      </ClientSideSuspense>
    </RoomProvider>
  );
};

export default DiscordEditorWrapper;
