import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { ReactNode, useEffect, useState } from "react";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import Checkbox from "../Checkbox";
import UserAvatar from "../UserAvatar";

type Option = {
  id: string;
  name?: string | null;
  detail?: string | null;
  disabled?: boolean;
  disableReason?: string;
  profileImageUrl?: string;
  subOption?: {
    title: string | ReactNode;
    body: string;
    isSelected?: boolean;
    isHidden?: boolean;
  };
};

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  title: string;
  subtitle?: string | ReactNode;
  options: Option[];
  noOptionsMessage?: string | ReactNode;
  isLoading?: boolean;
  errorMessage?: string;
  hiddenProfilePictures?: boolean;
} & (
  | {
      isMulti?: false | undefined;
      defaultSelected?: { id: string } | null;
      onSelect: (option: Option) => void;
    }
  | {
      isMulti: true;
      defaultSelected?: { id: string }[];
      onSelect: (options: Option[]) => void;
    }
);

const SelectModal = ({
  open,
  setOpen,
  title,
  subtitle,
  options,
  noOptionsMessage,
  onSelect,
  hiddenProfilePictures,
  defaultSelected,
  isLoading = false,
  isMulti = false,
  errorMessage = "Please select an option",
}: Props) => {
  const [selectedOptions, setSelectedOptions] = useState<Option[]>(
    defaultSelected
      ? typeof defaultSelected === "object"
        ? [defaultSelected as Option]
        : defaultSelected
      : []
  );
  const [showError, setShowError] = useState<boolean>(false);

  useEffect(() => {
    if (open == false) {
      setSelectedOptions([]);
      setShowError(false);
    }
  }, [open]);

  useEffect(() => {
    if (defaultSelected) {
      const selectedOptions = options.filter((option) => {
        return Array.isArray(defaultSelected)
          ? defaultSelected.some((selected) => selected.id === option.id)
          : defaultSelected.id === option.id;
      });
      setSelectedOptions(selectedOptions);
    }
  }, [defaultSelected]);

  return (
    <Credenza.Root
      open={open}
      onOpenChange={() => {
        setSelectedOptions([]);
        setOpen(false);
      }}
    >
      <Credenza.Header>
        <Credenza.Title>{title}</Credenza.Title>
        {subtitle && <Credenza.Description>{subtitle}</Credenza.Description>}
      </Credenza.Header>
      <div className="divide-y-lightest-gray scrollbar-hidden flex max-h-96 flex-col divide-y overflow-auto">
        {options.length === 0 && (
          <div className="font-body-sm mt-5 font-medium">
            {noOptionsMessage || "No options available"}
          </div>
        )}
        {options.map((option) => {
          const { subOption } = option;
          const isSelected = selectedOptions
            .map((option) => option.id)
            .includes(option.id);

          return (
            <div className="w-full" key={option.id}>
              <button
                disabled={option.disabled}
                className={classNames(
                  "my-1 flex w-full items-center justify-between rounded-md px-2 py-3 transition-all",
                  {
                    "hover:bg-slate": !option.disabled,
                    "opacity-50": (isSelected || option.disabled) && !isMulti,
                  }
                )}
                onClick={() => {
                  if (isSelected) {
                    setSelectedOptions(
                      selectedOptions.filter(
                        (selectedOption) => selectedOption.id !== option.id
                      )
                    );
                  } else {
                    if (isMulti) {
                      setSelectedOptions([...selectedOptions, option]);
                    } else {
                      setSelectedOptions([option]);
                    }
                  }
                }}
              >
                <div className="flex w-full items-center gap-8">
                  <Checkbox
                    value={isSelected}
                    onChange={() => {
                      setSelectedOptions([...selectedOptions, option]);
                    }}
                  />
                  <UserAvatar
                    user={{
                      firstName: option.name,
                      lastName: null,
                      detail: option.detail,
                      profileImageUrl: option.profileImageUrl,
                    }}
                    hiddenProfilePicture={hiddenProfilePictures}
                    size="md"
                    showFullName={true}
                    showDetail={true}
                  />
                </div>
                {option.disabled && (
                  <div className="font-body-sm text-medium-gray whitespace-nowrap">
                    {option.disableReason}
                  </div>
                )}
              </button>
              <AnimatePresence initial={false} mode="sync">
                {subOption && isSelected && !subOption.isHidden && (
                  <motion.div
                    key={`suboption-${option.id}`}
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="overflow-clip pl-2"
                  >
                    <button
                      className="bg-lightest-gray-30 mb-4 w-full rounded-md p-4.5 text-black"
                      onClick={() => {
                        const selectedOption = selectedOptions.find(
                          (selectedOption) => selectedOption.id === option.id
                        );

                        setSelectedOptions([
                          {
                            ...option,
                            subOption: {
                              ...subOption,
                              isSelected:
                                !selectedOption?.subOption?.isSelected,
                            },
                          },
                        ]);
                      }}
                    >
                      <div className="font-body-sm flex items-start gap-4">
                        <div className="pt-0.5">
                          <Checkbox
                            value={
                              !!selectedOptions.find(
                                (selectedOption) =>
                                  selectedOption.id === option.id
                              )?.subOption?.isSelected
                            }
                            onChange={() => {
                              const selectedOption = selectedOptions.find(
                                (selectedOption) =>
                                  selectedOption.id === option.id
                              );

                              setSelectedOptions([
                                {
                                  ...option,
                                  subOption: {
                                    ...subOption,
                                    isSelected:
                                      !selectedOption?.subOption?.isSelected,
                                  },
                                },
                              ]);
                            }}
                          />
                        </div>
                        <div className="flex flex-col items-start gap-1">
                          <div>{subOption.title}</div>
                          <div className="text-medium-dark-gray">
                            {subOption.body}
                          </div>
                        </div>
                      </div>
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          );
        })}
      </div>
      {showError && (
        <div className="font-body-sm text-danger ml-2 pt-0.5">
          {errorMessage}
        </div>
      )}
      <Credenza.Footer>
        <Button
          variant="secondary"
          onClick={() => {
            setSelectedOptions([]);
            setOpen(false);
          }}
        >
          Cancel
        </Button>
        {options.length > 0 && (
          <Button
            type="submit"
            isLoading={isLoading}
            onClick={() => {
              if (selectedOptions.length > 0) {
                if (isMulti === true) {
                  const onMultiSelect = onSelect as (options: Option[]) => void;
                  onMultiSelect(selectedOptions);
                } else {
                  const onSingleSelect = onSelect as (options: Option) => void;
                  onSingleSelect(selectedOptions[0]);
                }
              } else {
                setShowError(true);
              }
            }}
          >
            Select
          </Button>
        )}
      </Credenza.Footer>
    </Credenza.Root>
  );
};

export default SelectModal;
