import { useState } from "react";

import { CheckBadgeIcon } from "@heroicons/react/24/solid";

import classNames from "classnames";
import ReactMarkdown from "react-markdown";
import TweetThreadAssetPreview from "./TweetThreadAssetPreview";

import { useElementSize } from "@mantine/hooks";
import uniq from "lodash/uniq";
import AnimateChangeInHeight from "~/components/AnimateChangeInHeight";
import { urlRegex } from "~/constants";
import { useWorkspace } from "~/providers";
import { charCount, getDefaultProfilePicture, removeEmptyElems } from "~/utils";
import LinkPreview from "../LinkPreview";

type Props = {
  connectedAccount?: {
    profileImageUrl: string | null;
    name: string;
    verified: boolean;
    verifiedType: string;
    isBasicVerifiedOverride: boolean;
    username: string;
  } | null;
  tweet: {
    id: string;
    copy: string;
    assets:
      | {
          signedUrl: string;
          metadata: {
            mimetype: string;
          };
        }[]
      | null;
  };
  index: number;
  numTweets: number;
};

const TweetPreview = ({ tweet, connectedAccount, index, numTweets }: Props) => {
  const { currentWorkspace } = useWorkspace();
  const { ref, height } = useElementSize();
  const [expanded, setExpanded] = useState<boolean>(false);

  const isEmptyTweet = tweet.copy.length === 0 && !tweet.assets?.length;

  const websites = uniq(tweet.copy.match(urlRegex)) || [];

  const lastWebsite = websites.length ? websites[websites.length - 1] : null;

  const copyLength = charCount(tweet.copy, {
    halfNewLines: true,
    trimStart: true,
  });
  const tCopyLength = charCount(tweet.copy, {
    isTwitter: true,
  });

  const maxLength =
    connectedAccount?.isBasicVerifiedOverride ||
    connectedAccount?.verifiedType !== "none"
      ? 4000
      : 280;

  const copy =
    tCopyLength <= maxLength
      ? tweet.copy
      : tweet.copy.slice(0, maxLength + copyLength - tCopyLength);

  const isLongerThan280 = tCopyLength > 280;
  const cleanedCopy =
    lastWebsite &&
    tweet.assets?.length === 0 &&
    !isLongerThan280 &&
    copy.endsWith(lastWebsite)
      ? copy.slice(0, -lastWebsite.length).trimEnd()
      : copy;

  const shortenedCopy = () => {
    const tweetCopy = copy.slice(0, 280);

    // Remove any partial words at the end of the shortened tweet
    const words = tweetCopy.split(" ");
    let shortenedWords = words;

    if (tweetCopy.length === 280 && !tweetCopy.endsWith(" ")) {
      // If the last word is cut off, remove it
      shortenedWords = words.slice(0, -1);
    }

    const finalTweetCopy = shortenedWords.join(" ");
    return finalTweetCopy;
  };

  const displayCopy =
    isLongerThan280 && !expanded ? shortenedCopy() : cleanedCopy;

  return (
    <div className="font-smoothing relative flex items-start gap-2">
      <img
        className="h-10 w-10 rounded-full"
        src={
          connectedAccount?.profileImageUrl ??
          getDefaultProfilePicture("Twitter")
        }
      />

      <div className="w-full">
        <div className="flex items-center gap-1">
          <div className="font-semibold text-[#0F1419]">
            {connectedAccount?.name || currentWorkspace?.name}
          </div>
          {connectedAccount &&
            (connectedAccount?.verified ||
              connectedAccount?.verifiedType != "none") && (
              <CheckBadgeIcon className="text-twitter-blue h-5 w-5" />
            )}
          {connectedAccount && (
            <div className="text-[#536471]">@{connectedAccount?.username} </div>
          )}
        </div>
        {isEmptyTweet ? (
          <div className="text-medium-gray">Empty tweet...</div>
        ) : (
          <div>
            <AnimateChangeInHeight>
              <div
                ref={ref}
                className={classNames({
                  "max-h-[240px] overflow-clip": isLongerThan280 && !expanded,
                })}
              >
                {displayCopy.split("\n").map((line, index) => {
                  if (line.length === 0) {
                    return <div key={index} className="py-3" />;
                  } else {
                    const usernameRegex = /(?<=^|[\s.,])@[a-zA-Z0-9_]{1,15}/g;
                    const usernameTags = uniq(
                      line.match(usernameRegex) || []
                    ).filter(
                      (username) =>
                        !username.startsWith("/") && !username.startsWith("-")
                    );

                    let parsedLine = line.trimEnd();

                    const websites = uniq(line.match(urlRegex)) || [];

                    websites.forEach((website) => {
                      let httpWebsite = website;
                      if (!website.startsWith("http")) {
                        httpWebsite = `http://${website}`;
                      }
                      const escapedWebsite = website.replace(
                        /[.*+?^${}()|[\]\\]/g,
                        "\\$&"
                      );
                      const websiteRegex = new RegExp(
                        `${escapedWebsite}(?!\\/|[?#])`,
                        "g"
                      );
                      parsedLine = parsedLine.replace(
                        websiteRegex,
                        `[${website}](${httpWebsite})`
                      );
                    });

                    usernameTags.forEach((usernameWithSpace) => {
                      const username = usernameWithSpace.trimStart();

                      parsedLine = parsedLine.replaceAll(
                        username,
                        `[${username}](https://twitter.com/${username})`
                      );
                    });

                    return (
                      <div
                        key={`${tweet.id}-${index}-mrkdn`}
                        className="flex flex-col gap-2"
                        dir="auto"
                      >
                        <ReactMarkdown
                          className="w-[500px] break-words text-[#0F1419]"
                          linkTarget="_blank"
                          children={parsedLine}
                          components={{
                            h1: ({ node, ...props }) => {
                              return (
                                <span>
                                  # <span {...props} />
                                </span>
                              );
                            },
                            h2: ({ node, ...props }) => {
                              return (
                                <span>
                                  ## <span {...props} />
                                </span>
                              );
                            },
                            h3: ({ node, ...props }) => {
                              return (
                                <span>
                                  ### <span {...props} />
                                </span>
                              );
                            },
                            code: ({ node, ...props }) => {
                              return (
                                <span>
                                  `<span {...props} />`
                                </span>
                              );
                            },
                            em: ({ node, ...props }) => {
                              const start = node.position?.start.column;
                              const end = node.position?.end.column;

                              if (start != null && end != null) {
                                const copy = parsedLine.slice(
                                  start - 1,
                                  end - 1
                                );
                                return <span>{copy}</span>;
                              } else {
                                return (
                                  <span>
                                    *<span {...props} />*
                                  </span>
                                );
                              }
                            },
                            strong: ({ node, ...props }) => {
                              return (
                                <span>
                                  **
                                  <span {...props} />
                                  **
                                </span>
                              );
                            },
                            blockquote: ({ node, ...props }) => {
                              const children = removeEmptyElems(
                                props.children.filter((child) => child !== "\n")
                                // @ts-expect-error props is not defined on the child
                              ).map((child) => child.props.children);
                              return (
                                <span>
                                  {">"} {children}
                                </span>
                              );
                            },
                            hr: ({ node, ...props }) => {
                              const length = node.position?.end.offset || 3;
                              return (
                                <span>
                                  {Array.from({ length }).map((_, index) => (
                                    <span key={index}>-</span>
                                  ))}
                                </span>
                              );
                            },
                            ol: ({ node, ...props }) => {
                              return (
                                <ol
                                  {...props}
                                  className="list-inside list-decimal"
                                />
                              );
                            },
                            ul: ({ node, ...props }) => {
                              // @ts-expect-error children is not defined on the node
                              const children = props.children[1].props.children;
                              return (
                                <ol {...props} className="list-inside">
                                  <li>- {children}</li>
                                </ol>
                              );
                            },
                            a: ({ node, ...props }) => {
                              return (
                                <a
                                  className="text-twitter-blue hover:decoration-twitter-blue underline-offset-2 hover:underline"
                                  {...props}
                                />
                              );
                            },
                          }}
                        />
                      </div>
                    );
                  }
                })}
              </div>
            </AnimateChangeInHeight>
            {isLongerThan280 &&
              (height >= 240 || displayCopy.length !== copy.length) && (
                <button
                  className="text-twitter-blue mt-1 hover:underline"
                  onClick={() => setExpanded(!expanded)}
                >
                  {expanded ? "Show less" : "See more"}
                </button>
              )}
            <div className="mt-2">
              {!tweet.assets?.length && (
                <div>
                  {isLongerThan280 && lastWebsite ? (
                    <div className="font-body-sm bg-lightest-gray-30 text-medium-dark-gray rounded-md p-3">
                      Note: Twitter does not include URL Preview images on
                      long-form Tweets.
                    </div>
                  ) : (
                    <LinkPreview url={lastWebsite} channel="Twitter" />
                  )}
                </div>
              )}
              <TweetThreadAssetPreview assets={tweet.assets} />
            </div>
            {/* All of the icons below the tweet */}
            <div className="mt-3 flex items-center justify-between">
              <svg viewBox="0 0 24 24" className="h-5 w-5 fill-[#536471]">
                <path d="M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01zm8.005-6c-3.317 0-6.005 2.69-6.005 6 0 3.37 2.77 6.08 6.138 6.01l.351-.01h1.761v2.3l5.087-2.81c1.951-1.08 3.163-3.13 3.163-5.36 0-3.39-2.744-6.13-6.129-6.13H9.756z"></path>
              </svg>
              <svg viewBox="0 0 24 24" className="h-5 w-5 fill-[#536471]">
                <path d="M4.5 3.88l4.432 4.14-1.364 1.46L5.5 7.55V16c0 1.1.896 2 2 2H13v2H7.5c-2.209 0-4-1.79-4-4V7.55L1.432 9.48.068 8.02 4.5 3.88zM16.5 6H11V4h5.5c2.209 0 4 1.79 4 4v8.45l2.068-1.93 1.364 1.46-4.432 4.14-4.432-4.14 1.364-1.46 2.068 1.93V8c0-1.1-.896-2-2-2z"></path>
              </svg>
              <svg viewBox="0 0 24 24" className="h-5 w-5 fill-[#536471]">
                <path d="M16.697 5.5c-1.222-.06-2.679.51-3.89 2.16l-.805 1.09-.806-1.09C9.984 6.01 8.526 5.44 7.304 5.5c-1.243.07-2.349.78-2.91 1.91-.552 1.12-.633 2.78.479 4.82 1.074 1.97 3.257 4.27 7.129 6.61 3.87-2.34 6.052-4.64 7.126-6.61 1.111-2.04 1.03-3.7.477-4.82-.561-1.13-1.666-1.84-2.908-1.91zm4.187 7.69c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z"></path>
              </svg>
              <svg viewBox="0 0 24 24" className="h-5 w-5 fill-[#536471]">
                <path d="M8.75 21V3h2v18h-2zM18 21V8.5h2V21h-2zM4 21l.004-10h2L6 21H4zm9.248 0v-7h2v7h-2z"></path>
              </svg>
              <svg viewBox="0 0 24 24" className="h-5 w-5 fill-[#536471]">
                <path d="M12 2.59l5.7 5.7-1.41 1.42L13 6.41V16h-2V6.41l-3.3 3.3-1.41-1.42L12 2.59zM21 15l-.02 3.51c0 1.38-1.12 2.49-2.5 2.49H5.5C4.11 21 3 19.88 3 18.5V15h2v3.5c0 .28.22.5.5.5h12.98c.28 0 .5-.22.5-.5L19 15h2z"></path>
              </svg>
            </div>
          </div>
        )}
      </div>
      <div
        className={classNames(
          "absolute top-10 left-5 -ml-px h-full w-0.5 bg-[#CFD9DE]",
          { "opacity-0": index === numTweets - 1 }
        )}
      />
    </div>
  );
};

export default TweetPreview;
