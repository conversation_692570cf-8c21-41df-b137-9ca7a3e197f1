import axios from "axios";
import { Dub } from "dub";
import { google } from "googleapis";
import he from "he";
import { Metada<PERSON>, parser } from "html-metadata-parser";
import { parse } from "node-html-parser";
import { db } from "~/clients";

// https://www.linkedin.com/post-inspector/
const getMetadataFromLinkedIn = async (
  url: string
): Promise<{
  title: string | null;
  description: string | null;
  image: string | null;
  siteName: string | null;
  hostname: string | null;
}> => {
  try {
    const response = await axios.get(
      `https://www.linkedin.com/post-inspector-api/postInspector/${encodeURIComponent(
        url
      )}`,
      {
        maxBodyLength: Infinity,
        headers: {
          "Csrf-Token": "ajax:3123530247722623987",
          Cookie: `bcookie="v=2&69351523-481f-4332-8713-b1fa0a7c96f5"; bscookie="v=1&202406182334140591cc65-0778-4bd7-8b6c-6e9d16acb8b2AQHI7OjJhWLMvXp2GR2CdQPa2X5IBkBS"; li_gp=MTsxNzE4NzUzNjU0OzA=; li_sugr=37a04989-e28b-4342-8ce5-152d61b449f9; dfpfpt=8f89058ea58c47b2a47a29784d4ff793; at_check=true; s_cc=true; PLAY_LANG=en; li_theme=light; li_theme_set=app; sdsc=22%3A1%2C1721400686093%7EJAPP%2C0Jj7L%2FtiZEmGtbMw5XVxv8xeHWeQ%3D; PLAY_SESSION=eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7InNlc3Npb25faWQiOiI1YzRkMTliMy03ZTRlLTQ2OGEtYjFmOC01YzY4ZjY2MDVlMzJ8MTcyMzQ4NzI5OSIsImFsbG93bGlzdCI6Int9IiwicmVjZW50bHktc2VhcmNoZWQiOiIiLCJyZWZlcnJhbC11cmwiOiJodHRwczovL3d3dy5saW5rZWRpbi5jb20vaGVscC9saW5rZWRpbj9zcmM9ZGlyZWN0JTJGbm9uZSZ2ZWg9ZGlyZWN0JTJGbm9uZSU3Q2RpcmVjdCUyRm5vbmUiLCJyZWNlbnRseS12aWV3ZWQiOiIiLCJDUFQtaWQiOiIzw47Dri7ClcKcw7PCiHAwblrCllLDnCUiLCJmbG93VHJhY2tpbmdJZCI6Im9NcG9sT3RtUmlpek5DK3dBRHdzT1E9PSIsImV4cGVyaWVuY2UiOiIiLCJ0cmsiOiIifSwibmJmIjoxNzIzNDkwNTU3LCJpYXQiOjE3MjM0OTA1NTd9.Q2SAvTIj-6lpkeYT7dG1m3yCt8bv3kzbnhc1K37ZrdU; li_rm=AQFKV3J1ec9jXgAAAZFrRGO0os6QDwgyqsHJGxruOW5LJCWMfPaJFdtfHDpAaxzpDp1iY-XuRprBxTdBCyV4HEl_dWnPwiQQg_D9ktWH5ZrYFZ2CqPhpNjp4; visit=v=1&M; lang=v=2&lang=en-us; JSESSIONID="ajax:3123530247722623987"; liap=true; _guid=038e294d-eca7-4b4d-977c-6da345ed83ba; s_fid=42D3649B58615371-05F6D8AD98EFFB1A; timezone=America/Chicago; s_plt=2.19; s_pltp=www.linkedin.com%2Fsearch%2Fresults%2Fall%2F; s_ips=1096; gpv_pn=www.linkedin.com%2Fcompany%2Fid-redacted%2Fadmin%2Fdashboard%2F; s_tslv=1729697492868; s_sq=lnkdprod%3D%2526c.%2526a.%2526activitymap.%2526page%253Dwww.linkedin.com%25252Fcompany%25252Fid-redacted%25252Fadmin%25252Fdashboard%25252F%2526link%253DPage%252520posts%2526region%253Dorg-sidebar%2526pageIDType%253D1%2526.activitymap%2526.a%2526.c%2526pid%253Dwww.linkedin.com%25252Fcompany%25252Fid-redacted%25252Fadmin%25252Fdashboard%25252F%2526pidt%253D1%2526oid%253Dhttps%25253A%25252F%25252Fwww.linkedin.com%25252Fcompany%25252F38090873%25252Fadmin%25252Fpage-posts%25252Fpublished%2526ot%253DA; s_tp=2235; s_ppv=www.linkedin.com%2Fcompany%2Fid-redacted%2Fadmin%2Fdashboard%2F%2C51%2C49%2C1150.4444465637207%2C1%2C2; li_at=AQEDAQrMvQEA9dASAAABkWxgXYQAAAGS-LcdgVYASa5j7gx6ocwHnf-FHqEdliiz1cPRJ4zB58CJrWgWjDhNbflAFCNEKpViV8H2M0Vl99Py0-xORr-jn86tH4bNbYu955U3WGnv421dO0nD_VO6RedT; AnalyticsSyncHistory=AQJHQZIARr1qnwAAAZLUqp8GYvMBz6BvTzN9Sit95nOioJP6WqZX3uS8ARCWfM3LCVZ0uep5Q83IPoyypCTOhw; lms_ads=AQFe1szW9aL6ZAAAAZLUqp-sePCH-cQt-3dYpVkXETcX4SJI1_5nA4RkLDTHk2HKlZcLCHOqNTG6SHyeOb-qP7PIg8CFt4CZ; lms_analytics=AQFe1szW9aL6ZAAAAZLUqp-sePCH-cQt-3dYpVkXETcX4SJI1_5nA4RkLDTHk2HKlZcLCHOqNTG6SHyeOb-qP7PIg8CFt4CZ; fptctx2=taBcrIH61PuCVH7eNCyH0HyAAKgSb15ZEqidLg30r8OypVmrYgcKogKglmIAwxbdL%252fIyZDfvDW0J7IP%252bFhuFi98IEc9CLwBR3iyTg0Zz91VxULXcE%252bUEDefFLOqBtatw6shIxkH6hqOOa3dZyYl%252byOixJCjjiJKPHK4M0q7UR%252f9ArXFurc5MPTUR6nPyPlW7ciUKmasOKpgi5O4PJ54U%252bbsfU82okLWTergnDcW8X9okWj7nUHgjhV8XGN7IS1MHzNL4cNI%252bnYoClD16MLMQ0MJ0ZPPbbBogYyQ7ykFN1e%252fzC%252bUZzE9U%252b%252b%252fglotz2EXzcjJULKCM0esYnGx0ZCV%252f7O3%252bwfK2Fdl6TvdRSFGKRC0%253d; UserMatchHistory=AQIQ-gp7c0u1RAAAAZLZiUanW_da4icFN3dSapFwCHpe642LgDcC9u_PtiIo6Svb5yYHiV9b3_wFYPFTUEKcN7fAQ83sC8FXdzycIRS2gMdNLNeCuTRMtNQxWUI7Um2ZVK3zUvj_YNxYlgPNIw3-MSMdPyaxEwIZNqqFZ4ZYgzIYHMQ9i3pCd2TL7iUkD3LhomK1lWBXhbdSfm9_RX7QNm-OVvRjFR6pv4p-YBxvEAqq7N-h3mH8f0gucJaC_omIou2LRjWnWYBEeF1yU1iukvTlwWa1dQBl1fSdop0pGlGqqYZ21mf4eV6RrjGmJYAlnYTkjbnHUMRNLMprx2G7lREgaAUqLwV9suE3aodlNhQuhKl0nA; lidc="b=OB89:s=O:r=O:a=O:p=O:g=3967:u=1515:x=1:i=1730226505:t=1730288972:v=2:sig=AQE7sWumZsZf8PRPKVIA-145KGGMLEyD"; li_gpc=1`,
          Referer: "https://www.linkedin.com/post-inspector/inspect/",
          "X-Restli-Protocol-Version": "2.0.0",
          Accept: "*/*",
          Host: "www.linkedin.com",
          "Accept-Encoding": "gzip, deflate, br",
          Connection: "keep-alive",
          "User-Agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        },
      }
    );
    const data = response.data as {
      rawIngestedContentMetadata: {
        provider: {
          "com.linkedin.ingestedcontent.ProviderInfo": {
            name: string;
            url: string;
          };
        };
        specificMetadata: {
          "com.linkedin.ingestedcontent.RawVideoSpecificMetadata":
            | {
                thumbnails: {
                  "com.linkedin.ingestedcontent.AnnotatedImageMetadata": {
                    primaries: {
                      value: {
                        resolvedUrl: string;
                      };
                    }[];
                  };
                };
              }
            | undefined;
          "com.linkedin.ingestedcontent.RawArticleSpecificMetadata":
            | {
                images: {
                  "com.linkedin.ingestedcontent.AnnotatedImageMetadata": {
                    primaries: {
                      value: {
                        resolvedUrl: string;
                        alternateText: string;
                      };
                    }[];
                  };
                };
              }
            | undefined;
        };
        title: {
          "com.linkedin.ingestedcontent.internal.AnnotatedString": {
            primary: {
              value: string;
            };
          };
        };
      };
    };

    const title =
      data.rawIngestedContentMetadata.title[
        "com.linkedin.ingestedcontent.internal.AnnotatedString"
      ].primary.value;

    const rawVideoSpecificMetadata =
      data.rawIngestedContentMetadata.specificMetadata[
        "com.linkedin.ingestedcontent.RawVideoSpecificMetadata"
      ];
    const rawArticleSpecificMetadata =
      data.rawIngestedContentMetadata.specificMetadata[
        "com.linkedin.ingestedcontent.RawArticleSpecificMetadata"
      ];

    const image = rawVideoSpecificMetadata
      ? rawVideoSpecificMetadata.thumbnails[
          "com.linkedin.ingestedcontent.AnnotatedImageMetadata"
        ].primaries[0].value.resolvedUrl
      : rawArticleSpecificMetadata
        ? rawArticleSpecificMetadata.images[
            "com.linkedin.ingestedcontent.AnnotatedImageMetadata"
          ].primaries[0].value.resolvedUrl
        : null;

    const providerInfo =
      data.rawIngestedContentMetadata.provider[
        "com.linkedin.ingestedcontent.ProviderInfo"
      ];
    const siteName = providerInfo.name;
    let hostname = providerInfo.url;
    try {
      hostname = new URL(providerInfo.url).hostname;
    } catch (error) {
      console.log("Error parsing hostname", error);
    }

    return {
      title,
      description: null,
      image,
      siteName,
      hostname,
    };
  } catch (error) {
    console.log(error);
    return {
      title: null,
      description: null,
      image: null,
      siteName: null,
      hostname: null,
    };
  }
};

const getHeadChildNodes = (html: string) => {
  const ast = parse(html); // parse the html into AST format with node-html-parser
  const metaTags = ast.querySelectorAll("meta").map(({ attributes }) => {
    const property = attributes.property || attributes.name || attributes.href;
    return {
      property,
      content: attributes.content,
    };
  });
  const title = ast.querySelector("title")?.innerText;
  const linkTags = ast.querySelectorAll("link").map(({ attributes }) => {
    const { rel, href } = attributes;
    return {
      rel,
      href,
    };
  });

  return { metaTags, title, linkTags };
};

const getMetadataFromDubCo = async (url: string) => {
  try {
    const dub = new Dub({
      token: process.env.DUB_API_KEY,
    });
    const result = await dub.metatags.get({
      url,
    });

    return {
      ...result,
      siteName: null,
      hostname: new URL(url).hostname,
    };
  } catch (error) {
    return null;
  }
};

const getMetadataFromYoutube = async (url: string) => {
  try {
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXT_PUBLIC_DOMAIN}/settings/youtube/callback`
    );
    const youtubeIntegration = await db.youTubeIntegration.findFirstOrThrow({
      orderBy: {
        expiresAt: "desc",
      },
    });

    oauth2Client.setCredentials({
      access_token: youtubeIntegration.accessToken,
      refresh_token: youtubeIntegration.refreshToken,
      id_token: youtubeIntegration.idToken,
    });

    const youtube = google.youtube({
      version: "v3",
      auth: oauth2Client,
    });

    let videoID = url.split("/").pop() as string;
    if (videoID?.includes("watch")) {
      videoID = videoID.split("watch?v=")[1];
    } else {
      videoID = videoID.split("?")[0];
    }

    const response = await youtube.videos.list({
      part: ["snippet"],
      id: [videoID],
    });

    const video = response.data.items?.[0];

    if (video == null) {
      return null;
    }

    return {
      title: video.snippet?.title || null,
      description: video.snippet?.description || null,
      image: video.snippet?.thumbnails?.standard?.url || null,
      siteName: null,
      hostname: new URL(url).hostname,
    };
  } catch (error) {
    console.error(error);
    return null;
  }
};

const getMetaTags = async (url: string) => {
  const controller = new AbortController();
  const timeoutID = setTimeout(() => controller.abort(), 5000); // 5 seconds timeout

  const html = await fetch(url, {
    signal: controller.signal,
    headers: {
      "User-Agent": "Dub.co Bot",
    },
  })
    .then((res) => res.text())
    .catch((error) => {
      if (error.name === "AbortError") {
        console.log("Request timed out");
      }
      return null;
    })
    .finally(() => {
      clearTimeout(timeoutID);
    });

  if (!html) {
    return {
      title: url,
      description: "No description",
      image: null,
      siteName: null,
      hostname: new URL(url).hostname,
    };
  }
  const { metaTags, title: titleTag, linkTags } = getHeadChildNodes(html);

  let object: { [property: string]: string } = {};

  for (let k in metaTags) {
    let { property, content } = metaTags[k];

    // !object[property] → (meaning we're taking the first instance of a metatag and ignoring the rest)
    property &&
      !object[property] &&
      (object[property] = content && he.decode(content));
  }

  for (let m in linkTags) {
    let { rel, href } = linkTags[m];

    // !object[rel] → (ditto the above)
    rel && !object[rel] && (object[rel] = href);
  }

  const title = object["og:title"] || object["twitter:title"] || titleTag;

  const description =
    object["description"] ||
    object["og:description"] ||
    object["twitter:description"];

  let image =
    object["og:image"] ||
    object["twitter:image"] ||
    object["image_src"] ||
    object["icon"] ||
    object["shortcut icon"];

  if (url.includes("loom.com")) {
    if (object["preload"]) {
      image = object["preload"];
    }
  }

  return {
    title: title || url,
    description: description || "No description",
    image: image,
    siteName: object["og:site_name"] || object["twitter:site"] || url,
    hostname: new URL(url).hostname,
  };
};

const getParsedMetadata = async (
  url: string
): Promise<{
  title: string;
  description: string | null;
  image: string | null;
  siteName: string | null;
  hostname: string | null;
} | null> => {
  try {
    if (url.includes("bit.ly")) {
      const response = await axios.get(url);
      url = response.request.res.responseUrl;
    }

    const { hostname: hostnameWithWww } = new URL(url);
    const hostname = hostnameWithWww.replace("www.", "");

    let scrapedMetadata: {
      title: string | null;
      description: string | null;
      image: string | null;
      siteName: string | null;
      hostname: string;
    } | null = null;

    if (url.includes("loom.com")) {
      scrapedMetadata = await getMetaTags(url);
    }

    if (url.includes("youtu.be") || url.includes("youtube.com/watch")) {
      scrapedMetadata = await getMetadataFromYoutube(url);
    }

    if (url.includes("bloomberg.com")) {
      scrapedMetadata = await getMetadataFromDubCo(url);
    }

    if (scrapedMetadata != null) {
      return {
        ...scrapedMetadata,
        // Ensures that the title is not null
        title: scrapedMetadata.title || scrapedMetadata.hostname,
      };
    }

    // If the url redirects then we want to grab the metadata from the initial URL which
    // is why we set maxRedirects to 0 and the catch block handles grabbing from original URL
    try {
      const metadata: Metadata = await parser(url, {
        timeout: 5_000,
        maxRedirects: 0,
      });

      console.log(metadata);

      const scrapedMetadata = {
        title: metadata.meta.title || metadata.og.title || hostname,
        description:
          metadata.meta.description || metadata.og.description || null,
        image:
          metadata.og.image ||
          metadata.meta.image ||
          // @ts-ignore this is right
          (metadata.images && metadata.images[0]?.src) ||
          null,
        siteName:
          metadata.meta.site_name || metadata.og.site_name || url || null,
        hostname,
      };
      return scrapedMetadata;
    } catch (error) {
      console.log(error);
      return await getMetaTags(url);
    }
  } catch (error) {
    console.log(error);
    return null;
  }
};

const getLinkMetadata = async (url: string | null) => {
  if (url == null) {
    return null;
  }

  let httpUrl = url;
  if (!url.startsWith("http")) {
    httpUrl = `http://${url}`;
  }
  if (url.endsWith("/")) {
    httpUrl = url.slice(0, url.length - 1);
  }

  const httpResponse = await getParsedMetadata(httpUrl);

  if (httpResponse != null) {
    return httpResponse;
  }

  if (url.startsWith("https://")) {
    const httpUrl = url.replace("https://", "http://");
    const httpUrlResponse = await getParsedMetadata(httpUrl);

    return httpUrlResponse;
  }
};

export default getLinkMetadata;
