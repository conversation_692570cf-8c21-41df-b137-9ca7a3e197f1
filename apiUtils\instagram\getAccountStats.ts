import { InstagramIntegrationSource } from "@prisma/client";
import { DateTime } from "luxon";
import { prisma } from "~/clients";
import Instagram from "~/clients/facebook/instagram/Instagram";

type Integration = {
  id: string;
  accessToken: string;
  integrationSource: InstagramIntegrationSource;
  account: {
    id: string;
    username: string;
    externalID: string;
    stats: {
      followerCount: number | null;
      followCount: number | null;
      recordedAt: Date;
    }[];
  };
};

const getAccountStats = async (integration: Integration) => {
  console.log(`@${integration.account.username} querying stats`);
  try {
    const instagram = new Instagram(integration.accessToken, {
      platform: integration.integrationSource,
    });
    let timeframeDays = 29;
    const stats = integration.account.stats;

    // Get the highest count from all of the stats
    const followerCount = stats
      .map((stat) => stat.followerCount || 0)
      .sort((a, b) => b - a)[0];

    // Get the oldest stat that has a null follow count
    const oldestNullFollowCount = stats
      .filter((stat) => stat.followCount == null)
      .sort((a, b) => a.recordedAt.getTime() - b.recordedAt.getTime())[0];

    // If there is an old null follow count, then we need to backfill to that date
    if (oldestNullFollowCount != null) {
      timeframeDays = Math.ceil(
        Math.abs(
          DateTime.fromJSDate(oldestNullFollowCount.recordedAt).diffNow("days")
            .days
        )
      );
    }

    if (followerCount < 100) {
      timeframeDays = 1;
    }

    const response = await instagram.getFollowsAndUnfollowsTimeframe({
      instagramAccountID: integration.account.externalID,
      timeframeDays,
    });

    if (response.data) {
      const followCount =
        response.data.followsAndUnfollows[
          response.data.followsAndUnfollows.length - 1
        ].followCount;
      const unfollowCount =
        response.data.followsAndUnfollows[
          response.data.followsAndUnfollows.length - 1
        ].unfollowCount;
      const followerCount = response.data.followerCount;
      const followingCount = response.data.followingCount;

      console.log(
        `@${integration.account.username} followCount: ${followCount}, unfollowCount: ${unfollowCount}, followerCount: ${followerCount}, followingCount: ${followingCount}`
      );

      const { count: numCreated } =
        await prisma.instagramAccountStats.createMany({
          data: response.data.followsAndUnfollows.map((stat, index) => {
            if (index === response.data.followsAndUnfollows.length - 1) {
              return {
                accountID: integration.account.id,
                followCount: stat.followCount,
                unfollowCount: stat.unfollowCount,
                followerCount: response.data.followerCount,
                followingCount: response.data.followingCount,
              };
            } else {
              return {
                accountID: integration.account.id,
                followCount: stat.followCount,
                unfollowCount: stat.unfollowCount,
                recordedAt: stat.recordedAt,
              };
            }
          }),
          skipDuplicates: true,
        });

      // For each of the stats that were created, update the follow and unfollow count
      let index = 0;
      for await (const stat of response.data.followsAndUnfollows) {
        if (index === response.data.followsAndUnfollows.length - 1) {
          await prisma.instagramAccountStats.update({
            where: {
              accountID_recordedAt: {
                accountID: integration.account.id,
                recordedAt: new Date(),
              },
            },
            data: {
              followCount,
              unfollowCount,
              followerCount,
              followingCount,
            },
          });
        } else {
          await prisma.instagramAccountStats.update({
            where: {
              accountID_recordedAt: {
                accountID: integration.account.id,
                recordedAt: stat.recordedAt,
              },
            },
            data: {
              followCount: stat.followCount,
              unfollowCount: stat.unfollowCount,
            },
          });
        }
        index++;
      }

      return {
        username: integration.account.username,
        error: null,
        count: numCreated,
      };
    } else {
      console.log(
        `@${integration.account.username} error getting account stats`
      );
      console.log(response.errors);

      if (response.isAuthError) {
        await prisma.instagramIntegration.update({
          where: {
            id: integration.id,
          },
          data: {
            isReintegrationRequired: true,
          },
        });
      }

      return {
        username: integration.account.username,
        error: response.errors,
        count: 0,
      };
    }
  } catch (error) {
    console.error(error);
    return {
      username: integration.account.username,
      error: error,
    };
  }
};

export default getAccountStats;
