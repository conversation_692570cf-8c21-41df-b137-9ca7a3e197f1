import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Blockquote from "@tiptap/extension-blockquote";
import Bold from "@tiptap/extension-bold";
import BulletList from "@tiptap/extension-bullet-list";
import Code from "@tiptap/extension-code";
import Document from "@tiptap/extension-document";
import Heading, { Level } from "@tiptap/extension-heading";
import { History } from "@tiptap/extension-history";
import Italic from "@tiptap/extension-italic";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import Underline from "@tiptap/extension-underline";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { ClipboardEvent, useEffect, useRef, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import { EDITOR_CHANNELS } from "~/constants";
import { usePostIntegrations, useThreadComments } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { handleTrpcError, stripLiveblocksMarks, trpc } from "~/utils";
import ThreadCommentsPopover from "../posts/ThreadCommentsPopover";
import { Threads } from "./Threads";
import {
  CustomCommentThreadMark,
  Emoji,
  Link,
  PasteHandler,
} from "./extensions";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  title?: string;
  initialValue?: JSONContent | null;
  placeholder: string;
  isReadOnly?: boolean;
  onSave: (editorState: JSONContent) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  roomID: string;
};

type OtherChannelEditorFallbackProps = {
  content: JSONContent | null;
  placeholder: string;
};

const headingLevels: Level[] = [1, 2, 3, 4, 5, 6];

const createBaseExtensions = (placeholder: string) => [
  Document,
  Paragraph,
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  Text,
  Heading.configure({
    levels: headingLevels,
  }),
  History,
  BulletList,
  OrderedList,
  ListItem,
  Bold,
  Italic,
  Code,
  Underline,
  Blockquote,
  Emoji,
  Link.configure({
    openOnClick: true,
  }),
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  PasteHandler,
];

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const OtherChannelEditorFallback = ({
  content,
  placeholder,
}: OtherChannelEditorFallbackProps) => {
  const cleanContent = stripLiveblocksMarks(content);

  const editor = useEditor({
    extensions: createBaseExtensions(placeholder),
    immediatelyRender: false,
    content: cleanContent,
    editable: false,
  });

  if (!editor) return null;

  return <EditorContent editor={editor} />;
};

const OtherChannelEditor = ({
  title,
  initialValue,
  placeholder,
  isReadOnly,
  onSave,
  onImagePaste,
  roomID,
}: BaseProps) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue || null
  );
  const { setEditorForRoom } = useThreadComments();
  const { currentChannel } = usePostIntegrations();

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(placeholder),
    ],
    onUpdate: ({ editor }) => {
      setEditorState(editor.getJSON());
    },
    immediatelyRender: false,
    editable: !isReadOnly,
  });

  const isEditorReady = useIsEditorReady();

  useEffect(() => {
    if (editor && !EDITOR_CHANNELS.includes(currentChannel)) {
      setEditorForRoom(editor, roomID);
    }

    return () => {
      setEditorForRoom(null, roomID);
    };
  }, [editor, currentChannel, setEditorForRoom, roomID]);

  if (!editor) return null;

  return (
    <div
      className="py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor}>
        <ToolbarActions
          editor={editor}
          features={{
            headings: headingLevels,
            bulletList: true,
            orderedList: true,
            link: true,
          }}
        />
      </CustomFloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}

      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["Other"].label,
            }}
          />
          <ClientSideSuspense fallback={null}>
            <Threads editor={editor} />
          </ClientSideSuspense>
        </>
      ) : (
        <OtherChannelEditorFallback
          content={initialValue || null}
          placeholder={placeholder}
        />
      )}
    </div>
  );
};

const OtherChannelEditorWrapperNewDesign = ({
  title,
  initialValue,
  placeholder,
  isReadOnly,
  onSave,
  onImagePaste,
  roomID,
}: BaseProps) => {
  const { currentChannel } = usePostIntegrations();
  const threadCommentsContainerRef = useRef<HTMLDivElement>(null);

  const getOrCreateRoomMutation = trpc.liveblocks.getOrCreateRoom.useMutation({
    onError: (error) => {
      handleTrpcError({
        title: "Failed to create room",
        error,
      });
    },
  });

  useEffect(() => {
    if (!isReadOnly) {
      getOrCreateRoomMutation.mutateAsync({ roomID: roomID });
    }

    return () => {
      getOrCreateRoomMutation.reset();
    };
  }, [isReadOnly, roomID]);

  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense
        fallback={
          <div className="py-2">
            {title && (
              <div className="font-eyebrow text-dark-gray">{title}</div>
            )}
            <OtherChannelEditorFallback
              content={initialValue || null}
              placeholder={placeholder}
            />
          </div>
        }
      >
        <div className="relative">
          <OtherChannelEditor
            title={title}
            initialValue={initialValue}
            placeholder={placeholder}
            isReadOnly={isReadOnly}
            onSave={onSave}
            onImagePaste={onImagePaste}
            roomID={roomID}
          />
          <div ref={threadCommentsContainerRef} />
        </div>
      </ClientSideSuspense>
      {!EDITOR_CHANNELS.includes(currentChannel) && (
        <ThreadCommentsPopover
          key={roomID}
          roomID={roomID}
          containerRef={threadCommentsContainerRef}
        />
      )}
    </RoomProvider>
  );
};

export default OtherChannelEditorWrapperNewDesign;
