import { NextApiRequest, NextApiResponse } from "next";
import cors from "nextjs-cors";
import { createApiContext } from "server/apiRouters/apiContext";
import { apiRouter } from "server/apiRouters/v1/_api";
import { createOpenApiNextHandler } from "trpc-openapi";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  // Setup CORS
  await cors(req, res);

  // Handle incoming OpenAPI requests
  return createOpenApiNextHandler({
    router: apiRouter,
    createContext: createApiContext,
    onError: () => {},
    responseMeta: () => ({}),
  })(req, res);
};

export default handler;
