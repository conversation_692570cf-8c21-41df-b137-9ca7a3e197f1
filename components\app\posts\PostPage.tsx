import { useEffect } from "react";

import classNames from "classnames";
import { useRouter } from "next/router";
import { <PERSON><PERSON>, Loader } from "~/components";
import { CommentsAndActivity } from "~/components/app";
import { PostHeader, PostSidebar } from "~/components/app/posts";
import CopyAndDesigns from "~/components/app/posts/CopyAndDesigns";
import { useWindowTitle } from "~/hooks";
import { useWorkspace } from "~/providers";
import { trpc } from "~/utils";
import { WebflowField } from "./webflow/WebflowFieldInput";

type Props = {
  id: string;
};

const PostPage = ({ id }: Props) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();

  const { workspaces, currentWorkspace, setCurrentWorkspace } = useWorkspace();

  const postQuery = trpc.post.get.useQuery(
    {
      id,
    },
    {
      refetchInterval: false,
      refetchIntervalInBackground: false,
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    }
  );

  const data = postQuery.data;
  const post = data?.post || null;
  const isReadOnly = data?.isReadOnly;

  useEffect(() => {
    if (data) {
      if (
        currentWorkspace?.id !== data.post.workspaceID &&
        !data.post.isSharedPublicly
      ) {
        const newWorkspace = workspaces.find(
          (workspace) => workspace.id === data.post.workspaceID
        );
        if (newWorkspace) {
          setCurrentWorkspace(newWorkspace);
        } else {
          throw new Error("Unauthorized");
        }
      }

      trpcUtils.dub.getPostLinks.prefetch({
        postID: data.post.id,
      });

      const path = router.asPath;
      if (path.includes("/posts") && data.post.isIdea) {
        router.push(
          path.replace("/posts", "/ideas"),
          path.replace("/posts", "/ideas"),
          {
            shallow: true,
          }
        );
      } else if (path.includes("/ideas") && !data.post.isIdea) {
        router.push(
          path.replace("/ideas", "/posts"),
          path.replace("/ideas", "/posts"),
          {
            shallow: true,
          }
        );
      }
    }
  }, [data]);

  useEffect(() => {
    if (id && post && id !== post.id) {
      router.reload();
    }
  }, [id]);

  useWindowTitle(post ? `${post.title}` : "Assembly");

  if (!!postQuery.error) {
    if (postQuery.error.data?.code === "UNAUTHORIZED") {
      router.push("/login");
    }

    return (
      <div className="w-full">
        <div className="mx-auto mt-4 max-w-lg">
          <Alert
            title="Unable to Load Post Data"
            intent="danger"
            message={postQuery.error.message}
          />
        </div>
      </div>
    );
  }

  if (!post || id == null || isReadOnly == null) {
    return (
      <div className="flex h-full w-full items-center justify-center px-10 py-4">
        <Loader size="lg" />
      </div>
    );
  }

  return (
    <div
      className={classNames("h-full w-full pb-3", {
        "px-4 md:px-8": !router.asPath.includes("/inbox"),
      })}
    >
      <div className="flex h-full w-full gap-10">
        <div className="md:border-lightest-gray scrollbar-hidden h-full w-full space-y-3 overflow-y-auto md:mt-3 md:border-r md:pr-8">
          <PostHeader post={post} isReadOnly={isReadOnly} />
          <CopyAndDesigns
            post={{
              ...post,
              webflowContent: post.webflowContent && {
                ...post.webflowContent,
                fields: post.webflowContent.fields.map((field) => {
                  return field as WebflowField;
                }),
              },
            }}
            isReadOnly={isReadOnly}
          />
          <div className="pt-12">
            <CommentsAndActivity
              id={id}
              type="post"
              comments={post.comments}
              activity={post.activity}
              isReadOnly={isReadOnly}
            />
          </div>
        </div>
        <div className="hidden pt-3 md:block">
          <PostSidebar post={post} isReadOnly={isReadOnly} />
        </div>
      </div>
    </div>
  );
};

export default PostPage;
