import { PrismaClient } from "@prisma/client";
import { withAccelerate } from "@prisma/extension-accelerate";
import { JSONContent } from "@tiptap/core";
import { enhance } from "@zenstackhq/runtime";
import { fieldEncryptionExtension } from "prisma-field-encryption";
import { getDefaultProfilePicture } from "~/utils";

const prismaClientSingleton = () => {
  const prisma = new PrismaClient({
    transactionOptions: {
      timeout: 20_000,
    },
  })
    .$extends({
      name: "default-profile-image",
      result: {
        tikTokAccount: {
          profileImageUrl: {
            needs: { profileImageUrl: true },
            compute(account) {
              return (
                account.profileImageUrl ?? getDefaultProfilePicture("TikTok")
              );
            },
          },
        },
        facebookPage: {
          profileImageUrl: {
            needs: { profileImageUrl: true },
            compute(account) {
              return (
                account.profileImageUrl ?? getDefaultProfilePicture("Facebook")
              );
            },
          },
        },
        instagramAccount: {
          profileImageUrl: {
            needs: { profileImageUrl: true },
            compute(account) {
              return (
                account.profileImageUrl ?? getDefaultProfilePicture("Instagram")
              );
            },
          },
        },
        linkedInAccount: {
          profileImageUrl: {
            needs: { profileImageUrl: true, type: true },
            compute(account) {
              return (
                account.profileImageUrl ??
                getDefaultProfilePicture("LinkedIn", account.type)
              );
            },
          },
        },
        linkedInScrapedAccountData: {
          profileImageUrl: {
            needs: { profileImageUrl: true },
            compute(account) {
              return (
                account.profileImageUrl ??
                getDefaultProfilePicture("LinkedIn", "User")
              );
            },
          },
        },
        threadsAccount: {
          profileImageUrl: {
            needs: { profileImageUrl: true },
            compute(account) {
              return (
                account.profileImageUrl ?? getDefaultProfilePicture("Threads")
              );
            },
          },
        },
        twitterAccount: {
          profileImageUrl: {
            needs: { profileImageUrl: true },
            compute(account) {
              return (
                account.profileImageUrl ?? getDefaultProfilePicture("Twitter")
              );
            },
          },
        },
        youTubeChannel: {
          profileImageUrl: {
            needs: { profileImageUrl: true },
            compute(channel) {
              return (
                channel.profileImageUrl ??
                getDefaultProfilePicture("YouTubeShorts")
              );
            },
          },
        },
      },
    })
    .$extends(fieldEncryptionExtension())
    .$extends(withAccelerate())
    .$extends({
      name: "default-profile-images",
      result: {
        tikTokAccount: {
          profileImageUrl: {
            needs: { profileImageUrl: true },
            compute(account) {
              return (
                account.profileImageUrl ?? getDefaultProfilePicture("TikTok")
              );
            },
          },
        },
      },
    })
    .$extends({
      result: {
        post: {
          notesEditorState: {
            needs: { notesEditorState: true },
            compute(post) {
              return post.notesEditorState as JSONContent | null;
            },
          },
          otherChannelEditorState: {
            needs: { otherChannelEditorState: true },
            compute(post) {
              return post.otherChannelEditorState as JSONContent | null;
            },
          },
        },
        campaign: {
          overviewEditorState: {
            needs: { overviewEditorState: true },
            compute(campaign) {
              return campaign.overviewEditorState as JSONContent | null;
            },
          },
        },
        discordContent: {
          editorState: {
            needs: { editorState: true },
            compute(content) {
              return content.editorState as JSONContent | null;
            },
          },
        },
        facebookContent: {
          editorState: {
            needs: { editorState: true },
            compute(content) {
              return content.editorState as JSONContent | null;
            },
          },
        },
        facebookPostEngagement: {
          editorState: {
            needs: { editorState: true },
            compute(engagement) {
              return engagement.editorState as JSONContent | null;
            },
          },
        },
        instagramContent: {
          editorState: {
            needs: { editorState: true },
            compute(content) {
              return content.editorState as JSONContent | null;
            },
          },
        },
        instagramPostEngagement: {
          editorState: {
            needs: { editorState: true },
            compute(engagement) {
              return engagement.editorState as JSONContent | null;
            },
          },
        },
        linkedInContent: {
          editorState: {
            needs: { editorState: true },
            compute(content) {
              return content.editorState as JSONContent | null;
            },
          },
        },
        linkedInPostEngagement: {
          editorState: {
            needs: { editorState: true },
            compute(engagement) {
              return engagement.editorState as JSONContent | null;
            },
          },
        },
        tikTokContent: {
          editorState: {
            needs: { editorState: true },
            compute(content) {
              return content.editorState as JSONContent | null;
            },
          },
        },
        slackContent: {
          editorState: {
            needs: { editorState: true },
            compute(content) {
              return content.editorState as JSONContent | null;
            },
          },
        },
        threadsContent: {
          editorState: {
            needs: { editorState: true },
            compute(content) {
              return content.editorState as JSONContent | null;
            },
          },
        },
        tweetContent: {
          editorState: {
            needs: { editorState: true },
            compute(content) {
              return content.editorState as JSONContent | null;
            },
          },
        },
        twitterPostEngagement: {
          editorState: {
            needs: { editorState: true },
            compute(engagement) {
              return engagement.editorState as JSONContent | null;
            },
          },
        },
        webflowContent: {
          editorState: {
            needs: { editorState: true },
            compute(content) {
              return content.editorState as JSONContent | null;
            },
          },
        },
        youTubeShortsContent: {
          titleEditorState: {
            needs: { titleEditorState: true },
            compute(content) {
              return content.titleEditorState as JSONContent | null;
            },
          },
          descriptionEditorState: {
            needs: { descriptionEditorState: true },
            compute(content) {
              return content.descriptionEditorState as JSONContent | null;
            },
          },
        },
      },
    });

  return prisma;
};

type PrismaClientSingleton = ReturnType<typeof prismaClientSingleton>;

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClientSingleton | undefined;
  db: PrismaClientSingleton | undefined;
};

const prisma = globalForPrisma.prisma ?? prismaClientSingleton();
const db = globalForPrisma.db ?? (enhance(prisma) as PrismaClientSingleton);

const PRISMA_CACHE_TAGS = {
  findFirstOrThrow_authUser: "findFirstOrThrow_authUser",
};

export default prisma;
export { db, PRISMA_CACHE_TAGS };

if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;
  globalForPrisma.db = db;
}
