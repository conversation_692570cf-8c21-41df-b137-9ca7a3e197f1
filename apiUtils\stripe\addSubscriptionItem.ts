import { db } from "~/clients";
import stripe from "~/clients/stripe";
import { PRICE_LOOKUP_KEYS } from "~/constants";

const itemNameLimitMap = {
  ADDITIONAL_SEAT: "seatLimit" as const,
  ADDITIONAL_SOCIAL_PROFILE: "socialProfileLimit" as const,
  ADDITIONAL_ENGAGEMENT_PROFILE: "engagementProfileLimit" as const,
};

/**
 * Three cases:
 * 1. Single Workspace
 *    - Update in Stripe
 *    - Update workspace limit
 *    - Stripe webhooks will reconsolidate the subscription
 *
 * 2. Multiple Workspaces with subscription
 *    - Update workspace limit
 *    - Update company limit
 *    - Update in Stripe
 *
 * 3. Multiple Workspaces without subscription
 *    - Update workspace limit
 *    - Send Slack for us to update billing accordingly
 */
const addSubscriptionItem = async ({
  workspaceID,
  itemName,
  quantity,
}: {
  workspaceID: string;
  itemName: keyof typeof itemNameLimitMap;
  quantity: number;
}) => {
  const workspace = await db.workspace.findFirstOrThrow({
    where: {
      id: workspaceID,
    },
    include: {
      subscription: true,
      company: {
        include: {
          subscription: true,
          _count: {
            select: {
              workspaces: true,
            },
          },
        },
      },
    },
  });

  // Update the subscription in Stripe if it exists
  const companySubscription = workspace.company.subscription;
  if (companySubscription) {
    const billingPeriod = companySubscription.billingPeriod;
    const extraSeatLookupKey = PRICE_LOOKUP_KEYS[billingPeriod][itemName];

    const { data: prices } = await stripe.prices.list({
      lookup_keys: [extraSeatLookupKey],
    });
    const price = prices[0];

    const { data: subscriptionItems } = await stripe.subscriptionItems.list({
      subscription: companySubscription.stripeSubscriptionID,
    });

    const existingSubscriptionItem = subscriptionItems.find(
      (subscriptionItem) => subscriptionItem.price.id === price.id
    );
    if (existingSubscriptionItem) {
      await stripe.subscriptionItems.update(existingSubscriptionItem.id, {
        quantity: (existingSubscriptionItem.quantity || 1) + quantity,
        proration_behavior: "always_invoice",
      });
    } else {
      await stripe.subscriptionItems.create({
        subscription: companySubscription.stripeSubscriptionID,
        proration_behavior: "always_invoice",
        price: price.id,
        quantity,
      });
    }
  }

  const limitFieldName = itemNameLimitMap[itemName];

  if (workspace.subscription) {
    const newLimit = workspace.subscription[limitFieldName] + quantity;

    await db.workspaceSubscription.update({
      where: {
        workspaceID,
      },
      data: {
        [limitFieldName]: newLimit,
      },
    });
    return;
  }
};

export default addSubscriptionItem;
