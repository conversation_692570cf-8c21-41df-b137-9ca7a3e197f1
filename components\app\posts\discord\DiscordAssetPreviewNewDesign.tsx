import Uppy from "@uppy/core";
import { arrayMoveImmutable } from "array-move";
import classNames from "classnames";
import { motion } from "framer-motion";
import { useMemo, useState } from "react";
import AssetUploadContainer from "~/components/ui/AssetUploadContainer";
import { AssetType } from "~/types/Asset";
import { handleFilesUpload, trpc } from "~/utils";
import EditImageModal, {
  EditImageModalProperties,
} from "~/utils/EditImageModal";
import { AssetsUploadReorderControls } from "../../files/AssetControls";
import AssetsNewDesign, { UploadingAsset } from "../../files/AssetsNewDesign";
import FileDropzone from "../../files/FileDropzone";
import FileUploadInput from "../../files/FileUploadInput";

type Props = {
  contentID: string;
  assets: AssetType[];
  setAssets: (
    value: AssetType[] | ((prev: AssetType[]) => AssetType[])
  ) => void;
  uploadingAssets: UploadingAsset[];
  draggedOver: boolean;
  setDraggedOver: (isDragged: boolean) => void;
  uppy: Uppy | null;
  getValidFiles: (files: File[]) => Promise<File[]>;
  isReadOnly: boolean;
};

const DiscordAssetPreviewNewDesign = ({
  contentID,
  assets,
  setAssets,
  uploadingAssets,
  draggedOver,
  setDraggedOver,
  uppy,
  getValidFiles,
  isReadOnly,
}: Props) => {
  const randomID = useMemo(() => Math.random().toString(36), []);
  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);
  const [isEditingAssets, setIsEditingAssets] = useState<boolean>(false);

  const numAssets = assets.length;
  const updateAssetPositionsMutation =
    trpc.discordContent.updateAssetPositions.useMutation();
  const deleteAssetMutation = trpc.discordContent.deleteAsset.useMutation();

  const onSortEnd = (oldIndex: number, newIndex: number) => {
    setAssets((assets) => {
      const newAssets = arrayMoveImmutable(assets, oldIndex, newIndex);

      updateAssetPositionsMutation.mutate({
        assets: newAssets.map((asset, index) => ({
          id: asset.id,
          position: index,
        })),
      });

      return newAssets;
    });
  };

  const acceptedFileTypesMap = {
    "image/jpg": [".jpg", ".jpeg"],
    "image/png": [".png"],
    "image/gif": [".gif"],
    "video/mp4": [".mp4"],
    "video/quicktime": [".mov"],
  };

  const acceptedMimeTypes = Object.keys(acceptedFileTypesMap);

  const handleUploadButtonClick = () => {
    const fileInput = document.getElementById(
      `file-upload-${randomID}`
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  };

  const onEditClick = (asset: AssetType) => {
    if (asset.metadata.mimetype.startsWith("image/")) {
      setEditImageModalProperties({
        title: "Edit Image",
        channel: "Discord",
        isOpen: true,
        asset,
      });
    }
  };

  const onDeleteClick = (asset: AssetType) => {
    setAssets((prevAssets) =>
      prevAssets.filter((prevAsset) => prevAsset.id !== asset.id)
    );
    deleteAssetMutation.mutate({
      discordAssetID: asset.id,
    });
  };

  const renderContent = () => {
    const uploadedAssets = assets;
    const newUploadingAssets = uploadingAssets.filter(
      (asset) => !asset.updateAssetID
    );

    if (isEditingAssets) {
      return (
        <AssetsNewDesign
          assets={assets}
          uploadingAssets={uploadingAssets}
          onDeleteClick={(asset) => {
            setAssets((prevAssets) =>
              prevAssets.filter((prevAsset) => prevAsset.id !== asset.id)
            );
            deleteAssetMutation.mutate({
              discordAssetID: asset.id,
            });
          }}
          editImageModalProperties={editImageModalProperties}
          onEditClick={(asset) => {
            if (asset.metadata.mimetype.startsWith("image/")) {
              setEditImageModalProperties({
                title: "Edit Image",
                channel: "Discord",
                isOpen: true,
                asset,
              });
            }
          }}
          onUpload={async (file, updateAssetID) => {
            if (uppy) {
              if (updateAssetID) {
                uppy.addFile({
                  name: file.name,
                  type: file.type,
                  data: file,
                  meta: {
                    contentID,
                    updateAssetID,
                  },
                });
              } else {
                await handleFilesUpload({
                  files: [file],
                  uppy,
                  meta: {
                    contentID,
                  },
                  getValidFiles,
                });
              }
            }
          }}
          onEditImageModalClose={() => setEditImageModalProperties(null)}
          refetchAssets={() => {}}
          isReadOnly={isReadOnly}
          onDrop={async (files) => {
            setDraggedOver(false);
            if (uppy) {
              await handleFilesUpload({
                files,
                uppy,
                meta: {
                  contentID,
                },
                getValidFiles,
              });
            }
          }}
          showDropzone={draggedOver}
          acceptedFileTypes={acceptedFileTypesMap}
          onSortEnd={onSortEnd}
          showNewEditButton={true}
          onCloseClick={() => setIsEditingAssets(false)}
          onUploadButtonClick={handleUploadButtonClick}
        />
      );
    }

    const displayAssets = [...uploadedAssets, ...newUploadingAssets];
    if (displayAssets.length === 0) {
      return null;
    } else if (displayAssets.length === 1) {
      const asset = displayAssets[0];
      return (
        <AssetUploadContainer
          asset={asset}
          className="h-full w-full rounded-[8px]"
          style={{ aspectRatio: "1/1" }}
          isReadOnly={isReadOnly}
          onEditClick={onEditClick}
          onDeleteClick={onDeleteClick}
        />
      );
    } else if (displayAssets.length === 2) {
      return (
        <div className="flex items-center gap-0.5 overflow-clip rounded-[8px]">
          <AssetUploadContainer
            asset={displayAssets[0]}
            className="h-[230px] w-[230px] rounded-[3px] object-cover"
            style={{ aspectRatio: "1/1" }}
            isReadOnly={isReadOnly}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
            isSmall={true}
          />
          <AssetUploadContainer
            asset={displayAssets[1]}
            className="h-[230px] w-[230px] rounded-[3px] object-cover"
            style={{ aspectRatio: "1/1" }}
            isReadOnly={isReadOnly}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
          />
        </div>
      );
    } else if (displayAssets.length === 3) {
      return (
        <div className="grid h-full grid-cols-2 gap-0.5 overflow-clip rounded-[8px]">
          <AssetUploadContainer
            asset={displayAssets[0]}
            className="h-[350px] w-full rounded-[3px] object-cover object-top"
            style={{ aspectRatio: "1/1" }}
            isReadOnly={isReadOnly}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
            isSmall={true}
          />
          <div className="flex h-[350px] flex-col gap-0.5">
            <div className="flex h-[175px] items-center">
              <AssetUploadContainer
                asset={displayAssets[1]}
                className="h-[175px] w-full rounded-[3px] object-cover object-center"
                style={{ aspectRatio: "1/1" }}
                isReadOnly={isReadOnly}
                onEditClick={onEditClick}
                onDeleteClick={onDeleteClick}
              />
            </div>
            <div className="flex h-[175px] items-center">
              <AssetUploadContainer
                asset={displayAssets[2]}
                className="h-[175px] w-full rounded-[3px] object-cover object-top"
                style={{ aspectRatio: "1/1" }}
                isReadOnly={isReadOnly}
                onEditClick={onEditClick}
                onDeleteClick={onDeleteClick}
              />
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <>
      <FileUploadInput
        inputID={`file-upload-${randomID}`}
        contentID={contentID}
        uppy={uppy}
        getValidFiles={getValidFiles}
        acceptedMimeTypes={acceptedMimeTypes}
        isDisabled={uploadingAssets.length > 0}
        multiple={true}
      />
      {draggedOver ? (
        <motion.div
          key="dropzone"
          className="mb-3.5 w-full"
          initial={{ height: 338, opacity: 0 }}
          animate={{
            height: 338,
            opacity: 100,
          }}
        >
          <FileDropzone
            onDrop={async (files) => {
              setDraggedOver(false);
              if (uppy) {
                await handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID,
                  },
                  getValidFiles,
                });
              }
            }}
            acceptedFileTypes={acceptedFileTypesMap}
            uploadProgress={
              uploadingAssets.length > 0
                ? Math.round(
                    uploadingAssets.reduce(
                      (sum, asset) => sum + (asset.uploadProgress || 0),
                      0
                    ) / uploadingAssets.length
                  )
                : undefined
            }
          />
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{
            opacity: 100,
          }}
          className={classNames({
            hidden: assets.length === 0 && uploadingAssets?.length === 0,
          })}
        >
          <div className="group/asset-controls relative -ml-8 opacity-100 transition-all sm:-ml-0">
            {renderContent()}
            {assets.length > 0 && !isEditingAssets && (
              <AssetsUploadReorderControls
                onUploadClick={handleUploadButtonClick}
                onReorderClick={() => setIsEditingAssets(true)}
                isReadOnly={isReadOnly}
              />
            )}
          </div>
        </motion.div>
      )}
      {editImageModalProperties && (
        <EditImageModal
          title={editImageModalProperties.title}
          asset={editImageModalProperties.asset}
          maxSizeMB={editImageModalProperties.maxSizeMB}
          channel={editImageModalProperties.channel}
          isOpen={editImageModalProperties.isOpen}
          onClose={() => setEditImageModalProperties(null)}
          onUpload={async (file) => {
            if (uppy && editImageModalProperties.asset) {
              uppy.addFile({
                name: file.name,
                type: file.type,
                data: file,
                meta: {
                  contentID,
                  updateAssetID: editImageModalProperties.asset.id,
                },
              });
            }
          }}
        />
      )}
    </>
  );
};

export default DiscordAssetPreviewNewDesign;
