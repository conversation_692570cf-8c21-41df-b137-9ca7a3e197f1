import axios from "axios";

import { StatusCodes } from "http-status-codes";
import { createErrorResponse, Response } from "../clients/Response";
const catchAxiosError = (
  error: unknown,
  errorFomatter: (data: any) => string
): Response<any> => {
  console.error(error);
  if (axios.isAxiosError(error)) {
    const response = error.response;

    return createErrorResponse(
      [errorFomatter(response?.data)],
      response?.status || StatusCodes.UNPROCESSABLE_ENTITY,
      response?.data
    );
  } else {
    return createErrorResponse(
      ["Error making query"],
      StatusCodes.UNPROCESSABLE_ENTITY
    );
  }
};

export default catchAxiosError;
