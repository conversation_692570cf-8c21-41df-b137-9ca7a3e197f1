import { sentryMiddleware } from "@inngest/middleware-sentry";
import {
  LinkedInAccountType,
  LinkedInLeadWebhookChannel,
} from "@prisma/client";
import { EventSchemas, Inngest, slugify } from "inngest";
import { z } from "zod";
import { Channel } from "~/types";

const postEngagementEvent = {
  data: z.object({
    delayUntil: z.date(),
    engagementID: z.string().uuid(),
    externalPostID: z.string(),
    permalink: z.string(),
  }),
};

const eventsMap = {
  "linkedin.account.connected": {
    data: z.object({
      urn: z.string(),
      vanityName: z.string(),
      accountType: z.nativeEnum(LinkedInAccountType),
    }),
  },
  "linkedin.getFollowerCountAndPosts": {
    data: z.object({
      batchID: z.number(),
      vanityNames: z.array(z.string()),
    }),
  },
  "linkedin.scrapePostEngagements": {
    data: z.object({
      scrapingJobID: z.string().uuid(),
    }),
  },
  "linkedin.scrapeProfile": {
    data: z.object({
      encryptedUrn: z.string(),
    }),
  },
  "linkedin.postEngagement": postEngagementEvent,
  "twitter.postEngagement": postEngagementEvent,
  "instagram.postEngagement": postEngagementEvent,
  "facebook.postEngagement": postEngagementEvent,
  "post.scheduled": {
    data: z.object({
      postID: z.string(),
      channel: Channel,
      delayUntil: z.date(),
    }),
  },
  "post.rescheduled": {
    data: z.object({
      postID: z.string().uuid(),
      channel: Channel,
      delayUntil: z.date(),
    }),
  },
  "post.unscheduled": {
    data: z.object({
      postID: z.string().uuid(),
      channel: Channel,
      scheduledPostID: z.string().uuid(),
    }),
  },
  "post.webflow.previewed": {
    data: z.object({
      webflowIntegrationID: z.string().uuid(),
      collectionID: z.string(),
      itemID: z.string(),
      delayUntil: z.date(),
    }),
  },
  "post.webflow.previewUnpublished": {
    data: z.object({
      itemID: z.string(),
    }),
  },
  "post.deleted": {
    data: z.object({
      postID: z.string().uuid(),
    }),
  },
  "referral.created": {
    data: z.object({
      referralID: z.string().uuid(),
    }),
  },
  "referral.subscribed": {
    data: z.object({
      referralID: z.string().uuid(),
    }),
  },
  "svix.sendMessage": {
    data: z.object({
      applicationID: z.string().uuid(),
      eventType: z.string(),
      channels: z.array(z.nativeEnum(LinkedInLeadWebhookChannel)),
      payload: z.object({}),
    }),
  },
  "workspace.created": {
    data: z.object({
      workspaceID: z.string().uuid(),
    }),
  },
  "video.convertCodecs": {
    data: z.object({
      userID: z.string().uuid(),
      assetID: z.string().uuid(),
      audioCodec: z.string().nullable(),
      videoCodec: z.string().nullable(),
      channel: Channel,
    }),
  },
};

export const inngest = new Inngest({
  id: slugify("Assembly"),
  schemas: new EventSchemas().fromZod(eventsMap),
  middleware: [sentryMiddleware()],
});
