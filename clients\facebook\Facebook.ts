import { FacebookStatusType } from "@prisma/client";
import getAssetBuffer from "apiUtils/getAssetBuffer";
import assert from "assert";
import axios, { AxiosInstance } from "axios";
import FormData from "form-data";
import { StatusCodes } from "http-status-codes";
import uniq from "lodash/uniq";
import { DateTime } from "luxon";
import { urlRegex } from "~/constants";
import { removeEmptyElems, sleep } from "~/utils";
import {
  Response,
  createErrorResponse,
  createSuccessResponse,
} from "../Response";
import { FBPost, RawFBPost } from "./FacebookTypes";
import catchFacebookError from "./catchFacebookError";
import Instagram from "./instagram/Instagram";

type Status = "not_started" | "in_progress" | "complete" | "error";
type Phase =
  | {
      status: "complete" | "not_started" | "in_progress";
      errors?: never;
    }
  | {
      status: "error";
      errors: {
        code: number;
        message: string;
      }[];
    };

interface FacebookInsight {
  name: string;
  period: string;
  values: { value: number; end_time: string }[];
}

class Facebook {
  BASE_URL = "https://graph.facebook.com/v20.0";

  accessToken: string;

  client: AxiosInstance;

  protected _instagram?: Instagram;

  constructor(accessToken: string) {
    this.client = axios.create({
      baseURL: this.BASE_URL,
    });
    this.accessToken = accessToken;
  }

  getAccountByID = async (
    facebookPageID: string
  ): Promise<
    Response<{
      id: string;
      name: string;
      username: string | null;
      category: string;
      url: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
      instagramAccount: {
        id: string;
        name: string | null;
        username: string;
        profileImageUrl: string | null;
        profileImageExpiresAt: Date | null;
      } | null;
    }>
  > => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      fields: [
        "id",
        "name",
        "username",
        "category",
        "picture",
        "link",
        `instagram_business_account{${[
          "id",
          "name",
          "username",
          "profile_picture_url",
        ].join(",")}}`,
      ].join(","),
    });

    try {
      const response = await this.client.get(`/${facebookPageID}?${params}`);
      const { status } = response;

      const data: {
        id: string;
        name: string;
        username?: string;
        category: string;
        link: string;
        picture?: {
          data: {
            url: string;
          };
        };
        instagram_business_account?: {
          id: string;
          name: string;
          username: string;
          profile_picture_url: string;
        };
      } = response.data;

      const { id, name, username, category, picture, link } = data;

      const instagramAccount = data.instagram_business_account
        ? {
            id: data.instagram_business_account.id,
            name: data.instagram_business_account.name || null,
            username: data.instagram_business_account.username,
            profileImageUrl:
              data.instagram_business_account.profile_picture_url,
            profileImageExpiresAt: data.instagram_business_account
              .profile_picture_url
              ? DateTime.now().plus({ days: 3 }).toJSDate()
              : null,
          }
        : null;

      return createSuccessResponse(
        {
          id,
          name,
          username: username || null,
          category,
          url: link,
          profileImageUrl: picture?.data.url || null,
          profileImageExpiresAt: data.picture
            ? DateTime.now().plus({ days: 3 }).toJSDate()
            : null,
          instagramAccount,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  _convertRawPost = (rawPost: RawFBPost): FBPost => {
    const caption = rawPost.message || null;
    const permalinkUrl = rawPost.permalink_url;
    const statusType = rawPost.status_type.toUpperCase() as FacebookStatusType;
    const mediaAttachments = rawPost.attachments?.data || [];

    const imageUrls: string[] = [];
    const videoUrls: string[] = [];

    mediaAttachments.forEach((mediaAttachment) => {
      if (mediaAttachment.subattachments?.data) {
        mediaAttachment.subattachments.data.forEach((subAttachment) => {
          if (subAttachment.media?.image) {
            imageUrls.push(subAttachment.media.image.src);
          }
          if (subAttachment.media?.source) {
            videoUrls.push(subAttachment.media.source);
          }
        });
      } else {
        if (mediaAttachment.media?.image) {
          imageUrls.push(mediaAttachment.media.image.src);
        }
        if (mediaAttachment.media?.source) {
          videoUrls.push(mediaAttachment.media.source);
        }
      }
    });

    const messageTags = rawPost.message_tags?.map((tag) => tag.name) || [];
    const messageTagsCount = messageTags.length;
    const hasEvent = !!rawPost.event;
    const isPopular = rawPost.is_popular;
    const isHidden = rawPost.is_hidden;
    const isPublished = rawPost.is_published;
    const isSpherical = rawPost.is_spherical;
    const impressionCount =
      rawPost.insights?.data.find(
        (insight) => insight.name === "post_impressions"
      )?.values[0].value ?? 0;
    const shareCount = rawPost.shares?.count ?? 0;
    const reactionCount =
      rawPost.insights?.data.find(
        (insight) => insight.name === "post_reactions_like_total"
      )?.values[0].value ?? 0;
    const commentCount = rawPost.comments?.data.length ?? 0;

    return {
      id: rawPost.id,
      caption,
      permalinkUrl,
      statusType,
      imageUrls,
      videoUrls,
      messageTags,
      messageTagsCount,
      hasEvent,
      isPopular,
      isHidden,
      isPublished,
      isSpherical,
      impressionCount,
      shareCount,
      reactionCount,
      commentCount,
      publishedAt: new Date(rawPost.created_time),
      rawResponse: rawPost,
    };
  };

  // https://developers.facebook.com/docs/graph-api/reference/page/published_posts/
  getPagePosts = async (
    facebookPageID: string,
    filters: {
      since?: number;
    }
  ): Promise<Response<FBPost[]>> => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      fields: [
        "id",
        "message",
        "message_tags",
        "permalink_url",
        "status_type",
        "event",
        "is_popular",
        "is_hidden",
        "is_published",
        "is_spherical",
        "shares",
        "comments",
        "attachments",
        "created_time",
        `insights.metric(${[
          "post_impressions",
          "post_reactions_like_total",
        ].join(",")}).period(lifetime)`,
      ].join(","),
      limit: "100",
      period: "lifetime",
      since: filters.since
        ? filters.since.toString()
        : DateTime.now().minus({ years: 2 }).toUnixInteger().toString(),
    });

    try {
      const response = await this.client.get(
        `/${facebookPageID}/published_posts?${params}`
      );

      const postResponse: RawFBPost[] = response.data.data;

      const posts = postResponse.map((rawPost) => {
        return this._convertRawPost(rawPost);
      });

      return createSuccessResponse(posts, response.status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/graph-api/reference/page/insights/#page-engagement
  getFollowsAndUnfollows = async ({
    facebookPageID,
    timeframeDays,
  }: {
    facebookPageID: string;
    timeframeDays: number;
  }): Promise<
    Response<{
      followsAndUnfollows: {
        followCount: number | null;
        unfollowCount: number | null;
        recordedAt: Date;
      }[];
      followerCount: number | null;
    }>
  > => {
    try {
      const params = new URLSearchParams({
        access_token: this.accessToken,
        metric:
          "page_daily_follows_unique,page_daily_unfollows_unique,page_follows",
        period: "day",
        since: DateTime.now()
          .setZone("CST")
          .minus({ days: timeframeDays })
          .startOf("day")
          .toUnixInteger()
          .toString(),
        until: DateTime.now()
          .setZone("CST")
          .minus({ days: 1 })
          .endOf("day")
          .toUnixInteger()
          .toString(),
      });

      const {
        status,
        data: { data: insights },
      } = await this.client.get(`/${facebookPageID}/insights?${params}`);

      const followerData = insights.find(
        (insight: FacebookInsight) => insight.name === "page_follows"
      );

      // If we don't have permissions, we receive an empty data array,
      // so we return a 403 error
      if (!followerData?.values?.length) {
        return createSuccessResponse(
          {
            followsAndUnfollows: [
              {
                followCount: null,
                unfollowCount: null,
                recordedAt: DateTime.now()
                  .setZone("CST")
                  .startOf("day")
                  .plus({ hours: 12 })
                  .toJSDate(),
              },
            ],
            followerCount: null,
          },
          StatusCodes.FORBIDDEN
        );
      }

      const followerCount = followerData?.values?.[0].value ?? 0;

      const followsData = insights.find(
        (insight: FacebookInsight) =>
          insight.name === "page_daily_follows_unique"
      );
      const unfollowsData = insights.find(
        (insight: FacebookInsight) =>
          insight.name === "page_daily_unfollows_unique"
      );

      const followsAndUnfollows = followsData.values.map(
        (item: { value: number; end_time: string }, index: number) => ({
          followCount: followsData.values[index].value || 0,
          unfollowCount: unfollowsData.values[index].value || 0,
          recordedAt: DateTime.fromISO(item.end_time)
            .startOf("day")
            .plus({ hours: 12 })
            .toJSDate(),
        })
      );

      return createSuccessResponse(
        {
          followsAndUnfollows,
          followerCount,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/graph-api/reference/page/insights/#page-impressions
  // https://developers.facebook.com/docs/graph-api/reference/page/insights/#page-fans
  getPageLikesAndImpressions = async ({
    facebookPageID,
    timeframeDays,
  }: {
    facebookPageID: string;
    timeframeDays: number;
  }): Promise<
    Response<{
      likeCount: number | null;
      pageImpressions: number | null;
    }>
  > => {
    try {
      const params = new URLSearchParams({
        access_token: this.accessToken,
        metric: "page_fans,page_impressions",
        period: "day",
        since: DateTime.now()
          .setZone("CST")
          .minus({ days: timeframeDays })
          .startOf("day")
          .toUnixInteger()
          .toString(),
        until: DateTime.now()
          .setZone("CST")
          .endOf("day")
          .toUnixInteger()
          .toString(),
      });

      const {
        status,
        data: { data: insights },
      } = await this.client.get(`/${facebookPageID}/insights?${params}`);

      const likesData = insights.find(
        (insight: FacebookInsight) => insight.name === "page_fans"
      );
      const impressionsData = insights.find(
        (insight: FacebookInsight) => insight.name === "page_impressions"
      );

      if (!likesData || !impressionsData) {
        return createErrorResponse(
          ["Unable to fetch page likes and impressions"],
          StatusCodes.FORBIDDEN
        );
      }

      const likeCount = likesData?.values?.[0].value ?? 0;

      const impressions = impressionsData.values.map(
        (item: { value: number; end_time: string }) => ({
          count: item.value || null,
          recordedAt: DateTime.fromISO(item.end_time)
            .startOf("day")
            .plus({ hours: 12 })
            .toJSDate(),
        })
      );

      const totalImpressions =
        impressions && impressions.length > 0
          ? impressions.reduce(
              (sum: number, day: { count: number | null; recordedAt: Date }) =>
                sum + (day.count ?? 0),
              0
            )
          : null;

      return createSuccessResponse(
        {
          likeCount,
          pageImpressions: totalImpressions,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  getInstagramAccount = async (
    facebookPageID: string
  ): Promise<
    Response<{
      id: string;
      facebookPageID: string;
      name: string | null;
      username: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
    }>
  > => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      fields: [
        `instagram_business_account{${[
          "id",
          "name",
          "username",
          "profile_picture_url",
        ].join(",")}`,
      ].join(","),
    });

    try {
      const { status, data } = await this.client.get(
        `/${facebookPageID}?${params}`
      );

      return createSuccessResponse(
        {
          id: data.instagram_business_account.id,
          facebookPageID,
          name: data.instagram_business_account.name || null,
          username: data.instagram_business_account.username,
          profileImageUrl: data.instagram_business_account.profile_picture_url,
          profileImageExpiresAt: data.instagram_business_account
            .profile_picture_url
            ? DateTime.now().plus({ days: 3 }).toJSDate()
            : null,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  getCurrentPage = async (): Promise<
    Response<{
      id: string;
      name: string;
      username: string | null;
      category: string;
      url: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
      instagramAccount: {
        id: string;
        name: string | null;
        username: string;
        profileImageUrl: string | null;
        profileImageExpiresAt: Date | null;
      } | null;
    }>
  > => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      fields: [
        "id",
        "name",
        "username",
        "category",
        "picture",
        "link",
        `instagram_business_account{${[
          "id",
          "name",
          "username",
          "profile_picture_url",
        ].join(",")}}`,
      ].join(","),
    });

    try {
      const response = await this.client.get(`/me?${params}`);
      const { status } = response;

      const data: {
        id: string;
        name: string;
        username?: string;
        category: string;
        link: string;
        picture?: {
          data: {
            url: string;
          };
        };
        instagram_business_account?: {
          id: string;
          name: string;
          username: string;
          profile_picture_url: string;
        };
      } = response.data;

      const { id, name, username, category, picture, link } = data;

      const instagramAccount = data.instagram_business_account
        ? {
            id: data.instagram_business_account.id,
            name: data.instagram_business_account.name || null,
            username: data.instagram_business_account.username,
            profileImageUrl:
              data.instagram_business_account.profile_picture_url,
            profileImageExpiresAt: data.instagram_business_account
              .profile_picture_url
              ? DateTime.now().plus({ days: 3 }).toJSDate()
              : null,
          }
        : null;

      return createSuccessResponse(
        {
          id,
          name,
          username: username || null,
          category,
          url: link,
          profileImageUrl: picture?.data.url || null,
          profileImageExpiresAt: data.picture
            ? DateTime.now().plus({ days: 3 }).toJSDate()
            : null,
          instagramAccount,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/graph-api/reference/photo#Creating
  uploadPhoto = async ({
    accountID,
    url,
  }: {
    accountID: string;
    url: string;
  }): Promise<Response<{ [url: string]: string }>> => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
    });

    try {
      const { status, data } = await this.client.post(
        `/${accountID}/photos?${params}`,
        {
          url,
          published: false,
        }
      );

      return createSuccessResponse(
        {
          [url]: data.id,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  uploadPhotos = async ({
    accountID,
    urls,
  }: {
    accountID: string;
    urls: string[];
  }): Promise<Response<{ [url: string]: string }>> => {
    if (urls.length === 0) {
      return createErrorResponse(
        ["No photos to upload"],
        StatusCodes.BAD_REQUEST
      );
    }

    const promises = urls.map(
      async (url) =>
        await this.uploadPhoto({
          accountID,
          url,
        })
    );

    const responses = await Promise.all(promises);

    const erroredResponses = responses.filter(
      (response) => response.errors !== null
    );
    const errors = removeEmptyElems(
      erroredResponses.map((response) => response.errors)
    ).flat();
    if (errors.length > 0) {
      const errorStatus = erroredResponses[0].status;

      return createErrorResponse(errors, errorStatus);
    } else {
      // Combine all of the data in to one object
      const data = responses.reduce((acc, result) => {
        assert(result.data);
        return {
          ...acc,
          ...result.data,
        };
      }, {});

      return createSuccessResponse(data, StatusCodes.CREATED);
    }
  };

  // https://developers.facebook.com/docs/pages/publishing#publish-a-page-post
  publishMessage = async ({
    accountID,
    caption,
  }: {
    accountID: string;
    caption?: string | null;
  }): Promise<Response<{ id: string; url: string }>> => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
    });

    try {
      const websites = uniq((caption || "").match(urlRegex)) || [];
      const lastWebsite = websites.pop();

      const { status, data } = await this.client.post(
        `/${accountID}/feed?${params}`,
        {
          message: caption,
          link: lastWebsite,
        }
      );

      return createSuccessResponse(
        {
          id: data.id,
          url: `https://www.facebook.com/${data.id}`,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/graph-api/reference/page/videos/#Creating
  publishVideo = async ({
    accountID,
    url,
    caption,
    coverPhotoUrl,
  }: {
    accountID: string;
    url: string;
    caption?: string | null;
    coverPhotoUrl?: string | null;
  }): Promise<Response<{ id: string; url: string }>> => {
    try {
      const form = new FormData();

      if (coverPhotoUrl) {
        const fileContent = await getAssetBuffer(coverPhotoUrl);

        form.append("thumb", Buffer.from(fileContent), {
          filename: "thumbnail.jpg",
        });
      }

      const { status, data } = await this.client.post(
        `/${accountID}/videos`,
        form,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
          params: {
            access_token: this.accessToken,
            file_url: url,
            published: true,
            description: caption,
          },
        }
      );

      return createSuccessResponse(
        {
          id: data.id,
          url: `https://www.facebook.com/watch/?v=${data.id}`,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  getVideoStatus = async ({
    videoID,
  }: {
    videoID: string;
  }): Promise<
    Response<{
      copyright_check_status: { status: Status };
      video_status: Status;
      uploading_phase: Phase;
      processing_phase: Phase;
      publishing_phase: Phase;
    }>
  > => {
    try {
      const videoStatusParams = new URLSearchParams({
        access_token: this.accessToken,
        fields: "status",
      });

      const statusResponse = await this.client.get(
        `/${videoID}?${videoStatusParams}`
      );

      const statusData = statusResponse.data as {
        status: {
          copyright_check_status: { status: Status };
          video_status: Status;
          uploading_phase: Phase;
          processing_phase: Phase;
          publishing_phase: Phase;
        };
      };

      if (statusResponse.status !== StatusCodes.OK) {
        return createErrorResponse(
          ["Failed to check upload status"],
          statusResponse.status
        );
      }

      return createSuccessResponse(statusData.status, statusResponse.status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/video-api/guides/reels-publishing
  publishReel = async ({
    accountID,
    videoUrl,
    caption,
    coverPhotoUrl,
  }: {
    accountID: string;
    videoUrl: string;
    caption: string;
    coverPhotoUrl?: string | null;
  }): Promise<Response<{ id: string; url: string }>> => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
    });

    try {
      // 1. Initialize the upload
      console.log(`${accountID} Initializing Reel Upload: ${caption}`);
      const initResponse = await this.client.post(
        `/${accountID}/video_reels?${params}`,
        {
          upload_phase: "start",
          access_token: this.accessToken,
        }
      );

      if (initResponse.status !== StatusCodes.OK) {
        console.log(
          `Init Error (${initResponse.status}): ${initResponse.data}`
        );
        return createErrorResponse(
          ["Failed to initialize upload"],
          initResponse.status
        );
      }

      const initData = initResponse.data as {
        video_id: string;
        upload_url: string;
      };

      const videoID = initData.video_id;
      const uploadUrl = initData.upload_url;

      // 2. Upload the video
      console.log(`${accountID} Uploading Reel: ${caption}`);
      const uploadResponse = await axios.post(uploadUrl, undefined, {
        headers: {
          Authorization: `OAuth ${this.accessToken}`,
          file_url: videoUrl,
        },
      });

      if (!uploadResponse.data.success === true) {
        console.log(
          `Upload Error (${uploadResponse.status}): ${uploadResponse.data}`
        );
        return createErrorResponse(
          ["Failed to upload video"],
          uploadResponse.status
        );
      }

      // 3. Wait for video to finish uploading
      console.log(`${accountID} Waiting for Reel Upload: ${caption}`);
      const videoStatusParams = new URLSearchParams({
        access_token: this.accessToken,
        fields: "status",
      });

      let {
        status: videoStatusStatus,
        data: videoStatus,
        errors: videoStatusErrors,
      } = await this.getVideoStatus({ videoID });
      if (!videoStatus) {
        return createErrorResponse(
          videoStatusErrors || ["Error querying video status"],
          videoStatusStatus
        );
      }

      let readyToPublish = videoStatus.uploading_phase.status === "complete";
      while (!readyToPublish) {
        await sleep(1000);
        const statusResponse = await this.getVideoStatus({ videoID });

        const statusData = statusResponse.data;

        if (!statusData) {
          console.log(
            `Status Error (${statusResponse.status}): ${statusResponse.data}`
          );
          return createErrorResponse(
            ["Failed to check upload status"],
            statusResponse.status
          );
        }

        const uploadStatus = statusData.uploading_phase.status;
        const copyrightStatus = statusData.copyright_check_status.status;
        console.log(
          `${accountID} Waiting for Upload Status: ${uploadStatus} & Copyright Status ${copyrightStatus}`
        );
        readyToPublish = uploadStatus === "complete";
      }

      // 4. Upload Cover Photo
      if (coverPhotoUrl) {
        console.log(`Uploading Cover Photo`);
        const fileContent = await getAssetBuffer(coverPhotoUrl);

        const form = new FormData();
        form.append("source", Buffer.from(fileContent), {
          filename: "thumbnail.jpg",
        });

        const coverPhotoResponse = await this.client.post(
          `/${videoID}/thumbnails`,
          form,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
            params: {
              access_token: this.accessToken,
              is_preferred: true,
            },
          }
        );

        if (coverPhotoResponse.status !== StatusCodes.OK) {
          console.log(
            `Cover Photo Error (${coverPhotoResponse.status}): ${coverPhotoResponse.data}`
          );
          return createErrorResponse(
            ["Failed to upload cover photo"],
            coverPhotoResponse.status
          );
        }
      }

      // 5. Publish the Reel
      console.log(`${accountID} Publishing Reel`);
      const { status, data } = await this.client.post(
        `/${accountID}/video_reels`,
        {
          video_id: videoID,
          upload_phase: "finish",
          video_state: "PUBLISHED",
          access_token: this.accessToken,
          description: caption,
        }
      );

      console.log(`${accountID} Reel Publish Response ${status}`);

      const statusResponse = await this.getVideoStatus({ videoID });
      const statusData = statusResponse.data;
      if (!statusData) {
        return createErrorResponse(
          ["Error querying video status"],
          statusResponse.status
        );
      }

      console.log(`${accountID} Video Status`, statusData);

      let isPublished: boolean =
        statusData.publishing_phase.status === "complete";
      while (!isPublished) {
        console.log(`${accountID} Querying Reel Publish Status`);
        await sleep(1000);

        const statusResponse = await this.getVideoStatus({ videoID });

        const statusData = statusResponse.data;

        if (!statusData) {
          console.log(
            `Status Error (${statusResponse.status}): ${statusResponse.data}`
          );
          return createErrorResponse(
            ["Failed to check upload status"],
            statusResponse.status
          );
        }

        const videoStatus = statusData.video_status;
        if (videoStatus === "error") {
          // Get error message
          const publishingErrors =
            statusData.publishing_phase.errors?.map((error) => error.message) ||
            [];
          const processingErrors =
            statusData.processing_phase.errors?.map((error) => error.message) ||
            [];

          const errors = [...publishingErrors, ...processingErrors];

          return createErrorResponse(
            errors.length > 0 ? errors : ["Video failed to publish"],
            StatusCodes.INTERNAL_SERVER_ERROR
          );
        }
        const publishStatus = statusData.publishing_phase.status;
        isPublished = publishStatus === "complete";
        console.log(
          `${accountID} Waiting for Publish Status: ${publishStatus}`
        );
      }

      // 6. Get Reel URL
      console.log(`${accountID} Finding Reel URL`);
      const reelResponse = await this.getReels({
        accountID,
      });

      if (reelResponse.errors) {
        return reelResponse;
      }

      const newestReel = reelResponse.data.reduce((newest, reel) => {
        return newest.createdAt > reel.createdAt ? newest : reel;
      }, reelResponse.data[0]);

      console.log(`${accountID} Reel URL: ${newestReel.permalink}`);

      return createSuccessResponse(
        {
          id: newestReel.pagePostID,
          url: newestReel.permalink,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/pages-api/comments-mentions
  publishComment = async ({
    pagePostID,
    message,
  }: {
    pagePostID: string;
    message: string;
  }): Promise<Response<{ id: string }>> => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
    });

    try {
      const { status, data } = await this.client.post(
        `/${pagePostID}/comments?${params}`,
        {
          message,
        }
      );

      return createSuccessResponse(
        {
          id: data.id,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  getReels = async ({
    accountID,
  }: {
    accountID: string;
  }): Promise<
    Response<
      {
        id: string;
        pagePostID: string;
        permalink: string;
        description: string;
        createdAt: Date;
      }[]
    >
  > => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      fields: [
        "id",
        "post_id",
        "permalink_url",
        "description",
        "created_time",
        "status",
      ].join(","),
    });

    try {
      const { status, data } = await this.client.get(
        `/${accountID}/video_reels?${params}`
      );

      const rawData = data.data as {
        id: string;
        post_id: string;
        permalink_url: string;
        description: string;
        created_time: string;
        status: {
          publishing_phase: {
            status: Status;
          };
        };
      }[];

      const reels = rawData
        .map((reel) => ({
          id: reel.id,
          pagePostID: `${accountID}_${reel.post_id}`,
          permalink: `https://www.facebook.com${reel.permalink_url}`,
          description: reel.description,
          status: reel.status,
          createdAt: DateTime.fromISO(reel.created_time).toJSDate(),
        }))
        .filter((reel) => reel.status.publishing_phase.status === "complete");

      return createSuccessResponse(reels, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  getFeedPost = async ({
    postFeedID,
  }: {
    postFeedID: string;
  }): Promise<
    Response<{
      id: string;
      url: string;
    }>
  > => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      fields: ["id", "permalink_url"].join(","),
    });

    try {
      const { status, data } = await this.client.get(
        `/${postFeedID}?${params}`
      );

      return createSuccessResponse(
        {
          id: data.id,
          url: data.permalink_url,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/graph-api/reference/page/photos/#multi
  publishPhotos = async ({
    accountID,
    mediaIDs,
    caption,
  }: {
    accountID: string;
    mediaIDs: string[];
    caption: string;
  }): Promise<Response<{ id: string; url: string }>> => {
    const params = new URLSearchParams({
      access_token: this.accessToken,
    });

    try {
      console.log(`Publishing Photos: ${accountID} with ${mediaIDs}`);
      const { status, data } = await this.client.post(
        `/${accountID}/feed?${params}`,
        {
          message: caption,
          attached_media: mediaIDs.map((id) => ({
            media_fbid: id,
          })),
        }
      );

      const postResponse = await this.getFeedPost({
        postFeedID: data.id,
      });

      if (postResponse.errors) {
        return postResponse;
      }

      return createSuccessResponse(postResponse.data, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/graph-api/reference/page/picture/#Reading
  getPagePicture = async ({
    accountID,
  }: {
    accountID: string;
  }): Promise<
    Response<{
      url: string;
    }>
  > => {
    try {
      const response = await this.client.get(
        `/${accountID}/picture?redirect=0`,
        {
          params: {
            access_token: this.accessToken,
          },
        }
      );

      return createSuccessResponse(
        {
          url: response.data.data.url,
        },
        response.status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/pages-api/search-pages
  searchPages = async ({
    query,
  }: {
    query: string;
  }): Promise<
    Response<
      {
        id: string;
        name: string;
        profileImageUrl: string | null;
        location?: {
          city?: string;
          state?: string;
          country?: string;
          street?: string;
          zip?: string;
          latitude?: number;
          longitude?: number;
        };
        link: string;
        hasLatLong: boolean;
        isVerified: boolean;
      }[]
    >
  > => {
    try {
      const response = await this.client.get(`/pages/search`, {
        params: {
          access_token: this.accessToken,
          q: query,
          fields: [
            "id",
            "name",
            "location",
            "link",
            "verification_status",
          ].join(","),
        },
      });

      const data = response.data.data as {
        id: string;
        name: string;
        location?: {
          city?: string;
          state?: string;
          country?: string;
          street?: string;
          zip?: string;
          latitude?: number;
          longitude?: number;
        };
        link: string;
        verification_status: string;
        profileImageUrl: string | null;
      }[];

      const pages = data.map((page) => ({
        id: page.id,
        name: page.name,
        location: page.location,
        link: page.link,
        hasLatLong:
          page.location?.latitude != null && page.location?.longitude != null,
        isVerified: page.verification_status === "blue_verified",
      }));

      const profilePicturePromises = pages.map(
        async (page) => await this.getPagePicture({ accountID: page.id })
      );

      const profilePictureResults = await Promise.all(profilePicturePromises);

      const pagesWithPictures = pages.map((page, index) => ({
        ...page,
        profileImageUrl: profilePictureResults[index].data?.url ?? null,
      }));

      return createSuccessResponse(pagesWithPictures, response.status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };
}
export default Facebook;
