import { Controller, Path } from "react-hook-form";

import { Combobox, Label } from "..";

import { Option } from "~/components/Combobox";

import { Error } from ".";

import Rules from "./rules";

import camelCaseToWords from "~/utils/camelCaseToWords";

type FormComboboxProps<T1, T2> = {
  control: any;
  name: Path<T1>;
  /** The options that the select input contains */
  options: Option<T2>[];
  placeholder?: string;
  disabled?: boolean;
  isMulti?: boolean;
  readOnly?: boolean;
  label?: string | JSX.Element;
  isOptional?: boolean;
  tooltipText?: string;
  rules?: Rules;
};

const FormCombobox = <T1,>({
  control,
  name,
  options,
  placeholder,
  disabled,
  label,
  tooltipText,
  rules = {},
}: FormComboboxProps<T1, any>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({
        field: { onChange, onBlur, value },
        fieldState: { error },
      }) => {
        return (
          <Label
            value={label}
            isRequired={!!rules.required}
            tooltipText={tooltipText}
          >
            <>
              <Combobox
                value={value}
                options={options}
                onChange={onChange}
                onBlur={onBlur}
                placeholder={placeholder}
                disabled={disabled}
                // isInvalid={!!error}
              />
              <Error error={error} />
            </>
          </Label>
        );
      }}
    />
  );
};

export default FormCombobox;
