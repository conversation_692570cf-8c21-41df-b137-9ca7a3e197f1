import { Switch } from "@headlessui/react";

import classnames from "classnames";

import { motion } from "framer-motion";

export type Size = "sm" | "md" | "lg";

type Props = {
  value: boolean;
  disabled?: boolean;
  size?: Size;
  onChange: (value: boolean) => void;
  intent?: "basic" | "danger";
};

const Toggle = ({
  value,
  disabled = false,
  size = "md",
  onChange,
  intent = "basic",
}: Props) => {
  return (
    <Switch
      checked={value}
      onChange={onChange}
      className={classnames(
        "flex h-5 w-9 cursor-pointer rounded-full p-0.5 transition-all",
        {
          "bg-light-gray justify-start": value === false,
          "bg-basic justify-end": value === true,
        }
      )}
    >
      <span className="sr-only">Use setting</span>
      <motion.div
        className="h-4 w-4 rounded-full bg-white"
        layout={true}
        transition={{ type: "spring", stiffness: 700, damping: 35 }}
      />
    </Switch>
  );
};

export default Toggle;
