import { useRef, useState } from "react";

import { AddN<PERSON><PERSON><PERSON>on, Badge, Combobox } from "~/components";

import { ArrowRightIcon } from "@heroicons/react/24/outline";
import assert from "assert";
import { useRouter } from "next/router";
import { v4 as uuidv4 } from "uuid";
import colors from "~/styles/colors";

export type Label = {
  id: string;
  name: string;
  color: string;
  backgroundColor: string;
  createdAt: Date;
};

type Props = {
  value: { name: string; color: string; backgroundColor: string }[];
  onChange: (labels: Label[]) => void;
  isCreateable?: boolean;
  options: Label[];
  isReadOnly?: boolean;
  hotKeyOptions?: {
    /** The hotkeys that open the combobox */
    keys: string | string[];
    /** The label for the hotkeys */
    label: string;
  };
};

const LabelSelect = ({
  value,
  onChange,
  isCreateable = false,
  options,
  isReadOnly = false,
  hotKeyOptions,
}: Props) => {
  const router = useRouter();
  const menuRef = useRef<HTMLDivElement>(null);
  const [newLabel, setNewLabel] = useState<string | null>(null);

  const labelOptions = options.map((label) => {
    return {
      key: label.name,
      value: label,
      label: (
        <div className="flex items-center gap-1.5">
          <div
            className="h-1.5 w-1.5 rounded-full"
            style={{ backgroundColor: label.color }}
          />
          <div className="pt-0.5">{label.name}</div>
        </div>
      ),
      component: (
        <div className="inline-block -translate-y-0.5">
          <Badge
            label={label.name}
            color={label.color}
            backgroundColor={label.backgroundColor}
            kind="solid"
            isUppercase={true}
            showHoverState={true}
            truncateLabel={true}
          />
        </div>
      ),
    };
  });

  const createdOptions = value
    .filter((val) => !options.map((option) => option.name).includes(val.name))
    .map((label) => {
      return {
        key: label.name,
        value: label,
        label: (
          <div className="flex items-center gap-1.5">
            <div
              className="h-1.5 w-1.5 rounded-full"
              style={{ backgroundColor: label.color }}
            />
            {label.name}
          </div>
        ),
        component: (
          <Badge
            label={label.name}
            color={label.color}
            backgroundColor={label.backgroundColor}
            kind="solid"
            isUppercase={true}
            showHoverState={true}
          />
        ),
      };
    });

  return (
    <div ref={menuRef}>
      <Combobox
        key="label"
        disabled={isReadOnly}
        hotKeyOptions={hotKeyOptions}
        placeholder={
          <AddNewButton
            label={isReadOnly ? "No Label" : "Label"}
            isReadOnly={isReadOnly}
            isUppercase={true}
          />
        }
        value={value.map((val) => ({
          key: val.name,
          ...val,
        }))}
        isMulti={true}
        isCreateable={isCreateable}
        isUppercase={true}
        onChange={(labels) => {
          const newestLabel = labels[labels.length - 1];
          if (typeof newestLabel === "string") {
            setNewLabel(newestLabel);
          } else {
            onChange(labels);
            setNewLabel(null);
          }
        }}
        options={[...labelOptions, ...createdOptions]}
        actionButton={{
          label: (
            <div className="flex items-center gap-1">
              Edit labels
              <ArrowRightIcon className="h-3 w-3" />
            </div>
          ),
          onClick: () => {
            router.push("/settings/labels");
          },
        }}
      />

      <Combobox
        key="color"
        value={null}
        isOpen={newLabel != null}
        isButtonHidden={true}
        isSearchable={false}
        onPointerDownOutside={() => {
          setNewLabel(null);
        }}
        onChange={(colors) => {
          assert(newLabel);
          onChange([
            ...value,
            {
              id: uuidv4(),
              name: newLabel,
              ...colors,
              createdAt: new Date().toISOString(),
            },
          ]);
          setNewLabel(null);
        }}
        options={[
          {
            key: "yellow",
            value: {
              color: colors.accentYellow,
              backgroundColor: colors.accentYellowLight,
            },
            label: (
              <div className="ml-1 flex items-center gap-2">
                <div className="bg-accent-yellow h-1.5 w-1.5 rounded-full" />
                <div>Yellow</div>
              </div>
            ),
          },
          {
            key: "purple",
            value: {
              color: colors.accentPurple,
              backgroundColor: colors.accentPurpleLight,
            },
            label: (
              <div className="ml-1 flex items-center gap-2">
                <div className="bg-accent-purple h-1.5 w-1.5 rounded-full" />
                <div>Purple</div>
              </div>
            ),
          },
          {
            key: "orange",
            value: {
              color: colors.accentOrange,
              backgroundColor: colors.accentOrangeLight,
            },
            label: (
              <div className="ml-1 flex items-center gap-2">
                <div className="bg-accent-orange h-1.5 w-1.5 rounded-full" />
                <div>Orange</div>
              </div>
            ),
          },
          {
            key: "red",
            value: {
              color: colors.accentRed,
              backgroundColor: colors.accentRedLight,
            },
            label: (
              <div className="ml-1 flex items-center gap-2">
                <div className="bg-accent-red h-1.5 w-1.5 rounded-full" />
                <div>Red</div>
              </div>
            ),
          },
          {
            key: "brown",
            value: {
              color: colors.accentBrown,
              backgroundColor: colors.accentBrownLight,
            },
            label: (
              <div className="ml-1 flex items-center gap-2">
                <div className="bg-accent-brown h-1.5 w-1.5 rounded-full" />
                <div>Brown</div>
              </div>
            ),
          },
          {
            key: "green",
            value: {
              color: colors.accentGreen,
              backgroundColor: colors.accentGreenLight,
            },
            label: (
              <div className="ml-1 flex items-center gap-2">
                <div className="bg-accent-green h-1.5 w-1.5 rounded-full" />
                <div>Green</div>
              </div>
            ),
          },
          {
            key: "ocean-blue",
            value: {
              color: colors.accentOceanBlue,
              backgroundColor: colors.accentOceanBlueLight,
            },
            label: (
              <div className="ml-1 flex items-center gap-2">
                <div className="bg-accent-ocean-blue h-1.5 w-1.5 rounded-full" />
                <div>Blue</div>
              </div>
            ),
          },
          {
            key: "magenta",
            value: {
              color: colors.accentMagenta,
              backgroundColor: colors.accentMagentaLight,
            },
            label: (
              <div className="ml-1 flex items-center gap-2">
                <div className="bg-accent-magenta h-1.5 w-1.5 rounded-full" />
                <div>Magenta</div>
              </div>
            ),
          },
        ]}
      />
    </div>
  );
};

export default LabelSelect;
