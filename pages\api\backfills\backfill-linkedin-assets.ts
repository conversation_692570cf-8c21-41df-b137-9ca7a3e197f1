import getAssetBuffer from "apiUtils/getAssetBuffer";
import getFileHash from "apiUtils/getFileHash";
import getStorageAssetsForLinkedIn from "apiUtils/linkedin/getStorageAssetsForLinkedIn";
import imageSize from "image-size";
import minBy from "lodash/minBy";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const id = req.query.id as string | null;
  const createdAtDate = req.query.createdAt as string | null;
  const workspaceID = req.query.workspaceID as string | null;

  const posts = await db.post.findMany({
    where: {
      id: id ? id : undefined,
      createdAt: {
        lte: new Date(createdAtDate || new Date().toISOString()),
      },
      channels: {
        hasSome: ["LinkedIn"],
      },
      linkedInContent: {
        assets: {
          none: {},
        },
      },
      workspaceID: workspaceID ? workspaceID : undefined,
    },
    orderBy: {
      createdAt: "desc",
    },
    select: {
      id: true,
      workspaceID: true,
      createdByID: true,
      createdAt: true,
      linkedInContent: {
        select: {
          id: true,
          coverPhoto: true,
          captions: true,
        },
      },
    },
  });

  console.log(`Num posts to backfill ${posts.length}`);

  const postAssetPromises = posts.map(async (post) => {
    const linkedInAssets = await getStorageAssetsForLinkedIn(post);

    return {
      ...post,
      ...linkedInAssets,
    };
  });

  const postsWithAssets = await Promise.all(postAssetPromises);

  console.log("Got all assets for posts");

  const postsToBackfill = postsWithAssets.filter((post) => {
    return post.assets.length > 0 || post.coverPhoto || post.captions;
  });

  console.log(`Backfilling ${postsToBackfill.length} posts`);

  const getImageDimensions = async (
    asset: {
      mimetype: string;
      url: string;
    } | null
  ) => {
    try {
      if (asset && asset.mimetype.includes("image/")) {
        const buffer = await getAssetBuffer(asset.url);
        const dimensions = imageSize(buffer);
        const md5Hash = await getFileHash(buffer);
        const width = dimensions.width as number;
        const height = dimensions.height as number;
        return { width, height, md5Hash };
      } else {
        return {
          width: null,
          height: null,
          md5Hash: null,
        };
      }
    } catch (error) {
      return {
        width: null,
        height: null,
        md5Hash: null,
      };
    }
  };

  const allAssets = postsToBackfill.flatMap((post) => {
    const { assets: linkedInAssets, coverPhoto, captions } = post;

    const assets = linkedInAssets.map((asset) => ({
      ...asset,
      workspaceID: post.workspaceID,
      userID: post.createdByID,
    }));

    if (coverPhoto) {
      assets.push({
        ...coverPhoto,
        workspaceID: post.workspaceID,
        userID: post.createdByID,
      });
    }

    if (captions) {
      assets.push({
        ...captions,
        workspaceID: post.workspaceID,
        userID: post.createdByID,
      });
    }

    return assets;
  });

  const allAssetsWithDimensions = await Promise.all(
    allAssets.map(async (asset) => {
      const imageDimensions = await getImageDimensions(asset);

      return {
        ...asset,
        ...imageDimensions,
      };
    })
  );

  await db.asset.createMany({
    data: allAssetsWithDimensions.map((asset) => ({
      id: asset.id,
      name: asset.name,
      bucket: "assets",
      path: asset.path,
      eTag: asset.eTag,
      md5Hash: asset.md5Hash,
      mimetype: asset.mimetype,
      size: asset.sizeBytes,
      url: asset.url,
      width: asset.width,
      height: asset.height,
      urlExpiresAt: asset.urlExpiresAt,
      workspaceID: asset.workspaceID,
      userID: asset.userID,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
    })),
    skipDuplicates: true,
  });

  const backfillPromises = postsToBackfill.map(async (post) => {
    const { linkedInContent, assets, coverPhoto, captions } = post;

    await db.linkedInAsset.createMany({
      data: assets.map((asset, index) => ({
        position: index,
        assetID: asset.id,
        linkedInContentID: linkedInContent!.id,
      })),
      skipDuplicates: true,
    });

    if (coverPhoto || captions) {
      await db.linkedInContent.update({
        where: {
          id: linkedInContent!.id,
        },
        data: {
          coverPhotoID: coverPhoto?.id || null,
          captionsID: captions?.id || null,
        },
      });
    }
  });

  await Promise.all(backfillPromises);

  await db.post.updateMany({
    where: {
      id: {
        in: posts.map((post) => post.id),
      },
    },
    data: {
      hasBackfilledAssets: true,
    },
  });

  res.status(200).send({
    numPostsBackfilled: postsToBackfill.length,
    earliestPostDate: minBy(posts, "createdAt")?.createdAt || "No posts found",
  });
};

export default handler;
