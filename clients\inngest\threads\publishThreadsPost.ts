import notifyOfScheduledPost from "apiUtils/notifyOfScheduledPost";
import createThreadsContainers from "apiUtils/threads/createThreadsContainers";
import publishThreadsContainer from "apiUtils/threads/publishThreadsContainer";
import uploadThreadsCarouselItems from "apiUtils/threads/uploadThreadsCarouselItems";
import { NonRetriableError, RetryAfterError, slugify } from "inngest";
import { db } from "~/clients";
import { AuthError, ScheduledPostResult } from "~/types/ScheduledPostResult";
import { inngest } from "../inngest";

const RETRIES = 4;

export default inngest.createFunction(
  {
    id: slugify("Threads Publish Post"),
    retries: RETRIES,
    name: "Threads Publish Post",
    concurrency: {
      limit: 1,
      key: "event.data.postID",
    },
    cancelOn: [
      {
        event: "post.rescheduled",
        if: `event.data.channel == "Threads" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.unscheduled",
        if: `event.data.channel == "Threads" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.deleted",
        match: "data.postID",
      },
    ],
    onFailure: async ({ error, event, step }) => {
      const data = event.data.event.data;
      const { postID } = data;
      console.log(`Post:${postID}: Final Error ${error.message}`);

      const isAuthError = error.name === "AuthError";
      console.log(`Post:${postID}: Is Auth Error: ${isAuthError}`);

      const post = await db.post.findFirstOrThrow({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          threadsIntegration: {
            select: {
              account: {
                select: {
                  username: true,
                },
              },
            },
          },
          scheduledPosts: {
            where: {
              channel: "Threads",
              status: "Scheduled",
            },
          },
        },
      });

      const scheduledPost = post.scheduledPosts[0];

      if (scheduledPost == null) {
        console.log(`Post:${postID}: No longer scheduled`);
        return;
      }

      await notifyOfScheduledPost({
        post,
        scheduledPost,
        status: "Error",
        account: post.threadsIntegration?.account
          ? {
              name: `@${post.threadsIntegration?.account.username}`,
              url: `https://www.threads.com/@${post.threadsIntegration?.account.username}`,
            }
          : undefined,
        error: error.message,
        isAuthError,
      });
    },
  },
  [
    {
      event: "post.scheduled",
      if: `event.data.channel == "Threads"`,
    },
    {
      event: "post.rescheduled",
      if: `event.data.channel == "Threads"`,
    },
  ],
  async ({ event, step, attempt }) => {
    const { delayUntil, postID } = event.data;

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    const post = await db.post.findFirst({
      where: {
        id: postID,
      },
      select: {
        id: true,
        title: true,
        workspaceID: true,
        workspace: {
          select: {
            id: true,
            name: true,
            companyID: true,
            userWorkspaces: {
              select: {
                userID: true,
              },
            },
            slackWebhooks: {
              where: {
                type: "SuccessfulPost",
              },
              select: {
                type: true,
                url: true,
              },
            },
          },
        },
        subscribers: {
          select: {
            userID: true,
          },
        },
        createdByID: true,
        threadsIntegration: {
          select: {
            accessToken: true,
            account: {
              select: {
                username: true,
                threadsUserID: true,
              },
            },
          },
        },
        threadsContent: {
          select: {
            id: true,
            threadPosition: true,
            copy: true,
            assets: {
              select: {
                id: true,
                position: true,
                containerID: true,
                containerExpiresAt: true,
                asset: {
                  select: {
                    url: true,
                    mimetype: true,
                  },
                },
              },
              orderBy: {
                position: "asc",
              },
            },
          },
          orderBy: {
            threadPosition: "asc",
          },
        },
        scheduledPosts: {
          where: {
            channel: "Threads",
            status: "Scheduled",
          },
        },
        approvals: {
          where: {
            isBlocking: true,
            approvedAt: null,
          },
          select: {
            id: true,
          },
        },
      },
    });

    if (post == null) {
      console.log(`Post:${postID}: Post not found`);
      return {
        status: "Skipped",
        comment: "Post has been deleted",
      };
    }

    const scheduledPost = post.scheduledPosts[0];

    if (scheduledPost == null) {
      console.log(`Post:${postID}: No longer scheduled`);
      return {
        status: "Skipped",
        comment: "No scheduled posts",
      };
    }

    if (post.approvals.length > 0) {
      console.log(`Post:${postID}: Blocked by approval`);
      return {
        status: "Skipped",
        comment: "Post has been blocked by approval",
      };
    }

    const { threadsIntegration, threadsContent } = post;

    if (threadsIntegration == null) {
      console.log(`Post:${postID}: No Threads integration`);
      throw new NonRetriableError(`No Threads integration`);
    }

    if (threadsContent.length === 0) {
      console.log(`Post:${postID}: No content to post`);

      throw new NonRetriableError(`No Threads content to post`);
    }

    await step.run("Upload Threads Carousel Items", async () => {
      const uploadResponse = await uploadThreadsCarouselItems({
        post: {
          id: post.id,
          threadsIntegration,
          threadsContent,
        },
      });

      if (uploadResponse.status === "NonRetriableError") {
        if (uploadResponse.isAuthError) {
          throw new AuthError(uploadResponse.error);
        } else {
          throw new NonRetriableError(uploadResponse.error);
        }
      } else if (uploadResponse.status === "Error") {
        throw new RetryAfterError(uploadResponse.error, 10_000);
      }

      return uploadResponse.assetIDToContainerID;
    });

    const threadsWithContainers = await db.threadsContent.findMany({
      where: {
        postID,
      },
      select: {
        id: true,
        threadPosition: true,
        copy: true,
        assets: {
          select: {
            id: true,
            position: true,
            containerID: true,
            containerExpiresAt: true,
            asset: {
              select: {
                url: true,
                mimetype: true,
              },
            },
          },
          orderBy: {
            position: "asc",
          },
        },
      },
      orderBy: {
        threadPosition: "asc",
      },
    });

    let replyToID: null | string = null;
    let firstPublishResponse: null | ScheduledPostResult = null;
    for await (const thread of threadsWithContainers) {
      const threadNumber = thread.threadPosition + 1;
      const containerID = await step.run(
        `Create Thread #${threadNumber} Container`,
        async () => {
          console.log(
            `Post:${postID}: Creating Thread #${threadNumber} Container Reply to ${replyToID}`
          );

          const threadContainerResponse = await createThreadsContainers({
            post: {
              id: post.id,
              threadsIntegration,
            },
            thread,
            replyToID,
          });

          if (threadContainerResponse.status === "Success") {
            const containerID = threadContainerResponse.containerID;

            console.log(
              `Post:${postID}: Created Carousel Container ${threadContainerResponse.containerID}`
            );

            return containerID;
          } else if (threadContainerResponse.status === "NonRetriableError") {
            const error =
              thread.threadPosition === 0
                ? threadContainerResponse.error
                : `${threadContainerResponse.error} Thread is partially published ${firstPublishResponse?.postUrl}`;

            if (threadContainerResponse.isAuthError) {
              throw new AuthError(error);
            } else {
              throw new NonRetriableError(error);
            }
          } else if (threadContainerResponse.status === "Error") {
            const error =
              thread.threadPosition === 0
                ? threadContainerResponse.error
                : `${threadContainerResponse.error} Thread is partially published ${firstPublishResponse?.postUrl}`;

            throw new RetryAfterError(error, 45_000);
          } else {
            throw new NonRetriableError(
              `Unknown threadContainerResponse status: ${threadContainerResponse.status}`
            );
          }
        }
      );

      const { publishResponse, threadsPostID } = await step.run(
        `Publish Thread #${threadNumber}`,
        async () => {
          const publishResponse = await publishThreadsContainer({
            post: {
              ...post,
              threadsIntegration,
            },
            containerID,
            threadPosition: thread.threadPosition,
          });

          if (publishResponse.status === "Success") {
            const threadsPostID = publishResponse.socialPostID!;
            const threadsLink = publishResponse.postUrl!;

            console.log(
              `Post:${postID}: Published Thread #${threadNumber} ${threadsLink}`
            );
            if (thread.threadPosition === 0) {
              await db.post.update({
                where: {
                  id: post.id,
                },
                data: {
                  threadsPostID,
                  threadsLink,
                },
              });
            }

            return { publishResponse, threadsPostID };
          } else if (publishResponse.status === "Error") {
            const error =
              thread.threadPosition === 0
                ? publishResponse.error
                : `${publishResponse.error} Thread is partially published ${firstPublishResponse?.postUrl}`;

            throw new RetryAfterError(error, 30_000);
          } else if (publishResponse.status === "NonRetriableError") {
            if (publishResponse.isAuthError) {
              throw new AuthError(publishResponse.error);
            } else {
              throw new NonRetriableError(publishResponse.error);
            }
          } else {
            throw new NonRetriableError(
              `Unknown publishResponse status: ${publishResponse.status}`
            );
          }
        }
      );

      if (thread.threadPosition === 0) {
        firstPublishResponse = publishResponse;
      }
      replyToID = threadsPostID;
    }

    if (firstPublishResponse == null) {
      throw new NonRetriableError("Failed to publish first thread");
    }

    await notifyOfScheduledPost({
      post,
      scheduledPost,
      account: {
        name: `@${post.threadsIntegration!.account.username}`,
        url: `https://www.threads.com/@${
          post.threadsIntegration!.account.username
        }`,
      },
      ...firstPublishResponse,
    });

    return firstPublishResponse;
  }
);
