import ContentLoader from "react-content-loader";
import colors from "~/styles/colors";

const PostPageLoader = () => {
  return (
    <div className="flex gap-10">
      <ContentLoader
        speed={2}
        width="full"
        height={600}
        backgroundColor={colors.lightestGray}
        foregroundColor={colors.lightGray}
        radius={10}
      >
        <rect x="0" y="0" rx="6" ry="6" width="50" height="20" />
        <rect x="0" y="30" rx="6" ry="6" width="200" height="45" />
        <rect x="0" y="110" rx="6" ry="6" width="100%" height="40" />
        <rect x="0" y="180" rx="12" ry="12" width="100%" height="400" />
      </ContentLoader>
      <ContentLoader
        speed={2}
        width={350}
        height={1000}
        backgroundColor={colors.lightestGray}
        foregroundColor={colors.lightGray}
        radius={10}
      >
        <rect x="0" y="10" rx="6" ry="6" width="35" height="16.5" />
        <rect x="75" y="10" rx="6" ry="6" width="90" height="16.5" />
        <rect x="170" y="10" rx="6" ry="6" width="50" height="16.5" />
        <rect x="0" y="48" rx="6" ry="6" width="60" height="16.5" />
        <rect x="75" y="43" rx="12" ry="12" width="96" height="25" />
        <rect x="0" y="86" rx="6" ry="6" width="50" height="16.5" />
        <rect x="75" y="81" rx="12" ry="12" width="106" height="25" />
        <rect x="0" y="124" rx="6" ry="6" width="45" height="16.5" />
        <rect x="75" y="119" rx="12" ry="12" width="96" height="25" />
        <rect x="0" y="162" rx="6" ry="6" width="40" height="16.5" />
        <rect x="75" y="157" rx="12" ry="12" width="75" height="25" />
        {/* Divider */}
        <rect x="0" y="205" rx="6" ry="6" width="100%" height="1" />
        {/* Subscribers */}
        <rect x="0" y="230" rx="6" ry="6" width="60" height="16.5" />
        <circle cx="85" cy="237" r="10" />
        <circle cx="16" cy="275" r="16" />
        {/* Approvals */}
        <rect x="0" y="305" rx="6" ry="6" width="60" height="16.5" />
        <circle cx="85" cy="312" r="10" />
        <circle cx="16" cy="350" r="16" />
        <circle cx="55" cy="350" r="16" />
      </ContentLoader>
    </div>
  );
};

export default PostPageLoader;
