import { useElementSize } from "@mantine/hooks";
import Uppy from "@uppy/core";
import { arrayMoveImmutable } from "array-move";
import classNames from "classnames";
import { motion } from "framer-motion";
import { useMemo, useState } from "react";
import AssetUploadContainer from "~/components/ui/AssetUploadContainer";
import { useBreakpoints } from "~/hooks";
import { usePostIntegrations } from "~/providers";
import { AssetType } from "~/types/Asset";
import { handleFilesUpload, handleTrpcError, trpc } from "~/utils";
import EditImageModal, {
  EditImageModalProperties,
} from "~/utils/EditImageModal";
import { AssetsUploadReorderControls } from "../../files/AssetControls";
import AssetsNewDesign, { UploadingAsset } from "../../files/AssetsNewDesign";
import FileDropzone from "../../files/FileDropzone";
import FileUploadInput from "../../files/FileUploadInput";

type Props = {
  contentID: string;
  assets: AssetType[];
  setAssets: (
    value: AssetType[] | ((prev: AssetType[]) => AssetType[])
  ) => void;
  uploadingAssets: UploadingAsset[];
  uppy: Uppy | null;
  getValidFiles: (files: File[]) => Promise<File[]>;
  coverPhotoUrl?: string;
  setDraggedOver: (isDragged: boolean) => void;
  isReadOnly: boolean;
  draggedOver: boolean;
};

const FacebookAssetPreviewNewDesign = ({
  contentID,
  assets,
  setAssets,
  uploadingAssets,
  uppy,
  getValidFiles,
  coverPhotoUrl,
  setDraggedOver,
  isReadOnly,
  draggedOver,
}: Props) => {
  const { ref: heightRef, height: calculatedHeight } = useElementSize();
  const height = calculatedHeight || 460;

  const randomID = useMemo(() => Math.random().toString(36), []);
  const [isEditingAssets, setIsEditingAssets] = useState<boolean>(false);
  const { isMobile } = useBreakpoints();

  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);
  const deleteAssetMutation = trpc.facebookContent.deleteAsset.useMutation();
  const updateAssetPositionMutation =
    trpc.facebookContent.updateAssetPositions.useMutation();
  const { currentChannel } = usePostIntegrations();

  const acceptedFileTypesMap = {
    "image/jpeg": [".jpg", ".jpeg"],
    "image/png": [".png"],
    "video/mp4": [".mp4"],
    "video/quicktime": [".mov"],
  };

  const acceptedMimeTypes = Object.keys(acceptedFileTypesMap);

  const onSortEnd = (oldIndex: number, newIndex: number) => {
    setAssets((assets) => {
      const newAssets = arrayMoveImmutable(assets, oldIndex, newIndex);

      updateAssetPositionMutation.mutate({
        assets: newAssets.map((asset, index) => ({
          id: asset.id,
          position: index,
        })),
      });

      return newAssets;
    });
  };

  const handleUploadButtonClick = () => {
    const fileInput = document.getElementById(
      `file-upload-${randomID}`
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  };

  const onEditClick = (asset: AssetType) => {
    if (asset.metadata.mimetype.startsWith("image/")) {
      setEditImageModalProperties({
        title: "Edit Image",
        channel: "Facebook",
        maxSizeMB: 4,
        isOpen: true,
        asset,
      });
    }
  };

  const onDeleteClick = (asset: AssetType) => {
    setAssets((prevAssets) => {
      const newAssets = prevAssets.filter(
        (prevAsset) => prevAsset.id !== asset.id
      );
      if (newAssets.length === 0) {
        setIsEditingAssets(false);
      }
      return newAssets;
    });
    deleteAssetMutation.mutate(
      {
        facebookAssetID: asset.id,
      },
      {
        onError: (error) => {
          handleTrpcError({
            title: "Error Deleting Asset",
            error,
          });
        },
      }
    );
  };

  const renderContent = () => {
    const uploadedAssets = assets;
    const newUploadingAssets = uploadingAssets.filter(
      (asset) => !asset.updateAssetID
    );

    if (isEditingAssets) {
      return (
        <AssetsNewDesign
          assets={assets}
          uploadingAssets={uploadingAssets}
          onDeleteClick={onDeleteClick}
          editImageModalProperties={editImageModalProperties}
          onEditClick={onEditClick}
          onUpload={async (file, updateAssetID) => {
            if (uppy) {
              if (updateAssetID) {
                uppy.addFile({
                  name: file.name,
                  type: file.type,
                  data: file,
                  meta: {
                    contentID,
                    updateAssetID,
                  },
                });
              } else {
                await handleFilesUpload({
                  files: [file],
                  uppy,
                  meta: {
                    contentID,
                  },
                  getValidFiles,
                });
              }
            }
          }}
          onEditImageModalClose={() => setEditImageModalProperties(null)}
          showNewEditButton={true}
          refetchAssets={() => {}}
          isReadOnly={isReadOnly}
          onDrop={async (files) => {
            setDraggedOver(false);
            if (uppy) {
              await handleFilesUpload({
                files,
                uppy,
                meta: {
                  contentID,
                },
                getValidFiles,
              });
            }
          }}
          showDropzone={draggedOver}
          acceptedFileTypes={acceptedFileTypesMap}
          onSortEnd={onSortEnd}
          onCloseClick={() => setIsEditingAssets(false)}
          onUploadButtonClick={handleUploadButtonClick}
        />
      );
    }

    const hasUploadedVideo =
      uploadedAssets[0]?.metadata.mimetype.startsWith("video");
    const uploadingVideo = newUploadingAssets.find((asset) =>
      asset.type.startsWith("video/")
    );
    const isVideo = hasUploadedVideo || uploadingVideo;

    if (isVideo) {
      return (
        <AssetUploadContainer
          asset={!!uploadingVideo ? uploadingVideo : uploadedAssets[0]}
          coverPhotoUrl={coverPhotoUrl}
          onDeleteClick={onDeleteClick}
          isReadOnly={isReadOnly}
        />
      );
    }

    const displayAssets = [...uploadedAssets, ...newUploadingAssets];
    if (displayAssets.length === 0) {
      return null;
    } else if (displayAssets.length === 1) {
      const asset = displayAssets[0];
      return (
        <AssetUploadContainer
          asset={asset}
          isReadOnly={isReadOnly}
          onEditClick={onEditClick}
          onDeleteClick={onDeleteClick}
        />
      );
    } else if (displayAssets.length === 2) {
      // Copying from LinkedIn
      const aspectRatios = displayAssets.map(
        (asset) => (asset.width || 1) / (asset.height || 1)
      );
      const totalAspectRatio = aspectRatios.reduce(
        (sum, ratio) => sum + ratio,
        0
      );

      const widthPercentages = aspectRatios.map(
        (ratio) => (ratio / totalAspectRatio) * 100
      );

      return (
        <div className="flex gap-0.5">
          {displayAssets.map((asset, index) => (
            <div
              key={index}
              style={{
                width: `${widthPercentages[index]}%`,
                maxHeight: "500px",
              }}
            >
              <AssetUploadContainer
                asset={asset}
                className="h-auto w-full"
                isReadOnly={isReadOnly}
                onEditClick={onEditClick}
                onDeleteClick={onDeleteClick}
              />
            </div>
          ))}
        </div>
      );
    } else {
      const firstImage = displayAssets[0];
      const remainingImages = displayAssets.slice(1, 4);
      const missingImagesCount = displayAssets.slice(4).length;
      const firstImageAspectRatio =
        (firstImage.width || 1) / (firstImage.height || 1);
      const aspectRatios = remainingImages.map(
        (asset) => (asset.width || 1) / (asset.height || 1)
      );
      if (firstImageAspectRatio > 4 / 3) {
        const widestAspectRatio = Math.max(...aspectRatios);
        return (
          <div className="flex w-full flex-col gap-0.5">
            <AssetUploadContainer
              asset={firstImage}
              className="h-auto w-full"
              isReadOnly={isReadOnly}
              onEditClick={onEditClick}
              onDeleteClick={onDeleteClick}
            />
            <div className="flex gap-0.5">
              {remainingImages.map((image, index) => {
                return (
                  <div
                    key={image.id}
                    className="relative flex flex-1 items-center justify-center overflow-hidden"
                    style={{
                      aspectRatio: widestAspectRatio,
                    }}
                  >
                    <AssetUploadContainer
                      asset={image}
                      className="h-auto w-full"
                      style={{
                        width: "100%",
                        height: "100%",
                      }}
                      showControls={
                        !(
                          index === remainingImages.length - 1 &&
                          missingImagesCount > 0
                        )
                      }
                      isReadOnly={isReadOnly}
                      onEditClick={onEditClick}
                      onDeleteClick={onDeleteClick}
                    />
                    {index === remainingImages.length - 1 &&
                      missingImagesCount > 0 && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                          <span className="text-4xl font-medium text-white">
                            +{missingImagesCount}
                          </span>
                        </div>
                      )}
                  </div>
                );
              })}
            </div>
          </div>
        );
      } else {
        return (
          <div className="flex gap-0.5">
            <div ref={heightRef} className="h-full">
              <AssetUploadContainer
                asset={firstImage}
                className="h-full max-h-[540px] w-auto"
                style={{
                  height: isMobile ? undefined : height,
                }}
                isReadOnly={isReadOnly}
                onEditClick={onEditClick}
                onDeleteClick={onDeleteClick}
              />
            </div>
            <div
              className="flex h-full w-1/2 flex-col gap-0.5"
              style={{ height }}
            >
              {remainingImages.map((image, index) => (
                <div className="relative overflow-hidden" key={image.id}>
                  <AssetUploadContainer
                    asset={image}
                    style={{
                      height: height / remainingImages.length,
                      aspectRatio: "auto",
                    }}
                    className="h-full w-full object-cover"
                    showControls={
                      !(
                        index === remainingImages.length - 1 &&
                        missingImagesCount > 0
                      )
                    }
                    isReadOnly={isReadOnly}
                    onEditClick={onEditClick}
                    onDeleteClick={onDeleteClick}
                  />
                  {index === remainingImages.length - 1 &&
                    missingImagesCount > 0 && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                        <span className="text-4xl font-medium text-white">
                          +{missingImagesCount}
                        </span>
                      </div>
                    )}
                </div>
              ))}
            </div>
          </div>
        );
      }
    }
  };

  return (
    <>
      {draggedOver ? (
        <motion.div
          key="dropzone"
          className="mb-3.5 w-full"
          initial={{ height: 338, opacity: 0 }}
          animate={{
            height: 338,
            opacity: 100,
          }}
        >
          <FileDropzone
            onDrop={async (files) => {
              setDraggedOver(false);
              if (uppy) {
                await handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID,
                  },
                  getValidFiles,
                });
              }
            }}
            acceptedFileTypes={acceptedFileTypesMap}
            uploadProgress={
              uploadingAssets.length > 0
                ? Math.round(
                    uploadingAssets.reduce(
                      (sum, asset) => sum + (asset.uploadProgress || 0),
                      0
                    ) / uploadingAssets.length
                  )
                : undefined
            }
          />
        </motion.div>
      ) : (
        <motion.div
          key="assets"
          initial={{ opacity: 0 }}
          animate={{
            opacity: 100,
          }}
          className={classNames({
            hidden: assets.length === 0 && uploadingAssets?.length === 0,
          })}
        >
          <div className="group/asset-controls relative -mr-7 -ml-8 opacity-100 transition-all sm:mr-0 sm:ml-0">
            {renderContent()}
            {assets.length > 0 && !isEditingAssets && !isReadOnly && (
              <AssetsUploadReorderControls
                onUploadClick={handleUploadButtonClick}
                onReorderClick={() => setIsEditingAssets(true)}
                isReadOnly={isReadOnly}
              />
            )}
          </div>

          <FileUploadInput
            inputID={`file-upload-${randomID}`}
            contentID={contentID}
            uppy={uppy}
            getValidFiles={getValidFiles}
            acceptedMimeTypes={acceptedMimeTypes}
            isDisabled={uploadingAssets.length > 0}
            multiple={true}
          />
        </motion.div>
      )}
      {editImageModalProperties && (
        <EditImageModal
          title={editImageModalProperties.title}
          asset={editImageModalProperties.asset}
          maxSizeMB={editImageModalProperties.maxSizeMB}
          channel={editImageModalProperties.channel}
          isOpen={editImageModalProperties.isOpen}
          onClose={() => setEditImageModalProperties(null)}
          onUpload={async (file) => {
            if (uppy && editImageModalProperties.asset) {
              uppy.addFile({
                name: file.name,
                type: file.type,
                data: file,
                meta: {
                  contentID,
                  updateAssetID: editImageModalProperties.asset.id,
                },
              });
            }
          }}
        />
      )}
    </>
  );
};

export default FacebookAssetPreviewNewDesign;
