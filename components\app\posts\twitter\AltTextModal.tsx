import { Bars3BottomLeftIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { Carousel } from "react-responsive-carousel";
import { toast } from "sonner";
import { TextareaInput, Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { handleTrpcError, trpc } from "~/utils";

type Props = {
  assets: {
    id: string;
    metadata: {
      mimetype: string;
    };
    signedUrl: string;
    altText: string | null;
  }[];
  setTweetAssets: (assets: { [id: string]: string | null }) => void;
};

const AltTextModal = ({ assets, setTweetAssets }: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const [selectedAssetID, setSelectedAssetID] = useState<string | null>(
    assets[0]?.id
  );
  const [assetAltText, setAssetAltText] = useState<{
    [id: string]: string | null;
  }>(
    assets.reduce(
      (acc, asset) => {
        acc[asset.id] = asset.altText;
        return acc;
      },
      {} as { [id: string]: string | null }
    )
  );

  const updateAltTextMutation = trpc.tweetContent.updateAltText.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Unable to Update Alt Text", error });
    },
  });

  useEffect(() => {
    if (open) {
      setSelectedAssetID(assets[0]?.id);
      setAssetAltText(
        assets.reduce(
          (acc, asset) => {
            acc[asset.id] = asset.altText;
            return acc;
          },
          {} as { [id: string]: string | null }
        )
      );
    }
  }, [assets, open]);

  const isDisabled = assets.length === 0;
  const isVideo = assets.some((asset) =>
    asset.metadata.mimetype.startsWith("video")
  );
  const tooltipText = isDisabled
    ? "Add assets to add alt text"
    : isVideo
      ? "Alt text cannot be added to videos"
      : "Add description (alt text)";

  return (
    <div>
      <Tooltip value={tooltipText}>
        <Button
          variant="ghost"
          size="sm"
          disabled={isDisabled || isVideo}
          onClick={() => setOpen(true)}
        >
          <Bars3BottomLeftIcon className="h-4 w-4" />
        </Button>
      </Tooltip>
      <Credenza.Root open={open} onOpenChange={setOpen}>
        <Credenza.Header>
          <Credenza.Title>Add Description (Alt Text)</Credenza.Title>
          <Credenza.Description>
            Add description (alt text) to your images
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <Carousel
            showArrows={false}
            showIndicators={false}
            showStatus={false}
            showThumbs={true}
            onChange={(index) => {
              if (assets[index]) {
                setSelectedAssetID(assets[index].id);
              }
            }}
            renderThumbs={() => {
              return assets.map((asset) => {
                if (asset.metadata.mimetype.startsWith("image")) {
                  return (
                    <img
                      key={asset.id}
                      style={{
                        width: 80,
                        height: 80,
                      }}
                      className="object-cover"
                      src={asset.signedUrl}
                    />
                  );
                } else {
                  return (
                    <video
                      key={asset.id}
                      style={{
                        width: 80,
                        height: 80,
                      }}
                      className="object-cover"
                      src={asset.signedUrl}
                    />
                  );
                }
              });
            }}
          >
            {assets.map((asset, index) => {
              const mimetype = asset.metadata.mimetype;

              if (mimetype.startsWith("image")) {
                return (
                  <img
                    key={`img-${asset.id}`}
                    className="max-h-[250px] w-full object-contain"
                    src={asset.signedUrl}
                  />
                );
              } else {
                return (
                  <video
                    key={`video-${asset.id}`}
                    className="max-h-[250px] w-full object-contain"
                    playsInline={true}
                  >
                    <source src={asset.signedUrl} />
                  </video>
                );
              }
            })}
          </Carousel>
          <div className="font-body-sm font-medium">Description</div>
          <TextareaInput
            value={assetAltText[selectedAssetID || ""] || ""}
            onChange={(value) => {
              setAssetAltText((prevAltText) => ({
                ...prevAltText,
                [selectedAssetID || ""]: value,
              }));
            }}
            placeholder="Add description (alt text)"
            rows={3}
          />
        </Credenza.Body>
        <Credenza.Footer>
          <Button
            variant="secondary"
            disabled={updateAltTextMutation.isPending}
            onClick={() => {
              setOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            isLoading={updateAltTextMutation.isPending}
            onClick={() => {
              updateAltTextMutation.mutate(
                {
                  tweetAssets: assets.map((asset) => ({
                    id: asset.id,
                    altText: assetAltText[asset.id],
                  })),
                },
                {
                  onSuccess: () => {
                    toast.success("Alt Text Saved", {
                      description: "Alt text saved successfully",
                    });
                    setTweetAssets(assetAltText);
                    setOpen(false);
                  },
                  onError: (error) => {
                    handleTrpcError({
                      title: "Error Saving Alt Text",
                      error,
                    });
                  },
                }
              );
            }}
          >
            Save
          </Button>
        </Credenza.Footer>
      </Credenza.Root>
    </div>
  );
};

export default AltTextModal;
