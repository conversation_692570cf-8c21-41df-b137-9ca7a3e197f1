import { Channel } from "@prisma/client";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import { FormTextInput } from "~/components/form";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { urlRegex } from "~/constants";
import { CHANNEL_ICON_MAP } from "~/types";
import { handleTrpcError, trpc } from "~/utils";

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  channel: Channel & ("BlogPost" | "Email");
  onSave: (webhook: {
    id: string;
    name: string;
    hostname: string;
    urlLast4: string;
  }) => void;
  onError: (webhookID: string) => void;
};

type FormValues = {
  name: string;
  url: string;
};

const NewWebhookModal = ({
  open,
  setOpen,
  onSave,
  onError,
  channel,
}: Props) => {
  const router = useRouter();
  const { control, handleSubmit, reset } = useForm<FormValues>();

  const createWebhookMutation =
    trpc.integration.channelWebhook.create.useMutation({
      onSuccess: (data, variables) => {
        onSave(data.webhook);
      },
      onError: (error, variables) => {
        handleTrpcError({
          title: "Error Saving Webhook",
          error,
        });
        setOpen(false);
        onError(variables.id);
      },
    });

  const handleSaveWebhook = (values: FormValues) => {
    const id = uuidv4();
    createWebhookMutation.mutate({
      id,
      channel,
      ...values,
    });
    reset();
  };

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header>
        <Credenza.Title>
          Connect {CHANNEL_ICON_MAP[channel].label} via Webhook
        </Credenza.Title>
        <Credenza.Description>
          See instructions on the Blog Integration page on how to generate the
          webhook URL
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <form onSubmit={handleSubmit(handleSaveWebhook)}>
          <div className="mt-4 grid grid-cols-12 gap-4">
            <div className="col-span-6">
              <FormTextInput
                control={control}
                name="name"
                label="Name"
                kind="outline"
                placeholder="ex: Webflow Blog"
                rules={{ required: true }}
              />
            </div>
            <div className="col-span-6">
              <FormTextInput
                control={control}
                name="url"
                label="Webhook URL"
                kind="outline"
                placeholder="Paste in webhook url"
                rules={{
                  pattern: {
                    value: urlRegex,
                    message: "Must be a valid URL",
                  },
                  required: true,
                }}
              />
            </div>
          </div>
          <Credenza.Footer>
            <Button
              variant="secondary"
              disabled={createWebhookMutation.isPending}
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit" isLoading={createWebhookMutation.isPending}>
              Submit
            </Button>
          </Credenza.Footer>
        </form>
      </Credenza.Body>
    </Credenza.Root>
  );
};

export default NewWebhookModal;
