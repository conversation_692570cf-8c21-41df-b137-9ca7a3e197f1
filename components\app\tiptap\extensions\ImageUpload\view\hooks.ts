import { DragEvent, useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import compressImage from "~/utils/compressImage";

const fileToBase64 = (file: File): Promise<string> => {
  return new Promise(
    (resolve: (value: string) => void, reject: (reason: Error) => void) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        // reader.result can be string | ArrayBuffer | null, so we need to type check
        if (typeof reader.result === "string") {
          resolve(reader.result);
        } else {
          reject(new Error("Failed to convert file to base64"));
        }
      };
      reader.onerror = (error: ProgressEvent<FileReader>) =>
        reject(new Error("Error reading file"));
    }
  );
};

export const useUploader = ({
  onUpload,
  customUpload,
}: {
  onUpload: (url: string) => void;
  customUpload?: (file: File) => Promise<string>;
}) => {
  const [loading, setLoading] = useState(false);

  const uploadFile = useCallback(
    async (file: File) => {
      setLoading(true);

      const maxSizeMB = 3.5;

      if (file.size > maxSizeMB * 1024 * 1024) {
        const mimeType = file.type;
        if (mimeType === "image/png" || mimeType === "image/jpeg") {
          const compressedFile = await compressImage(file, {
            maxSizeMB: maxSizeMB,
            type: mimeType,
          });
          file = compressedFile;
        } else {
          toast.error("Error Uploading Image", {
            description: "Image must be less than 4MB",
          });
          setLoading(false);
          return;
        }
      }

      try {
        let url;
        if (customUpload) {
          // Use server upload if provided
          url = await customUpload(file);
        } else {
          // Fallback to base64 if no server upload provided
          url = await fileToBase64(file);
        }

        onUpload(url);
      } catch (errPayload: any) {
        console.log("errPayload", errPayload);
        const error =
          errPayload?.response?.data?.error || "Something went wrong";
        toast.error("Error Uploading Image", {
          description: error.message,
        });
      }
      setLoading(false);
    },
    [onUpload, customUpload]
  );

  return { loading, uploadFile };
};

export const useFileUpload = () => {
  const fileInput = useRef<HTMLInputElement>(null);

  const handleUploadClick = useCallback(() => {
    fileInput.current?.click();
  }, []);

  return { ref: fileInput, handleUploadClick };
};

export const useDropZone = ({
  uploader,
}: {
  uploader: (file: File) => void;
}) => {
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [draggedInside, setDraggedInside] = useState<boolean>(false);

  useEffect(() => {
    const dragStartHandler = () => {
      setIsDragging(true);
    };

    const dragEndHandler = () => {
      setIsDragging(false);
    };

    document.body.addEventListener("dragstart", dragStartHandler);
    document.body.addEventListener("dragend", dragEndHandler);

    return () => {
      document.body.removeEventListener("dragstart", dragStartHandler);
      document.body.removeEventListener("dragend", dragEndHandler);
    };
  }, []);

  const onDrop = useCallback(
    (e: DragEvent<HTMLDivElement>) => {
      setDraggedInside(false);
      if (e.dataTransfer.files.length === 0) {
        return;
      }

      const fileList = e.dataTransfer.files;

      const files: File[] = [];

      for (let i = 0; i < fileList.length; i += 1) {
        const item = fileList.item(i);
        if (item) {
          files.push(item);
        }
      }

      if (files.some((file) => file.type.indexOf("image") === -1)) {
        return;
      }

      e.preventDefault();

      const filteredFiles = files.filter((f) => f.type.indexOf("image") !== -1);

      const file = filteredFiles.length > 0 ? filteredFiles[0] : undefined;

      if (file) {
        uploader(file);
      }
    },
    [uploader]
  );

  const onDragEnter = () => {
    setDraggedInside(true);
  };

  const onDragLeave = () => {
    setDraggedInside(false);
  };

  return { isDragging, draggedInside, onDragEnter, onDragLeave, onDrop };
};
