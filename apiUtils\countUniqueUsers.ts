// Takes in a company with all workspaces/userWorkspaces and counts all unique users excluding admins

import { ADMINS_IDS } from "~/constants";

type Company = {
  workspaces: {
    userWorkspaces: {
      userID: string;
    }[];
    invites: {
      acceptedAt: Date | null;
      email: string;
    }[];
  }[];
};

const countUniqueUsers = (company: Company) => {
  const uniqueUsers = new Set<string>();
  company.workspaces.forEach((workspace) => {
    workspace.userWorkspaces.forEach((userWorkspace) => {
      if (!ADMINS_IDS.includes(userWorkspace.userID)) {
        uniqueUsers.add(userWorkspace.userID);
      }
    });
    workspace.invites.forEach((invite) => {
      if (invite.acceptedAt === null) {
        uniqueUsers.add(invite.email);
      }
    });
  });
  return uniqueUsers.size;
};

export default countUniqueUsers;
