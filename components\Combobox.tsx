import {
  FocusEvent,
  ReactElement,
  ReactNode,
  useEffect,
  useState,
} from "react";

import * as Icons from "@heroicons/react/24/outline";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { default as classnames, default as classNames } from "classnames";
import { <PERSON><PERSON>, Divider, Tooltip } from "~/components";
import { joinWithAnd } from "~/utils";
import AddNewButton from "./AddNewButton";

import assert from "assert";
import uniq from "lodash/uniq";
import KbdTooltip from "./app/KbdTooltip";
import { Command, CommandGroup, CommandInput, CommandItem } from "./Command";
import Button from "./ui/Button";
import Popover from "./ui/Popover";

export type Width = "fit" | "full";

export type ActionButton = {
  icon?: keyof typeof Icons;
  label: string | ReactElement;
  onClick: () => void;
};

export type Option<T> = {
  /** Optioinal unique identifier of each options if the value is harder to compare equality */
  key?: string;
  /** The value of the selected option, hidden from the user */
  value: T;
  /** The user visible representation of the option */
  label: string | number | JSX.Element;
  /** The section that the option belongs to */
  sectionName?: string;
  /** If the option has a component, it will be rendered instead of the label */
  component?: ReactElement;
  /** If extra detail is needed to describe the option, displayed below the label only in the dropdown */
  detail?: string | number | JSX.Element;
  /** If an option is disabled */
  disabled?: boolean;
  /** If an option is disabled, the reason why */
  disabledReason?: string | ReactNode;
};

type ComboboxProps<T> = {
  /** The currently selected value of the input */
  value: T | null;
  /** Called when the value of the select input is changed */
  onChange: (value: T) => void;
  /** The options that the select input contains */
  options: Option<T>[];
  /** The placeholder text that will be displayed when the input is empty */
  placeholder?: string | ReactElement;
  /** The button that opens the combobox */
  triggerButton?: ReactElement;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** To use the isMulti and pass an array to value instead of a single option. */
  isMulti?: boolean;
  /** The position of the anchor of the dropdown */
  dropdownPosition?: "left" | "right";
  /** Whether the button to open the dropdown is hidden. Used with isOpen to control the dropdown
   *  manually */
  isButtonHidden?: boolean;
  /** To force the select to open */
  isOpen?: boolean;
  /** Whether a new option can be created */
  isCreateable?: boolean;
  /** Whether the input is searchable */
  isSearchable?: boolean;
  /** When true, this allows the user to remove the last element */
  isClearable?: boolean;
  /** Event handler called when a pointer event occurs outside the bounds of the component. It can be prevented by calling event.preventDefault. */
  onPointerDownOutside?: () => void;
  /** Called when the input is blurred */
  onBlur?: (e: FocusEvent<HTMLButtonElement>) => void;
  /** If the selected option should be hidden from the list */
  hideSelectedOption?: boolean;
  /** Whether the options should be uppercase */
  isUppercase?: boolean;
  /** An optional buton that appears after all of the options */
  actionButton?: ActionButton;
  /** Whether the section names should be shown */
  showSectionNames?: boolean;
  hotKeyOptions?: {
    /** The hotkeys that open the combobox */
    keys: string | string[];
    /** The label for the hotkeys */
    label: string;
  };
  /** The offset of the dropdown */
  sideOffset?: number;
  alignOffset?: number;
  /** Custom label to display when no options are found */
  emptyLabel?: string;
};

const Combobox = ({
  value,
  onChange,
  options,
  isMulti = false,
  dropdownPosition = "left",
  placeholder = <AddNewButton label="Select" />,
  triggerButton,
  disabled,
  isButtonHidden = false,
  isOpen = false,
  isSearchable = true,
  isCreateable,
  isClearable = false,
  onPointerDownOutside,
  hideSelectedOption = false,
  isUppercase = false,
  actionButton,
  showSectionNames = true,
  hotKeyOptions,
  sideOffset,
  alignOffset,
  emptyLabel,
}: ComboboxProps<any>) => {
  const [open, setOpen] = useState<boolean>(isOpen);
  const [search, setSearch] = useState<string>("");
  const optionsValue =
    isMulti && Array.isArray(value)
      ? options.filter((options) =>
          value.some(
            (val) =>
              val === options.value ||
              (val.key === options.key && val.key != null)
          )
        )
      : options.find((options) => options.value === value) || null;

  let sectionNames = uniq(options.map((option) => option.sectionName));
  if (sectionNames.length > 1) {
    if (options.some((option) => option.sectionName == null)) {
      throw new Error(
        "If any option has a sectionName, all options must have a sectionName"
      );
    }
  }

  if (sectionNames.length === 0) {
    sectionNames = [undefined];
  }

  let TriggerButton =
    typeof placeholder === "string" ? (
      <AddNewButton label={placeholder} />
    ) : (
      placeholder
    );
  if (Array.isArray(optionsValue) && optionsValue.length > 0) {
    // If any of the options have a component, we render the components
    const hasComponent = optionsValue.some((option) => option.component);
    if (hasComponent) {
      TriggerButton = (
        <div className="w-full text-left leading-7">
          {optionsValue.map((option, index) => {
            if (option.component != null) {
              return (
                <span key={`trigger-${option.key || option.label.toString()}`}>
                  {option.component}{" "}
                </span>
              );
            } else {
              return (
                <div
                  key={option.key || option.label.toString()}
                  className="font-eyebrow text-green-50"
                >
                  {option.label}
                  {index !== optionsValue.length - 1 && ","}
                </div>
              );
            }
          })}
        </div>
      );
    } else {
      TriggerButton = (
        <Badge
          intent="secondary"
          kind="hollow"
          label={joinWithAnd(
            optionsValue.map((option) => option.label.toString())
          )}
          showHoverState={true}
        />
      );
    }
  } else if (!Array.isArray(optionsValue) && optionsValue != null) {
    TriggerButton = optionsValue.component || (
      <div className="font-eyebrow">{optionsValue.label}</div>
    );
  }

  useEffect(() => {
    setOpen(isOpen);
  }, [isOpen]);

  const filteredOptions = options.filter((option) => {
    const hideSelected =
      !isMulti && hideSelectedOption && option.value === value;

    if (hideSelected) {
      return false;
    }

    const lowercaseSearch = search.toLowerCase();

    return (
      option.key?.toString().toLowerCase().includes(lowercaseSearch) ||
      option.value?.toString().toLowerCase().includes(lowercaseSearch) ||
      option.label.toString().toLowerCase().includes(lowercaseSearch)
    );
  });

  const ActionButtonIcon = Icons[actionButton?.icon || "PlusIcon"];

  return (
    <Popover.Root
      open={open}
      onOpenChange={() => {
        if (!isOpen) {
          setOpen(!open);
        }
      }}
      defaultOpen={isOpen}
    >
      {isButtonHidden ? (
        <Popover.Anchor>
          <div className="h-0 w-0" />
        </Popover.Anchor>
      ) : (
        <KbdTooltip
          hotKeys={hotKeyOptions?.keys}
          label={hotKeyOptions?.label}
          onKeyPress={(_shortcut, e) => {
            e.preventDefault();
            setOpen(true);
          }}
        >
          <Popover.Trigger
            className="outline-secondary w-fit min-w-fit rounded-full"
            disabled={disabled}
          >
            {triggerButton || TriggerButton}
          </Popover.Trigger>
        </KbdTooltip>
      )}

      <Popover.Content
        onPointerDownOutside={onPointerDownOutside}
        align={dropdownPosition === "left" ? "start" : "end"}
        className={classNames(
          "w-content bg-surface w-fit rounded-lg py-1 shadow-xl focus:outline-hidden"
        )}
        sideOffset={sideOffset}
        alignOffset={alignOffset}
      >
        <Command shouldFilter={false}>
          {isSearchable && (
            <CommandInput
              value={search}
              onValueChange={setSearch}
              placeholder="Search..."
            />
          )}
          {isCreateable && search.length > 0 && (
            <button
              type="button"
              className="hover:bg-slate mt-1.5 ml-1.5 flex w-fit items-center rounded-md py-1.5"
              onClick={() => {
                if (Array.isArray(value)) {
                  onChange([...value, search]);
                } else {
                  onChange(search);
                }
                setOpen(false);
                setSearch("");
              }}
            >
              <div className="font-body-sm px-2 whitespace-nowrap text-black">
                + Create new: <span className="text-medium-gray">{search}</span>
              </div>
            </button>
          )}
          {isCreateable &&
            filteredOptions.length === 0 &&
            search.length === 0 && (
              <div className="text-medium-gray font-eyebrow px-4 py-2 text-left whitespace-nowrap">
                No options found
              </div>
            )}
          {!isCreateable && filteredOptions.length === 0 && (
            <div className="text-medium-gray font-eyebrow px-4 pt-2 text-left whitespace-nowrap">
              {search.length > 0
                ? "No options found"
                : emptyLabel || "No options found"}
            </div>
          )}
          <CommandGroup className="scrollbar-hidden max-h-60 overflow-scroll">
            <div>
              {sectionNames.map((sectionName, index) => {
                const sectionOptions =
                  sectionName != null
                    ? filteredOptions.filter(
                        (option) => option.sectionName === sectionName
                      )
                    : filteredOptions;

                if (sectionOptions.length === 0) {
                  return null;
                }

                return (
                  <div key={sectionName}>
                    <div>
                      {showSectionNames === true && (
                        <div className="font-eyebrow-sm text-medium-dark-gray mb-1">
                          {sectionName}
                        </div>
                      )}
                      {sectionOptions.map((option) => {
                        const selected = Array.isArray(optionsValue)
                          ? optionsValue.some(
                              (val) =>
                                val.value === option.value ||
                                (val.key === option.key && val.key != null)
                            )
                          : optionsValue?.value === option.value;
                        return (
                          <CommandItem
                            key={option.key || option.label.toString()}
                            className={classnames(
                              "relative flex w-full items-center gap-2 rounded-md py-1.5 select-none",
                              {
                                "text-secondary": selected,
                                "hover:bg-slate aria-selected:bg-slate cursor-pointer":
                                  !option.disabled,
                                "text-medium-gray cursor-not-allowed opacity-50":
                                  option.disabled,
                              }
                            )}
                            disabled={option.disabled}
                            onSelect={() => {
                              if (!isMulti) {
                                onChange(
                                  selected && isClearable ? null : option.value
                                );
                                setOpen(false);
                              } else {
                                assert(
                                  optionsValue == null ||
                                    Array.isArray(optionsValue)
                                );
                                // If the option is already selected, we remove it otherwise we add it
                                if (selected) {
                                  onChange(
                                    (optionsValue || [])
                                      .filter(
                                        (val) =>
                                          val.value !== option.value &&
                                          (val.key !== option.key ||
                                            val.key == null)
                                      )
                                      .map((option) => option.value)
                                  );
                                } else {
                                  onChange([
                                    ...(optionsValue || []).map(
                                      (option) => option.value
                                    ),
                                    option.value,
                                  ]);
                                }
                              }
                            }}
                          >
                            {isMulti && (
                              <div
                                className={classNames(
                                  "mt-0.5 flex h-3 w-3 items-center justify-center rounded-xs border",
                                  { "border-medium-gray": !selected },
                                  { "border-secondary": selected }
                                )}
                              >
                                <div
                                  className={classNames(
                                    "h-full w-full",
                                    { "bg-secondary": selected && !disabled },
                                    {
                                      "bg-secondary-light":
                                        selected && disabled,
                                    },
                                    { "bg-surface": !selected }
                                  )}
                                />
                              </div>
                            )}
                            <Tooltip
                              value={
                                option.disabled ? option.disabledReason : null
                              }
                            >
                              <div className="flex w-full items-center justify-between">
                                <div className="flex flex-col whitespace-nowrap">
                                  <span
                                    className={classNames({
                                      "font-body-sm": !isUppercase,
                                      "font-eyebrow": isUppercase,
                                    })}
                                  >
                                    {option.label}
                                  </span>
                                  {option.detail ? (
                                    <div
                                      className={classnames(
                                        "font-body-xs text-medium-dark-gray transition-colors"
                                      )}
                                    >
                                      {option.detail}
                                    </div>
                                  ) : null}
                                </div>
                                {isClearable && selected && (
                                  <button
                                    className="rounded-full p-1 opacity-0 transition-all hover:bg-white hover:opacity-100"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onChange(undefined);
                                    }}
                                  >
                                    <XMarkIcon className="h-3 w-3" />
                                  </button>
                                )}
                              </div>
                            </Tooltip>
                          </CommandItem>
                        );
                      })}
                    </div>
                    {!showSectionNames && index < sectionNames.length - 1 && (
                      <Divider padding="sm" />
                    )}
                  </div>
                );
              })}
            </div>
            {actionButton != null && (
              <div className="flex flex-col gap-1.5 pb-2.5">
                <Divider padding="sm" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={actionButton.onClick}
                >
                  {actionButton.icon != null && (
                    <ActionButtonIcon className="h-4 w-4" />
                  )}
                  {actionButton.label}
                </Button>
              </div>
            )}
          </CommandGroup>
        </Command>
      </Popover.Content>
    </Popover.Root>
  );
};

export default Combobox;
