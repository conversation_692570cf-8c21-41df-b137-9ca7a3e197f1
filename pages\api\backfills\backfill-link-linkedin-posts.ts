import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const vanityName = req.query.vanityName as string;

  const linkedinPosts = await db.linkedInPost.findMany({
    where: {
      author: {
        vanityName,
      },
    },
    select: {
      id: true,
    },
  });

  const importedUrns = linkedinPosts.map((post) => post.id);

  const assemblyPosts = await db.post.findMany({
    where: {
      OR: [
        {
          link: {
            in: importedUrns.map(
              (urn) => `https://www.linkedin.com/feed/update/${urn}`
            ),
          },
        },
        {
          linkedInLink: {
            in: importedUrns.map(
              (urn) => `https://www.linkedin.com/feed/update/${urn}`
            ),
          },
        },
      ],
      linkedInPostID: null,
    },
  });

  const postPromises = assemblyPosts.map(async (post) => {
    const { link, linkedInLink } = post;

    const postLink = linkedInLink || link;

    if (!postLink) {
      return null;
    }

    const urn = postLink.split("/").pop();

    await db.post.update({
      where: {
        id: post.id,
      },
      data: {
        linkedInPostID: urn,
      },
    });
  });

  await Promise.all(postPromises);

  res.status(200).send({
    numPostsConnected: assemblyPosts.length,
  });
};

export default handler;
