import completeOnboardingTask from "apiUtils/completeOnboardingTask";
import countSocialProfiles from "apiUtils/countSocialProfiles";
import addSubscriptionItem from "apiUtils/stripe/addSubscriptionItem";
import axios from "axios";
import { StatusCodes } from "http-status-codes";
import type { NextApiRequest, NextApiResponse } from "next";
import getUserWorkspaceWithSocialProfileUsage from "server/routers/utils/getUserWorkspaceWithSocialProfileUsage";
import prisma, { db } from "~/clients/Prisma";
import slack from "~/clients/Slack";
import createSupabaseServerClient from "~/clients/createSupabaseServerClient";
import Instagram from "~/clients/facebook/instagram/Instagram";
import posthog from "~/clients/posthog";
import { INSTAGRAM_SCOPES } from "~/components/app/settings/profiles/InstagramConnectPage";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { getCookie } from "~/utils/cookies";

const handleError = (
  res: NextApiResponse,
  message: string,
  referrer: string
) => {
  const params = new URLSearchParams({
    error_message: message,
    channel: "Instagram",
  });

  const url = `${referrer}?${params}`;

  return res.redirect(StatusCodes.MOVED_TEMPORARILY, url);
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res
      .status(StatusCodes.METHOD_NOT_ALLOWED)
      .json({ message: "Method not allowed" });
  }

  const { code, state } = req.query;
  const instagramIntegrationCookie = getCookie(
    "INSTAGRAM_INTEGRATION",
    req.headers.cookie
  );

  if (!instagramIntegrationCookie) {
    return handleError(
      res,
      "Session expired, please try again",
      "/settings/profiles"
    );
  }

  const { accountID, referrer } = instagramIntegrationCookie;

  if (!code) {
    return handleError(res, "No authorization code provided", referrer);
  } else if (!state || state !== instagramIntegrationCookie.state) {
    return handleError(res, "Session expired, please try again", referrer);
  }

  const supabase = await createSupabaseServerClient(req.headers.cookie);

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const workspace = await getUserWorkspaceWithSocialProfileUsage({
    user,
    currentWorkspaceID: getCookie("CURRENT_WORKSPACE_ID", req.headers.cookie),
  });

  if (!user || !workspace) {
    return handleError(res, "User not logged in", referrer);
  }

  try {
    let shortLivedToken;
    let scopes: string[];

    try {
      const formData = new URLSearchParams();
      formData.append(
        "client_id",
        process.env.NEXT_PUBLIC_INSTAGRAM_CLIENT_ID!
      );
      formData.append("client_secret", process.env.INSTAGRAM_CLIENT_SECRET!);
      formData.append("code", code as string);
      formData.append("grant_type", "authorization_code");
      formData.append(
        "redirect_uri",
        `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/instagram/callback`
      );

      const response = await axios.post(
        "https://api.instagram.com/oauth/access_token",
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const accessTokenData = response.data as {
        access_token: string;
        user_id: string;
        permissions: string[];
      };

      scopes = accessTokenData.permissions;

      if (!INSTAGRAM_SCOPES.every((scope) => scopes.includes(scope))) {
        return handleError(
          res,
          "Please accept all permissions to connect your Instagram account",
          referrer
        );
      }

      shortLivedToken = response.data.access_token;
    } catch (error) {
      console.error("Instagram Token Response Error", error);
      return handleError(
        res,
        "Unable to authenticate with Instagram",
        referrer
      );
    }

    if (!shortLivedToken) {
      return handleError(
        res,
        "Unable to authenticate with Instagram",
        referrer
      );
    }

    const authTokenResponse =
      await Instagram.accessToken.exchangeShortLivedToken(shortLivedToken);
    if (!authTokenResponse.success) {
      console.error(
        "Instagram Token Response Errors",
        authTokenResponse.errors
      );
      return handleError(
        res,
        "Unable to authenticate with Instagram",
        referrer
      );
    }

    const { accessToken, expiresAt } = authTokenResponse.data;
    const instagram = new Instagram(accessToken, {
      platform: "Instagram",
    });
    const userInfoResponse = await instagram.profile.getMe();

    if (!userInfoResponse.success) {
      return handleError(
        res,
        "Unable to authenticate with Instagram",
        referrer
      );
    }

    const { data: userInfo } = userInfoResponse;

    const instagramAccount = await db.instagramAccount.upsert({
      where: {
        externalID: userInfo.id,
      },
      update: {
        name: userInfo.name,
        username: userInfo.username,
        profileImageUrl: userInfo.profileImageUrl,
        profileImageExpiresAt: userInfo.profileImageExpiresAt,
        accountType: userInfo.accountType,
      },
      create: {
        externalID: userInfo.id,
        name: userInfo.name,
        username: userInfo.username,
        profileImageUrl: userInfo.profileImageUrl,
        profileImageExpiresAt: userInfo.profileImageExpiresAt,
        accountType: userInfo.accountType,
      },
    });

    const existingIntegration = await db.instagramIntegration.findFirst({
      where: {
        account: {
          externalID: userInfo.id,
        },
      },
      select: {
        id: true,
        workspaceID: true,
        account: {
          select: {
            username: true,
          },
        },
      },
    });

    if (accountID && userInfo.id !== accountID) {
      const expectedInstagramAccount = await db.instagramAccount.findFirst({
        where: {
          externalID: accountID,
        },
      });

      if (expectedInstagramAccount) {
        return handleError(
          res,
          `Switch to @${expectedInstagramAccount.username} on Instagram first and then try again`,
          referrer
        );
      } else {
        return handleError(
          res,
          "Please login with the account you want to connect first before integrating",
          referrer
        );
      }
    }

    if (existingIntegration) {
      if (existingIntegration.workspaceID !== workspace.id) {
        return handleError(
          res,
          `@${existingIntegration.account.username} is already integrated on another Assembly workspace. Message support if this is a mistake.`,
          referrer
        );
      }

      await db.instagramIntegration.update({
        where: {
          id: existingIntegration.id,
        },
        data: {
          accessToken,
          accessTokenExpiresAt: expiresAt,
          scope: scopes,
          integrationSource: "Instagram",
          isReintegrationRequired: false,
        },
      });
    } else {
      const { subscription } = workspace;

      if (subscription) {
        const numSocialProfiles = countSocialProfiles(workspace);

        if (numSocialProfiles >= subscription.socialProfileLimit) {
          await addSubscriptionItem({
            workspaceID: workspace.id,
            itemName: "ADDITIONAL_SOCIAL_PROFILE",
            quantity: numSocialProfiles - subscription.socialProfileLimit + 1,
          });
        }
      }

      const instagramIntegration = await db.instagramIntegration.create({
        data: {
          workspaceID: workspace.id,
          accessToken,
          accessTokenExpiresAt: expiresAt,
          userID: user.id,
          accountID: instagramAccount.id,
          scope: scopes,
          integrationSource: "Instagram",
        },
        include: {
          account: true,
          user: true,
        },
      });

      const deletedIntegrations = await prisma.instagramIntegration.findMany({
        where: {
          accountID: instagramAccount.id,
          workspaceID: workspace.id,
          deletedTimeStamp: {
            not: 0,
          },
        },
      });

      if (deletedIntegrations.length > 0) {
        await db.post.updateMany({
          where: {
            instagramIntegrationID: {
              in: deletedIntegrations.map((integration) => integration.id),
            },
          },
          data: {
            instagramIntegrationID: instagramIntegration.id,
          },
        });
      }

      posthog.capture({
        distinctId: user.id,
        event: POSTHOG_EVENTS.socialAccount.connected,
        properties: {
          channel: "Instagram",
        },
        groups: {
          workspace: workspace.id,
          company: workspace.company.id,
        },
      });

      await slack.newConnectedAccount({
        username: `@${instagramAccount.username}`,
        channel: "Instagram",
        url: `https://www.instagram.com/${instagramAccount.username}`,
        workspace,
        connectedBy: instagramIntegration.user,
      });

      if (workspace._count.instagramIntegrations === 0) {
        await db.postDefault.upsert({
          where: {
            workspaceID: workspace.id,
          },
          create: {
            workspaceID: workspace.id,
            instagramIntegrationID: instagramIntegration.id,
          },
          update: {
            instagramIntegrationID: instagramIntegration.id,
          },
        });
      }

      await completeOnboardingTask({
        workspaceID: workspace.id,
        userID: user.id,
        taskType: "CONNECT_ACCOUNT",
        state: "COMPLETED",
      });
    }

    const params = new URLSearchParams({
      account_name: `@${userInfo.username}`,
      channel: "Instagram",
      flow: accountID
        ? "reconnect_account"
        : existingIntegration
          ? "existing_account"
          : "new_account",
    });

    if (referrer.includes("/settings/profiles")) {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/profiles?${params}`
      );
    } else {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/instagram?${params}`
      );
    }
  } catch (error) {
    console.error("Instagram auth error:", error);
    return handleError(res, "Error connecting Instagram account", referrer);
  }
}
