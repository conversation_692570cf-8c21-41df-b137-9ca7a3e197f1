import { Asset } from "@prisma/client";
import sortBy from "lodash/sortBy";
import { AssetType } from "~/types/Asset";
import { removeEmptyElems } from "~/utils";
import enhanceAssetType from "./assets/enhanceAssetType";

const getAssetsForTikTok = (post: {
  tikTokContent: {
    assets: {
      id: string;
      position: number;
      asset: Asset;
    }[];
  } | null;
}): (AssetType & { position: number })[] => {
  if (!post.tikTokContent) {
    return [];
  }

  return removeEmptyElems(
    sortBy(post.tikTokContent.assets, "position").map((asset) => ({
      ...enhanceAssetType(asset.asset),
      id: asset.id,
      position: asset.position,
    }))
  );
};

export default getAssetsForTikTok;
