import { ReactElement, useEffect, useRef, useState } from "react";

import { ArrowDownIcon, ArrowDownTrayIcon } from "@heroicons/react/24/outline";
import assert from "assert";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import snakeCase from "lodash/snakeCase";
import uniqBy from "lodash/uniqBy";
import { DateTime } from "luxon";
import DataTable, { Selector, TableColumn } from "react-data-table-component";
import { Checkbox, Combobox } from "~/components";
import { downloadCSV } from "~/utils";
import { Option } from "./Combobox";
import DateRangeFilter, { Preset } from "./DateRangeFilter";
import Loader from "./Loader";
import TextInput from "./TextInput";
import ColumnSelect from "./app/ColumnSelect";
import { cleanUnicodeCharacters } from "./app/tiptap/utils/unicodeTextFormattingTools";
import Button from "./ui/Button";

type ExpandableRowsComponent = any;

type Props<T> = {
  title?: string | ReactElement;
  stickyHeader?: boolean;
  tableTitle?: string | ReactElement;
  overviewFn?: (data: T[]) => ReactElement | null;
  csvName?: string;
  columns: (TableColumn<T> & {
    id: string;
    label?: ReactElement | string;
    selector: Selector<any>;
    searchable?: boolean;
    dateFilterable?: boolean;
    minDate?: Date;
    maxDate?: Date;
    defaultStartDate?: Date | null;
    onStartDateChange?: (date: Date | null) => void;
    defaultEndDate?: Date | null;
    onEndDateChange?: (date: Date | null) => void;
    defaultOmitted?: boolean;
    hideFromColumnSelect?: boolean;
    sectionName?: string;
    filterable?: boolean;
    filterValue?: (row: T) => string | undefined | null;
    filterLabel?: (row: T) => string;
    defaultFilterValues?: any[];
  })[];
  noTableHead?: boolean;
  showColumnSelect?: boolean;
  onVisibleColumnChange?: (visibleColumns: string[]) => void;
  onSelectedRowsChange?: ({
    allSelected,
    selectedCount,
    selectedRows,
  }: {
    allSelected: boolean;
    selectedCount: number;
    selectedRows: T[];
  }) => void;
  selectableRowsSingle?: boolean;
  selectableRowSelected?: (row: T) => boolean;
  chartComponent?: (row: T) => ReactElement | null;
  noDataComponent?: ReactElement | string;
  showAllRows?: boolean;
  noDataComponentOffsetCenter?: boolean;
  actionContent?: ReactElement | null;
  showDownloadCSV?: boolean;
  defaultSortFieldId?: string | number;
  defaultSortDirection?: "asc" | "desc";
  expandableRowExpanded?: (row: T) => boolean;
  ExpandableRowsComponent?: ExpandableRowsComponent;
  dateRangePresets?: Preset[];
  data?: T[] | null;
  isLoading?: boolean;
  onRowClicked?: (row: T, event: React.MouseEvent) => void;
  hideSearch?: boolean;
  persistTableHead?: boolean;
  showAnalyticsExport?: boolean;
};

const Table = ({
  title,
  stickyHeader = false,
  overviewFn,
  tableTitle,
  csvName,
  columns,
  data,
  isLoading,
  noTableHead,
  showColumnSelect,
  onVisibleColumnChange,
  onSelectedRowsChange,
  selectableRowsSingle,
  selectableRowSelected,
  chartComponent,
  noDataComponent,
  noDataComponentOffsetCenter = true,
  actionContent,
  showDownloadCSV = false,
  defaultSortFieldId,
  defaultSortDirection,
  expandableRowExpanded,
  ExpandableRowsComponent,
  dateRangePresets,
  onRowClicked,
  hideSearch = false,
  persistTableHead = false,
  showAnalyticsExport = false,
  showAllRows = false,
}: Props<any>) => {
  const [showGraphs, setShowGraphs] = useState<boolean>(true);
  const [search, setSearch] = useState<string>("");
  // const [search, setSearch] = useQueryState("search", {
  // defaultValue: "",
  // });
  const [filterOptions, setFilterOptions] = useState<{
    [column: string]: Option<string>[];
  }>({});
  const [filters, setFilters] = useState<{ [column: string]: string[] }>({});
  // const [filters, setFilters] = useQueryState(
  //   "filters",
  //   parseAsJson<{ [column: string]: string[] }>().withDefault({})
  // );
  const [dateFilters, setDateFilters] = useState<{
    [column: string]: { startDate?: Date; endDate?: Date };
  }>({});
  const [defaultsSet, setDefaultsSet] = useState<boolean>(false);
  const [shownColumns, setShownColumns] = useState<string[]>(
    columns
      .filter((column) => !column.defaultOmitted)
      .map((column) => column.id)
  );

  const headerHeight = 51;
  const paginationHeight = 56;
  const [offsetTop, setOffsetTop] = useState<number>(0);
  const tableRef = useRef<HTMLDivElement>();

  const [hasSelectionChanged, setHasSelectionChanged] =
    useState<boolean>(false);

  const [isExportable, setIsExportable] = useState<boolean>(false);

  useEffect(() => {
    if (tableRef.current) {
      setOffsetTop(tableRef.current.offsetTop);
    }
  }, [tableRef]);

  const searchableColumns = columns.filter((column) => column.searchable);
  const filterableColumns = columns.filter((column) => column.filterable);
  const dateFilterableColumns = columns.filter(
    (column) => column.dateFilterable
  );

  let filteredData = data;
  // Filter by search
  if (searchableColumns && filteredData) {
    filteredData = filteredData.filter((row) => {
      return searchableColumns
        .map((columm) => {
          return columm.selector(row)?.toString().trim().toLowerCase() || "";
        })
        .join(" ")
        .includes(search.toLowerCase());
    });
  }

  // Options filter
  if (filteredData && filters && Object.keys(filterOptions).length) {
    filteredData = filteredData.filter((row) => {
      if (Object.values(filters).flat().length === 0) {
        return true;
      }

      return filterableColumns.every((column) => {
        if (column.id in filters) {
          const values = filters[column.id].map((option) => option);
          if (values.length === 0) {
            return true;
          }

          const { filterValue, selector } = column;
          return values.includes(
            filterValue ? filterValue(row) || "" : (selector(row) as string)
          );
        } else {
          return true;
        }
      });
    });
  }

  // Date filters
  if (filteredData && Object.keys(dateFilters).length) {
    filteredData = filteredData.filter((row) => {
      return dateFilterableColumns.every((column) => {
        const dateFilter = dateFilters[column.id];
        if (
          dateFilter == null ||
          (dateFilter.endDate == null && dateFilter.startDate == null)
        ) {
          return true;
        }

        const { selector } = column;
        const { startDate, endDate } = dateFilter;
        const value = selector(row) as string | undefined;
        if (value == null) {
          return false;
        }

        if (startDate && endDate == null) {
          return (
            DateTime.fromJSDate(startDate).startOf("day") <=
            DateTime.fromISO(value)
          );
        } else if (startDate == null && endDate) {
          return (
            DateTime.fromJSDate(endDate).endOf("day") >= DateTime.fromISO(value)
          );
        } else if (startDate != null && endDate != null) {
          return (
            DateTime.fromJSDate(startDate).startOf("day") <=
              DateTime.fromISO(value) &&
            DateTime.fromJSDate(endDate).endOf("day") >= DateTime.fromISO(value)
          );
        } else {
          return true;
        }
      });
    });
  }

  const progressPending = !filteredData || isLoading;

  const handleDownloadCSV = () => {
    if (!filteredData) {
      return;
    }

    const dateColumn = columns.find((column) => column.id === "datetime_utc");
    const sortedData = dateColumn
      ? [...filteredData].sort((currentRow, nextRow) => {
          const currentDate = DateTime.fromISO(
            String(dateColumn.selector(currentRow))
          );
          const nextDate = DateTime.fromISO(
            String(dateColumn.selector(nextRow))
          );
          return nextDate.toMillis() - currentDate.toMillis();
        })
      : filteredData;

    const headers = columns.map((column) => column.id);
    const rows = sortedData.map((row) =>
      columns.map((column) => {
        const value = column.selector(row);
        if (column.id === "datetime_utc") {
          const dateTime = DateTime.fromISO(String(value), { zone: "utc" });
          if (dateTime.isValid) {
            return dateTime.toFormat("yyyy-MM-dd'T'HH:mm:ss");
          }
        }
        if (typeof value === "string") {
          return cleanUnicodeCharacters(value);
        }
        return value;
      })
    );

    const startDate = Object.values(dateFilters)
      .map((filter) => filter.startDate)
      .filter(Boolean)[0];
    const endDate = Object.values(dateFilters)
      .map((filter) => filter.endDate)
      .filter(Boolean)[0];

    const formattedStartDate = startDate
      ? DateTime.fromJSDate(startDate).toFormat("yyyy-MM-dd")
      : "unknown";
    const formattedEndDate = endDate
      ? DateTime.fromJSDate(endDate).toFormat("yyyy-MM-dd")
      : "unknown";

    downloadCSV({
      headers,
      rows,
      fileName: csvName
        ? `${csvName}_data_${formattedStartDate}_to_${formattedEndDate}.csv`
        : `${
            typeof title == "string" ? snakeCase(title) : "table_export"
          }_${DateTime.now().toISO()}.csv`,
    });
  };

  useEffect(() => {
    if (data?.length && !isLoading) {
      const options: { [column: string]: Option<string>[] } = {};
      const defaultFilters: { [column: string]: string[] } = {};

      filterableColumns.forEach((column) => {
        const { filterValue, filterLabel, selector } = column;

        const values = data.map((row) => ({
          value: filterValue ? filterValue(row) || null : selector(row),
          label: filterLabel ? filterLabel(row) : selector(row).toString(),
        })) as Option<string>[];

        const uniqueOptions = uniqBy(values, "value").filter(
          (options) => options.value != null
        );

        options[column.id] = uniqueOptions;

        const defaultValues = column.defaultFilterValues
          ? uniqueOptions.filter((option) =>
              column.defaultFilterValues?.includes(option.value)
            )
          : null;

        if (defaultValues) {
          defaultFilters[column.id] = defaultValues.map(
            (option) => option.value
          );
        }
      });

      const defaultDateFilters: {
        [column: string]: { startDate?: Date; endDate?: Date };
      } = {};
      dateFilterableColumns.forEach((column) => {
        defaultDateFilters[column.id] = {
          startDate: column.defaultStartDate || undefined,
          endDate: column.defaultEndDate || undefined,
        };
      });

      if (!defaultsSet) {
        setDateFilters(defaultDateFilters);
        setFilters(defaultFilters);
        setDefaultsSet(true);
      }

      setFilterOptions(options);
    }
  }, [columns, data, isLoading]);

  useEffect(() => {
    setIsExportable(!!filteredData && filteredData.length > 0);
  }, [filteredData, dateFilters]);

  return (
    <div
      className={classNames("scrollbar-hidden relative overflow-auto", {
        "h-full": data && data.length > 0,
        "h-fit": data == null || data.length === 0,
        "pb-20": stickyHeader,
      })}
    >
      <div
        className={classNames({
          "border-lightest-gray sticky top-0 flex flex-col gap-3 border-b bg-white pb-5":
            stickyHeader,
        })}
      >
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            {title && <h4 className="mx-3.5">{title}</h4>}
            {!hideSearch && searchableColumns.length > 0 && (
              <div className="w-60">
                <TextInput
                  value={search}
                  onChange={setSearch}
                  placeholder="Search..."
                  showSearchIcon={true}
                />
              </div>
            )}
          </div>
          {actionContent != null && (
            <div className="mr-2.5">{actionContent}</div>
          )}
          {!tableTitle && showColumnSelect && (
            <ColumnSelect
              columns={columns
                .filter((column) => !column.hideFromColumnSelect)
                .map((column) => ({
                  id: column.id,
                  label: typeof column.name === "string" ? column.name : "",
                  value: shownColumns.includes(column.id),
                  sectionName: column.sectionName,
                }))}
              onChange={(changedColumns) => {
                const shownColumns = changedColumns
                  .filter((column) => column.value)
                  .map((column) => column.id);

                const visibleColumns = [
                  ...shownColumns,
                  ...columns
                    .filter((column) => !!column.hideFromColumnSelect)
                    .map((column) => column.id),
                ];

                onVisibleColumnChange?.(visibleColumns);

                setShownColumns(visibleColumns);
              }}
            />
          )}
          {showDownloadCSV && (
            <Button size="sm" onClick={handleDownloadCSV}>
              <ArrowDownTrayIcon className="h-4 w-4" />
              Download CSV
            </Button>
          )}
        </div>
        {overviewFn && (
          <div
            className={classNames("px-3.5", {
              "mt-2": !!title,
            })}
          >
            {overviewFn(filteredData || [])}
          </div>
        )}
        <div className="mx-3.5 flex items-center justify-between pr-3">
          <div className="mt-2 flex items-center gap-3">
            {dateFilterableColumns.map((column) => {
              return (
                <DateRangeFilter
                  key={column.id}
                  startDate={dateFilters[column.id]?.startDate}
                  isClearable={true}
                  minDate={column.minDate}
                  maxDate={column.maxDate}
                  presets={dateRangePresets}
                  onStartDateChange={(startDate) => {
                    if (column.onStartDateChange) {
                      column.onStartDateChange(startDate);
                    }

                    setDateFilters((dateFilters) => ({
                      ...dateFilters,
                      [column.id]: {
                        endDate: dateFilters[column.id]?.endDate,
                        startDate: startDate || undefined,
                      },
                    }));
                  }}
                  endDate={dateFilters[column.id]?.endDate}
                  onEndDateChange={(endDate) => {
                    if (column.onEndDateChange) {
                      column.onEndDateChange(endDate);
                    }

                    setDateFilters((dateFilters) => ({
                      ...dateFilters,
                      [column.id]: {
                        startDate: dateFilters[column.id]?.startDate,
                        endDate: endDate || undefined,
                      },
                    }));
                  }}
                />
              );
            })}
            {Object.keys(filterOptions).map((column) => {
              const col = columns.find((col) => col.id === column);
              assert(col);

              const options = filterOptions[column];

              if (options.length === 0) {
                return null;
              }

              return (
                <div key={column}>
                  <Combobox
                    value={filters[column]}
                    placeholder={`Filter ${col.name}`}
                    onChange={(value) => {
                      setFilters({ ...filters, [column]: value || [] });
                    }}
                    isMulti={true}
                    isClearable={true}
                    options={options}
                  />
                </div>
              );
            })}
          </div>
          {showAnalyticsExport && (
            <Button
              variant="secondary"
              size="sm"
              onClick={() => handleDownloadCSV()}
              disabled={!isExportable}
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              Export as CSV
            </Button>
          )}
        </div>
      </div>
      <AnimatePresence>
        {chartComponent != null && showGraphs && (
          <motion.div
            className="overflow-clip"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 100 }}
            exit={{ height: 0, opacity: 0 }}
          >
            {chartComponent(filteredData || [])}
          </motion.div>
        )}
      </AnimatePresence>
      {/* @ts-ignore TODO(francisco): not sure what the ref type is supposed to be */}
      <div ref={tableRef}>
        <div className="flex items-center justify-between px-4">
          {!progressPending && tableTitle && <h4>{tableTitle}</h4>}
          {!progressPending && tableTitle && showColumnSelect && (
            <ColumnSelect
              columns={columns
                .filter((column) => !column.hideFromColumnSelect)
                .map((column) => ({
                  id: column.id,
                  label:
                    typeof column.name === "string"
                      ? column.name
                      : typeof column.label === "string"
                        ? column.label
                        : "",
                  value: shownColumns.includes(column.id),
                  sectionName: column.sectionName,
                }))}
              onChange={(changedColumns) => {
                const shownColumns = changedColumns
                  .filter((column) => column.value)
                  .map((column) => column.id);

                const visibleColumns = [
                  ...shownColumns,
                  ...columns
                    .filter((column) => !!column.hideFromColumnSelect)
                    .map((column) => column.id),
                ];

                onVisibleColumnChange?.(visibleColumns);

                setShownColumns(visibleColumns);
              }}
            />
          )}
        </div>
        <DataTable
          columns={columns.map((column) => ({
            ...column,
            name: column.label || column.name,
            omit: column.omit || !shownColumns.includes(column.id),
          }))}
          data={filteredData || []}
          onRowClicked={onRowClicked}
          theme="assembly"
          pagination={
            filteredData != null && filteredData?.length > 15 && !showAllRows
          }
          noTableHead={noTableHead}
          highlightOnHover={!!onRowClicked}
          pointerOnHover={!!onRowClicked}
          fixedHeader={true}
          noDataComponent={
            noDataComponent ? (
              typeof noDataComponent === "string" ? (
                <div className="my-4 flex w-full items-center justify-start pl-4">
                  <span className="text-medium-gray font-body-sm">
                    {noDataComponent}
                  </span>
                </div>
              ) : (
                <div
                  className="flex w-full items-center justify-center"
                  style={{
                    height: noDataComponentOffsetCenter
                      ? `calc(100vh - ${2 * offsetTop}px)`
                      : "100%",
                  }}
                >
                  {noDataComponent}
                </div>
              )
            ) : undefined
          }
          defaultSortFieldId={defaultSortFieldId}
          defaultSortAsc={defaultSortDirection === "asc"}
          fixedHeaderScrollHeight={
            typeof window != "undefined"
              ? `${window.innerHeight - headerHeight - 2 * paginationHeight}px`
              : undefined
          }
          progressPending={progressPending}
          progressComponent={<Loader size="lg" />}
          paginationPerPage={15}
          selectableRows={!!onSelectedRowsChange}
          selectableRowsSingle={selectableRowsSingle}
          onSelectedRowsChange={(selection: {
            allSelected: boolean;
            selectedCount: number;
            selectedRows: any[];
          }) => {
            if (onSelectedRowsChange) {
              setHasSelectionChanged(true);
              onSelectedRowsChange(selection);
            }
          }}
          // @ts-ignore JSX.Element is fine for ReactNode
          selectableRowsComponent={(props: {
            checked: boolean;
            disabled: boolean;
            onClick: () => void;
          }) => {
            return (
              <Checkbox
                value={props.checked}
                onChange={props.onClick}
                disabled={props.disabled}
              />
            );
          }}
          selectableRowSelected={
            !hasSelectionChanged && selectableRowSelected
              ? selectableRowSelected
              : undefined
          }
          sortIcon={<ArrowDownIcon className="text-secondary! h-3! w-3!" />}
          expandableRowExpanded={expandableRowExpanded}
          expandableRows={!!ExpandableRowsComponent}
          expandableRowsComponent={ExpandableRowsComponent}
          paginationRowsPerPageOptions={[15, 30, 50, 100]}
          paginationComponentOptions={{ selectAllRowsItem: true }}
          persistTableHead={persistTableHead}
        />
      </div>
    </div>
  );
};

export default Table;
