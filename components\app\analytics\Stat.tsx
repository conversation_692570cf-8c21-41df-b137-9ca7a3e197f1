import { InformationCircleIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import Tooltip from "~/components/Tooltip";

type Props = {
  label: string;
  intent?: "none" | "basic" | "success" | "danger";
  value?: string | number | null;
  tooltipText?: string;
  isLoading?: boolean;
};

const Stat = ({
  label,
  intent = "none",
  value,
  tooltipText,
  isLoading,
}: Props) => {
  if (typeof value === "number") {
    if (value > 1000 && value < 10000) {
      value = value.toLocaleString();
    } else if (value >= 10000) {
      value = `${(value / 1000).toLocaleString()}K`;
    }
  }

  if (value == null && !isLoading) {
    return null;
  }

  return (
    <div>
      <div className="flex items-center">
        <div className="font-eyebrow text-medium-gray">{label}</div>
        {tooltipText ? (
          <>
            <div className="pl-1"> </div>
            <Tooltip side="top" value={tooltipText}>
              <InformationCircleIcon className="text-secondary h-3 w-3" />
            </Tooltip>
          </>
        ) : null}
      </div>
      {isLoading ? (
        <div className="bg-light-gray h-12 w-24 animate-pulse rounded-md" />
      ) : (
        <div
          className={classNames("h3", {
            "text-black": intent === "none",
            "text-secondary": intent === "basic",
            "text-success": intent === "success",
            "text-danger": intent === "danger",
          })}
        >
          {value}
        </div>
      )}
    </div>
  );
};

export default Stat;
