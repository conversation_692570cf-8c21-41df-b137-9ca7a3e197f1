import { prisma } from "~/clients";
import Graph from "~/clients/facebook/Graph";

const getFacebookPosts = async ({
  integration,
  since,
}: {
  integration: {
    id: string;
    page: {
      id: string;
      name: string;
      externalID: string;
    };
    accessToken: string;
  };
  since: number;
}) => {
  try {
    const graph = new Graph(integration.accessToken);

    const postResponse = await graph.FB.getPagePosts(
      integration.page.externalID,
      {
        since,
      }
    );

    if (postResponse.errors) {
      try {
        console.error(
          `Error getting posts ${
            integration.page.name
          }: ${postResponse.errors.join(",")}`
        );
        console.error(
          `Error getting posts ${integration.page.name}: ${JSON.stringify(
            postResponse.errors
          )}`
        );
      } catch (error) {
        console.error(error);
      }
      return 0;
    }

    await prisma.facebookPage.update({
      where: {
        id: integration.page.id,
      },
      data: {
        posts: {
          upsert: postResponse.data.map((post) => ({
            where: {
              id: post.id,
            },
            create: {
              id: post.id,
              caption: post.caption,
              permalink: post.permalinkUrl,
              statusType: post.statusType,
              imageUrls: post.imageUrls,
              videoUrls: post.videoUrls,
              messageTags: post.messageTags,
              messageTagsCount: post.messageTagsCount,
              hasEvent: post.hasEvent,
              isPopular: post.isPopular,
              isHidden: post.isHidden,
              isPublished: post.isPublished,
              isSpherical: post.isSpherical,
              impressionCount: post.impressionCount,
              shareCount: post.shareCount,
              reactionCount: post.reactionCount,
              commentCount: post.commentCount,
              publishedAt: post.publishedAt,
              rawResponse: post.rawResponse,
            },
            update: {
              caption: post.caption,
              permalink: post.permalinkUrl,
              statusType: post.statusType,
              imageUrls: post.imageUrls,
              videoUrls: post.videoUrls,
              messageTags: post.messageTags,
              messageTagsCount: post.messageTagsCount,
              impressionCount: post.impressionCount,
              commentCount: post.commentCount,
              shareCount: post.shareCount,
              rawResponse: post.rawResponse,
            },
          })),
        },
      },
    });
    const posts = await prisma.post.findMany({
      where: {
        OR: [
          {
            link: {
              in: postResponse.data.map((post) => post.permalinkUrl),
            },
          },
          {
            facebookLink: {
              in: postResponse.data.map((post) => post.permalinkUrl),
            },
          },
        ],
      },
    });

    const promises = posts.map(async (post) => {
      const matchingPost = postResponse.data.find(
        (fbPost) =>
          fbPost.permalinkUrl === post.link ||
          fbPost.permalinkUrl === post.facebookLink
      );

      if (matchingPost) {
        await prisma.post.update({
          where: { id: post.id },
          data: {
            facebookPost: {
              connect: { id: matchingPost.id },
            },
          },
        });
      }
    });

    await Promise.all(promises);

    return postResponse.data.length;
  } catch (error) {
    return 0;
  }
};

export default getFacebookPosts;
