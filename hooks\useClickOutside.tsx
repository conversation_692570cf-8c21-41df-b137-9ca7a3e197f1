import React, { ReactNode, useEffect, useRef } from "react";

/**
 * Hook that alerts clicks outside of the passed ref
 */
const useClickOutside = (
  ref: React.MutableRefObject<null>,
  onClickOutside: () => void
) => {
  useEffect(() => {
    /**
     * Alert if clicked on outside of element
     */
    const handleClickOutside = (event: MouseEvent) => {
      // @ts-ignore
      if (ref.current && !ref.current.contains(event.target)) {
        onClickOutside();
      }
    };
    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [ref]);
};

/**
 * Component that alerts if you click outside of it
 */
const OnClickOutisde = ({
  children,
  onClickOutside,
}: {
  children: ReactNode;
  onClickOutside: () => void;
}) => {
  const wrapperRef = useRef(null);
  useClickOutside(wrapperRef, onClickOutside);

  return <div ref={wrapperRef}>{children}</div>;
};

export default OnClickOutisde;
