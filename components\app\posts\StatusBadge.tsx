import { PostStatus } from "@prisma/client";
import { Badge } from "~/components";
import { TooltipSide } from "~/components/Tooltip";
import { capitalizeWords } from "~/utils";

type Props = {
  size?: "sm" | "md";
  status: PostStatus | "Idea";
  kind: "badge" | "label" | "icon";
  showHoverState?: boolean;
  tooltipText?: string;
  tooltipSide?: TooltipSide;
};

const StatusBadge = ({
  size = "md",
  status,
  kind,
  showHoverState,
  tooltipText,
  tooltipSide,
}: Props) => {
  const statusColor = (
    <img src={`/status_symbols/${status}.png`} className="h-[9px] w-[9px]" />
  );

  if (kind === "badge") {
    return (
      <div className="-translate-y-0.5">
        <Badge
          intent="none"
          border={size === "md"}
          size={size}
          kind="solid"
          isUppercase={true}
          tooltipText={tooltipText}
          tooltipSide={tooltipSide}
          showHoverState={status != "Idea" && showHoverState}
          icon={statusColor}
          label={capitalizeWords(status)}
        />
      </div>
    );
  } else if (kind === "label") {
    return (
      <div className="flex items-center gap-1">
        <img src={`/status_symbols/${status}.png`} className="h-3 w-3" />
        <div className="font-eyebrow-sm font-normal whitespace-nowrap">
          {capitalizeWords(status)}
        </div>
      </div>
    );
  } else {
    return (
      <img
        src={`/status_symbols/${status}.png`}
        className="h-[9px] w-[9px] shrink-0"
      />
    );
  }
};

export default StatusBadge;
