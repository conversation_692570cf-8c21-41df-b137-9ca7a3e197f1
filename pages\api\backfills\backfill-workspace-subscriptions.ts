import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const basicWorkspaceSubscriptions = await db.workspaceSubscription.updateMany(
    {
      where: {
        tier: "Basic",
      },
      data: {
        isTwitterAnalyticsEnabled: false,
      },
    }
  );

  const standardWorkspaceSubscriptions =
    await db.workspaceSubscription.updateMany({
      where: {
        tier: "Standard",
      },
      data: {
        isTwitterAnalyticsEnabled: true,
      },
    });

  res.status(StatusCodes.OK).send({
    basicWorkspaceSubscriptions: basicWorkspaceSubscriptions.count,
    standardWorkspaceSubscriptions: standardWorkspaceSubscriptions.count,
  });
};

export default handler;
