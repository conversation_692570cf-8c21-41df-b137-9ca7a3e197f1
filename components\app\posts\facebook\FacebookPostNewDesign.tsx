import { Channel, FacebookContentType } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import { arrayMoveImmutable } from "array-move";
import assert from "assert";
import posthog from "posthog-js";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import Select from "~/components/Select";
import TextLink from "~/components/TextLink";
import { MAX_VIDEO_UPLOAD_SIZE_MB, ONE_YEAR_IN_SECONDS } from "~/constants";
import facebookMentionRegex from "~/constants/facebookMentionRegex";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import tusOptions from "~/constants/tusOptions";
import {
  usePostIntegrations,
  useThreadComments,
  useWorkspace,
} from "~/providers";
import { AssetType } from "~/types/Asset";
import {
  charCount,
  getImageProperties,
  getVideoProperties,
  handleFilesUpload,
  handleTrpcError,
  openConfirmationModal,
  openIntercomChat,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import compressImage from "~/utils/compressImage";
import getAssetsWithProperties from "~/utils/getAssetsWithProperties";
import getFileProperties from "~/utils/getFileProperties";
import { UploadingAsset } from "../../files/AssetsNewDesign";
import CoverPhotoModal from "../CoverPhotoModal";
import EditorActionsBar from "../EditorActionsBar";
import PostPerformance from "../PostPerformance";
import { ScheduledPost } from "../SchedulePostAccountSelect";

import classNames from "classnames";
import { FacebookIntegration } from "~/providers/PostIntegrationsProvider";
import createRoomID from "~/utils/liveblocks/createRoomID";
import FacebookEditorWrapperNewDesign from "../../tiptap/FacebookEditorNewDesign";
import PostEngagementsNewDesign from "../PostEngagementsNewDesign";
import {
  PostOptionNewDesign,
  PostOptionsNewDesign,
} from "../PostOptionsNewDesign";
import FacebookAssetPreviewNewDesign from "./FacebookAssetPreviewNewDesign";
import FacebookReelsPreviewNewDesign from "./FacebookReelsPreviewNewDesign";
import FacebookSharedPreview from "./FacebookSharedPreview";
export type FacebookContent = {
  id: string;
  editorState: JSONContent | null;
  type: FacebookContentType;
  copy: string;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    scheduledPosts: ScheduledPost[];
    facebookAssets: {
      assets: AssetType[];
      coverPhoto: AssetType | null;
    };
    facebookContent: FacebookContent | null;
    facebookIntegration: FacebookIntegration | null;
    facebookLink: string | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const FacebookPostNewDesign = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const trpcUtils = trpc.useUtils();
  const [isRoomReady, setIsRoomReady] = useState<boolean>(false);

  useEffect(() => {
    if (post.facebookContent && !isSyncingComplete) {
      setIsRoomReady(true);
    }
  }, [post.facebookContent, isSyncingComplete]);

  const [contentAssets, setContentAssets] = useState<AssetType[]>(
    post.facebookAssets.assets
  );
  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);
  const [coverPhoto, setCoverPhoto] = useState<AssetType | null>(
    post.facebookAssets.coverPhoto
  );
  const [coverPhotoModalOpen, setCoverPhotoModalOpen] =
    useState<boolean>(false);

  const { currentWorkspace } = useWorkspace();

  const [draggedOver, setDraggedOver] = useState<boolean>(false);

  const [content, setContent] = useState<FacebookContent>(
    post.facebookContent || {
      id: uuidv4(),
      editorState: null,
      copy: "",
      type: "Feed",
    }
  );
  const [integration, setIntegration] = useState<FacebookIntegration | null>(
    post.facebookIntegration || null
  );

  const {
    connectedIntegrations,
    selectedIntegrations,
    currentChannel,
    getChannelIntegration,
  } = usePostIntegrations();
  const connectedFacebookIntegrations = connectedIntegrations.Facebook || [];

  const engagementsQuery = trpc.facebookContent.getEngagements.useQuery({
    postID: post.id,
  });
  const engagements = engagementsQuery.data?.engagements || [];
  const createEngagementMutation =
    trpc.facebookContent.createEngagement.useMutation();
  const updateEngagementMutation =
    trpc.facebookContent.updateEngagement.useMutation();
  const deleteEngagementMutation =
    trpc.facebookContent.deleteEngagement.useMutation();
  const updatePostMutation = trpc.post.update.useMutation();

  useEffect(() => {
    if (selectedIntegrations.Facebook && connectedFacebookIntegrations.length) {
      const selectedIntegration = connectedFacebookIntegrations.find(
        (integration) => integration.id === selectedIntegrations.Facebook
      );

      if (
        selectedIntegration &&
        (!integration || integration.id !== selectedIntegration.id)
      ) {
        const facebookIntegration = getChannelIntegration(
          "Facebook",
          selectedIntegrations.Facebook
        );
        if (facebookIntegration) {
          setIntegration(facebookIntegration);
          updatePostMutation.mutate(
            {
              id: post.id,
              facebookIntegrationID: facebookIntegration.id,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getIntegrations.refetch({
                  id: post.id,
                });
              },
            }
          );
        }
      }
    }
  }, [selectedIntegrations, connectedFacebookIntegrations]);

  const isVideo = contentAssets[0]?.metadata.mimetype.startsWith("video");

  const facebookContentQuery = trpc.facebookContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const facebookContentData = facebookContentQuery.data;
  useEffect(() => {
    const setCrossPostData = async () => {
      if (facebookContentData?.facebookContent != null) {
        setContent(facebookContentData.facebookContent);
        setContentAssets(facebookContentData.assets);
        setCoverPhoto(facebookContentData.coverPhoto);
        await maybeCompressAssets(facebookContentData.assets);
        setIsRoomReady(true);
        clearIsSyncing();
      }
    };
    setCrossPostData();
  }, [facebookContentData]);

  const upsertFacebookMutation = trpc.facebookContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
  });
  const compressVideoMutation = trpc.video.compress.useMutation();
  const createAssetsMutation = trpc.facebookContent.createAssets.useMutation();
  const deleteAssetMutation = trpc.facebookContent.deleteAsset.useMutation();
  const updateAssetPositionMutation =
    trpc.facebookContent.updateAssetPositions.useMutation();
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const upsertCoverPhotoMutation =
    trpc.facebookContent.upsertCoverPhoto.useMutation();
  const deleteCoverPhotoMutation =
    trpc.facebookContent.deleteCoverPhoto.useMutation();
  const updateAssetMutation = trpc.facebookContent.updateAsset.useMutation();

  const maybeCompressAssets = async (assets: AssetType[]) => {
    const videoToCompress = assets.find(
      (asset) => asset.sizeMB > 125 && asset.metadata.mimetype.includes("video")
    );
    if (videoToCompress != null) {
      const { width, height } = await getVideoProperties(
        videoToCompress.signedUrl
      );
      compressVideoMutation.mutate(
        {
          video: {
            path: videoToCompress.path,
            resolution: {
              width,
              height,
            },
          },
          channel: "Facebook",
        },
        {
          onError: (error) => {
            handleTrpcError({ title: "Error Compressing Video", error });
          },
          onSuccess: (data) => {
            if (data.jobID != null) {
              toast.info("Optimizing Attachment", {
                description:
                  "We're automatically optimizing your attachment (files >125MB are not accepted on Facebook).",
              });
            }
          },
        }
      );
    }
  };

  const onSortEnd = (oldIndex: number, newIndex: number) => {
    setContentAssets((assets) => {
      const newAssets = arrayMoveImmutable(assets, oldIndex, newIndex);

      updateAssetPositionMutation.mutate({
        assets: newAssets.map((asset, index) => ({
          id: asset.id,
          position: index,
        })),
      });

      return newAssets;
    });
  };

  const integrationOptions = isReadOnly
    ? engagements.map((engagement) => ({
        value: engagement.integrationID,
        label: engagement.integration.page.name,
        profilePicture: engagement.integration.page.profileImageUrl,
      }))
    : connectedFacebookIntegrations.map((integration) => ({
        value: integration.id,
        label: integration.label,
        profilePicture: integration.account.profileImageUrl,
      }));

  const numSetOptions = [isVideo && coverPhoto != null].filter(
    (option) => option
  ).length;

  const getValidFiles = async (files: File[]) => {
    const validFiles: File[] = [];

    if (files.length === 0) {
      return [];
    }

    const currentAssetIsVideo = contentAssets.some((asset) =>
      asset.metadata.mimetype.startsWith("video/")
    );
    const currentAssetIsImage = contentAssets.some((asset) =>
      asset.metadata.mimetype.startsWith("image/")
    );

    if (currentAssetIsVideo) {
      toast.error("Too Many Attachments", {
        description: "You can add up to a total of 10 images or 1 video",
      });

      return [];
    }

    const initialFileType = files[0].type;
    if (currentAssetIsImage && initialFileType.startsWith("video/")) {
      toast.error("Too Many Attachments", {
        description: "You can add up to a total of 10 images or 1 video",
      });

      return [];
    }

    const isImage = initialFileType.startsWith("image/");
    const maxFiles = isImage ? 10 - contentAssets.length : 1;
    const currentFiles = files
      .filter((file) => {
        const type = file.type;
        if (isImage) {
          return type.startsWith("image/") && type !== "image/gif";
        } else {
          return true;
        }
      })
      .slice(0, maxFiles);

    if (currentFiles.length !== files.length) {
      toast.error("Too Many Attachments", {
        description: "You can add a maximum of 10 photos and videos to a post.",
      });
    }

    const assetNames = contentAssets.map((asset) => asset.name);

    for await (const file of currentFiles) {
      const fileType = file.type;
      const fileSizeMB = file.size / 1_000 / 1_000;

      if (assetNames.includes(file.name)) {
        toast.error(`Duplicate Attachment: "${file.name}"`, {
          description: "Each attachment must have a unique name",
        });
        continue;
      }

      // https://developers.facebook.com/docs/video-api/guides/reels-publishing#requirements
      if (content.type === "Reel") {
        if (fileType !== "video/mp4" && fileType !== "video/quicktime") {
          toast.error("Facebook requires videos to be .mp4 or .mov");
          continue;
        } else if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
          toast.error("Video is too large", {
            description:
              "Max file size is ${MB. Contact support for help compressing your video.",
            action: {
              label: "Contact Support",
              onClick: () => {
                openIntercomChat(
                  `I need help compressing a ${fileSizeMB.toFixed(1)} MB video.`
                );
              },
            },
          });
          continue;
        }

        const { duration, height, width } = await getVideoProperties(file);

        if (duration > 90) {
          toast.error("Video is too long", {
            description:
              "Facebook requires videos to be shorter than 90 seconds",
          });
          continue;
        } else if (Math.abs(width / height - 9 / 16) > 0.01) {
          toast.error("Video is not the correct aspect ratio", {
            description:
              "Facebook requires videos to be in a 9:16 aspect ratio",
          });
          continue;
        } else if (width < 540) {
          toast.error("Video width is too narrow", {
            description:
              "Facebook requires videos to have a minimum width of 540px and height of 960px",
          });
          continue;
        } else if (height < 960) {
          toast.error("Video height is too short", {
            description:
              "Facebook requires videos to have a minimum width of 540px and height of 960px",
          });
          continue;
        }

        validFiles.push(file);
      } else if (content.type === "Feed") {
        if (["image/jpeg", "image/png"].includes(fileType)) {
          const { pixels } = await getImageProperties(file);

          // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/multiimage-post-api?view=li-lms-2023-02&tabs=http#multiimage
          if (pixels >= 36_152_320) {
            toast.error("Image is too large", {
              description: "An image can have a maximum of 36,152,319 pixels",
            });
            continue;
          }

          // https://developers.facebook.com/docs/graph-api/reference/page/photos/#photo-specifications
          if (fileSizeMB > 4) {
            const compressedFile = await compressImage(file, {
              maxSizeMB: 4,
            });
            toast.info("Attachment Optimized", {
              description: `${file.name} was optimized (files >4MB are not accepted on Facebook)`,
            });
            posthog.capture(POSTHOG_EVENTS.image.compressed, {
              originalSizeMB: file.size / 1_024 / 1_024,
              compressedSizeMB: compressedFile.size / 1_024 / 1_024,
              channel: "Facebook",
              channelPostType: content.type,
            });
            validFiles.push(compressedFile);
          } else {
            if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
              toast.error("Video is too large", {
                description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
                action: {
                  label: "Contact Support",
                  onClick: () => {
                    openIntercomChat(
                      `I need help compressing a ${fileSizeMB.toFixed(
                        1
                      )} MB video.`
                    );
                  },
                },
              });
              continue;
            }

            validFiles.push(file);
          }
        } else {
          if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
            toast.error("Video is too large", {
              description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
              action: {
                label: "Contact Support",
                onClick: () => {
                  openIntercomChat(
                    `I need help compressing a ${fileSizeMB.toFixed(
                      1
                    )} MB video.`
                  );
                },
              },
            });
            continue;
          }

          validFiles.push(file);
        }
      }
    }

    return validFiles;
  };

  const [uppy, setUppy] = useState<Uppy | null>(null);

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `facebook-${post.id}`,
        allowMultipleUploads: true,
        restrictions: {
          maxNumberOfFiles: content.type === "Feed" ? 10 : 1,
        },
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", async (file) => {
        assert(currentWorkspace);

        const contentID = file.meta.contentID;
        assert(contentID);

        assert(content);

        setUploadingAssets((uploadingAssets) => {
          return [
            ...uploadingAssets,
            {
              id: file.id,
              name: file.name!,
              size: file.size!,
              type: file.type,
              uploadProgress: 0,
              updateAssetID: file.meta.updateAssetID as string | undefined,
              width: 0,
              height: 0,
            },
          ];
        });

        const objectName = `${currentWorkspace.id}/${post.id}/facebook/${contentID}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-start", async (data) => {
        for (const file of data) {
          if (
            file.type.startsWith("image/") ||
            file.type.startsWith("video/")
          ) {
            const assetProperties = file.type.startsWith("video/")
              ? await getVideoProperties(file.data)
              : await getImageProperties(file.data);

            setUploadingAssets((uploadingAssets) => {
              const uploadingAsset = uploadingAssets.find(
                (asset) => asset.name === file.name
              );
              if (uploadingAsset) {
                return uploadingAssets.map((asset) =>
                  asset.name === file.name
                    ? {
                        ...asset,
                        width: assetProperties.width,
                        height: assetProperties.height,
                      }
                    : asset
                );
              }
              return uploadingAssets;
            });
          }
        }
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (uppyData) => {
        const path = `${currentWorkspace!.id}/${post.id}/facebook/${
          content.id
        }`;
        const fileNames = uppyData.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          uppyData.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              if (uppyData.successful![0].meta.updateAssetID) {
                const updateAssetID = uppyData.successful![0].meta
                  .updateAssetID as string;
                const newAsset = newAssetsWithProperties[0];

                await updateAssetMutation.mutateAsync({
                  facebookAssetID: updateAssetID,
                  newAsset: {
                    id: newAsset.id,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                });

                setContentAssets((prevAssets) =>
                  prevAssets.map((asset) =>
                    asset.id === updateAssetID
                      ? {
                          id: asset.id,
                          name: newAsset.name,
                          path: newAsset.path,
                          sizeBytes: newAsset.sizeBytes,
                          eTag: newAsset.eTag,
                          md5Hash: newAsset.md5Hash,
                          mimetype: newAsset.mimetype,
                          width: newAsset.width,
                          height: newAsset.height,
                          metadata: newAsset.metadata,
                          url: newAsset.signedUrl,
                          sizeMB: newAsset.sizeMB,
                          sizeKB: newAsset.sizeKB,
                          createdAt: newAsset.createdAt,
                          updatedAt: newAsset.updatedAt,
                          duration: newAsset.duration,
                          signedUrl: newAsset.signedUrl,
                          urlExpiresAt: newAsset.urlExpiresAt,
                        }
                      : asset
                  )
                );
                setUploadingAssets([]);
              } else {
                const nextPosition = contentAssets.length;

                createAssetsMutation.mutate(
                  {
                    id: content.id,
                    assets: newAssetsWithProperties.map((asset, index) => ({
                      id: asset.id,
                      assetID: asset.assetID,
                      name: asset.name,
                      path: asset.path,
                      size: asset.sizeBytes,
                      eTag: asset.eTag,
                      md5Hash: asset.md5Hash,
                      mimetype: asset.mimetype,
                      width: asset.width,
                      height: asset.height,
                      duration: asset.duration,
                      url: asset.signedUrl,
                      urlExpiresAt: asset.urlExpiresAt,
                      position: nextPosition + index,
                    })),
                  },
                  {
                    onSuccess: async () => {
                      await maybeCompressAssets(newAssetsWithProperties);
                    },
                    onError: (error) => {
                      handleTrpcError({ title: "Error Creating Asset", error });
                    },
                  }
                );

                setUploadingAssets([]);
                setContentAssets((assets) => [
                  ...assets,
                  ...newAssetsWithProperties.map((asset) => ({
                    ...asset,
                    tags: [],
                  })),
                ]);
              }
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace, content.id, content.type]);

  const updateContentType = (type: FacebookContentType) => {
    setContent((prevContent) => {
      upsertFacebookMutation.mutate({
        id: prevContent.id,
        editorState: prevContent.editorState,
        copy: prevContent.copy,
        type,
        postID: post.id,
      });

      return {
        ...prevContent,
        type,
      };
    });
  };

  const { setRoomIDs } = useThreadComments();

  const currentRoomID = createRoomID({
    workspaceID: post.workspaceID,
    contentType: "post",
    postID: post.id,
    roomContentID: content.id,
  });

  useEffect(() => {
    if (content.id && currentChannel === "Facebook") {
      setRoomIDs([currentRoomID]);
    }
  }, [content.id, currentRoomID, setRoomIDs, currentChannel]);

  const editorComponent = isRoomReady && (
    <FacebookEditorWrapperNewDesign
      key={content.id}
      postID={post.id}
      isReadOnly={isReadOnly}
      initialValue={content.editorState}
      placeholder="Add copy for the post here"
      onImagePaste={(event) =>
        handleFilesUpload({
          items: event.clipboardData.items,
          uppy: uppy!,
          meta: {
            contentID: content.id,
          },
          getValidFiles,
        })
      }
      onSave={(editorState) => {
        setContent((prevContent) => {
          upsertFacebookMutation.mutate({
            id: prevContent.id,
            copy: prevContent.copy,
            type: prevContent.type,
            editorState: editorState,
            postID: post.id,
          });

          return {
            ...prevContent,
            editorState,
          };
        });
      }}
      onUpdateContent={(copy) => {
        setContent((prevContent) => ({
          ...prevContent,
          copy,
        }));
      }}
      roomID={createRoomID({
        workspaceID: post.workspaceID,
        contentType: "post",
        postID: post.id,
        roomContentID: content.id,
      })}
    />
  );

  return (
    <div
      // This avoids triggering the dropzone when dragging internal post content like existing images
      onDragOver={(event) => {
        if (
          !coverPhotoModalOpen &&
          event.dataTransfer.types.includes("Files") &&
          !event.dataTransfer.types.includes("text/html")
        ) {
          event.preventDefault();
          setDraggedOver(true);
        }
      }}
      onDragLeave={(event) => {
        const currentTarget = event.currentTarget;
        const relatedTarget = event.relatedTarget as Node | null;

        if (!relatedTarget || !currentTarget.contains(relatedTarget)) {
          setDraggedOver(false);
        }
      }}
      onDrop={() => {
        setDraggedOver(false);
      }}
    >
      <PostPerformance channel="Facebook" url={post.facebookLink} />
      <Select
        options={[
          {
            value: "Feed",
            label: "Feed",
          },
          {
            value: "Reel",
            label: "Reel",
          },
        ]}
        disabled={isReadOnly}
        value={content.type}
        onChange={async (value) => {
          const newType = value as FacebookContentType;
          if (newType === "Reel") {
            if (contentAssets.length > 1) {
              return openConfirmationModal({
                title: "Too many attachments",
                body: "To convert this post from a Feed post to Reels, please ensure the post only has one attachment, in the form of a .mp4 or .mov file.",
                cancelButton: null,
                primaryButton: {
                  label: "Got It",
                },
              });
            } else if (contentAssets.length === 1) {
              if (!contentAssets[0].metadata.mimetype.includes("video")) {
                return openConfirmationModal({
                  title: "No videos associated with post",
                  body: `To convert this post from a ${content.type} post to Reels, please ensure the post only has one attachment, in the form of a .mp4 or .mov file.`,
                  cancelButton: null,
                  primaryButton: {
                    label: "Got It",
                  },
                });
              }
              const asset = contentAssets[0];

              const { duration, height, width } = await getVideoProperties(
                asset.signedUrl
              );
              if (duration > 90) {
                return openConfirmationModal({
                  title: "Video is too long",
                  body: "To convert this post from a Feed post to Reels, please ensure the video is shorter than 90 seconds.",
                  cancelButton: null,
                  primaryButton: {
                    label: "Got It",
                  },
                });
              } else if (Math.abs(width / height - 9 / 16) > 0.01) {
                return openConfirmationModal({
                  title: "Video is not the correct aspect ratio",
                  body: "To convert this post from a Feed post to Reels, please ensure the video is in a 9:16 aspect ration.",
                  cancelButton: null,
                  primaryButton: {
                    label: "Got It",
                  },
                });
              } else if (width < 540) {
                return openConfirmationModal({
                  title: "Video width is too narrow",
                  body: "To convert this post from a Feed post to Reels, please ensure the video has a minimum width of 540px and height of 960px.",
                  cancelButton: null,
                  primaryButton: {
                    label: "Got It",
                  },
                });
              } else if (height < 960) {
                return openConfirmationModal({
                  title: "Video height is too short",
                  body: "To convert this post from a Feed post to Reels, please ensure the video has a minimum width of 540px and height of 960px.",
                  cancelButton: null,
                  primaryButton: {
                    label: "Got It",
                  },
                });
              }
            }
          } else if (newType === "Feed") {
            const asset = contentAssets[0];
            const isVideo = asset?.metadata.mimetype.includes("video");
            const isImage = asset?.metadata.mimetype.includes("image");
            if (isVideo) {
              if (asset.sizeMB > 125) {
                return openConfirmationModal({
                  title: "Video is too large",
                  body: "To convert this post from a Reel to a Feed post, please ensure the video is smaller than 125MB.",
                  cancelButton: null,
                  primaryButton: {
                    label: "Got It",
                  },
                });
              }
            } else if (isImage) {
              // Nothing to check here
            }
          }
          updateContentType(newType);
        }}
      />
      <div className="mt-5 min-h-[289px]">
        <FacebookSharedPreview
          type={content.type}
          integration={integration}
          currentWorkspace={currentWorkspace || null}
          editor={editorComponent}
          assetPreview={
            content.type === "Feed" ? (
              <FacebookAssetPreviewNewDesign
                contentID={content.id}
                assets={contentAssets}
                setAssets={setContentAssets}
                uploadingAssets={uploadingAssets}
                uppy={uppy}
                getValidFiles={getValidFiles}
                setDraggedOver={setDraggedOver}
                coverPhotoUrl={coverPhoto?.signedUrl}
                isReadOnly={isReadOnly}
                draggedOver={draggedOver}
              />
            ) : (
              <FacebookReelsPreviewNewDesign
                content={content}
                assets={contentAssets}
                connectedIntegration={integration}
                coverPhotoUrl={coverPhoto?.signedUrl}
                isReadOnly={isReadOnly}
                uppy={uppy}
                uploadingAssets={uploadingAssets}
                getValidFiles={getValidFiles}
                onDrop={() => setDraggedOver(false)}
                onDeleteClick={(asset) => {
                  setContentAssets((prevContentAssets) =>
                    prevContentAssets.filter(
                      (contentAsset) => contentAsset.id !== asset.id
                    )
                  );
                  deleteAssetMutation.mutate(
                    {
                      facebookAssetID: asset.id,
                    },
                    {
                      onError: (error) => {
                        handleTrpcError({
                          title: "Error Deleting Asset",
                          error,
                        });
                      },
                    }
                  );
                }}
                isDraggedOver={draggedOver}
              />
            )
          }
        />
        <div
          className={classNames({
            "mt-2":
              (contentAssets.length > 0 || uploadingAssets.length > 0) &&
              content.type === "Feed",
          })}
        >
          <EditorActionsBar
            post={post}
            channel="Facebook"
            visible={true}
            isReadOnly={isReadOnly}
            characterCount={{
              current: charCount(content.copy, {
                regexTransformers: [
                  {
                    from: facebookMentionRegex,
                  },
                ],
              }),
              max: 2200,
            }}
            uploadAssetButton={{
              acceptedMimetypes:
                content.type === "Feed"
                  ? ["image/jpeg", "image/png", "video/mp4", "video/quicktime"]
                  : ["video/mp4", "video/quicktime"],
              acceptMultiple: true,
              onUploadFiles: async (files) => {
                if (uppy) {
                  handleFilesUpload({
                    files,
                    uppy,
                    meta: {
                      contentID: content.id,
                    },
                    getValidFiles,
                  });
                }
              },
              isLoading: uploadingAssets.length > 0,
            }}
          />
        </div>
      </div>
      {(isVideo || content.type === "Reel") && (
        <PostOptionsNewDesign>
          <PostOptionNewDesign
            title="Customize Cover Photo"
            disabled={!isVideo && content.type !== "Reel"}
          >
            <CoverPhotoModal
              contentID={content.id}
              video={contentAssets[0]}
              getValidFiles={async (files: File[]) => {
                if (files.length > 1) {
                  toast.error("Too many files", {
                    description: "Please only upload one image.",
                  });
                  return [];
                } else {
                  const file = files[0];

                  if (file.type !== "image/jpeg" && file.type !== "image/png") {
                    toast.error("Invalid file type", {
                      description: "Please upload a JPG or PNG file.",
                    });
                    return [];
                  }

                  const fileSizeMB = file.size / 1000 / 1000;
                  if (fileSizeMB > 8) {
                    const compressedFile = await compressImage(file, {
                      maxSizeMB: 8,
                    });
                    toast.info("Attachment Optimized", {
                      description: `${file.name} was optimized (files >8MB are not accepted on Facebook)`,
                    });
                    return [compressedFile];
                  } else {
                    return files;
                  }
                }
              }}
              onUpload={(fileName, onSave) => {
                getNewAssetsMutation.mutate(
                  {
                    path: `${currentWorkspace!.id}/${post.id}/facebook/${
                      content.id
                    }/cover_photo`,
                    fileNames: [fileName],
                  },
                  {
                    onSuccess: async (data) => {
                      const { assets } = data;
                      const newAssetsWithProperties = (
                        await getAssetsWithProperties(assets)
                      ).map((asset) => ({
                        ...asset,
                        id: uuidv4(),
                        assetID: asset.id,
                      }));

                      const coverPhotoAsset = newAssetsWithProperties[0];

                      setCoverPhoto(coverPhotoAsset);

                      upsertCoverPhotoMutation.mutate({
                        id: content.id,
                        asset: {
                          id: coverPhotoAsset.id,
                          name: coverPhotoAsset.name,
                          path: coverPhotoAsset.path,
                          eTag: coverPhotoAsset.eTag,
                          md5Hash: coverPhotoAsset.md5Hash,
                          size: coverPhotoAsset.sizeBytes,
                          mimetype: coverPhotoAsset.mimetype,
                          width: coverPhotoAsset.width,
                          height: coverPhotoAsset.height,
                          url: coverPhotoAsset.signedUrl,
                          urlExpiresAt: coverPhotoAsset.urlExpiresAt,
                        },
                      });

                      onSave();
                    },
                    onError: (error) => {
                      handleTrpcError({
                        title: "Failed to upload cover photo",
                        error,
                      });
                    },
                  }
                );
              }}
              onDelete={(asset) => {
                setCoverPhoto(null);
                deleteCoverPhotoMutation.mutate({
                  facebookContentID: content.id,
                  path: asset.path,
                });
              }}
              storagePath={`${currentWorkspace?.id}/${post.id}/facebook/${content.id}/cover_photo`}
              coverPhoto={coverPhoto}
              open={coverPhotoModalOpen}
              setOpen={setCoverPhotoModalOpen}
            />
          </PostOptionNewDesign>
        </PostOptionsNewDesign>
      )}
      <div className="my-12">
        <PostEngagementsNewDesign
          channel="Facebook"
          engagements={engagements}
          onEngagementCreate={async (type) => {
            const engagementID = uuidv4();

            await trpcUtils.facebookContent.getEngagements.cancel();

            assert(integration);

            createEngagementMutation.mutate({
              engagementID,
              contentID: content.id,
              type,
              integrationID: integration.id,
            });

            // Optimistically update the restponse to the engagementsQuery
            trpcUtils.facebookContent.getEngagements.setData(
              {
                postID: post.id,
              },
              {
                engagements: [
                  ...engagements,
                  {
                    id: engagementID,
                    type,
                    copy: null,
                    editorState: null,
                    integrationID: integration.id,
                    integration: {
                      id: integration.id,
                      page: {
                        name: integration.account.name || "",
                        profileImageUrl: integration.account.profileImageUrl,
                      },
                    },
                    postDelaySeconds: 0,
                    createdAt: new Date(),
                  },
                ],
              }
            );
          }}
          accountChooserDisabled={true}
          onEngagementUpdate={(engagementID, engagement) => {
            updateEngagementMutation.mutate(
              {
                engagementID,
                ...engagement,
              },
              {
                onSuccess: () => {
                  trpcUtils.facebookContent.getEngagements.invalidate();
                },
              }
            );
          }}
          onEngagementDelete={(engagementID) => {
            deleteEngagementMutation.mutate(
              {
                engagementID,
              },
              {
                onSuccess: () => {
                  trpcUtils.facebookContent.getEngagements.invalidate();
                },
              }
            );

            // Optimistically update the restponse to the engagementsQuery to remove this engagement
            trpcUtils.facebookContent.getEngagements.setData(
              {
                postID: post.id,
              },
              {
                engagements: (engagementsQuery.data?.engagements || []).filter(
                  (engagement) => engagement.id !== engagementID
                ),
              }
            );
          }}
          disableReposts={true}
          integrationOptions={integrationOptions}
          infoBanner={
            !(
              integration?.scope.includes("pages_read_user_content") &&
              integration?.scope.includes("pages_manage_engagement")
            )
              ? {
                  title: "Additional Permissions Needed",
                  message: (
                    <>
                      We need some additional permissions from Facebook in order
                      to post comments.{" "}
                      <TextLink
                        value="Click here"
                        href="/settings/profiles"
                        size="sm"
                        openInNewTab={false}
                      />{" "}
                      to reintegrate.
                    </>
                  ),
                }
              : undefined
          }
          Editor={({
            initialValue,
            onSave,
            onUpdateContent,
            placeholder,
            engagementID,
          }) => (
            <FacebookEditorWrapperNewDesign
              postID={post.id}
              initialValue={initialValue}
              placeholder={placeholder}
              onSave={onSave}
              onUpdateContent={onUpdateContent}
              isReadOnly={isReadOnly}
              size="sm"
              roomID={null}
            />
          )}
          isReadOnly={isReadOnly}
        />
      </div>
    </div>
  );
};

export default FacebookPostNewDesign;
