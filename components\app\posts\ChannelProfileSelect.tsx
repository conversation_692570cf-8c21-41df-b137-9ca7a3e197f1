import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { useElementSize } from "@mantine/hooks";
import { Channel } from "@prisma/client";
import classNames from "classnames";
import { useRouter } from "next/router";
import { useState } from "react";
import { Combobox, Loader } from "~/components";
import { CHANNELS_WITH_ACCOUNT } from "~/constants";
import { CHANNEL_ICON_MAP } from "~/types";
import ChannelIcon from "../ChannelIcon";

type ChannelProfileSelectProps = {
  channel: Channel;
  isSelected: boolean;
  onSelect: () => void;
  integrationOptions: {
    key: string;
    value: string;
    label: React.ReactNode;
    labelText: string;
  }[];
  selectedIntegration: string | null;
  onSelectIntegration: (integrationID: string) => void;
  isLoading?: boolean;
  isOverflowed?: boolean;
};

const ChannelProfileSelect = ({
  channel,
  isSelected,
  onSelect,
  integrationOptions,
  selectedIntegration,
  onSelectIntegration,
  isLoading,
  isOverflowed,
}: ChannelProfileSelectProps) => {
  const { ref: buttonRef, width: buttonWidth } = useElementSize();
  const router = useRouter();
  const [isComboboxOpen, setIsComboboxOpen] = useState<boolean>(false);

  const selectedAccountName =
    integrationOptions.find((option) => option.key === selectedIntegration)
      ?.labelText || CHANNEL_ICON_MAP[channel].label;

  const isChannelWithAccount = CHANNELS_WITH_ACCOUNT[channel];

  const handleCardClick = () => {
    if (isSelected && !isLoading) {
      setIsComboboxOpen(!isComboboxOpen);
      return;
    }
    onSelect();
  };

  return (
    <button
      ref={buttonRef}
      className={classNames(
        "group hover:bg-lightest-gray-50 relative flex max-h-9 shrink-0 items-center gap-1 rounded-[3px] px-3 py-2 transition-colors",
        {
          "bg-lightest-gray-50": isSelected,
          "max-w-[143px]": !isOverflowed || isSelected,
          "opacity-50": isLoading,
        }
      )}
      disabled={isLoading}
      onClick={handleCardClick}
    >
      <div className="flex w-full gap-2">
        <ChannelIcon channel={channel} width={18} showLabel={false} />
        {(!isOverflowed || isSelected) && (
          <span
            className={classNames(
              "font-body-sm text-medium-dark-gray truncate transition-colors",
              {
                "text-secondary": isSelected,
                "max-w-[76px]": isSelected && isChannelWithAccount,
                "max-w-[90px] group-hover:max-w-[76px]":
                  !isSelected && isChannelWithAccount,
                "max-w-[90px]": !isSelected && !isChannelWithAccount,
              }
            )}
          >
            {selectedAccountName}
          </span>
        )}
        {isLoading && <Loader color="mediumDarkGray" size="sm" />}
      </div>
      {isChannelWithAccount && !isLoading && (!isOverflowed || isSelected) && (
        <div
          className={classNames("flex", {
            "hidden sm:flex": !isSelected,
          })}
          onClick={(e) => {
            if (!isSelected) {
              e.stopPropagation();
              e.preventDefault();
            }
          }}
        >
          <Combobox
            value={selectedIntegration}
            onChange={onSelectIntegration}
            options={integrationOptions.map((option) => ({
              key: option.key,
              value: option.key,
              label: option.label as JSX.Element,
            }))}
            isOpen={isComboboxOpen}
            onPointerDownOutside={() => setIsComboboxOpen(false)}
            triggerButton={
              <ChevronDownIcon
                className={classNames(
                  "text-medium-dark-gray h-3.5 w-3.5 p-0 transition-opacity group-hover:opacity-100",
                  {
                    "opacity-100": isSelected,
                    "opacity-0": !isSelected,
                  }
                )}
              />
            }
            dropdownPosition="left"
            isSearchable={integrationOptions.length > 1}
            emptyLabel={`No ${channel} profiles connected`}
            actionButton={{
              icon: "PlusIcon",
              label: "Connect another profile",
              onClick: () => router.push("/settings/profiles"),
            }}
            sideOffset={14}
            alignOffset={-buttonWidth + 24}
          />
        </div>
      )}
    </button>
  );
};

export default ChannelProfileSelect;
