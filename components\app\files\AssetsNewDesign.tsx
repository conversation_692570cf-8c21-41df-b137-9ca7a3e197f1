import { PhotoIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { CropPresetOption, OptionGroup } from "@pqina/pintura";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { DateTime } from "luxon";
import { useEffect, useRef, useState } from "react";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import { Accept } from "react-dropzone";
import SortableList, { SortableItem } from "react-easy-sort";
import { Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import colors from "~/styles/colors";
import { AssetType } from "~/types/Asset";
import { getTimezoneName } from "~/utils";
import EditImageModal, {
  EditImageModalProperties,
  ValidationRules,
} from "~/utils/EditImageModal";
import AssetNewDesign from "./AssetNewDesign";
import FileDropzone from "./FileDropzone";

export type UploadingAsset = {
  id: string;
  name: string;
  uploadProgress: number;
  size: number;
  type: string;
  updateAssetID?: string;
  width: number;
  height: number;
};

type Props = {
  assets: AssetType[];
  uploadingAssets?: UploadingAsset[] | null;
  onDeleteClick?: (asset: AssetType) => void;
  onEditClick?: (asset: AssetType) => void;
  refetchAssets: () => void;
  isReadOnly?: boolean;
  showDropzone?: boolean;
  onDrop: (acceptedFiles: File[]) => void;
  acceptedFileTypes?: Accept;
  onSortEnd?: (oldIndex: number, newIndex: number) => void;
  editImageModalProperties?: EditImageModalProperties | null;
  validationRules?: ValidationRules;
  cropSelectPresetOptions?: OptionGroup[] | CropPresetOption[];
  onEditImageModalClose?: () => void;
  onUpload?: (file: File, assetID?: string) => Promise<void>;
  showNewEditButton?: boolean;
  onCloseClick?: () => void;
  onUploadButtonClick?: () => void;
};

const AssetsNewDesign = ({
  assets,
  uploadingAssets = [],
  onDeleteClick,
  onEditClick,
  refetchAssets,
  isReadOnly = false,
  showDropzone = false,
  onDrop,
  acceptedFileTypes,
  onSortEnd,
  editImageModalProperties,
  onEditImageModalClose,
  validationRules,
  cropSelectPresetOptions,
  onUpload,
  showNewEditButton = false,
  onCloseClick,
  onUploadButtonClick,
}: Props) => {
  const [height, setHeight] = useState<number>(100);
  const assetsRef = useRef<HTMLDivElement>(null);

  const isSortable = !!onSortEnd;

  useEffect(() => {
    if (
      assetsRef.current &&
      assets.length > 0 &&
      assetsRef.current.clientHeight > 0
    ) {
      // Set height to the height of the assets container
      setHeight(assetsRef.current.clientHeight || 100);
    }
  }, [assetsRef.current?.clientHeight, assets]);

  return (
    <AnimatePresence>
      {showDropzone ? (
        <motion.div
          key="dropzone"
          className="w-full"
          initial={{ height: assets.length > 0 ? height : 0, opacity: 0 }}
          animate={{
            height,
            opacity: 100,
          }}
          exit={assets.length > 0 ? undefined : { height: 0, opacity: 0 }}
        >
          <FileDropzone
            onDrop={onDrop}
            acceptedFileTypes={acceptedFileTypes || {}}
          />
        </motion.div>
      ) : (
        <motion.div
          key="assets"
          ref={assetsRef}
          initial={{ opacity: 0 }}
          animate={{
            opacity: 100,
          }}
          className={classNames(
            "border-light-gray rounded-[5px] border bg-white p-6",
            {
              hidden: assets.length === 0 && uploadingAssets?.length === 0,
            }
          )}
        >
          <div className="mb-5 flex flex-col gap-2">
            <div className="flex flex-col gap-0.5">
              <div className="flex items-center justify-between">
                <span className="font-body-sm leading-none font-medium">
                  Edit / Reorder Assets
                </span>
                {onCloseClick && (
                  <XMarkIcon
                    className={classNames(
                      "text-medium-dark-gray hover:text-basic h-4 w-4 cursor-pointer transition-colors"
                    )}
                    onClick={onCloseClick}
                  />
                )}
              </div>
              <p className="font-body-sm text-medium-dark-gray">
                Hover to see options. Drag and drop if there are multiple assets
                to reorder.
              </p>
            </div>
            {onUploadButtonClick && (
              <Button
                variant="secondary"
                size="sm"
                onClick={onUploadButtonClick}
              >
                <PhotoIcon className="h-4 w-4" />
                Upload more assets
              </Button>
            )}
          </div>
          {isSortable ? (
            <SortableList
              onSortEnd={onSortEnd}
              className="flex max-h-[518px] flex-wrap gap-2 overflow-y-auto"
              draggedItemClassName="dragged"
            >
              {assets.map((asset) => {
                const isEditing = uploadingAssets?.some(
                  (uploadingAsset) => uploadingAsset.updateAssetID === asset.id
                );
                return (
                  <SortableItem key={asset.id}>
                    <div className="item h-[180px] w-[180px] cursor-grab">
                      <AssetNewDesign
                        asset={asset}
                        onDeleteClick={onDeleteClick}
                        onEditClick={() => onEditClick?.(asset)}
                        refetchAssets={refetchAssets}
                        isReadOnly={isReadOnly}
                        showNewEditButton={showNewEditButton}
                        isEditing={isEditing}
                      />
                    </div>
                  </SortableItem>
                );
              })}
              {(uploadingAssets || [])
                .filter((asset) => !asset.updateAssetID)
                .map((uploadingAsset) => {
                  return (
                    <UploadingAsset
                      key={uploadingAsset.name}
                      uploadingAsset={uploadingAsset}
                    />
                  );
                })}
            </SortableList>
          ) : (
            <div className="grid max-h-[518px] grid-cols-3 gap-4 overflow-y-auto">
              {assets?.map((asset) => {
                const isEditing = uploadingAssets?.some(
                  (uploadingAsset) => uploadingAsset.updateAssetID === asset.id
                );
                return (
                  <AssetNewDesign
                    asset={asset}
                    key={asset.id}
                    onDeleteClick={onDeleteClick}
                    onEditClick={() => onEditClick?.(asset)}
                    refetchAssets={refetchAssets}
                    isReadOnly={isReadOnly}
                    showNewEditButton={showNewEditButton}
                    isEditing={isEditing}
                  />
                );
              })}
              {(uploadingAssets || [])
                .filter((asset) => !asset.updateAssetID)
                .map((uploadingAsset) => {
                  return (
                    <UploadingAsset
                      key={uploadingAsset.name}
                      uploadingAsset={uploadingAsset}
                    />
                  );
                })}
            </div>
          )}
        </motion.div>
      )}
      {editImageModalProperties && (
        <EditImageModal
          title={editImageModalProperties.title}
          asset={editImageModalProperties.asset}
          maxSizeMB={editImageModalProperties.maxSizeMB}
          channel={editImageModalProperties.channel}
          isOpen={editImageModalProperties.isOpen}
          error={editImageModalProperties.error}
          validationRules={validationRules}
          cropSelectPresetOptions={cropSelectPresetOptions}
          onClose={onEditImageModalClose!}
          onUpload={async (file) =>
            onUpload?.(file, editImageModalProperties.asset.id)
          }
        />
      )}
    </AnimatePresence>
  );
};

const UploadingAsset = ({
  uploadingAsset,
}: {
  uploadingAsset?: UploadingAsset | null;
}) => {
  if (!uploadingAsset) {
    return null;
  }

  return (
    <div className="bg-lightest-gray-30 border-medium-gray/30 relative h-[180px] w-[180px] max-w-[180px] rounded-xl border">
      <div className="flex aspect-square w-full max-w-[180px] items-center justify-center">
        <div className="h-8 w-8">
          <CircularProgressbar
            value={uploadingAsset.uploadProgress}
            strokeWidth={5}
            styles={buildStyles({
              pathColor: colors.accentBlue,
            })}
          />
        </div>
      </div>
      <div className="px-3 py-2">
        <Tooltip value={uploadingAsset.name}>
          <div className="font-body-xs overflow-clip text-ellipsis whitespace-nowrap">
            {uploadingAsset.name}
          </div>
        </Tooltip>
        <div className="font-eyebrow-sm text-medium-gray">
          {Math.round((uploadingAsset.size / 1_024 / 1_024) * 10) / 10} MB |{" "}
          {DateTime.now().toFormat("MM/dd/yy h:mm a")} {getTimezoneName()}
        </div>
      </div>
    </div>
  );
};

export default AssetsNewDesign;
