import { BillingPeriod, SubscriptionTier } from "@prisma/client";
import assert from "assert";
import sumBy from "lodash/sumBy";
import Stripe from "stripe";
import Intercom from "~/clients/Intercom";
import { db } from "~/clients/Prisma";
import { inngest } from "~/clients/inngest/inngest";
import posthog from "~/clients/posthog";
import stripe from "~/clients/stripe";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { capitalizeWords } from "~/utils";

const saveCompanySubscription = async (
  subscription: Stripe.Subscription,
  companyID?: string
) => {
  const stripeCustomerID = subscription.customer as string;
  const stripeSubscriptionID = subscription.id;
  const subscriptionItems = subscription.items.data;

  if (companyID == null) {
    const company = await db.company.findFirst({
      where: {
        stripeCustomerID,
      },
    });
    if (!company) {
      console.log(
        `There's no company with this customer ID:`,
        stripeCustomerID
      );
      return;
    }

    companyID = company.id;
  }

  const isTrialing = subscription.status === "trialing";
  const trialEndsAt =
    isTrialing && subscription.trial_end
      ? new Date(subscription.trial_end * 1000)
      : null;

  const billingPeriod = subscriptionItems[0].plan.interval as BillingPeriod;

  const productIDs = subscriptionItems.map(
    (subscriptionItem) => subscriptionItem.price.product as string
  );
  const productsResponse = await await stripe.products.list({
    ids: productIDs,
    limit: 100,
  });
  const products = productsResponse.data;
  const workspaceItem = products.find(
    (product) => product.metadata.tier != null
  );
  assert(workspaceItem);
  const workspaceMetadata = workspaceItem.metadata;
  const tier = workspaceMetadata.tier as SubscriptionTier;

  const itemsWithMetadata = subscriptionItems.map((item) => {
    const product = products.find(
      (product) => product.id === item.price.product
    );

    assert(product);

    return {
      ...item,
      product,
    };
  });

  const getLimit = (metadataKey: string) =>
    sumBy(itemsWithMetadata, (subscriptionItem) =>
      subscriptionItem.product.metadata[metadataKey]
        ? parseInt(subscriptionItem.product.metadata[metadataKey]) *
          (subscriptionItem.quantity || 1)
        : 0
    );

  const seatLimit = getLimit("seatLimit");
  const aiChatLimit = getLimit("aiChatLimit");
  const basicWorkspaceLimit = getLimit("basicWorkspaceLimit");
  const socialProfileLimit = getLimit("socialProfileLimit");
  const engagementProfileLimit = getLimit("engagementProfileLimit");
  const scheduledPostLimit = getLimit("scheduledPostLimit");

  const company = await db.company.update({
    where: {
      id: companyID,
    },
    data: {
      stripeCustomerID,
      trialEndsAt,
    },
    select: {
      id: true,
      name: true,
      createdByID: true,
      workspaces: {
        select: {
          id: true,
          subscription: true,
          referral: true,
        },
      },
      subscription: {
        select: {
          isGrandfathered: true,
          tier: true,
        },
      },
    },
  });

  try {
    const { subscription } = company;
    const numWorkspaces = company.workspaces.length;
    const isAgency = numWorkspaces > 1;

    if (subscription !== null) {
      let plan = subscription.isGrandfathered
        ? `Grandfathered - ${subscription.tier}`
        : subscription.tier;

      const theraCompanyID = "2463d9e8-8744-4404-87c1-9869deb004a8";

      if (isAgency && company.id !== theraCompanyID) {
        plan = "Agency";
      }

      await Intercom.companies.create({
        companyId: companyID,
        name: company.name,
        plan: company.subscription?.isGrandfathered
          ? `Grandfathered - ${company.subscription.tier}`
          : company.subscription?.tier,
      });
    }
  } catch (error) {
    console.error("Failed to update Intercom company profile", error);
  }

  // This is a standard company with a single workspace
  // We can update the workspace subscription to match
  if (company.workspaces.length === 1) {
    const workspace = company.workspaces[0];
    const workspaceSubscription = workspace.subscription;
    await db.workspaceSubscription.upsert({
      where: {
        workspaceID: workspace.id,
      },
      create: {
        workspaceID: workspace.id,
        tier,
        seatLimit,
        aiChatLimit,
        scheduledPostLimit,
        socialProfileLimit,
        engagementProfileLimit,
        isAnalyticsAllTimeEnabled:
          workspaceMetadata.isAnalyticsAllTimeEnabled === "true",
        isAutoEngagementEnabled:
          workspaceMetadata.isAutoEngagementEnabled === "true",
        isBlogEnabled: workspaceMetadata.isBlogEnabled === "true",
        isTwitterAnalyticsEnabled:
          workspaceMetadata.isTwitterAnalyticsEnabled === "true",
      },
      update: {
        tier,
        seatLimit,
        aiChatLimit,
        scheduledPostLimit,
        socialProfileLimit,
        engagementProfileLimit,
        isAnalyticsAllTimeEnabled:
          workspaceSubscription?.isAnalyticsAllTimeEnabled ||
          workspaceMetadata.isAnalyticsAllTimeEnabled === "true",
        isAutoEngagementEnabled:
          workspaceSubscription?.isAutoEngagementEnabled ||
          workspaceMetadata.isAutoEngagementEnabled === "true",
        isBlogEnabled:
          workspaceSubscription?.isBlogEnabled ||
          workspaceMetadata.isBlogEnabled === "true",
        isTwitterAnalyticsEnabled:
          workspaceSubscription?.isTwitterAnalyticsEnabled ||
          workspaceMetadata.isTwitterAnalyticsEnabled === "true",

        // Don't include these fields for now as we don't sell upgrades for them
        // so if someone has them we've given it to them for free

        // isAnalyticsAllTimeEnabled:
        //   workspaceMetadata.isAnalyticsAllTimeEnabled === "true",
        // isAutoEngagementEnabled:
        //   workspaceMetadata.isAutoEngagementEnabled === "true",
        // isBlogEnabled: workspaceMetadata.isBlogEnabled === "true",
        // isTwitterAnalyticsEnabled:
        //   workspaceMetadata.isTwitterAnalyticsEnabled === "true",
      },
    });

    const referral = workspace.referral;
    if (referral?.status === "SignedUp" && subscription.status === "active") {
      await db.referral.update({
        where: {
          id: referral.id,
        },
        data: {
          status: "Subscribed",
        },
      });
      await inngest.send({
        name: "referral.subscribed",
        data: {
          referralID: referral.id,
        },
      });
      posthog.capture({
        distinctId: referral.referredUserID,
        event: POSTHOG_EVENTS.referral.subscribed,
        properties: {
          referralID: referral.id,
          referringUserID: referral.referringUserID,
          referralCreatedAt: referral.createdAt,
        },
        groups: {
          workspace: workspace.id,
          company: company.id,
        },
      });
    }
  }

  const newSubscription = await db.subscription.upsert({
    where: {
      companyID,
    },
    create: {
      companyID,
      billingPeriod,
      tier,
      status: subscription.status,
      stripeSubscriptionID,
      numSeats: seatLimit,
      numAIChats: aiChatLimit,
      numWorkspaces: basicWorkspaceLimit,
      numSocialProfiles: socialProfileLimit,
      numScheduledPosts: scheduledPostLimit,
      engagementProfileLimit,
    },
    update: {
      billingPeriod,
      tier,
      status: subscription.status,
      stripeSubscriptionID,
      numSeats: seatLimit,
      numAIChats: aiChatLimit,
      numWorkspaces: basicWorkspaceLimit,
      numSocialProfiles: socialProfileLimit,
      numScheduledPosts: scheduledPostLimit,
      engagementProfileLimit,
    },
  });

  // This means the company just created a subscription
  if (company.subscription == null) {
    posthog.capture({
      distinctId: company.createdByID,
      event: POSTHOG_EVENTS.subscription.created,
      properties: {
        billingPeriod: capitalizeWords(billingPeriod),
        tier: capitalizeWords(tier),
      },
      groups: {
        company: company.id,
      },
    });
  }

  return newSubscription;
};

export default saveCompanySubscription;
