import { Channel } from "@prisma/client";
import { DateTime } from "luxon";
import { db } from "~/clients";

import assert from "assert";
import { SCAFFOLDING_WORKSPACE_ID } from "~/constants";
import { filterPosts } from "~/utils";
import { copyStorageFiles } from "./copyStorageFiles";
import getStorageAssetsForDiscord from "./discord/getStorageAssetsForDiscord";
import getStorageAssetsForFacebook from "./facebook/getStorageAssetsForFacebook";
import getStorageAssetsForInstagram from "./instagram/getStorageAssetsForInstagram";
import getStorageAssetsForLinkedIn from "./linkedin/getStorageAssetsForLinkedIn";
import getStorageAssetsForSlack from "./slack/getStorageAssetsForSlack";
import getStorageAssetsForTikTok from "./tiktok/getStorageAssetsForTikTok";
import getStorageAssetsForTwitter from "./twitter/getStorageAssetsForTwitter";
import getStorageAssetsForYouTubeShorts from "./youtube/getStorageAssetsForYouTubeShorts";

const createScaffolding = async ({
  userID,
  workspaceID,
}: {
  userID: string;
  workspaceID: string;
}) => {
  const posts = filterPosts(
    await db.post.findMany({
      where: {
        workspaceID: SCAFFOLDING_WORKSPACE_ID,
        status: "ToDo",
        postLabels: {
          some: {
            label: {
              name: {
                equals: "Tips",
                mode: "insensitive",
              },
            },
          },
        },
      },
      include: {
        linkedInContent: {
          include: {
            coverPhoto: true,
            captions: true,
            assets: {
              include: {
                asset: true,
              },
            },
          },
        },
        tweets: {
          include: {
            assets: {
              include: {
                asset: true,
              },
            },
          },
        },
        tikTokContent: {
          include: {
            assets: {
              include: {
                asset: true,
              },
            },
          },
        },
        youTubeShortsContent: {
          include: {
            asset: {
              include: {
                asset: true,
              },
            },
          },
        },
        instagramContent: {
          include: {
            coverPhoto: true,
            assets: {
              include: {
                tags: true,
                asset: true,
              },
            },
          },
        },
        facebookContent: {
          include: {
            coverPhoto: true,
            assets: {
              include: {
                asset: true,
              },
            },
          },
        },
        slackContent: {
          include: {
            assets: {
              include: {
                asset: true,
              },
            },
          },
        },
        discordContent: {
          include: {
            assets: {
              include: {
                asset: true,
              },
            },
          },
        },
        webflowContent: true,
        threadsContent: true,
      },
      orderBy: {
        publishDate: "asc",
      },
    })
  );

  const firstPost = posts[0];
  const firstPostDate = DateTime.fromJSDate(firstPost.publishDate);
  const mostRecentMonday = DateTime.now()
    .startOf("week")
    .plus({ hours: firstPostDate.hour });

  const tipsLabel = await db.label.findFirst({
    where: {
      workspaceID,
      name: {
        equals: "Tips",
        mode: "insensitive",
      },
    },
  });

  const promises = posts.map(async (sourcePost) => {
    const daysDeltaFromFirstPostDate = firstPostDate.diff(
      DateTime.fromJSDate(sourcePost.publishDate),
      "days"
    );

    const {
      tweets,
      linkedInContent,
      tikTokContent,
      youTubeShortsContent,
      instagramContent,
      facebookContent,
      slackContent,
      threadsContent,
      discordContent,
      webflowContent,
    } = sourcePost;

    const post = await db.post.create({
      data: {
        title: sourcePost.title,
        publishDate: mostRecentMonday
          .plus({ days: Math.abs(daysDeltaFromFirstPostDate.days) })
          .toJSDate(),
        publishTime: sourcePost.publishTime,
        status: "ToDo",
        isIdea: false,
        channels: sourcePost.channels,
        workspaceID: workspaceID,
        createdByID: userID,
        notesEditorState: sourcePost.notesEditorState || undefined,
        tweets:
          tweets.length > 0
            ? {
                createMany: {
                  data: tweets.map((tweet) => {
                    return {
                      copy: tweet.copy,
                      editorState: tweet.editorState || undefined,
                      threadPosition: tweet.threadPosition,
                      mediaTaggedUsernames: tweet.mediaTaggedUsernames,
                    };
                  }),
                },
              }
            : undefined,
        facebookContent: facebookContent
          ? {
              create: {
                copy: facebookContent.copy,
                editorState: facebookContent.editorState || undefined,
              },
            }
          : undefined,
        instagramContent: instagramContent
          ? {
              create: {
                copy: instagramContent.copy,
                editorState: instagramContent.editorState || undefined,
                type: instagramContent.type,
                collaborators: instagramContent.collaborators,
                reelShareToFeed: instagramContent.reelShareToFeed,
              },
            }
          : undefined,
        linkedInContent: linkedInContent
          ? {
              create: {
                copy: linkedInContent.copy,
                editorState: linkedInContent.editorState || undefined,
              },
            }
          : undefined,
        tikTokContent: tikTokContent
          ? {
              create: {
                copy: tikTokContent.copy,
                editorState: tikTokContent.editorState || undefined,
              },
            }
          : undefined,
        slackContent: slackContent
          ? {
              create: {
                copy: slackContent.copy,
                editorState: slackContent.editorState || undefined,
              },
            }
          : undefined,
        discordContent: discordContent
          ? {
              create: {
                copy: discordContent.copy,
                editorState: discordContent.editorState || undefined,
              },
            }
          : undefined,
        youTubeShortsContent: youTubeShortsContent
          ? {
              create: {
                title: youTubeShortsContent.title,
                titleEditorState:
                  youTubeShortsContent.titleEditorState || undefined,
                description: youTubeShortsContent.description,
                descriptionEditorState:
                  youTubeShortsContent.descriptionEditorState || undefined,
              },
            }
          : undefined,
        threadsContent: threadsContent
          ? {
              createMany: {
                data: threadsContent.map((content) => {
                  return {
                    copy: content.copy,
                    editorState: content.editorState || undefined,
                    threadPosition: content.threadPosition,
                  };
                }),
              },
            }
          : undefined,
        webflowContent: webflowContent
          ? {
              create: {
                title: webflowContent.title,
                body: webflowContent.body,
                bodyEditorState: webflowContent.bodyEditorState || undefined,
              },
            }
          : undefined,
      },
      include: {
        tweets: true,
        linkedInContent: true,
        tikTokContent: true,
        youTubeShortsContent: true,
        instagramContent: true,
        facebookContent: true,
        slackContent: true,
        discordContent: true,
        webflowContent: true,
        threadsContent: true,
      },
    });

    if (tipsLabel) {
      await db.assetLabel.create({
        data: {
          postID: post.id,
          labelID: tipsLabel.id,
        },
      });
    }

    // Map from old content IDs to new content IDs
    const folderRenames: { [fromName: string]: string } = {};

    const isPostChannel = (channel: Channel) => {
      return post.channels.includes(channel);
    };

    if (isPostChannel("Twitter")) {
      sourcePost.tweets.forEach((tweet) => {
        const newTweet = post.tweets.find(
          (newTweet) => newTweet.threadPosition === tweet.threadPosition
        );
        folderRenames[tweet.id] = newTweet!.id;
      });
    }

    if (isPostChannel("Facebook")) {
      folderRenames[sourcePost.facebookContent!.id] = post.facebookContent!.id;
    }

    if (isPostChannel("Instagram")) {
      folderRenames[sourcePost.instagramContent!.id] =
        post.instagramContent!.id;
    }

    if (isPostChannel("LinkedIn")) {
      folderRenames[sourcePost.linkedInContent!.id] = post.linkedInContent!.id;
    }

    if (isPostChannel("TikTok")) {
      folderRenames[sourcePost.tikTokContent!.id] = post.tikTokContent!.id;
    }

    if (isPostChannel("YouTubeShorts")) {
      folderRenames[sourcePost.youTubeShortsContent!.id] =
        post.youTubeShortsContent!.id;
    }

    if (isPostChannel("Slack")) {
      folderRenames[sourcePost.slackContent!.id] = post.slackContent!.id;
    }

    if (isPostChannel("Discord")) {
      folderRenames[sourcePost.discordContent!.id] = post.discordContent!.id;
    }

    if (isPostChannel("Webflow")) {
      folderRenames[sourcePost.webflowContent!.id] = post.webflowContent!.id;
    }

    if (isPostChannel("Threads")) {
      sourcePost.threadsContent.forEach((content) => {
        folderRenames[content.id] = post.threadsContent.find(
          (newContent) => newContent.threadPosition === content.threadPosition
        )!.id;
      });
    }

    await copyStorageFiles({
      sourceBasePath: `${sourcePost.workspaceID}/${sourcePost.id}`,
      destinationBasePath: `${workspaceID}/${post.id}`,
      copySourceFolderStructure: true,
      folderRenames,
      userID,
    });

    if (sourcePost.instagramContent) {
      const igAssets = await getStorageAssetsForInstagram(post);
      const originalAssets = sourcePost.instagramContent.assets;

      await db.asset.createMany({
        data: igAssets.assets.map((asset) => {
          const originalAsset = sourcePost.instagramContent?.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.instagramAsset.createMany({
        data: igAssets.assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            fileName: asset.name,
            mimetype: asset.mimetype,
            instagramContentID: post.instagramContent!.id,
          };
        }),
      });

      if (igAssets.coverPhoto) {
        await db.instagramContent.update({
          where: {
            id: post.instagramContent!.id,
          },
          data: {
            coverPhoto: {
              create: {
                id: igAssets.coverPhoto.id,
                name: igAssets.coverPhoto.name,
                bucket: "assets",
                path: igAssets.coverPhoto.path,
                eTag: igAssets.coverPhoto.eTag,
                mimetype: igAssets.coverPhoto.mimetype,
                size: igAssets.coverPhoto.sizeBytes,
                url: igAssets.coverPhoto.url,
                width: sourcePost.instagramContent.coverPhoto?.width,
                height: sourcePost.instagramContent.coverPhoto?.height,
                urlExpiresAt: igAssets.coverPhoto.urlExpiresAt,
                workspaceID: post.workspaceID,
                userID: post.createdByID,
              },
            },
          },
        });
      }

      const newInstagramAssets = await db.instagramAsset.findMany({
        where: {
          instagramContentID: post.instagramContent!.id,
        },
        select: {
          id: true,
          asset: {
            select: {
              name: true,
            },
          },
        },
      });

      await db.instagramAssetTag.createMany({
        data: igAssets.assets.flatMap((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return originalAsset.tags.map((tag) => {
            const newAsset = newInstagramAssets.find(
              (newAsset) => newAsset.asset.name === asset.name
            );

            return {
              assetID: newAsset!.id,
              username: tag.username,
              x: tag.x,
              y: tag.y,
            };
          });
        }),
      });
    }
    if (sourcePost.facebookContent) {
      const { assets, coverPhoto } = await getStorageAssetsForFacebook(post);
      const originalAssets = sourcePost.facebookContent.assets;

      await db.asset.createMany({
        data: assets.map((asset) => {
          const originalAsset = sourcePost.facebookContent?.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.facebookAsset.createMany({
        data: assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            fileName: asset.name,
            mimetype: asset.metadata.mimetype,
            facebookContentID: post.facebookContent!.id,
          };
        }),
      });

      if (coverPhoto) {
        await db.facebookContent.update({
          where: {
            id: post.facebookContent!.id,
          },
          data: {
            coverPhoto: {
              create: {
                id: coverPhoto.id,
                name: coverPhoto.name,
                bucket: "assets",
                path: coverPhoto.path,
                eTag: coverPhoto.eTag,
                mimetype: coverPhoto.mimetype,
                size: coverPhoto.sizeBytes,
                url: coverPhoto.url,
                width: sourcePost.facebookContent.coverPhoto?.width,
                height: sourcePost.facebookContent.coverPhoto?.height,
                urlExpiresAt: coverPhoto.urlExpiresAt,
                workspaceID: post.workspaceID,
                userID: post.createdByID,
              },
            },
          },
        });
      }
    }
    if (sourcePost.tweets.length > 0) {
      const twitterAssets = await getStorageAssetsForTwitter(post);

      await db.asset.createMany({
        data: Object.entries(twitterAssets).flatMap(([id, assets]) => {
          const tweetPosition = post.tweets.find(
            (tweet) => tweet.id === id
          )!.threadPosition;

          const originalTweet = sourcePost.tweets.find(
            (tweet) => tweet.threadPosition === tweetPosition
          );

          return assets.map((asset) => {
            const originalAsset = originalTweet!.assets.find(
              (originalAsset) => originalAsset.asset.eTag === asset.eTag
            );
            return {
              id: asset.id,
              name: asset.name,
              bucket: "assets",
              path: asset.path,
              eTag: asset.eTag,
              mimetype: asset.mimetype,
              size: asset.sizeBytes,
              url: asset.url,
              width: originalAsset?.asset.width,
              height: originalAsset?.asset.height,
              duration: originalAsset?.asset.duration,
              md5Hash: originalAsset?.asset.md5Hash,
              urlExpiresAt: asset.urlExpiresAt,
              workspaceID: post.workspaceID,
              userID: post.createdByID,
            };
          });
        }),
      });

      await db.tweetAsset.createMany({
        data: Object.entries(twitterAssets).flatMap(([id, assets]) => {
          const tweetPosition = post.tweets.find(
            (tweet) => tweet.id === id
          )!.threadPosition;

          const originalTweet = sourcePost.tweets.find(
            (tweet) => tweet.threadPosition === tweetPosition
          );

          return assets.map((asset) => {
            const originalAsset = originalTweet!.assets.find(
              (originalAsset) => originalAsset.asset.name === asset.name
            );
            return {
              assetID: asset.id,
              position: originalAsset!.position,
              tweetContentID: id,
            };
          });
        }),
      });
    }
    if (sourcePost.linkedInContent) {
      const linkedInAssets = await getStorageAssetsForLinkedIn(post);
      const originalAssets = sourcePost.linkedInContent!.assets;

      await db.asset.createMany({
        data: linkedInAssets.assets.map((asset) => {
          const originalAsset = sourcePost.linkedInContent?.assets.find(
            (originalAsset) => originalAsset.asset.eTag === asset.eTag
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.linkedInAsset.createMany({
        data: linkedInAssets.assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset, "original asset must exist");

          return {
            assetID: asset.id,
            position: originalAsset.position,
            linkedInContentID: post.linkedInContent!.id,
          };
        }),
      });

      if (linkedInAssets.coverPhoto) {
        await db.linkedInContent.update({
          where: {
            id: post.linkedInContent!.id,
          },
          data: {
            coverPhoto: {
              create: {
                id: linkedInAssets.coverPhoto.id,
                name: linkedInAssets.coverPhoto.name,
                bucket: "assets",
                path: linkedInAssets.coverPhoto.path,
                eTag: linkedInAssets.coverPhoto.eTag,
                mimetype: linkedInAssets.coverPhoto.mimetype,
                size: linkedInAssets.coverPhoto.sizeBytes,
                url: linkedInAssets.coverPhoto.url,
                width: sourcePost.linkedInContent!.coverPhoto?.width,
                height: sourcePost.linkedInContent!.coverPhoto?.height,
                md5Hash: sourcePost.linkedInContent!.coverPhoto?.md5Hash,
                urlExpiresAt: linkedInAssets.coverPhoto.urlExpiresAt,
                workspaceID: post.workspaceID,
                userID: post.createdByID,
              },
            },
          },
        });
      }

      if (linkedInAssets.captions) {
        await db.linkedInContent.update({
          where: {
            id: post.linkedInContent!.id,
          },
          data: {
            captions: {
              create: {
                id: linkedInAssets.captions.id,
                name: linkedInAssets.captions.name,
                bucket: "assets",
                path: linkedInAssets.captions.path,
                eTag: linkedInAssets.captions.eTag,
                mimetype: linkedInAssets.captions.mimetype,
                size: linkedInAssets.captions.sizeBytes,
                url: linkedInAssets.captions.url,
                width: sourcePost.linkedInContent!.captions?.width,
                height: sourcePost.linkedInContent!.captions?.height,
                md5Hash: sourcePost.linkedInContent!.captions?.md5Hash,
                urlExpiresAt: linkedInAssets.captions.urlExpiresAt,
                workspaceID: post.workspaceID,
                userID: post.createdByID,
              },
            },
          },
        });
      }
    }
    if (sourcePost.discordContent) {
      const assets = await getStorageAssetsForDiscord(post);
      const originalAssets = sourcePost.discordContent.assets;

      await db.asset.createMany({
        data: assets.map((asset) => {
          const originalAsset = sourcePost.discordContent!.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.discordAsset.createMany({
        data: assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            discordContentID: post.discordContent!.id,
          };
        }),
      });
    }

    if (sourcePost.tikTokContent) {
      const assets = await getStorageAssetsForTikTok(post);
      const originalAssets = sourcePost.tikTokContent.assets;

      await db.asset.createMany({
        data: assets.map((asset) => {
          const originalAsset = sourcePost.tikTokContent!.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.tikTokAsset.createMany({
        data: assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            tikTokContentID: post.tikTokContent!.id,
          };
        }),
      });
    }

    if (sourcePost.slackContent) {
      const assets = await getStorageAssetsForSlack(post);
      const originalAssets = sourcePost.slackContent.assets;

      await db.asset.createMany({
        data: assets.map((asset) => {
          const originalAsset = sourcePost.slackContent!.assets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          return {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset?.asset.width,
            height: originalAsset?.asset.height,
            duration: originalAsset?.asset.duration,
            md5Hash: originalAsset?.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          };
        }),
      });

      await db.slackAsset.createMany({
        data: assets.map((asset) => {
          const originalAsset = originalAssets.find(
            (originalAsset) => originalAsset.asset.name === asset.name
          );

          assert(originalAsset);

          return {
            assetID: asset.id,
            position: originalAsset.position,
            slackContentID: post.slackContent!.id,
          };
        }),
      });
    }

    if (sourcePost.youTubeShortsContent) {
      const { asset } = await getStorageAssetsForYouTubeShorts(post);
      const originalAsset = sourcePost.youTubeShortsContent.asset;

      if (asset && originalAsset) {
        await db.asset.create({
          data: {
            id: asset.id,
            name: asset.name,
            bucket: "assets",
            path: asset.path,
            eTag: asset.eTag,
            mimetype: asset.mimetype,
            size: asset.sizeBytes,
            url: asset.url,
            width: originalAsset.asset.width,
            height: originalAsset.asset.height,
            duration: originalAsset.asset.duration,
            md5Hash: originalAsset.asset.md5Hash,
            urlExpiresAt: asset.urlExpiresAt,
            workspaceID: post.workspaceID,
            userID: post.createdByID,
          },
        });
        await db.youTubeShortsAsset.create({
          data: {
            assetID: asset.id,
            youTubeShortsContentID: post.youTubeShortsContent!.id,
          },
        });
      }
    }
  });

  await Promise.all(promises);
};

export default createScaffolding;
