import { TRPCError } from "@trpc/server";
import catchZenstackDeleteError from "apiUtils/catchZenstackDeleteError";
import removeSubscriptionItem from "apiUtils/stripe/removeSubscriptionItem";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const disconnectFacebookAccount = async ({
  integrationID,
  workspaceID,
  userID,
}: {
  integrationID: string;
  workspaceID: string;
  userID: string;
}) => {
  const facebookIntegration = await db.facebookIntegration.findFirst({
    where: {
      id: integrationID,
      workspaceID,
    },
    select: {
      id: true,
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
      instagramIntegrations: {
        select: {
          id: true,
        },
      },
    },
  });

  if (!facebookIntegration) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "No integration found",
    });
  }

  const workspace = facebookIntegration.workspace;

  const postsToUpdate = await db.post.findMany({
    where: {
      facebookIntegrationID: integrationID,
      scheduledPosts: {
        some: {
          status: "Scheduled",
          channel: "Facebook",
          publishAt: {
            gt: new Date(),
          },
        },
      },
    },
    select: {
      id: true,
    },
  });
  const postIDs = postsToUpdate.map((post) => post.id);

  // Cancel all scheduled posts for just Facebook
  await db.scheduledPost.updateMany({
    where: {
      channel: "Facebook",
      status: "Scheduled",
      publishAt: {
        gt: new Date(),
      },
      post: {
        id: {
          in: postIDs,
        },
      },
    },
    data: {
      status: "Cancelled",
    },
  });

  const updatedPosts = await db.post.findMany({
    where: {
      id: {
        in: postIDs,
      },
    },
    select: {
      id: true,
      _count: {
        select: {
          scheduledPosts: {
            where: {
              status: "Scheduled",
            },
          },
        },
      },
    },
  });

  const postsWithoutScheduledPosts = updatedPosts.filter(
    (post) => post._count.scheduledPosts === 0
  );
  await db.post.updateMany({
    where: {
      id: {
        in: postsWithoutScheduledPosts.map((post) => post.id),
      },
    },
    data: {
      status: "Finalized",
    },
  });

  const postDefaults = await db.postDefault.findFirst({
    where: {
      workspaceID: workspaceID,
    },
    select: {
      facebookIntegrationID: true,
      workspace: {
        select: {
          facebookIntegrations: {
            where: {
              id: {
                not: integrationID,
              },
            },
            select: {
              id: true,
              createdAt: true,
            },
          },
        },
      },
    },
  });

  if (postDefaults?.facebookIntegrationID === integrationID) {
    const oldestFacebookIntegration =
      postDefaults.workspace.facebookIntegrations.sort(
        (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
      )[0];

    await db.postDefault.update({
      where: {
        workspaceID: workspaceID,
      },
      data: {
        facebookIntegrationID:
          oldestFacebookIntegration != null
            ? oldestFacebookIntegration.id
            : null,
      },
    });
  }

  await catchZenstackDeleteError(async () => {
    if (facebookIntegration.instagramIntegrations.length > 0) {
      await db.facebookIntegration.update({
        where: {
          id: integrationID,
        },
        data: {
          isIntegrated: false,
        },
      });
    } else {
      await db.facebookIntegration.update({
        where: {
          id: integrationID,
        },
        data: {
          deletedTimeStamp: Math.floor(new Date().getTime() / 1000),
          deletedByID: userID,
        },
      });
    }
  });

  await removeSubscriptionItem({
    workspaceID,
    itemName: "ADDITIONAL_SOCIAL_PROFILE",
    quantity: 1,
  });

  posthog.capture({
    distinctId: userID,
    event: POSTHOG_EVENTS.socialAccount.disconnected,
    properties: {
      channel: "Facebook",
    },
    groups: {
      workspace: workspaceID,
      company: workspace.companyID,
    },
  });

  return facebookIntegration;
};

export default disconnectFacebookAccount;
