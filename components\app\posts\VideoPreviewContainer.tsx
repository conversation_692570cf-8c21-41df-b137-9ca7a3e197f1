import Uppy from "@uppy/core";
import classNames from "classnames";
import { motion } from "framer-motion";
import { ReactNode } from "react";
import { Accept } from "react-dropzone";
import VideoPlayer from "~/components/ui/VideoPlayer";
import { AssetType } from "~/types/Asset";
import { handleFilesUpload } from "~/utils";
import { AssetControls } from "../files/AssetControls";
import { UploadingAsset } from "../files/Assets";
import FileDropzone from "../files/FileDropzone";

type Props = {
  video?: AssetType;
  coverPhotoUrl?: string;
  getValidFiles: (files: File[]) => Promise<File[]>;
  acceptedFileTypes: Accept;
  contentID?: string;
  uploadingAssets: UploadingAsset[];
  isDraggedOver: boolean;
  isReadOnly: boolean;
  uppy?: Uppy | null;
  onDrop: (files: File[]) => void;
  onDeleteClick: (asset: AssetType) => void;
  children: ReactNode;
};

const VideoPreviewContainer = ({
  video,
  coverPhotoUrl,
  children,
  acceptedFileTypes,
  contentID,
  uploadingAssets,
  isDraggedOver,
  isReadOnly,
  uppy,
  getValidFiles,
  onDrop,
  onDeleteClick,
}: Props) => {
  const uploadingVideo = uploadingAssets[0];

  const handleVideoClick = (e: React.MouseEvent<HTMLVideoElement>) => {
    const video = e.target as HTMLVideoElement;

    if (video.paused) {
      video.play();
    } else {
      video.pause();
    }
  };

  if (!video) {
    return (
      <motion.div
        key="dropzone"
        className="h-[532px] w-60 md:w-[300px]"
        initial={{ height: 532, opacity: 0 }}
        animate={{
          height: 532,
          opacity: 100,
        }}
      >
        <FileDropzone
          onDrop={async (files) => {
            if (uppy) {
              await handleFilesUpload({
                files,
                uppy,
                meta: {
                  contentID,
                },
                getValidFiles,
              });
            }
            onDrop(files);
          }}
          acceptedFileTypes={acceptedFileTypes}
          borderClassName={`rounded-3xl ${
            !isDraggedOver ? "border-none" : undefined
          }`}
          backgroundImage={
            !isDraggedOver ? "/instagram/placeholder.png" : undefined
          }
          label={
            !isDraggedOver
              ? "Click or drag and drop to upload media"
              : undefined
          }
          uploadProgress={uploadingVideo?.uploadProgress}
        />
      </motion.div>
    );
  }

  const isTall =
    video.width && video.height && video.width / video.height < 0.8;

  return (
    <motion.div
      className="group/asset scrollbar-hidden relative flex h-[532px] w-60 justify-center overflow-hidden rounded-3xl sm:w-[300px]"
      initial={{ height: 532, opacity: 0 }}
      animate={{
        height: 532,
        opacity: 100,
      }}
    >
      <div
        className={classNames("relative w-[300px] text-white", {
          "bg-black": !!video,
          "bg-medium-gray": !video,
        })}
      >
        <VideoPlayer.Root className="h-full w-full bg-transparent">
          <VideoPlayer.Content
            className={classNames({
              "h-full w-full": !isTall,
            })}
            src={video.signedUrl}
            loop={true}
            preload="auto"
            poster={coverPhotoUrl}
            onClick={handleVideoClick}
          />
        </VideoPlayer.Root>
        {children}
        <AssetControls
          asset={video}
          isReadOnly={isReadOnly}
          onDeleteClick={onDeleteClick}
        />
      </div>
    </motion.div>
  );
};

export default VideoPreviewContainer;
