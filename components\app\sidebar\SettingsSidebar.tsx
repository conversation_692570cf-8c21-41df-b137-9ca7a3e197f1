import {
  ArrowUpTrayIcon,
  BellIcon,
  BuildingOffice2Icon,
  Cog6ToothIcon,
  CreditCardIcon,
  DocumentTextIcon,
  MapIcon,
  QueueListIcon,
  Square3Stack3DIcon,
  SquaresPlusIcon,
  TagIcon,
  UsersIcon,
  WrenchScrewdriverIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useMounted } from "@mantine/hooks";
import { DateTime } from "luxon";
import Link from "next/link";
import { useMemo } from "react";
import Sidebar from "~/components/ui/Sidebar";
import { whitelistedBulkUploadWorkspaceIDs } from "~/constants";
import { useUser, useWorkspace } from "~/providers";
import { trpc } from "~/utils";
import MenuSidebarContent, { Item } from "./MenuSidebarContent";

export const SettingsSidebar = () => {
  const hasMounted = useMounted();
  const trpcUtils = trpc.useUtils();
  const billingPortalUrlQuery = trpc.stripe.getBillingPortalUrl.useQuery();
  const { currentWorkspace } = useWorkspace();
  const { user } = useUser();

  const billingPortalUrl = billingPortalUrlQuery.data?.billingPortalUrl;

  const schedulingItems: Item[] = useMemo(
    () => [
      {
        name: "Social Profiles",
        icon: <SquaresPlusIcon className="h-4 w-4" />,
        href: "/settings/profiles",
        onMouseEnter: () => {
          trpcUtils.workspace.getSchedulingProfiles.prefetch();
        },
      },
      ...(whitelistedBulkUploadWorkspaceIDs.includes(
        currentWorkspace?.id || ""
      ) || user?.isAdmin
        ? [
            {
              name: "Bulk Scheduling",
              icon: <ArrowUpTrayIcon className="h-4 w-4" />,
              href: "/settings/bulk-scheduling",
            },
          ]
        : []),
    ],
    [trpcUtils, currentWorkspace?.id, user?.isAdmin]
  );

  const workspaceSettingsItems: Item[] = useMemo(
    () => [
      {
        name: "General",
        icon: <Cog6ToothIcon className="h-4 w-4" />,
        href: "/settings/general",
        onMouseEnter: () => {
          trpcUtils.workspace.getCurrent.prefetch();
        },
      },
      {
        name: "Members",
        icon: <UsersIcon className="h-4 w-4" />,
        href: "/settings/members",
        onMouseEnter: () => {
          trpcUtils.workspace.getUsers.prefetch();
          trpcUtils.invite.getAllPending.prefetch();
        },
      },
      {
        name: "Labels",
        icon: <TagIcon className="h-4 w-4" />,
        href: "/settings/labels",
        onMouseEnter: () => {
          trpcUtils.label.getAll.prefetch();
        },
      },
      {
        name: "Post Defaults",
        icon: <DocumentTextIcon className="h-4 w-4" />,
        href: "/settings/post-defaults",
        onMouseEnter: () => {
          trpcUtils.postDefault.get.prefetch();
        },
      },
      {
        name: "Plans",
        icon: <MapIcon className="h-4 w-4" />,
        href: "/settings/pricing",
        onMouseEnter: () => {
          trpcUtils.stripe.getCurrentSubscription.prefetch();
        },
      },
      ...(billingPortalUrl
        ? [
            {
              name: "Billing",
              icon: <CreditCardIcon className="h-4 w-4" />,
              href: billingPortalUrl,
            },
          ]
        : []),
      {
        name: "Slack Integration",
        icon: <img src="/integrations/slack.png" className="h-4 w-4" />,
        href: "/settings/slack",
        onMouseEnter: () => {
          trpcUtils.slack.getNofiticationConfig.prefetch();
          trpcUtils.slack.getWorkspaceWebhooks.prefetch();
        },
      },
    ],
    [billingPortalUrl]
  );

  const accountSettingsItems: Item[] = useMemo(
    () => [
      {
        name: "Preferences",
        icon: <Cog6ToothIcon className="h-4 w-4" />,
        href: "/settings/user",
        onMouseEnter: () => {
          trpcUtils.user.getUserAndPreferences.prefetch();
        },
      },
      {
        name: "Notifications",
        icon: <BellIcon className="h-4 w-4" />,
        href: "/settings/notifications",
      },
    ],
    []
  );

  const adminSettingsItems: Item[] = useMemo(
    () => [
      {
        name: "Workspaces",
        icon: <Square3Stack3DIcon className="h-4 w-4" />,
        href: "/admin/settings/workspaces",
        onMouseEnter: () => {
          trpcUtils.user.currentUser.prefetch();
          trpcUtils.admin.getWorkspaces.prefetch();
        },
      },
      {
        name: "Companies",
        icon: <BuildingOffice2Icon className="h-4 w-4" />,
        href: "/admin/settings/companies",
        onMouseEnter: () => {
          trpcUtils.admin.company.getAll.prefetch();
        },
      },
      {
        name: "Scheduled Posts",
        icon: <QueueListIcon className="h-4 w-4" />,
        href: "/admin/settings/scheduled-posts",
        onMouseEnter: () => {
          trpcUtils.admin.scheduledPost.getAll.prefetch({
            startDate: DateTime.now().startOf("day").toJSDate(),
            endDate: DateTime.now().endOf("day").toJSDate(),
          });
        },
      },
      {
        name: "Tools",
        icon: <WrenchScrewdriverIcon className="h-4 w-4" />,
        subItems: {
          defaultOpen: false,
          items: [
            {
              name: "Users",
              href: "/admin/settings/users",
              onMouseEnter: () => {
                trpcUtils.admin.getWorkspaces.prefetch();
                trpcUtils.admin.user.getAll.prefetch();
              },
            },
            {
              name: "Delete Post",
              href: "/admin/settings/delete-post",
            },
            {
              name: "LinkedIn Leads",
              href: "/admin/settings/linkedin-leads",
            },
            {
              name: "Referrals",
              href: "/admin/settings/referrals",
              onMouseEnter: () => {
                trpcUtils.admin.referral.getAll.prefetch();
              },
            },
            {
              name: "Invites",
              href: "/admin/settings/invites",
              onMouseEnter: () => {
                trpcUtils.admin.getPendingInvites.prefetch();
              },
            },
            {
              name: "Prompts",
              href: "/admin/settings/prompts",
              onMouseEnter: () => {
                trpcUtils.admin.prompt.getAll.prefetch();
              },
            },
            {
              name: "Prompt Usage",
              href: "/admin/settings/prompt-usage",
              onMouseEnter: () => {
                trpcUtils.admin.prompt.getAllDrafts.prefetch();
              },
            },
          ],
        },
      },
    ],
    []
  );

  const sidebarGroups: {
    name: string;
    items: Item[];
  }[] = useMemo(
    () => [
      {
        name: "Connect Accounts",
        items: schedulingItems,
      },
      {
        name: "Workspace Settings",
        items: workspaceSettingsItems,
      },
      {
        name: "Account Settings",
        items: accountSettingsItems,
      },
      ...(user?.isAdmin
        ? [{ name: "Admin Settings", items: adminSettingsItems }]
        : []),
    ],
    [
      user?.isAdmin,
      schedulingItems,
      workspaceSettingsItems,
      accountSettingsItems,
      adminSettingsItems,
    ]
  );

  return (
    <Sidebar.Root>
      <Sidebar.Header className="px-5 pt-6">
        <div className="flex items-start justify-between">
          <div className="font-body-sm flex flex-col justify-start gap-0">
            <span className="font-medium">Settings</span>
            <span className="text-medium-dark-gray">
              {hasMounted && currentWorkspace?.name}
            </span>
          </div>
          <Link
            className="text-medium-dark-gray hover:text-basic transition-colors"
            href="/"
          >
            <XMarkIcon className="h-4 w-4" />
          </Link>
        </div>
      </Sidebar.Header>
      <MenuSidebarContent groups={sidebarGroups} />
    </Sidebar.Root>
  );
};
