import getAssetBuffer from "apiUtils/getAssetBuffer";
import getFileHash from "apiUtils/getFileHash";
import getStorageAssetsForYouTubeShorts from "apiUtils/youtube/getStorageAssetsForYouTubeShorts";
import imageSize from "image-size";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.findMany({
    take: 500,
    where: {
      channels: {
        hasSome: ["YouTubeShorts"],
      },
      youTubeShortsContent: {
        asset: null,
      },
    },
    select: {
      id: true,
      workspaceID: true,
      createdByID: true,
      youTubeShortsContent: {
        select: {
          id: true,
          asset: {
            select: {
              id: true,
              asset: true,
              assetID: true,
            },
          },
        },
      },
    },
  });

  console.log(`Num posts to backfill ${posts.length}`);

  const postAssetPromises = posts.map(async (post) => {
    const { asset } = await getStorageAssetsForYouTubeShorts(post);

    return {
      ...post,
      asset,
    };
  });

  const postsWithAssets = await Promise.all(postAssetPromises);

  console.log("Got all assets for posts");

  const postsToBackfill = postsWithAssets.filter((post) => {
    return post.asset != null;
  });

  console.log(`Backfilling ${postsToBackfill.length} posts`);

  const getImageDimensions = async (
    asset: {
      mimetype: string;
      url: string;
    } | null
  ) => {
    try {
      if (asset && asset.mimetype.includes("image/")) {
        const buffer = await getAssetBuffer(asset.url);
        const dimensions = imageSize(buffer);
        const md5Hash = await getFileHash(buffer);
        const width = dimensions.width as number;
        const height = dimensions.height as number;
        return { width, height, md5Hash };
      } else {
        return {
          width: null,
          height: null,
          md5Hash: null,
        };
      }
    } catch (error) {
      return {
        width: null,
        height: null,
        md5Hash: null,
      };
    }
  };

  const allAssets = postsToBackfill.flatMap((post) => {
    const { asset } = post;

    if (asset) {
      return [
        {
          ...asset,
          workspaceID: post.workspaceID,
          userID: post.createdByID,
        },
      ];
    } else {
      return [];
    }
  });

  const allAssetsWithDimensions = await Promise.all(
    allAssets.map(async (asset) => {
      const imageDimensions = await getImageDimensions(asset);

      return {
        ...asset,
        ...imageDimensions,
      };
    })
  );

  await db.asset.createMany({
    data: allAssetsWithDimensions.map((asset) => ({
      id: asset.id,
      name: asset.name,
      bucket: "assets",
      path: asset.path,
      eTag: asset.eTag,
      md5Hash: asset.md5Hash,
      mimetype: asset.mimetype,
      size: asset.sizeBytes,
      url: asset.url,
      width: asset.width,
      height: asset.height,
      urlExpiresAt: asset.urlExpiresAt,
      workspaceID: asset.workspaceID,
      userID: asset.userID,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
    })),
    skipDuplicates: true,
  });

  const backfillPromises = postsToBackfill.map(async (post) => {
    const { asset, youTubeShortsContent } = post;

    await db.youTubeShortsAsset.create({
      data: {
        assetID: asset!.id,
        youTubeShortsContentID: youTubeShortsContent!.id,
      },
    });
  });

  await Promise.all(backfillPromises);

  await db.post.updateMany({
    where: {
      id: {
        in: posts.map((post) => post.id),
      },
    },
    data: {
      hasBackfilledAssets: true,
    },
  });

  res.status(200).send({
    numPostsBackfilled: postsToBackfill.length,
  });
};

export default handler;
