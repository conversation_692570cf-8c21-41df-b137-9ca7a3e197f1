import catchAxiosError from "apiUtils/catchAxiosError";
import axios, { AxiosInstance } from "axios";
import { v4 as uuidv4 } from "uuid";
import { createSuccessResponse, Response } from "./Response";

class Typefully {
  BASE_URL = "https://api.typefully.com";

  client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: this.BASE_URL,
    });
  }

  getTwitterAccounts = async (
    username: string
  ): Promise<
    Response<
      {
        id: string;
        name: string;
        username: string;
        profileImageUrl: string;
        followersCount: number;
        verifiedType: "blue" | null;
      }[]
    >
  > => {
    try {
      const params = new URLSearchParams({
        q: username,
        Session: uuidv4(),
        Account: process.env.TYPEFULLY_ACCOUNT_ID as string,
        X: process.env.TYPEFULLY_BEARER_TOKEN as string,
      });

      const { status, data } = await this.client.get(
        `/editor/mention-suggestions-v2?${params}`
      );

      const rawUsers: {
        id_str: string;
        name: string;
        screen_name: string;
        profile_image_urls: {
          "400x400": string;
        };
        followers_count: number;
        verified_type?: "blue";
      }[] = data;

      const users = rawUsers.map((user) => ({
        // id is a number and is rounded so it fails to be parsed by the client
        id: user.id_str,
        name: user.name,
        username: user.screen_name,
        profileImageUrl: user.profile_image_urls["400x400"],
        followersCount: user.followers_count,
        verifiedType: user.verified_type || null,
      }));

      return createSuccessResponse(users, status);
    } catch (error) {
      return catchAxiosError(error, () => "Error Querying Users");
    }
  };
}

export default Typefully;
