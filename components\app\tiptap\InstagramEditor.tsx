import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import NumberFlow from "@number-flow/react";
import Document from "@tiptap/extension-document";
import { History } from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import classNames from "classnames";
import { ClipboardEvent, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import { HASHTAG_REGEX } from "~/constants";
import { CHANNEL_ICON_MAP } from "~/types";
import { stripLiveblocksMarks } from "~/utils";
import { Threads } from "./Threads";
import {
  <PERSON>Hashtag,
  AutoMention,
  CharacterCount,
  CustomCommentThreadMark,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "./extensions";
import BoldUnicodeButton from "./ui/BoldUnicodeButton";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import FloatingToolbar from "./ui/FloatingToolbar";
import ItalicsUnicodeButton from "./ui/ItalicsUnicodeButton";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  title?: string;
  postID?: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  size?: "sm" | "md";
  showHashtagCount?: boolean;
  isEditable?: boolean;
  roomID: string | null;
};

type InstagramEditorProps = Omit<BaseProps, "roomID">;

const createBaseExtensions = (
  postID: string | undefined,
  placeholder: string,
  size: "sm" | "md"
) => [
  Document,
  Paragraph.configure({
    HTMLAttributes: {
      class: size === "sm" ? "font-body-sm" : "font-body",
    },
  }),
  Text,
  History,
  Emoji,
  Link.configure({
    isLinkShorteningEnabled: !!postID ? true : false,
    postID,
  }),
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  AutoHashtag.configure({
    urlFn: (hashtag) => `https://www.instagram.com/explore/tags/${hashtag}`,
  }),
  AutoMention.configure({
    baseUrl: "https://instagram.com/",
    regex: /(?<=^|\s)(@[A-Za-z0-9_\.]{3,30})/g,
  }),
  CharacterCount.configure({
    limit: 2200,
  }),
  PasteHandler,
];

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const InstagramEditorFallback = ({
  initialValue,
  size = "md",
  postID,
  placeholder,
  onSave,
  onUpdateContent,
  isReadOnly = false,
  isEditable = false,
  showHashtagCount = false,
}: InstagramEditorProps) => {
  const cleanContent = stripLiveblocksMarks(initialValue);

  const maxHashtags = 30;
  const hashtagRegex = HASHTAG_REGEX;
  const [numHashtags, setNumHashtags] = useState<number>(0);

  const [editorState, setEditorState] = useState<JSONContent | null>(
    cleanContent
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(cleanContent) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const editor = useEditor({
    extensions: createBaseExtensions(postID, placeholder, size),
    immediatelyRender: false,
    content: cleanContent,
    editable: isEditable && !isReadOnly,
    onCreate: ({ editor }) => {
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      setNumHashtags(textContent.match(hashtagRegex)?.length || 0);
    },
    onUpdate: ({ editor }) => {
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      setNumHashtags(textContent.match(hashtagRegex)?.length || 0);

      onUpdateContent(textContent);

      const json = editor.getJSON();
      setEditorState(json);
    },
  });

  if (!editor) return null;

  return isEditable && !isReadOnly ? (
    <div className="relative py-2">
      <FloatingToolbar editor={editor} shouldHideWithLink={true}>
        <BoldUnicodeButton editor={editor} useCustomToolbarButton={true} />
        <ItalicsUnicodeButton editor={editor} useCustomToolbarButton={true} />
      </FloatingToolbar>
      <EditorContent editor={editor} />
      {showHashtagCount && (
        <div
          className={classNames("font-body-xs transition-colors", {
            "text-medium-dark-gray": numHashtags <= maxHashtags,
            "text-danger": numHashtags > maxHashtags,
            "absolute -bottom-5 left-0 mt-0": size === "sm",
            "mt-2": size === "md",
          })}
        >
          <NumberFlow value={numHashtags} /> hashtags
          {numHashtags > maxHashtags &&
            `. A max of ${maxHashtags} hashtags are allowed.`}
        </div>
      )}
    </div>
  ) : (
    <EditorContent editor={editor} />
  );
};

const InstagramEditor = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  size = "md",
}: InstagramEditorProps) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(postID, placeholder, size),
    ],
    immediatelyRender: false,
    editable: !isReadOnly,
    onCreate: ({ editor }) => {
      // TipTap doubles newlines for paragraph breaks (e.g., pressing Enter once creates two \n).
      // We halve the newlines to match LinkedIn's expected format while preserving paragraph structure.
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
    },
    onUpdate: ({ editor }) => {
      // TipTap doubles newlines for paragraph breaks (e.g., pressing Enter once creates two \n).
      // We halve the newlines to match LinkedIn's expected format while preserving paragraph structure.
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      onUpdateContent(textContent);

      const json = editor.getJSON();
      setEditorState(json);
    },
  });

  const isEditorReady = useIsEditorReady();

  if (!editor) return null;

  return (
    <div
      className="relative py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor} shouldHideWithLink={true}>
        <ToolbarActions
          editor={editor}
          features={{
            unicodeFormatting: true,
          }}
        />
      </CustomFloatingToolbar>

      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}
      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["Instagram"].label,
            }}
          />
          <ClientSideSuspense fallback={null}>
            <Threads editor={editor} />
          </ClientSideSuspense>
        </>
      ) : (
        <InstagramEditorFallback
          initialValue={initialValue}
          size={size}
          postID={postID}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
        />
      )}
    </div>
  );
};

const InstagramEditorWrapper = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  size = "md",
  showHashtagCount = false,
  roomID,
}: BaseProps) => {
  if (!roomID) {
    return (
      <InstagramEditorFallback
        postID={postID}
        initialValue={initialValue}
        placeholder={placeholder}
        onSave={onSave}
        onUpdateContent={onUpdateContent}
        size={size}
        showHashtagCount={showHashtagCount}
        isReadOnly={isReadOnly}
        isEditable={!isReadOnly}
      />
    );
  }

  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense
        fallback={
          <div className="py-2">
            <InstagramEditorFallback
              initialValue={initialValue}
              size={size}
              postID={postID}
              placeholder={placeholder}
              onSave={onSave}
              onUpdateContent={onUpdateContent}
            />
          </div>
        }
      >
        <InstagramEditor
          title={title}
          postID={postID}
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
          onImagePaste={onImagePaste}
          isReadOnly={isReadOnly}
          isEditable={!isReadOnly}
          size={size}
          showHashtagCount={showHashtagCount}
        />
      </ClientSideSuspense>
    </RoomProvider>
  );
};

export default InstagramEditorWrapper;
