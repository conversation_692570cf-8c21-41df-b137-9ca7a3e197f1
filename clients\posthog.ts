import { PostHog } from "posthog-node";

class CustomPostHog extends PostHog {
  private sessionID?: string;

  setSessionID(sessionID: string | undefined) {
    this.sessionID = sessionID;
  }

  capture(params: Parameters<PostHog["capture"]>[0]) {
    return super.capture({
      ...params,
      properties: {
        ...params.properties,
        $session_id: this.sessionID,
      },
    });
  }
}

const posthog = new CustomPostHog(
  process.env.NEXT_PUBLIC_POSTHOG_API_KEY as string
);

export default posthog;
