import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import chunk from "lodash/chunk";
import { db } from "~/clients";
import knock from "~/clients/Knock";
import { getFullName } from "~/utils";

const sleep = (milliseconds: number) => {
  return new Promise((resolve) => setTimeout(resolve, milliseconds));
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const email = req.query.email as string;

  const users = await db.user.findMany({
    where: {
      email,
    },
  });

  if (email && users.length > 0) {
    await knock.users.merge(users[0].id, email);
  }

  const chunks = chunk(users, 100);
  let i = 1;
  for await (const chunk of chunks) {
    console.log(`Chunk ${i}/${chunks.length}`);
    i++;
    await knock.users.bulkIdentify(
      chunk.map((user) => ({
        id: user.id,
        name: get<PERSON><PERSON><PERSON><PERSON>(user),
        email: user.email,
      }))
    );
    await sleep(2000);
  }

  res.status(StatusCodes.OK).send({ chunks });
};

export default handler;
