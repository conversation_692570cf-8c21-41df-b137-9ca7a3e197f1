import { assert } from "console";
import omit from "lodash/omit";
import { db } from "~/clients";
import { filterPosts } from "~/utils";
import { getPostProfiles } from "~/utils/posts/getPostProfiles";
import { protectedProcedure, router } from "../../trpc";

export const agencyRouter = router({
  getWorkspaces: protectedProcedure.query(async ({ ctx }) => {
    const userCompanies = await db.userCompany.findMany({
      where: {
        userID: ctx.user.id,
      },
      take: 1,
      select: {
        company: {
          select: {
            id: true,
            name: true,
            workspaces: {
              where: {
                userWorkspaces: {
                  some: {
                    userID: ctx.user.id,
                  },
                },
              },
              select: {
                id: true,
                name: true,
                posts: {
                  where: {
                    isIdea: false,
                  },
                  select: {
                    id: true,
                    title: true,
                    channels: true,
                    campaignID: true,
                    publishAt: true,
                    publishDate: true,
                    publishTime: true,
                    status: true,
                    isIdea: true,
                    instagramIntegrationID: true,
                    instagramIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    instagramContent: {
                      select: {
                        type: true,
                      },
                    },
                    threadsIntegrationID: true,
                    threadsIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    facebookIntegrationID: true,
                    facebookIntegration: {
                      select: {
                        id: true,
                        page: {
                          select: {
                            name: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    linkedInIntegrationID: true,
                    linkedInIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            name: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    tikTokIntegrationID: true,
                    tikTokIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    youTubeIntegrationID: true,
                    youTubeIntegration: {
                      select: {
                        id: true,
                        channel: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    twitterIntegrationID: true,
                    twitterIntegration: {
                      select: {
                        id: true,
                        account: {
                          select: {
                            username: true,
                            profileImageUrl: true,
                          },
                        },
                      },
                    },
                    discordWebhookID: true,
                    discordWebhook: {
                      select: {
                        id: true,
                        channel: true,
                        username: true,
                        profileImageUrl: true,
                      },
                    },
                    slackIntegrationID: true,
                    slackIntegration: {
                      select: {
                        id: true,
                        displayName: true,
                        profileImageUrl: true,
                      },
                    },
                    webflowIntegrationID: true,
                    webflowIntegration: {
                      select: {
                        id: true,
                        siteName: true,
                        collectionName: true,
                      },
                    },
                    subscribers: {
                      select: {
                        id: true,
                        userID: true,
                        user: {
                          select: {
                            firstName: true,
                            lastName: true,
                          },
                        },
                      },
                    },
                    postLabels: {
                      select: {
                        label: {
                          select: {
                            id: true,
                            name: true,
                            color: true,
                            backgroundColor: true,
                            createdAt: true,
                          },
                        },
                      },
                    },
                    approvals: {
                      select: {
                        id: true,
                        user: {
                          select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                          },
                        },
                        status: true,
                        isBlocking: true,
                        approvedAt: true,
                      },
                    },
                    createdAt: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return {
      company: {
        ...userCompanies[0].company,
        workspaces: userCompanies[0].company.workspaces.map((workspace) => {
          return {
            ...workspace,
            posts: filterPosts(
              workspace.posts.map((post) => {
                const publishDate = post.publishDate;
                assert(publishDate != null);

                const { postLabels, ...rest } = omit(post, [
                  "linkedInIntegration",
                  "twitterIntegration",
                  "instagramIntegration",
                  "threadsIntegration",
                  "facebookIntegration",
                  "tikTokIntegration",
                  "youTubeIntegration",
                  "discordWebhook",
                  "slackIntegration",
                  "webflowIntegration",
                ]);

                const accounts = getPostProfiles(post);

                return {
                  ...rest,
                  publishDate,
                  subscribers: post.subscribers.map((subscriber) => {
                    return {
                      ...subscriber,
                      user: subscriber.user,
                    };
                  }),
                  accounts,
                  labels: postLabels.map((postLabel) => {
                    return {
                      id: postLabel.label.id,
                      name: postLabel.label.name,
                      color: postLabel.label.color,
                      backgroundColor: postLabel.label.backgroundColor,
                      createdAt: postLabel.label.createdAt,
                    };
                  }),
                };
              })
            ),
          };
        }),
      },
    };
  }),

  getMembers: protectedProcedure.query(async ({ ctx }) => {
    // Obtener la company del usuario actual (siguiendo el patrón de getWorkspaces)
    const userCompanies = await db.userCompany.findMany({
      where: {
        userID: ctx.user.id,
      },
      take: 1,
      select: {
        company: {
          select: {
            id: true,
            workspaces: {
              select: {
                id: true,
                name: true,
                userWorkspaces: {
                  select: {
                    userID: true,
                    user: {
                      select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!userCompanies[0]) {
      return {
        agencyUsers: [] as Array<{
          id: string;
          firstName: string | null;
          lastName: string | null;
          email: string;
          workspaceCount: number;
        }>,
        clientUsers: [] as Array<{
          id: string;
          firstName: string | null;
          lastName: string | null;
          email: string;
          workspaceCount: number;
        }>,
        pendingUsers: [] as Array<{
          id: string;
          firstName: string | null;
          lastName: string | null;
          email: string;
          workspaceCount: number;
        }>,
      };
    }

    // Definir el tipo del usuario con workspace count
    type UserWithWorkspaceCount = {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
      workspaceCount: number;
    };

    // Procesar los datos para contar workspaces por usuario
    const userWorkspaceCount = new Map<string, number>();
    const allUsers = new Map<string, {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    }>();

    userCompanies[0].company.workspaces.forEach((workspace) => {
      workspace.userWorkspaces.forEach((userWorkspace) => {
        const userId = userWorkspace.userID;
        const user = userWorkspace.user;

        // Contar workspaces por usuario
        userWorkspaceCount.set(userId, (userWorkspaceCount.get(userId) || 0) + 1);

        // Almacenar información del usuario
        if (!allUsers.has(userId)) {
          allUsers.set(userId, {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            // profileImageUrl: user.profileImageUrl,
            // status: userWorkspace.status,
          });
        }
      });
    });

    // Categorizar usuarios basado en el número de workspaces con tipos explícitos
    const agencyUsers: UserWithWorkspaceCount[] = [];
    const clientUsers: UserWithWorkspaceCount[] = [];
    const pendingUsers: UserWithWorkspaceCount[] = [];

    allUsers.forEach((user, userId) => {
      const workspaceCount = userWorkspaceCount.get(userId) || 0;
      const userWithCount: UserWithWorkspaceCount = {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        // profileImageUrl: user.profileImageUrl,
        workspaceCount,
      };

      // Por ahora todos los usuarios se consideran activos
      // TODO: Implementar lógica de pending users cuando se agregue el campo status
      if (workspaceCount > 1) {
        agencyUsers.push(userWithCount);
      } else {
        clientUsers.push(userWithCount);
      }
    });

    return {
      agencyUsers,
      clientUsers,
      pendingUsers,
    };
  }),
});

