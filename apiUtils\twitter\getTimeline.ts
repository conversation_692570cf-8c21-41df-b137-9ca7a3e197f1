import assert from "assert";
import sortBy from "lodash/sortBy";
import getAuthedTwitterClient from "../getAuthedTwitterClient";

const getTimeline = async ({
  integration,
  sinceID,
  untilID,
  startTime,
}: {
  integration: {
    accessToken: string;
    accessSecret: string;
    accountID: string;
    account: {
      username: string;
    };
  };
  sinceID?: string;
  untilID?: string;
  startTime?: Date;
}) => {
  const client = getAuthedTwitterClient(integration);

  const timeline = await client.v2.userTimeline(integration.accountID, {
    max_results: 100,
    since_id: sinceID,
    until_id: untilID,
    start_time: startTime?.toISOString(),
    exclude: ["retweets", "replies"],
    "tweet.fields": [
      "id",
      // "organic_metrics",
      "public_metrics",
      // "non_public_metrics",
      "text",
      "created_at",
      "conversation_id",
      "in_reply_to_user_id",
      "referenced_tweets",
      "attachments",
      "author_id",
    ],
    "user.fields": [
      "created_at",
      "description",
      "entities",
      "id",
      "location",
      "name",
      "pinned_tweet_id",
      "profile_image_url",
      "protected",
      "public_metrics",
      "url",
      "username",
      "verified",
      "verified_type",
      "withheld",
    ],
    "media.fields": [
      "duration_ms",
      "height",
      "media_key",
      "preview_image_url",
      "type",
      "url",
      "width",
      "public_metrics",
      // "non_public_metrics",
      // "organic_metrics",
      "alt_text",
      "variants",
    ],
    expansions: [
      "author_id",
      "referenced_tweets.id",
      "referenced_tweets.id.author_id",
      "in_reply_to_user_id",
      "entities.mentions.username",
      "attachments.media_keys",
    ],
  });

  const media = timeline.includes?.media || [];

  const includedUsers = timeline.includes.users;
  const includedUserIDs = includedUsers.map((user) => user.id);

  const tweets = sortBy(
    timeline.tweets.map((tweet) => {
      assert(tweet.created_at != null);
      const publishedAt = new Date(tweet.created_at);

      const mediaKeys = tweet.attachments?.media_keys || [];
      const tweetMedia = media.filter((media) =>
        mediaKeys.includes(media.media_key)
      );

      const imageUrls = tweetMedia
        .filter((media) => media.type === "photo")
        .map((media) => media.url as string);
      const videoUrls = tweetMedia
        .filter((media) => media.type === "video")
        .map((media) => {
          const variants = media.variants || [];
          const highestBitRateVariant = variants.reduce(
            (highestBitRate, variant) => {
              if ((variant.bit_rate || 0) > (highestBitRate.bit_rate || 0)) {
                return variant;
              }

              return highestBitRate;
            },
            variants[0]
          );

          return highestBitRateVariant.url;
        });
      const gifUrls = tweetMedia
        .filter((media) => media.type === "animated_gif")
        .map((media) => {
          const variants = media.variants || [];
          const highestBitRateVariant = variants.reduce(
            (highestBitRate, variant) => {
              if ((variant.bit_rate || 0) > (highestBitRate.bit_rate || 0)) {
                return variant;
              }

              return highestBitRate;
            },
            variants[0]
          );

          return highestBitRateVariant.url;
        });

      assert(tweet.public_metrics != null);
      const metrics = {
        retweetCount: tweet.public_metrics.retweet_count,
        replyCount: tweet.public_metrics.reply_count,
        likeCount: tweet.public_metrics.like_count,
        quoteCount: tweet.public_metrics.quote_count,
        // @ts-ignore it does exist
        bookmarkCount: tweet.public_metrics.bookmark_count as number,
        impressionCount: tweet.public_metrics.impression_count,
      };

      return {
        id: tweet.id,
        text: tweet.text,
        accountID: tweet.author_id as string,
        type:
          tweet.referenced_tweets?.[0]?.type === "replied_to"
            ? ("Reply" as const)
            : ("Tweet" as const),
        permalink: `https://twitter.com/${integration.account.username}/status/${tweet.id}`,
        mentionedAccountIDs: (
          tweet.entities?.mentions?.map((mention) => mention.id) || []
        ).filter((mentionID) => includedUserIDs.includes(mentionID)),
        imageUrls,
        videoUrls,
        gifUrls,
        metrics,
        publishedAt,
        rawResponse: tweet,
      };
    }),
    "publishedAt"
  );

  return {
    timeline,
    tweets,
  };
};

export default getTimeline;
