import * as TabsPrimitive from "@radix-ui/react-tabs";
import * as React from "react";

import { EasingDefinition } from "framer-motion";
import { cn } from "~/utils";
import AnimateChangeInHeight from "../AnimateChangeInHeight";

const Tabs = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Root> & {
    transitionDuration?: number;
    transitionEase?: EasingDefinition;
    value?: string;
    defaultValue?: string;
  }
>(
  (
    {
      className,
      children,
      transitionDuration = 0.1,
      transitionEase = "linear",
      value,
      defaultValue,
      ...props
    },
    ref
  ) => {
    // Create a ref to store the available tab values
    const availableTabsRef = React.useRef<Set<string>>(new Set());

    // Create a state to track the current value
    const [currentValue, setCurrentValue] = React.useState<string | undefined>(
      value || defaultValue
    );

    // Update current value when value prop changes
    React.useEffect(() => {
      if (value !== undefined) {
        setCurrentValue(value);
      }
    }, [value]);

    // Find available tab values by scanning children
    React.useEffect(() => {
      // Clear the set first
      availableTabsRef.current.clear();

      // Function to recursively scan children for TabsTrigger components
      const scanForTriggers = (child: React.ReactNode) => {
        if (!React.isValidElement(child)) return;

        // If it's a TabsTrigger, add its value to the available tabs
        if (child.type === TabsTrigger && child.props.value) {
          availableTabsRef.current.add(child.props.value);
        }

        // If it has children, scan them too
        if (child.props && child.props.children) {
          React.Children.forEach(child.props.children, scanForTriggers);
        }
      };

      // Scan all children
      React.Children.forEach(children, scanForTriggers);

      // Check if current value is available, if not, use the first available tab
      if (currentValue && !availableTabsRef.current.has(currentValue)) {
        const firstAvailableTab = Array.from(availableTabsRef.current)[0];
        if (firstAvailableTab) {
          setCurrentValue(firstAvailableTab);
          // If onValueChange is provided, call it with the new value
          if (props.onValueChange) {
            props.onValueChange(firstAvailableTab);
          }
        }
      }
    }, [children, currentValue, props.onValueChange]);

    return (
      <TabsPrimitive.Root
        ref={ref}
        className={cn(className)}
        value={currentValue}
        defaultValue={defaultValue}
        onValueChange={setCurrentValue}
        {...props}
      >
        <AnimateChangeInHeight
          transitionDuration={transitionDuration}
          transitionEase={transitionEase}
        >
          {children}
        </AnimateChangeInHeight>
      </TabsPrimitive.Root>
    );
  }
);
Tabs.displayName = TabsPrimitive.Root.displayName;

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex h-9 items-center justify-center gap-3",
      className
    )}
    {...props}
  />
));
TabsList.displayName = TabsPrimitive.List.displayName;

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "text-medium-dark-gray hover:border-medium-gray font-body-sm ring-offset-background focus-visible:ring-secondary data-[state=active]:border-basic data-[state=active]:text-basic inline-flex items-center justify-center border-b border-transparent py-1 font-medium whitespace-nowrap transition-all focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50",
      className
    )}
    {...props}
  />
));
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "ring-offset-background focus-visible:ring-secondary mt-2 max-w-full focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden",
      className
    )}
    {...props}
  >
    {children}
  </TabsPrimitive.Content>
));
TabsContent.displayName = TabsPrimitive.Content.displayName;

const Root = Tabs;
const List = TabsList;
const Trigger = TabsTrigger;
const Content = TabsContent;

export default {
  Root,
  List,
  Trigger,
  Content,
};
