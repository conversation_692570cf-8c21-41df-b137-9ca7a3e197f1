import { InstagramAccountType } from "@prisma/client";
import { AxiosInstance } from "axios";
import { DateTime } from "luxon";
import { createSuccessResponse, Response } from "~/clients/Response";
import catchFacebookError from "../../catchFacebookError";

class ProfileService {
  private readonly client: AxiosInstance;
  private readonly platform: "Facebook" | "Instagram";

  constructor(
    client: AxiosInstance,
    { platform }: { platform: "Facebook" | "Instagram" }
  ) {
    this.client = client;
    this.platform = platform;
  }

  private getIDField = () => {
    return this.platform === "Instagram" ? "user_id" : "id";
  };

  getMe = async (): Promise<
    Response<{
      id: string;
      name: string | null;
      username: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
      biography: string | null;
      accountType: InstagramAccountType | null;
    }>
  > => {
    try {
      const fields = [
        this.getIDField(),
        "name",
        "username",
        "profile_picture_url",
        "biography",
        this.platform === "Instagram" ? "account_type" : null,
      ]
        .filter(Boolean)
        .join(",");

      const params = new URLSearchParams({
        fields:
          this.platform === "Instagram"
            ? fields
            : `instagram_business_account{${fields}}`,
      });

      const { data, status } = await this.client.get("/me", { params });

      const rawInstagramAccount =
        this.platform === "Instagram" ? data : data.instagram_business_account;

      const account = {
        id: rawInstagramAccount[this.getIDField()],
        name: rawInstagramAccount.name || null,
        username: rawInstagramAccount.username,
        profileImageUrl: rawInstagramAccount.profile_picture_url || null,
        profileImageExpiresAt: rawInstagramAccount.profile_picture_url
          ? DateTime.now().plus({ days: 3 }).toJSDate()
          : null,
        biography: rawInstagramAccount.biography || null,
        accountType: rawInstagramAccount.account_type || null,
      };

      return createSuccessResponse(account, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  getByID = async (
    instagramAccountID: string
  ): Promise<
    Response<{
      id: string;
      name: string | null;
      username: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
      followersCount: number;
      followsCount: number;
      mediaCount: number;
    }>
  > => {
    const params = new URLSearchParams({
      fields: [
        this.getIDField(),
        "name",
        "username",
        "profile_picture_url",
        "followers_count",
        "follows_count",
        "media_count",
      ].join(","),
    });

    try {
      const { status, data } = await this.client.get(
        `/${instagramAccountID}?${params}`
      );

      const account = {
        id: data[this.getIDField()],
        name: data.name || null,
        username: data.username,
        profileImageUrl: data.profile_picture_url || null,
        profileImageExpiresAt: data.profile_picture_url
          ? DateTime.now().plus({ days: 3 }).toJSDate()
          : null,
        followersCount: data.followers_count,
        followsCount: data.follows_count,
        mediaCount: data.media_count,
      };

      return createSuccessResponse(account, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };
}

export default ProfileService;
