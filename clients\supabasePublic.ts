import { CookieOptions, createBrowserClient } from "@supabase/ssr";
import { parse, serialize } from "cookie";

// Helper to get the correct auth cookie name based on environment
const getAuthCookieName = () => {
  if (process.env.NODE_ENV === "production") {
    return "sb-mletdxmpsjlbfpnymbcu-auth-token";
  }
  return "sb-localhost-auth-token";
};

const supabasePublic = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    auth: {
      flowType: "pkce",
      autoRefreshToken: true,
      persistSession: true,
      storage: {
        getItem: (key) => {
          if (typeof document === "undefined") return null;
          try {
            const cookies = parse(document.cookie);
            const cookieName = getAuthCookieName();

            // Get all numbered cookies
            let value = cookies[cookieName] || "";
            let index = 0;
            while (cookies[`${cookieName}.${index}`]) {
              value += cookies[`${cookieName}.${index}`];
              index++;
            }
            return value || null;
          } catch (error) {
            console.error("Error reading auth cookie:", error);
            return null;
          }
        },
        setItem: (key, value) => {
          if (typeof document === "undefined") return;
          try {
            const cookieName = getAuthCookieName();
            const maxLength = 4000; // Slightly under 4KB to be safe

            // Split the value into chunks
            const chunks =
              value.match(new RegExp(`.{1,${maxLength}}`, "g")) || [];

            // Set the main cookie with first chunk if exists
            if (chunks.length > 0) {
              document.cookie = serialize(cookieName, chunks[0] as string, {
                path: "/",
                secure: process.env.NODE_ENV === "production",
                sameSite: "lax",
                maxAge: 60 * 60 * 24 * 365, // 1 year
              });
            }

            // Set numbered cookies for remaining chunks
            chunks.slice(1).forEach((chunk, index) => {
              document.cookie = serialize(`${cookieName}.${index}`, chunk, {
                path: "/",
                secure: process.env.NODE_ENV === "production",
                sameSite: "lax",
                maxAge: 60 * 60 * 24 * 365, // 1 year
              });
            });
          } catch (error) {
            console.error("Error setting auth cookie:", error);
          }
        },
        removeItem: (key) => {
          if (typeof document === "undefined") return;
          try {
            const cookieName = getAuthCookieName();
            // Remove main cookie
            document.cookie = serialize(cookieName, "", {
              path: "/",
              expires: new Date(0),
            });
            // Remove all numbered cookies
            for (let i = 0; i < 10; i++) {
              // Arbitrary limit
              document.cookie = serialize(`${cookieName}.${i}`, "", {
                path: "/",
                expires: new Date(0),
              });
            }
          } catch (error) {
            console.error("Error removing auth cookie:", error);
          }
        },
      },
    },
    cookies: {
      get(name: string) {
        if (typeof document === "undefined") return "";
        const cookies = parse(document.cookie);
        return cookies[name];
      },
      set(name: string, value: string, options: CookieOptions) {
        if (typeof document === "undefined") return;
        document.cookie = serialize(name, value, options);
      },
      remove(name: string, options: CookieOptions) {
        if (typeof document === "undefined") return;
        document.cookie = serialize(name, "", {
          ...options,
          expires: new Date(0),
        });
      },
    },
  }
);

export default supabasePublic;
