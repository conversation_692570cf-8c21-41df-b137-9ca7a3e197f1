import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import { db } from "~/clients";
import knock from "~/clients/Knock";
import { sleep } from "~/utils";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const workspaces = await db.workspace.findMany();

  const response = [];

  let index = 0;
  for await (const workspace of workspaces) {
    const tenant = await knock.tenants.set(workspace.id, {
      name: workspace.name,
    });

    response.push(tenant);
    index++;

    if (index % 59 === 0) {
      await sleep(1000);
    }
  }

  res.status(StatusCodes.OK).send({ response });
};

export default handler;
