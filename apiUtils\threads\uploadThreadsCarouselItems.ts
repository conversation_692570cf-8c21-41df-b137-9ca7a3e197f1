import assert from "assert";
import { DateTime } from "luxon";
import Threads from "~/clients/facebook/Threads";
import { db } from "~/clients/Prisma";

const uploadThreadsCarouselItems = async ({
  post,
}: {
  post: {
    id: string;
    threadsIntegration: {
      accessToken: string;
      account: {
        threadsUserID: string;
      };
    };
    threadsContent: {
      id: string;
      assets: {
        id: string;
        asset: {
          url: string;
          mimetype: string;
        };
      }[];
    }[];
  };
}): Promise<
  | {
      status: "Success";
      error?: never;
      assetIDToContainerID: { [assetID: string]: string };
    }
  | {
      status: "Error" | "NonRetriableError";
      error: string;
      assetIDToContainerID?: never;
      isAuthError: boolean;
    }
  | {
      status: "Skipped";
      error?: never;
      assetIDToContainerID?: never;
    }
> => {
  const { threadsContent, threadsIntegration } = post;

  if (!threadsContent) {
    console.error(
      `Threads Upload Carousel Items Error post:${post.id}: No Threads content to Post`
    );
    return {
      status: "NonRetriableError",
      error: "No Threads content to post",
      isAuthError: false,
    };
  }

  if (!threadsIntegration) {
    console.error(
      `Threads Upload Carousel Items Error post:${post.id}: No Threads integration`
    );
    return {
      status: "NonRetriableError",
      error: "No Threads Integration",
      isAuthError: false,
    };
  }

  const threadsToUpload = threadsContent.filter(
    (thread) => thread.assets.length > 1
  );

  const threadsAssets = threadsToUpload.flatMap((thread) => thread.assets);

  if (threadsAssets.length === 0) {
    console.log(
      `Skip Uploading Threads post:${post.id}: No carousel items to upload`
    );
    return {
      status: "Skipped",
    };
  }

  const threads = new Threads({
    accessToken: threadsIntegration.accessToken,
    threadsUserID: threadsIntegration.account.threadsUserID,
  });

  let uploadResponse = await threads.createCarouselItemContainers({
    assets: threadsAssets.map((asset) => ({
      id: asset.id,
      url: asset.asset.url,
      mimetype: asset.asset.mimetype,
    })),
  });

  if (uploadResponse.errors) {
    // Retry uploading assets
    uploadResponse = await threads.createCarouselItemContainers({
      assets: threadsAssets.map((asset) => ({
        id: asset.id,
        url: asset.asset.url,
        mimetype: asset.asset.mimetype,
      })),
    });

    if (uploadResponse.errors) {
      const error = uploadResponse.errors.join(". ");
      console.error(`Threads Carousel Upload Error post:${post.id}: ${error}`);

      const isAuthError = uploadResponse.isAuthError;
      return {
        status: isAuthError ? "NonRetriableError" : "Error",
        error,
        isAuthError,
      };
    }
  }

  assert(uploadResponse.data);
  const assetIDToContainerID = uploadResponse.data;

  await db.$transaction(async (tx) => {
    for await (const asset of threadsAssets) {
      await tx.threadsAsset.update({
        where: {
          id: asset.id,
        },
        data: {
          containerID: assetIDToContainerID[asset.id],
          containerExpiresAt: DateTime.now().plus({ days: 1 }).toJSDate(),
        },
      });
    }
  });

  console.log(`Uploaded Threads Carousel Assets Successfully post:${post.id}`);

  return {
    status: "Success",
    assetIDToContainerID,
  };
};

export default uploadThreadsCarouselItems;
