import { Asset, YouTubePrivacyStatus } from "@prisma/client";
import getAssetBuffer from "apiUtils/getAssetBuffer";
import getAssetsForYouTubeShorts from "apiUtils/getAssetsForYouTubeShorts";
import { google } from "googleapis";
import { StatusCodes } from "http-status-codes";
import { Readable } from "stream";
import { GENERIC_ERROR_MESSAGE } from "~/constants";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";
import refreshAccessToken from "./refreshAccessToken";

type Post = {
  id: string;
  youTubeIntegration: {
    id: string;
    accessToken: string;
    refreshToken: string;
    idToken: string;
    expiresAt: Date;
    channel: {
      username: string;
    };
  } | null;
  youTubeShortsContent: {
    id: string;
    title: string | null;
    description: string | null;
    categoryID: string | null;
    privacyStatus: YouTubePrivacyStatus;
    notifySubscribers: boolean;
    isEmbeddable: boolean;
    isSelfDeclaredMadeForKids: boolean;
    asset: {
      id: string;
      asset: Asset;
    } | null;
  } | null;
  title: string;
  workspaceID: string;
};

const publishYouTubeVideo = async (
  post: Post
): Promise<ScheduledPostResult> => {
  const { youTubeShortsContent } = post;
  let youTubeIntegration = post.youTubeIntegration;

  if (!youTubeShortsContent) {
    return {
      status: "NonRetriableError",
      error: "No YouTube Shorts Content to Post",
    };
  }

  if (!youTubeIntegration) {
    return {
      status: "NonRetriableError",
      error: "No YouTube Integration",
    };
  }

  // If the integration is expired, refresh it
  if (youTubeIntegration.expiresAt < new Date()) {
    const response = await refreshAccessToken(youTubeIntegration);

    if (response.status === "Error") {
      return {
        status: "NonRetriableError",
        error: response.error,
        isAuthError: response.isAuthError,
      };
    } else {
      youTubeIntegration = response.youTubeIntegration;
    }
  }

  const { asset } = getAssetsForYouTubeShorts(post);

  if (asset == null || !asset.metadata.mimetype.includes("video")) {
    return {
      status: "NonRetriableError",
      error: "No video to post",
    };
  }

  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    `${process.env.NEXT_PUBLIC_DOMAIN}/settings/youtube/callback`
  );
  oauth2Client.setCredentials({
    access_token: youTubeIntegration.accessToken,
    refresh_token: youTubeIntegration.refreshToken,
    id_token: youTubeIntegration.idToken,
  });

  const youtube = google.youtube({
    version: "v3",
    auth: oauth2Client,
  });

  const buffer = await getAssetBuffer(asset.signedUrl);

  try {
    const stream = Readable.from(buffer);

    const response = await youtube.videos.insert({
      part: ["id", "snippet", "status"],
      notifySubscribers: youTubeShortsContent.notifySubscribers,
      requestBody: {
        snippet: {
          title: youTubeShortsContent.title,
          description: youTubeShortsContent.description,
          categoryId: youTubeShortsContent.categoryID,
        },
        status: {
          privacyStatus: youTubeShortsContent.privacyStatus,
          embeddable: youTubeShortsContent.isEmbeddable,
          selfDeclaredMadeForKids:
            youTubeShortsContent.isSelfDeclaredMadeForKids,
        },
      },
      media: {
        body: stream,
      },
    });

    const uploadStatus = response.data.status?.uploadStatus;

    if (uploadStatus === "failed" || uploadStatus === "rejected") {
      console.log("Video Failed to Upload:", response.data.status);
      const error =
        response.data.status?.failureReason ||
        response.data.status?.rejectionReason;
      return {
        status: "Error",
        error:
          error ||
          "Video upload failed. Reach <NAME_EMAIL> for more details.",
        rawError: response.data,
      };
    } else if (response.data.id) {
      const youTubeLink = `https://www.youtube.com/shorts/${response.data.id}`;
      console.log("Video Uploaded Successfully:", youTubeLink);
      return {
        status: "Success",
        postUrl: youTubeLink,
      };
    } else {
      console.log("Unable to Find Video ID Data:,", response.data);
      console.log("Unable to Find Video ID Status:,", response.data.status);
      const error = GENERIC_ERROR_MESSAGE;

      return {
        status: "Error",
        error,
        rawError: response.data,
      };
    }
  } catch (error) {
    try {
      console.error(error);

      const youtubeError = error as {
        status: number;
        statusText: string;
        errors: {
          domain: string;
          location: string;
          locationType: string;
          message: string;
          reason: string;
        }[];
      };

      const isAuthError = youtubeError.status === StatusCodes.UNAUTHORIZED;
      const errorMessage = youtubeError.errors[0]?.message;

      return {
        status: isAuthError ? "NonRetriableError" : "Error",
        error: errorMessage || GENERIC_ERROR_MESSAGE,
        rawError: error,
        isAuthError,
      };
    } catch (error) {
      console.error(error);
      return {
        status: "Error",
        error: GENERIC_ERROR_MESSAGE,
      };
    }
  }
};

export default publishYouTubeVideo;
