import * as React from "react";
import { JSXElementConstructor, ReactElement, ReactNode } from "react";

import * as TooltipPrimitive from "@radix-ui/react-tooltip";
import { cn } from "~/utils";

const TooltipProvider = TooltipPrimitive.Provider;

const TooltipRoot = TooltipPrimitive.Root;

const TooltipTrigger = TooltipPrimitive.Trigger;

const TooltipArrow = TooltipPrimitive.Arrow;

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    onClick={(e) => {
      e.stopPropagation();
    }}
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      "border-lightest-gray font-body-sm box-shadown animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-[300px] overflow-hidden rounded-md border bg-white px-3 py-2",
      className
    )}
    {...props}
  />
));
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

export type TooltipSide = "top" | "right" | "bottom" | "left";

type Props = {
  delayDuration?: number;
  side?: TooltipSide;
  sideOffset?: number;
  align?: "start" | "center" | "end";
  value?: ReactNode | string;
  children: ReactElement<any, string | JSXElementConstructor<any>>;
  className?: string;
};

const Tooltip = ({
  delayDuration = 300,
  side,
  sideOffset,
  align,
  value,
  children,
  className,
}: Props) => {
  if (!value) {
    return children;
  }

  return (
    <TooltipProvider delayDuration={delayDuration}>
      <TooltipRoot>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipPrimitive.TooltipPortal>
          <TooltipContent
            side={side}
            align={align}
            sideOffset={sideOffset}
            className={className}
          >
            {value}
            <TooltipArrow className="fill-lightest-gray box-shadow" />
          </TooltipContent>
        </TooltipPrimitive.TooltipPortal>
      </TooltipRoot>
    </TooltipProvider>
  );
};

export default Tooltip;

export {
  TooltipArrow,
  TooltipContent,
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
};
