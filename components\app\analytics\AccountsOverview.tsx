import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { Channel } from "@prisma/client";
import { differenceInDays } from "date-fns";
import { DateTime } from "luxon";
import Link from "next/link";
import { useRouter } from "next/router";
import { Loader, TextLink } from "~/components";
import { useAnalyticsView, useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { RequireAtLeastOne } from "~/types/RequireAtLeastOne";
import { formatPercentage, formatStat, trpc } from "~/utils";
import { AnalyticsHeader } from ".";
import ChannelIcon from "../ChannelIcon";

type Props = {
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
};

const AccountsOverview = ({ dateRange }: Props) => {
  const workspaceAccountStatsQuery = trpc.analytics.getAccountStats.useQuery({
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
  });

  const workspaceAccountStats = workspaceAccountStatsQuery.data;

  const daysDelta = Math.abs(
    differenceInDays(dateRange.startDate, dateRange.endDate)
  );
  const prevStartDate = DateTime.fromJSDate(dateRange.startDate).minus({
    days: daysDelta,
  });
  const prevEndDate = DateTime.fromJSDate(dateRange.endDate).minus({
    days: daysDelta,
  });

  const previousPeriod = `${prevStartDate.toFormat(
    "MM/dd"
  )} - ${prevEndDate.toFormat("MM/dd")}`;

  return (
    <div className="scrollbar-hidden h-full space-y-8 overflow-auto">
      {workspaceAccountStats == null ? (
        <div className="flex w-full items-center justify-center">
          <Loader size="lg" />
        </div>
      ) : (
        <div className="space-y-6">
          <AccountOverview
            channel="LinkedIn"
            accounts={workspaceAccountStats.linkedInAccounts}
            previousPeriod={previousPeriod}
          />
          <AccountOverview
            channel="Twitter"
            accounts={workspaceAccountStats.twitterAccounts}
            previousPeriod={previousPeriod}
          />
          <AccountOverview
            channel="Instagram"
            accounts={workspaceAccountStats.instagramAccounts}
            previousPeriod={previousPeriod}
          />
          <AccountOverview
            channel="Facebook"
            accounts={workspaceAccountStats.facebookAccounts}
            previousPeriod={previousPeriod}
          />
        </div>
      )}
    </div>
  );
};

const AccountOverview = ({
  channel,
  accounts,
  previousPeriod,
}: {
  channel: Channel & ("Twitter" | "LinkedIn" | "Instagram" | "Facebook");
  accounts: RequireAtLeastOne<
    {
      id: string;
      name: string | null;
      username: string | null;
      profileImageUrl: string;
      stats: {
        label: string;
        value: number | null;
        prevValue: number | null;
      }[];
    },
    "name" | "username"
  >[];
  previousPeriod: string;
}) => {
  const router = useRouter();
  const { currentWorkspace } = useWorkspace();
  const { setAnalyticsAccountID } = useAnalyticsView();
  const channelInfo = CHANNEL_ICON_MAP[channel];
  const channelName = channelInfo.label;
  let href = `/analytics/${channelInfo.slug}`;
  if (channel === "Twitter") {
    href = currentWorkspace?.subscription?.isTwitterAnalyticsEnabled
      ? `/analytics/twitter`
      : "/analytics/twitter-basic";
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <ChannelIcon channel={channel} width={14} />
        <div className="font-body-sm font-medium">{channelName}</div>
        {accounts.length > 0 && (
          <div>
            <Link
              href={href}
              className="text-basic font-body-sm flex items-center gap-1 font-medium"
            >
              See More
              <ArrowRightIcon className="h-4 w-4" />
            </Link>
          </div>
        )}
      </div>
      {accounts.length === 0 ? (
        <div className="font-body-sm text-medium-dark-gray">
          You don't have any {channelName} accounts currently connected to this
          workspace.{" "}
          <TextLink href="/settings/profiles" value="Connect an account" /> in
          order to access.
        </div>
      ) : (
        <div>
          {accounts.map((account) => (
            <AnalyticsHeader
              account={account}
              stats={account.stats.map((stat) => ({
                label: stat.label,
                value: stat.value,
                prevValue: stat.prevValue,
                formatter: (value: number) => {
                  if (value > 0 && value < 1) {
                    return formatPercentage(value, 1);
                  } else {
                    return formatStat(value);
                  }
                },
              }))}
              onClick={() => {
                setAnalyticsAccountID({ channel, accountID: account.id });
                router.push(href);
              }}
              previousPeriod={previousPeriod}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default AccountsOverview;
