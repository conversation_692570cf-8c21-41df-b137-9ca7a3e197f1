import { PlusIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/router";
import { ReactNode } from "react";
import ContentLoader from "react-content-loader";
import Divider from "~/components/Divider";
import OptionsDropdown from "~/components/OptionsDropdown";
import Button from "~/components/ui/Button";
import UserAvatar from "~/components/UserAvatar";
import { useWorkspace } from "~/providers";
import colors from "~/styles/colors";
import { openIntercomChat, removeEmptyElems } from "~/utils";

type Account = {
  id: string;
  name: string | null;
  detail?: string | null;
  profileImageUrl?: string;
};

type Props = {
  isLoadingAccounts: boolean;
  accounts: Account[];
  isDisabled?: boolean;
  hiddenProfilePictures?: boolean;
  connectButton: {
    label?: string;
    onClick: () => void;
    isLoading?: boolean;
    disabled?: boolean;
  };
  modifyAccountButton?: {
    label: string;
    onClick: (account: Account) => void;
    variant: "secondary" | "warning";
    isHidden?: (id: string) => boolean;
    isLoading?: (id: string) => boolean;
    disabled?: boolean;
  };
  reconnectAccountButton?: {
    onClick: (account: Account) => void;
  };
  disconnectAccountButton: {
    onClick: (account: Account) => void;
  };
  connectAnotherButton: {
    label?: string;
    onClick: () => void;
    additionalInstructions?: string | ReactNode;
  };
};

const ConnectedAccounts = ({
  isLoadingAccounts,
  hiddenProfilePictures,
  accounts,
  isDisabled = false,
  connectButton,
  modifyAccountButton,
  reconnectAccountButton,
  disconnectAccountButton,
  connectAnotherButton,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const router = useRouter();

  if (isLoadingAccounts || currentWorkspace == null) {
    return (
      <div className="border-light-gray w-full rounded-2xl border p-6">
        <ContentLoader
          height={95}
          width="100%"
          backgroundColor={colors.lightestGray}
          foregroundColor={colors.lightGray}
        >
          <circle cx="17" cy="18" r="16" />
          <rect x="43" y="1" rx="3" ry="3" width="60" height="14" />
          <rect x="43" y="19" rx="3" ry="3" width="80" height="12" />

          <rect x="0" y="53" rx="0" ry="0" width="100%" height="1" />

          <rect x="4" y="75" rx="3" ry="3" width="60%" height="16" />
        </ContentLoader>
      </div>
    );
  }

  if (isDisabled) {
    return (
      <div className="border-light-gray flex w-fit items-center justify-between gap-4 rounded-2xl border px-6 py-4 md:gap-28">
        <div className="font-body-sm w-full md:w-[340px]">
          This feature is only available on{" "}
          <span className="font-medium">Standard</span> workspace plans and
          above.
        </div>
        <Button
          variant="secondary"
          size="sm"
          onClick={() => {
            router.push("/settings/pricing");
          }}
        >
          See Plans
        </Button>
      </div>
    );
  }

  if (accounts.length === 0) {
    return (
      <Button
        onClick={connectButton.onClick}
        disabled={connectButton.disabled}
        isLoading={connectButton.isLoading}
      >
        {connectButton.label || "Connect"}
      </Button>
    );
  } else {
    return (
      <div>
        <div className="border-light-gray w-full rounded-2xl border p-6">
          <div className="flex flex-col gap-1 space-y-2">
            {accounts.map((account) => {
              return (
                <div
                  key={account.id}
                  className="flex items-center justify-between"
                >
                  <UserAvatar
                    user={{
                      firstName: account.name,
                      detail: account.detail,
                      profileImageUrl: account.profileImageUrl,
                    }}
                    hiddenProfilePicture={hiddenProfilePictures}
                    showFullName={true}
                    showDetail={true}
                  />
                  <div className="flex gap-2">
                    {modifyAccountButton != null &&
                      (modifyAccountButton.isHidden == null ||
                        !modifyAccountButton.isHidden(account.id)) && (
                        <Button
                          variant={modifyAccountButton.variant}
                          size="sm"
                          isLoading={
                            modifyAccountButton.isLoading != null &&
                            modifyAccountButton.isLoading(account.id)
                          }
                          onClick={() => {
                            modifyAccountButton.onClick(account);
                          }}
                        >
                          {modifyAccountButton.label}
                        </Button>
                      )}
                    <OptionsDropdown
                      options={removeEmptyElems([
                        reconnectAccountButton
                          ? {
                              key: "reconnect",
                              icon: "ArrowPathIcon",
                              label: "Reconnect Account",
                              onClick: () => {
                                reconnectAccountButton.onClick(account);
                              },
                            }
                          : null,
                        {
                          key: "disconnect",
                          icon: "BackspaceIcon",
                          label: "Disconnect Account",
                          intent: "danger",
                          onClick: () => {
                            disconnectAccountButton.onClick(account);
                          },
                        },
                      ])}
                    />
                  </div>
                </div>
              );
            })}
          </div>
          {connectAnotherButton != null && (
            <>
              <Divider padding="md" />
              <div className="font-body-sm">
                <button
                  onClick={connectAnotherButton.onClick}
                  className="text-basic hover:bg-basic/10 flex w-fit items-center gap-2 rounded-full px-2 py-1 font-medium transition-colors"
                >
                  <PlusIcon className="h-5 w-5" />
                  {connectAnotherButton.label || "Connect another account"}
                </button>
                {connectAnotherButton.additionalInstructions && (
                  <div className="text-medium-gray mt-1 ml-10">
                    {connectAnotherButton.additionalInstructions}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
        {!disconnectAccountButton && (
          <div className="text-medium-dark-gray font-body-sm mt-2">
            Need to disconnect an account?{" "}
            <Button
              variant="link"
              size="sm"
              onClick={() => {
                openIntercomChat(
                  "I need help disconnecting an account.\n\nAccount name: (Name of the account you want disconnected)\n\nChannel: (Channel the account is on - e.g. LinkedIn)"
                );
              }}
            >
              Send us a message
            </Button>{" "}
            and we can help remove it.
          </div>
        )}
      </div>
    );
  }
};

export default ConnectedAccounts;
