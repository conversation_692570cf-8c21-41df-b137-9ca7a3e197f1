import { slugify } from "inngest";
import { db, slack } from "~/clients";
import FreeConvert from "~/clients/FreeConvert";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { inngest } from "../inngest";

export default inngest.createFunction(
  {
    id: slugify("Convert Codecs"),
    name: "Convert Codecs",
    onFailure: async ({ error, event, step }) => {
      const data = event.data.event.data;
      const { assetID } = data;
      console.log(`Asset:${assetID}: Final Error ${error.message}`);

      const asset = await db.asset.findFirst({
        where: {
          id: assetID,
        },
        select: {
          instagramAssets: {
            select: {
              instagramContent: {
                select: {
                  post: {
                    select: {
                      id: true,
                      title: true,
                      workspace: {
                        select: {
                          name: true,
                          company: {
                            select: { name: true },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (asset) {
        await slack.genericError({
          title: "Codec Conversion Error",
          workspace: asset.instagramAssets[0]?.instagramContent.post.workspace,
          error: error.message,
        });
      }
    },
  },
  { event: "video.convertCodecs" },
  async ({ event, step }) => {
    const { userID, assetID, channel, audioCodec, videoCodec } = event.data;

    const response = await step.run("Trigger FreeConvert Job", async () => {
      console.log(`Asset:${assetID}: Triggering FreeConvert Job`);
      const video = await db.asset.findFirst({
        where: {
          id: assetID,
        },
        select: {
          id: true,
          size: true,
          url: true,
          name: true,
          path: true,
          mimetype: true,
          instagramAssets: {
            select: {
              instagramContent: {
                select: {
                  post: {
                    select: {
                      id: true,
                      workspace: {
                        select: {
                          id: true,
                          companyID: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          threadsAssets: {
            select: {
              threadsContent: {
                select: {
                  post: {
                    select: {
                      id: true,
                      workspace: {
                        select: {
                          id: true,
                          companyID: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          tweetAssets: {
            select: {
              tweetContent: {
                select: {
                  post: {
                    select: {
                      id: true,
                      workspace: {
                        select: {
                          id: true,
                          companyID: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!video) {
        console.error(`Asset:${assetID}: Asset not found`);
        return {
          status: "Skipped",
          comment: `Asset:${assetID} not found`,
        };
      }

      const post =
        channel === "Instagram"
          ? video.instagramAssets[0].instagramContent.post
          : channel === "Threads"
            ? video.threadsAssets[0].threadsContent.post
            : video.tweetAssets[0].tweetContent.post;
      const workspace = post.workspace;

      posthog.capture({
        distinctId: userID,
        event: POSTHOG_EVENTS.video.compressionStarted,
        groups: {
          workspace: workspace.id,
          company: workspace.companyID,
        },
        properties: {
          id: video.id,
          fileName: video.name,
          sizeMB: video.size / 1_024 / 1_024,
          isInvalidCodec: true,
          audioCodec,
          videoCodec,
          channel,
        },
      });

      const freeConvert = new FreeConvert();

      const jobResponse = await freeConvert.createCompressJob({
        video: {
          url: video.url,
          filename: video.name,
          mimetype: video.mimetype,
        },
        sizeCompression: {
          type: "by_percentage",
          percentage: 80,
        },
      });

      if (jobResponse.errors) {
        console.error(
          `Asset:${assetID}: FreeConvert Job Error`,
          jobResponse.errors
        );

        throw new Error(`FreeConvert Job Error: ${jobResponse.errors}`);
      }

      const job = jobResponse.data;

      const freeConvertJob = await db.freeConvertJob.create({
        data: {
          id: job.id,
          fileID: video.id,
          path: video.path,
          postID: post.id,
          status: job.status,
          channel,
          fileName: video.name,
          startedAt: job.startedAt,
          originalSize: video.size,
          createdByID: userID,
        },
      });

      return {
        status: "Success",
        freeConvertJob: freeConvertJob,
      };
    });

    return response;
  }
);
