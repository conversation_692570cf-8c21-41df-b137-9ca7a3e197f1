import { Divider, Loader } from "~/components";
import { GENERATED_POST_DELIMITER } from "~/constants";
import { trpc } from "~/utils";
import GeneratedDiscordPost from "./GeneratedDiscordPost";
import GeneratedLinkedInPost from "./GeneratedLinkedinPost";
import GeneratedSlackPost from "./GeneratedSlackPost";
import GeneratedTweetThread from "./GeneratedTweetThread";

export type AIChannel = "LinkedIn" | "Twitter" | "Discord" | "Slack";

type Props = {
  title: string | null;
  draftID?: string;
  isLoading: boolean;
  numPosts: number;
  channel: AIChannel;
  content?: string | null;
};

const GeneratedContent = ({
  title,
  draftID,
  isLoading,
  numPosts,
  channel,
  content,
}: Props) => {
  const linkedInAccountsQuery =
    trpc.integration.linkedin.getWorkspaceIntegrations.useQuery();
  const linkedInAccounts = linkedInAccountsQuery.data?.integrations ?? [];

  const twitterAccountsQuery =
    trpc.integration.twitter.getConnectedAccounts.useQuery();
  const twitterAccounts = twitterAccountsQuery.data?.accounts ?? [];
  const slackAccountsQuery =
    trpc.integration.slack.getWorkspaceAccounts.useQuery();
  const slackAccounts = slackAccountsQuery.data?.accounts ?? [];
  const discordAccountsQuery = trpc.integration.discord.getWebhooks.useQuery();
  const discordAccounts = discordAccountsQuery.data?.webhooks ?? [];

  if (isLoading && !content) {
    return (
      <div className="flex h-full w-full flex-col items-center justify-center gap-4">
        <h3>Generating Posts....</h3>
        <Loader size="lg" />
      </div>
    );
  } else if (content == null || draftID == null) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <h3 className="text-medium-gray">No results generated yet.</h3>
      </div>
    );
  } else {
    const splitContent = content
      .split(GENERATED_POST_DELIMITER)
      .map((content) => content.trim());
    const cleanedSplitContent =
      splitContent.length > 1
        ? splitContent.filter((content) => content !== "")
        : splitContent;
    const numGeneratedPosts = cleanedSplitContent.length;

    let ContentCotainer = null;
    if (channel === "Twitter") {
      ContentCotainer = (
        <div className="flex h-full flex-col gap-8">
          {cleanedSplitContent.map((content, index) => {
            const loading = isLoading && index === numGeneratedPosts - 1;
            return (
              <GeneratedTweetThread
                key={index}
                title={title}
                draftID={draftID}
                isLoading={loading}
                navigateOnSuccess={numPosts === 1}
                content={content}
                accountOptions={twitterAccounts}
              />
            );
          })}
        </div>
      );
    } else if (channel === "LinkedIn") {
      ContentCotainer = (
        <div className="flex h-full flex-col gap-8">
          {cleanedSplitContent.map((content, index) => {
            const loading = isLoading && index === numGeneratedPosts - 1;
            return (
              <GeneratedLinkedInPost
                key={`${index}-${numGeneratedPosts}`}
                title={title}
                draftID={draftID}
                isLoading={loading}
                navigateOnSuccess={numPosts === 1}
                content={content}
                accountOptions={linkedInAccounts
                  .filter((integration) => integration.type === "Scheduling")
                  .map((integration) => ({
                    id: integration.id,
                    name: integration.account.name,
                    profileImageUrl: integration.account.profileImageUrl,
                  }))}
              />
            );
          })}
        </div>
      );
    } else if (channel === "Discord") {
      ContentCotainer = (
        <GeneratedDiscordPost
          draftID={draftID}
          title={title}
          isLoading={isLoading}
          content={content}
          accountOptions={discordAccounts}
        />
      );
    } else if (channel === "Slack") {
      ContentCotainer = (
        <GeneratedSlackPost
          draftID={draftID}
          title={title}
          isLoading={isLoading}
          content={content}
          accountOptions={slackAccounts}
        />
      );
    }

    return (
      <div className="scrollbar-hidden flex h-full w-full justify-center overflow-auto">
        <div className="mx-auto w-fit">
          <div className="sticky top-0 z-10 pt-4 backdrop-blur-md">
            <h4>Results</h4>
            <Divider padding="md" />
          </div>
          {ContentCotainer}
        </div>
      </div>
    );
  }
};

export default GeneratedContent;
