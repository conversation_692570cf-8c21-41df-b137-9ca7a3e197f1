import classNames from "classnames";
import { DateTime } from "luxon";
import Link from "next/link";
import { toast } from "sonner";
import { sleep, trpc } from "~/utils";

type Props = {
  campaign: {
    id: string;
    name: string;
    startDate: Date;
    endDate: Date;
  };
  hoveredCampaignID: string | null;
  setHoveredCampaignID: (hoveredCampaignID: string | null) => void;
  day: Date;
  campaignOffsetMapping: { [id: string]: number };
  previousOffset: number | null;
};

const Campaign = ({
  campaign,
  day,
  hoveredCampaignID,
  setHoveredCampaignID,
  campaignOffsetMapping,
  previousOffset,
}: Props) => {
  const trpcUtils = trpc.useUtils();

  const dateTimeDay = DateTime.fromJSDate(day);
  const dayOfYear = dateTimeDay.toFormat("o");

  const isStartDate =
    dayOfYear ===
    DateTime.fromJSDate(campaign.startDate, {
      zone: "utc",
    }).toFormat("o");
  const isEndDate =
    dayOfYear ===
    DateTime.fromJSDate(campaign.endDate, {
      zone: "utc",
    }).toFormat("o");
  const isStartOfWeek =
    dateTimeDay.weekday === DateTime.now().startOf("week").weekday;

  if (!(campaign.id in campaignOffsetMapping)) {
    campaignOffsetMapping[campaign.id] =
      previousOffset == null ? 0 : previousOffset + 1;
  }

  const existingOffset = campaignOffsetMapping[campaign.id];

  const isMisaligned =
    previousOffset != null && existingOffset > previousOffset + 1;

  let offset = 0;
  if (isMisaligned) {
    offset = existingOffset - previousOffset - 1;
  }

  return (
    <div
      key={campaign.id}
      onMouseEnter={() => {
        trpcUtils.campaign.get.prefetch({
          id: campaign.id,
        });
        trpcUtils.post.getMany.prefetch({
          campaignID: campaign.id,
        });
        trpcUtils.comment.getForContent.prefetch({
          id: campaign.id,
          type: "campaign",
        });
        trpcUtils.activity.getForContent.prefetch({
          id: campaign.id,
          type: "campaign",
        });
        trpcUtils.subscriber.getForContent.prefetch({
          id: campaign.id,
          type: "campaign",
        });
        trpcUtils.approval.getForContent.prefetch({
          id: campaign.id,
          type: "campaign",
        });
      }}
    >
      <Link
        href={`/campaigns/${campaign.id}`}
        style={{
          marginTop: isMisaligned ? `${offset * 32}px` : 0,
        }}
        onClick={async () => {
          sleep(250);
          toast.dismiss(campaign.id);
        }}
        className={classNames(
          "bg-secondary-light text-secondary font-eyebrow relative flex h-6 items-center border-y pl-2 text-xs whitespace-nowrap transition-colors",
          {
            "ml-2 rounded-l-full border-l": isStartDate,
            "mr-2 rounded-r-full border-r": isEndDate,
            "opacity-40":
              hoveredCampaignID != null && campaign.id !== hoveredCampaignID,
            "border-transparent": hoveredCampaignID === null,
            "border-secondary": hoveredCampaignID === campaign.id,
          }
        )}
        onMouseEnter={() => setHoveredCampaignID(campaign.id)}
        onMouseLeave={() => setHoveredCampaignID(null)}
      >
        {(isStartDate || isStartOfWeek) && (
          <div className="w-full truncate">{campaign.name}</div>
        )}
      </Link>
    </div>
  );
};

export default Campaign;
