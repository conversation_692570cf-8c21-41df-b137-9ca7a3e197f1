import { JSXElementConstructor, ReactElement } from "react";

import classNames from "classnames";

import { InformationCircleIcon } from "@heroicons/react/24/outline";

import { Tooltip } from "./";

type LabelProps = {
  /** The value to be displayed, if no value then just children will be returned */
  value?: string | ReactElement;
  /** The description to be displayed below the label */
  description?: string;
  /** When used with forms `isRequired` will add an asterisk */
  isRequired?: boolean;
  /** When used with forms `isOptional` will add text indicating the field is optional */
  isOptional?: boolean;
  /** Both toggles the presence of the info icon and defines the tooltip text shown */
  tooltipText?: string;
  /** If the label should be uppercase */
  isUppercase?: boolean;
  /** The component that the label wraps */
  children:
    | string
    | number
    | ReactElement<any, string | JSXElementConstructor<any>>
    | ReactElement<any, string | JSXElementConstructor<any>>[];
};

const Label = ({
  value,
  description,
  isRequired = false,
  isOptional = false,
  tooltipText,
  isUppercase = true,
  children,
}: LabelProps) => {
  if (!value) {
    return <>{children}</>;
  }

  return (
    <div>
      <div className="mb-1">
        <div className="flex items-center leading-relaxed">
          <div className="whitespace-nowrap">
            <div
              className={classNames({
                "font-eyebrow": isUppercase,
                "font-body-sm font-medium": !isUppercase,
              })}
            >
              {value}
            </div>
          </div>
          {tooltipText ? (
            <>
              <div className="pl-1"> </div>
              <Tooltip side="top" value={tooltipText}>
                <InformationCircleIcon className="h-3 w-3" />
              </Tooltip>
            </>
          ) : null}
          {isRequired ? (
            <span style={{ marginLeft: 2 }} className="text-danger">
              *
            </span>
          ) : null}
          {isOptional ? (
            <span
              className={classNames("text-medium-dark-gray", {
                "font-eyebrow-sm ml-2": isUppercase,
                "font-body-sm ml-1": !isUppercase,
              })}
            >
              (Optional)
            </span>
          ) : null}
        </div>
        {description ? (
          <div className="font-body-sm text-medium-dark-gray">
            {description}
          </div>
        ) : null}
      </div>
      {children}
    </div>
  );
};

export default Label;
