import classNames from "classnames";
import React, { ReactNode } from "react";

type Props = {
  children: ReactNode;
};

const PostOptionsNewDesign = ({ children }: Props) => {
  let childrenArray = React.Children.toArray(children).sort((a, b) => {
    // @ts-ignore
    const aProps = a.props as PostOptionProps;
    // @ts-ignore
    const bProps = b.props as PostOptionProps;

    if (!aProps.disabled && bProps.disabled) {
      return -1;
    } else if (aProps.disabled && !bProps.disabled) {
      return 1;
    } else {
      return 0;
    }
  });

  return (
    <div className="my-12 flex w-full flex-col gap-4">
      <span className="font-body-sm font-medium">Post Options</span>
      <div className="flex flex-wrap gap-6">
        {childrenArray.map((child, index) => (
          <div key={index} className="flex gap-6">
            {child}
          </div>
        ))}
      </div>
    </div>
  );
};

type PostOptionProps = {
  title: string | ReactNode;
  isRequired?: boolean;
  disabled?: boolean;
  children: ReactNode;
};

const PostOptionNewDesign = ({
  title,
  isRequired = false,
  disabled = false,
  children,
}: PostOptionProps) => {
  return (
    <div className="flex flex-col gap-2">
      <span
        className={classNames("font-eyebrow font-medium transition-colors", {
          "text-medium-dark-gray": !disabled,
          "text-medium-gray": disabled,
        })}
      >
        {title} {isRequired && <span className="text-secondary">*</span>}
      </span>
      {children}
    </div>
  );
};

export { PostOptionNewDesign, PostOptionsNewDesign };
