import * as Icons from "@heroicons/react/24/outline";
import classNames from "classnames";

type Props = {
  icon: keyof typeof Icons;
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  label?: string;
};

const AssetButton = ({ icon, onClick, label }: Props) => {
  const Icon = Icons[icon];

  return (
    <button
      className={classNames(
        "font-body-sm max-h-7 rounded-[2.5px] bg-white/60 p-1.5 text-black outline-hidden backdrop-blur-xs transition-colors hover:bg-white",
        { "flex items-center justify-center gap-1.5": !!label }
      )}
      onClick={onClick}
      type="button"
    >
      <Icon className="h-4 w-4" />
      <span className="hidden whitespace-nowrap sm:block">{label}</span>
    </button>
  );
};

export default AssetButton;
