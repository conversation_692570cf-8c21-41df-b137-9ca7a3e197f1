import { LinkedInAccountType } from "@prisma/client";
import assert from "assert";
import { StatusCodes } from "http-status-codes";
import { db } from "~/clients";
import LinkedIn from "~/clients/linkedin/LinkedIn";
import { getFullName, removeEmptyElems } from "~/utils";

const searchAccountsByVanityName = async ({
  workspaceID,
  accounts,
}: {
  workspaceID: string;
  accounts: {
    vanityName: string;
    type: LinkedInAccountType;
  }[];
}) => {
  const workspaceIntegrations = await db.linkedInIntegration.findMany({
    where: {
      workspaceID,
      account: {
        type: "Organization",
      },
      accessTokenExpiresAt: {
        gt: new Date(),
      },
    },
    orderBy: {
      createdAt: "asc",
    },
    select: {
      accessToken: true,
      account: {
        select: {
          urn: true,
        },
      },
    },
  });

  let workspaceIntegration = workspaceIntegrations[0];

  if (!workspaceIntegration) {
    const assemblyWorkspaceIntegration =
      await db.linkedInIntegration.findFirstOrThrow({
        where: {
          account: {
            urn: "urn:li:organization:********",
          },
        },
        select: {
          accessToken: true,
          account: {
            select: {
              urn: true,
            },
          },
        },
      });
    workspaceIntegration = assemblyWorkspaceIntegration;
  }

  assert(workspaceIntegration, "No workspace integration found");

  const accountUrn = workspaceIntegration.account.urn;

  const linkedIn = new LinkedIn(workspaceIntegration.accessToken);

  const promises = accounts.map(async (account) => {
    if (account.type === "Organization") {
      const { data: organization, status } =
        await linkedIn.getOrganizationByVanityName(account.vanityName);

      if (!organization) {
        if (status === StatusCodes.UNAUTHORIZED) {
          console.log("Unauthorized");
          throw new Error("Unauthorized");
        } else {
          return null;
        }
      }

      return await db.linkedInAccount.upsert({
        where: {
          urn: organization.urn,
        },
        create: {
          linkedInID: organization.id.toString(),
          urn: organization.urn,
          name: organization.name,
          type: "Organization",
          profileImageUrl: organization.profileImageUrl,
          profileImageExpiresAt: organization.profileImageExpiresAt,
          vanityName: organization.vanityName,
        },
        update: {
          type: "Organization",
          name: organization.name,
          profileImageUrl: organization.profileImageUrl,
          profileImageExpiresAt: organization.profileImageExpiresAt,
        },
      });
    } else {
      const { data: user } = await linkedIn.getPersonByVanityName({
        vanityName: account.vanityName,
        organizationUrn: accountUrn,
      });

      if (!user) {
        return null;
      }

      return await await db.linkedInAccount.upsert({
        where: {
          urn: user.urn,
        },
        create: {
          linkedInID: user.id,
          urn: user.urn,
          name: getFullName(user),
          vanityName: user.vanityName,
          profileImageUrl: user.profileImageUrl,
          profileImageExpiresAt: user.profileImageExpiresAt,
          type: "User",
        },
        update: {
          name: getFullName(user),
          profileImageUrl: user.profileImageUrl,
          profileImageExpiresAt: user.profileImageExpiresAt,
        },
      });
    }
  });

  const results = removeEmptyElems(await Promise.all(promises));

  return results;
};

export default searchAccountsByVanityName;
