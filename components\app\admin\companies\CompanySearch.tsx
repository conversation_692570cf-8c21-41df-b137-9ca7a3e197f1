import { ArrowRightIcon } from "@heroicons/react/24/outline";
import Fuse from "fuse.js";
import { useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import { TextInput } from "~/components";
import AnimateChangeInHeight from "~/components/AnimateChangeInHeight";
import SubscriptionBadge from "~/components/app/admin/companies/SubscriptionBadge";
import Card from "~/components/ui/Card";
import Separator from "~/components/ui/Separator";
import { cn, trpc } from "~/utils";

const CompanySearch = () => {
  const router = useRouter();
  const companiesQuery = trpc.admin.company.getAll.useQuery();

  const companies = companiesQuery.data?.companies || [];

  const [search, setSearch] = useState<string>("");
  const [inputFocused, setInputFocused] = useState<boolean>(false);
  const [focusedIndex, setFocusedIndex] = useState<number>(0);

  const fuse = useMemo(() => {
    return new Fuse(companies, {
      includeScore: true,
      threshold: 0.2,
      keys: ["name", "workspaces.name"],
    });
  }, [companies]);

  const filteredCompanies = fuse
    .search(search)
    .map((result) => result.item)
    .slice(0, 7);

  useEffect(() => {
    if (
      filteredCompanies.length > 0 &&
      filteredCompanies.length - 1 < focusedIndex
    ) {
      setFocusedIndex(filteredCompanies.length - 1);
    }
  }, [filteredCompanies]);

  return (
    <div className="flex h-full w-full items-center justify-center overflow-auto px-4">
      <div className="flex w-full max-w-sm flex-col items-center gap-4">
        <TextInput
          autoFocus={true}
          showSearchIcon={true}
          kind="outline"
          value={search}
          onFocus={() => {
            setInputFocused(true);
          }}
          onBlur={() => {
            setInputFocused(false);
          }}
          onEnterPress={() => {
            const focusedCompany = filteredCompanies[focusedIndex];
            if (focusedCompany) {
              router.push(`/admin/settings/companies/${focusedCompany.id}`);
            }
          }}
          onUpPress={() => {
            setFocusedIndex((prev) => Math.max(prev - 1, 0));
          }}
          onDownPress={() => {
            setFocusedIndex((prev) =>
              Math.min(prev + 1, filteredCompanies.length - 1)
            );
          }}
          onChange={(value) => setSearch(value)}
          placeholder="Search for a company or workspace"
        />
        <div className="w-full">
          <AnimateChangeInHeight>
            <div className="space-y-2">
              {filteredCompanies.map((company, index) => {
                const { numSocialProfiles, numUsers, workspaces } = company;

                return (
                  <Card.Root
                    className={cn(
                      "hover:bg-lightest-gray-30 cursor-pointer rounded-lg transition-colors",
                      {
                        "bg-lightest-gray-30":
                          index === focusedIndex && inputFocused,
                      }
                    )}
                    onClick={(e) => {
                      if (e.ctrlKey || e.metaKey) {
                        window.open(
                          `/admin/settings/companies/${company.id}`,
                          "_blank"
                        );
                      } else {
                        router.push(`/admin/settings/companies/${company.id}`);
                      }
                    }}
                  >
                    <Card.Header className="relative p-3 px-4">
                      <Card.Title className="flex items-center gap-2">
                        {company.name}
                        <SubscriptionBadge company={company} />
                      </Card.Title>
                      <Card.Description>
                        <div className="font-body-xs flex h-4 items-center gap-2">
                          {
                            workspaces.filter(
                              (workspace) => workspace.numUsers > 0
                            ).length
                          }{" "}
                          Workspaces <Separator orientation="vertical" />{" "}
                          {numSocialProfiles} Accounts{" "}
                          <Separator orientation="vertical" /> {numUsers} Users
                        </div>
                      </Card.Description>
                      <div
                        className={cn(
                          "absolute top-6 right-4 transition-opacity",
                          {
                            "opacity-0": index != focusedIndex || !inputFocused,
                            "opacity-100":
                              index === focusedIndex && inputFocused,
                          }
                        )}
                      >
                        <ArrowRightIcon className="h-4 w-4" />
                      </div>
                    </Card.Header>
                  </Card.Root>
                );
              })}
            </div>
          </AnimateChangeInHeight>
        </div>
      </div>
    </div>
  );
};

export default CompanySearch;
