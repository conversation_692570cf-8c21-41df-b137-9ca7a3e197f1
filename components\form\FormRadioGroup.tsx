import { Controller, Path } from "react-hook-form";

import Rules from "./rules";

import RadioGroup from "~/components/ui/RadioGroup";
import camelCaseToWords from "~/utils/camelCaseToWords";
import Error from "./Error";

type FormRadioGroupProps<T> = {
  control: any;
  name: Path<T>;
  label?: string | JSX.Element;
  children: React.ReactNode;
  rules?: Rules;
};

const FormRadioGroup = <T,>({
  control,
  name,
  label,
  children,
  rules = {},
}: FormRadioGroupProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <>
            <RadioGroup.Root value={value} onValueChange={onChange}>
              {children}
            </RadioGroup.Root>
            <Error error={error} />
          </>
        );
      }}
    />
  );
};

export default FormRadioGroup;
