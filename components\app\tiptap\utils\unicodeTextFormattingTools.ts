const normalToBoldMapping: { [key: string]: string } = {
  A: "𝗔",
  B: "𝗕",
  C: "𝗖",
  D: "𝗗",
  E: "𝗘",
  F: "𝗙",
  G: "𝗚",
  H: "𝗛",
  I: "𝗜",
  J: "𝗝",
  K: "𝗞",
  L: "𝗟",
  M: "𝗠",
  N: "𝗡",
  O: "𝗢",
  P: "𝗣",
  Q: "𝗤",
  R: "𝗥",
  S: "𝗦",
  T: "𝗧",
  U: "𝗨",
  V: "𝗩",
  W: "𝗪",
  X: "𝗫",
  Y: "𝗬",
  Z: "𝗭",
  a: "𝗮",
  b: "𝗯",
  c: "𝗰",
  d: "𝗱",
  e: "𝗲",
  f: "𝗳",
  g: "𝗴",
  h: "𝗵",
  i: "𝗶",
  j: "𝗷",
  k: "𝗸",
  l: "𝗹",
  m: "𝗺",
  n: "𝗻",
  o: "𝗼",
  p: "𝗽",
  q: "𝗾",
  r: "𝗿",
  s: "𝘀",
  t: "𝘁",
  u: "𝘂",
  v: "𝘃",
  w: "𝘄",
  x: "𝘅",
  y: "𝘆",
  z: "𝘇",
  "0": "𝟬",
  "1": "𝟭",
  "2": "𝟮",
  "3": "𝟯",
  "4": "𝟰",
  "5": "𝟱",
  "6": "𝟲",
  "7": "𝟳",
  "8": "𝟴",
  "9": "𝟵",
};

const boldToNormalMapping: { [key: string]: string } = Object.fromEntries(
  Object.entries(normalToBoldMapping).map(([key, value]) => [value, key])
);

const normalToItalicMapping: { [key: string]: string } = {
  A: "𝘈",
  B: "𝘉",
  C: "𝘊",
  D: "𝘋",
  E: "𝘌",
  F: "𝘍",
  G: "𝘎",
  H: "𝘏",
  I: "𝘐",
  J: "𝘑",
  K: "𝘒",
  L: "𝘓",
  M: "𝘔",
  N: "𝘕",
  O: "𝘖",
  P: "𝘗",
  Q: "𝘘",
  R: "𝘙",
  S: "𝘚",
  T: "𝘛",
  U: "𝘜",
  V: "𝘝",
  W: "𝘞",
  X: "𝘟",
  Y: "𝘠",
  Z: "𝘡",
  a: "𝘢",
  b: "𝘣",
  c: "𝘤",
  d: "𝘥",
  e: "𝘦",
  f: "𝘧",
  g: "𝘨",
  h: "𝘩",
  i: "𝘪",
  j: "𝘫",
  k: "𝘬",
  l: "𝘭",
  m: "𝘮",
  n: "𝘯",
  o: "𝘰",
  p: "𝘱",
  q: "𝘲",
  r: "𝘳",
  s: "𝘴",
  t: "𝘵",
  u: "𝘶",
  v: "𝘷",
  w: "𝘸",
  x: "𝘹",
  y: "𝘺",
  z: "𝘻",
  "0": "𝟘",
  "1": "𝟙",
  "2": "𝟚",
  "3": "𝟛",
  "4": "𝟜",
  "5": "𝟝",
  "6": "𝟞",
  "7": "𝟟",
  "8": "𝟠",
  "9": "𝟡",
};

const italicToNormalMapping: { [key: string]: string } = Object.fromEntries(
  Object.entries(normalToItalicMapping).map(([key, value]) => [value, key])
);

const normalToUnderlineMapping: { [key: string]: string } = {
  a: "a͟",
  b: "b͟",
  c: "c͟",
  d: "d͟",
  e: "e͟",
  f: "f͟",
  g: "g͟",
  h: "h͟",
  i: "i͟",
  j: "j͟",
  k: "k͟",
  l: "l͟",
  m: "m͟",
  n: "n͟",
  o: "o͟",
  p: "p͟",
  q: "q͟",
  r: "r͟",
  s: "s͟",
  t: "t͟",
  u: "u͟",
  v: "v͟",
  w: "w͟",
  x: "x͟",
  y: "y͟",
  z: "z͟",
  "0": "0͟",
  "1": "1͟",
  "2": "2͟",
  "3": "3͟",
  "4": "4͟",
  "5": "5͟",
  "6": "6͟",
  "7": "7͟",
  "8": "8͟",
  "9": "9͟",
};

const underlineToNormalMapping: { [key: string]: string } = Object.fromEntries(
  Object.entries(normalToUnderlineMapping).map(([key, value]) => [value, key])
);

const cleanUnicodeCharacters = (text: string) => {
  return Array.from(text)
    .map(
      (char) =>
        boldToNormalMapping[char] ||
        italicToNormalMapping[char] ||
        underlineToNormalMapping[char] ||
        char
    )
    .join("");
};

const convertToUnicodeBold = (text: string) => {
  const boldedText = Array.from(text)
    .map((char) => {
      const normalChar = cleanUnicodeCharacters(char);
      return normalToBoldMapping[normalChar] || char;
    })
    .join("");

  // Convert any mentions back to normal text
  const finalText = boldedText.replace(/@([^\s]+)/g, (match) => {
    return cleanUnicodeCharacters(match);
  });
  return finalText;
};

const isUnicodeBold = (text: string) => {
  if (text.trim() === "") {
    return false;
  }

  const boldChars = Object.values(normalToBoldMapping);
  const normalChars = Object.keys(normalToBoldMapping);
  return Array.from(text).every((char) => {
    const normalChar = cleanUnicodeCharacters(char);
    if (!normalChars.includes(normalChar)) {
      return true;
    }

    return boldChars.includes(char);
  });
};

const convertToUnicodeItalic = (text: string) => {
  const italicedText = Array.from(text)
    .map((char) => {
      const normalChar = cleanUnicodeCharacters(char);
      return normalToItalicMapping[normalChar] || char;
    })
    .join("");

  // Convert any mentions back to normal text
  const finalText = italicedText.replace(/@([^\s]+)/g, (match) => {
    return cleanUnicodeCharacters(match);
  });
  return finalText;
};

const isUnicodeItalic = (text: string) => {
  if (text.trim() === "") {
    return false;
  }

  const italicChars = Object.values(normalToItalicMapping);
  const normalChars = Object.keys(normalToItalicMapping);
  return Array.from(text).every((char) => {
    const normalChar = cleanUnicodeCharacters(char);
    if (!normalChars.includes(normalChar)) {
      return true;
    }

    return italicChars.includes(char);
  });
};

export {
  cleanUnicodeCharacters,
  convertToUnicodeBold,
  convertToUnicodeItalic,
  isUnicodeBold,
  isUnicodeItalic,
};
