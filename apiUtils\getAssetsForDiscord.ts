import { Asset } from "@prisma/client";
import sortBy from "lodash/sortBy";
import { AssetType } from "~/types/Asset";
import { removeEmptyElems } from "~/utils";
import enhanceAssetType from "./assets/enhanceAssetType";

const getAssetsForDiscord = async (post: {
  discordContent: {
    assets: {
      id: string;
      asset: Asset;
      position: number;
    }[];
  } | null;
}): Promise<(AssetType & { position: number })[]> => {
  if (!post.discordContent) {
    return [];
  }

  return removeEmptyElems(
    sortBy(post.discordContent.assets, "position").map((asset) => ({
      ...enhanceAssetType(asset.asset),
      id: asset.id,
      position: asset.position,
    }))
  );
};

export default getAssetsForDiscord;
