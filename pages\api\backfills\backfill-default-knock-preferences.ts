import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import chunk from "lodash/chunk";
import { db } from "~/clients";
import knock from "~/clients/Knock";
import { defaultKnockPreferences } from "~/constants";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const users = await db.user.findMany({
    where: {
      firstName: {
        not: null,
      },
      id: {
        in: [
          "70d69835-fd96-4266-9721-5ea6149c0159",
          "1563daf0-6ba1-4c85-8c09-81e2872755fa",
          "c5216014-70f1-44da-9749-2df14766601e",
          "f80d328f-aaee-4457-af53-21123a4a8f71",
          "b5cb5edc-12cc-4d3d-a57f-886a4919b87b",
          "76876425-69d6-4bdd-aa6f-d7b7a6730682",
          "de1d0747-b6c6-426f-811c-3f704e5d8a37",
          "f0c559a2-3a0f-4e7a-8a13-800628525add",
          "b9ecde9e-7a3e-4d67-97d6-e85f6d7f3f3c",
        ],
      },
    },
  });

  const chunks = chunk(users, 100);
  for await (const chunk of chunks) {
    await knock.users.bulkSetPreferences(
      chunk.map((user) => user.id),
      {
        workflows: {
          ...defaultKnockPreferences,
        },
      }
    );
  }

  res.status(StatusCodes.OK).send({ chunks });
};

export default handler;
