import { Channel, EngagementType } from "@prisma/client";
import { Block, KnownBlock } from "@slack/web-api";
import countSocialProfiles, {
  WorkspaceSocialProfileUsage,
} from "apiUtils/countSocialProfiles";
import axios from "axios";
import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import colors from "~/styles/colors";
import { CHANNEL_ICON_MAP } from "~/types";
import { getFullName, removeEmptyElems } from "~/utils";

/**
 * Assembly Error Bot: https://api.slack.com/apps/A05G0E47ZSR/incoming-webhooks
 * Marketoooor Bot: https://api.slack.com/apps/A04Q6PQ38J3/incoming-webhooks
 */
const CHANNEL_TO_WEBHOOK = {
  "#scheduled-post-notifs":
    "*********************************************************************************",
  "#scheduled-post-errors":
    "*********************************************************************************",
  "#connected-account-notifs":
    "*********************************************************************************",
  "#invite-notifs":
    "*********************************************************************************",
  "#stripe-notifs":
    "*********************************************************************************",
  "#update-stripe-notifs":
    "*********************************************************************************",
};

class Slack {
  constructor() {}

  _formatDateTime = (date: Date | string) => {
    return DateTime.fromJSDate(new Date(date))
      .setZone("PST")
      .toFormat("MMM d, yyyy @ h:mm a ZZZZ");
  };

  newScheduledPost = async ({
    post,
    scheduledPost,
    channels,
  }: {
    post: {
      id: string;
      title: string;
      channels: Channel[];
      workspace: {
        name: string;
      };
      tweets: {
        copy: string;
        threadPosition: number;
        engagements: {
          type: EngagementType;
          copy: string | null;
          postDelaySeconds: number;
          integration: {
            account: {
              username: string;
            };
          };
        }[];
      }[];
      twitterIntegration: {
        account: {
          username: string;
        };
      } | null;
      instagramContent: {
        copy: string;
        engagements: {
          type: EngagementType;
          copy: string | null;
          postDelaySeconds: number;
          integration: {
            account: {
              username: string;
            };
          };
        }[];
      } | null;
      instagramIntegration: {
        account: {
          username: string;
        };
      } | null;
      linkedInContent: {
        copy: string;
        engagements: {
          type: EngagementType;
          copy: string | null;
          postDelaySeconds: number;
          integration: {
            account: {
              name: string;
            };
          };
        }[];
      } | null;
      linkedInIntegration: {
        account: {
          name: string;
        };
      } | null;
      facebookContent: {
        copy: string;
      } | null;
      facebookIntegration: {
        page: {
          name: string;
        };
      } | null;
      discordContent: {
        copy: string;
      } | null;
      slackContent: {
        copy: string;
      } | null;
      slackIntegration: {
        displayName: string;
      } | null;
      tikTokContent: {
        copy: string;
      } | null;
      tikTokIntegration: {
        account: {
          username: string;
        };
      } | null;
      webflowContent: {
        body: string | null;
      } | null;
      webflowIntegration: {
        siteName: string;
        collectionName: string;
      } | null;
      youTubeShortsContent: {
        title: string | null;
      } | null;
      youTubeIntegration: {
        channel: {
          username: string;
        };
      } | null;
    };
    channels: Channel[];
    scheduledPost: {
      createdBy: {
        firstName: string | null;
        lastName: string | null;
      };
      publishAt: Date | string;
    };
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      const workspace = post.workspace;

      const formattedDate = this._formatDateTime(scheduledPost.publishAt);

      const text = `*<${process.env.NEXT_PUBLIC_DOMAIN}/posts/${post.id}|${post.title}>* | ${formattedDate}`;

      // https://app.slack.com/block-kit-builder/T052JRFTPJL#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22New%20Scheduled%20Post%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Cwww.google.com%7CMarketing%20Tips%3E*%20%7C%20Feb%2023rd,%202023%20@%206:00%20PM%20PST%22%7D%7D,%7B%22type%22:%22context%22,%22elements%22:%5B%7B%22type%22:%22mrkdwn%22,%22text%22:%22Francisco%20Delgado%20from%20Fradel%22%7D%5D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*Twitter%20@francistogram%20-%20:twitter:*%22%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22Examining%20popular%20brands%20and%20those%20that%20we%20love%20can%20teach%20us%20a%20great%20deal%20about%20DTC%20best%20practices.%20In%20our%20latest%20blog%20post,%20we%20compile%20the%20brand%20crushes%20of%2010%20ecommerce%20experts%20featured%20in%20the%20Friends%20of%20Tydo%20directory,%20as%20well%20as%20a%20few%20from%20our%20own%20team%20%F0%9F%91%80%F0%9F%92%8C%5Cn%5Cnhttps://www.tydo.com/blog/february-brand-crushes%22%7D%7D,%7B%22type%22:%22context%22,%22elements%22:%5B%7B%22type%22:%22mrkdwn%22,%22text%22:%22:repeat:%20Retweet%205%20minutes%20%7C%20@francistogram%5Cn:speech_balloon:%20Comment%200%20minutes%20%7C%20@francistogram%22%7D%5D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*LinkedIn%20@francistogram%20-%20:linkedin:*%22%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22Examining%20popular%20brands%20and%20those%20that%20we%20love%20can%20teach%20us%20a%20great%20deal%20about%20DTC%20best%20practices.%20In%20our%20latest%20blog%20post,%20we%20compile%20the%20brand%20crushes%20of%2010%20ecommerce%20experts%20featured%20in%20the%20Friends%20of%20Tydo%20directory,%20as%20well%20as%20a%20few%20from%20our%20own%20team%20%F0%9F%91%80%F0%9F%92%8C%5Cn%5Cnhttps://www.tydo.com/blog/february-brand-crushes%22%7D%7D,%7B%22type%22:%22context%22,%22elements%22:%5B%7B%22type%22:%22mrkdwn%22,%22text%22:%22:repeat:%20Repost%205%20minutes%20-%20@francistogram%5Cn:speech_balloon:%20Comment%200%20minutes%20-%20@francistogram%22%7D%5D%7D%5D%7D
      const blocks: (KnownBlock | Block)[] = [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `New Scheduled Post`,
            emoji: true,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text,
          },
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `${getFullName(scheduledPost.createdBy)} from ${
                workspace.name
              }`,
            },
          ],
        },
      ];

      channels.forEach((channel) => {
        blocks.push({
          type: "divider",
        });

        if (channel === "Twitter") {
          const sortedTweets = sortBy(post.tweets, "threadPosition");
          const copy = sortedTweets.map((tweet) => tweet.copy).join("\n\n");

          const accountName = `@${post.twitterIntegration?.account.username}`;

          blocks.push({
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Twitter ${accountName} :twitter:*`,
            },
          });
          blocks.push({
            type: "section",
            text: {
              type: "mrkdwn",
              text: copy,
            },
          });

          const firstTweet = sortedTweets[0];
          const engagements = firstTweet?.engagements || [];
          if (engagements.length > 0) {
            const engagementTextArray = engagements.map((engagement) => {
              const accountName = `@${engagement.integration.account.username}`;
              const emoji =
                engagement.type === "Repost" ? ":repeat:" : ":speech_balloon:";
              const type =
                engagement.type === "Repost"
                  ? engagement.copy?.length
                    ? "Quote Tweet"
                    : "Retweet"
                  : engagement.type;
              const minutes = Math.round(engagement.postDelaySeconds / 60);

              return `${emoji} ${type} ${minutes} minutes | ${accountName}`;
            });
            const engagementText = engagementTextArray.join("\n");
            blocks.push({
              type: "context",
              elements: [
                {
                  type: "mrkdwn",
                  text: engagementText,
                },
              ],
            });
          }
        } else if (channel === "LinkedIn") {
          const linkedInContent = post.linkedInContent?.copy || "_No copy_";
          const accountName = post.linkedInIntegration?.account.name;

          if (linkedInContent && accountName) {
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*LinkedIn ${accountName} :linkedin:*`,
              },
            });
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: linkedInContent,
              },
            });

            const engagements = post.linkedInContent?.engagements || [];
            if (engagements.length > 0) {
              const engagementTextArray = engagements.map((engagement) => {
                const accountName = engagement.integration.account.name;
                const emoji =
                  engagement.type === "Repost"
                    ? ":repeat:"
                    : engagement.type === "Comment"
                      ? ":speech_balloon:"
                      : ":+1:";
                const type =
                  engagement.type === "Repost"
                    ? engagement.copy?.length
                      ? "Quote Post"
                      : "Repost"
                    : engagement.type;
                const minutes = Math.round(engagement.postDelaySeconds / 60);

                return `${emoji} ${type} ${minutes} minutes | ${accountName}`;
              });
              const engagementText = engagementTextArray.join("\n");
              blocks.push({
                type: "context",
                elements: [
                  {
                    type: "mrkdwn",
                    text: engagementText,
                  },
                ],
              });
            }
          }
        } else if (channel === "Instagram") {
          const instagramContent = post.instagramContent?.copy || "_No copy_";
          const instagramAccountName = `@${post.instagramIntegration?.account.username}`;

          if (instagramAccountName) {
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Instagram ${instagramAccountName} :instagram:*`,
              },
            });
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: instagramContent,
              },
            });

            const engagements = post.instagramContent?.engagements || [];
            if (engagements.length > 0) {
              const engagementTextArray = engagements.map((engagement) => {
                const accountName = `@${engagement.integration.account.username}`;
                const emoji = ":speech_balloon:";
                const minutes = Math.round(engagement.postDelaySeconds / 60);

                return `${emoji} Comment ${minutes} minutes | ${accountName}`;
              });
              const engagementText = engagementTextArray.join("\n");
              blocks.push({
                type: "context",
                elements: [
                  {
                    type: "mrkdwn",
                    text: engagementText,
                  },
                ],
              });
            }
          }
        } else if (channel === "Facebook") {
          const facebookContent = post.facebookContent?.copy || "_No copy_";
          const facebookAccountName = post.facebookIntegration?.page.name;

          if (facebookAccountName) {
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Facebook ${facebookAccountName} :facebook:*`,
              },
            });
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: facebookContent,
              },
            });
          }
        } else if (channel === "YouTubeShorts") {
          const youTubeShortsContent =
            post.youTubeShortsContent?.title || "_No copy_";
          const youTubeAccountName = `@${post.youTubeIntegration?.channel.username}`;

          if (youTubeShortsContent && youTubeAccountName) {
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*YouTube Shorts ${youTubeAccountName} :youtube_shorts:*`,
              },
            });
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: youTubeShortsContent,
              },
            });
          }
        } else if (channel === "TikTok") {
          const tikTokContent = post.tikTokContent?.copy || "_No copy_";
          const tikTokAccountName = `@${post.tikTokIntegration?.account.username}`;

          if (tikTokAccountName) {
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*TikTok ${tikTokAccountName} :tiktok:*`,
              },
            });
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: tikTokContent,
              },
            });
          }
        } else if (channel === "Slack") {
          const slackContent = post.slackContent?.copy || "_No copy_";
          const slackAccountName = post.slackIntegration?.displayName;

          if (slackAccountName) {
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Slack ${slackAccountName} :slack:*`,
              },
            });
            blocks.push({
              type: "section",
              text: {
                type: "mrkdwn",
                text: slackContent,
              },
            });
          }
        } else if (channel === "Discord") {
          const discordContent = post.discordContent?.copy || "_No copy_";

          blocks.push({
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Discord :discord:*`,
            },
          });
          blocks.push({
            type: "section",
            text: {
              type: "mrkdwn",
              text: discordContent,
            },
          });
        } else if (channel === "Webflow") {
          const webflowContent = post.webflowContent?.body || "_No body_";

          blocks.push({
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Webflow ${post.webflowIntegration?.siteName}/${post.webflowIntegration?.collectionName} :webflow:*`,
            },
          });
          blocks.push({
            type: "section",
            text: {
              type: "mrkdwn",
              text: webflowContent,
            },
          });
        }
      });

      await axios.post(CHANNEL_TO_WEBHOOK["#scheduled-post-notifs"], {
        attachments: [
          {
            color: colors.secondary,
            fallback: `New Scheduled Post`,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };

  newSuccessfulPost = async ({
    post,
    url,
    account,
    channel: postChannel,
    webhookUrls,
  }: {
    post: {
      id: string;
      title: string;
      workspace: {
        name: string;
      };
    };
    account?: {
      name: string;
      url?: string;
    };
    channel: Channel;
    url: string | null;
    webhookUrls: string[];
  }) => {
    try {
      // if (process.env.NODE_ENV === "development") {
      //   return;
      // }

      const channel = CHANNEL_ICON_MAP[postChannel];
      const accountName = account ? account.name : post.workspace.name;

      const text = `*<${process.env.NEXT_PUBLIC_DOMAIN}/posts/${post.id}|${post.title}>* | ${accountName}`;

      // https://app.slack.com/block-kit-builder/T9L3SLEQP#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22Succesful%20Twitter%20Post%20:twitter:%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Chttps://app.meetassembly.com/posts/1234%7CNew%20Analytics%20Page%3E*%20%7C%20Affine%22%7D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22actions%22,%22elements%22:%5B%7B%22type%22:%22button%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22View%20on%20Twitter%22%7D,%22url%22:%22https://www.linkedin.com/feed/update/urn:li:activity:704****************%22,%22value%22:%22view_post%22,%22action_id%22:%22button%22%7D%5D%7D%5D%7D
      const blocks = ({
        internal,
      }: {
        internal: boolean;
      }): (KnownBlock | Block)[] => {
        const payload: (KnownBlock | Block)[] = [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: internal
                ? `Successful ${channel.label} Post ${channel.slackIcon}`
                : `New ${channel.label} Post`,
              emoji: true,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text,
            },
          },
        ];

        if (url) {
          payload.push({
            type: "divider",
          });
          payload.push({
            type: "actions",
            elements: [
              {
                type: "button",
                text: {
                  type: "plain_text",
                  text: `View on ${channel.label}`,
                },
                url,
                value: "view_post",
                action_id: "button",
              },
            ],
          });
        }

        return payload;
      };

      const promises = [
        await axios.post(CHANNEL_TO_WEBHOOK["#scheduled-post-notifs"], {
          attachments: [
            {
              color: colors.success,
              fallback: `Successful ${channel.label} Post`,
              blocks: blocks({ internal: true }),
            },
          ],
        }),
      ];

      if (webhookUrls.length > 0) {
        await Promise.all(
          webhookUrls.map(async (webhookUrl) => {
            await axios.post(webhookUrl, {
              attachments: [
                {
                  color: colors.success,
                  fallback: `New ${channel.label} Post`,
                  blocks: blocks({ internal: false }),
                },
              ],
            });
          })
        );
      }

      await Promise.all(promises);
    } catch (err) {
      console.error(err);
    }
  };

  newFailedPost = async ({
    post,
    channel: postChannel,
    account,
    error,
    isAuthError,
  }: {
    post: {
      id: string;
      title: string;
      workspace: {
        name: string;
      };
    };
    channel: Channel;
    account?: {
      name: string;
      url?: string;
    };
    error: string;
    isAuthError: boolean;
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      const channel = CHANNEL_ICON_MAP[postChannel];

      // https://app.slack.com/block-kit-builder/T9L3SLEQP#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22Twitter%20Post%20Error%20:twitter:%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Chttps://app.meetassembly.com/posts/1234%7CNew%20Analytics%20Page%3E*%20%7C%20Affine%22%7D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*Error*:%20Too%20many%20characters%22%7D%7D%5D%7D
      const blocks: KnownBlock[] = [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `${channel.label} Post Error ${channel.slackIcon}`,
            emoji: true,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*<${process.env.NEXT_PUBLIC_DOMAIN}/posts/${post.id}|${post.title}>* | ${post.workspace.name}`,
          },
        },
        {
          type: "divider",
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `${isAuthError ? "" : "@channel \n\n"} *${
              isAuthError ? "Auth Error" : "Error"
            }*: ${error}`,
          },
        },
      ];

      if (account && account.url) {
        blocks.push({
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: `View ${account.name} on ${channel.label}`,
              },
              url: account.url,
              value: "view-account",
              action_id: "view-account",
            },
          ],
        });
      }

      await axios.post(CHANNEL_TO_WEBHOOK["#scheduled-post-errors"], {
        attachments: [
          {
            color: colors.danger,
            fallback: `${channel.label} Post Error`,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };

  uncaughtFailedEngagement = async ({
    channel: postChannel,
    engagementID,
    post,
    postUrl,
    workspace,
    error,
    profile,
    runID,
  }: {
    channel: Channel;
    engagementID: string;
    post: {
      id: string;
      title: string;
    };
    postUrl: string;
    workspace: {
      name: string;
      company: {
        name: string;
      };
    };
    error: string;
    profile: {
      name: string;
      url: string;
    };
    runID: string;
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      const channel = CHANNEL_ICON_MAP[postChannel];

      // https://app.slack.com/block-kit-builder/T9L3SLEQP#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22Twitter%20Post%20Error%20:twitter:%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Chttps://app.meetassembly.com/posts/1234%7CNew%20Analytics%20Page%3E*%20%7C%20Affine%22%7D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*Error*:%20Too%20many%20characters%22%7D%7D%5D%7D
      const blocks: KnownBlock[] = [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `${channel.label} Post Engagement Error ${channel.slackIcon}`,
            emoji: true,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*<${process.env.NEXT_PUBLIC_DOMAIN}/posts/${post.id}|${post.title}>* | ${workspace.name}`,
          },
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `${workspace.company.name}`,
            },
          ],
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `@channel\n\n*EngagementID:* \`${engagementID}\`\n\n *Error*: ${error}`,
          },
        },
        {
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: `View Post on ${channel.label}`,
              },
              url: postUrl,
              value: "view-post",
              action_id: "view-post",
            },
            {
              type: "button",
              text: {
                type: "plain_text",
                text: `View Run on Inngest`,
              },
              url: `https://app.inngest.com/env/production/runs/${runID}`,
              value: "view-run",
              action_id: "view-run",
            },
            {
              type: "button",
              text: {
                type: "plain_text",
                text: `View ${profile.name} on ${channel.label}`,
              },
              url: profile.url,
              value: "view-profile",
              action_id: "view-profile",
            },
          ],
        },
      ];

      await axios.post(CHANNEL_TO_WEBHOOK["#scheduled-post-errors"], {
        attachments: [
          {
            color: colors.danger,
            fallback: `${channel.label} Post Engagement Error`,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };

  genericError = async ({
    title,
    workspace,
    error,
  }: {
    title: string;
    workspace: {
      name: string;
    };
    error: string;
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      // https://app.slack.com/block-kit-builder/T9L3SLEQP#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22Twitter%20Post%20Error%20:twitter:%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Chttps://app.meetassembly.com/posts/1234%7CNew%20Analytics%20Page%3E*%20%7C%20Affine%22%7D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*Error*:%20Too%20many%20characters%22%7D%7D%5D%7D
      const blocks: KnownBlock[] = [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: title,
            emoji: true,
          },
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `${workspace.name}`,
            },
          ],
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `Error: ${error}`,
          },
        },
      ];

      await axios.post(CHANNEL_TO_WEBHOOK["#scheduled-post-errors"], {
        attachments: [
          {
            color: colors.danger,
            fallback: title,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };

  deletedLinkedInPosts = async ({
    posts,
  }: {
    posts: {
      id: string;
      title: string;
      workspace: {
        name: string;
      };
    }[];
  }) => {
    const blocks: KnownBlock[] = [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `Deleted LinkedIn Posts`,
          emoji: true,
        },
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `Unable to query recently posted LinkedIn posts. Check with the owner of the workspace to ensure this was intentional.`,
        },
      },
    ];

    posts.forEach((post) => {
      blocks.push({
        type: "section",
        text: {
          type: "mrkdwn",
          text: `*<${process.env.NEXT_PUBLIC_DOMAIN}/posts/${post.id}|${post.title}>* | ${post.workspace.name}`,
        },
      });
    });

    await axios.post(CHANNEL_TO_WEBHOOK["#scheduled-post-errors"], {
      attachments: [
        {
          color: colors.danger,
          fallback: `${posts.length} Deleted LinkedIn Posts`,
          blocks,
        },
      ],
    });
  };

  potentiallyMispostedLinkedInPosts = async ({
    posts,
  }: {
    posts: {
      id: string;
      title: string;
      workspace: {
        name: string;
      };
      linkedInLink: string | null;
    }[];
  }) => {
    const blocks: KnownBlock[] = [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `Potentially Misposted LinkedIn Posts`,
          emoji: true,
        },
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `Double check the post to make sure it was posted correctly`,
        },
      },
    ];

    posts.forEach((post) => {
      blocks.push({
        type: "section",
        text: {
          type: "mrkdwn",
          text: `*<${process.env.NEXT_PUBLIC_DOMAIN}/posts/${post.id}|${post.title}>* | ${post.workspace.name} | <${post.linkedInLink}|LinkedIn>`,
        },
      });
    });

    await axios.post(CHANNEL_TO_WEBHOOK["#scheduled-post-errors"], {
      attachments: [
        {
          color: colors.danger,
          fallback: `${posts.length} Misposted LinkedIn Posts`,
          blocks,
        },
      ],
    });
  };

  newMissedScheduledPosts = async ({ numPosts }: { numPosts: number }) => {
    const blocks: KnownBlock[] = [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `${numPosts} Missed Scheduled Posts`,
          emoji: true,
        },
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `You have ${numPosts} posts that did not get posted in the last 5 minutes and need to be rescheduled. Check the schedule post page for more info.\n\n@channel`,
        },
      },
      {
        type: "actions",
        elements: [
          {
            type: "button",
            text: {
              type: "plain_text",
              text: `View Scheduled Posts`,
            },
            url: "https://app.meetassembly.com/admin/settings/scheduled-posts",
            value: "view-scheduled-posts",
            action_id: "view-scheduled-posts",
          },
        ],
      },
    ];

    await axios.post(CHANNEL_TO_WEBHOOK["#scheduled-post-errors"], {
      attachments: [
        {
          color: colors.danger,
          fallback: `${numPosts} Missed Scheduled Posts`,
          blocks,
        },
      ],
    });
  };

  failedFileConversion = async ({
    jobID,
    error,
  }: {
    jobID: string;
    error?: string;
  }) => {
    if (process.env.NODE_ENV === "development") {
      return;
    }

    const blocks: KnownBlock[] = [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `Failed to Convert File`,
          emoji: true,
        },
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `${jobID} failed to convert. Check the jobs page for more info.${
            error ? `\n\n> ${error}` : ""
          }\n\n@channel`,
        },
      },
      {
        type: "actions",
        elements: [
          {
            type: "button",
            text: {
              type: "plain_text",
              text: `View Compression Jobs`,
            },
            url: "https://www.freeconvert.com/account/jobs",
            value: "view-jobs",
            action_id: "view-jobs",
          },
        ],
      },
    ];

    await axios.post(CHANNEL_TO_WEBHOOK["#scheduled-post-errors"], {
      attachments: [
        {
          color: colors.danger,
          fallback: `Failed to Convert File`,
          blocks,
        },
      ],
    });
  };

  whitelistingForTwitterAnalyticsRequest = async ({
    user,
    workspace,
  }: {
    user: {
      firstName: string | null;
      lastName: string | null;
      email: string;
    };
    workspace: {
      name: string;
    };
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      const text = `*${getFullName(user)} (${user.email})* | ${workspace.name}`;

      // https://app.slack.com/block-kit-builder/T9L3SLEQP#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22Succesful%20Twitter%20Post%20:twitter:%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Chttps://app.meetassembly.com/posts/1234%7CNew%20Analytics%20Page%3E*%20%7C%20Affine%22%7D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22actions%22,%22elements%22:%5B%7B%22type%22:%22button%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22View%20on%20Twitter%22%7D,%22url%22:%22https://www.linkedin.com/feed/update/urn:li:activity:704****************%22,%22value%22:%22view_post%22,%22action_id%22:%22button%22%7D%5D%7D%5D%7D
      const blocks = removeEmptyElems([
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `Twitter Analytics Request Clicked`,
            emoji: true,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text,
          },
        },
      ]);

      await axios.post(CHANNEL_TO_WEBHOOK["#connected-account-notifs"], {
        attachments: [
          {
            color: colors.success,
            fallback: `Twitter Analytics Request Clicked`,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };

  newConnectedAccount = async ({
    username,
    channel: channelName,
    url,
    workspace,
    connectedBy,
  }: {
    username: string;
    channel: Channel | Channel[];
    url?: string;
    workspace: {
      name: string;
      company: {
        name: string;
      };
    };
    connectedBy: {
      firstName: string | null;
      lastName: string | null;
    };
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      const channelNames = Array.isArray(channelName)
        ? channelName
        : [channelName];

      const channels = channelNames.map((channel) => {
        return CHANNEL_ICON_MAP[channel];
      });
      const channel = channels[0];
      const text = `*${getFullName(connectedBy)}* | ${workspace.name}`;

      // https://app.slack.com/block-kit-builder/T9L3SLEQP#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22Succesful%20Twitter%20Post%20:twitter:%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Chttps://app.meetassembly.com/posts/1234%7CNew%20Analytics%20Page%3E*%20%7C%20Affine%22%7D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22actions%22,%22elements%22:%5B%7B%22type%22:%22button%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22View%20on%20Twitter%22%7D,%22url%22:%22https://www.linkedin.com/feed/update/urn:li:activity:704****************%22,%22value%22:%22view_post%22,%22action_id%22:%22button%22%7D%5D%7D%5D%7D
      const blocks = removeEmptyElems([
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `${username} Connected ${channels
              .map((channel) => channel.slackIcon)
              .join(" ")}`,
            emoji: true,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text,
          },
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `${workspace.company.name}`,
            },
          ],
        },
        url && {
          type: "divider",
        },
        url && {
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: `View ${username} on ${channel.label}`,
              },
              url,
              value: "view_account",
              action_id: "button",
            },
          ],
        },
      ]);

      await axios.post(CHANNEL_TO_WEBHOOK["#connected-account-notifs"], {
        attachments: [
          {
            color: colors.success,
            fallback: `${username} Connected ${channel.label}`,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };

  updateStripeForAgency = async ({
    company,
  }: {
    company: {
      id: string;
      notes: string | null;
      name: string;
    };
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      const blocks = removeEmptyElems([
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `${company.name} - Social Profiles Changed`,
            emoji: true,
          },
        },
        {
          type: "divider",
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*Notes:*\n${company.notes || "_None_"}`,
          },
        },
        {
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: `View on Assembly`,
              },
              url: `https://app.meetassembly.com/admin/settings/companies/${company.id}`,
              value: "view_account",
              action_id: "view_account_button",
            },
          ],
        },
      ]);

      await axios.post(CHANNEL_TO_WEBHOOK["#update-stripe-notifs"], {
        attachments: [
          {
            color: colors.success,
            fallback: `${company.name} Social Profiles Changed`,
            blocks,
          },
        ],
      });
    } catch (error) {
      console.error(error);
    }
  };

  maybeUpdateStripe = async ({
    workspace,
    company,
    action,
  }: {
    workspace: {
      name: string;
    };
    company: {
      id: string;
      notes: string | null;
      name: string;
      workspaces: WorkspaceSocialProfileUsage[];
      stripeCustomerID: string | null;
    };
    action: "Social Profile Added" | "Social Profile Removed";
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      const isPositive = action.toLocaleLowerCase().includes("added");

      const companyName = company.name;

      const text = `${workspace.name} | *${companyName}*`;

      // https://app.slack.com/block-kit-builder/T9L3SLEQP#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22Succesful%20Twitter%20Post%20:twitter:%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Chttps://app.meetassembly.com/posts/1234%7CNew%20Analytics%20Page%3E*%20%7C%20Affine%22%7D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22actions%22,%22elements%22:%5B%7B%22type%22:%22button%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22View%20on%20Twitter%22%7D,%22url%22:%22https://www.linkedin.com/feed/update/urn:li:activity:704****************%22,%22value%22:%22view_post%22,%22action_id%22:%22button%22%7D%5D%7D%5D%7D
      const blocks = removeEmptyElems([
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `${action}`,
            emoji: true,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text,
          },
        },
        {
          type: "divider",
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `Social Profiles: ${countSocialProfiles(company)}`,
            },
          ],
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*Notes:*\n${company.notes || "_None_"}`,
          },
        },
        {
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: `View ${companyName} on Assembly`,
              },
              url: `https://app.meetassembly.com/companies/${company.id}`,
              value: "view_account",
              action_id: "view_account_button",
            },
            {
              type: "button",
              text: {
                type: "plain_text",
                text: `View ${companyName} on Stripe`,
              },
              url: `https://dashboard.stripe.com/customers/${company.stripeCustomerID}`,
              value: "view_stripe",
              action_id: "view_stripe_button",
            },
          ],
        },
      ]);

      await axios.post(CHANNEL_TO_WEBHOOK["#update-stripe-notifs"], {
        attachments: [
          {
            color: isPositive ? colors.success : colors.danger,
            fallback: `${action} | ${companyName}`,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };

  newUserInvited = async ({
    email,
    workspace,
    invitedBy,
  }: {
    email: string;
    workspace: {
      name: string;
    };
    invitedBy: {
      firstName: string | null;
      lastName: string | null;
    };
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      const text = `*${getFullName(invitedBy)}* | ${workspace.name}`;

      // https://app.slack.com/block-kit-builder/T9L3SLEQP#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22Succesful%20Twitter%20Post%20:twitter:%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Chttps://app.meetassembly.com/posts/1234%7CNew%20Analytics%20Page%3E*%20%7C%20Affine%22%7D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22actions%22,%22elements%22:%5B%7B%22type%22:%22button%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22View%20on%20Twitter%22%7D,%22url%22:%22https://www.linkedin.com/feed/update/urn:li:activity:704****************%22,%22value%22:%22view_post%22,%22action_id%22:%22button%22%7D%5D%7D%5D%7D
      const blocks = removeEmptyElems([
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `${email} Invited to Workspace :mailbox_with_mail:`,
            emoji: true,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text,
          },
        },
      ]);

      await axios.post(CHANNEL_TO_WEBHOOK["#invite-notifs"], {
        attachments: [
          {
            color: colors.secondary,
            fallback: `${email} Invited to ${workspace.name}`,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };

  acceptedInvite = async ({
    user,
    workspace,
  }: {
    user: {
      email: string;
    };
    workspace: {
      name: string;
    };
  }) => {
    try {
      if (process.env.NODE_ENV === "development") {
        return;
      }

      const text = `*${user.email}* | ${workspace.name}`;

      // https://app.slack.com/block-kit-builder/T9L3SLEQP#%7B%22blocks%22:%5B%7B%22type%22:%22header%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22Succesful%20Twitter%20Post%20:twitter:%22,%22emoji%22:true%7D%7D,%7B%22type%22:%22section%22,%22text%22:%7B%22type%22:%22mrkdwn%22,%22text%22:%22*%3Chttps://app.meetassembly.com/posts/1234%7CNew%20Analytics%20Page%3E*%20%7C%20Affine%22%7D%7D,%7B%22type%22:%22divider%22%7D,%7B%22type%22:%22actions%22,%22elements%22:%5B%7B%22type%22:%22button%22,%22text%22:%7B%22type%22:%22plain_text%22,%22text%22:%22View%20on%20Twitter%22%7D,%22url%22:%22https://www.linkedin.com/feed/update/urn:li:activity:704****************%22,%22value%22:%22view_post%22,%22action_id%22:%22button%22%7D%5D%7D%5D%7D
      const blocks = removeEmptyElems([
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `${user.email} Accepted Invite :white_check_mark:`,
            emoji: true,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text,
          },
        },
      ]);

      await axios.post(CHANNEL_TO_WEBHOOK["#invite-notifs"], {
        attachments: [
          {
            color: colors.success,
            fallback: `${user.email} Accepted Invite to ${workspace.name}`,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };

  notifyOfNewWorkspace = async ({
    workspace,
    result,
    url,
    userEmail,
  }: {
    workspace: {
      id: string;
      name: string;
      companyID: string;
    };
    url: string | null;
    result: {
      businessType: string;
      description: string;
    } | null;
    userEmail: string;
  }) => {
    try {
      // if (process.env.NODE_ENV === "development") {
      //   return;
      // }

      const blocks: KnownBlock[] = removeEmptyElems([
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `${workspace.name}`,
            emoji: true,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: result
              ? `*Business Type:* ${result.businessType}\n\n*Description:* ${result.description}`
              : "_Unable to scrape website_",
          },
        },
      ]);

      if (url) {
        blocks.push({
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "View Website",
                emoji: true,
              },
              url,
              value: "view-website",
              action_id: "view-website",
            },
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "View Company",
                emoji: true,
              },
              url: `${process.env.NEXT_PUBLIC_DOMAIN}/companies/${workspace.companyID}`,
              value: "view-company",
              action_id: "view-company",
            },
          ],
        });
      } else {
        blocks.push({
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `No website found: ${userEmail}`,
            },
          ],
        });
        blocks.push({
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "View on Assembly",
                emoji: true,
              },
              url: `${process.env.NEXT_PUBLIC_DOMAIN}/companies/${workspace.companyID}`,
              value: "view-company",
              action_id: "view-company",
            },
          ],
        });
      }

      await axios.post(CHANNEL_TO_WEBHOOK["#stripe-notifs"], {
        attachments: [
          {
            color: colors.success,
            fallback: `${workspace.name} - ${result?.businessType || url}`,
            blocks,
          },
        ],
      });
    } catch (err) {
      console.error(err);
    }
  };
}

const slack = new Slack();

export default slack;
