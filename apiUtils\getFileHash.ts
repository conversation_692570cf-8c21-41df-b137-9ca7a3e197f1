import { md5 } from "js-md5";
import getAssetBuffer from "./getAssetBuffer";

const getFileHash = async (bufferOrUrl: Buffer | ArrayBuffer | string) => {
  // If the type is string then it's a file URL
  if (typeof bufferOrUrl === "string") {
    const buffer = await getAssetBuffer(bufferOrUrl);
    return md5(buffer);
  } else {
    const hash = md5(bufferOrUrl);
    return hash;
  }
};

export default getFileHash;
