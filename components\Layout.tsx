import { initIntercomWindow } from "next-intercom";
import { ReactNode, useEffect, useState } from "react";
import Sidebar, { SidebarProvider } from "~/components/ui/Sidebar";

import { useRouter } from "next/router";

import Link from "next/link";
import { Banner } from "~/components";

import { DateTime } from "luxon";
import dynamic from "next/dynamic";
import Image from "next/image";
import { SCAFFOLDING_WORKSPACE_ID } from "~/constants";
import { useMounted } from "~/hooks";
import { useUser, useWorkspace } from "~/providers";
import { getFullName, openTrialExpiredModal, trpc } from "~/utils";
import openPaymentPastDueModal from "~/utils/openPaymentPastDueModal";
import openSubscriptionCancelledModal from "~/utils/openSubscriptionCancelledModal";
import { AgencySettingsSidebar } from "./app/sidebar/AgencySettingsSidebar";
import { MainSidebar } from "./app/sidebar/MainSidebar";
import { SettingsSidebar } from "./app/sidebar/SettingsSidebar";

const CommandPalette = dynamic(() => import("~/components/CommandPalette"), {
  ssr: false,
});

type Props = {
  children: ReactNode;
};

const Layout = ({ children }: Props) => {
  const router = useRouter();
  const { user } = useUser();

  const paymentMethodUpdateUrlQuery =
    trpc.stripe.getPaymentMethodUpdateUrl.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });

  const paymentMethodUpdateUrl =
    paymentMethodUpdateUrlQuery.data?.paymentMethodUrl;

  const [commandPalleteOpen, setCommandPalletteOpen] = useState<boolean>(false);
  const { hasMounted } = useMounted();

  const { pathname } = router;

  const { currentWorkspace } = useWorkspace();

  const trialEndsAt = !!currentWorkspace?.company?.trialEndsAt
    ? DateTime.fromJSDate(new Date(currentWorkspace.company.trialEndsAt))
    : null;

  const hoursRemaining = trialEndsAt
    ? trialEndsAt.diffNow("hours").hours
    : null;
  const daysRemaining = hoursRemaining
    ? Math.max(Math.round(hoursRemaining / 24), 1)
    : null;
  const paymentPastDue =
    currentWorkspace?.company?.subscription?.status === "past_due";

  useEffect(() => {
    if (
      hoursRemaining &&
      hoursRemaining <= 0 &&
      pathname !== "/authed/settings/pricing" &&
      pathname !== "/authed/settings/members" &&
      !pathname.startsWith("/authed/admin")
    ) {
      openTrialExpiredModal();
    }
  }, [pathname, daysRemaining]);

  useEffect(() => {
    if (paymentPastDue && paymentMethodUpdateUrl) {
      openPaymentPastDueModal(paymentMethodUpdateUrl);
    }
  }, [paymentPastDue, paymentMethodUpdateUrl]);

  useEffect(() => {
    const isCanceled =
      currentWorkspace?.company.subscription?.status === "canceled";
    if (
      isCanceled &&
      pathname !== "/authed/settings/pricing" &&
      !pathname.startsWith("/authed/admin")
    ) {
      openSubscriptionCancelledModal();
    }
  }, [pathname, currentWorkspace?.company.subscription]);

  useEffect(() => {
    if (user) {
      initIntercomWindow({
        appId: "dnfttorn",
        email: user.email,
        name: getFullName(user),
        user_id: user.id,
        intercom_user_jwt: user.intercomUserJwt,
        company: {
          id: currentWorkspace?.company.id,
          name: currentWorkspace?.company.name,
          created_at: DateTime.fromJSDate(user.createdAt).toUnixInteger(),
        },
        workspace: {
          id: currentWorkspace?.id,
          name: currentWorkspace?.name,
        },
      });
      window.Intercom?.("update", { z_index: 55 });
    }
  }, [pathname, user]);

  const isSettings =
    pathname.startsWith("/authed/settings") ||
    pathname.startsWith("/authed/admin/settings");
  const isAgencySettings = pathname.startsWith("/authed/agency/settings");

  const noBannerPages = ["/authed/try-desktop"];
  const isBannerHidden = noBannerPages.includes(pathname);

  if (pathname.includes("/authed") && pathname !== "/authed/onboarding") {
    return (
      <SidebarProvider className="h-full w-full">
        <div className="h-full w-full">
          <Banner
            intent="none"
            isHidden={true}
            message={
              <span>
                Twitter / X is experiencing an outage, which may cause scheduled
                posts to error. We're monitoring the situation closely.
              </span>
            }
          />
          <Banner
            intent="none"
            message={
              <span>
                Some of your LinkedIn accounts need to be reconnected.
                <Link href="/settings/profiles">Click here to reconnect.</Link>
              </span>
            }
            isHidden={
              currentWorkspace == null ||
              currentWorkspace.soonestLinkedInIntegrationExpiresAt == null ||
              DateTime.fromJSDate(
                new Date(currentWorkspace.soonestLinkedInIntegrationExpiresAt)
              ) > DateTime.now().plus({ weeks: 2 }).endOf("day")
            }
          />
          <Banner
            intent="none"
            message={
              <span>
                This workspace is used to define the scaffold.{" "}
                <span className="font-medium">
                  Only posts with To Do status and the Tips label will be copied
                  over.
                </span>
              </span>
            }
            isHidden={currentWorkspace?.id !== SCAFFOLDING_WORKSPACE_ID}
          />
          <div className="bg-surface relative flex h-full w-full flex-col text-black md:flex-row">
            <>
              <CommandPalette
                open={commandPalleteOpen}
                setOpen={setCommandPalletteOpen}
              />
              {isSettings ? (
                <SettingsSidebar />
              ) : isAgencySettings ? (
                <AgencySettingsSidebar />
              ) : (
                <>
                  {user != null && (
                    <MainSidebar
                      setCommandPalletteOpen={setCommandPalletteOpen}
                    />
                  )}
                </>
              )}
              {!pathname.includes("/posts-new") && (
                <div className="flex items-center justify-between pt-3 pr-3 pl-5 md:hidden">
                  <Link href="/">
                    <Image
                      src="/logos/assembly_logo_text.svg"
                      width={76}
                      height={16}
                      className="mt-1"
                      alt="Assembly"
                    />
                  </Link>
                  <Sidebar.Trigger />
                </div>
              )}
            </>
            <div className="scrollbar-hidden h-full flex-1">{children}</div>
          </div>
        </div>
      </SidebarProvider>
    );
  } else {
    return (
      <div className="scrollbar-hidden h-full flex-1 overflow-auto">
        {children}
      </div>
    );
  }
};

export default Layout;
