import axios from "axios";
import { StatusCodes } from "http-status-codes";
import { createErrorResponse, Response } from "../Response";

const catchThreadsError = (
  error: unknown,
  defaultErrorMessage: string
): Response<any> => {
  console.error("Threads error", error);
  try {
    if (axios.isAxiosError(error)) {
      const response = error.response;
      console.error(error.response?.data);
      const fbError = response?.data.error;
      let message = fbError.message;
      const rawUserTitle = response?.data.error.error_user_title;
      let userTitle = null;
      if (rawUserTitle) {
        userTitle = response?.data.error.error_user_title.replace(
          "Cannot load user with a private account or invalid username",
          "Cannot tag or collaborate with a user with a private account or invalid username"
        );
        message = response?.data.error.error_user_msg || message;
        if (message === "An unknown error occurred") {
          if (fbError.code === 1) {
            message =
              "Possibly a temporary issue due to downtime. Wait and retry the operation";
          }
        }
      }
      const userErrorMessage = userTitle ? `${userTitle}. ${message}` : message;

      let errorType = null;
      let isTransient = true;
      try {
        errorType = response?.data.error.type;
        isTransient = !!response?.data.error.is_transient;
      } catch (error) {
        console.error("Error getting error type", response?.data);
      }

      const subCode = response?.data.error.error_subcode;
      const isMediaNotFoundError = subCode === 4279009;
      const isAuthError =
        errorType === "OAuthException" && !isTransient && !isMediaNotFoundError;

      return createErrorResponse(
        [userErrorMessage],
        response?.status || StatusCodes.UNPROCESSABLE_ENTITY,
        response?.data,
        isAuthError
      );
    } else {
      return createErrorResponse(
        [defaultErrorMessage],
        StatusCodes.UNPROCESSABLE_ENTITY
      );
    }
  } catch (error) {
    console.error("Error catching facebook error", error);
    return createErrorResponse(
      [defaultErrorMessage],
      StatusCodes.UNPROCESSABLE_ENTITY
    );
  }
};

export default catchThreadsError;
