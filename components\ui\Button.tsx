import { cva } from "class-variance-authority";
import * as React from "react";
import { cn } from "~/utils";
import Loader from "../Loader";

const buttonVariants = cva(
  "relative focus-visible:ring-basic w-fit font-body-sm inline-flex gap-1 items-center justify-center rounded-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-70 whitespace-nowrap",
  {
    variants: {
      variant: {
        default: "bg-basic text-basic-light hover:bg-basic/80",
        secondary: "bg-slate text-basic hover:bg-light-gray",
        danger: "bg-danger text-white hover:bg-danger/90",
        warning: "bg-slate text-danger hover:bg-light-gray",
        ghost: "bg-transparent text-basic hover:bg-basic/10",
        link: "underline-offset-4 hover:underline text-basic p-0 h-auto",
      },
      size: {
        default: "h-10 px-6 py-2 font-eyebrow-lg",
        xs: "h-fit px-1 py-0.5 font-body-xs",
        sm: "h-8 px-2 py-1",
        lg: "h-12 px-8 py-2 font-eyebrow-lg",
        icon: "h-8 w-8 p-0",
      },
    },
    compoundVariants: [
      {
        variant: "link",
        size: "default",
        class: "h-auto px-0 py-0",
      },
      {
        variant: "link",
        size: "sm",
        class: "h-auto px-0 py-0",
      },
      {
        variant: "link",
        size: "lg",
        class: "h-auto px-0 py-0",
      },
      {
        variant: "link",
        size: "icon",
        class: "h-auto w-auto px-0 py-0",
      },
    ],
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, "children"> {
  variant?: "default" | "secondary" | "danger" | "warning" | "ghost" | "link";
  size?: "default" | "xs" | "sm" | "lg" | "icon";
  isLoading?: boolean;
  asChild?: boolean;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = "default",
      size = "default",
      type = "button",
      asChild = false,
      isLoading = false,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? "span" : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size }), className)}
        type={asChild ? undefined : type}
        ref={ref}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading && (
          <span className="absolute inset-0 flex items-center justify-center">
            <Loader
              color={
                variant === "default" || variant === "danger"
                  ? "white"
                  : "basic"
              }
              size={size === "sm" ? "sm" : "md"}
            />
          </span>
        )}
        <span
          className={cn("flex items-center justify-center", {
            "gap-1": size !== "xs",
            "gap-0.5": size === "xs",
            invisible: isLoading,
          })}
        >
          {children}
        </span>
      </Comp>
    );
  }
);
Button.displayName = "Button";

export default Button;
