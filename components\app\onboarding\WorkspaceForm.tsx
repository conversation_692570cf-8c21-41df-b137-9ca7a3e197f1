import { useRouter } from "next/router";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import Cookies from "universal-cookie";
import { Label } from "~/components";
import { FormTextInput } from "~/components/form";
import Button from "~/components/ui/Button";
import { useBreakpoints } from "~/hooks";
import { useWorkspace } from "~/providers";
import { handleTrpcError, trpc } from "~/utils";
import * as OnboardingCard from "./OnboardingCard";

type FormValues = {
  name: string;
};

type Props = {
  onBackClick: () => void;
};

const WorkspaceForm = ({ onBackClick }: Props) => {
  const router = useRouter();
  const { isMobile } = useBreakpoints();
  const { currentWorkspace, refetchWorkspaces } = useWorkspace();
  const { control, handleSubmit } = useForm<FormValues>();

  const createWorkspaceMutation = trpc.workspace.create.useMutation();

  const cookies = new Cookies();
  const referralID = cookies.get("referralID");

  const handleOnboard = ({ name }: FormValues) => {
    import("react-facebook-pixel")
      .then((x) => x.default)
      .then((ReactPixel) => {
        ReactPixel.track("StartTrial");
        createWorkspaceMutation.mutate(
          {
            name,
            referralID,
          },
          {
            onSuccess: () => {
              refetchWorkspaces();
              cookies.remove("referralID");
              if (isMobile) {
                router.push("/try-desktop");
              }
            },
            onError: (error) => {
              handleTrpcError({ title: "Error Saving Workspace", error });
            },
          }
        );
      });
  };

  useEffect(() => {
    if (currentWorkspace && !isMobile) {
      router.push("/");
    }
  }, [currentWorkspace]);

  return (
    <OnboardingCard.Content title="Configure your workspace">
      <form
        onSubmit={handleSubmit(handleOnboard)}
        className="flex w-full flex-col gap-4"
      >
        <Label value="Company Name">
          <FormTextInput
            control={control}
            name="name"
            kind="outline"
            size="2xl"
            placeholder="Enter your company name"
            rules={{ required: true }}
          />
        </Label>
        <Button
          type="submit"
          size="lg"
          className="w-full"
          isLoading={createWorkspaceMutation.isPending}
        >
          Launch Workspace
        </Button>
      </form>
    </OnboardingCard.Content>
  );
};

export default WorkspaceForm;
