import { Icon } from "@liveblocks/react-ui";
import { Editor } from "@tiptap/react";
import {
  convertToUnicodeItalic,
  isUnicodeItalic,
} from "../utils/unicodeTextFormattingTools";
import UnicodeStyleButton from "./UnicodeStyleButton";

type Props = {
  editor: Editor | null;
  useCustomToolbarButton?: boolean;
};

const ItalicsUnicodeButton = ({
  editor,
  useCustomToolbarButton = false,
}: Props) => {
  return (
    <UnicodeStyleButton
      editor={editor}
      name="Italic"
      icon={<Icon.Italic />}
      isStyleActive={isUnicodeItalic}
      convertToStyle={convertToUnicodeItalic}
      shortcutKey="i"
      useCustomToolbarButton={useCustomToolbarButton}
    />
  );
};

export default ItalicsUnicodeButton;
