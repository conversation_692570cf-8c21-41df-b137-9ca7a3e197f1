import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import assert from "assert";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { EDITOR_CHANNELS, ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import {
  usePostIntegrations,
  useThreadComments,
  useWorkspace,
} from "~/providers";
import type { AssetType } from "~/types/Asset";
import {
  cleanFileName,
  handleFilesUpload,
  handleTrpcError,
  trpc,
} from "~/utils";
import createRoomID from "~/utils/liveblocks/createRoomID";
import AssetsNewDesign, { UploadingAsset } from "../files/AssetsNewDesign";
import OtherChannelEditorWrapperNewDesign from "../tiptap/OtherChannelEditorNewDesign";
import EditorActionsBar from "./EditorActionsBar";

type Post = {
  id: string;
  workspaceID: string;
  otherChannelEditorState: JSONContent | null;
  assets: AssetType[];
};

type Props = {
  post: Post;
  isReadOnly?: boolean;
};

const OtherChannelPostNewDesign = ({ post, isReadOnly }: Props) => {
  const randomID = useMemo(() => Math.random().toString(36), []);
  const trpcUtils = trpc.useUtils();
  const { currentWorkspace } = useWorkspace();
  const { currentChannel } = usePostIntegrations();
  const [editorState, setEditorState] = useState<JSONContent | null>(
    post.otherChannelEditorState
  );
  const [assets, setAssets] = useState<AssetType[]>(post.assets);
  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);
  const [isRefetching, setIsRefetching] = useState<boolean>(false);
  const [draggedOver, setDraggedOver] = useState<boolean>(false);
  const [uppy, setUppy] = useState<Uppy | null>(null);

  const updatePostMutation = trpc.post.update.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Error Saving Post", error });
    },
  });

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        restrictions: { maxNumberOfFiles: 4 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", async (file) => {
        assert(currentWorkspace);
        setUploadingAssets((uploadingAssets) => {
          return [
            ...uploadingAssets,
            {
              id: file.id,
              name: file.name!,
              size: file.size!,
              type: file.type,
              uploadProgress: 0,
              width: 0,
              height: 0,
            },
          ];
        });
        const objectName = `${currentWorkspace.id}/${post.id}/${file.name}`;
        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", () => {
        assetQuery.refetch();
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace]);

  const assetQuery = trpc.asset.getAll.useQuery({
    contentID: post.id,
  });

  const assetData = assetQuery.data;
  useEffect(() => {
    if (assetData) {
      if (isRefetching) {
        setIsRefetching(false);
        setAssets(assetData.assets);
      }

      if (
        !isReadOnly &&
        uploadingAssets.length &&
        assetData.assets.length !== assets.length
      ) {
        const stillUploadingAssets = uploadingAssets.filter((asset) => {
          return !assetData.assets.some((a) => a.name === asset.name);
        });
        setUploadingAssets(stillUploadingAssets);
        setAssets(assetData.assets);
      }
    }
  }, [assetData]);

  const handleUploadButtonClick = () => {
    const fileInput = document.getElementById(
      `file-upload-${randomID}`
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  };

  const { setRoomIDs } = useThreadComments();

  const currentRoomID = createRoomID({
    workspaceID: post.workspaceID,
    contentType: "post",
    postID: post.id,
    roomContentID: "other",
  });

  useEffect(() => {
    if (currentChannel && !EDITOR_CHANNELS.includes(currentChannel)) {
      setRoomIDs([currentRoomID]);
    }
  }, [currentRoomID, setRoomIDs, currentChannel]);

  return (
    <div
      className="group mb-12"
      onDragOverCapture={() => {
        setDraggedOver(true);
      }}
      onDragLeaveCapture={() => {
        setDraggedOver(false);
      }}
    >
      <OtherChannelEditorWrapperNewDesign
        initialValue={editorState}
        isReadOnly={isReadOnly}
        onImagePaste={async (event) => {
          handleFilesUpload({
            items: event.clipboardData.items,
            uppy: uppy!,
            meta: {
              contentID: post.id,
            },
            getValidFiles: async (files) => files,
          });
        }}
        onSave={(editorState) => {
          updatePostMutation.mutate({
            id: post.id,
            otherChannelEditorState: editorState,
          });
          const postData = trpcUtils.post.get.getData({
            id: post.id,
          });
          if (postData) {
            trpcUtils.post.get.setData(
              {
                id: post.id,
              },
              {
                post: {
                  ...postData.post,
                  otherChannelEditorState: editorState as {},
                },
                isReadOnly: postData.isReadOnly,
              }
            );
          }
          setEditorState(editorState);
        }}
        placeholder="Add any copy, attachments, links or notes here."
        roomID={currentRoomID}
      />
      <EditorActionsBar
        visible={true}
        isReadOnly={isReadOnly}
        uploadAssetButton={{
          isLoading: uploadingAssets.length > 0,
          acceptedMimetypes: [
            "image/*",
            "video/*",
            "audio/*",
            "application/pdf",
          ],
          acceptMultiple: true,
          hideTooltip: true,
          onUploadFiles: async (files) => {
            files.forEach((file) => {
              uppy!.addFile({
                source: "File Input",
                meta: {
                  contentID: post.id,
                },
                name: cleanFileName(file.name),
                type: file.type,
                data: file,
              });
            });
          },
        }}
      />
      <div className="mt-2">
        <AssetsNewDesign
          assets={assets}
          refetchAssets={() => {
            setIsRefetching(true);
            assetQuery.refetch();
          }}
          uploadingAssets={uploadingAssets}
          onDrop={async (files) => {
            setDraggedOver(false);
            await handleFilesUpload({
              files,
              uppy: uppy!,
              meta: {
                contentID: post.id,
              },
              getValidFiles: async (files) => files,
            });
          }}
          showDropzone={draggedOver}
          isReadOnly={isReadOnly}
          onUploadButtonClick={handleUploadButtonClick}
        />
      </div>
      <input
        disabled={uploadingAssets.length > 0}
        id={`file-upload-${randomID}`}
        type="file"
        multiple={true}
        accept={["image/*", "video/*", "audio/*", "application/pdf"].join(",")}
        className="hidden"
        onChange={async (event) => {
          const files = Array.from(event.target.files || []);
          event.target.value = "";

          if (uppy) {
            await handleFilesUpload({
              files,
              uppy,
              meta: {
                contentID: post.id,
              },
              getValidFiles: async (files) => files,
            });
          }
        }}
      />
    </div>
  );
};

export default OtherChannelPostNewDesign;
