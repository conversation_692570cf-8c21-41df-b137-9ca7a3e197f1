import { TRPCError } from "@trpc/server";
import catchZenstackDeleteError from "apiUtils/catchZenstackDeleteError";
import removeSubscriptionItem from "apiUtils/stripe/removeSubscriptionItem";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const disconnectYouTubeAccount = async ({
  integrationID,
  workspaceID,
  userID,
}: {
  integrationID: string;
  workspaceID: string;
  userID: string;
}) => {
  const youTubeIntegration = await db.youTubeIntegration.findFirst({
    where: {
      id: integrationID,
      workspaceID,
    },
    select: {
      id: true,
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
    },
  });

  if (!youTubeIntegration) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "No integration found",
    });
  } else {
    const workspace = youTubeIntegration.workspace;

    const postsToUpdate = await db.post.findMany({
      where: {
        youTubeIntegrationID: youTubeIntegration.id,
        scheduledPosts: {
          some: {
            status: "Scheduled",
            channel: "YouTubeShorts",
            publishAt: {
              gt: new Date(),
            },
          },
        },
      },
      select: {
        id: true,
      },
    });
    const postIDs = postsToUpdate.map((post) => post.id);

    // Cancel all scheduled posts for just tikTok
    await db.scheduledPost.updateMany({
      where: {
        channel: "YouTubeShorts",
        status: "Scheduled",
        publishAt: {
          gt: new Date(),
        },
        post: {
          id: {
            in: postIDs,
          },
        },
      },
      data: {
        status: "Cancelled",
      },
    });

    const updatedPosts = await db.post.findMany({
      where: {
        id: {
          in: postIDs,
        },
      },
      select: {
        id: true,
        _count: {
          select: {
            scheduledPosts: {
              where: {
                status: "Scheduled",
              },
            },
          },
        },
      },
    });

    const postsWithoutScheduledPosts = updatedPosts.filter(
      (post) => post._count.scheduledPosts === 0
    );
    await db.post.updateMany({
      where: {
        id: {
          in: postsWithoutScheduledPosts.map((post) => post.id),
        },
      },
      data: {
        status: "Finalized",
      },
    });

    const postDefaults = await db.postDefault.findFirst({
      where: {
        workspaceID: workspaceID,
      },
      select: {
        youTubeIntegrationID: true,
        workspace: {
          select: {
            youTubeIntegrations: {
              where: {
                id: {
                  not: youTubeIntegration.id,
                },
              },
              select: {
                id: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    if (postDefaults?.youTubeIntegrationID === youTubeIntegration.id) {
      const oldestyouTubeIntegration =
        postDefaults.workspace.youTubeIntegrations.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
        )[0];

      await db.postDefault.update({
        where: {
          workspaceID: workspaceID,
        },
        data: {
          youTubeIntegrationID:
            oldestyouTubeIntegration != null
              ? oldestyouTubeIntegration.id
              : null,
        },
      });
    }

    await catchZenstackDeleteError(async () => {
      await db.youTubeIntegration.update({
        where: {
          id: youTubeIntegration.id,
        },
        data: {
          deletedTimeStamp: Math.floor(new Date().getTime() / 1000),
          deletedByID: userID,
        },
      });
    });

    await removeSubscriptionItem({
      workspaceID,
      itemName: "ADDITIONAL_SOCIAL_PROFILE",
      quantity: 1,
    });

    posthog.capture({
      distinctId: userID,
      event: POSTHOG_EVENTS.socialAccount.disconnected,
      properties: {
        channel: "YouTube",
      },
      groups: {
        workspace: workspaceID,
        company: workspace.companyID,
      },
    });

    return youTubeIntegration;
  }
};

export default disconnectYouTubeAccount;
