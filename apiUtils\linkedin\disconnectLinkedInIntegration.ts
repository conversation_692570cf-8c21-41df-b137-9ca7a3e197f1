import { TRPCError } from "@trpc/server";
import catchZenstackDeleteError from "apiUtils/catchZenstackDeleteError";
import removeSubscriptionItem from "apiUtils/stripe/removeSubscriptionItem";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const disconnectLinkedInIntegration = async ({
  integrationID,
  workspaceID,
  userID,
}: {
  integrationID: string;
  workspaceID: string;
  userID: string;
}) => {
  const linkedInIntegration = await db.linkedInIntegration.findFirst({
    where: {
      id: integrationID,
      workspaceID,
    },
    select: {
      id: true,
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
      account: {
        select: {
          id: true,
          name: true,
          type: true,
        },
      },
    },
  });

  if (!linkedInIntegration) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "No integration account found",
    });
  }

  const workspace = linkedInIntegration.workspace;

  const postsToUpdate = await db.post.findMany({
    where: {
      linkedInIntegrationID: integrationID,
      scheduledPosts: {
        some: {
          status: "Scheduled",
          channel: "LinkedIn",
          publishAt: {
            gt: new Date(),
          },
        },
      },
    },
    select: {
      id: true,
    },
  });
  const postIDs = postsToUpdate.map((post) => post.id);

  // Delete all engagements for the future scheduled posts
  await db.linkedInPostEngagement.deleteMany({
    where: {
      integrationID: integrationID,
      content: {
        post: {
          id: {
            in: postIDs,
          },
        },
      },
    },
  });

  // Cancel all scheduled posts for just Twitter
  await db.scheduledPost.updateMany({
    where: {
      channel: "LinkedIn",
      status: "Scheduled",
      publishAt: {
        gt: new Date(),
      },
      post: {
        id: {
          in: postIDs,
        },
      },
    },
    data: {
      status: "Cancelled",
    },
  });

  const updatedPosts = await db.post.findMany({
    where: {
      id: {
        in: postIDs,
      },
    },
    select: {
      id: true,
      _count: {
        select: {
          scheduledPosts: {
            where: {
              status: "Scheduled",
            },
          },
        },
      },
    },
  });

  const postsWithoutScheduledPosts = updatedPosts.filter(
    (post) => post._count.scheduledPosts === 0
  );
  await db.post.updateMany({
    where: {
      id: {
        in: postsWithoutScheduledPosts.map((post) => post.id),
      },
    },
    data: {
      status: "Finalized",
    },
  });

  const postDefaults = await db.postDefault.findFirst({
    where: {
      workspaceID: workspaceID,
    },
    select: {
      linkedInIntegrationID: true,
      workspace: {
        select: {
          linkedInIntegrations: {
            where: {
              id: {
                not: integrationID,
              },
            },
            select: {
              id: true,
              createdAt: true,
            },
          },
        },
      },
    },
  });

  if (postDefaults?.linkedInIntegrationID === integrationID) {
    const oldestLinkedInIntegration =
      postDefaults.workspace.linkedInIntegrations.sort(
        (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
      )[0];

    await db.postDefault.update({
      where: {
        workspaceID: workspaceID,
      },
      data: {
        linkedInIntegrationID:
          oldestLinkedInIntegration != null
            ? oldestLinkedInIntegration.id
            : null,
      },
    });
  }

  const deletedTimeStamp = Math.floor(new Date().getTime() / 1000);
  await catchZenstackDeleteError(async () => {
    await db.linkedInIntegration.update({
      where: {
        id: integrationID,
      },
      data: {
        deletedTimeStamp,
        deletedByID: userID,
      },
    });
  });

  await removeSubscriptionItem({
    workspaceID,
    itemName: "ADDITIONAL_SOCIAL_PROFILE",
    quantity: 1,
  });

  posthog.capture({
    distinctId: userID,
    event: POSTHOG_EVENTS.socialAccount.disconnected,
    properties: {
      channel: "LinkedIn",
      accountType: linkedInIntegration.account.type,
    },
    groups: {
      workspace: workspaceID,
      company: workspace.companyID,
    },
  });

  return linkedInIntegration.account;
};

export default disconnectLinkedInIntegration;
