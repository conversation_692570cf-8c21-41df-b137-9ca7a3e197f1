import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

import stripe from "~/clients/stripe";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const subscriptions = await stripe.subscriptions.list({
    status: "canceled",
    limit: 100,
  });

  const updated = await db.subscription.updateMany({
    where: {
      stripeSubscriptionID: {
        in: subscriptions.data.map((sub) => sub.id),
      },
    },
    data: {
      status: "canceled",
    },
  });

  res.status(200).send({
    numUpdated: updated.count,
  });
};

export default handler;
