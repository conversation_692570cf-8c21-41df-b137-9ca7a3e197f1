import { StatusCodes } from "http-status-codes";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import { db } from "~/clients";
import { validateRequestSchema } from "~/middlewares";

const RequestParams = z.object({
  assetID: z.string().uuid(),
});

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { query } = req;
  const { assetID } = RequestParams.parse(query);

  const asset = await db.asset.findUnique({
    where: {
      id: assetID,
    },
    select: {
      path: true,
      url: true,
      urlExpiresAt: true,
    },
    cacheStrategy: {
      ttl: 60 * 60 * 24 * 30, // 30 days
    },
  });

  if (!asset) {
    return res.status(StatusCodes.NOT_FOUND).json({
      message: "Asset not found",
    });
  }

  res.redirect(asset.url);
};

export default validateRequestSchema(handler, { query: RequestParams });
