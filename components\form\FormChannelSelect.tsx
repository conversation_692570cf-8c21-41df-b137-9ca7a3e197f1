import { Controller, Path } from "react-hook-form";

import { Label } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import { Channel } from "@prisma/client";
import { ChannelSelect } from "~/components/app/posts";
import camelCaseToWords from "~/utils/camelCaseToWords";

type FormChannelSelectProps<T> = {
  control: any;
  name: Path<T>;
  label?: string | JSX.Element;
  isReadOnly?: boolean;
  isMulti?: boolean;
  isOptional?: boolean;
  tooltipText?: string;
  ignoredChannels?: Channel[];
  recentlyUsedChannels?: Channel[];
  rules?: Rules;
  placeholder?: string;
};

const FormChannelSelect = <T,>({
  control,
  name,
  label,
  isReadOnly = false,
  isMulti = false,
  isOptional = false,
  tooltipText,
  ignoredChannels,
  recentlyUsedChannels,
  rules = {},
  placeholder,
}: FormChannelSelectProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <Label
            value={label}
            isRequired={!!rules.required}
            isOptional={isOptional}
            tooltipText={tooltipText}
          >
            <>
              <ChannelSelect
                value={value}
                onChange={onChange}
                isReadOnly={isReadOnly}
                ignoredChannels={ignoredChannels}
                recentlyUsedChannels={recentlyUsedChannels}
                isMulti={isMulti}
                placeholder={placeholder}
              />
              <Error error={error} />
            </>
          </Label>
        );
      }}
    />
  );
};

export default FormChannelSelect;
