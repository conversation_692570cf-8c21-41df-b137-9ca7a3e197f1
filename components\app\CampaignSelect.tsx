import { AddNew<PERSON>utton, Badge, Combobox } from "~/components";
import { getDateUTC, trpc } from "~/utils";

import assert from "assert";
import { DateTime } from "luxon";

type Campaign = {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
};

type Props = {
  value: string | null | undefined;
  onChange: (campaign: Campaign | null) => void;
  defaultCampaigns?: Campaign[];
  dropdownPosition?: "left" | "right";
  isClearable?: boolean;
  isReadOnly?: boolean;
  publishDate?: Date | null;
};

const CampaignSelect = ({
  value,
  onChange,
  defaultCampaigns = [],
  dropdownPosition,
  isClearable = true,
  isReadOnly = false,
  publishDate,
}: Props) => {
  const campaignsQuery = trpc.campaign.getAll.useQuery(undefined, {
    refetchInterval: false,
    refetchOnWindowFocus: false,
  });

  const campaigns = campaignsQuery.data?.campaigns || defaultCampaigns;
  const sortedCampigns = [...campaigns].sort((a, b) => {
    const now = publishDate ? getDateUTC(publishDate) : new Date();
    const aStart = getDateUTC(a.startDate);
    const aEnd = getDateUTC(a.endDate);
    const bStart = getDateUTC(b.startDate);
    const bEnd = getDateUTC(b.endDate);

    // Helper to check if a campaign is current (overlaps with today)
    const isCurrent = (start: Date, end: Date) => start <= now && end >= now;

    // Helper to check if a campaign is in the future
    const isFuture = (start: Date) => start > now;

    // Helper to check if a campaign is in the past
    const isPast = (end: Date) => end < now;

    const aIsCurrent = isCurrent(aStart, aEnd);
    const bIsCurrent = isCurrent(bStart, bEnd);
    const aIsFuture = isFuture(aStart);
    const bIsFuture = isFuture(bStart);
    const aIsPast = isPast(aEnd);
    const bIsPast = isPast(bEnd);

    // Current campaigns first
    if (aIsCurrent && !bIsCurrent) return -1;
    if (!aIsCurrent && bIsCurrent) return 1;

    // If both are current, prioritize the one with the closer start date to now
    if (aIsCurrent && bIsCurrent) {
      const aStartDiff = Math.abs(now.getTime() - aStart.getTime());
      const bStartDiff = Math.abs(now.getTime() - bStart.getTime());
      return aStartDiff - bStartDiff;
    }

    // Future campaigns next, sorted by start date (soonest first)
    if (aIsFuture && !bIsFuture) return -1;
    if (!aIsFuture && bIsFuture) return 1;
    if (aIsFuture && bIsFuture) return aStart.getTime() - bStart.getTime();

    // Past campaigns last, sorted by end date (most recent first)
    if (aIsPast && bIsPast) return bEnd.getTime() - aEnd.getTime();

    // Default sort by start date
    return aStart.getTime() - bStart.getTime();
  });

  return (
    <Combobox
      placeholder={
        <AddNewButton
          label={isReadOnly ? "No Campaign" : "Campaign"}
          isReadOnly={isReadOnly}
          isUppercase={true}
        />
      }
      value={value || null}
      dropdownPosition={dropdownPosition}
      onChange={(campaignID) => {
        if (!campaignID) {
          onChange(null);
        } else {
          const campaign = campaigns.find(
            (campaign) => campaign.id === campaignID
          );
          assert(campaign);
          onChange(campaign);
        }
      }}
      disabled={isReadOnly}
      isClearable={isClearable}
      isUppercase={true}
      options={sortedCampigns.map((campaign) => ({
        value: campaign.id,
        label: (
          <span className="flex items-center gap-2">
            {campaign.name}
            <span className="text-medium-dark-gray">
              {DateTime.fromJSDate(getDateUTC(campaign.startDate)).toFormat(
                "M/d"
              )}{" "}
              -{" "}
              {DateTime.fromJSDate(getDateUTC(campaign.endDate)).toFormat(
                "M/d"
              )}
            </span>
          </span>
        ),
        component: (
          <Badge
            intent="secondary"
            label={campaign.name}
            showHoverState={!isReadOnly}
            isUppercase={true}
            truncateLabel={true}
          />
        ),
      }))}
    />
  );
};

export default CampaignSelect;
