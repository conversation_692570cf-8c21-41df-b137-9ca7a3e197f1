import { WebflowFieldType } from "@prisma/client";
import getAssetBuffer from "apiUtils/getAssetBuffer";
import getFileHash from "apiUtils/getFileHash";
import axios, { AxiosInstance } from "axios";
import FormData from "form-data";
import { StatusCodes } from "http-status-codes";
import { removeEmptyElems } from "~/utils";
import {
  createErrorResponse,
  createSuccessResponse,
  Response,
} from "./Response";

export type WebflowFieldValidation =
  | {
      type: WebflowFieldType;
      validations: null;
    }
  | {
      type: "Option";
      validations: {
        options: {
          id: string;
          name: string;
        }[];
      };
    }
  | {
      type: "Reference";
      validations: {
        collectionId: string;
        options: {
          id: string;
          name: string;
        }[];
      };
    }
  | {
      type: "MultiReference";
      validations: {
        collectionId: string;
        options: {
          id: string;
          name: string;
        }[];
      };
    }
  | {
      type: "Number";
      validations: {
        format?: "integer" | "decimal";
        precision: number;
        allowNegative: boolean;
        minValue?: number;
        maxValue?: number;
      } | null;
    }
  | {
      type: "PlainText";
      validations: {
        singleLine: boolean;
        minLength?: number;
        maxLength?: number;
      } | null;
    }
  | {
      type: "Image";
      validations: {
        minImageSize?: number;
        maxImageWidth?: number;
        minImageHeight?: number;
        maxImageHeight?: number;
        minImageWidth?: number;
        maxImageSize?: number;
      } | null;
    }
  | {
      type: "DateTime";
      validations: {
        format: "date-time";
      } | null;
    };

type WebflowField = {
  id: string;
  displayName: string;
  helpText: string | null;
  isRequired: boolean;
  isEditable: boolean;
  slug: string;
} & WebflowFieldValidation;

class Webflow {
  BASE_URL = "https://api.webflow.com/v2";

  client: AxiosInstance;

  constructor(accessToken: string) {
    this.client = axios.create({
      baseURL: this.BASE_URL,
      headers: {
        authorization: `Bearer ${accessToken}`,
      },
    });
  }

  // https://docs.developers.webflow.com/data/reference/authorization#step-3-get-access-token-and-make-a-request
  static authorize = async (
    code: string
  ): Promise<
    Response<{
      accessToken: string;
      scope: string[];
    }>
  > => {
    try {
      const { data, status } = await axios.post(
        "https://api.webflow.com/oauth/access_token",
        {
          client_id: process.env.NEXT_PUBLIC_WEBFLOW_CLIENT_ID,
          client_secret: process.env.WEBFLOW_CLIENT_SECRET,
          grant_type: "authorization_code",
          code,
          redirect_uri: `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/webflow/callback`,
        }
      );

      return createSuccessResponse(
        {
          accessToken: data.access_token,
          scope: data.scope.split(" "),
        },
        status
      );
    } catch (error) {
      return catchWebflowError("Error Authorizing Webflow", error);
    }
  };

  // https://docs.developers.webflow.com/data/reference/list-sites
  listSites = async (): Promise<
    Response<
      {
        id: string;
        workspaceID: string;
        displayName: string;
        shortName: string;
        previewUrl: string | null;
        customDomains: {
          id: string;
          url: string;
        }[];
      }[]
    >
  > => {
    try {
      const { data, status } = await this.client.get("/sites");

      const sites = data.sites as {
        id: string;
        workspaceId: string;
        createdOn: string;
        displayName: string;
        shortName: string;
        lastPublished: string;
        previewUrl: string;
        timeZone: string;
        customDomains: {
          id: string;
          url: string;
        }[];
      }[];

      return createSuccessResponse(
        sites.map((site) => ({
          id: site.id,
          workspaceID: site.workspaceId,
          displayName: site.displayName,
          shortName: site.shortName,
          previewUrl: site.previewUrl,
          customDomains: site.customDomains,
        })),
        status
      );
    } catch (error) {
      return catchWebflowError("Error Listing Webflow Sites", error);
    }
  };

  // https://docs.developers.webflow.com/data/reference/list-pages
  listPages = async (
    siteID: string
  ): Promise<
    Response<
      {
        id: string;
        siteID: string;
        title: string;
        slug: string;
        collectionID: string | null;
        isArchived: boolean;
        isDraft: boolean;
        canBranch: boolean;
        isMembersOnly: boolean;
        seo: {
          title: string | null;
          description: string | null;
        };
        openGraph: {
          title: string | null;
          titleCopied: boolean;
          description: string | null;
          descriptionCopied: boolean;
        };
        createdAt: Date;
        updatedAt: Date;
      }[]
    >
  > => {
    try {
      const { data, status } = await this.client.get(`/sites/${siteID}/pages`, {
        params: {
          limit: 100,
        },
      });

      const pages = data.pages as {
        id: string;
        siteId: string;
        title: string;
        slug: string;
        collectionId: string | null;
        createdOn: string;
        lastUpdated: string;
        archived: boolean;
        draft: boolean;
        canBranch: boolean;
        isMembersOnly: boolean;
        seo: {
          title: string | null;
          description: string | null;
        };
        openGraph: {
          title: string | null;
          titleCopied: boolean;
          description: string | null;
          descriptionCopied: boolean;
        };
      }[];

      return createSuccessResponse(
        pages.map((page) => ({
          id: page.id,
          siteID: page.siteId,
          title: page.title,
          slug: page.slug,
          collectionID: page.collectionId,
          createdAt: new Date(page.createdOn),
          updatedAt: new Date(page.lastUpdated),
          isArchived: page.archived,
          isDraft: page.draft,
          canBranch: page.canBranch,
          isMembersOnly: page.isMembersOnly,
          seo: page.seo,
          openGraph: page.openGraph,
        })),
        status
      );
    } catch (error) {
      return catchWebflowError("Error Listing Webflow Pages", error);
    }
  };

  // https://developers.webflow.com/data/reference/cms/collections/list
  listCollections = async (
    siteID: string
  ): Promise<
    Response<
      {
        id: string;
        displayName: string;
        singularName: string;
        slug: string;
        createdAt: Date;
        updatedAt: Date;
      }[]
    >
  > => {
    try {
      const { data, status } = await this.client.get(
        `/sites/${siteID}/collections`
      );

      const collections = data.collections as {
        id: string;
        displayName: string;
        singularName: string;
        slug: string;
        createdOn: string;
        lastUpdated: string;
      }[];

      return createSuccessResponse(
        collections.map((collection) => ({
          id: collection.id,
          displayName: collection.displayName,
          singularName: collection.singularName,
          slug: collection.slug,
          createdAt: new Date(collection.createdOn),
          updatedAt: new Date(collection.lastUpdated),
        })),
        status
      );
    } catch (error) {
      return catchWebflowError("Error Listing Webflow Collections", error);
    }
  };

  // https://docs.developers.webflow.com/data/reference/create-asset-folder
  createAssetFolder = async ({
    siteID,
    folderName,
    parentFolderID,
  }: {
    siteID: string;
    folderName: string;
    parentFolderID?: string | null;
  }): Promise<
    Response<{
      id: string;
      name: string;
      siteID: string;
      parentFolderID: string | null;
      createdAt: Date;
      updatedAt: Date;
    }>
  > => {
    try {
      const { status, data } = await this.client.post(
        `/sites/${siteID}/asset_folders`,
        {
          displayName: folderName,
          parentFolder: parentFolderID || null,
        }
      );

      const folder = data as {
        id: string;
        displayName: string;
        parent: string | null;
        assets: string[];
        siteId: string;
        createdOn: string;
        lastUpdated: string;
      };

      return createSuccessResponse(
        {
          id: folder.id,
          name: folder.displayName,
          siteID: folder.siteId,
          parentFolderID: folder.parent,
          createdAt: new Date(folder.createdOn),
          updatedAt: new Date(folder.lastUpdated),
        },
        status
      );
    } catch (error) {
      return catchWebflowError("Error Creating Asset Folder", error);
    }
  };

  // https://docs.developers.webflow.com/data/reference/list-asset-folders
  listAssetFolders = async (
    siteID: string
  ): Promise<
    Response<
      {
        id: string;
        name: string;
        siteID: string;
        parentFolderID: string | null;
        createdAt: Date;
        updatedAt: Date;
      }[]
    >
  > => {
    try {
      const { data, status } = await this.client.get(
        `/sites/${siteID}/asset_folders`
      );

      const folders = data.assetFolders as {
        id: string;
        displayName: string;
        parentFolder: string | null;
        assets: string[];
        siteId: string;
        createdOn: string;
        lastUpdated: string;
      }[];

      return createSuccessResponse(
        folders.map((folder) => ({
          id: folder.id,
          name: folder.displayName,
          siteID: folder.siteId,
          parentFolderID: folder.parentFolder,
          createdAt: new Date(folder.createdOn),
          updatedAt: new Date(folder.lastUpdated),
        })),
        status
      );
    } catch (error) {
      return catchWebflowError("Error Listing Asset Folders", error);
    }
  };

  // https://docs.developers.webflow.com/data/reference/create-asset
  uploadAsset = async ({
    siteID,
    file,
    parentFolderID,
  }: {
    siteID: string;
    file: {
      signedUrl: string;
      name: string;
    };
    parentFolderID?: string;
  }): Promise<
    Response<{
      id: string;
      url: string;
    }>
  > => {
    try {
      const assetBuffer = await getAssetBuffer(file.signedUrl);

      const fileHash = await getFileHash(assetBuffer);

      const { status, data } = await this.client.post(
        `/sites/${siteID}/assets`,
        {
          fileName: file.name,
          fileHash,
          parentFolder: parentFolderID,
        }
      );

      const assetMetadata = data as {
        id: string;
        uploadUrl: string;
        hostedUrl: string;
        uploadDetals: { [key: string]: string };
      };

      const form = new FormData();

      for (const [key, value] of Object.entries(data.uploadDetails)) {
        form.append(key, value);
      }
      form.append("file", assetBuffer, file.name);

      const uploadUrl = data.uploadUrl as string;

      await axios.post(uploadUrl, form, {
        headers: form.getHeaders(),
      });

      return createSuccessResponse(
        { id: assetMetadata.id, url: assetMetadata.hostedUrl },
        status
      );
    } catch (error) {
      return catchWebflowError("Error Creating Asset Metadata", error);
    }
  };

  // https://developers.webflow.com/data/reference/sites/get
  getSite = async (
    siteID: string
  ): Promise<
    Response<{
      id: string;
      workspaceID: string;
      displayName: string;
      shortName: string;
      previewUrl: string | null;
      cmsLocaleID: string | null;
      customDomains: {
        id: string;
        url: string;
      }[];
    }>
  > => {
    try {
      const { data, status } = await this.client.get(`/sites/${siteID}`);

      const site = data as {
        id: string;
        workspaceId: string;
        createdOn: string;
        displayName: string;
        shortName: string;
        lastUpdated: string;
        lastPublished: string;
        previewUrl: string;
        timeZone: string;
        customDomains: {
          id: string;
          url: string;
        }[];
        locales: {
          primary: {
            id: string;
            cmsLocaleId: string;
            displayName: string;
          };
        } | null;
      };

      return createSuccessResponse(
        {
          id: site.id,
          workspaceID: site.workspaceId,
          displayName: site.displayName,
          shortName: site.shortName,
          previewUrl: site.previewUrl,
          customDomains: site.customDomains,
          cmsLocaleID: site.locales?.primary.cmsLocaleId || null,
        },
        status
      );
    } catch (error) {
      return catchWebflowError("Error Getting Webflow Site", error);
    }
  };

  // https://developers.webflow.com/data/reference/cms/collections/get
  getCollectionDetails = async (
    collectionID: string
  ): Promise<
    Response<{
      id: string;
      displayName: string;
      singularName: string;
      slug: string;
      createdAt: Date;
      updatedAt: Date;
      fields: WebflowField[];
    }>
  > => {
    try {
      const { data, status } = await this.client.get(
        `/collections/${collectionID}`
      );

      const collection = data as {
        id: string;
        displayName: string;
        singularName: string;
        slug: string;
        createdOn: string;
        lastUpdated: string;
        fields: WebflowField[];
      };

      const collectionIDs = removeEmptyElems(
        collection.fields.map((field) =>
          field.type === "Reference" || field.type === "MultiReference"
            ? field.validations?.collectionId
            : null
        )
      );

      const collectionItemsPromises = collectionIDs.map(
        async (collectionID) => await this.listCollectionItems(collectionID)
      );

      const collectionItemResponses = await Promise.all(
        collectionItemsPromises
      );
      if (collectionItemResponses.some((collection) => collection.errors)) {
        return createErrorResponse(
          removeEmptyElems(
            collectionItemResponses
              .filter((collection) => !!collection.errors)
              .map((collection) => collection.errors?.join(","))
          ),
          StatusCodes.UNPROCESSABLE_ENTITY
        );
      }
      const collectionItems = removeEmptyElems(
        collectionItemResponses.map((collection) => collection.data)
      );
      const collectionItemsMap: { [id: string]: (typeof collectionItems)[0] } =
        {};
      collectionItems.forEach((collection, index) => {
        collectionItemsMap[collectionIDs[index]] = collection;
      });

      const fields = collection.fields.map((field) => {
        if (field.type === "Reference" || field.type === "MultiReference") {
          const collectionItems =
            collectionItemsMap[field.validations!.collectionId];

          return {
            ...field,
            type: field.type,
            validations: {
              collectionId: field.validations!.collectionId,
              options: collectionItems.map((item) => ({
                id: item.id,
                name: item.name,
              })),
            },
          };
        }

        return field;
      });

      return createSuccessResponse(
        {
          id: collection.id,
          displayName: collection.displayName,
          singularName: collection.singularName,
          slug: collection.slug,
          createdAt: new Date(collection.createdOn),
          updatedAt: new Date(collection.lastUpdated),
          fields: fields,
        },
        status
      );
    } catch (error) {
      return catchWebflowError("Error Getting Collection Details", error);
    }
  };

  // https://developers.webflow.com/data/reference/cms/collection-items/live-items/list-items-live
  listCollectionItems = async (
    collectionID: string,
    params: {
      limit: number;
      offset: number;
    } = {
      limit: 100,
      offset: 0,
    }
  ): Promise<
    Response<
      {
        id: string;
        name: string;
        cmsLocaleID: string;
        createdAt: Date;
        updatedAt: Date;
        publishedAt: Date | null;
        isArchived: boolean;
        isDraft: boolean;
        fieldData: {
          [key: string]: string | boolean | null | object | number;
        } & {
          name: string;
        };
      }[]
    >
  > => {
    try {
      const { data, status } = await this.client.get(
        `/collections/${collectionID}/items`,
        {
          params,
        }
      );

      const rawItems = data.items as {
        id: string;
        cmsLocaleId: string;
        lastPublished: string | null;
        lastUpdated: string;
        createdOn: string;
        isArchived: boolean;
        isDraft: boolean;
        fieldData: {
          [key: string]: string | boolean | null | object | number;
        } & {
          name: string;
        };
      }[];

      const items = rawItems.map((item) => ({
        id: item.id,
        cmsLocaleID: item.cmsLocaleId,
        name: item.fieldData.name,
        createdAt: new Date(item.createdOn),
        updatedAt: new Date(item.lastUpdated),
        publishedAt: item.lastPublished ? new Date(item.lastPublished) : null,
        isArchived: item.isArchived,
        isDraft: item.isDraft,
        fieldData: item.fieldData,
      }));

      const pagination = data.pagination as {
        total: number;
        offset: number;
        limit: number;
      };

      const hasMore = pagination.offset + params.limit < pagination.total;

      if (hasMore) {
        const nextOffset = params.offset + params.limit;
        const nextResponse = await this.listCollectionItems(collectionID, {
          limit: params.limit,
          offset: nextOffset,
        });

        if (nextResponse.success) {
          items.push(...nextResponse.data);
        } else {
          console.error("Error Listing Collection Items", nextResponse.errors);
        }
      }

      return createSuccessResponse(items, status);
    } catch (error) {
      return catchWebflowError("Error Listing Collection Items", error);
    }
  };

  // https://developers.webflow.com/data/reference/cms/collection-items/live-items/create-item-live
  createCollectionItem = async ({
    collectionID,
    cmsLocaleID,
    item,
    isDraft,
  }: {
    collectionID: string;
    cmsLocaleID?: string | null;
    item: {
      name: string;
      data: {
        [key: string]:
          | string
          | string[]
          | { url: string; alt?: string }[]
          | null;
      };
    };
    isDraft?: boolean;
  }): Promise<
    Response<{
      id: string;
      name: string;
      cmsLocaleID: string;
      createdAt: Date;
      updatedAt: Date;
      publishedAt: Date | null;
      isArchived: boolean;
      isDraft: boolean;
      fieldData: {
        [key: string]: string;
      };
    }>
  > => {
    try {
      const { status, data } = await this.client.post(
        `/collections/${collectionID}/items/live`,
        {
          cmsLocaleID,
          isArchived: false,
          isDraft: !!isDraft,
          fieldData: {
            name: item.name,
            ...item.data,
          },
        }
      );

      const newItem = data as {
        id: string;
        cmsLocaleId: string;
        lastPublished: string | null;
        lastUpdated: string;
        createdOn: string;
        isArchived: boolean;
        isDraft: boolean;
        fieldData: {
          [key: string]: string;
        };
      };

      return createSuccessResponse(
        {
          id: newItem.id,
          name: newItem.fieldData.name,
          cmsLocaleID: newItem.cmsLocaleId,
          createdAt: new Date(newItem.createdOn),
          updatedAt: new Date(newItem.lastUpdated),
          publishedAt: newItem.lastPublished
            ? new Date(newItem.lastPublished)
            : null,
          isArchived: newItem.isArchived,
          isDraft: newItem.isDraft,
          fieldData: newItem.fieldData,
        },
        status
      );
    } catch (error) {
      return catchWebflowError("Error Creating Collection Item", error);
    }
  };

  // https://developers.webflow.com/data/reference/cms/collection-items/live-items/delete-item-live
  deleteCollectionItem = async ({
    collectionID,
    itemID,
  }: {
    collectionID: string;
    itemID: string;
  }): Promise<Response<null>> => {
    try {
      await this.client.delete(
        `/collections/${collectionID}/items/${itemID}/live`
      );

      const { status } = await this.client.delete(
        `/collections/${collectionID}/items/${itemID}`
      );

      return createSuccessResponse(null, status);
    } catch (error) {
      return catchWebflowError("Error Deleting Collection Item", error);
    }
  };
}

export default Webflow;

const catchWebflowError = (
  defaultErrorMessage: string,
  error: unknown
): Response<any> => {
  console.error(`Webflow Error - ${defaultErrorMessage}`, error);
  if (axios.isAxiosError(error)) {
    const data = error.response?.data;
    console.log("Webflow Error Data", data);
    try {
      const webflowErrorData = data as {
        message: string;
        code: string;
        externalReference: string | null;
        details: {
          description: string;
          param: string;
        }[];
      };

      const detailErrors = webflowErrorData.details.map(
        (detail) => `${detail.param} - ${detail.description}`
      );

      return createErrorResponse(
        detailErrors.length ? detailErrors : [webflowErrorData.message],
        error.response?.status || StatusCodes.UNPROCESSABLE_ENTITY,
        webflowErrorData
      );
    } catch (error) {
      console.error("Error Parsing Webflow Error", error);
      return createErrorResponse(
        [defaultErrorMessage],
        StatusCodes.UNPROCESSABLE_ENTITY,
        data
      );
    }
  } else {
    return createErrorResponse(
      [defaultErrorMessage],
      StatusCodes.UNPROCESSABLE_ENTITY
    );
  }
};
