import { useRouter } from "next/router";

import { Channel, PostStatus } from "@prisma/client";

import { MinusIcon } from "@heroicons/react/24/outline";
import sortBy from "lodash/sortBy";
import { useMemo } from "react";
import { Table } from "~/components";
import { useCalendarView } from "~/providers";
import { getFullName, getPostPublishAtMoment, trpc } from "~/utils";
import ApprovalTooltip, { Approval } from "../ApprovalTooltip";
import CampaignSelect from "../CampaignSelect";
import LabelSelect, { Label } from "../LabelSelect";
import StatusButton from "../posts/StatusButton";
import AccountList, { Account } from "./AccountList";

type User = {
  firstName: string | null;
  lastName: string | null;
};

type Campaign = {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
};

type Post = {
  id: string;
  title: string;
  channels: Channel[];
  status: PostStatus;
  publishDate: Date;
  publishAt: Date | null;
  publishTime: Date | null;
  campaignID: string | null;
  isIdea: false;
  labels: {
    id: string;
    name: string;
    color: string;
    backgroundColor: string;
  }[];
  campaign?: Campaign;
  accounts: Account[];
  approvals: Approval[];
};

type Props = {
  posts: Post[] | undefined;
  campaigns: Campaign[] | undefined;
  updatePost: (
    postID: string,
    updatedValues: {
      labels?: Label[];
      campaign?: Campaign | null;
    }
  ) => void;
};

const PostList = ({ posts, campaigns, updatePost }: Props) => {
  const { listFilters, setListFilters } = useCalendarView();

  const router = useRouter();

  const postsWithCampaigns = posts?.map((post) => {
    if (post.campaignID) {
      const campaign = campaigns?.find(
        (campaign) => campaign.id === post.campaignID
      );
      return {
        ...post,
        campaign,
      };
    } else {
      return post;
    }
  });

  const labelQuery = trpc.label.getAll.useQuery();
  const labelOptions = labelQuery.data?.labels || [];

  const columns = useMemo(
    () => [
      {
        id: "date",
        name: "Date",
        width: "120px",
        sortable: true,
        dateFilterable: true,
        defaultStartDate: listFilters.startDate,
        onStartDateChange: (startDate: Date | null) =>
          setListFilters({ ...listFilters, startDate }),
        defaultEndDate: listFilters.endDate,
        onEndDateChange: (endDate: Date | null) =>
          setListFilters({ ...listFilters, endDate }),
        selector: (row: Post) =>
          getPostPublishAtMoment(row).toISO()?.replace("Z", "") || "",
        cell: (row: Post) => {
          const dateMoment = getPostPublishAtMoment(row);
          if (row.publishAt) {
            return (
              <div className="font-body-sm flex items-center gap-2 whitespace-nowrap">
                <div data-tag="allowRowEvents">
                  {dateMoment.toFormat("M/dd")}
                </div>
                <div
                  className="text-medium-dark-gray"
                  data-tag="allowRowEvents"
                >
                  {dateMoment.toFormat("h:mma")}
                </div>
              </div>
            );
          } else {
            return dateMoment.toFormat("M/dd");
          }
        },
      },
      {
        id: "post_name",
        name: "Post Name",
        minWidth: "80px",
        maxWidth: "600px",
        sortable: true,
        searchable: true,
        selector: (row: Post) => row.title,
        grow: 1,
      },
      {
        id: "status",
        name: "Status",
        minWidth: "80px",
        maxWidth: "200px",
        selector: (row: Post) => row.status,
        cell: (row: Post) => <StatusButton post={row} />,
      },
      {
        id: "campaign",
        name: "Campaign",
        minWidth: "80px",
        selector: (row: Post) => row.campaign?.name || "",
        cell: (row: Post) => (
          <CampaignSelect
            value={row.campaign?.id}
            onChange={(campaign) => {
              updatePost(row.id, { campaign });
            }}
            defaultCampaigns={row.campaign ? [row.campaign] : undefined}
            dropdownPosition="left"
            publishDate={row.publishDate}
          />
        ),
        grow: 1,
      },
      {
        id: "channels",
        name: "Channel",
        minWidth: "80px",
        maxWidth: "220px",
        selector: (row: Post) => row.channels.join(", "),
        cell: (row: Post) => (
          <div className="flex gap-2">
            <AccountList
              accounts={row.accounts}
              numAccountsBeforeOverflow={3}
            />
          </div>
        ),
        grow: 1,
      },
      {
        id: "labels",
        name: "Labels",
        minWidth: "120px",
        selector: (row: Post) =>
          row.labels.map((label) => label.name).join(", "),
        cell: (row: Post) => {
          const updateLabels = (
            labels: {
              id: string;
              name: string;
              color: string;
              backgroundColor: string;
              createdAt: Date;
            }[]
          ) => {
            updatePost(row.id, { labels });
          };

          return (
            <LabelSelect
              value={row.labels}
              onChange={updateLabels}
              isReadOnly={false}
              isCreateable={true}
              options={labelOptions}
            />
          );
        },
        grow: 1,
      },
      {
        id: "approvers",
        name: "Approvers",
        right: true,
        minWidth: "70px",
        selector: (row: Post) =>
          row.approvals
            ?.map((approval) => getFullName(approval.user))
            .join(", ") || "",
        cell: (row: Post) => {
          const totalApprovals = row.approvals.length;

          if (totalApprovals === 0) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            return <ApprovalTooltip approvals={row.approvals} />;
          }
        },
      },
    ],
    [labelOptions]
  );

  return (
    <div className="pr-4 md:max-w-[calc(100vw-200px)]">
      <Table
        title="Posts List View"
        data={sortBy(postsWithCampaigns, "publishDate")}
        columns={columns}
        defaultSortFieldId="date"
        defaultSortDirection="desc"
        onRowClicked={(row, event) => {
          if (event.ctrlKey || event.metaKey) {
            window.open(`/posts/${row.id}`, "_blank");
          } else {
            router.push(`/posts/${row.id}`);
          }
        }}
        noDataComponent="No posts in this date range."
        persistTableHead={true}
      />
    </div>
  );
};

export default PostList;
