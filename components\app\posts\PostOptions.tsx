import classNames from "classnames";
import React, { ReactNode } from "react";
import { Divider } from "~/components";

type Props = {
  children: ReactNode;
  hideDivider?: boolean;
};

const PostOptions = ({ children, hideDivider }: Props) => {
  let childrenArray = React.Children.toArray(children).sort((a, b) => {
    // @ts-ignore
    const aProps = a.props as PostOptionProps;
    // @ts-ignore
    const bProps = b.props as PostOptionProps;

    if (!aProps.disabled && bProps.disabled) {
      return -1;
    } else if (aProps.disabled && !bProps.disabled) {
      return 1;
    } else {
      return 0;
    }
  });

  return (
    <div className="flex flex-col">
      {childrenArray.map((child, index) => {
        return (
          <div key={index}>
            {child}
            {index != childrenArray.length - 1 && !hideDivider && (
              <Divider padding="lg" />
            )}
            {index != childrenArray.length - 1 && !!hideDivider && (
              <div className="h-8" />
            )}
          </div>
        );
      })}
    </div>
  );
};

type PostOptionProps = {
  title: string;
  description?: string | null | ReactNode;
  isRequired?: boolean;
  disabled?: boolean;
  disabledMessage?: string;
  children: ReactNode;
};

const PostOption = ({
  title,
  description,
  isRequired = false,
  disabled = false,
  disabledMessage,
  children,
}: PostOptionProps) => {
  return (
    <div>
      <div
        className={classNames("font-body pb-1 font-medium transition-colors", {
          "text-black": !disabled,
          "text-medium-gray": disabled,
        })}
      >
        {title} {isRequired && <span className="text-secondary">*</span>}
      </div>
      <div
        className={classNames("font-body-sm pb-2", {
          "text-medium-dark-gray": !disabled,
          "text-medium-gray": disabled,
        })}
      >
        {disabled ? disabledMessage : description}
      </div>
      {!disabled && children}
    </div>
  );
};

export { PostOption, PostOptions };
