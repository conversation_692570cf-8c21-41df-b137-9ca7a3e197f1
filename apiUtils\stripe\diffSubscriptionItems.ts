import { BillingPeriod } from "@prisma/client";
import countMaxSocialProfiles from "apiUtils/countMaxSocialProfiles";
import countUniqueUsers from "apiUtils/countUniqueUsers";
import assert from "assert";
import Stripe from "stripe";
import { db } from "~/clients";
import stripe from "~/clients/stripe";
import {
  BASIC_WORKSPACE_LIMITS,
  PRICE_LOOKUP_KEYS,
  STANDARD_WORKSPACE_LIMITS,
  STRIPE_PRICES,
} from "~/constants";

const diffSubscriptionItems = async (
  companyID: string,
  tier: "Basic" | "Standard",
  billingPeriod: BillingPeriod
) => {
  const company = await db.company.findFirstOrThrow({
    where: {
      id: companyID,
    },
    include: {
      subscription: true,
      workspaces: {
        include: {
          _count: {
            select: {
              userWorkspaces: true,
              twitterIntegrations: true,
              instagramIntegrations: true,
              threadsIntegrations: true,
              linkedInIntegrations: true,
              discordWebhooks: true,
              tikTokIntegrations: true,
              slackIntegrations: true,
              facebookIntegrations: true,
              youTubeIntegrations: true,
              webflowIntegrations: true,
            },
          },
          userWorkspaces: true,
          invites: {
            where: {
              acceptedAt: null,
            },
          },
        },
      },
    },
  });

  const numUsers = countUniqueUsers(company);
  const numSocialProfiles = countMaxSocialProfiles(company);
  const { stripeCustomerID, subscription } = company;
  assert(subscription != null && stripeCustomerID != null);

  const stripeSubscription = await stripe.subscriptions.retrieve(
    subscription.stripeSubscriptionID
  );

  const subscriptionLookupKeys = PRICE_LOOKUP_KEYS[subscription.billingPeriod];

  const additionalSeatLookupKey = subscriptionLookupKeys.ADDITIONAL_SEAT;
  const freeSeatCurrentSubscriptionLookupKey = subscriptionLookupKeys.FREE_SEAT;

  const additionalSocialProfileLookupKey =
    subscriptionLookupKeys.ADDITIONAL_SOCIAL_PROFILE;
  const freeSocialProfileLookupKey = subscriptionLookupKeys.FREE_SOCIAL_PROFILE;

  const freeAiChatLookupKey = subscriptionLookupKeys.FREE_AI_CHAT;

  const freeScheduledPostLookupKey = subscriptionLookupKeys.FREE_SCHEDULED_POST;

  const { data: prices } = await stripe.prices.list({
    lookup_keys: [
      subscriptionLookupKeys.BASIC_WORKSPACE,
      subscriptionLookupKeys.STANDARD_WORKSPACE,

      additionalSeatLookupKey,
      freeSeatCurrentSubscriptionLookupKey,

      additionalSocialProfileLookupKey,
      freeSocialProfileLookupKey,

      freeAiChatLookupKey,

      freeScheduledPostLookupKey,
    ],
    limit: 10,
  });

  const basicWorkspacePrice = prices.find(
    (price) => price.lookup_key === subscriptionLookupKeys.BASIC_WORKSPACE
  );
  const standardWorkspacePrice = prices.find(
    (price) => price.lookup_key === subscriptionLookupKeys.STANDARD_WORKSPACE
  );
  const extraSeatPrice = prices.find(
    (price) => price.lookup_key === additionalSeatLookupKey
  );
  const freeSeatPrice = prices.find(
    (price) => price.lookup_key === freeSeatCurrentSubscriptionLookupKey
  );
  const extraSocialProfilePrice = prices.find(
    (price) => price.lookup_key === additionalSocialProfileLookupKey
  );
  const freeSocialProfilePrice = prices.find(
    (price) => price.lookup_key === freeSocialProfileLookupKey
  );

  const freeAiChatPrice = prices.find(
    (price) => price.lookup_key === freeAiChatLookupKey
  );

  const freeScheduledPostPrice = prices.find(
    (price) => price.lookup_key === freeScheduledPostLookupKey
  );

  assert(
    basicWorkspacePrice &&
      standardWorkspacePrice &&
      extraSeatPrice &&
      freeSeatPrice &&
      extraSocialProfilePrice &&
      freeSocialProfilePrice &&
      freeAiChatPrice &&
      freeScheduledPostPrice,
    "Missing price in stripe prices list"
  );

  const basicSeatLimit = BASIC_WORKSPACE_LIMITS.seatLimit;
  const standardSeatLimit = STANDARD_WORKSPACE_LIMITS.seatLimit;

  const basicSocialProfileLimit = BASIC_WORKSPACE_LIMITS.socialProfileLimit;
  const standardSocialProfileLimit =
    STANDARD_WORKSPACE_LIMITS.socialProfileLimit;

  const additionalSeatSubscriptionItem = stripeSubscription.items.data.find(
    (item) => item.price.lookup_key === additionalSeatLookupKey
  );

  const additionalSocialProfileSubscriptionItem =
    stripeSubscription.items.data.find(
      (item) => item.price.lookup_key === additionalSocialProfileLookupKey
    );

  let workspaceLookupKeys: string[] = [];
  if (subscription.tier === "Basic") {
    workspaceLookupKeys = [
      subscriptionLookupKeys.BASIC_WORKSPACE,
      subscriptionLookupKeys.BASIC_WORKSPACE_GRANDFATHERED,
    ];
  } else if (subscription.tier === "Standard") {
    workspaceLookupKeys = [
      subscriptionLookupKeys.STANDARD_WORKSPACE,
      subscriptionLookupKeys.STANDARD_WORKSPACE_GRANDFATHERED,
    ];
  } else {
    throw new Error("Invalid subscription tier");
  }

  const workspaceSubscriptionItemID = stripeSubscription.items.data.find(
    (item) => workspaceLookupKeys.includes(item.price.lookup_key || "")
  )?.id;

  const subscriptionItems: Stripe.InvoiceRetrieveUpcomingParams.SubscriptionItem[] =
    [
      {
        id: workspaceSubscriptionItemID,
        deleted: true,
      },
      {
        price:
          STRIPE_PRICES[process.env.NODE_ENV as "development" | "production"][
            tier
          ][billingPeriod],
        quantity: 1,
      },
    ];

  const switchedBillingPeriod = subscription.billingPeriod !== billingPeriod;

  // If we're switching tiers then we want to clear out any free seat, social profile, ai chat, or scheduled post
  if (switchedBillingPeriod) {
    const newSubscriptionLookupKeys = PRICE_LOOKUP_KEYS[billingPeriod];
    const freeSeatNewSubscriptionLookupKey =
      newSubscriptionLookupKeys.FREE_SEAT;
    const freeSocialProfileNewSubscriptionLookupKey =
      newSubscriptionLookupKeys.FREE_SOCIAL_PROFILE;
    const freeAiChatNewSubscriptionLookupKey =
      newSubscriptionLookupKeys.FREE_AI_CHAT;
    const freeScheduledPostNewSubscriptionLookupKey =
      newSubscriptionLookupKeys.FREE_SCHEDULED_POST;

    const { data: newPrices } = await stripe.prices.list({
      lookup_keys: [
        freeSeatNewSubscriptionLookupKey,
        freeSocialProfileNewSubscriptionLookupKey,
        freeAiChatNewSubscriptionLookupKey,
        freeScheduledPostNewSubscriptionLookupKey,
      ],
      limit: 10,
    });

    const freeSeatNewSubscriptionPrice = newPrices.find(
      (price) => price.lookup_key === freeSeatNewSubscriptionLookupKey
    );
    const freeSocialProfileNewSubscriptionPrice = newPrices.find(
      (price) => price.lookup_key === freeSocialProfileNewSubscriptionLookupKey
    );
    const freeAiChatNewSubscriptionPrice = newPrices.find(
      (price) => price.lookup_key === freeAiChatNewSubscriptionLookupKey
    );
    const freeScheduledPostNewSubscriptionPrice = newPrices.find(
      (price) => price.lookup_key === freeScheduledPostNewSubscriptionLookupKey
    );
    const freeSeatSubscriptionItem = stripeSubscription.items.data.find(
      (item) => item.price.lookup_key === freeSeatCurrentSubscriptionLookupKey
    );

    assert(
      freeSeatNewSubscriptionPrice &&
        freeSocialProfileNewSubscriptionPrice &&
        freeAiChatNewSubscriptionPrice &&
        freeScheduledPostNewSubscriptionPrice,
      "Missing new price in stripe prices list"
    );

    if (freeSeatSubscriptionItem) {
      subscriptionItems.push({
        id: freeSeatSubscriptionItem.id,
        deleted: true,
      });

      subscriptionItems.push({
        price: freeSeatNewSubscriptionPrice.id,
        quantity: freeSeatSubscriptionItem.quantity,
      });
    }

    const freeSocialProfileSubscriptionItem =
      stripeSubscription.items.data.find(
        (item) => item.price.lookup_key === freeSocialProfileLookupKey
      );
    if (freeSocialProfileSubscriptionItem) {
      subscriptionItems.push({
        id: freeSocialProfileSubscriptionItem.id,
        deleted: true,
      });

      subscriptionItems.push({
        price: freeSocialProfileNewSubscriptionPrice.id,
        quantity: freeSocialProfileSubscriptionItem.quantity,
      });
    }

    const freeAiChatSubscriptionItem = stripeSubscription.items.data.find(
      (item) => item.price.lookup_key === freeAiChatLookupKey
    );
    if (freeAiChatSubscriptionItem) {
      subscriptionItems.push({
        id: freeAiChatSubscriptionItem.id,
        deleted: true,
      });

      subscriptionItems.push({
        price: freeAiChatNewSubscriptionPrice.id,
        quantity: freeAiChatSubscriptionItem.quantity,
      });
    }

    const freeScheduledPostSubscriptionItem =
      stripeSubscription.items.data.find(
        (item) => item.price.lookup_key === freeScheduledPostLookupKey
      );
    if (freeScheduledPostSubscriptionItem) {
      subscriptionItems.push({
        id: freeScheduledPostSubscriptionItem.id,
        deleted: true,
      });

      subscriptionItems.push({
        price: freeScheduledPostNewSubscriptionPrice.id,
        quantity: freeScheduledPostSubscriptionItem.quantity,
      });
    }
  }

  const futureSeatLimit = tier === "Basic" ? basicSeatLimit : standardSeatLimit;
  if (subscription.billingPeriod !== billingPeriod) {
    // Since we're switching billing periods, we'll always want to remove the additional
    // seat subscription item since they're incompatible
    if (additionalSeatSubscriptionItem) {
      subscriptionItems.push({
        id: additionalSeatSubscriptionItem.id,
        deleted: true,
      });
    }

    // Add any additional seats, if necessary
    if (numUsers > futureSeatLimit) {
      subscriptionItems.push({
        price: extraSeatPrice.id,
        quantity: numUsers - futureSeatLimit,
      });
    }
  } else {
    // If we're not switching billing periods, we'll want to update the quantity of the
    // additional seat subscription item if it exists
    if (additionalSeatSubscriptionItem) {
      // If we don't need extra seats in the new plan we want to delete the
      // subscription item
      if (numUsers <= futureSeatLimit) {
        subscriptionItems.push({
          id: additionalSeatSubscriptionItem.id,
          deleted: true,
        });
      } else {
        // We want to update the quantity of the additional seat subscription item
        subscriptionItems.push({
          id: additionalSeatSubscriptionItem.id,
          quantity: numUsers - futureSeatLimit,
        });
      }
    } else if (numUsers > futureSeatLimit) {
      // If no existing additional seat subscription item exists, we'll want to add one
      // if necessary for the remaining seats
      subscriptionItems.push({
        price: extraSeatPrice.id,
        quantity: numUsers - futureSeatLimit,
      });
    }
  }

  // Add any additional social profiles, if necessary
  const futureNumSocialProfiles =
    tier === "Basic" ? basicSocialProfileLimit : standardSocialProfileLimit;
  if (subscription.billingPeriod !== billingPeriod) {
    // Since we're switching billion periods, we'll always want to remove the additiona
    // social profile subscription item since they're incompatible
    if (additionalSocialProfileSubscriptionItem) {
      subscriptionItems.push({
        id: additionalSocialProfileSubscriptionItem.id,
        deleted: true,
      });
    }

    // Add any additional social profiles, if necessary
    if (numSocialProfiles > futureNumSocialProfiles) {
      subscriptionItems.push({
        price: extraSocialProfilePrice.id,
        quantity: numSocialProfiles - futureNumSocialProfiles,
      });
    }
  } else {
    // If we're not switching billing periods, we'll want to update the quantity of the
    // additional social profile subscription item if it exists

    if (additionalSocialProfileSubscriptionItem) {
      // If we don't need extra social profiles in the new plan we want to delete the
      // subscription item
      if (numSocialProfiles <= futureNumSocialProfiles) {
        subscriptionItems.push({
          id: additionalSocialProfileSubscriptionItem.id,
          deleted: true,
        });
      } else {
        // We want to update the quantity of the additional social profile subscription item
        subscriptionItems.push({
          id: additionalSocialProfileSubscriptionItem.id,
          quantity: numSocialProfiles - futureNumSocialProfiles,
        });
      }
    } else if (numSocialProfiles > futureNumSocialProfiles) {
      // If no existing additional social profile subscription item exists, we'll want to add one
      // if necessary for the remaining social profiles
      subscriptionItems.push({
        price: extraSocialProfilePrice.id,
        quantity: numSocialProfiles - futureNumSocialProfiles,
      });
    }
  }

  return subscriptionItems;
};

export default diffSubscriptionItems;
