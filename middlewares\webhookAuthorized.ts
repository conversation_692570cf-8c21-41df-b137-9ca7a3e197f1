import { StatusCodes } from "http-status-codes";
import { NextApiHandler, NextApiRequest, NextApiResponse } from "next";

const webhookAuthorized = (handler: NextApiHandler) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const query = req.query;
    if (!query || !query.apiKey) {
      return res.status(StatusCodes.UNAUTHORIZED).send({
        errors: [`No API key found in query`],
        query,
      });
    } else {
      const apiKey = query.apiKey;
      if (apiKey !== process.env.WEBHOOK_API_KEY) {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .send({ errors: ["No user with this API key found"] });
      } else {
        return handler(req, res);
      }
    }
  };
};

export default webhookAuthorized;
