import classNames from "classnames";
import uniq from "lodash/uniq";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import { urlRegex } from "~/constants";
import facebookMentionRegex from "~/constants/facebookMentionRegex";
import { useWorkspace } from "~/providers";
import { getDefaultProfilePicture, removeEmptyElems } from "~/utils";
import LinkPreview from "../LinkPreview";
import FacebookAssetPreview from "./FacebookAssetPreview";

type IntegrationAccount = {
  id: string;
  name: string;
  profileImageUrl: string;
};

type Props = {
  content: {
    copy?: string;
    assets: {
      name: string;
      signedUrl: string;
      metadata: {
        mimetype: string;
      };
      createdAt: Date;
    }[];
  } | null;
  coverPhotoUrl?: string;
  connectedAccount: IntegrationAccount | null | undefined;
  maxHeight?: number | "full";
};

const FacebookFeedPreview = ({
  content,
  coverPhotoUrl,
  connectedAccount,
  maxHeight = 600,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const websites = uniq(content?.copy?.match(urlRegex)) || [];

  const lastWebsite = websites.length ? websites[websites.length - 1] : null;

  const nonEmptyCopy = content && content.copy && content.copy != "";
  const asset = content?.assets?.[0];
  const nonEmpty = nonEmptyCopy || asset != null;

  if (!content || !nonEmpty) {
    return (
      <div className="font-body text-medium-dark-gray mt-1">
        Preview will show when post content is added below
      </div>
    );
  } else {
    return (
      <div
        className="flex min-h-[300px] items-center justify-center"
        style={{ fontFamily: "system-ui" }}
      >
        <div
          className={classNames(
            "scrollbar-hidden h-fit w-[540px] space-y-4 overflow-auto rounded-lg bg-white py-3",
            {
              [`max-h-[${maxHeight}px]`]: typeof maxHeight === "number",
              "max-h-full": maxHeight === "full",
            }
          )}
        >
          <div className="font-smoothing flex items-center gap-2 px-4 leading-4">
            <img
              className="h-10 w-10 rounded-full"
              src={
                connectedAccount?.profileImageUrl ??
                getDefaultProfilePicture("Facebook")
              }
            />
            <div className="flex flex-col items-start gap-1">
              <span className="text-[15px] font-semibold text-[#050505]">
                {connectedAccount?.name || currentWorkspace?.name}
              </span>
              <div className="flex items-center gap-1 text-[12px] text-[#65676B]">
                <span>Just now</span>
                <span>·</span>
                <svg
                  fill="currentColor"
                  viewBox="0 0 16 16"
                  width="1em"
                  height="1em"
                  className="text-gray-500"
                >
                  <title>Shared with Public</title>
                  <g fillRule="evenodd" transform="translate(-448 -544)">
                    <g>
                      <path
                        d="M109.5 408.5c0 3.23-2.04 5.983-4.903 7.036l.07-.036c1.167-1 1.814-2.967 2-3.834.214-1 .303-1.3-.5-1.96-.31-.253-.677-.196-1.04-.476-.246-.19-.356-.59-.606-.73-.594-.337-1.107.11-1.954.223a2.666 2.666 0 0 1-1.15-.123c-.007 0-.007 0-.013-.004l-.083-.03c-.164-.082-.077-.206.006-.36h-.006c.086-.17.086-.376-.05-.529-.19-.214-.54-.214-.804-.224-.106-.003-.21 0-.313.004l-.003-.004c-.04 0-.084.004-.124.004h-.037c-.323.007-.666-.034-.893-.314-.263-.353-.29-.733.097-1.09.28-.26.863-.8 1.807-.22.603.37 1.166.667 1.666.5.33-.11.48-.303.094-.87a1.128 1.128 0 0 1-.214-.73c.067-.776.687-.84 1.164-1.2.466-.356.68-.943.546-1.457-.106-.413-.51-.873-1.28-1.01a7.49 7.49 0 0 1 6.524 7.434"
                        transform="translate(354 143.5)"
                      ></path>
                      <path
                        d="M104.107 415.696A7.498 7.498 0 0 1 94.5 408.5a7.48 7.48 0 0 1 3.407-6.283 5.474 5.474 0 0 0-1.653 2.334c-.753 2.217-.217 4.075 2.29 4.075.833 0 1.4.561 1.333 2.375-.013.403.52 1.78 2.45 1.89.7.04 1.184 1.053 1.33 1.74.06.29.127.65.257.97a.174.174 0 0 0 .193.096"
                        transform="translate(354 143.5)"
                      ></path>
                      <path
                        fillRule="nonzero"
                        d="M110 408.5a8 8 0 1 1-16 0 8 8 0 0 1 16 0zm-1 0a7 7 0 1 0-14 0 7 7 0 0 0 14 0z"
                        transform="translate(354 143.5)"
                      ></path>
                    </g>
                  </g>
                </svg>
              </div>
            </div>
          </div>
          <div className="text-[14px] leading-5">
            <div className="px-4">
              {content.copy?.split("\n").map((line, index) => {
                if (line === "") {
                  return <div key={index} className="py-2" />;
                } else {
                  let parsedLine = line;
                  const websites = uniq(line.match(urlRegex)) || [];

                  websites.forEach((website) => {
                    let httpWebsite = website;
                    if (!website.startsWith("http")) {
                      httpWebsite = `http://${website}`;
                    }
                    const websiteRegex = new RegExp(
                      `${website.replace("?", "\\?")}(?!\\/|[?#])`,
                      "g"
                    );
                    parsedLine = parsedLine.replace(
                      websiteRegex,
                      `[${website}](${httpWebsite})`
                    );
                  });

                  // Returns ["@[pageName](id)", "@[pageName](id)", ...]
                  const mentions = uniq(line.match(facebookMentionRegex) || []);
                  mentions.forEach((mention) => {
                    facebookMentionRegex.lastIndex = 0;
                    const pageMatch = facebookMentionRegex.exec(mention);
                    if (pageMatch) {
                      const pageId = pageMatch[2];
                      const pageLink = `https://www.facebook.com/${pageId}`;
                      const newMention = mention
                        .slice(1)
                        .replace(pageId, pageLink);
                      parsedLine = parsedLine.replaceAll(mention, newMention);
                    }
                  });

                  return (
                    <div key={index} className="mb-2" dir="auto">
                      <ReactMarkdown
                        className="w-[500px] text-[15px] break-words text-[#050505]"
                        linkTarget="_blank"
                        children={parsedLine}
                        components={{
                          h1: ({ node, ...props }) => {
                            return (
                              <span>
                                # <span {...props} />
                              </span>
                            );
                          },
                          h2: ({ node, ...props }) => {
                            return (
                              <span>
                                ## <span {...props} />
                              </span>
                            );
                          },
                          h3: ({ node, ...props }) => {
                            return (
                              <span>
                                ### <span {...props} />
                              </span>
                            );
                          },
                          strong: "span",
                          em: "span",
                          blockquote: ({ node, ...props }) => {
                            const children = removeEmptyElems(
                              props.children.filter((child) => child !== "\n")
                              // @ts-expect-error props is not defined on the child
                            ).map((child) => child.props.children);
                            return (
                              <span>
                                {">"} {children}
                              </span>
                            );
                          },
                          code: ({ node, ...props }) => {
                            return (
                              <span>
                                `<span {...props} />`
                              </span>
                            );
                          },
                          hr: ({ node, ...props }) => {
                            const length = node.position?.end.offset || 3;
                            return (
                              <span>
                                {Array.from({ length }).map((_, index) => (
                                  <span key={index}>-</span>
                                ))}
                              </span>
                            );
                          },
                          ol: ({ node, ...props }) => {
                            return (
                              <ol
                                {...props}
                                className="list-inside list-decimal"
                              />
                            );
                          },
                          ul: ({ node, ...props }) => {
                            // @ts-expect-error children is not defined on the node
                            const children = props.children[1].props.children;
                            return (
                              <ol {...props} className="list-inside">
                                <li>- {children}</li>
                              </ol>
                            );
                          },
                          a: ({ node, ...props }) => {
                            return (
                              <a
                                className="text-linkedin-blue hover:decoration-linkedin-blue font-semibold underline-offset-2 hover:underline"
                                {...props}
                              />
                            );
                          },
                        }}
                      />
                    </div>
                  );
                }
              })}
            </div>
            {content.assets.length === 0 && (
              <LinkPreview url={lastWebsite} channel="Facebook" />
            )}
            <FacebookAssetPreview
              assets={content.assets}
              coverPhotoUrl={coverPhotoUrl}
            />
          </div>
          <div className="flex items-center justify-evenly text-[15px] font-semibold text-[#65676B]">
            <div className="flex items-center gap-1.5">
              <i
                data-visualcompletion="css-img"
                style={{
                  backgroundImage:
                    'url("https://static.xx.fbcdn.net/rsrc.php/v3/y5/r/5YA9yox4TsG.png")',
                  backgroundPosition: "0px -84px",
                  backgroundSize: "auto",
                  width: "20px",
                  height: "20px",
                  backgroundRepeat: "no-repeat",
                  display: "inline-block",
                }}
              />
              <span>Like</span>
            </div>
            <div className="flex items-center gap-1.5">
              <i
                data-visualcompletion="css-img"
                style={{
                  backgroundImage:
                    'url("https://static.xx.fbcdn.net/rsrc.php/v3/y5/r/5YA9yox4TsG.png")',
                  backgroundPosition: "0px 0px",
                  backgroundSize: "auto",
                  width: "20px",
                  height: "20px",
                  backgroundRepeat: "no-repeat",
                  display: "inline-block",
                }}
              />
              <span>Comment</span>
            </div>
            <div className="flex items-center gap-1.5">
              <i
                data-visualcompletion="css-img"
                style={{
                  backgroundImage:
                    'url("https://static.xx.fbcdn.net/rsrc.php/v3/y5/r/5YA9yox4TsG.png")',
                  backgroundPosition: "0px -147px",
                  backgroundSize: "auto",
                  width: "20px",
                  height: "20px",
                  backgroundRepeat: "no-repeat",
                  display: "inline-block",
                }}
              />
              <span>Share</span>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default FacebookFeedPreview;
