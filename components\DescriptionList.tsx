import { ReactNode } from "react";

type Props<T> = {
  /** The data object that will be displayed */
  data: T;
  /** The definition for both the label and value for each row where value is a function of the data */
  rows: {
    label: string;
    value: (data: T) => ReactNode;
  }[];
};

const DescriptionList = <T,>({ data, rows }: Props<T>) => {
  return (
    <div className="space-y-2">
      {rows.map((row) => {
        return (
          <div key={row.label} className="flex items-center justify-between">
            <div className="text-ui-600">{row.label}</div>
            <div>{row.value(data)}</div>
          </div>
        );
      })}
    </div>
  );
};

export default DescriptionList;
