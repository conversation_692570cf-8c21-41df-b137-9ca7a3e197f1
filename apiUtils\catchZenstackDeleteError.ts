import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";

const catchZenstackDeleteError = async (deleteFn: () => Promise<void>) => {
  try {
    await deleteFn();
  } catch (error) {
    try {
      // We expect an error https://zenstack.dev/docs/the-complete-guide/part1/access-policy/model-level#%EF%B8%8F-giving-it-a-try
      if ((error as any)?.name === "PrismaClientKnownRequestError") {
        const prismaError = error as PrismaClientKnownRequestError;

        if (
          prismaError.code !== "P2004" ||
          prismaError.meta?.reason !== "RESULT_NOT_READABLE"
        ) {
          throw error;
        }
      } else {
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }
};

export default catchZenstackDeleteError;
