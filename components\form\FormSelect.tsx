import { Controller, Path } from "react-hook-form";

import { Label, Select } from "~/components";
import type { Option } from "~/components/Combobox";

import { Error } from ".";

import Rules from "./rules";

import camelCaseToWords from "~/utils/camelCaseToWords";

type FormSelectProps<T> = {
  control: any;
  name: Path<T>;
  options: Option<T>[];
  label?: string | JSX.Element;
  isOptional?: boolean;
  tooltipText?: string;
  rules?: Rules;
};

const FormSelect = <T,>({
  control,
  name,
  options,
  label,
  tooltipText,
  rules = {},
}: FormSelectProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <Label
            value={label}
            isRequired={!!rules.required}
            tooltipText={tooltipText}
          >
            <div>
              <Select value={value} onChange={onChange} options={options} />
              <Error error={error} />
            </div>
          </Label>
        );
      }}
    />
  );
};

export default FormSelect;
