import { PlusIcon } from "@heroicons/react/24/outline";
import { InstagramContentType } from "@prisma/client";
import assert from "assert";
import classNames from "classnames";
import { useCallback, useState } from "react";
import { Carousel } from "react-responsive-carousel";
import { toast } from "sonner";
import { AddNewButton, Badge, TextInput, Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { handleTrpcError, joinWithMore, trpc } from "~/utils";
import { InstagramAsset } from "./InstagramPost";
import UserTag from "./UserTag";

type Props = {
  postID: string;
  assets: InstagramAsset[];
  onTagCreate: (tag: {
    id: string;
    assetID: string;
    x: number | null;
    y: number | null;
    username: string;
  }) => void;
  onTagDelete: (tag: { id: string; assetID: string }) => void;
  contentType: InstagramContentType;
  isDisabled?: boolean;
};

const TagUsersModal = ({
  postID,
  assets,
  onTagCreate,
  onTagDelete,
  contentType,
  isDisabled = false,
}: Props) => {
  const [username, setUsername] = useState<string>("");
  const [offset, setOffset] = useState<{ left: number; top: number }>({
    left: 0,
    top: 0,
  });
  const [dimensions, setDimensions] = useState<{
    width: number;
    height: number;
  }>({ width: 400, height: 500 });

  const firstImageRef = useCallback((ref: HTMLImageElement) => {
    if (!ref) {
      return;
    }

    setDimensions({
      width: ref.clientWidth,
      height: ref.clientHeight,
    });
    const rect = ref.getBoundingClientRect();
    setOffset({
      left: rect.left,
      top: rect.top,
    });
  }, []);
  const firstVideoRef = useCallback((ref: HTMLVideoElement) => {
    if (!ref) {
      return;
    }

    ref.onloadedmetadata = () => {
      const videoWidth = ref?.videoWidth;
      const videoHeight = ref?.videoHeight;

      if (!videoHeight || !videoWidth) {
        return;
      }

      let width = 0;
      let height = 0;

      if (videoWidth > videoHeight) {
        width = 400;
        height = (videoHeight / videoWidth) * width;
      } else {
        height = 500;
        width = (videoWidth / videoHeight) * height;
      }

      setDimensions({
        width,
        height,
      });
      const rect = ref.getBoundingClientRect();
      setOffset({
        left: rect.left,
        top: rect.top,
      });
    };
  }, []);

  const createTagMutation = trpc.instagramContent.createAssetTag.useMutation({
    onSuccess: (data) => {
      setClick(null);
      setUsername("");

      onTagCreate(data.instagramAssetTag);
    },
    onError: (error) => {
      handleTrpcError({ title: "Failed to Add Tag", error });
    },
  });
  const deleteTagMutation = trpc.instagramContent.deleteAssetTag.useMutation({
    onSuccess: (data) => {
      onTagDelete(data.instagramAssetTag);
    },
    onError: (error) => {
      handleTrpcError({ title: "Failed to Delete Tag", error });
    },
  });

  const [click, setClick] = useState<{
    assetID: string;
    x: number;
    y: number;
  } | null>(null);
  const [open, setOpen] = useState<boolean>(false);

  const allTags = assets.reduce((acc, asset) => {
    const tags = asset.tags.map((tag) => tag.username);

    return [...acc, ...tags];
  }, [] as string[]);

  const uniqueTags = Array.from(new Set(allTags));

  if (isDisabled) {
    return (
      <Tooltip value="Add assets to tag users">
        <div className="font-body-sm bg-surface border-light-gray text-medium-gray flex h-0 items-center gap-1.5 rounded-full border-[0.25px] px-3 py-3">
          <PlusIcon className="h-3 w-3" />
          <div className="w-full max-w-[230px] 2xl:max-w-full">Tag Users</div>
        </div>
      </Tooltip>
    );
  }

  if (!assets.length) {
    return null;
  }

  return (
    <div>
      {uniqueTags.length === 0 ? (
        <AddNewButton label="Tag Users" onClick={() => setOpen(true)} />
      ) : (
        <Badge intent="secondary" kind="hollow" onClick={() => setOpen(true)}>
          <span className="text-medium-dark-gray">Tagged Users: </span>
          <span>{joinWithMore(uniqueTags)}</span>
        </Badge>
      )}
      <Credenza.Root open={open} onOpenChange={setOpen}>
        <Credenza.Header>
          <Credenza.Title>Tag Users</Credenza.Title>
          <Credenza.Description>
            {contentType === "Feed"
              ? "For photos, click anywhere to tag a public user. For videos, tagging is not supported through Instagram's API."
              : "Type in a user's Instagram handle to tag them in the video."}
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          {open && (
            <Carousel
              showArrows={false}
              showIndicators={false}
              showStatus={false}
              showThumbs={contentType === "Feed"}
              onChange={() => {
                setClick(null);
                setUsername("");
              }}
              renderThumbs={() => {
                return assets.map((asset) => {
                  if (asset.metadata.mimetype.startsWith("image")) {
                    return (
                      <img
                        key={asset.id}
                        style={{
                          width: 80,
                          height: (dimensions.height / dimensions.width) * 80,
                        }}
                        className="object-cover"
                        src={asset.signedUrl}
                      />
                    );
                  } else {
                    return (
                      <video
                        key={asset.id}
                        style={{
                          width: 80,
                          height: (dimensions.height / dimensions.width) * 80,
                        }}
                        className="object-cover"
                        src={asset.signedUrl}
                      />
                    );
                  }
                });
              }}
            >
              {assets.map((asset, index) => {
                const mimetype = asset.metadata.mimetype;

                if (mimetype.startsWith("image")) {
                  return (
                    <div
                      key={`img-${asset.id}`}
                      style={{
                        width: index === 0 ? "auto" : dimensions.width,
                        height: index === 0 ? "auto" : dimensions.height,
                      }}
                      onClick={(e) => {
                        if (click) {
                          setClick(null);
                        } else {
                          setClick({
                            assetID: asset.id,
                            x: e.clientX,
                            y: e.clientY,
                          });
                        }
                      }}
                    >
                      <img
                        ref={index === 0 ? firstImageRef : null}
                        key={`${asset.id}-${open}-${dimensions.width}-${dimensions.height}}`}
                        style={{
                          width: index === 0 ? "auto" : dimensions.width,
                          height: index === 0 ? "auto" : dimensions.height,
                        }}
                        className="w-full max-w-[528px] object-cover"
                        src={asset.signedUrl}
                      />
                      {asset.tags.map((tag) => {
                        assert(
                          tag.x != null && tag.y != null,
                          "Tag must have a position"
                        );
                        return (
                          <div
                            key={tag.id}
                            className="z-20"
                            style={{
                              position: "absolute",
                              left: `${
                                Math.min(
                                  tag.x,
                                  1 - (tag.username.length * 2) / 100
                                ) * 100
                              }%`,
                              top: `${tag.y * 100}%`,
                            }}
                          >
                            <UserTag
                              tag={tag}
                              onDeleteTagClick={() => {
                                deleteTagMutation.mutate({
                                  tagID: tag.id,
                                });
                              }}
                            />
                          </div>
                        );
                      })}
                    </div>
                  );
                } else {
                  return (
                    <div
                      key={`${asset.id}-${dimensions.width}-${dimensions.height}}`}
                    >
                      <div
                        className="flex justify-center"
                        style={{
                          width: dimensions.width,
                          height: dimensions.height,
                        }}
                      >
                        <video
                          ref={index === 0 ? firstVideoRef : null}
                          controls={true}
                          className="block object-cover"
                          playsInline={true}
                          width={dimensions.width}
                          height={dimensions.height}
                        >
                          <source src={asset.signedUrl} />
                        </video>
                      </div>
                      {contentType === "Feed" ? (
                        <div className="font-body-sm mt-2 text-left">
                          Tagging is not supported for Instagram videos.
                        </div>
                      ) : (
                        <div className="mt-2 flex items-center gap-2">
                          <div className="flex items-center gap-2">
                            <div className="w-40">
                              <TextInput
                                placeholder="@instagram-handle"
                                value={username}
                                onChange={setUsername}
                                onEnterPress={() => {
                                  const cleanedUsername = username.replace(
                                    "@",
                                    ""
                                  );
                                  createTagMutation.mutate({
                                    assetID: asset.id,
                                    tag: {
                                      username: cleanedUsername,
                                    },
                                  });
                                }}
                              />
                            </div>
                            <Button
                              size="sm"
                              isLoading={createTagMutation.isPending}
                              onClick={() => {
                                const cleanedUsername = username.replace(
                                  "@",
                                  ""
                                );
                                createTagMutation.mutate({
                                  assetID: asset.id,
                                  tag: {
                                    username: cleanedUsername,
                                  },
                                });
                              }}
                            >
                              Save
                            </Button>
                          </div>
                          <div className="scrollbar-hidden overflow-x-auto py-0.5">
                            <div className="scrollbar-hidden flex w-full items-center gap-2">
                              {asset.tags.map((tag) => {
                                return (
                                  <UserTag
                                    key={tag.id}
                                    tag={tag}
                                    onDeleteTagClick={() => {
                                      deleteTagMutation.mutate({
                                        tagID: tag.id,
                                      });
                                    }}
                                  />
                                );
                              })}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                }
              })}
            </Carousel>
          )}
          {click && (
            <div
              className={classNames(
                "absolute flex translate-y-8 items-center gap-2 rounded-lg bg-white p-3 shadow-sm",
                {
                  "-translate-x-1/2":
                    click.x < offset.left + dimensions.width - 100,
                }
              )}
              style={{
                left:
                  click.x >= offset.left + dimensions.width - 100
                    ? dimensions.width - 80
                    : click.x - offset.left + 24,
                top: click.y - offset.top + 64 + 32,
              }}
            >
              <TextInput
                placeholder="@instagram-handle"
                value={username}
                onChange={setUsername}
              />

              <Button
                size="sm"
                isLoading={createTagMutation.isPending}
                onClick={() => {
                  const cleanedUsername = username.replace("@", "");
                  if (cleanedUsername.length < 3) {
                    return toast.error("Invalid Instagram Handle", {
                      description:
                        "Instagram handles must be at least 3 characters",
                    });
                  } else {
                    createTagMutation.mutate({
                      assetID: click.assetID,
                      tag: {
                        username: cleanedUsername,
                        x: Math.max(
                          Math.min(
                            (click.x - offset.left) / dimensions.width,
                            1
                          ),
                          0
                        ),
                        y: Math.max(
                          Math.min(
                            (click.y - offset.top) / dimensions.height,
                            1
                          ),
                          0
                        ),
                      },
                    });
                  }
                }}
              >
                Save
              </Button>
            </div>
          )}
        </Credenza.Body>
      </Credenza.Root>
    </div>
  );
};

export default TagUsersModal;
