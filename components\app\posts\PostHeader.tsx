import {
  ArrowLeftIcon,
  ArrowUturnRightIcon,
  CheckCircleIcon,
  CheckIcon,
  DocumentDuplicateIcon,
  EllipsisVerticalIcon,
} from "@heroicons/react/24/outline";
import { ApprovalStatus, Channel, PostStatus } from "@prisma/client";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import { useState } from "react";
import { toast } from "sonner";
import { AutosizeInput, Divider, TextLink } from "~/components";
import { SuggestedTimes } from "~/components/TimeInput";
import Button from "~/components/ui/Button";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { useUser, useWorkspace } from "~/providers";
import {
  capitalizeWords,
  dateFromUTC,
  getPostPublishAtMoment,
  handleTrpcError,
  openConfirmationModal,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import { Label } from "../LabelSelect";
import ShareLinkDropdown from "../ShareLinkDropdown";
import BulkDuplicatePostModal from "./BulkDuplicatePostModal";
import SchedulePostSection from "./SchedulePostSection";

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    approvals: {
      id: string;
      userID: string;
      status: ApprovalStatus;
    }[];
    labels: Label[];
    channels: Channel[];
    status: PostStatus;
    isIdea: boolean;
    isSharedPublicly: boolean;
  } & PostOrIdea;
  isReadOnly?: boolean;
};

const PostHeader = ({ post, isReadOnly = false }: Props) => {
  const router = useRouter();
  const { user } = useUser();

  const { currentWorkspace } = useWorkspace();

  const trpcUtils = trpc.useUtils();
  const [title, setTitle] = useState<string>(post.title);
  const [duplicateModalOpen, setDuplicateModalOpen] = useState<boolean>(false);
  const [bulkDuplicateModalOpen, setBulkDuplicateModalOpen] =
    useState<boolean>(false);
  const [isPostApproved, setIsPostApproved] = useState<true | null>(null);

  const publishDate = post.publishDate ? dateFromUTC(post.publishDate) : null;
  const publishTime = post.publishTime
    ? getPostPublishAtMoment(post).toJSDate()
    : null;

  const postType = post.isIdea ? "idea" : "post";

  const requestedApproval = post.approvals.find(
    (approval) => approval.userID === user?.id
  );
  const approveMutation = trpc.approval.approve.useMutation({
    onError: (error) => {
      handleTrpcError({
        title: `Failed to Approve ${capitalizeWords(postType)}`,
        error,
      });
    },
    onSuccess: (data) => {
      setIsPostApproved(true);
      toast.success("Post Approved");
    },
  });
  const duplicatePostMutation = trpc.post.duplicate.useMutation({
    onError: (error) => {
      handleTrpcError({
        title: `Failed to Duplicate ${capitalizeWords(postType)}`,
        error,
      });
    },
  });

  const integrationsQuery = trpc.post.getIntegrations.useQuery(
    {
      id: post.id,
    },
    {
      refetchOnWindowFocus: false,
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    }
  );
  const accounts = integrationsQuery.data?.accounts ?? [];
  const mostRecentlyUsedQuery = trpc.post.getMostRecentlyUsed.useQuery();
  const mostRecentlyUsedTimes = (
    mostRecentlyUsedQuery.data?.publishAts || []
  ).map((publishAt) => new Date(publishAt));
  const optimalPublishTimeQuery = trpc.post.getBestTimeToPost.useQuery(
    {
      channel: post.channels[0],
      publishDate,
    },
    {
      enabled: postType === "post",
      refetchOnWindowFocus: false,
      refetchInterval: false,
    }
  );
  const optimalPublishTime = optimalPublishTimeQuery.data?.publishTime;

  const suggestedTimes: SuggestedTimes[] = [];
  if (publishTime == null) {
    if (currentWorkspace?.timezone == null) {
      suggestedTimes.push({
        label: "Recommended",
        howToComponent: (
          <TextLink
            href="/settings/general"
            size="sm"
            wrapText={true}
            openInNewTab={false}
            value="Set a timezone to see 
      recommendations ->"
          />
        ),
      });
    } else if (optimalPublishTime) {
      suggestedTimes.push({
        label: "Recommended",
        times: [
          DateTime.fromJSDate(optimalPublishTime).setLocale("local").toJSDate(),
        ],
      });
    }

    suggestedTimes.push({
      label: "Recently Used",
      times: mostRecentlyUsedTimes,
    });
  }

  const deletePostMutation = trpc.post.delete.useMutation();
  const updatePostMutation = trpc.post.update.useMutation();

  const handleDeleteContent = () => {
    openConfirmationModal({
      title: `Confirm Deletion of ${capitalizeWords(postType)}`,
      body: `This action cannot be undone. Please confirm you want to delete this ${postType}.`,
      cancelButton: {
        label: "Cancel, don't delete",
      },
      primaryButton: {
        label: `Delete ${capitalizeWords(postType)}`,
        variant: "danger",
        onClick: () => {
          toast.promise(
            deletePostMutation.mutateAsync(
              {
                id: post.id,
              },
              {
                onSuccess: (data) => {
                  if (data.post.isIdea) {
                    trpcUtils.idea.getAll.refetch();
                    router.push("/ideas");
                  } else {
                    trpcUtils.post.getMany.refetch();
                    router.push("/");
                  }
                },
              }
            ),
            {
              loading: "Deleting Post",
              success: () => {
                return {
                  message: "Post Deleted",
                  description: "Successfully deleted post",
                };
              },
              error: (error) => {
                return {
                  message: "Error Deleting Post",
                  description: error.message as string,
                };
              },
            }
          );
        },
      },
    });
  };

  return (
    <div className="sticky top-0 z-10 bg-white pt-1 md:pt-2">
      <div className="flex h-8 items-center gap-2">
        {!isReadOnly ? (
          <div className="hidden md:block">
            {router.asPath.includes("/inbox") ? (
              <button
                className="text-basic font-body-sm flex items-center gap-1 font-medium whitespace-nowrap"
                onClick={() => {
                  router.push(`/${postType}s/${post.id}`);
                }}
              >
                <ArrowUturnRightIcon className="h-4 w-4" />
                Go to Post Page
              </button>
            ) : (
              <button
                className="text-basic font-body-sm flex items-center gap-1 font-medium"
                onClick={() => {
                  router.back();
                }}
              >
                <ArrowLeftIcon className="h-4 w-4" />
                Back
              </button>
            )}
          </div>
        ) : null}
        {!isReadOnly && (
          <Divider orientation="vertical" className="hidden md:block" />
        )}
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-0.5">
            <div className="max-w-[100px] md:max-w-none">
              <AutosizeInput
                value={title}
                onChange={setTitle}
                isReadOnly={isReadOnly}
                onBlur={() => {
                  if (title.length === 0) {
                    setTitle(post.title);
                    toast.error("Update failed", {
                      description: "Title must be at least one character long",
                    });
                  } else if (post.title !== title) {
                    updatePostMutation.mutate(
                      {
                        id: post.id,
                        title,
                      },
                      {
                        onSuccess: () => {
                          if (post.isIdea) {
                            const ideasData = trpcUtils.idea.getAll.getData();

                            if (ideasData) {
                              const ideaIndex = ideasData.ideas.findIndex(
                                (idea) => idea.id === post.id
                              );

                              const newIdeasData = [...ideasData.ideas];
                              newIdeasData[ideaIndex] = {
                                ...newIdeasData[ideaIndex],
                                title,
                              };

                              trpcUtils.idea.getAll.setData(undefined, {
                                ideas: newIdeasData,
                              });
                            }
                          } else {
                            const postData = trpcUtils.post.get.getData({
                              id: post.id,
                            });

                            if (postData) {
                              trpcUtils.post.get.setData(
                                { id: post.id },
                                {
                                  post: {
                                    ...postData.post,
                                    title,
                                  },
                                  isReadOnly,
                                }
                              );
                            }
                          }
                        },
                      }
                    );
                  }
                }}
                size="sm"
                weight="medium"
              />
            </div>
          </div>
          {!isReadOnly ? (
            <div className="flex items-center gap-0 md:gap-2">
              <DropdownMenu.Root>
                <DropdownMenu.Trigger>
                  <Button variant="ghost" size="icon">
                    <DocumentDuplicateIcon className="h-5 w-5" />
                  </Button>
                </DropdownMenu.Trigger>
                <DropdownMenu.Content position="bottom-left">
                  <DropdownMenu.Group>
                    <DropdownMenu.Item
                      icon="DocumentDuplicateIcon"
                      onClick={async () => {
                        const postType = post.isIdea ? "Idea" : "Post";
                        toast.promise(
                          duplicatePostMutation.mutateAsync(
                            {
                              sourcePostID: post.id,
                            },
                            {
                              onSuccess: (data) => {
                                if (post.isIdea) {
                                  router.push(`/ideas/${data.post.id}`);
                                } else {
                                  router.push(`/posts/${data.post.id}`);
                                }
                              },
                            }
                          ),
                          {
                            loading: `Duplicating ${postType}`,
                            success: () => {
                              return {
                                message: `${postType} Duplicated`,
                                description: `Successfully duplicated ${postType.toLocaleLowerCase()}`,
                              };
                            },
                            error: (error) => {
                              return {
                                message: `Error Duplicating ${postType}`,
                                description: error.message as string,
                              };
                            },
                          }
                        );
                      }}
                    >
                      Duplicate {capitalizeWords(postType)}
                    </DropdownMenu.Item>
                    {!post.isIdea && (
                      <DropdownMenu.Item
                        icon="DocumentDuplicateIcon"
                        onClick={() => setBulkDuplicateModalOpen(true)}
                      >
                        Batch Duplicate
                      </DropdownMenu.Item>
                    )}
                  </DropdownMenu.Group>
                </DropdownMenu.Content>
              </DropdownMenu.Root>
              <ShareLinkDropdown
                id={post.id}
                type="post"
                isSharedPublicly={post.isSharedPublicly}
              />
              <DropdownMenu.Root>
                <DropdownMenu.Trigger>
                  <Button variant="ghost" size="icon">
                    <EllipsisVerticalIcon className="h-5 w-5" />
                  </Button>
                </DropdownMenu.Trigger>
                <DropdownMenu.Content position="bottom-left">
                  <DropdownMenu.Group>
                    <DropdownMenu.Item
                      icon="TrashIcon"
                      intent="danger"
                      onClick={handleDeleteContent}
                    >
                      Delete {capitalizeWords(postType)}
                    </DropdownMenu.Item>
                  </DropdownMenu.Group>
                </DropdownMenu.Content>
              </DropdownMenu.Root>
            </div>
          ) : null}
        </div>
      </div>
      <Divider padding="md" />
      {!post.isIdea && (
        <BulkDuplicatePostModal
          open={bulkDuplicateModalOpen}
          setOpen={setBulkDuplicateModalOpen}
          onSuccess={(posts) => {
            toast.success(`${posts.length} Posts Created`);
          }}
          post={post}
        />
      )}
      <div className="flex items-center gap-2 pb-2 md:hidden">
        {requestedApproval?.status === ApprovalStatus.Requested && (
          <Button
            size="sm"
            onClick={() =>
              approveMutation.mutate({
                id: requestedApproval.id,
              })
            }
            isLoading={approveMutation.isPending}
          >
            <CheckIcon className="h-4 w-4" /> Approve
          </Button>
        )}
        {(requestedApproval?.status === ApprovalStatus.Approved ||
          isPostApproved) && (
          <div className="body-sm text-success mr-2 flex items-center gap-1">
            <CheckCircleIcon className="h-4 w-4" />
            <div className="font-medium">Approved by you</div>
          </div>
        )}
        <SchedulePostSection post={post} isReadOnly={isReadOnly} />
      </div>
    </div>
  );
};

export default PostHeader;
