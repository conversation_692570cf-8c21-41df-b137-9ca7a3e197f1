import classNames from "classnames";
import { useBreakpoints } from "~/hooks";
import { AssetType } from "~/types/Asset";
import { AssetControls } from "../app/files/AssetControls";
import { UploadingAsset } from "../app/files/AssetsNewDesign";
import { ImageZoom } from "./ImageZoom";
import Progress from "./Progress";
import VideoPlayer from "./VideoPlayer";

type Asset = AssetType | UploadingAsset;

export const UploadProgress = ({
  progress,
  colorClass = "text-black",
}: {
  progress: number;
  colorClass?: string;
}) => {
  return (
    <div className="absolute inset-0 flex items-center justify-center">
      <div className="text-center">
        <Progress value={progress} className="mb-2 h-1 w-32" />
        <span className={`font-body-sm ${colorClass}`}>{progress}%</span>
      </div>
    </div>
  );
};

type AssetUploadContainerProps = {
  asset: Asset;
  coverPhotoUrl?: string;
  onEditClick?: (asset: AssetType) => void;
  onDeleteClick: (asset: AssetType) => void;
  isReadOnly: boolean;
  className?: string;
  style?: React.CSSProperties;
  useLinkedInLayout?: boolean;
  showControls?: boolean;
  onImageLoad?: () => void;
  isSmall?: boolean;
};

const AssetUploadContainer = ({
  asset,
  coverPhotoUrl,
  onEditClick,
  onDeleteClick,
  isReadOnly,
  className,
  style,
  useLinkedInLayout = false,
  showControls = true,
  onImageLoad,
  isSmall = false,
}: AssetUploadContainerProps) => {
  const isUploading = "uploadProgress" in asset;
  const width = asset.width;
  const height = asset.height;
  const hasDimensions = width && height;
  const aspectRatio =
    style?.aspectRatio || (hasDimensions ? `${width}/${height}` : undefined);
  const isTall =
    useLinkedInLayout && hasDimensions ? width / height > 0.8 : false;
  const isVideo = isUploading
    ? asset.type.startsWith("video")
    : asset.metadata?.mimetype?.startsWith("video");
  const { isMobile } = useBreakpoints();

  if (isUploading) {
    return (
      <div
        className={classNames(
          "bg-slate relative animate-pulse",
          {
            "mx-auto w-[70%]": !isTall && useLinkedInLayout,
          },
          className
        )}
        style={{
          ...(aspectRatio && { aspectRatio }),
          ...style,
        }}
      >
        <UploadProgress progress={asset.uploadProgress} />
      </div>
    );
  }

  if (isVideo) {
    if (useLinkedInLayout && !isTall) {
      return (
        <div
          className={classNames(
            "group/asset relative h-full overflow-clip",
            className
          )}
        >
          <div className="absolute">
            <video
              className="-translate-y-[15%] scale-110 blur-xl"
              src={asset.signedUrl}
              poster={coverPhotoUrl}
            />
          </div>
          <div className="mx-auto w-[70%]">
            <div
              className="w-full"
              style={{
                ...(aspectRatio && { aspectRatio }),
              }}
            >
              <VideoPlayer.Root className="h-full w-full align-middle">
                <VideoPlayer.Content
                  slot="media"
                  src={asset.signedUrl}
                  preload="auto"
                  muted={true}
                  poster={coverPhotoUrl}
                />
                <VideoPlayer.ControlBar>
                  <VideoPlayer.PlayButton />
                  <VideoPlayer.TimeRange />
                  <VideoPlayer.TimeDisplay showDuration={true} />
                  <VideoPlayer.MuteButton />
                  <VideoPlayer.VolumeRange hidden={isMobile} />
                </VideoPlayer.ControlBar>
              </VideoPlayer.Root>
            </div>
          </div>
          {showControls && (
            <AssetControls
              asset={asset}
              isReadOnly={isReadOnly}
              onDeleteClick={onDeleteClick}
              isSmall={isSmall}
            />
          )}
        </div>
      );
    }

    return (
      <div
        className="group/asset relative"
        style={{
          ...(aspectRatio && { aspectRatio }),
          ...style,
        }}
      >
        <VideoPlayer.Root className="h-full w-full">
          <VideoPlayer.Content
            slot="media"
            className={className}
            src={asset.signedUrl}
            preload="auto"
            muted={true}
            poster={coverPhotoUrl}
          />
          <VideoPlayer.ControlBar>
            <VideoPlayer.PlayButton />
            <VideoPlayer.TimeRange />
            <VideoPlayer.TimeDisplay showDuration={true} />
            <VideoPlayer.MuteButton />
            <VideoPlayer.VolumeRange hidden={isMobile} />
          </VideoPlayer.ControlBar>
        </VideoPlayer.Root>
        {showControls && (
          <AssetControls
            asset={asset}
            isReadOnly={isReadOnly}
            onDeleteClick={onDeleteClick}
            isSmall={isSmall}
          />
        )}
      </div>
    );
  }

  return (
    <div
      className="group/asset relative"
      style={{
        ...(aspectRatio && { aspectRatio }),
        ...style,
      }}
    >
      <ImageZoom>
        <img
          src={asset.signedUrl}
          className={classNames("object-cover", className)}
          onLoad={onImageLoad}
          style={style}
        />
      </ImageZoom>
      {showControls && (
        <AssetControls
          asset={asset}
          isReadOnly={isReadOnly}
          onEditClick={onEditClick}
          onDeleteClick={onDeleteClick}
          isSmall={isSmall}
        />
      )}
    </div>
  );
};

export default AssetUploadContainer;
