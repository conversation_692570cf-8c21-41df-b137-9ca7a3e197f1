import { BillingPeriod } from "@prisma/client";
import { db } from "~/clients";
import stripe from "~/clients/stripe";
import {
  BASIC_WORKSPACE_LIMITS,
  PRICE_LOOKUP_KEYS,
  STANDARD_WORKSPACE_LIMITS,
} from "~/constants";

const itemNameLimitMap = {
  ADDITIONAL_SOCIAL_PROFILE: "socialProfileLimit" as const,
  ADDITIONAL_SEAT: "seatLimit" as const,
};

const updateStripeSubscription = async ({
  subscription,
  itemName,
  quantity,
}: {
  subscription: {
    billingPeriod: BillingPeriod;
    stripeSubscriptionID: string;
  };
  itemName: keyof typeof itemNameLimitMap;
  quantity: number;
}) => {
  const billingPeriod = subscription.billingPeriod;
  const extraSeatLookupKey = PRICE_LOOKUP_KEYS[billingPeriod][itemName];

  const { data: prices } = await stripe.prices.list({
    lookup_keys: [extraSeatLookupKey],
  });
  const price = prices[0];

  const { data: subscriptionItems } = await stripe.subscriptionItems.list({
    subscription: subscription.stripeSubscriptionID,
  });

  const existingSubscriptionItem = subscriptionItems.find(
    (subscriptionItem) => subscriptionItem.price.id === price.id
  );
  if (existingSubscriptionItem) {
    const existingQuantity = existingSubscriptionItem.quantity || 1;
    if (existingQuantity === quantity) {
      await stripe.subscriptionItems.del(existingSubscriptionItem.id, {
        proration_behavior: "always_invoice",
      });
    } else {
      await stripe.subscriptionItems.update(existingSubscriptionItem.id, {
        quantity: existingQuantity - quantity,
        proration_behavior: "always_invoice",
      });
    }

    return true;
  } else {
    return false;
  }
};

const removeSubscriptionItem = async ({
  workspaceID,
  itemName,
  quantity,
}: {
  workspaceID: string;
  itemName: keyof typeof itemNameLimitMap;
  quantity: number;
}) => {
  const limitFieldName = itemNameLimitMap[itemName];

  const workspace = await db.workspace.findFirstOrThrow({
    where: {
      id: workspaceID,
    },
    include: {
      subscription: true,
      company: {
        include: {
          subscription: true,
          _count: {
            select: {
              workspaces: true,
            },
          },
        },
      },
    },
  });

  const workspaceSubscription = workspace.subscription;
  const numWorkspaces = workspace.company._count.workspaces;
  const companySubscription = workspace.company.subscription;
  if (numWorkspaces === 1) {
    if (!workspaceSubscription) {
      return;
    }

    // Update the subscription in Stripe if it exists
    if (companySubscription) {
      const didRemoveItem = await updateStripeSubscription({
        subscription: companySubscription,
        itemName,
        quantity,
      });

      if (workspaceSubscription && didRemoveItem) {
        const newLimit = workspaceSubscription[limitFieldName] - quantity;
        await db.workspaceSubscription.update({
          where: {
            workspaceID,
          },
          data: {
            [limitFieldName]: newLimit,
          },
        });
      }
    }
  } else {
    const workspaceSubscription = workspace.subscription;
    if (!workspaceSubscription || !companySubscription) {
      const company = await db.company.findFirstOrThrow({
        where: {
          id: workspace.companyID,
        },
      });

      return;
    }

    let didRemoveItem = false;
    if (
      workspaceSubscription.tier === "Basic" &&
      workspaceSubscription[limitFieldName] >
        BASIC_WORKSPACE_LIMITS[limitFieldName]
    ) {
      didRemoveItem = await updateStripeSubscription({
        subscription: companySubscription,
        itemName,
        quantity,
      });
    } else if (
      workspaceSubscription.tier === "Standard" &&
      workspaceSubscription[limitFieldName] >
        STANDARD_WORKSPACE_LIMITS[limitFieldName]
    ) {
      didRemoveItem = await updateStripeSubscription({
        subscription: companySubscription,
        itemName,
        quantity,
      });
    }

    if (didRemoveItem) {
      await db.workspaceSubscription.update({
        where: {
          workspaceID,
        },
        data: {
          [limitFieldName]: workspaceSubscription[limitFieldName] - quantity,
        },
      });
    }
  }
};

export default removeSubscriptionItem;
