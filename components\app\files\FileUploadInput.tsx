import Uppy from "@uppy/core";
import { handleFilesUpload } from "~/utils";

type FileUploadInputProps = {
  inputID: string;
  contentID: string;
  uppy: Uppy | null;
  getValidFiles: (files: File[]) => Promise<File[]>;
  acceptedMimeTypes: string[];
  isDisabled: boolean;
  multiple: boolean;
};

const FileUploadInput = ({
  inputID,
  contentID,
  uppy,
  getValidFiles,
  acceptedMimeTypes,
  isDisabled,
  multiple,
}: FileUploadInputProps) => {
  return (
    <input
      id={inputID}
      disabled={isDisabled}
      type="file"
      multiple={multiple}
      accept={acceptedMimeTypes.join(",")}
      className="hidden"
      onChange={async (event) => {
        const files = Array.from(event.target.files || []);
        // Clears the input value so that the user can upload the same file again
        event.target.value = "";

        if (uppy) {
          await handleFilesUpload({
            files,
            uppy,
            meta: {
              contentID,
            },
            getValidFiles,
          });
        }
      }}
    />
  );
};

export default FileUploadInput;
