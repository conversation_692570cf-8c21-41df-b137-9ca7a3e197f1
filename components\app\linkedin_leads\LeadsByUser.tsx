import {
  ArrowDownTrayIcon,
  ChatBubbleLeftEllipsisIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  HandThumbUpIcon,
  MinusIcon,
} from "@heroicons/react/24/outline";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import assert from "assert";
import { useMemo } from "react";
import { Loader, TextLink, Tooltip } from "~/components";
import Avatar from "~/components/ui/Avatar";
import Button from "~/components/ui/Button";
import Table from "~/components/ui/Table";
import { downloadCSV, trpc } from "~/utils";
import getLinkedInProfilePermalink from "~/utils/linkedin/getLinkedInProfilePermalink";
import { RouterOutput } from "~/utils/trpc";

type LeadsByUserProps = {
  integration:
    | {
        id: string;
        account: {
          name: string;
          vanityName: string;
          profileImageUrl: string;
        };
      }
    | undefined;
};

type Profile =
  RouterOutput["leads"]["linkedIn"]["getLeadsByUser"]["profiles"][number];

const LeadsByUser = ({ integration }: LeadsByUserProps) => {
  const getLeadsByUserQuery = trpc.leads.linkedIn.getLeadsByUser.useQuery(
    {
      integrationID: integration!.id,
    },
    { enabled: !!integration?.id }
  );

  const columnHelper = createColumnHelper<Profile>();

  const columns = useMemo(() => {
    return [
      columnHelper.accessor((row) => `${row.firstName} ${row.lastName}`, {
        header: "Name",
        id: "name",
        cell: (info) => (
          <div className="flex items-center gap-2">
            <Avatar.Root>
              <Avatar.Image
                src={info.row.original.profileImageUrl}
                className="h-6 w-6 rounded-full"
              />
              <Avatar.Fallback>
                <img
                  src="/linkedin/default_user.png"
                  className="h-6 w-6 rounded-full"
                />
              </Avatar.Fallback>
            </Avatar.Root>
            <TextLink
              href={getLinkedInProfilePermalink({
                vanityName: info.row.original.vanityName,
                type: "User",
              })}
              value={`${info.row.original.firstName} ${info.row.original.lastName}`}
            />
          </div>
        ),
      }),
      columnHelper.accessor("engagementsCount", {
        header: "Total",
        enableSorting: true,
        cell: (info) => {
          return info.getValue();
        },
      }),
      columnHelper.accessor("reactionsCount", {
        header: "Likes",
        enableSorting: true,
        cell: (info) => {
          const reactionsCount = info.getValue();
          if (reactionsCount == null) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            return (
              <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1">
                <HandThumbUpIcon className="h-4 w-4" />
                <div>{reactionsCount}</div>
              </div>
            );
          }
        },
      }),
      columnHelper.accessor("commentsCount", {
        header: "Comments",
        enableSorting: true,
        cell: (info) => {
          const commentsCount = info.getValue();
          if (commentsCount == null) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            return (
              <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1">
                <ChatBubbleLeftEllipsisIcon className="h-4 w-4" />
                <div>{commentsCount}</div>
              </div>
            );
          }
        },
      }),
      columnHelper.accessor((row) => row.mostRecentPost, {
        header: "Most Recent Post",
        id: "mostRecentPost",
        cell: (info) => {
          const post = info.getValue();
          if (!post) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            return (
              <Tooltip value={post.post?.title || post.commentary || ""}>
                <div className="max-w-48 truncate">
                  {post.post?.title || post.commentary || "Untitled"}
                </div>
              </Tooltip>
            );
          }
        },
      }),
      columnHelper.accessor((row) => row.currentJob?.title, {
        header: "Role",
        id: "role",
        enableSorting: false,
        cell: (info) => {
          const title = info.getValue();
          if (!title) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            return (
              <div className="font-body-sm max-w-48 truncate">{title}</div>
            );
          }
        },
      }),
      columnHelper.accessor((row) => row.currentJob, {
        header: "Company",
        id: "company",
        enableSorting: false,
        cell: (info) => {
          const currentJob = info.getValue();
          if (!currentJob) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else if (currentJob.organization) {
            return (
              <div className="bg-lightest-gray-50 w-fit max-w-48 truncate rounded-full px-2 py-1">
                <a
                  href={getLinkedInProfilePermalink({
                    vanityName: currentJob.organization.vanityName,
                    type: "Organization",
                  })}
                  target="_blank"
                  className="hover:text-secondary transition-colors"
                >
                  {currentJob.organization.name}
                </a>
              </div>
            );
          } else {
            return (
              <div className="bg-lightest-gray-50 w-fit max-w-48 truncate rounded-full px-2 py-1">
                {currentJob.organizationName}
              </div>
            );
          }
        },
      }),
      columnHelper.accessor((row) => row.currentJob, {
        header: "Industry",
        id: "industry",
        cell: (info) => {
          const currentJob = info.getValue();
          if (!currentJob) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            const industry =
              currentJob.organization?.organizationData?.industry;
            if (!industry) {
              return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
            } else {
              return (
                <div className="bg-lightest-gray-50 w-fit max-w-48 truncate rounded-full px-2 py-1">
                  {industry}
                </div>
              );
            }
          }
        },
      }),
      columnHelper.accessor((row) => row.currentJob?.location, {
        header: "Location",
        id: "location",
        cell: (info) => {
          const location = info.getValue();
          if (!location) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            return (
              <Tooltip value={location}>
                <div className="max-w-40 truncate">{location}</div>
              </Tooltip>
            );
          }
        },
      }),
    ];
  }, []);

  const profiles = getLeadsByUserQuery.data?.profiles || [];

  const leadsTable = useReactTable({
    data: profiles,
    columns: columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      sorting: [
        {
          id: "engagementsCount",
          desc: true,
        },
      ],
    },
  });

  if (integration == null) {
    return null;
  }

  if (getLeadsByUserQuery.isLoading) {
    return (
      <div className="flex w-full items-center justify-center">
        <Loader size="lg" />
      </div>
    );
  }

  const handleDownloadCSV = () => {
    const headers = [
      "id",
      "first_name",
      "last_name",
      "headline",
      "url",
      "follower_count",
      "engagements_count",
      "comments_count",
      "reactions_count",
      "post_url",
      "post_published_at",
      "job_title",
      "job_start_date",
      "job_end_date",
      "company",
      "company_url",
      "company_industry",
      "location",
    ];

    const rows = profiles.map((profile) => [
      profile.id,
      profile.firstName,
      profile.lastName,
      profile.headline,
      getLinkedInProfilePermalink({
        vanityName: profile.vanityName,
        type: "User",
      }),
      profile.followerCount,
      profile.engagementsCount,
      profile.commentsCount,
      profile.reactionsCount,
      `https://www.linkedin.com/feed/update/${profile.mostRecentPost?.id}`,
      profile.mostRecentPost?.publishedAt?.toISOString().replace("Z", ""),
      profile.currentJob?.title,
      profile.currentJob?.startDate.toISOString().replace("Z", ""),
      profile.currentJob?.endDate?.toISOString().replace("Z", ""),
      profile.currentJob?.organizationName,
      profile.currentJob?.organization
        ? getLinkedInProfilePermalink({
            vanityName: profile.currentJob?.organization?.vanityName,
            type: "Organization",
          })
        : "",
      profile.currentJob?.organization?.organizationData?.industry,
      profile.currentJob?.location,
    ]);

    assert(integration, "Selected integration not found");

    const fileName = `linkedin_leads_${integration.account.vanityName}_${
      new Date().toISOString().split("T")[0]
    }.csv`;

    downloadCSV({
      headers,
      rows,
      fileName,
    });
  };

  return (
    <div className="font-body-sm mt-4 flex max-w-full flex-col gap-3 overflow-x-auto pb-4">
      <div className="flex items-center justify-end">
        <Button size="sm" onClick={handleDownloadCSV}>
          <ArrowDownTrayIcon className="h-4 w-4" />
          Export as CSV
        </Button>
      </div>
      <Table.Root>
        <Table.Header>
          {leadsTable.getHeaderGroups().map((headerGroup) => (
            <Table.Row key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Table.Head
                  key={header.id}
                  isSortable={header.column.getCanSort()}
                  onClick={header.column.getToggleSortingHandler()}
                >
                  <div className="flex items-center gap-1">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    {header.column.getCanSort() && (
                      <div className="flex flex-col">
                        {header.column.getIsSorted() === "asc" ? (
                          <ChevronUpIcon className="h-3 w-3" />
                        ) : header.column.getIsSorted() === "desc" ? (
                          <ChevronDownIcon className="h-3 w-3" />
                        ) : (
                          <div className="h-3 w-3" />
                        )}
                      </div>
                    )}
                  </div>
                </Table.Head>
              ))}
            </Table.Row>
          ))}
        </Table.Header>
        <Table.Body>
          {leadsTable.getRowModel().rows.map((row) => {
            return (
              <Table.Row key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Table.Cell key={cell.id} className="max-w-48 truncate">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Table.Cell>
                ))}
              </Table.Row>
            );
          })}
        </Table.Body>
      </Table.Root>
    </div>
  );
};

export default LeadsByUser;
