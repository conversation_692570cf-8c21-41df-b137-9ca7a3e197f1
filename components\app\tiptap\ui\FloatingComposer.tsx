import { FloatingComposer as LiveblocksFloatingComposer } from "@liveblocks/react-tiptap";
import { Editor } from "@tiptap/react";
import { FocusEvent } from "react";

type FloatingComposerProps = {
  editor: Editor;
  metadata: {
    channel: string;
  };
};

const FloatingComposer = ({ editor, metadata }: FloatingComposerProps) => {
  const handleBlur = (event: FocusEvent<HTMLFormElement>) => {
    const target = event.relatedTarget as HTMLElement;

    // Don't clear selection if clicking on threads UI or related components
    if (
      target?.closest(
        `.lb-root, .lb-threads-container, .lb-composer, .lb-portal, 
         [role="menu"], [aria-haspopup="menu"], [aria-haspopup="dialog"], 
         .lb-composer-editor-action, .lb-composer-footer, .lb-composer-editor-actions,
         .tippy-box, .emoji-picker, [data-editor-modal]`
      )
    ) {
      return;
    }

    // Clear selection when clicking outside the text editor
    editor.commands.selectTextblockEnd();
  };

  return (
    <LiveblocksFloatingComposer
      editor={editor}
      className="w-96"
      onBlur={handleBlur}
      metadata={{
        channel: metadata.channel,
      }}
    />
  );
};

export default FloatingComposer;
