import axios from "axios";

const compressVideoAPI = async (video: {
  url: string;
  filename: string;
  mimetype: string;
}) => {
  const format = video.mimetype.split("/")[1];

  const inputBody = {
    tasks: {
      "import-url": {
        operation: "import/url",
        url: video.url,
        filename: video.filename,
      },
      "compress-1": {
        operation: "compress",
        input: "import-url",
        input_format: format,
        output_format: format,
        options: {
          video_codec: "h264",
          compress_video: "by_resolution",
          by_width_keep_ar: 1280,
        },
      },
      "export-1": {
        operation: "export/url",
        input: ["compress-1"],
      },
    },
  };

  const response = await axios.post(
    "https://api.freeconvert.com/v1/process/jobs",
    inputBody,
    {
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: `Bearer ${process.env.FREE_CONVERT_ACCESS_TOKEN}`,
      },
    }
  );

  return response.data;
};

export default compressVideoAPI;
