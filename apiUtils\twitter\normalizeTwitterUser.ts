import assert from "assert";
import { UserV2 } from "twitter-api-v2";

const normalizeTwitterUser = (user: UserV2) => {
  const profileImageUrl = user.profile_image_url?.replace(
    "_normal",
    "_400x400"
  );
  assert(profileImageUrl != null);

  const urlMap = new Map(
    user.entities?.url?.urls.map((url) => [url.url, url.expanded_url])
  );

  const descriptionUrlMap = new Map(
    user.entities?.description?.urls?.map((url) => [url.url, url.expanded_url])
  );

  const url = user.url?.replace(
    /https?:\/\/t\.co\/[a-zA-Z0-9]+/g,
    (url) => urlMap.get(url) ?? url
  );

  const description =
    user.description?.replace(
      /https?:\/\/t\.co\/[a-zA-Z0-9]+/g,
      (url) => descriptionUrlMap.get(url) ?? url
    ) || "";

  assert(user.created_at != null);
  const accountCreatedAt = new Date(user.created_at);

  const publicMetrics = user.public_metrics;
  assert(publicMetrics != null);
  const followerCount = publicMetrics.followers_count;
  const followingCount = publicMetrics.following_count;
  const tweetCount = publicMetrics.tweet_count;
  const listedCount = publicMetrics.listed_count;
  assert(
    followerCount != null &&
      followingCount != null &&
      tweetCount != null &&
      listedCount != null
  );

  return {
    id: user.id,
    username: user.username,
    name: user.name,
    url,
    description,
    location: user.location,
    profileImageUrl,
    verified: !!user.verified,
    verifiedType: user.verified_type || "none",
    accountCreatedAt,
    stats: {
      followerCount,
      followingCount,
      tweetCount,
      listedCount,
    },
  };
};

export default normalizeTwitterUser;
