import getAssetBuffer from "apiUtils/getAssetBuffer";
import axios, { AxiosInstance } from "axios";
import { StatusCodes } from "http-status-codes";
import { DateTime } from "luxon";
import {
  Response,
  createErrorResponse,
  createSuccessResponse,
} from "../Response";
// @ts-ignore, no types for this https://github.com/t6tn4k/node-split
import { TikTokPrivacyLevel } from "@prisma/client";
import chunkBuffer from "apiUtils/chunkBuffer";
import { removeEmptyElems } from "~/utils";
import catchTikTokError from "./catchTikTokError";

class TikTok {
  BASE_URL = "https://open.tiktokapis.com/v2/";

  accessToken: string;

  client: AxiosInstance;

  constructor(accessToken: string) {
    this.client = axios.create({
      baseURL: this.BASE_URL,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    this.accessToken = accessToken;
  }

  // https://developers.tiktok.com/doc/oauth-user-access-token-management
  static getAccessToken = async (
    code: string
  ): Promise<
    Response<{
      accessToken: string;
      expiresAt: Date;
      refreshToken: string;
      refreshTokenExpiresAt: Date;
      scope: string[];
    }>
  > => {
    const params = {
      client_key: `${process.env.NEXT_PUBLIC_TIK_TOK_CLIENT_KEY}`,
      client_secret: `${process.env.TIK_TOK_CLIENT_SECRET}`,
      code,
      grant_type: "authorization_code",
      redirect_uri: `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/tiktok/callback`,
    };

    const urlParams = new URLSearchParams(params);

    try {
      const now = DateTime.now();

      const response = await axios.post(
        `https://open.tiktokapis.com/v2/oauth/token/`,
        urlParams.toString(),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const data = response.data as {
        access_token: string;
        expires_in: number;
        open_id: number;
        refresh_token: string;
        refresh_expires_in: number;
        scope: string;
        error_code?: number;
        error_description?: string;
      };

      if (data.error_description) {
        return createErrorResponse(
          [data.error_description],
          StatusCodes.UNPROCESSABLE_ENTITY
        );
      } else {
        return createSuccessResponse(
          {
            accessToken: data.access_token,
            expiresAt: DateTime.fromSeconds(now.toSeconds())
              .plus({ seconds: data.expires_in })
              .toJSDate(),
            refreshToken: data.refresh_token,
            refreshTokenExpiresAt: DateTime.fromSeconds(now.toSeconds())
              .plus({ seconds: data.refresh_expires_in })
              .toJSDate(),
            scope: data.scope.split(","),
          },
          StatusCodes.OK
        );
      }
    } catch (error) {
      console.error(error);
      return createErrorResponse(
        ["Error getting auth token"],
        StatusCodes.UNPROCESSABLE_ENTITY
      );
    }
  };

  // https://developers.tiktok.com/doc/oauth-user-access-token-management#2._refresh_an_access_token_using_a_refresh_token
  static refreshAccessToken = async (
    refreshToken: string
  ): Promise<
    Response<{
      accessToken: string;
      expiresAt: Date;
      refreshToken: string;
      refreshTokenExpiresAt: Date;
      scope: string[];
    }>
  > => {
    const params = {
      client_key: `${process.env.NEXT_PUBLIC_TIK_TOK_CLIENT_KEY}`,
      client_secret: `${process.env.TIK_TOK_CLIENT_SECRET}`,
      grant_type: "refresh_token",
      refresh_token: refreshToken,
    };

    try {
      const now = DateTime.now();

      const response = await axios.post(
        `https://open.tiktokapis.com/v2/oauth/token/`,
        new URLSearchParams(params).toString(),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const data = response.data as {
        access_token: string;
        expires_in: number;
        open_id: number;
        refresh_token: string;
        refresh_expires_in: number;
        scope: string;
        error_code?: number;
        error_description?: string;
      };

      if (data.error_description) {
        return createErrorResponse(
          [data.error_description],
          StatusCodes.UNPROCESSABLE_ENTITY
        );
      } else {
        return createSuccessResponse(
          {
            accessToken: data.access_token,
            expiresAt: DateTime.fromSeconds(now.toSeconds())
              .plus({ seconds: data.expires_in })
              .toJSDate(),
            refreshToken: data.refresh_token,
            refreshTokenExpiresAt: DateTime.fromSeconds(now.toSeconds())
              .plus({ seconds: data.refresh_expires_in })
              .toJSDate(),
            scope: data.scope.split(","),
          },
          StatusCodes.OK
        );
      }
    } catch (error) {
      console.error(error);
      return createErrorResponse(
        ["Error getting auth token"],
        StatusCodes.UNPROCESSABLE_ENTITY
      );
    }
  };

  // https://developers.tiktok.com/doc/tiktok-api-v2-get-user-info/
  getMe = async (): Promise<
    Response<{
      account: {
        id: string;
        name: string;
        username: string;
        bioDescription: string;
        profileLink: string;
        isVerified: boolean;
        profileImageUrl: string;
      };
      stats: {
        followingCount: number;
        followerCount: number;
        likeCount: number;
      };
    }>
  > => {
    const params = new URLSearchParams({
      fields: [
        "open_id",
        "username",
        "avatar_url",
        "avatar_large_url",
        "display_name",
        "bio_description",
        "profile_deep_link",
        "is_verified",
        "follower_count",
        "following_count",
        "likes_count",
      ].join(","),
    });

    try {
      const response = await this.client.get(`/user/info/?${params}`);

      const status = response.status;
      const rawData = response.data as {
        data: {
          user: {
            open_id: string;
            profile_deep_link: string;
            avatar_large_url: string;
            username: string;
            bio_description: string;
            avatar_url: string;
            display_name: string;
            is_verified: boolean;
            follower_count: number;
            following_count: number;
            likes_count: number;
          };
        };
        error: {
          code: "ok" | string;
          message: string;
        };
      };

      if (rawData.error.code != "ok") {
        return createErrorResponse(
          [rawData.error.message],
          StatusCodes.UNPROCESSABLE_ENTITY
        );
      }

      const rawUser = rawData.data.user;

      const data = {
        account: {
          id: rawUser.open_id,
          name: rawUser.display_name,
          username: rawUser.username,
          bioDescription: rawUser.bio_description,
          profileLink: rawUser.profile_deep_link,
          isVerified: rawUser.is_verified,
          profileImageUrl: rawUser.avatar_large_url,
        },
        stats: {
          followerCount: rawUser.follower_count,
          followingCount: rawUser.following_count,
          likeCount: rawUser.likes_count,
        },
      };

      return createSuccessResponse(data, status);
    } catch (error) {
      return catchTikTokError(error, "Error getting TikTok account info");
    }
  };

  // https://developers.tiktok.com/doc/content-posting-api-reference-direct-post#query_creator_information
  getCreatorInfo = async (): Promise<
    Response<{
      avatarUrl: string;
      username: string;
      name: string;
      duetDisabled: boolean;
      stitchDisabled: boolean;
      commentDisabled: boolean;
      privacyLevelOptions: string[];
      maxVideoPostDurationSec: number;
    }>
  > => {
    try {
      const response = await this.client.post(
        `/post/publish/creator_info/query/`
      );

      const data = response.data as {
        data: {
          privacy_level_options: string[];
          stitch_disabled: boolean;
          comment_disabled: boolean;
          creator_avatar_url: string;
          creator_nickname: string;
          creator_username: string;
          duet_disabled: boolean;
          max_video_post_duration_sec: number;
        };
        error: {
          code: "ok" | string;
          message: string;
        };
      };

      if (data.error.code != "ok") {
        return createErrorResponse(
          [data.error.message],
          StatusCodes.UNPROCESSABLE_ENTITY
        );
      }

      const creatorInfo = data.data;

      return createSuccessResponse(
        {
          avatarUrl: creatorInfo.creator_avatar_url,
          username: creatorInfo.creator_username,
          name: creatorInfo.creator_nickname,
          duetDisabled: creatorInfo.duet_disabled,
          stitchDisabled: creatorInfo.stitch_disabled,
          commentDisabled: creatorInfo.comment_disabled,
          privacyLevelOptions: creatorInfo.privacy_level_options,
          maxVideoPostDurationSec: creatorInfo.max_video_post_duration_sec,
        },
        response.status
      );
    } catch (error) {
      return catchTikTokError(error, "Error getting TikTok creator info");
    }
  };

  // https://developers.tiktok.com/doc/content-posting-api-reference-direct-post#initialize_the_posting_request
  publishVideo = async ({
    caption,
    video,
    privacyLevel,
    isCommentDisabled,
    isDuetDisabled,
    isStitchDisabled,
    isPromotingBusiness,
    isPaidPartnership,
  }: {
    caption: string | null;
    video: {
      url: string;
      mimetype: string;
    };
    privacyLevel: TikTokPrivacyLevel;
    isCommentDisabled: boolean;
    isDuetDisabled: boolean;
    isStitchDisabled: boolean;
    isPromotingBusiness: boolean;
    isPaidPartnership: boolean | null;
  }): Promise<
    Response<{
      id: string;
    }>
  > => {
    try {
      const assetBuffer = await getAssetBuffer(video.url);
      const videoSize = assetBuffer.byteLength;
      let chunkSize = videoSize;
      if (videoSize > 50_000_000) {
        // Try to do half the size or default to 50MB chunks
        const numMB = Math.floor(videoSize / 1_000_000);
        if (numMB % 2 === 0) {
          chunkSize = Math.min(50_000_000, numMB * 500_000);
        } else {
          chunkSize = Math.min(50_000_000, (numMB - 1) * 500_000);
        }
      }
      const totalChunkCount = Math.floor(videoSize / chunkSize);

      const body = {
        post_info: {
          title: caption,
          privacy_level: privacyLevel,
          disable_duet: isDuetDisabled,
          disable_stitch: isStitchDisabled,
          disable_comment: isCommentDisabled,
          video_cover_timestamp_ms: 0,
          brand_content_toggle: isPromotingBusiness,
          brand_organic_toggle: isPromotingBusiness
            ? isPaidPartnership
            : undefined,
        },
        source_info: {
          source: "FILE_UPLOAD",
          video_size: videoSize,
          chunk_size: chunkSize,
          total_chunk_count: totalChunkCount,
        },
      };

      console.log("TikTok Publish Init", body);

      const response = await this.client.post(
        `/post/publish/video/init/`,
        body,
        {
          headers: {
            "Content-Type": "application/json; charset=UTF-8",
          },
        }
      );

      const data = response.data as {
        data: {
          publish_id: string;
          upload_url: string;
        };
        error: {
          code: "ok" | string;
          message: string;
        };
      };

      if (data.error.code != "ok") {
        console.log("TikTok Publish Init Error", data.error);
        return createErrorResponse(
          [data.error.message],
          StatusCodes.UNPROCESSABLE_ENTITY,
          data.error
        );
      }

      console.log("TikTok Publish Init Success", data.data);

      // https://developers.tiktok.com/doc/content-posting-api-reference-direct-post#send_video_to_tiktok_servers
      const rawVideoChunks =
        totalChunkCount === 1
          ? [assetBuffer]
          : chunkBuffer(assetBuffer, chunkSize / 1_000_000);
      // If we can't divide evenly in to chunkSize then the last chunk will be smaller and we want to combine
      // the last two chunks in to one
      const videoChunks =
        rawVideoChunks.length > 2
          ? removeEmptyElems(
              rawVideoChunks.map((chunk, index) => {
                if (index === rawVideoChunks.length - 1) {
                  return null;
                } else if (index === rawVideoChunks.length - 2) {
                  const lastChunk = rawVideoChunks[rawVideoChunks.length - 1];
                  const secondToLastChunk =
                    rawVideoChunks[rawVideoChunks.length - 2];
                  const combinedChunk = Buffer.concat([
                    secondToLastChunk,
                    lastChunk,
                  ]);
                  return combinedChunk;
                } else {
                  return chunk;
                }
              })
            )
          : rawVideoChunks;

      console.log(`TikTok Publishing: ${videoChunks.length}} Chunks`);

      let byteRangeStart = 0;
      for await (const chunk of videoChunks) {
        const uploadResponse = await axios.put(data.data.upload_url, chunk, {
          headers: {
            "Content-Type": video.mimetype,
            "Content-Length": chunk.byteLength,
            "Content-Range": `bytes ${byteRangeStart}-${
              byteRangeStart + chunk.byteLength - 1
            }/${videoSize}`,
          },
        });

        byteRangeStart += chunk.byteLength;
      }

      console.log(`TikTok Publishing Completed`);

      return createSuccessResponse(
        {
          id: data.data.publish_id,
        },
        response.status
      );
    } catch (error) {
      return catchTikTokError(error, "Error publishing video");
    }
  };

  // https://developers.tiktok.com/doc/tiktok-api-v2-video-list/
  getVideos = async (): Promise<
    Response<
      {
        id: string;
        title: string;
        permalink: string;
        coverImageUrl: string;
        embedLink: string;
        embedHtml: string;
        commentCount: number;
        likeCount: number;
        shareCount: number;
        viewCount: number;
        duration: number;
      }[]
    >
  > => {
    try {
      const params = new URLSearchParams({
        fields: [
          "id",
          "create_time",
          "cover_image_url",
          "share_url",
          "video_description",
          "duration",
          "height",
          "width",
          "title",
          "embed_html",
          "embed_link",
          "like_count",
          "comment_count",
          "share_count",
          "view_count",
        ].join(","),
      });

      const response = await this.client.post(`/video/list/?${params}`);

      const data = response.data as {
        data: {
          videos: {
            id: string;
            create_time: number;
            cover_image_url: string;
            share_url: string;
            video_description: string;
            duration: number;
            height: number;
            width: number;
            title: string;
            embed_html: string;
            embed_link: string;
            like_count: number;
            comment_count: number;
            share_count: number;
            view_count: number;
          }[];
        };
        error: {
          code: "ok" | string;
          message: string;
        };
      };

      if (data.error.code != "ok") {
        return createErrorResponse(
          [data.error.message],
          StatusCodes.UNPROCESSABLE_ENTITY
        );
      }

      const videos = data.data.videos.map((video) => ({
        id: video.id,
        title: video.title,
        permalink: video.share_url.split("?")[0],
        coverImageUrl: video.cover_image_url,
        embedLink: video.embed_link,
        embedHtml: video.embed_html,
        commentCount: video.comment_count,
        likeCount: video.like_count,
        shareCount: video.share_count,
        viewCount: video.view_count,
        duration: video.duration,
      }));

      return createSuccessResponse(videos, response.status);
    } catch (error) {
      console.error(error);
      return catchTikTokError(error, "Error getting videos");
    }
  };
}

export default TikTok;
