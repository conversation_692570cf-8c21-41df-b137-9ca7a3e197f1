"use client";

import { useMap } from "@mantine/hooks";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import Button from "~/components/ui/Button";
import Card from "~/components/ui/Card";
import Credenza from "~/components/ui/Credenza";

const ButtonPage = () => {
  const isButtonLoading = useMap<string, boolean>([]);

  const isLoading = (key: string) => {
    return isButtonLoading.get(key) ?? false;
  };

  const handleButtonClick = (key: string) => {
    isButtonLoading.set(key, true);
    setTimeout(() => {
      isButtonLoading.set(key, false);
    }, 1000);
  };

  return (
    <SettingsLayout.Root>
      <SettingsLayout.Header>
        <SettingsLayout.Title>Button</SettingsLayout.Title>
        <SettingsLayout.Description>
          Buttons are used to trigger actions.
        </SettingsLayout.Description>
      </SettingsLayout.Header>
      <div className="grid grid-cols-1 gap-4">
        <Credenza.Root>
          <Credenza.Trigger>
            <Button>Open Credenza</Button>
          </Credenza.Trigger>
          <Credenza.Header>
            <Credenza.Title>Credenza</Credenza.Title>
          </Credenza.Header>
          <Credenza.Body>
            <Credenza.Description>Credenza</Credenza.Description>
          </Credenza.Body>
          <Credenza.Footer>
            <Button>Close</Button>
          </Credenza.Footer>
        </Credenza.Root>
        <Card.Root>
          <Card.Header>
            <Card.Title>Buttons</Card.Title>
            <Card.Description>
              Buttons are used to trigger actions.
            </Card.Description>
          </Card.Header>
          <Card.Content className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <Button
                variant="default"
                isLoading={isLoading("default")}
                onClick={() => handleButtonClick("default")}
              >
                Default
              </Button>
              <Button
                variant="default"
                size="sm"
                isLoading={isLoading("defaultSm")}
                onClick={() => handleButtonClick("defaultSm")}
              >
                Default
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                isLoading={isLoading("secondary")}
                onClick={() => handleButtonClick("secondary")}
              >
                Secondary
              </Button>
              <Button
                variant="secondary"
                size="sm"
                isLoading={isLoading("secondarySm")}
                onClick={() => handleButtonClick("secondarySm")}
              >
                Secondary
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="danger"
                isLoading={isLoading("danger")}
                onClick={() => handleButtonClick("danger")}
              >
                Danger
              </Button>
              <Button
                variant="danger"
                size="sm"
                isLoading={isLoading("dangerSm")}
                onClick={() => handleButtonClick("dangerSm")}
              >
                Danger
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="warning"
                isLoading={isLoading("warning")}
                onClick={() => handleButtonClick("warning")}
              >
                Warning
              </Button>
              <Button
                variant="warning"
                size="sm"
                isLoading={isLoading("warningSm")}
                onClick={() => handleButtonClick("warningSm")}
              >
                Warning
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                isLoading={isLoading("ghost")}
                onClick={() => handleButtonClick("ghost")}
              >
                Ghost
              </Button>
              <Button
                variant="ghost"
                size="sm"
                isLoading={isLoading("ghostSm")}
                onClick={() => handleButtonClick("ghostSm")}
              >
                Ghost
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="link"
                isLoading={isLoading("link")}
                onClick={() => handleButtonClick("link")}
              >
                Link
              </Button>
              <Button
                variant="link"
                size="sm"
                isLoading={isLoading("linkSm")}
                onClick={() => handleButtonClick("linkSm")}
              >
                Link
              </Button>
            </div>
          </Card.Content>
        </Card.Root>
      </div>
    </SettingsLayout.Root>
  );
};

export default ButtonPage;
