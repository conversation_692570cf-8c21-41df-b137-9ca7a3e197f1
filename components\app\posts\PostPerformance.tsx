import { MinusIcon } from "@heroicons/react/24/outline";
import { Channel } from "@prisma/client";
import Link from "next/link";
import { CHANNEL_ICON_MAP } from "~/types";

const PostPerformance = ({
  channel,
  stats,
  url,
}: {
  channel: Channel;
  stats?:
    | {
        label: string;
        value: React.ReactNode | string | number | null | undefined;
      }[]
    | null;
  url: string | null;
}) => {
  if (url === null) {
    return null;
  }

  return (
    <div className="w-full pb-4">
      <div className="flex items-center gap-2 pb-1">
        {stats && (
          <div className="font-body-sm font-medium">Post Performance</div>
        )}
        <Link
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="text-basic font-body-sm group relative inline-flex items-center justify-start overflow-hidden rounded-full py-1 pr-6 pl-2 font-medium transition-all duration-150 ease-in-out"
        >
          <span className="bg-basic/10 absolute bottom-0 left-0 h-0 w-full transition-all duration-150 ease-in-out group-hover:h-full"></span>
          <span className="absolute right-1 duration-200 ease-out group-hover:translate-x-6">
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M14 5l7 7m0 0l-7 7m7-7H3"
              ></path>
            </svg>
          </span>
          <span className="absolute right-3 -translate-x-6 pl-2.5 opacity-0 duration-200 ease-out group-hover:translate-x-0 group-hover:opacity-100">
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M14 5l7 7m0 0l-7 7m7-7H3"
              ></path>
            </svg>
          </span>
          <span className="relative w-full text-left transition-colors duration-200 ease-in-out hover:pr-2">
            View post on {CHANNEL_ICON_MAP[channel].label}
          </span>
        </Link>
      </div>
      {stats && (
        <div className="flex w-full flex-wrap justify-between py-3.5 sm:flex-nowrap">
          {stats.map((stat) => {
            return (
              <div key={stat.label}>
                <div className="font-eyebrow text-medium-gray">
                  {stat.label}
                </div>
                {stat.value != null ? (
                  <h4>{stat.value}</h4>
                ) : (
                  <MinusIcon className="text-medium-gray mt-1 h-4 w-4" />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default PostPerformance;
