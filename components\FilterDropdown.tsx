import { Fragment, ReactElement, useState } from "react";

import { Menu, Transition } from "@headlessui/react";

import { Bars3BottomLeftIcon } from "@heroicons/react/24/solid";
import classNames from "classnames";
import { Checkbox, TextInput } from "~/components";
import Section from "./Section";

type Option = {
  label: string;
  value: string | number;
  component?: ReactElement;
  selected?: boolean;
  onClick: () => void;
};

type OptionSection = {
  name: string;
  options: Option[];
};

type Props = {
  sections: OptionSection[];
};

const FilterDropdown = ({ sections }: Props) => {
  const totalSelected = sections
    .map((section) => section.options)
    .flat()
    .filter((option) => option.selected).length;

  return (
    <Menu as="div" className="relative inline-block text-left">
      <Menu.Button className="font-body-sm bg-slate text-basic hover:bg-light-gray flex items-center gap-1 rounded-md px-1.5 py-1 transition-colors">
        <Bars3BottomLeftIcon className="h-4 w-4" />
        Filter{" "}
        {totalSelected > 0 && (
          <span className="mt-0.5 text-xs">({totalSelected})</span>
        )}
      </Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="bg-surface box-shadow absolute left-0 z-10 mt-2 w-80 origin-top-left rounded-md px-4 pt-4 pb-2">
          {sections.map((section, index) => {
            const [filterText, setFilterText] = useState<string>("");

            const numSelectedOptions = section.options.filter(
              (option) => option.selected
            ).length;

            return (
              <div
                className={classNames("border-b py-3", {
                  "border-lightest-gray": index < sections.length - 1,
                  "border-transparent": index === sections.length - 1,
                })}
                key={section.name}
              >
                <Section
                  title={
                    <div className="font-eyebrow flex items-center gap-1.5">
                      <div className="ml-0.5">{section.name}</div>
                      <div
                        className={classNames(
                          "bg-secondary text-secondary-light font-eyebrow-sm flex h-4 w-4 items-center justify-center rounded-full transition-opacity",
                          {
                            "opacity-0": numSelectedOptions === 0,
                            "opacity-100": numSelectedOptions !== 0,
                          }
                        )}
                      >
                        {numSelectedOptions}
                      </div>
                    </div>
                  }
                >
                  <TextInput
                    value={filterText}
                    onChange={setFilterText}
                    placeholder="Search..."
                  />
                  <div className="pt-1">
                    {section.options
                      .filter((option) =>
                        option.label
                          .toLowerCase()
                          .includes(filterText.toLowerCase())
                      )
                      .map((option) => {
                        return (
                          <button
                            key={option.value}
                            onClick={() => option.onClick()}
                            className="font-body-sm hover:bg-lightest-gray-30 flex w-full items-center gap-1.5 rounded-md p-2 transition-all"
                          >
                            <Checkbox
                              value={!!option.selected}
                              onChange={() => {}}
                            />
                            {option.component ? option.component : option.label}
                          </button>
                        );
                      })}
                  </div>
                </Section>
              </div>
            );
          })}
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

export default FilterDropdown;
