import { PlusIcon } from "@heroicons/react/24/outline";
import { AiPromptInputType, Channel } from "@prisma/client";
import { motion, useMotionTemplate, useMotionValue } from "framer-motion";
import groupBy from "lodash/groupBy";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { ReactElement, useCallback, useEffect } from "react";
import ChannelIcon from "~/components/app/ChannelIcon";
import Button from "~/components/ui/Button";
import { joinWithOr } from "~/utils";

type AiPrompt = {
  id: string;
  name: string;
  description: string;
  category: string;
  inputTypes: AiPromptInputType[];
  channels: Channel[];
};

type Props = {
  title: string;
  description?: string | ReactElement;
  prompts: AiPrompt[];
  onNewPromptClick?: () => void;
  onPromptPrefetch: (promptID: string) => void;
};

const PromptList = ({
  title,
  description,
  prompts,
  onNewPromptClick,
  onPromptPrefetch,
}: Props) => {
  const router = useRouter();
  const groupedPrompts = groupBy(prompts, (prompt) => prompt.category);

  const CATEGORY_ORDER = [
    "General",
    "Product Marketing",
    "Founder Marketing",
    "Trending Formats",
    "Announcements",
  ];

  Object.keys(groupedPrompts).forEach((category) => {
    if (!CATEGORY_ORDER.includes(category)) {
      CATEGORY_ORDER.push(category);
    }
  });

  return (
    <div>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <div className="font-body font-medium">{title}</div>
            <div className="font-body-sm text-medium-dark-gray">
              {description}
            </div>
          </div>
          {onNewPromptClick && (
            <Button onClick={onNewPromptClick} size="sm">
              <PlusIcon className="h-4 w-4" />
              New Prompt
            </Button>
          )}
        </div>
        <div className="space-y-8 pt-8 pb-20">
          {CATEGORY_ORDER.map((category) => {
            const prompts = groupedPrompts[category] || [];
            return (
              prompts.length > 0 && (
                <div key={category}>
                  <div className="bg-light-green font-eyebrow w-fit rounded-full px-3 py-1 text-white">
                    {category}
                  </div>
                  <div className="grid grid-cols-3 gap-2 pt-4">
                    {prompts.map((prompt) => (
                      <PromptCard
                        key={prompt.id}
                        prompt={prompt}
                        href={router.asPath + "/" + prompt.id}
                        onPromptPrefetch={onPromptPrefetch}
                      />
                    ))}
                  </div>
                </div>
              )
            );
          })}
        </div>
      </div>
    </div>
  );
};

const PromptCard = ({
  prompt,
  href,
  onPromptPrefetch,
}: {
  prompt: {
    id: string;
    name: string;
    description: string;
    inputTypes: AiPromptInputType[];
    channels: Channel[];
  };
  href: string;
  onPromptPrefetch: (promptID: string) => void;
}) => {
  const gradientSize = 200;
  const gradientColor = "#E7F4EF";
  const gradientOpacity = 0.8;
  const mouseX = useMotionValue(-gradientSize);
  const mouseY = useMotionValue(-gradientSize);

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const { left, top } = e.currentTarget.getBoundingClientRect();
      mouseX.set(e.clientX - left);
      mouseY.set(e.clientY - top);
    },
    [mouseX, mouseY]
  );

  const handleMouseLeave = useCallback(() => {
    mouseX.set(-gradientSize);
    mouseY.set(-gradientSize);
  }, [mouseX, mouseY, gradientSize]);

  useEffect(() => {
    mouseX.set(-gradientSize);
    mouseY.set(-gradientSize);
  }, [mouseX, mouseY, gradientSize]);

  return (
    <Link href={href} onMouseEnter={() => onPromptPrefetch(prompt.id)}>
      <div
        key={prompt.id}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        className="group border-light-gray hover:border-basic relative flex rounded-lg border bg-white p-4 transition-all"
      >
        <div className="z-10">
          <div className="font-body-sm font-medium">{prompt.name}</div>
          <div className="font-body-sm text-medium-dark-gray pt-1">
            {prompt.description}
          </div>
          <div className="flex items-center gap-6 pt-4">
            <div>
              <div className="font-eyebrow-sm text-medium-dark-gray">Input</div>
              <div className="font-body-sm">
                {joinWithOr(prompt.inputTypes)}
              </div>
            </div>
            <div>
              <div className="font-eyebrow-sm text-medium-dark-gray">
                Output
              </div>
              <div className="flex items-center gap-1">
                {prompt.channels.map((channel) => {
                  return (
                    <ChannelIcon key={channel} channel={channel} width={16} />
                  );
                })}
              </div>
            </div>
          </div>
        </div>
        <motion.div
          className="pointer-events-none absolute -inset-px rounded-lg opacity-0 transition-opacity duration-300 group-hover:opacity-100"
          style={{
            background: useMotionTemplate`
              radial-gradient(${gradientSize}px circle at ${mouseX}px ${mouseY}px, ${gradientColor}, transparent 100%)
            `,
            opacity: gradientOpacity,
          }}
        />
      </div>
    </Link>
  );
};

export default PromptList;
