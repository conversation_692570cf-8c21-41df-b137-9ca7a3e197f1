import { FloatingToolbar as LiveblocksFloatingToolbar } from "@liveblocks/react-tiptap";
import { Editor } from "@tiptap/react";
import React, { ReactNode, useEffect, useState } from "react";

type CustomFloatingToolbarProps = {
  editor: Editor | null;
  shouldHideWithLink?: boolean;
  children?: ReactNode;
};

const CustomFloatingToolbar = ({
  editor,
  shouldHideWithLink,
  children,
}: CustomFloatingToolbarProps) => {
  const [hasLink, setHasLink] = useState(false);

  useEffect(() => {
    if (!editor) return;

    const checkForLinks = () => {
      const { from, to } = editor.state.selection;

      let selectionHasLink = false;

      if (from !== to) {
        editor.state.doc.nodesBetween(from, to, (node) => {
          if (node.marks?.some((mark) => mark.type.name === "link")) {
            selectionHasLink = true;
            return false;
          }
          return true;
        });
      }

      setHasLink(selectionHasLink);
    };

    const onSelectionChange = checkForLinks;

    checkForLinks();

    editor.on("selectionUpdate", onSelectionChange);
    editor.on("update", onSelectionChange);
    editor.on("focus", onSelectionChange);

    return () => {
      editor.off("selectionUpdate", onSelectionChange);
      editor.off("update", onSelectionChange);
      editor.off("focus", onSelectionChange);
    };
  }, [editor]);

  if (!editor) {
    return null;
  }

  const filteredChildren = React.Children.toArray(children).filter((child) => {
    // Always keep SectionCollaboration
    if (
      React.isValidElement(child) &&
      child.type.toString().includes("SectionCollaboration")
    ) {
      return true;
    }

    // Filter out formatting buttons when a link is selected
    return !hasLink || !shouldHideWithLink;
  });

  return (
    <LiveblocksFloatingToolbar editor={editor}>
      {filteredChildren}
    </LiveblocksFloatingToolbar>
  );
};

export default CustomFloatingToolbar;
