import {
  LinkIcon,
  PencilSquareIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { Toolbar } from "@liveblocks/react-tiptap";
import * as Popover from "@radix-ui/react-popover";
import { Editor } from "@tiptap/react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { TextInput } from "~/components";
import Button from "~/components/ui/Button";
import { trpc } from "~/utils";

type LinkMenuProps = {
  editor: Editor;
};

const LinkMenu = ({ editor }: LinkMenuProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [state, setState] = useState<"edit" | "display">("edit");
  const [link, setLink] = useState<string | null>(null);
  const buttonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateLinkState = () => {
      if (editor.isActive("link")) {
        const href = editor.getAttributes("link")?.href;
        if (href && isOpen) {
          setLink(href);
          setState("display");
        }
      }
    };

    editor.on("selectionUpdate", updateLinkState);

    return () => {
      editor.off("selectionUpdate", updateLinkState);
    };
  }, [editor, isOpen]);

  const linkPreviewQuery = trpc.linkPreview.getUrlData.useQuery(
    {
      url: link,
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      enabled: !!link,
    }
  );

  const metadata = linkPreviewQuery.data?.metadata;
  const title = metadata?.title || "";
  const image = metadata?.image;

  const hasLink = editor.isActive("link");

  const handleSaveLink = () => {
    if (link) {
      const linkWithProtocol = link.startsWith("http")
        ? link
        : `https://${link}`;

      const result = editor
        .chain()
        .extendMarkRange("link")
        .setLink({ href: linkWithProtocol })
        .run();
      if (result) {
        editor.chain().focus().run();
        setIsOpen(false);
      } else {
        return toast.error("Invalid link", {
          description: "Please enter a valid link",
        });
      }
    } else {
      editor.chain().focus().unsetLink().run();
      setIsOpen(false);
    }
  };

  const handleButtonClick = () => {
    const href = editor.getAttributes("link")?.href;
    setLink(href ?? null);
    const newState = href ? "display" : "edit";
    setState(newState);
    setIsOpen(!isOpen);
  };

  return (
    <Popover.Root open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        <div ref={buttonRef}>
          <Toolbar.Button
            onClick={handleButtonClick}
            name="Link"
            icon={<LinkIcon className="h-4 w-full stroke-2" />}
          />
        </div>
      </Popover.Trigger>
      <Popover.Content
        align="center"
        sideOffset={28}
        className="animate-in fade-in zoom-in-95 w-min min-w-80 transform rounded-lg bg-white p-4 shadow-xl transition-all duration-100 ease-out"
        onMouseDown={(e) => e.preventDefault()}
      >
        {state === "edit" && (
          <div className="flex items-center gap-2">
            <TextInput
              autoFocus={true}
              type="text"
              kind="outline"
              placeholder="Enter link"
              value={link ?? ""}
              onChange={(newValue) => setLink(newValue)}
              onEnterPress={handleSaveLink}
              onEscPress={() => {
                setIsOpen(false);
                editor.commands.focus();
              }}
            />
            <Button size="sm" onClick={handleSaveLink}>
              Save
            </Button>
          </div>
        )}
        {state === "display" && (
          <div>
            <div className="font-body-sm flex items-center justify-between gap-4">
              <div>
                <div className="font-medium whitespace-nowrap">{title}</div>
                <div className="text-medium-dark-gray">{link}</div>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setState("edit")}
                >
                  <PencilSquareIcon className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="warning"
                  onClick={() => {
                    editor.chain().focus().unsetLink().run();
                    setIsOpen(false);
                  }}
                >
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
            {image != null && (
              <img
                src={image ?? ""}
                alt={title}
                className="mt-2 h-auto w-full rounded-md"
              />
            )}
          </div>
        )}
      </Popover.Content>
    </Popover.Root>
  );
};

export default LinkMenu;
