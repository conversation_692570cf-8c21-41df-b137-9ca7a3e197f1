import notifyOfScheduledPost from "apiUtils/notifyOfScheduledPost";
import publishTweetThread from "apiUtils/twitter/publishTweetThread";
import { NonRetriableError, RetryAfterError, slugify } from "inngest";
import { db } from "~/clients";
import { inngest } from "../inngest";

const RETRIES = 0;

export default inngest.createFunction(
  {
    id: slugify("Twitter Publish Post"),
    retries: RETRIES,
    name: "Twitter Publish Post",
    concurrency: {
      limit: 1,
      key: "event.data.postID",
    },
    cancelOn: [
      {
        event: "post.rescheduled",
        if: `event.data.channel == "Twitter" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.unscheduled",
        if: `event.data.channel == "Twitter" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.deleted",
        match: "data.postID",
      },
    ],
    onFailure: async ({ error, event, step }) => {
      const data = event.data.event.data;
      const { postID } = data;
      console.log(`Post:${postID}: Final Error ${error.message}`);

      const post = await db.post.findFirstOrThrow({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          twitterIntegration: {
            select: {
              account: {
                select: {
                  username: true,
                },
              },
            },
          },
          scheduledPosts: {
            where: {
              channel: "Twitter",
              status: "Scheduled",
            },
          },
        },
      });

      const scheduledPost = post.scheduledPosts[0];

      if (scheduledPost == null) {
        console.log(`Post:${postID}: No longer scheduled`);
        return;
      }

      await notifyOfScheduledPost({
        post,
        scheduledPost,
        status: "Error",
        account: post.twitterIntegration?.account
          ? {
              name: post.twitterIntegration.account.username,
              url: `https://x.com/${post.twitterIntegration.account.username}`,
            }
          : undefined,
        error: error.message,
      });
    },
  },
  [
    {
      event: "post.scheduled",
      if: `event.data.channel == "Twitter"`,
    },
    {
      event: "post.rescheduled",
      if: `event.data.channel == "Twitter"`,
    },
  ],
  async ({ event, step, attempt }) => {
    const { delayUntil, postID } = event.data;

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    const response = await step.run("Publish Twitter Post", async () => {
      console.log(
        `Post:${postID}: Publishing Twitter Post Attempt ${attempt + 1}/${
          RETRIES + 1
        }`
      );

      const post = await db.post.findFirst({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          workspaceID: true,
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
              slackWebhooks: {
                where: {
                  type: "SuccessfulPost",
                },
                select: {
                  type: true,
                  url: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          createdByID: true,
          twitterIntegration: {
            select: {
              accessToken: true,
              accessSecret: true,
              account: {
                select: {
                  username: true,
                },
              },
            },
          },
          tweets: {
            select: {
              id: true,
              copy: true,
              threadPosition: true,
              assets: {
                select: {
                  id: true,
                  position: true,
                  altText: true,
                  asset: true,
                },
                orderBy: {
                  position: "asc",
                },
              },
              mediaTaggedUsernames: true,
              engagements: {
                select: {
                  id: true,
                  type: true,
                  postDelaySeconds: true,
                },
              },
            },
          },
          scheduledPosts: {
            where: {
              channel: "Twitter",
              status: "Scheduled",
            },
          },
          approvals: {
            where: {
              isBlocking: true,
              approvedAt: null,
            },
            select: {
              id: true,
            },
          },
        },
      });

      if (post == null) {
        console.log(`Post:${postID}: Post not found`);
        return {
          status: "Skipped",
          comment: "Post has been deleted",
        };
      }

      const scheduledPost = post.scheduledPosts[0];

      if (scheduledPost == null) {
        console.log(`Post:${postID}: No longer scheduled`);
        return {
          status: "Skipped",
          comment: "No scheduled posts",
        };
      }

      if (post.approvals.length > 0) {
        console.log(`Post:${postID}: Blocked by approval`);
        return {
          status: "Skipped",
          comment: "Post has been blocked by approval",
        };
      }

      const publishResponse = await publishTweetThread(post);

      if (publishResponse.status === "NonRetriableError") {
        console.log(
          `Post:${postID}: NonRetriableError ${publishResponse.error}`
        );

        throw new NonRetriableError(publishResponse.error);
      } else if (publishResponse.status === "Error") {
        console.log(
          `Post:${postID}: Publish Attempt ${attempt + 1}/${
            RETRIES + 1
          } Error ${publishResponse.error}`
        );

        throw new RetryAfterError(publishResponse.error, 10_000);
      }

      console.log(`Post:${postID}: Published Twitter Post`);

      await notifyOfScheduledPost({
        post,
        scheduledPost,
        account: {
          name: `@${post.twitterIntegration!.account.username}`,
          url: `https://x.com/${post.twitterIntegration!.account.username}`,
        },
        ...publishResponse,
      });

      return publishResponse;
    });

    return response;
  }
);
