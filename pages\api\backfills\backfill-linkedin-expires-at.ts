import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import { DateTime } from "luxon";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const accounts = await db.linkedInAccount.findMany({
    where: {
      profileImageExpiresAt: null,
    },
  });

  const IDsToDelete = accounts
    .filter((account) => account.urn.includes("member"))
    .map((account) => account.id);

  const { count } = await db.linkedInAccount.deleteMany({
    where: {
      id: {
        in: IDsToDelete,
      },
    },
  });

  for await (const account of accounts) {
    if (IDsToDelete.includes(account.id)) {
      continue;
    }

    console.log("Updating", account.name);

    const expiresAtRegex = /e=(\d+)/;
    const match = expiresAtRegex.exec(account.profileImageUrl);
    if (!match) {
      console.log(account.id, "no match", account.profileImageUrl);
      continue;
    }

    const expiresAt = DateTime.fromSeconds(parseInt(match[1])).toJSDate();

    await db.linkedInAccount.update({
      where: {
        id: account.id,
      },
      data: {
        profileImageExpiresAt: expiresAt,
      },
    });
  }

  res.status(StatusCodes.OK).send({ deleted: count });
};

export default handler;
