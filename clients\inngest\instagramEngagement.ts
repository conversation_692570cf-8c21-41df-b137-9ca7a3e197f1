import { NonRetriableError, slugify } from "inngest";
import { db, slack } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import Instagram from "../facebook/instagram/Instagram";
import { inngest } from "./inngest";

export default inngest.createFunction(
  {
    id: slugify("Instagram Post Engagement"),
    name: "Instagram Post Engagement",
    onFailure: async ({ error, event }) => {
      const data = event.data.event.data;
      const runID = event.data.run_id;
      const { engagementID, permalink } = data;
      console.log(
        `Instagram Engagement:${engagementID}: Final Error ${error.message}`
      );

      const engagement = await db.instagramPostEngagement.findFirst({
        where: {
          id: engagementID,
        },
        select: {
          integration: {
            select: {
              account: {
                select: {
                  username: true,
                },
              },
            },
          },
          content: {
            select: {
              post: {
                select: {
                  id: true,
                  title: true,
                  workspace: {
                    select: {
                      name: true,
                      company: {
                        select: { name: true },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!engagement) {
        console.error(
          `Instagram Engagement:${engagementID}: Engagement deleted`
        );
        return;
      } else {
        const account = engagement.integration.account;

        const profile = {
          name: `@${account.username}`,
          url: `https://www.instagram.com/${account.username}`,
        };

        await slack.uncaughtFailedEngagement({
          channel: "Instagram",
          post: engagement.content.post,
          postUrl: permalink,
          workspace: engagement.content.post.workspace,
          engagementID,
          error: error.message,
          profile,
          runID,
        });
      }
    },
  },
  { event: "instagram.postEngagement" },
  async ({ event, step }) => {
    const {
      delayUntil,
      engagementID,
      externalPostID: instagramPostID,
      permalink,
    } = event.data;

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    await step.run("Post Instagram Post Engagement", async () => {
      console.log(`Instagram Engagement:${engagementID}: Finding record`);
      const engagement = await db.instagramPostEngagement.findFirst({
        where: {
          id: engagementID,
        },
        select: {
          id: true,
          type: true,
          copy: true,
          url: true,
          postDelaySeconds: true,
          content: {
            select: {
              post: {
                select: {
                  id: true,
                  createdByID: true,
                  workspace: {
                    select: {
                      id: true,
                      companyID: true,
                    },
                  },
                },
              },
            },
          },
          integration: {
            select: {
              accessToken: true,
              integrationSource: true,
            },
          },
        },
      });
      if (!engagement) {
        return {
          skipped: true,
          reason: "Engagement deleted",
        };
      }

      console.log(`Instagram Engagement:${engagementID}: Found record`);

      if (engagement.url != null) {
        console.log(
          `Instagram Engagement:${engagementID}: Already commented on post, skipping`
        );
        throw new NonRetriableError("Already commented on post");
      }

      const engagementClient = new Instagram(
        engagement.integration.accessToken,
        {
          platform: engagement.integration.integrationSource,
        }
      );

      const { copy } = engagement;

      if (!copy || copy.length === 0) {
        console.log(
          `Instagram Engagement:${engagementID}: Nothing to post, skipping`
        );
        return {
          skipped: true,
          reason: "Nothing to post",
        };
      } else if (engagement.type === "Like" || engagement.type === "Repost") {
        return {
          skipped: true,
          reason: `${engagement.type} engagement not supported`,
        };
      } else {
        console.log(`Instagram Engagement:${engagementID}: Creating comment`);
        const response = await engagementClient.comment({
          mediaID: instagramPostID,
          message: copy,
        });

        if (response.errors) {
          const error = response.errors.join(", ");
          console.log(
            `Instagram Engagement:${engagementID}: Errored: ${error}`
          );
          throw new Error(error);
        } else {
          console.log(
            `Instagram Engagement:${engagementID}: Posted to ${permalink}`
          );
          await db.instagramPostEngagement.update({
            where: {
              id: engagement.id,
            },
            data: {
              url: permalink,
            },
          });
          posthog.capture({
            distinctId: engagement.content.post.createdByID,
            event: POSTHOG_EVENTS.engagement.published,
            properties: {
              channel: "Instagram",
              engagementType: engagement.type,
              postID: engagement.content.post.id,
              delaySeconds: engagement.postDelaySeconds,
            },
            groups: {
              workspace: engagement.content.post.workspace.id,
              company: engagement.content.post.workspace.companyID,
            },
          });
          return {
            url: permalink,
          };
        }
      }
    });
  }
);
