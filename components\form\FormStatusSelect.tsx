import { Controller, Path } from "react-hook-form";

import { Label } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import { StatusSelect } from "~/components/app/posts";
import camelCaseToWords from "~/utils/camelCaseToWords";

type FormStatusSelectProps<T> = {
  control: any;
  name: Path<T>;
  label?: string | JSX.Element;
  isOptional?: boolean;
  tooltipText?: string;
  rules?: Rules;
};

const FormStatusSelect = <T,>({
  control,
  name,
  label,
  tooltipText,
  rules = {},
}: FormStatusSelectProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <Label
            value={label}
            isRequired={!!rules.required}
            tooltipText={tooltipText}
          >
            <>
              <StatusSelect value={value} onChange={onChange} />
              <Error error={error} />
            </>
          </Label>
        );
      }}
    />
  );
};

export default FormStatusSelect;
