import { DateTime } from "luxon";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { useWorkspace } from "~/providers";
import { Slackintegration } from "./SlackPost";

type Props = {
  content: {
    copy?: string;
    assets: {
      name: string;
      signedUrl: string;
      metadata: {
        mimetype: string;
      };
      createdAt: Date;
    }[];
  };
  connectedAccount: Slackintegration | null | undefined;
};

const SlackPostPreview = ({ content, connectedAccount }: Props) => {
  const { currentWorkspace } = useWorkspace();
  const asset = content?.assets?.[0];
  const nonEmpty = (content.copy && content.copy != "") || asset != null;

  const copy = (content?.copy || "")
    .replaceAll(/\*(.*?)\*/g, "**$1**")
    .replace(/<([^|>]+)\|([^>]+)>/g, "[$2]($1)");

  if (!nonEmpty) {
    return (
      <div className="font-body text-medium-dark-gray mt-1">
        Preview will show when post content is added below
      </div>
    );
  } else {
    return (
      <div className="flex min-h-[300px] items-center justify-center">
        <div className="font-lato scrollbar-hidden h-fit max-h-[600px] w-[540px] space-y-4 overflow-auto rounded-lg bg-white py-3 text-[15px] text-[#1D1C1D]">
          <div className="font-smoothing flex items-start gap-2 px-4 leading-4">
            <img
              className="h-10 w-10 rounded-[4px]"
              src={
                connectedAccount?.profileImageUrl ||
                "/integrations/slack/default_user.png"
              }
            />
            <div className="flex flex-col items-start">
              <div className="flex items-baseline gap-1">
                <span className="font-semibold">
                  {connectedAccount?.displayName || currentWorkspace?.name}
                </span>
                <div
                  key={DateTime.now().toFormat("h:mm a")}
                  className="text-[12px] text-[#616061]"
                >
                  {DateTime.now().toFormat("h:mm a")}
                </div>
              </div>
              <div className="leading-[22px]">
                {copy.split("\n").map((line, index) => {
                  if (line === "") {
                    return <div key={index} className="py-2" />;
                  } else {
                    let parsedLine = line;

                    const getSpan = (text: string) => {
                      return `<span className="bg-[#F2C74466] text-[#0B4C8C] rounded-[3px] p-px">${text}</span>`;
                    };

                    parsedLine = parsedLine
                      .replaceAll("<!here>", getSpan("@here"))
                      .replaceAll("<!everyone>", getSpan("@everyone"))
                      .replaceAll("<!channel>", getSpan("@channel"));

                    return (
                      <div key={index} dir="auto">
                        <ReactMarkdown
                          className="break-words"
                          linkTarget="_blank"
                          children={parsedLine}
                          remarkPlugins={[remarkGfm]}
                          rehypePlugins={[rehypeRaw]}
                          components={{
                            blockquote: ({ node, ...props }) => {
                              return (
                                <div className="flex items-center">
                                  <blockquote
                                    className="border-l-4 border-[#4E5058] pl-4"
                                    {...props}
                                  />
                                </div>
                              );
                            },
                            ol: ({ node, ...props }) => {
                              return (
                                <ol
                                  {...props}
                                  className="list-inside list-decimal"
                                />
                              );
                            },
                            ul: ({ node, ...props }) => {
                              return (
                                <ul
                                  {...props}
                                  className="list-inside list-disc"
                                />
                              );
                            },
                            code: ({ node, ...props }) => {
                              const children = props.children;

                              if (
                                Array.isArray(children) &&
                                children.some(
                                  (child) =>
                                    typeof child === "string" &&
                                    child.includes("•")
                                )
                              ) {
                                const initialSpaces = "    ";
                                const indentationLevels = children.map(
                                  (child) => {
                                    if (typeof child === "string") {
                                      let count = 0;
                                      let pos = 0;
                                      while (
                                        child.indexOf(initialSpaces, pos) ===
                                        pos
                                      ) {
                                        count++;
                                        pos += initialSpaces.length;
                                      }
                                      return count;
                                    }
                                    return 0;
                                  }
                                );
                                return (
                                  <ul
                                    style={{
                                      marginLeft: `${
                                        (indentationLevels[0] + 1) * 32
                                      }px`,
                                    }}
                                    className="font-lato"
                                    {...props}
                                  />
                                );
                              } else {
                                return (
                                  <code
                                    className="bg-lightest-gray/40 body border-lightest-gray rounded-[3px] border p-0.5 text-[12px] text-[#E01E5A]"
                                    {...props}
                                  />
                                );
                              }
                            },
                            strong: ({ node, ...props }) => {
                              return (
                                <span className="font-semibold" {...props} />
                              );
                            },
                            a: ({ node, ...props }) => {
                              return (
                                <a
                                  className="text-[#00AAFC] hover:underline hover:decoration-[#00AAFC]"
                                  {...props}
                                />
                              );
                            },
                          }}
                        />
                      </div>
                    );
                  }
                })}
              </div>
              {asset != null && (
                <>
                  {asset.metadata.mimetype.includes("image") ? (
                    <div className="w-full">
                      <div className="flex w-full items-center gap-1">
                        <div className="text-[#1D1C1DB3 text-[13px]">
                          {asset.name}
                        </div>
                        <svg
                          data-1qi="true"
                          aria-hidden="true"
                          viewBox="0 0 20 20"
                          className="h-4 w-4"
                        >
                          <path
                            fill="currentColor"
                            fillRule="evenodd"
                            d="M13.75 7.25h-7.5a.75.75 0 0 0-.576 1.23l3.75 4.5a.75.75 0 0 0 1.152 0l3.75-4.5a.75.75 0 0 0-.576-1.23Z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </div>
                      <img
                        className="mt-1 w-full rounded-[4px]"
                        src={asset.signedUrl}
                      />
                    </div>
                  ) : (
                    <div>
                      <div></div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default SlackPostPreview;
