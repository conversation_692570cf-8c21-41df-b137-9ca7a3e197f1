import { ArrowsRightLeftIcon, PlusIcon } from "@heroicons/react/24/outline";
import { Channel, PostStatus } from "@prisma/client";
import { useCallback, useEffect, useRef, useState } from "react";
import Tooltip from "~/components/Tooltip";
import Button from "~/components/ui/Button";
import Separator from "~/components/ui/Separator";
import { EDITOR_CHANNELS } from "~/constants";
import { usePostIntegrations } from "~/providers";
import { EditorChannel } from "~/providers/PostIntegrationsProvider";
import { handleTrpcError, trpc } from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import ChannelIcon from "../ChannelIcon";
import ChannelProfileSelect from "./ChannelProfileSelect";
import ChannelSelect from "./ChannelSelect";
import { ScheduledPost } from "./SchedulePostAccountSelect";
import SyncContentModal from "./SyncContentModal";

type Props = {
  post: {
    id: string;
    title: string;
    status: PostStatus;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    scheduledPosts: ScheduledPost[];
  } & PostOrIdea;
  isReadOnly: boolean;
};

const CrossPostBar = ({ post, isReadOnly }: Props) => {
  const trpcUtils = trpc.useUtils();
  const [syncContentModalOpen, setSyncContentModalOpen] =
    useState<boolean>(false);

  const [
    isChannelProfileSelectOverflowed,
    setIsChannelProfileSelectOverflowed,
  ] = useState<boolean>(false);

  const channelSelectRef = useRef<HTMLDivElement>(null);
  const crossPostRef = useRef<HTMLDivElement>(null);

  const checkOverflow = useCallback(() => {
    const windowWidth = window.innerWidth;
    const channelCount = post.channels.length;

    const shouldOverflow =
      channelCount >= 4 || (channelCount >= 3 && windowWidth < 1280);

    setIsChannelProfileSelectOverflowed(shouldOverflow);
  }, [post.channels.length]);

  useEffect(() => {
    const handleResize = () => checkOverflow();

    window.addEventListener("resize", handleResize);
    checkOverflow();

    return () => window.removeEventListener("resize", handleResize);
  }, [checkOverflow]);

  const mostRecentlyUsedQuery = trpc.post.getMostRecentlyUsed.useQuery();
  const updatePostMutation = trpc.post.update.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Failed to Update Post", error });
    },
    onSuccess: (data) => {
      if (post.isIdea) {
        trpcUtils.idea.getAll.refetch();
      } else {
        trpcUtils.post.getMany.refetch();
      }
    },
  });

  const {
    currentChannel,
    setCurrentChannel,
    connectedIntegrations,
    selectedIntegrations,
    setSelectedIntegration,
    isLoading,
    syncChannels,
    handleChannelRemoval,
    channelSyncState,
  } = usePostIntegrations();

  const mainChannel = post.channels[0];
  const hasTwoEditorChannels =
    post.channels.filter((channel) => EDITOR_CHANNELS.includes(channel))
      .length > 1;

  const updateChannels = async (newChannels: Channel[]) => {
    const removedChannels = post.channels.filter(
      (channel) => !newChannels.includes(channel)
    );

    handleChannelRemoval(removedChannels);

    const addedChannels = newChannels.filter(
      (channel) => !post.channels.includes(channel)
    );
    syncChannels(addedChannels);

    const newChannelsInOrder = [
      ...post.channels.filter((channel) => newChannels.includes(channel)),
      ...addedChannels,
    ];

    const currentPostData = trpcUtils.post.get.getData({ id: post.id });
    const currentPost = currentPostData?.post;
    if (currentPost) {
      await trpcUtils.post.get.cancel({
        id: post.id,
      });

      trpcUtils.post.get.setData(
        { id: post.id },
        {
          post: {
            ...currentPost,
            channels: newChannelsInOrder,
          },
          isReadOnly,
        }
      );
      trpcUtils.post.getIntegrations.refetch({
        id: post.id,
      });
    }

    updatePostMutation.mutate(
      {
        id: post.id,
        channels: newChannelsInOrder,
      },
      {
        onSuccess: () => {
          trpcUtils.post.getMany.invalidate();
          trpcUtils.post.getIntegrations.refetch({
            id: post.id,
          });
        },
      }
    );
  };

  const getIntegrationOptionsForChannel = (channel: Channel) => {
    if (!EDITOR_CHANNELS.includes(channel)) {
      return [];
    }

    const integrations = connectedIntegrations[channel as EditorChannel] || [];

    return integrations.map((integration) => ({
      key: integration.id,
      value: integration.label,
      label: (
        <div className="flex items-center gap-1.5">
          <ChannelIcon channel={channel} width={14} showLabel={false} />
          <span className="font-body-sm">{integration.label}</span>
        </div>
      ),
      labelText: integration.label,
    }));
  };

  return (
    <div className="border-lightest-gray relative flex flex-row items-center rounded-[4px] border p-[3px] lg:max-w-[622px]">
      <div
        ref={channelSelectRef}
        className="scrollbar-hidden flex min-w-0 flex-1 flex-row flex-wrap gap-1 overflow-x-auto xl:flex-nowrap"
      >
        {post.channels.map((channel) => (
          <ChannelProfileSelect
            key={channel}
            channel={channel}
            isSelected={channel === currentChannel}
            onSelect={() => setCurrentChannel(channel)}
            integrationOptions={getIntegrationOptionsForChannel(channel)}
            selectedIntegration={
              EDITOR_CHANNELS.includes(channel as EditorChannel)
                ? selectedIntegrations[channel as EditorChannel]
                : null
            }
            onSelectIntegration={(integrationID) =>
              EDITOR_CHANNELS.includes(channel as EditorChannel) &&
              setSelectedIntegration(channel as EditorChannel, integrationID)
            }
            isLoading={isLoading || channelSyncState[channel] === "Syncing"}
            isOverflowed={isChannelProfileSelectOverflowed}
          />
        ))}
      </div>
      {!isReadOnly && (
        <div
          ref={crossPostRef}
          className="mx-1 ml-1 flex h-full shrink-0 items-center gap-1 bg-white"
        >
          <ChannelSelect
            triggerButton={
              <div className="translate-y-0.5">
                <Button size="sm" variant="ghost">
                  <PlusIcon className="h-4 w-4" />
                  <span className="hidden sm:block">Cross-Post</span>
                </Button>
              </div>
            }
            disabledChannel={post.channels.length === 1 ? mainChannel : null}
            value={post.channels}
            isMulti={true}
            recentlyUsedChannels={
              mostRecentlyUsedQuery.data?.recentlyUsedChannels
            }
            onChange={async (newChannels) => {
              await updateChannels(newChannels);
            }}
          />
          <Separator orientation="vertical" className="block sm:hidden" />
          <Tooltip
            value={
              hasTwoEditorChannels
                ? null
                : "Add a cross-post channel to sync content across channels."
            }
          >
            <Button
              size="sm"
              variant="ghost"
              disabled={!hasTwoEditorChannels}
              onClick={() => setSyncContentModalOpen(true)}
            >
              <ArrowsRightLeftIcon className="h-4 w-4" />
              <span className="hidden sm:block">Sync</span>
            </Button>
          </Tooltip>
        </div>
      )}
      <SyncContentModal
        open={syncContentModalOpen}
        setOpen={setSyncContentModalOpen}
        post={post}
        defaultSourceChannel={currentChannel}
      />
    </div>
  );
};

export default CrossPostBar;
