import { v4 as uuidv4 } from "uuid";
import { Divider, TextLink } from "~/components";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import { useWindowTitle } from "~/hooks";
import { openIntercomChat } from "~/utils";
import { setCookie } from "~/utils/cookies";

type Props = {
  type: "scheduling" | "engagement";
};

const handleConnectLinkedInAccount = (accountUrn?: string) => {
  const state = uuidv4();

  setCookie(
    "LINKEDIN_INTEGRATION",
    {
      state,
      accountUrn,
      referrer: window.location.pathname + window.location.search,
    },
    {
      path: "/",
      secure: true,
      sameSite: "lax",
      maxAge: 5 * 60, // 5 minutes in seconds
    }
  );

  const params = new URLSearchParams({
    response_type: "code",
    client_id: process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID as string,
    redirect_uri: `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/linkedin/callback`,
    state,
    // From https://www.linkedin.com/developers/apps/*********/auth
    scope: [
      "r_member_postAnalytics",
      "r_organization_followers",
      "r_organization_social",
      "rw_organization_admin",
      "r_organization_social_feed",
      "w_member_social",
      "rw_events",
      "r_member_profileAnalytics",
      "w_organization_social",
      "r_basicprofile",
      "w_organization_social_feed",
      "r_events",
      "w_member_social_feed",
      "r_1st_connections_size",
    ].join(","),
  });

  window.location.href = `https://www.linkedin.com/oauth/v2/authorization?${params}`;
};

const LinkedInConnectPage = ({ type }: Props) => {
  useWindowTitle("LinkedIn Integration");

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      <ConnectProfile.Root>
        <ConnectProfile.Header
          channel="LinkedIn"
          title={
            type === "scheduling" ? "LinkedIn" : "LinkedIn (Engagement Only)"
          }
          description="Connect your LinkedIn profile to Assembly."
        />
        <ConnectProfile.Content>
          <Button size="sm" onClick={() => handleConnectLinkedInAccount()}>
            Connect LinkedIn
          </Button>
        </ConnectProfile.Content>
        <Divider className="my-2 md:my-4" />
        <ConnectProfile.Footer>
          <div className="space-y-1.5">
            <p className="font-medium">
              What permissions do I need to connect a LinkedIn page?
            </p>
            <p className="text-medium-dark-gray">
              You must be a Super Admin or Content Admin to connect a LinkedIn
              company page. Both personal LinkedIn profiles and company pages
              are supported.
            </p>
          </div>
          <div className="space-y-1.5">
            <p className="font-medium">Need Help?</p>
            <p className="text-medium-dark-gray">
              Check out our help center article for a video walkthrough on how
              to connect. If you're still having issues, please{" "}
              <Button
                size="sm"
                variant="link"
                onClick={() => {
                  openIntercomChat(
                    "I'm having trouble connecting my Instagram profile and getting this error: ERROR"
                  );
                }}
              >
                chat with us
              </Button>
              .
            </p>
          </div>
          <div className="space-y-1.5">
            <p className="font-medium">
              How do I login to another LinkedIn account to connect a profile?
            </p>
            <p className="text-medium-dark-gray">
              If you're logging in to someone else's LinkedIn account to connect
              a profile, you'll need to first logout on{" "}
              <TextLink href="https://linkedin.com" value="linkedin.com" />{" "}
              first, then hit connect on Assembly.
            </p>
          </div>
        </ConnectProfile.Footer>
      </ConnectProfile.Root>
    </SettingsLayout.Root>
  );
};

export default LinkedInConnectPage;
export { handleConnectLinkedInAccount };
