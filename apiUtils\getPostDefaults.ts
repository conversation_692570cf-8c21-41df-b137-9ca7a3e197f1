import { WebflowPreviewType } from "@prisma/client";
import { db } from "~/clients";

const getPostDefaults = async (
  workspaceID: string
): Promise<{
  linkedInIntegrationID: string | null;

  twitterIntegrationID: string | null;

  instagramIntegrationID: string | null;
  instagramReelShareToFeed: boolean;
  instagramLocation: {
    id: string;
    pageID: string;
    name: string;
  } | null;

  facebookIntegrationID: string | null;

  tikTokIntegrationID: string | null;

  youTubeIntegrationID: string | null;
  youTubeShortsNotifySubscribers: boolean;

  threadsIntegrationID: string | null;

  slackIntegrationID: string | null;

  discordWebhookID: string | null;

  webflowIntegrationID: string | null;
  webflowPreviewType: WebflowPreviewType;
}> => {
  const postDefaults = await db.postDefault.findFirst({
    where: {
      workspaceID,
    },
    include: {
      instagramLocation: {
        select: {
          id: true,
          pageID: true,
          name: true,
        },
      },
    },
  });

  if (postDefaults) {
    return postDefaults;
  } else {
    return {
      linkedInIntegrationID: null,
      twitterIntegrationID: null,
      instagramIntegrationID: null,
      instagramReelShareToFeed: true,
      instagramLocation: null,
      facebookIntegrationID: null,
      tikTokIntegrationID: null,
      youTubeIntegrationID: null,
      youTubeShortsNotifySubscribers: true,
      threadsIntegrationID: null,
      slackIntegrationID: null,
      discordWebhookID: null,
      webflowIntegrationID: null,
      webflowPreviewType: "Live" as const,
    };
  }
};

export default getPostDefaults;
