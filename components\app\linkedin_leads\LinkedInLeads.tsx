import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { Select } from "~/components";
import Button from "~/components/ui/Button";
import Tabs from "~/components/ui/Tabs";
import { useWindowTitle } from "~/hooks";
import { useUser } from "~/providers";
import { openIntercomChat, trpc } from "~/utils";
import LeadsByPost from "./LeadsByPost";
import LeadsByUser from "./LeadsByUser";

const LinkedInLeads = () => {
  useWindowTitle("LinkedIn Leads");

  const router = useRouter();

  const { user } = useUser();

  const connectedIntegrationsQuery =
    trpc.leads.linkedIn.getWorkspaceIntegrations.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });

  const integrations = connectedIntegrationsQuery.data?.integrations || [];

  const [selectedIntegrationID, setSelectedIntegrationID] = useState<
    string | null
  >(null);
  const selectedIntegration = integrations.find(
    (integration) => integration.id === selectedIntegrationID
  );

  useEffect(() => {
    if (!selectedIntegrationID) {
      const enabledIntegration = integrations.find(
        (integration) => integration.isLeadsScrapingEnabled
      );
      if (enabledIntegration) {
        setSelectedIntegrationID(enabledIntegration.id);
      }
    }
  }, [integrations.length]);

  const enableAll =
    user?.isAdmin &&
    integrations.every((integration) => !integration.isLeadsScrapingEnabled);

  return (
    <div className="h-full w-full">
      <div className="bg-surface border-lightest-gray sticky top-0 z-10 flex max-w-full items-center justify-between border-b p-10">
        <h4 className="flex items-center gap-3">
          <div>LinkedIn Leads</div>
          <Select
            options={integrations.map((integration) => ({
              label: integration.account.name,
              value: integration.id,
              disabled: !integration.isLeadsScrapingEnabled && !enableAll,
              disabledReason: !integration.isLeadsScrapingEnabled ? (
                <div className="flex flex-col items-start gap-2">
                  <div>
                    <span className="text-secondary">
                      {integration.account.name}
                    </span>{" "}
                    is not currently enabled for the LinkedIn Leads feature.
                    Contact us to enable.
                  </div>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => {
                      openIntercomChat(
                        `I'd like to enable LinkedIn Leads for ${integration.account.name}`
                      );
                    }}
                  >
                    Contact Us
                  </Button>
                </div>
              ) : undefined,
            }))}
            value={selectedIntegrationID}
            onChange={(value) => {
              setSelectedIntegrationID(value);
            }}
          />
        </h4>
        <Button
          variant="secondary"
          size="sm"
          onClick={() => {
            router.push("/linkedin-leads/integrations");
          }}
        >
          Manage Integrations
        </Button>
      </div>
      <div className="space-y-4 px-10 pt-6">
        <Tabs.Root defaultValue="post">
          <Tabs.List>
            <Tabs.Trigger value="post">By Post</Tabs.Trigger>
            <Tabs.Trigger value="user">By User</Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="post">
            <LeadsByPost integrationID={selectedIntegrationID} />
          </Tabs.Content>
          <Tabs.Content value="user">
            <LeadsByUser integration={selectedIntegration} />
          </Tabs.Content>
        </Tabs.Root>
      </div>
    </div>
  );
};

export default LinkedInLeads;
