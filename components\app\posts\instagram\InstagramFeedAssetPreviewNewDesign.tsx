import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { useInViewport } from "@mantine/hooks";
import Uppy from "@uppy/core";
import classNames from "classnames";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { Carousel } from "react-responsive-carousel";
import { Loader } from "~/components";
import { UploadProgress } from "~/components/ui/AssetUploadContainer";
import { ImageZoom } from "~/components/ui/ImageZoom";
import VideoPlayer from "~/components/ui/VideoPlayer";
import { handleFilesUpload } from "~/utils";
import {
  AssetControls,
  AssetsUploadReorderControls,
} from "../../files/AssetControls";
import { UploadingAsset } from "../../files/AssetsNewDesign";
import FileDropzone from "../../files/FileDropzone";
import { InstagramAsset } from "./InstagramPost";

type Props = {
  assets: InstagramAsset[];
  uploadingAssets?: UploadingAsset[];
  setIsEditingAssets: (isEditingAssets: boolean) => void;
  uppy?: Uppy | null;
  getValidFiles?: (files: File[]) => Promise<File[]>;
  contentID?: string;
  onDrop: (files: File[]) => void;
  handleUploadButtonClick: () => void;
  onDeleteClick: (asset: InstagramAsset) => void;
  isDraggedOver: boolean;
  isReadOnly: boolean;
  onEditClick: (asset: InstagramAsset) => void;
};

const InstagramAssetPreviewNewDesign = ({
  assets,
  uploadingAssets,
  setIsEditingAssets,
  uppy,
  getValidFiles,
  contentID,
  onDrop,
  handleUploadButtonClick,
  onDeleteClick,
  isDraggedOver,
  isReadOnly,
  onEditClick,
}: Props) => {
  const [dimensions, setDimensions] = useState<{
    width: number;
    height: number;
  }>({ width: 375, height: 375 });
  const firstImageRef = useRef<HTMLImageElement>(null);
  const firstVideoRef = useRef<HTMLVideoElement>(null);

  const { ref, inViewport } = useInViewport();

  useEffect(() => {
    if (firstImageRef.current) {
      firstImageRef.current.onload = () => {
        const current = firstImageRef.current;
        if (!current) {
          return;
        }

        setDimensions({
          width: current.clientWidth,
          height: current.clientHeight,
        });
      };

      if (firstImageRef.current) {
        setDimensions({
          width: firstImageRef.current.clientWidth,
          height: firstImageRef.current.clientHeight,
        });
      }
    }
  }, [firstImageRef, assets[0]?.signedUrl, inViewport]);

  useEffect(() => {
    if (firstVideoRef.current) {
      firstVideoRef.current.onloadedmetadata = () => {
        const current = firstVideoRef.current;

        const videoWidth = current?.videoWidth;
        const videoHeight = current?.videoHeight;

        if (!videoHeight || !videoWidth) {
          return;
        }

        let width = 0;
        let height = 0;

        if (videoWidth > videoHeight) {
          width = 300;
          height = Math.min((videoHeight / videoWidth) * width, 475);
        } else if (videoWidth === videoHeight) {
          width = 300;
          height = 300;
        } else {
          height = 475;
          width = Math.min((videoWidth / videoHeight) * height, 300);
        }

        setDimensions({
          width,
          height,
        });
      };
    }
  }, [firstVideoRef, assets[0]?.signedUrl]);

  const isAssetBeingEdited = (assetID: string) => {
    return uploadingAssets?.some(
      (uploadingAsset) => uploadingAsset.updateAssetID === assetID
    );
  };

  const averageUploadProgress =
    uploadingAssets && uploadingAssets.length > 0
      ? Math.round(
          uploadingAssets.reduce(
            (sum, asset) => sum + (asset.uploadProgress || 0),
            0
          ) / uploadingAssets.length
        )
      : undefined;

  if (assets.length === 0) {
    return (
      <div className="relative flex h-[375px] w-full items-center justify-center">
        {uppy && (
          <motion.div
            key="dropzone"
            className="w-full"
            initial={{ height: 375, opacity: 0 }}
            animate={{
              height: 375,
              opacity: 100,
            }}
          >
            <FileDropzone
              onDrop={async (files) => {
                if (uppy && getValidFiles) {
                  await handleFilesUpload({
                    files,
                    uppy,
                    meta: {
                      contentID,
                    },
                    getValidFiles,
                  });
                }
                onDrop(files);
              }}
              acceptedFileTypes={{
                "image/jpeg": [".jpg", ".jpeg"],
                "image/png": [".png"],
                "image/webp": [".webp"],
                "image/gif": [".gif"],
                "video/mp4": [".mp4"],
                "video/quicktime": [".mov"],
              }}
              backgroundImage={
                !isDraggedOver ? "/instagram/placeholder.png" : undefined
              }
              label={
                !isDraggedOver
                  ? "Click or drag and drop to upload media"
                  : undefined
              }
              borderClassName={!isDraggedOver ? "border-none" : "rounded-none"}
              uploadProgress={averageUploadProgress}
            />
          </motion.div>
        )}
      </div>
    );
  }

  const numAssets = assets.length;

  let content;

  if (numAssets === 1) {
    const isVideo = assets[0].mimetype.startsWith("video");
    if (isVideo) {
      content = (
        <div className="group/asset">
          <VideoPlayer.Root className="h-full w-full">
            <VideoPlayer.Content
              src={assets[0].signedUrl}
              slot="media"
              preload="auto"
            />
            <VideoPlayer.ControlBar>
              <VideoPlayer.PlayButton />
              <VideoPlayer.TimeRange />
              <VideoPlayer.TimeDisplay showDuration />
              <VideoPlayer.MuteButton />
              <VideoPlayer.VolumeRange />
            </VideoPlayer.ControlBar>
          </VideoPlayer.Root>
          <AssetControls
            asset={assets[0]}
            isReadOnly={isReadOnly}
            onEditClick={(asset) => onEditClick(asset as InstagramAsset)}
            onDeleteClick={(asset) => onDeleteClick(asset as InstagramAsset)}
          />
        </div>
      );
    } else {
      const isEditing = isAssetBeingEdited(assets[0].id);
      content = (
        <div className="group/asset relative">
          <ImageZoom>
            <img
              ref={firstImageRef}
              className="max-h-[475px] w-full object-cover"
              src={assets[0].signedUrl}
            />
          </ImageZoom>
          {isEditing && (
            <div className="absolute inset-1/2 inset-y-1/2 h-full w-full -translate-x-3 -translate-y-4">
              <div className="bg-opacity-80 w-fit rounded-full bg-white p-2">
                <Loader size="md" />
              </div>
            </div>
          )}
          <AssetControls
            asset={assets[0]}
            isReadOnly={isReadOnly}
            onEditClick={(asset) => onEditClick(asset as InstagramAsset)}
            onDeleteClick={(asset) => onDeleteClick(asset as InstagramAsset)}
          />
        </div>
      );
    }
  } else {
    content = (
      <div ref={ref}>
        <Carousel
          className="ig-carousel-slider"
          showThumbs={false}
          showStatus={false}
          renderArrowNext={(onClickHandler, hasNext, label) =>
            hasNext && (
              <button
                type="button"
                onClick={onClickHandler}
                title={label}
                className="bg-light-gray absolute top-1/2 right-2 z-10 -translate-y-1/2 transform rounded-full p-0.5 opacity-70"
              >
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            )
          }
          renderArrowPrev={(onClickHandler, hasPrev, label) =>
            hasPrev && (
              <button
                type="button"
                onClick={onClickHandler}
                title={label}
                className="bg-light-gray absolute top-1/2 left-2 z-10 -translate-y-1/2 transform rounded-full p-0.5 opacity-70"
              >
                <ChevronLeftIcon className="h-4 w-4" />
              </button>
            )
          }
          renderIndicator={(onClick, isSelected, index, label) => {
            return (
              <li
                key={index}
                onClick={onClick}
                className={classNames(
                  "mx-1 inline-block h-1.5 w-1.5 cursor-pointer rounded-full transition-colors hover:bg-[#0099FF]",
                  {
                    "bg-[#0099FF]": isSelected,
                    "bg-[#DBDBDB]": !isSelected,
                  }
                )}
              />
            );
          }}
        >
          {assets.map((asset, index) => {
            if (asset.mimetype.startsWith("video")) {
              const isEditing = isAssetBeingEdited(asset.id);
              return (
                <div
                  key={`${asset.signedUrl}-${dimensions.width}-${dimensions.height}}`}
                  className="group/asset relative flex justify-center"
                  style={{
                    width: dimensions.width,
                    height: dimensions.height,
                  }}
                >
                  <VideoPlayer.Root className="h-full w-full">
                    <VideoPlayer.Content
                      ref={index === 0 ? firstVideoRef : null}
                      src={asset.signedUrl}
                      slot="media"
                      preload="auto"
                      loop={true}
                      className="block object-cover"
                      width={dimensions.width}
                      height={dimensions.height}
                    />
                    <VideoPlayer.ControlBar>
                      <VideoPlayer.PlayButton />
                      <VideoPlayer.TimeRange />
                      <VideoPlayer.TimeDisplay showDuration />
                      <VideoPlayer.MuteButton />
                      <VideoPlayer.VolumeRange />
                    </VideoPlayer.ControlBar>
                  </VideoPlayer.Root>
                  {isEditing && (
                    <div className="absolute inset-1/2 inset-y-1/2 h-full w-full -translate-x-3 -translate-y-4">
                      <div className="bg-opacity-80 w-fit rounded-full bg-white p-2">
                        <Loader size="md" />
                      </div>
                    </div>
                  )}
                  <AssetControls
                    asset={asset}
                    isReadOnly={isReadOnly}
                    onEditClick={(asset) =>
                      onEditClick(asset as InstagramAsset)
                    }
                    onDeleteClick={(asset) =>
                      onDeleteClick(asset as InstagramAsset)
                    }
                  />
                </div>
              );
            } else {
              const isEditing = isAssetBeingEdited(asset.id);
              return (
                <div className="group/asset relative">
                  <img
                    ref={index === 0 ? firstImageRef : null}
                    key={`${asset.signedUrl}-${dimensions.width}-${dimensions.height}}`}
                    style={{
                      width: index === 0 ? "auto" : dimensions.width,
                      height: index === 0 ? "auto" : dimensions.height,
                    }}
                    className="max-h-[475px] w-full object-cover"
                    src={asset.signedUrl}
                  />
                  {isEditing && (
                    <div className="absolute inset-1/2 inset-y-1/2 h-full w-full -translate-x-3 -translate-y-4">
                      <div className="bg-opacity-80 w-fit rounded-full bg-white p-2">
                        <Loader size="md" />
                      </div>
                    </div>
                  )}
                  <AssetControls
                    asset={asset}
                    isReadOnly={isReadOnly}
                    onEditClick={(asset) =>
                      onEditClick(asset as InstagramAsset)
                    }
                    onDeleteClick={(asset) =>
                      onDeleteClick(asset as InstagramAsset)
                    }
                  />
                </div>
              );
            }
          })}
        </Carousel>
      </div>
    );
  }

  return (
    <div className="relative">
      <motion.div
        className="group/asset-controls relative opacity-100 transition-all"
        initial={{ opacity: 0 }}
        animate={{
          opacity: 100,
        }}
      >
        {content}
        {assets.length > 0 && (
          <AssetsUploadReorderControls
            onUploadClick={handleUploadButtonClick}
            onReorderClick={() => setIsEditingAssets(true)}
            isReadOnly={isReadOnly}
          />
        )}
      </motion.div>

      {uploadingAssets && uploadingAssets.length > 0 && (
        <motion.div
          className="bg-slate absolute inset-0 z-20 flex items-center justify-center backdrop-blur-xs"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <UploadProgress progress={averageUploadProgress || 0} />
        </motion.div>
      )}
      {isDraggedOver && uppy && (
        <motion.div
          className="absolute inset-0 z-20 h-full w-full bg-white"
          initial={{ opacity: 0 }}
          animate={{
            opacity: 100,
          }}
        >
          <FileDropzone
            onDrop={async (files) => {
              if (uppy && getValidFiles) {
                await handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID,
                  },
                  getValidFiles,
                });
              }
              onDrop(files);
            }}
            acceptedFileTypes={{
              "image/jpeg": [".jpg", ".jpeg"],
              "image/png": [".png"],
              "image/webp": [".webp"],
              "image/gif": [".gif"],
              "video/mp4": [".mp4"],
              "video/quicktime": [".mov"],
            }}
            label="Drop to add more media"
            borderClassName={!isDraggedOver ? "border-none" : "rounded-none"}
            uploadProgress={averageUploadProgress}
          />
        </motion.div>
      )}
    </div>
  );
};

export default InstagramAssetPreviewNewDesign;
