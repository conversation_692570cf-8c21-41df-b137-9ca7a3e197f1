import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import assert from "assert";
import truncate from "lodash/truncate";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { AddNewButton, Badge, IconButton } from "~/components";
import Credenza from "~/components/ui/Credenza";
import { ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import { useWorkspace } from "~/providers";
import { AssetType } from "~/types/Asset";
import { cleanFileName, handleFilesUpload } from "~/utils";
import FileDropzone from "../files/FileDropzone";

type Props = {
  contentID: string;
  video: AssetType | null;
  coverPhoto: AssetType | null;
  getValidFiles: (files: File[]) => Promise<File[]>;
  onUpload: (fileName: string, onSave: () => void) => void;
  onDelete: (asset: { id: string; path: string }) => void;
  storagePath: string;
  open: boolean;
  setOpen: (open: boolean) => void;
};

const CoverPhotoModal = ({
  contentID,
  video,
  coverPhoto,
  getValidFiles,
  onUpload,
  onDelete,
  storagePath,
  open,
  setOpen,
}: Props) => {
  const { currentWorkspace } = useWorkspace();

  const [uploadingAsset, setUploadingAsset] = useState<{
    name: string;
    size: number;
    uploadProgress: number;
  } | null>(null);

  const [uppy, setUppy] = useState<Uppy | null>(null);

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        restrictions: { maxNumberOfFiles: 1 },
        autoProceed: true,
      }).use(Tus, options);

      // Set up event listeners
      uppyInstance.on("file-added", async (file) => {
        assert(currentWorkspace);

        setUploadingAsset({
          name: file.name!,
          size: file.size!,
          uploadProgress: 0,
        });

        const objectName = `${storagePath}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
          contentID,
        };

        return file;
      });
      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAsset((uploadingAsset) => {
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            return {
              ...uploadingAsset,
              uploadProgress,
            };
          } else {
            return uploadingAsset;
          }
        });
      });
      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });
      uppyInstance.on("complete", (data) => {
        const file = data.successful![0];
        onUpload(cleanFileName(file.name!), () => {
          setUploadingAsset(null);
          setOpen(false);
        });
      });

      setUppy(uppyInstance);
    };

    initUppy();

    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [currentWorkspace, storagePath, contentID]);

  return (
    <div>
      {!coverPhoto ? (
        <AddNewButton label="Cover Photo" onClick={() => setOpen(true)} />
      ) : (
        <Badge
          intent="secondary"
          kind="hollow"
          label={truncate(coverPhoto.name, { length: 20 })}
          onClick={() => setOpen(true)}
        />
      )}
      <Credenza.Root open={open} onOpenChange={setOpen}>
        <Credenza.Header>
          <Credenza.Title>Add a Cover Photo</Credenza.Title>
          <Credenza.Description className="sr-only">
            Attach a cover photo for your video. By default, if no photo is
            added, we will use the first frame of the video.
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <div className="grid grid-cols-12 gap-4">
            {coverPhoto ? (
              <div className="group relative col-span-3 h-full">
                <img
                  className="h-auto max-h-full w-auto max-w-full"
                  src={coverPhoto?.signedUrl}
                  alt={coverPhoto?.name}
                />
                <div className="z-20 opacity-0 transition-opacity group-hover:opacity-100">
                  <div className="absolute top-2 right-2 flex gap-1.5">
                    <IconButton
                      icon="TrashIcon"
                      intent="danger"
                      onClick={() => {
                        onDelete(coverPhoto);
                      }}
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="col-span-3">
                {video && (
                  <video className="h-full">
                    <source src={video?.signedUrl} />
                  </video>
                )}
              </div>
            )}
            <div className="col-span-9">
              <FileDropzone
                uploadProgress={uploadingAsset?.uploadProgress}
                onDrop={async (files) => {
                  if (uppy) {
                    await handleFilesUpload({
                      files,
                      uppy,
                      meta: {
                        contentID,
                      },
                      getValidFiles,
                    });
                  }
                }}
                acceptedFileTypes={{
                  "image/jpg": [".jpg", ".jpeg"],
                  "image/png": [".png"],
                }}
              />
            </div>
          </div>
        </Credenza.Body>
      </Credenza.Root>
    </div>
  );
};

export default CoverPhotoModal;
