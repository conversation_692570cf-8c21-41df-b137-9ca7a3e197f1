import { Asset } from "@prisma/client";
import { WebClient as SlackWebClient } from "@slack/web-api";
import getAssetBuffer from "apiUtils/getAssetBuffer";
import getAssetsForSlack from "apiUtils/getAssetsForSlack";
import { GENERIC_ERROR_MESSAGE } from "~/constants";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";

type Post = {
  id: string;
  slackIntegration: {
    accessToken: string;
    channelID: string;
  } | null;
  slackContent: {
    id: string;
    copy: string;
    assets: {
      id: string;
      position: number;
      asset: Asset;
    }[];
  } | null;
  title: string;
  workspaceID: string;
};

const publishSlackPost = async (post: Post): Promise<ScheduledPostResult> => {
  const { slackContent, slackIntegration } = post;

  if (!slackContent) {
    return {
      status: "NonRetriableError",
      error: "No copy to post",
    };
  }

  if (!slackIntegration) {
    return {
      status: "NonRetriableError",
      error: "No Slack integration",
    };
  }

  const client = new SlackWebClient(slackIntegration.accessToken);

  const assets = await getAssetsForSlack(post);
  const asset = assets[0];

  let error = null;
  if (!asset) {
    // https://api.slack.com/methods/chat.postMessage
    const response = await client.chat.postMessage({
      channel: slackIntegration.channelID,
      text: slackContent.copy,
    });

    if (!response.ok) {
      console.log("Slack Error", response);
      error = response.error || GENERIC_ERROR_MESSAGE;
    }
  } else {
    const buffer = await getAssetBuffer(asset.signedUrl);

    const response = await client.files.uploadV2({
      file: buffer,
      channel_id: slackIntegration.channelID,
      filename: asset.name,
      initial_comment: slackContent.copy,
    });

    if (!response.ok) {
      console.log("Slack Error", response);
      error = response.error || GENERIC_ERROR_MESSAGE;
    }
  }

  if (!error) {
    return {
      status: "Success",
      postUrl: null,
    };
  } else {
    return {
      status: "Error",
      error,
    };
  }
};

export default publishSlackPost;
