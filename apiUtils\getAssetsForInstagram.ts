import { Asset } from "@prisma/client";
import sortBy from "lodash/sortBy";
import { AssetType } from "~/types/Asset";
import { removeEmptyElems } from "~/utils";
import enhanceAssetType from "./assets/enhanceAssetType";

export type Tag = {
  id: string;
  username: string;
  x: number | null;
  y: number | null;
};

const getAssetsForInstagram = (post: {
  id: string;
  instagramContent: {
    id: string;
    coverPhoto: Asset | null;
    assets: {
      id: string;
      position: number;
      asset: Asset | null;
      tags: Tag[];
    }[];
  } | null;
  workspaceID: string;
}): {
  assets: (AssetType & {
    position: number;
    tags: Tag[];
  })[];
  coverPhoto: AssetType | null;
} => {
  const { instagramContent } = post;
  if (!instagramContent) {
    return { assets: [], coverPhoto: null };
  }

  return {
    assets: removeEmptyElems(
      sortBy(instagramContent.assets, "position").map((asset) => {
        if (!asset.asset) {
          return null;
        }

        return {
          ...enhanceAssetType(asset.asset),
          id: asset.id,
          position: asset.position,
          tags: asset.tags,
        };
      })
    ),
    coverPhoto: instagramContent.coverPhoto
      ? enhanceAssetType(instagramContent.coverPhoto)
      : null,
  };
};

export default getAssetsForInstagram;
