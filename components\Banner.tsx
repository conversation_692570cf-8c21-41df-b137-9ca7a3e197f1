import classnames from "classnames";
import { ReactNode } from "react";
import { useMounted } from "~/hooks";

type BannerProps = {
  /** Defines the visual style which conveys the level of importance / urgency to the user */
  intent?: "none";
  /** The main content to be displayed in the center of the banner */
  message: string | ReactNode;
  isHidden?: boolean;
};

const Alert = ({ intent, message, isHidden }: BannerProps) => {
  const { hasMounted } = useMounted();

  const backgroundClassName = classnames(
    "mx-auto py-1.5 px-3 sm:px-6 lg:px-8 font-medium font-body-sm [&_a]:p-1 [&_a]:rounded-md [&_a]:font-semibold [&_a]:ml-2 [&_a]:transition-colors",
    {
      "text-basic bg-lightest-gray hover:[&_a]:bg-basic/10": intent === "none",
    }
  );

  if (!hasMounted || isHidden) {
    return null;
  }

  return (
    <div className={backgroundClassName}>
      <div className="pr-16 sm:px-16 sm:text-center">
        <p>{message}</p>
      </div>
    </div>
  );
};

export default Alert;
