import { InstagramContentType } from "@prisma/client";
import assert from "assert";
import axios, { AxiosInstance } from "axios";
import { StatusCodes } from "http-status-codes";
import { DateTime } from "luxon";
import { getKeys, removeEmpty<PERSON><PERSON>s, sleep } from "~/utils";
import {
  Response,
  createErrorResponse,
  createSuccessResponse,
} from "../../Response";
import catchFacebookError, {
  INSTAGRAM_ERROR_CODE_MESSAGES,
} from "../catchFacebookError";
import { IGPost } from "./InstagramTypes";
import PostsService from "./services/PostsService";
import ProfileService from "./services/ProfileService";

class Instagram {
  baseUrlMap = {
    Facebook: "https://graph.facebook.com/v22.0",
    Instagram: "https://graph.instagram.com/v22.0",
  };
  private readonly client: AxiosInstance;

  profile: ProfileService;
  posts: PostsService;

  constructor(
    accessToken: string,
    {
      platform,
    }: {
      platform: "Facebook" | "Instagram";
    }
  ) {
    this.client = axios.create({
      baseURL: this.baseUrlMap[platform],
    });

    // Add request interceptor to automatically append access_token to all requests
    this.client.interceptors.request.use((config) => {
      // If URL already has params, append to them, otherwise create new params
      const separator = config.url?.includes("?") ? "&" : "?";
      config.url = `${config.url}${separator}access_token=${accessToken}`;
      return config;
    });

    this.profile = new ProfileService(this.client, {
      platform,
    });
    this.posts = new PostsService(this.client, {
      platform,
    });
  }

  static accessToken = {
    // https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login/business-login#long-lived
    exchangeShortLivedToken: async (
      shortLivedToken: string
    ): Promise<
      Response<{ accessToken: string; tokenType: string; expiresAt: Date }>
    > => {
      try {
        const { status, data } = await axios.get(
          `https://graph.instagram.com/access_token`,
          {
            params: {
              grant_type: "ig_exchange_token",
              client_secret: process.env.INSTAGRAM_CLIENT_SECRET as string,
              access_token: shortLivedToken,
            },
          }
        );

        const authorization = {
          accessToken: data.access_token,
          expiresAt: DateTime.now()
            .plus({ seconds: data.expires_in })
            .toJSDate(),
          tokenType: data.token_type,
        };

        return createSuccessResponse(authorization, status);
      } catch (error) {
        return catchFacebookError(error);
      }
    },

    // https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login/business-login#refresh-a-long-lived-token
    refresh: async (
      accessToken: string
    ): Promise<
      Response<{
        accessToken: string;
        tokenType: string;
        expiresAt: Date;
      }>
    > => {
      try {
        const { data, status } = await axios.get(
          "https://graph.instagram.com/refresh_access_token",
          {
            params: {
              grant_type: "ig_refresh_token",
              access_token: accessToken,
            },
          }
        );

        return createSuccessResponse(
          {
            accessToken: data.access_token,
            tokenType: data.token_type,
            expiresAt: DateTime.now()
              .plus({ seconds: data.expires_in })
              .toJSDate(),
          },
          status
        );
      } catch (error) {
        return catchFacebookError(error);
      }
    },
  };

  // ONLY WORKS IF FACEBOOK INTEGRATION IS USED
  // https://developers.facebook.com/docs/instagram-platform/instagram-graph-api/reference/ig-user/business_discovery
  getAccountByUsername = async (
    instagramAccountID: string,
    username: string
  ): Promise<
    Response<{
      id: string;
      name: string;
      username: string;
      profileImageUrl: string;
      followerCount: number;
      followingCount: number;
      mediaCount: number;
    }>
  > => {
    const params = new URLSearchParams({
      fields: `business_discovery.username(${username}){${[
        "id",
        "username",
        "name",
        "profile_picture_url",
        "follows_count",
        "followers_count",
        "media_count",
      ].join(",")}}`,
    });

    try {
      const { status, data } = await this.client.get(
        `/${instagramAccountID}?${params}`
      );

      const user = data.business_discovery;

      const account = {
        id: user.id,
        name: user.name,
        username: user.username,
        profileImageUrl: user.profile_picture_url,
        followerCount: user.followers_count,
        followingCount: user.follows_count,
        mediaCount: user.media_count,
      };

      return createSuccessResponse(account, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/instagram-api/reference/ig-user/media#request-syntax
  uploadAsset = async ({
    instagramAccountID,
    contentType,
    locationID,
    asset,
    collaborators,
    reelShareToFeed,
    caption,
    isCarouselItem,
  }: {
    instagramAccountID: string;
    contentType: InstagramContentType;
    locationID?: string | null;
    asset: {
      id: string;
      name: string;
      signedUrl: string;
      type: "image" | "video";
      userTags?: {
        username: string;
        x: number | null;
        y: number | null;
      }[];
      coverPhotoUrl?: string;
    };
    collaborators: string[];
    reelShareToFeed: boolean;
    caption: string;
    isCarouselItem: boolean;
  }): Promise<Response<{ [assetID: string]: string }>> => {
    const paramObject: Record<string, string> = {
      fields: ["id", "status", "status_code"].join(","),
    };

    if (
      asset.userTags?.length &&
      ((["image"].includes(asset.type) && contentType !== "Story") ||
        contentType === "Reel")
    ) {
      paramObject.user_tags = JSON.stringify(
        asset.userTags.map((tag) => {
          if (asset.type === "image") {
            return {
              username: tag.username,
              x: tag.x,
              y: tag.y,
            };
          } else {
            return {
              username: tag.username,
            };
          }
        })
      );
    }

    if (!isCarouselItem) {
      if (collaborators.length && contentType !== "Story") {
        paramObject.collaborators = JSON.stringify(collaborators);
      }
      paramObject.caption = caption;
    }

    if (contentType === InstagramContentType.Reel) {
      paramObject.media_type = "REELS";
      paramObject.video_url = asset.signedUrl;
      paramObject.share_to_feed = reelShareToFeed.toString();
      if (asset.coverPhotoUrl) {
        try {
          await axios.get(asset.coverPhotoUrl);
        } catch (error) {
          return createErrorResponse(
            [
              "Instagram Reel cover photo inaccessible. Please reupload and try again",
            ],
            StatusCodes.BAD_REQUEST
          );
        }
        paramObject.cover_url = asset.coverPhotoUrl;
      }
    } else if (contentType === InstagramContentType.Feed) {
      if (asset.type === "image") {
        paramObject.image_url = asset.signedUrl;
        paramObject.is_carousel_item = isCarouselItem.toString();
      } else if (asset.type === "video") {
        paramObject.media_type = "VIDEO";
        paramObject.video_url = asset.signedUrl;
        paramObject.is_carousel_item = isCarouselItem.toString();
      }

      if (locationID) {
        paramObject.location_id = locationID;
      }
    } else if (contentType === InstagramContentType.Story) {
      paramObject.media_type = "STORIES";
      if (asset.type === "image") {
        paramObject.image_url = asset.signedUrl;
      } else if (asset.type === "video") {
        paramObject.video_url = asset.signedUrl;
      }
    }

    const params = new URLSearchParams(paramObject);

    try {
      console.log(`Uploading Asset (${asset.name})`);
      const { status, data } = await this.client.post(
        `/${instagramAccountID}/media?${params}`
      );
      console.log(`Uploaded Asset (${asset.name})`);

      return createSuccessResponse(
        {
          [asset.id]: data.id,
        },
        status
      );
    } catch (error) {
      console.log(error);
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/instagram-api/reference/ig-container#reading
  getUploadStatus = async (
    containerID: string
  ): Promise<
    Response<{
      statusCode:
        | "EXPIRED"
        // | "ERROR" we'll throw an error if this happens
        | "FINISHED"
        | "IN_PROGRESS"
        | "PUBLISHED";
    }>
  > => {
    const params = new URLSearchParams({
      fields: ["id", "status", "status_code"].join(","),
    });

    try {
      const { status, data } = await this.client.get(
        `/${containerID}?${params}`
      );

      if (data.status_code === "ERROR") {
        let errorMessage = data.status as string;
        const instagramErrorCodes = getKeys(INSTAGRAM_ERROR_CODE_MESSAGES);
        instagramErrorCodes.forEach((code) => {
          if (errorMessage.includes(code)) {
            errorMessage += `. ${INSTAGRAM_ERROR_CODE_MESSAGES[code]}`;
          }
        });

        return createErrorResponse([errorMessage], StatusCodes.BAD_REQUEST);
      }

      return createSuccessResponse(
        {
          statusCode: data.status_code,
        },
        status
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  createCarouselContainer = async ({
    instagramAccountID,
    caption,
    collaborators,
    containerIDs,
    locationID,
  }: {
    instagramAccountID: string;
    caption: string;
    collaborators: string[];
    containerIDs: string[];
    locationID?: string | null;
  }): Promise<
    Response<{
      id: string;
    }>
  > => {
    let promises = containerIDs.map(
      async (containerID) => await this.getUploadStatus(containerID)
    );

    let responses = await Promise.all(promises);
    let someInProgress = responses.some(
      (response) => response.data?.statusCode === "IN_PROGRESS"
    );
    while (someInProgress) {
      await sleep(1000);
      promises = containerIDs.map(
        async (containerID) => await this.getUploadStatus(containerID)
      );
      responses = await Promise.all(promises);
      someInProgress = responses.some(
        (response) => response.data?.statusCode === "IN_PROGRESS"
      );
      console.log(`Uploading Carousel In Progress:`, someInProgress);
    }

    // If any of the containers have errored, return the error
    const erroredResponses = responses.filter(
      (response) => response.data == null
    );

    if (erroredResponses.length) {
      const firstError = erroredResponses[0];

      const allErrors = removeEmptyElems(
        erroredResponses.map((response) => response.errors)
      ).flat();

      return createErrorResponse(allErrors, firstError.status);
    }

    const nonFinishedStatusCodes = removeEmptyElems(
      responses
        .filter((response) => response.data?.statusCode !== "FINISHED")
        .map((response) => response.data?.statusCode)
    );

    if (nonFinishedStatusCodes.length > 0) {
      if (nonFinishedStatusCodes.includes("EXPIRED")) {
        return createErrorResponse(
          ["One or more of the containers have expired"],
          StatusCodes.BAD_REQUEST
        );
      } else if (nonFinishedStatusCodes.includes("IN_PROGRESS")) {
        return createErrorResponse(
          ["One or more of the containers hasn't finished uploading"],
          StatusCodes.BAD_REQUEST
        );
      } else if (nonFinishedStatusCodes.includes("PUBLISHED")) {
        return createErrorResponse(
          ["One or more of the containers has already been published"],
          StatusCodes.BAD_REQUEST
        );
      }
    }

    try {
      const rawParams: { [param: string]: string } = {
        media_type: "CAROUSEL",
        caption,
        children: containerIDs.join(","),
      };

      if (collaborators.length > 0) {
        rawParams.collaborators = JSON.stringify(collaborators);
      }

      if (locationID) {
        rawParams.location_id = locationID;
      }

      const params = new URLSearchParams(rawParams);

      // At this point everything should be finished uploading and we create the carousel
      const { status, data } = await this.client.post(
        `/${instagramAccountID}/media?${params}`
      );

      return createSuccessResponse(data, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/instagram-api/reference/ig-user/media#creating
  uploadAssets = async ({
    instagramAccountID,
    contentType,
    locationID,
    assets,
    collaborators,
    reelShareToFeed,
    caption,
  }: {
    instagramAccountID: string;
    contentType: InstagramContentType;
    locationID?: string | null;
    assets: {
      id: string;
      name: string;
      signedUrl: string;
      type: "image" | "video";
      userTags?: {
        username: string;
        x: number | null;
        y: number | null;
      }[];
      coverPhotoUrl?: string;
      fileSizeBytes?: number;
    }[];
    collaborators: string[];
    reelShareToFeed: boolean;
    caption: string;
  }): Promise<Response<{ [assetID: string]: string }>> => {
    if (assets.length === 0) {
      return createErrorResponse(
        ["You must have at least one asset to upload"],
        StatusCodes.BAD_REQUEST
      );
    }

    if (contentType === InstagramContentType.Reel) {
      if (assets.length !== 1) {
        return createErrorResponse(
          ["Reel posts can only have one asset"],
          StatusCodes.BAD_REQUEST
        );
      } else if (assets[0].type !== "video") {
        return createErrorResponse(
          ["Reel posts must have a video asset"],
          StatusCodes.BAD_REQUEST
        );
      }
    }

    const promises = assets.map(
      async (asset) =>
        await this.uploadAsset({
          instagramAccountID,
          contentType,
          locationID,
          asset,
          reelShareToFeed,
          collaborators: assets.length > 1 ? [] : collaborators,
          caption,
          isCarouselItem: assets.length > 1,
        })
    );

    const results = await Promise.all(promises);
    const errors = results.filter((result) => result.status !== StatusCodes.OK);

    if (errors.length) {
      return createErrorResponse(
        removeEmptyElems(errors.map((error) => error.errors).flat()),
        errors[0].status
      );
    } else if (results.length === 1) {
      assert(results[0].data);
      return createSuccessResponse(results[0].data, results[0].status);
    } else {
      // Combine all of the returned data into one object
      const data = results.reduce((acc, result) => {
        assert(result.data);
        return {
          ...acc,
          ...result.data,
        };
      }, {});

      return createSuccessResponse(data, StatusCodes.OK);
    }
  };

  // https://developers.facebook.com/docs/instagram-api/guides/content-publishing#step-2-of-2--publish-container
  publishPost = async ({
    instagramAccountID,
    creationID,
    type,
  }: {
    instagramAccountID: string;
    creationID: string;
    type: InstagramContentType;
  }): Promise<Response<IGPost>> => {
    console.log(
      `Posting Creation (${creationID}): Waiting for upload to finish`
    );

    const timer = Array.from({ length: 4 * 60 }, (_, i) => i + 1);
    for await (const _ of timer) {
      console.log(`Posting Creation (${creationID}): Querying upload status`);
      const uploadResponse = await this.getUploadStatus(creationID);
      if (!uploadResponse.data) {
        console.log(
          `Posting Creation (${creationID}): Upload Error ${uploadResponse.errors.join(
            ". "
          )}`
        );

        return createErrorResponse(
          uploadResponse.errors,
          uploadResponse.status
        );
      } else {
        const statusCode = uploadResponse.data.statusCode;
        console.log(
          `Posting Creation (${creationID}): Status Code:`,
          statusCode
        );
        if (statusCode === "FINISHED") {
          break;
        } else if (statusCode === "PUBLISHED") {
          return createErrorResponse(
            [`This container ${creationID} has already been published`],
            StatusCodes.CONFLICT
          );
        } else if (statusCode === "IN_PROGRESS") {
          console.log(`Posting Creation (${creationID}): Sleeping for 1s`);
          await sleep(1000);
          console.log(`Posting Creation (${creationID}): Done sleeping`);
        }
      }
    }

    console.log(
      `Posting Creation (${creationID}): Upload finished, publishing`
    );

    try {
      const { status, data } = await this.client.post(
        `/${instagramAccountID}/media_publish?creation_id=${creationID}`
      );

      const mediaID = data.id;
      const postResponse = await this.posts.get({ mediaID, type });
      const post = postResponse.data;

      if (!post) {
        return createErrorResponse(postResponse.errors, postResponse.status);
      }

      return createSuccessResponse(post, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/instagram-api/reference/ig-media/comments#creating-a-comment-on-a-media-object
  comment = async ({
    mediaID,
    message,
  }: {
    mediaID: string;
    message: string;
  }): Promise<Response<{ id: string }>> => {
    try {
      const { status, data } = await this.client.post(`/${mediaID}/comments`, {
        message,
      });

      return createSuccessResponse(data, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/instagram-api/reference/ig-media#fields
  getBusinessAccountMedia = async (
    businessAccountID: string
  ): Promise<
    Response<{
      id: string;
      caption: string;
      like_count: string;
      media_url: string;
      comments_count: string;
      media_type: "CAROUSEL_ALBUM" | "IMAGE" | "VIDEO";
    }>
  > => {
    const params = new URLSearchParams({
      fields: [
        "id",
        "caption",
        "like_count",
        "media_url",
        "comments_count",
        "media_type",
      ].join(","),
    });

    try {
      const { status, data } = await this.client.get(
        `/${businessAccountID}/media?${params}`
      );

      return createSuccessResponse(data, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/instagram-api/reference/ig-user/insights#request-syntax-2
  getFollowsAndUnfollowsTimeframe = async ({
    instagramAccountID,
    timeframeDays,
  }: {
    instagramAccountID: string;
    timeframeDays: number;
  }): Promise<
    Response<{
      followsAndUnfollows: {
        followCount: number | null;
        unfollowCount: number | null;
        recordedAt: Date;
      }[];
      followerCount: number;
      followingCount: number;
    }>
  > => {
    const baseParams = {
      metric_type: "total_value",
      metric: "follows_and_unfollows",
      breakdown: "follow_type",
      period: "day",
    };

    const daysArray = Array.from({ length: timeframeDays }, (_, i) => i);

    try {
      // Create array of promises for insights data
      const insightPromises = daysArray.map((day) => {
        const params = new URLSearchParams({
          ...baseParams,
          since: DateTime.now()
            .setZone("CST")
            .minus({ days: timeframeDays - day - 1 })
            .startOf("day")
            .toUnixInteger()
            .toString(),
          until: DateTime.now()
            .setZone("CST")
            .minus({ days: timeframeDays - day - 1 })
            .endOf("day")
            .toUnixInteger()
            .toString(),
        });

        return this.client.get(`/${instagramAccountID}/insights?${params}`);
      });

      // Add promise for account data
      const accountPromise = this.client.get(
        `/${instagramAccountID}?fields=id,followers_count,follows_count`
      );

      // Wait for all promises to resolve
      const [accountResponse, ...insightResponses] = await Promise.all([
        accountPromise,
        ...insightPromises,
      ]);

      const accountData = accountResponse.data as {
        followers_count: number;
        follows_count: number;
      };

      // If the account has less than 100 followers, we can't get the data
      if (accountData.followers_count < 100) {
        return createSuccessResponse(
          {
            followsAndUnfollows: [
              {
                followCount: null,
                unfollowCount: null,
                recordedAt: DateTime.now()
                  .setZone("CST")
                  .startOf("day")
                  .plus({ hours: 12 })
                  .toJSDate(),
              },
            ],
            followerCount: accountData.followers_count,
            followingCount: accountData.follows_count,
          },
          StatusCodes.OK
        );
      }

      const data = insightResponses.map((response, index) => {
        const rawData = response.data as {
          data: [
            {
              total_value: {
                breakdowns: {
                  results?: [
                    {
                      dimension_values: ["FOLLOWER"];
                      value: number;
                    },
                    {
                      dimension_values: ["NON_FOLLOWER"];
                      value: number;
                    },
                  ];
                }[];
              };
            },
          ];
        };

        const rawFollowCount =
          rawData.data[0].total_value.breakdowns[0].results?.find(
            (result) => result.dimension_values[0] === "FOLLOWER"
          )?.value;
        const rawUnfollowCount =
          rawData.data[0].total_value.breakdowns[0].results?.find(
            (result) => result.dimension_values[0] === "NON_FOLLOWER"
          )?.value;

        let defaultValue = null;
        if (
          index < timeframeDays - 3 ||
          rawFollowCount != null ||
          rawUnfollowCount != null
        ) {
          defaultValue = 0;
        }

        const followCount = rawFollowCount || defaultValue;
        const unfollowCount = rawUnfollowCount || defaultValue;

        const until = DateTime.now()
          .setZone("CST")
          .minus({ days: timeframeDays - index - 1 })
          .startOf("day")
          .plus({ hours: 12 });

        return {
          recordedAt: until.toJSDate(),
          followCount,
          unfollowCount,
        };
      });

      return createSuccessResponse(
        {
          followsAndUnfollows: data,
          followerCount: accountData.followers_count,
          followingCount: accountData.follows_count,
        },
        StatusCodes.OK
      );
    } catch (error) {
      return catchFacebookError(error);
    }
  };
}

export default Instagram;
