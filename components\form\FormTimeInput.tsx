import { Controller, Path } from "react-hook-form";

import { Label, TimeInput } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import camelCaseToWords from "~/utils/camelCaseToWords";
import { SuggestedTimes } from "../TimeInput";

type FormTimeInputProps<T> = {
  control: any;
  name: Path<T>;
  disabled?: boolean;
  label?: string | JSX.Element;
  isOptional?: boolean;
  tooltipText?: string;
  acceptedMinuteMultiple?: number;
  rules?: Rules;
  suggestedTimes?: SuggestedTimes[];
  displayStyle?: "underline" | "border";
};

const FormTimeInput = <T,>({
  control,
  name,
  label,
  isOptional,
  tooltipText,
  acceptedMinuteMultiple,
  rules = {},
  suggestedTimes,
  displayStyle,
}: FormTimeInputProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <Label
            value={label}
            isRequired={!!rules.required}
            tooltipText={tooltipText}
            isOptional={isOptional}
          >
            <TimeInput
              value={value}
              onChange={onChange}
              acceptedMinuteMultiple={acceptedMinuteMultiple}
              suggestedTimes={suggestedTimes}
              displayStyle={displayStyle}
            />
            <Error error={error} />
          </Label>
        );
      }}
    />
  );
};

export default FormTimeInput;
