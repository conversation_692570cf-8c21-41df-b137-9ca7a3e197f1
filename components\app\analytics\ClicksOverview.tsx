import { ArrowTurnDownRightIcon, LinkIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import { DateTime } from "luxon";
import Image from "next/image";
import { Area, AreaChart, XAxis, YAxis } from "recharts";
import Tooltip from "~/components/Tooltip";
import Stat from "~/components/app/analytics/Stat";
import Avatar from "~/components/ui/Avatar";
import Card from "~/components/ui/Card";
import Chart from "~/components/ui/Chart";
import Tabs from "~/components/ui/Tabs";
import colors, { chartColors } from "~/styles/colors";
import { trpc } from "~/utils";

const chartConfig = {
  clicks: {
    label: "Clicks",
    color: chartColors[0],
  },
};

type Props = {
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
};

const ClicksOverview = ({ dateRange }: Props) => {
  const { data } = trpc.dub.getAnalytics.useQuery({
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
  });

  const {
    timeseries,
    linkClicks,
    urlClicks,
    countries,
    regions,
    referrers,
    devices,
    browsers,
    operatingSystems,
  } = data || {};

  const isEmpty =
    timeseries != null && timeseries.every((item) => item.clicks === 0);

  return (
    <div className="h-full w-full">
      <div className="font-body-sm mb-6 flex flex-col gap-1.5">
        <span className="font-medium">Click Data (Link Shortener)</span>
        <span className="text-medium-dark-gray">
          Analytics around click data for posts that included links using
          Assembly's link shortener.
        </span>
      </div>
      <div className="space-y-6">
        <Card.Root className="relative">
          {isEmpty && (
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="font-body-sm text-medium-dark-gray">
                No link click data during this time period.
              </span>
            </div>
          )}
          <Card.Content>
            <div className="mb-4 flex items-center gap-7">
              <Stat
                label="Clicks"
                value={linkClicks?.reduce((acc, item) => acc + item.clicks, 0)}
                intent="basic"
              />
            </div>
            <Chart.Container
              config={chartConfig}
              className="max-h-[300px] min-h-[200px] w-full -translate-x-10"
            >
              <AreaChart accessibilityLayer={true} data={timeseries}>
                <defs>
                  <linearGradient id="fillClicks" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor={chartColors[0]}
                      stopOpacity={0.7}
                    />
                    <stop
                      offset="95%"
                      stopColor={chartColors[0]}
                      stopOpacity={0}
                    />
                  </linearGradient>
                </defs>
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  className="font-eyebrow-sm text-medium-dark-gray"
                  tickFormatter={(value) =>
                    DateTime.fromISO(value).toFormat("M/d")
                  }
                />
                <YAxis
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  className="font-eyebrow-sm text-medium-dark-gray"
                />
                <Chart.Tooltip
                  content={
                    <Chart.TooltipContent
                      labelFormatter={(value) =>
                        DateTime.fromISO(value).toLocaleString(
                          DateTime.DATE_MED
                        )
                      }
                    />
                  }
                />
                <Area
                  dot={{
                    fill: colors.secondary,
                  }}
                  dataKey="clicks"
                  fill="url(#fillClicks)"
                  stroke={chartColors[0]}
                  radius={4}
                />
              </AreaChart>
            </Chart.Container>
          </Card.Content>
        </Card.Root>
        <div className="grid grid-cols-2 gap-6">
          <div className="font-body-sm">
            <div className="pb-2.5 font-medium">Post</div>
            <Card.Root>
              <Card.Content>
                <Tabs.Root defaultValue="link">
                  <Tabs.List>
                    <Tabs.Trigger value="link">Link</Tabs.Trigger>
                    <Tabs.Trigger value="destination-url">
                      Destination URL
                    </Tabs.Trigger>
                  </Tabs.List>
                  <LinkClicksBarChart
                    tabValue="link"
                    data={linkClicks?.map((item) => ({
                      id: item.id,
                      label: item.shortLink,
                      clicks: item.clicks,
                    }))}
                    tooltipValueFn={(item) => {
                      const linkData = linkClicks?.find(
                        (link) => link.id === item.id
                      );
                      return (
                        <div>
                          {linkData?.shortLink}{" "}
                          <div className="text-medium-dark-gray flex items-center gap-1">
                            <ArrowTurnDownRightIcon className="h-4 w-4" />
                            {linkData?.url}
                          </div>
                        </div>
                      );
                    }}
                  />
                  <LinkClicksBarChart
                    tabValue="destination-url"
                    data={urlClicks?.map((item) => ({
                      label: item.url,
                      clicks: item.clicks,
                    }))}
                  />
                </Tabs.Root>
              </Card.Content>
            </Card.Root>
          </div>
          <div className="font-body-sm">
            <div className="pb-2.5 font-medium">Location</div>
            <Card.Root>
              <Card.Content>
                <Tabs.Root defaultValue="country">
                  <Tabs.List>
                    <Tabs.Trigger value="country">Country</Tabs.Trigger>
                    <Tabs.Trigger value="region">Region</Tabs.Trigger>
                  </Tabs.List>
                  <LinkClicksBarChart
                    tabValue="country"
                    data={countries?.map((item) => ({
                      label: (
                        <div className="flex items-center gap-2">
                          <img
                            src={`https://flag.vercel.app/m/${item.countryCode}.svg`}
                            alt={item.country}
                            className="h-3 w-5"
                          />
                          {item.country}
                        </div>
                      ),
                      clicks: item.clicks,
                    }))}
                  />
                  <LinkClicksBarChart
                    tabValue="region"
                    data={regions?.map((item) => ({
                      label: (
                        <div className="flex items-center gap-2">
                          <Image
                            src={`https://flag.vercel.app/m/${item.countryCode}.svg`}
                            alt={item.country}
                            width={12}
                            height={20}
                          />
                          {item.region}
                        </div>
                      ),
                      clicks: item.clicks,
                    }))}
                  />
                </Tabs.Root>
              </Card.Content>
            </Card.Root>
          </div>
          <div className="font-body-sm">
            <div className="pb-2.5 font-medium">Referers</div>
            <Card.Root>
              <Card.Content>
                <Tabs.Root defaultValue="referer">
                  <LinkClicksBarChart
                    tabValue="referer"
                    data={referrers?.map((item) => ({
                      label: (
                        <div className="flex items-center gap-2">
                          {item.referrer === "Direct" ? (
                            <LinkIcon className="mr-1 h-3 w-3" />
                          ) : (
                            <Image
                              src={`https://www.google.com/s2/favicons?sz=64&domain_url=${item.referrerUrl}`}
                              alt={item.referrer}
                              width={16}
                              height={16}
                            />
                          )}
                          {item.referrer}
                        </div>
                      ),
                      clicks: item.clicks,
                    }))}
                  />
                </Tabs.Root>
              </Card.Content>
            </Card.Root>
          </div>
          <div className="font-body-sm">
            <div className="pb-2.5 font-medium">By Device</div>
            <Card.Root>
              <Card.Content>
                <Tabs.Root defaultValue="device">
                  <Tabs.List>
                    <Tabs.Trigger value="device">Device</Tabs.Trigger>
                    <Tabs.Trigger value="browser">Browser</Tabs.Trigger>
                    <Tabs.Trigger value="os">OS</Tabs.Trigger>
                  </Tabs.List>
                  <LinkClicksBarChart
                    tabValue="device"
                    data={devices?.map((item) => ({
                      label: item.device,
                      clicks: item.clicks,
                    }))}
                  />
                  <LinkClicksBarChart
                    tabValue="browser"
                    data={browsers?.map((item) => ({
                      label: (
                        <div className="flex items-center gap-2">
                          <Avatar.Root className="h-4 w-auto">
                            <Avatar.Image
                              src={`https://uaparser.dev/images/browsers/${item.browser.toLowerCase()}.png`}
                              alt={item.browser}
                            />
                            <Avatar.Fallback className="bg-lightest-gray h-4 w-4 rounded-full">
                              {item.browser.slice(0, 2)}
                            </Avatar.Fallback>
                          </Avatar.Root>
                          {item.browser}
                        </div>
                      ),
                      clicks: item.clicks,
                    }))}
                  />
                  <LinkClicksBarChart
                    tabValue="os"
                    data={operatingSystems?.map((item) => ({
                      label: (
                        <div className="flex items-center gap-2">
                          <Avatar.Root className="h-4 w-auto">
                            <Avatar.Image
                              src={`https://uaparser.dev/images/os/${item.os
                                .replace(" ", "")
                                .toLowerCase()}.png`}
                              alt={item.os}
                            />
                            <Avatar.Fallback className="bg-lightest-gray h-4 w-4 rounded-full">
                              {item.os.slice(0, 2)}
                            </Avatar.Fallback>
                          </Avatar.Root>
                          {item.os}
                        </div>
                      ),
                      clicks: item.clicks,
                    }))}
                  />
                </Tabs.Root>
              </Card.Content>
            </Card.Root>
          </div>
        </div>
      </div>
    </div>
  );
};

const LinkClicksBarChart = ({
  tabValue,
  data,
  tooltipValueFn,
}: {
  tabValue: string;
  data: { id?: string; label: React.ReactNode; clicks: number }[] | undefined;
  tooltipValueFn?: (item: {
    id?: string;
    label: string | React.ReactNode;
    clicks: number;
  }) => React.ReactNode;
}) => {
  const maxClicks = Math.max(...(data?.map((item) => item.clicks) ?? []));

  const isEmpty = data == null || data?.length === 0;

  return (
    <Tabs.Content
      value={tabValue}
      className="scrollbar-hidden h-[350px] overflow-y-auto"
    >
      <div className="flex h-full flex-col gap-2">
        {data?.map((item) => (
          <Tooltip
            key={item.id}
            align="start"
            delayDuration={200}
            value={tooltipValueFn?.(item)}
          >
            <div className="group relative flex h-7 items-center">
              <div className="absolute top-1 right-2.5 z-10">{item.clicks}</div>
              <div className="z-10 ml-2.5">{item.label}</div>
              <div
                className={classNames(
                  "absolute h-full rounded-md bg-[#EEF1FF] p-2 transition-colors",
                  {
                    "group-hover:bg-secondary/30": !!tooltipValueFn,
                  }
                )}
                style={{
                  width: `${(item.clicks / maxClicks) * 100}%`,
                }}
              />
            </div>
          </Tooltip>
        ))}
        {isEmpty && (
          <div className="flex h-full items-center justify-center">
            <span className="text-medium-dark-gray">
              No link click data during this time period.
            </span>
          </div>
        )}
      </div>
    </Tabs.Content>
  );
};

export default ClicksOverview;
