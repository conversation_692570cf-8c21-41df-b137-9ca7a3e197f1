import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import classNames from "classnames";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import Checkbox from "~/components/Checkbox";
import Avatar from "~/components/ui/Avatar";
import Skeleton from "~/components/ui/Skeleton";
import { handleTrpcError, trpc } from "~/utils";

type User = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  workspaceCount: number;
};

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User | null;
  onSuccess?: () => void;
  onClose?: () => void;
};

const EditWorkspacesModal = ({ open, setOpen, user, onSuccess, onClose }: Props) => {
  const [selectedWorkspaceIDs, setSelectedWorkspaceIDs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // tRPC queries and mutations
  const userWorkspacesQuery = trpc.agency.getUserWorkspaces.useQuery(
    { userID: user?.id || "" },
    {
      enabled: !!user?.id && open,
      refetchOnWindowFocus: false,
    }
  );

  const updateUserWorkspacesMutation = trpc.agency.updateUserWorkspaces.useMutation({
    onSuccess: (data) => {
      toast.success("Workspace access updated successfully", {
        description: `Added to ${data.added} workspaces, removed from ${data.removed} workspaces`,
      });
      setOpen(false);
      onSuccess?.();
    },
    onError: (error) => {
      handleTrpcError({ title: "Error Updating Workspace Access", error });
    },
    onSettled: () => {
      setIsLoading(false);
    },
  });

  // Initialize selected workspaces when data loads
  useEffect(() => {
    if (userWorkspacesQuery.data?.workspaces) {
      const currentlySelected = userWorkspacesQuery.data.workspaces
        .filter((workspace) => workspace.isMember)
        .map((workspace) => workspace.id);
      setSelectedWorkspaceIDs(currentlySelected);
    }
  }, [userWorkspacesQuery.data]);

  // Reset when modal closes or when user changes
  useEffect(() => {
    if (!open) {
      setSelectedWorkspaceIDs([]);
    }
  }, [open]);

  // Reset to original values when modal opens or user changes
  useEffect(() => {
    if (open && userWorkspacesQuery.data?.workspaces) {
      const currentlySelected = userWorkspacesQuery.data.workspaces
        .filter((workspace) => workspace.isMember)
        .map((workspace) => workspace.id);
      setSelectedWorkspaceIDs(currentlySelected);
    }
  }, [open, userWorkspacesQuery.data]);

  const workspaces = userWorkspacesQuery.data?.workspaces || [];

  const handleWorkspaceToggle = (workspaceID: string) => {
    setSelectedWorkspaceIDs((prev) => {
      if (prev.includes(workspaceID)) {
        return prev.filter((id) => id !== workspaceID);
      } else {
        return [...prev, workspaceID];
      }
    });
  };

  const handleSave = () => {
    if (!user) return;

    setIsLoading(true);
    updateUserWorkspacesMutation.mutate({
      userID: user.id,
      workspaceIDs: selectedWorkspaceIDs,
    });
  };

  const getUserDisplayName = () => {
    if (!user) return "";
    const firstName = user.firstName || "";
    const lastName = user.lastName || "";
    return `${firstName} ${lastName}`.trim() || user.email;
  };

  const handleClose = () => {
    // Reset to original values
    if (userWorkspacesQuery.data?.workspaces) {
      const currentlySelected = userWorkspacesQuery.data.workspaces
        .filter((workspace) => workspace.isMember)
        .map((workspace) => workspace.id);
      setSelectedWorkspaceIDs(currentlySelected);
    }
    setOpen(false);
    onClose?.();
  };

  const LoadingSkeleton = () => (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-center justify-between w-full gap-6">
          <div className="flex items-center gap-3 flex-1">
            <Skeleton className="h-5 w-5" />
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex flex-col gap-1">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <Credenza.Root
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          handleClose();
        } else {
          setOpen(isOpen);
        }
      }}
    >
      <Credenza.Header className="w-[90%]">
        <Credenza.Title>
          {/* Is okey using h3 here in the title? * Because text size with classes was not working */}
          <h3>
            Edit Workspaces for{' '}
            <span className="text-secondary">
              {getUserDisplayName()}
            </span>
          </h3>
        </Credenza.Title>
        <Credenza.Description>
          <span className="text-medium-dark-gray">
            Edit which agency workspaces {getUserDisplayName()} is a member of and should have access to:
          </span>
        </Credenza.Description>
      </Credenza.Header>

      <Credenza.Body>
        <div className="divide-y-lightest-gray scrollbar-hidden flex max-h-96 flex-col divide-y overflow-auto">
          {userWorkspacesQuery.isLoading ? (
            <LoadingSkeleton />
          ) : workspaces.length === 0 ? (
            <div className="font-body-sm mt-5 font-medium text-medium-dark-gray">
              No workspaces available
            </div>
          ) : (
            <>
              {/* Select All Checkbox */}
              <div className="w-full">
                <button
                  className="my-1 flex w-full items-center justify-between rounded-md px-2 py-3 transition-all hover:bg-slate"
                  onClick={() => {
                    if (selectedWorkspaceIDs.length === workspaces.length) {
                      setSelectedWorkspaceIDs([]);
                    } else {
                      setSelectedWorkspaceIDs(workspaces.map(w => w.id));
                    }
                  }}
                >
                  <div className="flex w-full items-center gap-3">
                    <Checkbox
                      value={selectedWorkspaceIDs.length === workspaces.length}
                      onChange={() => { }}
                    />
                    <div className="flex items-center gap-3">
                      <div className="flex flex-col items-start">
                        <div className="text-medium-dark-gray">
                          {selectedWorkspaceIDs.length === workspaces.length
                            ? "Deselect all workspaces"
                            : selectedWorkspaceIDs.length > 0
                              ? `Select all workspaces (${selectedWorkspaceIDs.length}/${workspaces.length} selected)`
                              : "Select all workspaces"
                          }
                        </div>

                      </div>
                    </div>
                  </div>
                </button>
              </div>

              {/* Individual Workspaces */}
              <div className="overflow-y-auto">

                {workspaces.map((workspace) => {
                  const isSelected = selectedWorkspaceIDs.includes(workspace.id);
                  const workspaceInitials = workspace.name
                    .split(' ')
                    .map(word => word.charAt(0))
                    .join('')
                    .toUpperCase()
                    .slice(0, 2);

                  return (
                    <div className="w-full" key={workspace.id}>
                      <button
                        className={classNames(
                          "my-1 flex w-full items-center justify-between rounded-md px-2 py-3 transition-all hover:bg-slate"
                        )}
                        onClick={() => handleWorkspaceToggle(workspace.id)}
                      >
                        <div className="flex w-full items-center gap-3">
                          <Checkbox
                            value={isSelected}
                            onChange={() => { }}
                          />
                          <div className="flex items-center gap-3">
                            <Avatar.Root className="h-10 w-10">
                              <Avatar.Fallback className="font-medium">
                                {workspaceInitials}
                              </Avatar.Fallback>
                            </Avatar.Root>
                            <div className="flex flex-col items-start">
                              <div >
                                {workspace.name}
                              </div>
                              <div className="text-medium-dark-gray text-sm">
                                {workspace.memberCount} {workspace.memberCount === 1 ? 'member' : 'members'}
                              </div>
                            </div>
                          </div>
                        </div>
                      </button>
                    </div>
                  );
                })}
              </div>

            </>
          )}

        </div>
      </Credenza.Body>

      <Credenza.Footer>
        <Button
          variant="secondary"
          onClick={handleClose}
        >
          Cancel
        </Button>
        {workspaces.length > 0 && (
          <Button
            type="submit"
            isLoading={isLoading}
            onClick={handleSave}
          >
            Save
          </Button>
        )}
      </Credenza.Footer>
    </Credenza.Root >
  );
};

export default EditWorkspacesModal;
