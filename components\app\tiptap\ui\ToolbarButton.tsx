import classNames from "classnames";
import { ReactNode } from "react";

type ToolbarButtonProps = {
  children: ReactNode;
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  isActive?: boolean | (() => boolean);
  isDisabled?: boolean;
  className?: string;
};

const ToolbarButton = ({
  children,
  onClick,
  isActive = false,
  isDisabled = false,
  className,
}: ToolbarButtonProps) => {
  const isButtonActive = typeof isActive === "function" ? isActive() : isActive;

  return (
    <button
      onClick={onClick}
      disabled={isDisabled}
      className={classNames(
        "hover:bg-slate rounded-sm px-1.5 py-1 transition-colors disabled:cursor-not-allowed disabled:opacity-50",
        {
          "text-secondary": isButtonActive,
        },
        className
      )}
    >
      {children}
    </button>
  );
};

export default ToolbarButton;
