import { useViewportSize } from "@mantine/hooks";
import { Editor } from "@tiptap/react";
import { useEffect, useRef, useState } from "react";
import { Tooltip } from "~/components";
import { useBreakpoints } from "~/hooks";

const EditorHeightTooltip = ({
  editor,
  editorContainer,
}: {
  editor: Editor;
  editorContainer?: HTMLElement | null;
}) => {
  const [showTooltip, setShowTooltip] = useState<boolean>(false);
  const [tooltipYPosition, setTooltipYPosition] = useState<number>(52);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const { width } = useViewportSize();
  const { isMobile } = useBreakpoints();

  useEffect(() => {
    if (!editor || !editor.view || !editor.view.dom) return;

    const calculatePosition = () => {
      const editorElement = editor.view.dom;
      if (editorElement) {
        // Get the actual height of the editor content
        const editorRect = editorElement.getBoundingClientRect();
        const contentHeight = editorRect.height;
        const lineHeight = 26;

        // Calculate the target line position based on line height
        const targetLine = isMobile ? 5 : 3;
        const cutoffPosition = (targetLine - 1) * lineHeight;

        // Show tooltip when content height exceeds the cutoff position
        const shouldShow = contentHeight > cutoffPosition;

        setShowTooltip(shouldShow);

        if (shouldShow) {
          setTooltipYPosition(cutoffPosition);
        } else {
          setTooltipYPosition(52);
        }
      }
    };

    calculatePosition();

    const resizeObserver = new ResizeObserver(calculatePosition);
    resizeObserver.observe(editor.view.dom);

    window.addEventListener("resize", calculatePosition);
    editor.on("update", calculatePosition);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener("resize", calculatePosition);
      editor.off("update", calculatePosition);
    };
  }, [editor, width]);

  useEffect(() => {
    if (editorContainer && tooltipRef.current) {
      const updateUnderlinePosition = () => {
        const tooltipElement = tooltipRef.current;
        if (tooltipElement && editorContainer) {
          const tooltipRect = tooltipElement.getBoundingClientRect();
          const editorRect = editorContainer.getBoundingClientRect();

          const distanceToLeft = tooltipRect.left - editorRect.left;

          // Set CSS properties for the underline position
          tooltipElement.style.setProperty(
            "--underline-width",
            `calc(100% + ${distanceToLeft}px)`
          );
          tooltipElement.style.setProperty(
            "--underline-left",
            `-${distanceToLeft}px`
          );
        }
      };

      updateUnderlinePosition();
      window.addEventListener("resize", updateUnderlinePosition);

      return () => {
        window.removeEventListener("resize", updateUnderlinePosition);
      };
    }
  }, [editorContainer, showTooltip]);

  if (!showTooltip) return null;

  return (
    <div
      className="text-right"
      style={{
        marginTop: tooltipYPosition ? `${tooltipYPosition}px` : "52px",
        height: "26px",
      }}
    >
      <Tooltip
        side="top"
        align="end"
        value="Shows how much of your post is visible by default in LinkedIn's feed before users have to click “...more”."
      >
        <div className="group relative" ref={tooltipRef}>
          <span className="text-medium-dark-gray hover:text-dark-gray font-body-sm cursor-default transition-colors">
            ...more
          </span>
          <div
            className="pointer-events-none absolute right-0 bottom-0 opacity-0 transition-opacity duration-200 group-hover:opacity-100"
            style={{
              width: "var(--underline-width, 100%)",
              left: "var(--underline-left, 0px)",
            }}
          >
            <div className="bg-medium-gray h-px w-full" />
          </div>
        </div>
      </Tooltip>
    </div>
  );
};

export default EditorHeightTooltip;
