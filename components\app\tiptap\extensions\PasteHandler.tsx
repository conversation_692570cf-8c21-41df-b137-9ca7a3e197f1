import { Extension } from "@tiptap/core";
import { Plugin } from "prosemirror-state";
import { formatTextAsTipTapEditorState } from "~/utils";

const PasteHandler = Extension.create({
  name: "pasteHandler",

  addProseMirrorPlugins() {
    return [
      new Plugin({
        props: {
          handlePaste: (view, event) => {
            const { state } = view;
            const { selection } = state;
            const { $from } = selection;

            // Check if we're inside a code block
            const isInCodeBlock = $from.parent.type.name === "codeBlock";

            const text = event.clipboardData
              ?.getData("text/plain")
              .replaceAll("\r", "\n")
              .replaceAll("\n\n\n\n", "\n\n")
              // Remove tabs at beginning of lines
              .replace(/^(\t+)/gm, "")
              // Replace tabs in middle of lines with spaces
              .replace(/([^\n])\t/g, "$1 ")
              // Replace non-breaking spaces with spaces
              .replaceAll(" ", " ");
            if (!text) return false;

            // If we're in a code block, just insert the raw text
            if (isInCodeBlock) {
              this.editor.commands.insertContent(text);
              return true;
            }

            // This is to handle pasts from Google Docs and Airtable
            if (text.includes("\n")) {
              const content = formatTextAsTipTapEditorState(text).content;
              if (content) {
                this.editor.commands.insertContent(content);
                return true;
              }
            } else {
              this.editor.commands.insertContent(text);
              return true;
            }

            return false;
          },
          handleDOMEvents: {
            copy: (view, event) => {
              const { state } = view;
              const { empty } = state.selection;

              if (empty) return false;

              // Get the selected text and normalize newlines
              const text = view.dom.ownerDocument
                .getSelection()
                ?.toString()
                ?.replaceAll("\n\n", "\n")
                ?.replaceAll("\r", "\n");

              if (!text) return false;

              // Set the normalized text in clipboard
              event.clipboardData?.setData("text/plain", text);
              event.preventDefault();
              return true;
            },
          },
        },
      }),
    ];
  },
});

export default PasteHandler;
