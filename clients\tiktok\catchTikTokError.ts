import axios from "axios";
import { StatusCodes } from "http-status-codes";
import { createErrorResponse, Response } from "../Response";

const catchTikTokError = (
  error: unknown,
  defaultErrorMessage: string
): Response<any> => {
  if (axios.isAxiosError(error)) {
    const response = error.response;

    const isAuthError = response?.status === StatusCodes.UNAUTHORIZED;

    console.log("Status Code", response?.status);
    console.log("TikTok Error", response?.data?.error || response?.statusText);

    const rawError = response?.data?.error || response?.statusText;

    if (typeof rawError === "string") {
      return createErrorResponse(
        [rawError],
        response?.status || StatusCodes.UNPROCESSABLE_ENTITY,
        rawError,
        isAuthError
      );
    } else {
      const message = rawError.message as string;

      return createErrorResponse(
        [message],
        response?.status || StatusCodes.UNPROCESSABLE_ENTITY,
        rawError,
        isAuthError
      );
    }
  } else {
    return createErrorResponse(
      [defaultErrorMessage],
      StatusCodes.UNPROCESSABLE_ENTITY
    );
  }
};

export default catchTikTokError;
