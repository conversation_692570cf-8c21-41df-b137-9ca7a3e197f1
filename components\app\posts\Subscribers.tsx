import { BellIcon, BellSlashIcon } from "@heroicons/react/24/outline";
import { User } from "@prisma/client";
import difference from "lodash/difference";
import { useRouter } from "next/router";
import { toast } from "sonner";
import { Combobox, Tooltip } from "~/components";
import Avatar from "~/components/ui/Avatar";
import Button from "~/components/ui/Button";
import { useUser } from "~/providers";
import { getFullName, handleTrpcError, trpc } from "~/utils";

export type Subscriber = {
  id: string;
  userID: string;
  user: {
    firstName: string | null;
    lastName: string | null;
  };
};

type OptionValue = {
  id: string;
  firstName: string | null;
  lastName: string | null;
};

type Props = {
  id: string;
  type: "post" | "campaign";
  isReadOnly: boolean;
  workspaceUsers: User[];
};

const Subscribers = ({ id, type, isReadOnly, workspaceUsers }: Props) => {
  const { user } = useUser();
  const trpcUtils = trpc.useUtils();
  const router = useRouter();

  const subscribersQuery = trpc.subscriber.getForContent.useQuery(
    { id, type },
    {
      refetchInterval: 5000,
    }
  );

  const subscribers = subscribersQuery.data?.subscribers || [];
  const isLoading = subscribersQuery.isLoading;

  const updateSubscriberMutation = trpc.subscriber.updateSubscriber.useMutation(
    {
      onMutate: async ({ userID, action }) => {
        await trpcUtils.subscriber.getForContent.cancel({ id, type });

        const previousData = trpcUtils.subscriber.getForContent.getData({
          id,
          type,
        });

        // Optimistically update
        if (userID === user?.id) {
          const message =
            action === "add" ? "Subscribed to Post" : "Unsubscribed from Post";
          const description =
            action === "add"
              ? "Turned on notifications for this post"
              : "Turned off notifications for this post";

          toast.success(message, {
            description,
            action: {
              label: "Settings",
              onClick: () => {
                router.push("/settings/notifications");
              },
            },
          });
        }

        if (previousData) {
          if (action === "add") {
            const userToAdd = workspaceUsers.find((user) => user.id === userID);
            if (userToAdd) {
              trpcUtils.subscriber.getForContent.setData(
                { id, type },
                {
                  subscribers: [
                    ...previousData.subscribers,
                    {
                      id: `temp-${Date.now()}`,
                      userID,
                      user: userToAdd,
                      createdByID: user!.id,
                      createdAt: new Date(),
                      updatedAt: new Date(),
                      campaignID: type === "campaign" ? id : null,
                      postID: type === "post" ? id : null,
                    },
                  ],
                }
              );
            }
          } else {
            trpcUtils.subscriber.getForContent.setData(
              { id, type },
              {
                subscribers: previousData.subscribers.filter(
                  (subscriber) => subscriber.userID !== userID
                ),
              }
            );
          }
        }

        return { previousData };
      },
      onError: (error) => {
        handleTrpcError({
          title: "Failed to Update Subscribers",
          error: error,
        });
      },
    }
  );

  const updateSubscriberStatus = (userID: string, action: "add" | "remove") => {
    updateSubscriberMutation.mutate({
      id,
      userID,
      type,
      action,
    });
  };

  const userOptions = workspaceUsers.map((user) => ({
    key: `${user.firstName} ${user.lastName}`,
    value: {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
    },
    label: (
      <div className="flex items-center gap-2">
        <Avatar.Root className="h-5 w-5">
          <Avatar.Fallback className="bg-secondary font-sans text-[8px] leading-none text-white">
            {getFullName(user, { initialsOnly: true })}
          </Avatar.Fallback>
        </Avatar.Root>
        <span>{getFullName(user)}</span>
      </div>
    ),
  }));

  const selectedOptions = userOptions.filter((option) =>
    subscribers.some((subscriber) => subscriber.userID === option.value.id)
  );

  const isCurrentUserSubscribed = subscribers.some(
    (subscriber) => subscriber.userID === user?.id
  );

  if (isLoading) return null;

  return (
    <div className="flex items-center gap-3">
      {!isReadOnly && (
        <Button
          size="sm"
          variant="ghost"
          onClick={() => {
            if (!user) return;
            const action = isCurrentUserSubscribed ? "remove" : "add";
            updateSubscriberStatus(user.id, action);
          }}
        >
          {isCurrentUserSubscribed ? (
            <div className="flex items-center gap-2">
              <BellSlashIcon className="h-4 w-4" />
              Unsubscribe
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <BellIcon className="h-4 w-4" />
              Subscribe
            </div>
          )}
        </Button>
      )}
      <Combobox
        value={selectedOptions}
        onChange={(newSelectedOptions) => {
          const currentUserIDs = subscribers.map(
            (subscriber) => subscriber.userID
          );
          const newUserIDs = newSelectedOptions.map(
            (option: OptionValue) => option.id
          );

          const usersToRemove = difference(currentUserIDs, newUserIDs);
          usersToRemove.forEach((userID) => {
            updateSubscriberStatus(userID, "remove");
          });

          const usersToAdd = difference(newUserIDs, currentUserIDs);
          usersToAdd.forEach((userID) => {
            updateSubscriberStatus(userID, "add");
          });
        }}
        options={userOptions}
        isMulti={true}
        isSearchable={true}
        disabled={isReadOnly}
        triggerButton={
          subscribers.length > 0 ? (
            <div className="flex cursor-pointer -space-x-3">
              {subscribers.slice(0, 3).map((subscriber) => (
                <Tooltip
                  key={subscriber.id}
                  value={getFullName(subscriber.user)}
                >
                  <Avatar.Root className="h-8 w-8">
                    <Avatar.Fallback className="font-body-xs bg-secondary flex items-center justify-center border-2 border-white !text-[10px] leading-none text-white">
                      {getFullName(subscriber.user, { initialsOnly: true })}
                    </Avatar.Fallback>
                  </Avatar.Root>
                </Tooltip>
              ))}
            </div>
          ) : (
            <img
              src="/empty_profile_image.png"
              alt="empty profile"
              className="h-8 w-8 border-2 border-white"
            />
          )
        }
      />
    </div>
  );
};

export default Subscribers;
