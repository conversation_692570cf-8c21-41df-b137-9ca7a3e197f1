import ffmpegPath from "ffmpeg-static";
import { path as ffprobePath } from "ffprobe-static";
import ffmpeg from "fluent-ffmpeg";

interface CodecInfo {
  videoCodec: string | null;
  audioCodec: string | null;
}

const getCodecs = async (videoUrl: string): Promise<CodecInfo> => {
  ffmpeg.setFfmpegPath(ffmpegPath ?? "");
  ffmpeg.setFfprobePath(ffprobePath ?? "");

  const codecInfo: CodecInfo = await new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoUrl, (err, metadata) => {
      if (err) {
        return reject("Error retrieving metadata: " + err);
      }

      const videoStream = metadata.streams.find(
        (stream) => stream.codec_type === "video"
      );
      const audioStream = metadata.streams.find(
        (stream) => stream.codec_type === "audio"
      );

      resolve({
        videoCodec: videoStream?.codec_name ?? null,
        audioCodec: audioStream?.codec_name ?? null,
      });
    });
  });

  return codecInfo;
};

export default getCodecs;
