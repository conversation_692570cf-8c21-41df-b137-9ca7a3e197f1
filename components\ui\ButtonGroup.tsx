import * as React from "react";
import { cn } from "~/utils"; // or use classnames if you prefer
import SeparatorBase from "./Separator";

const Root = ({
  children,
  className,
}: React.PropsWithChildren<{ className?: string }>) => {
  return (
    <div
      className={cn(
        "border-lightest-gray inline-flex h-10 items-center gap-0 overflow-hidden rounded-md border",
        className
      )}
    >
      {children}
    </div>
  );
};

const Button = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ className, children, ...props }, ref) => (
  <button
    ref={ref}
    className={cn(
      "font-body-sm text-basic hover:bg-basic/10 focus:ring-primary m-1 inline-flex items-center gap-2 rounded-sm px-3 py-1.5 font-medium transition-colors focus:ring-2 focus:outline-none",
      className
    )}
    {...props}
  >
    {children}
  </button>
));
Button.displayName = "ButtonGroupButton";

const Separator = () => {
  return <SeparatorBase orientation="vertical" className="h-[65%]" />;
};

const Details = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="border-lightest-gray bg-lightest-gray-30 -ml-px flex h-10 items-center rounded-r-md border px-3">
      <div className="text-medium-dark-gray font-body-sm font-medium">
        {children}
      </div>
    </div>
  );
};

export default {
  Root,
  Button,
  Details,
  Separator,
};
