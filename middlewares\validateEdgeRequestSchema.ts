import { StatusCodes } from "http-status-codes";
import { NextRequest } from "next/server";
import { ZodIssue, ZodType } from "zod";

const zodIssueToErrors = (issues: ZodIssue[]) => {
  return issues.map((issue) => {
    const code = issue.code === "invalid_type" ? "Invalid Type" : issue.code;

    if (issue.message) {
      return `${issue.path[0]}: ${issue.message}`;
    } else {
      // @ts-ignore
      return `${issue.path[0]} ${code}: Expected ${issue.expected} & received ${issue.received}`;
    }
  });
};

const validateEdgeRequestSchema = (
  handler: (req: NextRequest, body: any) => Promise<Response>,
  schema: {
    body?: ZodType;
    query?: ZodType;
  }
) => {
  return async (req: NextRequest) => {
    const body = await req.json();
    if (schema.body) {
      const parseResult = schema.body.safeParse(body);

      if (!parseResult.success) {
        return new Response(
          zodIssueToErrors(parseResult.error.issues).join("\n"),
          { status: StatusCodes.UNPROCESSABLE_ENTITY }
        );
      }
    }

    if (schema.query) {
      const query = new URL(req.url);
      const parseResult = schema.query.safeParse(query);

      if (!parseResult.success) {
        return new Response(
          zodIssueToErrors(parseResult.error.issues).join("\n"),
          { status: StatusCodes.UNPROCESSABLE_ENTITY }
        );
      }
    }

    return handler(req, body);
  };
};

export default validateEdgeRequestSchema;
