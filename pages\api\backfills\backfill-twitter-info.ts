import getAuthedTwitterClient from "apiUtils/getAuthedTwitterClient";
import normalizeTwitterUser from "apiUtils/twitter/normalizeTwitterUser";
import chunk from "lodash/chunk";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const twitterIntegrations = await db.twitterIntegration.findMany({
    include: {
      account: true,
    },
    where: {
      account: {
        accountCreatedAt: null,
      },
    },
  });

  const firstIntegration = twitterIntegrations[0];

  const twitter = getAuthedTwitterClient(firstIntegration);

  const ids = twitterIntegrations.map((integration) => integration.account.id);

  const usernameChunks = chunk(ids, 100);

  for await (const chunk of usernameChunks) {
    const response = await twitter.v2.users(chunk, {
      "user.fields": [
        "id",
        "name",
        "username",
        "created_at",
        "description",
        "entities",
        "location",
        "pinned_tweet_id",
        "profile_image_url",
        "protected",
        "public_metrics",
        "url",
        "verified",
        "verified_type",
        "withheld",
      ],
    });

    const users = response.data;

    await db.$transaction(async (tx) => {
      for await (const rawUser of users) {
        const user = normalizeTwitterUser(rawUser);
        await tx.twitterAccount.update({
          where: {
            id: user.id,
          },
          data: {
            accountCreatedAt: user.accountCreatedAt,
            description: user.description,
            location: user.location,
            name: user.name,
            profileImageUrl: user.profileImageUrl,
            url: user.url,
            username: user.username,
            verified: user.verified,
            verifiedType: user.verifiedType,
            stats: {
              create: {
                ...user.stats,
              },
            },
          },
        });
      }
    });
    console.log({ users, errors: response.errors });
  }

  res.status(200).send({
    numIntegrations: twitterIntegrations.length,
  });
};

export default handler;
