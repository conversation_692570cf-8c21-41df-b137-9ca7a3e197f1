import { InstagramContentType } from "@prisma/client";
import publishUploadedPosts from "apiUtils/instagram/publishUploadedPosts";
import uploadInstagramAssets from "apiUtils/instagram/uploadInstagramAssets";
import notifyOfScheduledPost from "apiUtils/notifyOfScheduledPost";
import assert from "assert";
import { NonRetriableError, RetryAfterError, slugify } from "inngest";
import { DateTime } from "luxon";
import { db } from "~/clients";
import Instagram from "~/clients/facebook/instagram/Instagram";
import { AuthError } from "~/types/ScheduledPostResult";
import { inngest } from "../inngest";

const RETRIES = 4;

export default inngest.createFunction(
  {
    id: slugify("Instagram Publish Post"),
    retries: RETRIES,
    name: "Instagram Publish Post",
    concurrency: {
      limit: 1,
      key: "event.data.postID",
    },
    cancelOn: [
      {
        event: "post.rescheduled",
        if: `event.data.channel == "Instagram" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.unscheduled",
        if: `event.data.channel == "Instagram" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.deleted",
        match: "data.postID",
      },
    ],
    onFailure: async ({ error, event, step }) => {
      const data = event.data.event.data;
      const { postID } = data;
      console.log(`Post:${postID}: Final Error ${error.message}`);

      const isAuthError = error.name === "AuthError";

      const post = await db.post.findFirstOrThrow({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          instagramIntegration: {
            select: {
              account: {
                select: {
                  username: true,
                },
              },
            },
          },
          scheduledPosts: {
            where: {
              channel: "Instagram",
              status: "Scheduled",
            },
          },
        },
      });

      const scheduledPost = post.scheduledPosts[0];

      if (scheduledPost == null) {
        console.log(`Post:${postID}: No longer scheduled`);
        return;
      }

      await notifyOfScheduledPost({
        post,
        scheduledPost,
        status: "Error",
        account: post.instagramIntegration
          ? {
              name: `@${post.instagramIntegration.account.username}`,
              url: `https://www.instagram.com/${post.instagramIntegration.account.username}`,
            }
          : undefined,
        error: error.message,
        isAuthError,
      });
    },
  },
  [
    {
      event: "post.scheduled",
      if: `event.data.channel == "Instagram"`,
    },
    {
      event: "post.rescheduled",
      if: `event.data.channel == "Instagram"`,
    },
  ],
  async ({ event, step, attempt }) => {
    const { delayUntil, postID } = event.data;

    const delayUntil5MinutesBefore = DateTime.fromJSDate(new Date(delayUntil))
      .minus({
        minutes: 2,
      })
      .toJSDate();

    await step.sleepUntil(
      "Upload Assets 2 Minutes Before",
      delayUntil5MinutesBefore
    );

    const getPost = async () => {
      return await db.post.findFirst({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          workspaceID: true,
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
              slackWebhooks: {
                where: {
                  type: "SuccessfulPost",
                },
                select: {
                  type: true,
                  url: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          createdByID: true,
          instagramIntegration: {
            select: {
              id: true,
              account: {
                select: {
                  id: true,
                  externalID: true,
                  username: true,
                },
              },
              accessToken: true,
              integrationSource: true,
            },
          },
          instagramContent: {
            select: {
              id: true,
              copy: true,
              type: true,
              coverPhoto: true,
              collaborators: true,
              reelShareToFeed: true,
              location: {
                select: {
                  pageID: true,
                },
              },
              engagements: {
                select: {
                  id: true,
                  postDelaySeconds: true,
                },
              },
              assets: {
                select: {
                  id: true,
                  position: true,
                  asset: true,
                  mimetype: true,
                  containerID: true,
                  containerExpiresAt: true,
                  tags: {
                    select: {
                      id: true,
                      username: true,
                      x: true,
                      y: true,
                    },
                  },
                },
                orderBy: {
                  position: "asc",
                },
              },
            },
          },
          scheduledPosts: {
            where: {
              channel: "Instagram",
              status: "Scheduled",
            },
          },
          approvals: {
            where: {
              isBlocking: true,
              approvedAt: null,
            },
            select: {
              id: true,
            },
          },
        },
      });
    };

    await step.run("Upload Instagram Assets", async () => {
      const postToUpload = await getPost();

      if (postToUpload == null) {
        console.log(`Post:${postID}: Post not found`);
        return {
          status: "Skipped",
          comment: "Post has been deleted",
        };
      }

      const uploadResponse = await uploadInstagramAssets(postToUpload);

      if (uploadResponse.status === "NonRetriableError") {
        if (uploadResponse.isAuthError) {
          throw new AuthError(uploadResponse.error);
        } else {
          throw new NonRetriableError(uploadResponse.error);
        }
      } else if (uploadResponse.status === "Error") {
        throw new Error(uploadResponse.error);
      }

      return uploadResponse;
    });

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    // We reupload the assets again to ensure that the assets are still ready
    // if they are ready then it's a no-op
    const uploadResponse = await step.run(
      "Backup Reupload Instagram Assets",
      async () => {
        const postToUpload = await getPost();

        if (postToUpload == null) {
          console.log(`Post:${postID}: Post not found`);
          return {
            status: "Skipped",
            comment: "Post has been deleted",
            post: null,
          };
        }
        await uploadInstagramAssets(postToUpload);

        const uploadedPost = await getPost();

        if (uploadedPost == null) {
          throw new NonRetriableError("Post not found");
        }

        return {
          status: "Uploaded",
          post: uploadedPost,
        };
      }
    );

    const post = uploadResponse.post;

    if (post == null) {
      console.log(`Post:${postID}: Post not found`);
      return {
        status: "Skipped",
        comment: "Post has been deleted",
      };
    }

    const instagramContent = post.instagramContent;
    if (instagramContent == null) {
      console.log(`Post:${postID}: Instagram Content not found`);
      return {
        status: "Skipped",
        comment: "Instagram content not found",
      };
    }

    const scheduledPost = post.scheduledPosts[0];

    if (scheduledPost == null) {
      console.log(`Post:${postID}: No longer scheduled`);
      return {
        status: "Skipped",
        comment: "No scheduled posts",
      };
    }

    if (post.approvals.length > 0) {
      console.log(`Post:${postID}: Blocked by approval`);
      return {
        status: "Skipped",
        comment: "Post has been blocked by approval",
      };
    }

    if (instagramContent.type === InstagramContentType.Story) {
      console.log(`Post:${postID}: Publishing Instagram Story`);
      let permalink: string | undefined = undefined;
      const assetToStoryIDs: { [assetID: string]: string } = {};
      for await (const asset of instagramContent.assets) {
        const publishResponse = await step.run(
          `Publish Instagram Story Container ${asset.containerID}`,
          async () => {
            const containerID = asset.containerID;
            if (containerID == null) {
              console.log(
                `Post:${postID}: Publishing Instagram Story Container ${asset.containerID}`
              );
              throw new NonRetriableError("Instagram story was not uploaded");
            } else {
              const instagramIntegration = post.instagramIntegration!;
              const instagram = new Instagram(
                instagramIntegration.accessToken,
                {
                  platform: instagramIntegration.integrationSource,
                }
              );
              const instagramAccountID =
                instagramIntegration.account.externalID;
              const response = await instagram.publishPost({
                instagramAccountID,
                creationID: containerID,
                type: instagramContent.type,
              });

              if (!response.success) {
                if (response.isAuthError) {
                  throw new AuthError(response.errors.join(", "));
                } else {
                  throw new RetryAfterError(response.errors.join(", "), 10_000);
                }
              } else {
                return response.data;
              }
            }
          }
        );

        if (!permalink) {
          permalink = publishResponse.permalink;
        }
        assetToStoryIDs[asset.id] = publishResponse.id;
      }

      await db.$transaction(async (tx) => {
        for await (const assetID of Object.keys(assetToStoryIDs)) {
          await tx.instagramAsset.update({
            where: {
              id: assetID,
            },
            data: {
              storyID: assetToStoryIDs[assetID],
            },
          });
        }
      });

      assert(permalink != null, "Permalink is required");

      await notifyOfScheduledPost({
        post,
        scheduledPost: {
          id: scheduledPost.id,
          channel: scheduledPost.channel,
          publishAt: new Date(scheduledPost.publishAt),
          createdByID: scheduledPost.createdByID,
        },
        status: "Success",
        postUrl: permalink,
      });

      return {
        status: "Success",
        permalink,
      };
    } else {
      console.log(
        `Post:${postID}: Publishing Instagram ${instagramContent.type}`
      );
      const response = await step.run("Publish Instagram Post", async () => {
        console.log(
          `Post:${postID}: Publishing Instagram Post Attempt ${attempt + 1}/${
            RETRIES + 1
          }`
        );

        const post = await getPost();

        if (post == null) {
          throw new NonRetriableError("Post not found");
        }

        const scheduledPost = post.scheduledPosts[0];

        if (scheduledPost == null) {
          throw new NonRetriableError("Post is no longer scheduled");
        }

        const publishResponse = await publishUploadedPosts(post);

        if (publishResponse.status === "NonRetriableError") {
          console.log(
            `Post:${postID}: NonRetriableError ${publishResponse.error}`
          );

          if (publishResponse.isAuthError) {
            throw new AuthError(publishResponse.error);
          } else {
            throw new NonRetriableError(publishResponse.error);
          }
        } else if (publishResponse.status === "Error") {
          console.log(
            `Post:${postID}: Publish Attempt ${attempt + 1}/${
              RETRIES + 1
            } Error ${publishResponse.error}`
          );

          throw new RetryAfterError(publishResponse.error, 10_000);
        } else {
          console.log(`Post:${postID}: Published Instagram Post`);

          await notifyOfScheduledPost({
            post,
            scheduledPost,
            account: {
              name: `@${post.instagramIntegration!.account.username}`,
              url: `https://www.instagram.com/${
                post.instagramIntegration!.account.username
              }`,
            },
            ...publishResponse,
          });

          return publishResponse;
        }
      });

      return response;
    }
  }
);
