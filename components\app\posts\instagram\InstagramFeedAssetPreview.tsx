import {
  ChevronLeftIcon,
  ChevronRightIcon,
  PhotoIcon,
} from "@heroicons/react/24/outline";
import { useInViewport } from "@mantine/hooks";
import { useEffect, useRef, useState } from "react";
import { Carousel } from "react-responsive-carousel";
import { Player } from "video-react";

type Props = {
  assets: {
    id: string;
    signedUrl: string;
    metadata: {
      mimetype: string;
    };
    createdAt: Date;
  }[];
};

const InstagramAssetPreview = ({ assets }: Props) => {
  const [dimensions, setDimensions] = useState<{
    width: number;
    height: number;
  }>({ width: 300, height: 375 });
  const firstImageRef = useRef<HTMLImageElement>(null);
  const firstVideoRef = useRef<HTMLVideoElement>(null);

  const { ref, inViewport } = useInViewport();

  useEffect(() => {
    if (firstImageRef.current) {
      firstImageRef.current.onload = () => {
        const current = firstImageRef.current;
        if (!current) {
          return;
        }

        setDimensions({
          width: current.clientWidth,
          height: current.clientHeight,
        });
      };

      if (firstImageRef.current) {
        setDimensions({
          width: firstImageRef.current.clientWidth,
          height: firstImageRef.current.clientHeight,
        });
      }
    }
  }, [firstImageRef, assets[0]?.signedUrl, inViewport]);

  useEffect(() => {
    if (firstVideoRef.current) {
      firstVideoRef.current.onloadedmetadata = () => {
        const current = firstVideoRef.current;

        const videoWidth = current?.videoWidth;
        const videoHeight = current?.videoHeight;

        if (!videoHeight || !videoWidth) {
          return;
        }

        let width = 0;
        let height = 0;

        if (videoWidth > videoHeight) {
          width = 300;
          height = Math.min((videoHeight / videoWidth) * width, 375);
        } else if (videoWidth === videoHeight) {
          width = 300;
          height = 300;
        } else {
          height = 375;
          width = Math.min((videoWidth / videoHeight) * height, 300);
        }

        setDimensions({
          width,
          height,
        });
      };
    }
  }, [firstVideoRef, assets[0]?.signedUrl]);

  if (assets.length === 0) {
    return (
      <div className="bg-lightest-gray flex h-[240px] w-full items-center justify-center">
        <div className="text-medium-dark-gray flex flex-col items-center gap-2">
          <PhotoIcon className="h-6 w-6" />
          <div className="font-body text-center">
            Upload video or image in the <br />
            editor below
          </div>
        </div>
      </div>
    );
  }

  const numAssets = assets.length;

  if (numAssets === 1) {
    const isVideo = assets[0].metadata.mimetype.startsWith("video");
    if (isVideo) {
      return <Player src={assets[0].signedUrl} />;
    } else {
      return (
        <img
          ref={firstImageRef}
          className="max-h-[375px] w-full object-cover"
          src={assets[0].signedUrl}
        />
      );
    }
  } else {
    return (
      <div ref={ref}>
        <Carousel
          showThumbs={false}
          showStatus={false}
          renderArrowNext={(onClickHandler, hasNext, label) =>
            hasNext && (
              <button
                type="button"
                onClick={onClickHandler}
                title={label}
                className="bg-light-gray absolute top-1/2 right-2 z-10 -translate-y-1/2 transform rounded-full p-0.5 opacity-70"
              >
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            )
          }
          renderArrowPrev={(onClickHandler, hasPrev, label) =>
            hasPrev && (
              <button
                type="button"
                onClick={onClickHandler}
                title={label}
                className="bg-light-gray absolute top-1/2 left-2 z-10 -translate-y-1/2 transform rounded-full p-0.5 opacity-70"
              >
                <ChevronLeftIcon className="h-4 w-4" />
              </button>
            )
          }
        >
          {assets.map((asset, index) => {
            if (asset.metadata.mimetype.startsWith("video")) {
              return (
                <div
                  key={`${asset.signedUrl}-${dimensions.width}-${dimensions.height}}`}
                  className="flex justify-center"
                  style={{
                    width: dimensions.width,
                    height: dimensions.height,
                  }}
                >
                  <video
                    ref={index === 0 ? firstVideoRef : null}
                    controls={true}
                    className="block object-cover"
                    playsInline={true}
                    width={dimensions.width}
                    height={dimensions.height}
                  >
                    <source src={asset.signedUrl} />
                  </video>
                </div>
              );
            } else {
              return (
                <img
                  ref={index === 0 ? firstImageRef : null}
                  key={`${asset.signedUrl}-${dimensions.width}-${dimensions.height}}`}
                  style={{
                    width: index === 0 ? "auto" : dimensions.width,
                    height: index === 0 ? "auto" : dimensions.height,
                  }}
                  className="max-h-[375px] w-full object-cover"
                  src={asset.signedUrl}
                />
              );
            }
          })}
        </Carousel>
      </div>
    );
  }
};

export default InstagramAssetPreview;
