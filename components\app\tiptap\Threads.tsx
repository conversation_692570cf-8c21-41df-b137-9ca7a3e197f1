import { FloatingThreads } from "@liveblocks/react-tiptap";
import { useThreads } from "@liveblocks/react/suspense";
import { Editor } from "@tiptap/react";
import { useEffect, useState } from "react";

export const Threads = ({ editor }: { editor: Editor }) => {
  const { threads } = useThreads();
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const isReadOnly = editor.isEditable === false;

  // Close threads component when clicking outside of the editor e.g when clicking on another channel
  useEffect(() => {
    const handleFocus = () => setIsVisible(true);

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      if (editor.view.dom.contains(target)) {
        return;
      }

      // Don't hide if clicking on threads UI
      if (
        target.closest(
          `
            .lb-root,
            .lb-threads-container,
            .lb-composer,
            .lb-portal,
            [role="menu"],
            [aria-haspopup="menu"]
          `
        )
      ) {
        return;
      }

      setIsVisible(false);
    };

    editor.on("focus", handleFocus);

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      editor.off("focus", handleFocus);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [editor]);

  return (
    <FloatingThreads
      editor={editor}
      threads={threads}
      className={`w-96 ${isReadOnly ? "read-only-threads" : ""}`}
      hidden={isReadOnly ? false : !isVisible}
    />
  );
};
