import {
  ThreadsPost as PrismaThreadsPost,
  ThreadsMediaProductType,
  ThreadsMediaType,
} from "@prisma/client";
import axios, { AxiosInstance } from "axios";
import { StatusCodes } from "http-status-codes";
import { DateTime } from "luxon";
import { removeEmptyElems } from "~/utils";
import {
  Response,
  createErrorResponse,
  createSuccessResponse,
} from "../Response";
import catchThreadsError from "./catchThreadsError";

type ThreadsPost = Omit<
  PrismaThreadsPost,
  "threadsUserID" | "createdAt" | "updatedAt"
> & {
  threadsUser: {
    id: string | null;
    username: string;
  };
};

class Threads {
  BASE_URL = "https://graph.threads.net/v1.0";

  accessToken: string;
  threadsUserID: string;
  client: AxiosInstance;

  constructor({
    accessToken,
    threadsUserID,
  }: {
    accessToken: string;
    threadsUserID: string;
  }) {
    this.client = axios.create({
      baseURL: this.BASE_URL,
    });
    this.threadsUserID = threadsUserID;
    this.accessToken = accessToken;
  }

  // https://developers.facebook.com/docs/threads/get-started/long-lived-tokens#get-a-long-lived-token
  static getOauthToken = async (
    accessToken: string
  ): Promise<
    Response<{
      accessToken: string;
      expiresAt: Date;
      threadsUserID: string;
    }>
  > => {
    try {
      const { data, status } = await axios.get(
        "https://graph.threads.net/access_token",
        {
          params: {
            grant_type: "th_exchange_token",
            access_token: accessToken,
            client_secret: process.env.THREADS_CLIENT_SECRET,
          },
        }
      );

      const { data: meData } = await axios.get(
        "https://graph.threads.net/v1.0/me",
        {
          params: {
            access_token: data.access_token,
          },
        }
      );

      const threadsUserID = meData.id;

      return createSuccessResponse(
        {
          accessToken: data.access_token,
          expiresAt: DateTime.now()
            .plus({ seconds: data.expires_in })
            .toJSDate(),
          threadsUserID,
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error getting oauth token");
    }
  };

  // https://developers.facebook.com/docs/threads/get-started/long-lived-tokens#refresh-a-long-lived-token
  static refreshAccessToken = async (
    accessToken: string
  ): Promise<
    Response<{
      accessToken: string;
      expiresAt: Date;
    }>
  > => {
    try {
      const { data, status } = await axios.get(
        "https://graph.threads.net/refresh_access_token",
        {
          params: {
            grant_type: "th_refresh_token",
            access_token: accessToken,
          },
        }
      );

      return createSuccessResponse(
        {
          accessToken: data.access_token,
          expiresAt: DateTime.now()
            .plus({ seconds: data.expires_in })
            .toJSDate(),
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error refreshing access token");
    }
  };

  _extractProfileImageExpiration = (
    profileImageUrl: string | null
  ): Date | null => {
    if (!profileImageUrl) return null;

    try {
      // The URL looks like https://scontent-iad3-1.cdninstagram.com/v/t51.2885-15/359474175_140931915690138_8178789541364249254_n.jpg?_nc_cat=101&ccb=1-7&_nc_sid=18de74&_nc_ohc=t_wCpgm5RdEQ7kNvgG8FRAz&_nc_ht=scontent-iad3-1.cdninstagram.com&edm=AP4hL3IEAAAA&oh=00_AYBBxXsiSCi331NttsN65ABqpk0HIE-cd75MTjq8ZJZkBQ&oe=66F6A70F
      const url = new URL(profileImageUrl);
      const oe = url.searchParams.get("oe");
      const unixTimestamp = parseInt(oe!, 16) * 1000;

      return new Date(unixTimestamp);
    } catch (error) {
      return null;
    }
  };

  // https://developers.facebook.com/docs/threads/threads-profiles#retrieve-a-threads-user-s-profile-information
  getMe = async (): Promise<
    Response<{
      id: string;
      name: string | null;
      username: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
      biography: string | null;
      followerCount: number;
    }>
  > => {
    try {
      const { status, data } = await this.client.get("/me", {
        params: {
          access_token: this.accessToken,
          fields: [
            "id",
            "username",
            "name",
            "threads_profile_picture_url",
            "threads_biography",
            "threads_insights.metric(followers_count)",
          ].join(","),
        },
      });

      const account = data as {
        id: string;
        username: string;
        name?: string;
        threads_profile_picture_url?: string;
        threads_biography?: string;
        threads_insights: {
          data: {
            name: string;
            total_value: {
              value: number;
            };
          }[];
        };
      };

      const profileImageUrl = account.threads_profile_picture_url || null;
      const profileImageExpiresAt =
        this._extractProfileImageExpiration(profileImageUrl);

      const followerCount = account.threads_insights.data.find(
        (insight) => insight.name === "followers_count"
      )!.total_value.value as number;

      return createSuccessResponse(
        {
          id: account.id,
          name: account.name || null,
          username: account.username,
          profileImageUrl,
          profileImageExpiresAt,
          biography: account.threads_biography || null,
          followerCount,
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error getting me");
    }
  };

  // https://developers.facebook.com/docs/threads/threads-profiles#retrieve-a-threads-user-s-profile-information
  getThreadsAccount = async ({
    threadsUserID,
  }: {
    threadsUserID: string;
  }): Promise<
    Response<{
      id: string;
      name: string;
      username: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
      biography: string | null;
    }>
  > => {
    try {
      const { status, data } = await this.client.get(`/${threadsUserID}`, {
        params: {
          access_token: this.accessToken,
          fields: [
            "id",
            "username",
            "name",
            "threads_profile_picture_url",
            "threads_biography",
          ].join(","),
        },
      });

      const account = data as {
        id: string;
        username: string;
        name: string;
        threads_profile_picture_url?: string;
        threads_biography?: string;
      };

      const profileImageUrl = account.threads_profile_picture_url || null;
      const profileImageExpiresAt =
        this._extractProfileImageExpiration(profileImageUrl);

      return createSuccessResponse(
        {
          id: account.id,
          name: account.name,
          username: account.username,
          profileImageUrl,
          profileImageExpiresAt,
          biography: account.threads_biography || null,
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error getting threads account");
    }
  };

  _createThreadContainer = async ({
    asset,
    text,
    isCarouselItem,
    replyToID,
  }: {
    asset:
      | {
          url: string;
          mimetype: string;
        }
      | null
      | undefined;
    text?: string;
    isCarouselItem: boolean;
    replyToID?: string | null;
  }): Promise<
    Response<{
      containerID: string;
    }>
  > => {
    try {
      const isText = asset == null;
      const isImage = asset?.mimetype.includes("image");
      const isVideo = asset?.mimetype.includes("video");
      const mediaType = isText ? "TEXT" : isVideo ? "VIDEO" : "IMAGE";

      const { status, data } = await this.client.post(
        `${this.threadsUserID}/threads`,
        {
          access_token: this.accessToken,
          is_carousel_item: isText ? undefined : isCarouselItem,
          media_type: mediaType,
          image_url: isImage ? asset!.url : undefined,
          video_url: isVideo ? asset!.url : undefined,
          text: isCarouselItem ? undefined : text,
          reply_to_id: replyToID ?? undefined,
        }
      );

      const containerID = data.id;

      return createSuccessResponse(
        {
          containerID,
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error creating thread container");
    }
  };

  // https://developers.facebook.com/docs/threads/posts#single-thread-posts
  createThreadContainer = async ({
    asset,
    text,
    replyToID,
  }: {
    asset:
      | {
          url: string;
          mimetype: string;
        }
      | null
      | undefined;
    text: string;
    replyToID?: string | null;
  }): Promise<
    Response<{
      containerID: string;
    }>
  > => {
    try {
      const threadContainerResponse = await this._createThreadContainer({
        asset,
        text,
        isCarouselItem: false,
        replyToID,
      });

      return threadContainerResponse;
    } catch (error) {
      return catchThreadsError(error, "Error creating thread container");
    }
  };

  // https://developers.facebook.com/docs/threads/posts#step-1--create-an-item-container
  createCarouselItemContainers = async ({
    assets,
  }: {
    assets: {
      id: string;
      url: string;
      mimetype: string;
    }[];
  }): Promise<Response<{ [assetID: string]: string }>> => {
    try {
      const containerPromises = assets.map(async (asset) => {
        return await this._createThreadContainer({
          asset,
          isCarouselItem: true,
        });
      });

      const containerResponses = await Promise.all(containerPromises);

      const failedResponses = containerResponses.filter(
        (response) => response.status !== StatusCodes.OK
      );

      if (failedResponses.length > 0) {
        return createErrorResponse(
          failedResponses.flatMap((response) => response.errors ?? []),
          StatusCodes.BAD_REQUEST
        );
      }

      const containerIDs = containerResponses.map(
        (response) => response.data!.containerID
      );

      const assetIDToContainerID = containerIDs.reduce(
        (acc, containerID, index) => {
          acc[assets[index].id] = containerID;
          return acc;
        },
        {} as { [assetID: string]: string }
      );

      return createSuccessResponse(assetIDToContainerID, StatusCodes.CREATED);
    } catch (error) {
      return catchThreadsError(
        error,
        "Error creating carousel item containers"
      );
    }
  };

  // https://developers.facebook.com/docs/threads/posts#step-2--create-a-carousel-container
  createCarouselContainer = async ({
    carouselItemContainerIDs,
    text,
    replyToID,
  }: {
    carouselItemContainerIDs: string[];
    text: string | null;
    replyToID?: string | null;
  }): Promise<
    Response<{
      containerID: string;
    }>
  > => {
    try {
      const containerStatusPromises = carouselItemContainerIDs.map(
        async (containerID) => {
          return await this.getContainerStatus({ containerID });
        }
      );

      const containerStatusResponses = await Promise.all(
        containerStatusPromises
      );

      if (containerStatusResponses.some((response) => response.errors)) {
        return createErrorResponse(
          containerStatusResponses.flatMap((response) => response.errors ?? []),
          StatusCodes.BAD_REQUEST
        );
      }

      const erroredResponses = containerStatusResponses.filter(
        (response) => response.data?.status === "ERROR"
      );
      if (erroredResponses.length > 0) {
        return createErrorResponse(
          removeEmptyElems(
            erroredResponses.map((response) => response.data?.errorMessage)
          ),
          StatusCodes.BAD_REQUEST
        );
      }

      const publishedResponses = containerStatusResponses.filter(
        (response) => response.data?.status === "PUBLISHED"
      );
      if (publishedResponses.length > 0) {
        return createErrorResponse(
          ["Carousel item containers already published"],
          StatusCodes.BAD_REQUEST
        );
      }

      const expiredResponses = containerStatusResponses.filter(
        (response) => response.data?.status === "EXPIRED"
      );
      if (expiredResponses.length > 0) {
        return createErrorResponse(
          ["Carousel item containers already expired"],
          StatusCodes.BAD_REQUEST
        );
      }

      const pendingResponses = containerStatusResponses.filter(
        (response) => response.data?.status === "IN_PROGRESS"
      );
      if (pendingResponses.length > 0) {
        return createErrorResponse(
          ["Carousel item containers are still uploading"],
          StatusCodes.PROCESSING
        );
      }

      const { status, data } = await this.client.post(
        `${this.threadsUserID}/threads`,
        {
          access_token: this.accessToken,
          media_type: "CAROUSEL",
          children: carouselItemContainerIDs.join(","),
          text: text ?? undefined,
          reply_to_id: replyToID ?? undefined,
        }
      );

      return createSuccessResponse(
        {
          containerID: data.id,
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error creating carousel container");
    }
  };

  // https://developers.facebook.com/docs/threads/troubleshooting#publishing-does-not-return-a-media-id
  getContainerStatus = async ({
    containerID,
  }: {
    containerID: string;
  }): Promise<
    Response<
      | {
          status: "EXPIRED" | "FINISHED" | "IN_PROGRESS" | "PUBLISHED";
          errorMessage: null;
        }
      | {
          status: "ERROR";
          errorMessage: string;
        }
    >
  > => {
    try {
      const { status, data } = await this.client.get(`${containerID}`, {
        params: {
          access_token: this.accessToken,
          fields: ["id", "status", "error_message"].join(","),
        },
      });

      return createSuccessResponse(
        {
          status: data.status,
          errorMessage: data.error_message || null,
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error getting container status");
    }
  };

  // https://developers.facebook.com/docs/threads/posts#step-3--publish-the-carousel-container
  publishContainer = async ({
    containerID,
  }: {
    containerID: string;
  }): Promise<Response<{ mediaID: string }>> => {
    try {
      const { status, data } = await this.client.post(
        `${this.threadsUserID}/threads_publish`,
        { access_token: this.accessToken, creation_id: containerID }
      );

      return createSuccessResponse(
        {
          mediaID: data.id,
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error publishing post");
    }
  };

  _convertThreadsPost = (post: {
    id: string;
    media_product_type: ThreadsMediaProductType;
    media_type: ThreadsMediaType;
    media_url: string | undefined;
    shortcode: string;
    permalink: string;
    owner:
      | {
          id: string;
        }
      | undefined;
    username: string;
    children:
      | {
          data: {
            id: string;
          }[];
        }
      | undefined;
    text: string | undefined;
    is_quote_post: boolean;
    timestamp: string;
  }): ThreadsPost => {
    return {
      id: post.id,
      mediaProductType: post.media_product_type,
      mediaType: post.media_type,
      permalink: post.permalink,
      threadsUser: {
        id: post.owner?.id ?? null,
        username: post.username,
      },
      text: post.text ?? null,
      isQuotePost: post.is_quote_post,
      shortcode: post.shortcode,
      mediaUrl: post.media_url ?? null,
      childrenIDs: post.children?.data.map((child) => child.id) ?? [],
      publishedAt: new Date(post.timestamp),
    };
  };

  // https://developers.facebook.com/docs/threads/threads-media#retrieve-a-single-threads-media-object
  getThreadsPost = async ({
    mediaID,
  }: {
    mediaID: string;
  }): Promise<Response<{ post: ThreadsPost }>> => {
    try {
      const { status, data } = await this.client.get(`/${mediaID}`, {
        params: {
          fields: [
            "id",
            "media_product_type",
            "media_type",
            "media_url",
            "permalink",
            "owner",
            "username",
            "text",
            "timestamp",
            "shortcode",
            "thumbnail_url",
            "children",
            "is_quote_post",
          ].join(","),
          access_token: this.accessToken,
        },
      });

      return createSuccessResponse(
        {
          post: this._convertThreadsPost(data),
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error getting threads post");
    }
  };

  getThreadsPosts = async (
    {
      threadsUserID,
    }: {
      threadsUserID: string;
    } = {
      threadsUserID: this.threadsUserID,
    }
  ): Promise<
    Response<{
      posts: ThreadsPost[];
    }>
  > => {
    try {
      const { status, data } = await this.client.get(
        `/${threadsUserID}/threads`,
        {
          params: {
            fields: [
              "id",
              "media_product_type",
              "media_type",
              "media_url",
              "permalink",
              "owner",
              "username",
              "text",
              "timestamp",
              "shortcode",
              "thumbnail_url",
              "children",
              "is_quote_post",
            ].join(","),
            access_token: this.accessToken,
          },
        }
      );

      const posts = data.data;

      return createSuccessResponse(
        {
          posts: posts.map((row: any) => {
            return this._convertThreadsPost(row);
          }),
        },
        status
      );
    } catch (error) {
      return catchThreadsError(error, "Error getting threads posts");
    }
  };
}

export default Threads;
