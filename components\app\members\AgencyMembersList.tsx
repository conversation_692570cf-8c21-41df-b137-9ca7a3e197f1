import { InformationCircleIcon, PencilIcon, TrashIcon, EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import { useState, ReactNode } from "react";
import { toast } from "sonner";
import { Tooltip } from "~/components";
import MembersList, { User } from "./MembersList";
import DropdownMenu from "~/components/ui/DropdownMenu";

interface MemberAction {
  label: string;
  onClick: (user: User) => void;
  variant?: 'destructive';
  icon?: ReactNode;
}

interface AgencyMembersListProps {
  agencyUsers: User[];
  clientUsers: User[];
  pendingUsers: User[];
  isLoading?: boolean;
}

const showAgencyMemberActions = (user: User, actions: MemberAction[], loadingUserId: string | null) => {
  if (actions.length === 0) return null;

  const isLoading = loadingUserId === user.id;

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger disabled={isLoading}>
        <EllipsisHorizontalIcon className={`h-5 w-5 transition-colors ${isLoading
          ? "text-medium-gray cursor-not-allowed"
          : "text-medium-dark-gray hover:text-basic cursor-pointer"
          }`} />
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end">
        <DropdownMenu.Group>
          {actions.map((action, actionIndex) => (
            <DropdownMenu.Item
              key={actionIndex}
              onClick={() => action.onClick(user)}
              disabled={isLoading}
              className={
                action.variant === 'destructive'
                  ? "text-red-600 hover:text-red-700"
                  : ""
              }
            >
              <div className="flex items-center gap-2">
                {action.icon && action.icon}
                {action.label}
              </div>
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Group>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};

const AgencyMembersList = ({
  agencyUsers,
  clientUsers,
  pendingUsers,
  isLoading = false,
}: AgencyMembersListProps) => {
  const [loadingUserId, setLoadingUserId] = useState<string | null>(null);

  const handleEditWorkspaceAccess = (user: User) => {
    toast.info(`Edit workspace access for ${user.firstName} ${user.lastName}`);
    // TODO: Implement edit workspace access functionality
  };

  const handleRemoveUser = async (user: User) => {
    if (window.confirm(`Are you sure you want to remove ${user.firstName} ${user.lastName}?`)) {
      setLoadingUserId(user.id);
      try {
        // TODO: Implement remove user functionality
        toast.success(`${user.firstName} ${user.lastName} has been removed`);
      } catch (error) {
        toast.error("Failed to remove user");
      } finally {
        setLoadingUserId(null);
      }
    }
  };

  const agencyUserActions: MemberAction[] = [
    {
      label: "Edit Workspace Access",
      onClick: handleEditWorkspaceAccess,
      icon: <PencilIcon className="h-4 w-4" />,
    },
    {
      label: "Remove User",
      onClick: handleRemoveUser,
      variant: "destructive",
      icon: <TrashIcon className="h-4 w-4" />,
    },
  ];

  const clientUserActions: MemberAction[] = [
    {
      label: "Edit Workspace Access",
      onClick: handleEditWorkspaceAccess,
      icon: <PencilIcon className="h-4 w-4" />,
    },
    {
      label: "Remove User",
      onClick: handleRemoveUser,
      variant: "destructive",
      icon: <TrashIcon className="h-4 w-4" />,
    },
  ];

  const pendingUserActions: MemberAction[] = [
    {
      label: "Remove User",
      onClick: handleRemoveUser,
      variant: "destructive",
      icon: <TrashIcon className="h-4 w-4" />,
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="text-medium-dark-gray">Loading members...</div>
      </div>
    );
  }

  const isEmpty = agencyUsers.length === 0 && clientUsers.length === 0 && pendingUsers.length === 0;

  return (
    <div className="space-y-8">
      {/* Agency Users Section */}
      {agencyUsers.length > 0 && (
        <MembersList.Root isLoading={false} isEmpty={false}>
          <MembersList.Title>
            <div className="flex items-center gap-2 text-basic text-sm font-medium">
              <div>Agency Users</div>
              <Tooltip value="Agency users are any users that are part of more than 1 of your Client workspace(s)." side="bottom" align="start">
                <InformationCircleIcon className="h-4 w-4 text-medium-dark-gray" />
              </Tooltip>
            </div>
          </MembersList.Title>
          {agencyUsers.map((user) => (
            <MembersList.Member key={user.id}>
              <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
              <MembersList.MemberActions>
                {showAgencyMemberActions(user, agencyUserActions, loadingUserId)}
              </MembersList.MemberActions>
            </MembersList.Member>
          ))}
        </MembersList.Root>
      )}

      {/* Pending Users Section */}
      {pendingUsers.length > 0 && (
        <MembersList.Root isLoading={false} isEmpty={false}>
          <MembersList.Title>
            <div className="text-medium-gray text-sm font-medium">Pending</div>
          </MembersList.Title>
          {pendingUsers.map((user) => (
            <MembersList.Member key={user.id}>
              <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
              <MembersList.MemberActions>
                {showAgencyMemberActions(user, pendingUserActions, loadingUserId)}
              </MembersList.MemberActions>
            </MembersList.Member>
          ))}
        </MembersList.Root>
      )}

      {/* Client Users Section */}
      {clientUsers.length > 0 && (
        <MembersList.Root isLoading={false} isEmpty={false}>
          <MembersList.Title>
            <div className="text-basic text-sm font-medium">Client Users</div>
          </MembersList.Title>
          {clientUsers.map((user) => (
            <MembersList.Member key={user.id}>
              <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
              <MembersList.MemberActions>
                {showAgencyMemberActions(user, clientUserActions, loadingUserId)}
              </MembersList.MemberActions>
            </MembersList.Member>
          ))}
        </MembersList.Root>
      )}

      {/* Empty State */}
      {isEmpty && (
        <div className="flex justify-center py-12">
          <div className="text-center">
            <div className="text-medium-dark-gray mb-2">No members found</div>
            <div className="text-sm text-light-gray">
              Invite members to get started
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgencyMembersList;
