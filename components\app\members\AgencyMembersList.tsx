import { InformationCircleIcon, PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { toast } from "sonner";
import { Tooltip } from "~/components";
import MembersList, { User, MemberAction } from "./MembersList";

interface AgencyMembersListProps {
  agencyUsers: User[];
  clientUsers: User[];
  pendingUsers: User[];
  isLoading?: boolean;
}

const AgencyMembersList = ({
  agencyUsers,
  clientUsers,
  pendingUsers,
  isLoading = false,
}: AgencyMembersListProps) => {
  const [loadingUserId, setLoadingUserId] = useState<string | null>(null);

  const handleEditWorkspaceAccess = (user: User) => {
    toast.info(`Edit workspace access for ${user.firstName} ${user.lastName}`);
    // TODO: Implement edit workspace access functionality
  };

  const handleRemoveUser = async (user: User) => {
    if (window.confirm(`Are you sure you want to remove ${user.firstName} ${user.lastName}?`)) {
      setLoadingUserId(user.id);
      try {
        // TODO: Implement remove user functionality
        toast.success(`${user.firstName} ${user.lastName} has been removed`);
      } catch (error) {
        toast.error("Failed to remove user");
      } finally {
        setLoadingUserId(null);
      }
    }
  };

  const agencyUserActions: MemberAction[] = [
    {
      label: "Edit Workspace Access",
      onClick: handleEditWorkspaceAccess,
      icon: <PencilIcon className="h-4 w-4" />,
    },
    {
      label: "Remove User",
      onClick: handleRemoveUser,
      variant: "destructive",
      icon: <TrashIcon className="h-4 w-4" />,
    },
  ];

  const clientUserActions: MemberAction[] = [
    {
      label: "Edit Workspace Access",
      onClick: handleEditWorkspaceAccess,
      icon: <PencilIcon className="h-4 w-4" />,
    },
    {
      label: "Remove User",
      onClick: handleRemoveUser,
      variant: "destructive",
      icon: <TrashIcon className="h-4 w-4" />,
    },
  ];

  const pendingUserActions: MemberAction[] = [
    {
      label: "Remove User",
      onClick: handleRemoveUser,
      variant: "destructive",
      icon: <TrashIcon className="h-4 w-4" />,
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="text-medium-dark-gray">Loading members...</div>
      </div>
    );
  }

  const isEmpty = agencyUsers.length === 0 && clientUsers.length === 0 && pendingUsers.length === 0;

  return (
    <div className="space-y-8">
      {/* Agency Users Section */}
      {agencyUsers.length > 0 && (
        <MembersList.Root isLoading={false} isEmpty={false}>
          <MembersList.Title>
            <div className="flex items-center gap-2 text-basic text-sm font-medium">
              <div>Agency Users</div>
              <Tooltip value="Agency users are any users that are part of more than 1 of your Client workspace(s)." side="bottom" align="start">
                <InformationCircleIcon className="h-4 w-4 text-medium-dark-gray" />
              </Tooltip>
            </div>
          </MembersList.Title>
          {agencyUsers.map((user) => (
            <MembersList.Member key={user.id}>
              <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
              <MembersList.MemberActions user={user} actions={agencyUserActions} />
            </MembersList.Member>
          ))}
        </MembersList.Root>
      )}

      {/* Pending Users Section */}
      {pendingUsers.length > 0 && (
        <MembersList.Root isLoading={false} isEmpty={false}>
          <MembersList.Title>
            <div className="text-medium-gray text-sm font-medium">Pending</div>
          </MembersList.Title>
          {pendingUsers.map((user) => (
            <MembersList.Member key={user.id}>
              <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
              <MembersList.MemberActions user={user} actions={pendingUserActions} />
            </MembersList.Member>
          ))}
        </MembersList.Root>
      )}

      {/* Client Users Section */}
      {clientUsers.length > 0 && (
        <MembersList.Root isLoading={false} isEmpty={false}>
          <MembersList.Title>
            <div className="text-basic text-sm font-medium">Client Users</div>
          </MembersList.Title>
          {clientUsers.map((user) => (
            <MembersList.Member key={user.id}>
              <MembersList.MemberDetails user={user} showWorkspaceCount={true} />
              <MembersList.MemberActions user={user} actions={clientUserActions} />
            </MembersList.Member>
          ))}
        </MembersList.Root>
      )}

      {/* Empty State */}
      {isEmpty && (
        <div className="flex justify-center py-12">
          <div className="text-center">
            <div className="text-medium-dark-gray mb-2">No members found</div>
            <div className="text-sm text-light-gray">
              Invite members to get started
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgencyMembersList;
