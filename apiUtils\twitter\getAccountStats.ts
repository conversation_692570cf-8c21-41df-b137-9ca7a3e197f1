import getAuthedTwitterClient from "apiUtils/getAuthedTwitterClient";
import chunk from "lodash/chunk";
import { db } from "~/clients";

type TwitterAccount = {
  id: string;
  name: string;
  username: string;
  profileImageUrl: string;
  verified: boolean;
  verifiedType: string;
  stats: {
    followerCount: number;
    followingCount: number;
    tweetCount: number;
    listedCount: number;
  };
};

const getAccountStats = async (ids: string[]): Promise<TwitterAccount[]> => {
  const integration = await db.twitterIntegration.findFirstOrThrow({
    where: {
      account: {
        username: "francistogram",
      },
    },
  });

  const client = getAuthedTwitterClient(integration);

  const idChunks = chunk(ids, 100);
  const promises = idChunks.map(
    async (chunk) =>
      await client.v2.users(chunk, {
        "user.fields": [
          "id",
          "name",
          "username",
          "created_at",
          "description",
          "entities",
          "location",
          "pinned_tweet_id",
          "profile_image_url",
          "protected",
          "public_metrics",
          "url",
          "verified",
          "verified_type",
          "withheld",
        ],
      })
  );

  const users = (await Promise.all(promises))
    .map((response) => {
      return response.data
        .map((user) => {
          return {
            id: user.id,
            name: user.name,
            username: user.username,
            profileImageUrl: user.profile_image_url as string,
            verified: user.verified as boolean,
            verifiedType: user.verified_type as string,
            stats: {
              followerCount: user.public_metrics?.followers_count as number,
              followingCount: user.public_metrics?.following_count as number,
              tweetCount: user.public_metrics?.tweet_count as number,
              listedCount: user.public_metrics?.listed_count as number,
            },
          };
        })
        .flat();
    })
    .flat();

  return users;
};

export default getAccountStats;
