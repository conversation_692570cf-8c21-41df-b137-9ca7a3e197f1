import { ChevronRightIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import Link from "next/link";
import { useRouter } from "next/router";
import React from "react";
import NewBadge from "~/components/NewBadge";
import Collapsible from "~/components/ui/Collapsible";
import Sidebar from "~/components/ui/Sidebar";

type SubMenuItem = {
  name: string;
  href: string;
  onMouseEnter?: () => void;
};

type BaseItem = {
  name: string;
  showNewBadge?: boolean;
  icon: React.ReactNode;
  onMouseEnter?: () => void;
  isActiveFn?: (pathname: string) => boolean;
};

type LinkItem = BaseItem & {
  href: string;
  subItems?: undefined;
};

type SubMenuParentItem = BaseItem & {
  href?: string;
  subItems: {
    defaultOpen: boolean;
    name?: string;
    items: (SubMenuItem | React.ReactElement)[];
  };
};

export type Item = LinkItem | SubMenuParentItem;

export type Group = {
  name: string;
  items: Item[];
};

const MenuSidebarContent = ({ groups }: { groups: Group[] }) => {
  const router = useRouter();

  return (
    <Sidebar.Content className="scrollbar-hidden overflow-y-auto">
      {groups.map((group) => (
        <Sidebar.Group key={group.name}>
          <Sidebar.GroupLabel>{group.name}</Sidebar.GroupLabel>
          <Sidebar.Menu>
            {group.items.map((item) => {
              const isActive = item.isActiveFn
                ? item.isActiveFn(router.pathname)
                : router.pathname.replace("/authed", "") === item.href;

              return (
                <Collapsible.Root
                  key={item.name}
                  asChild={true}
                  defaultOpen={item.subItems?.defaultOpen}
                >
                  <Sidebar.MenuItem onMouseEnter={item.onMouseEnter}>
                    {item.href ? (
                      <>
                        <Sidebar.MenuButton asChild={true} tooltip={item.name}>
                          <Link
                            href={item.href}
                            className={classNames(
                              "hover:text-basic flex items-center gap-2 whitespace-nowrap transition-colors",
                              {
                                "text-basic font-medium": isActive,
                              }
                            )}
                          >
                            {item.icon}
                            {item.name}
                            {item.showNewBadge && <NewBadge />}
                          </Link>
                        </Sidebar.MenuButton>

                        {item.subItems != null && (
                          <Collapsible.Trigger asChild={true}>
                            <Sidebar.MenuAction className="data-[state=open]:rotate-90">
                              <ChevronRightIcon />
                              <span className="sr-only">Toggle</span>
                            </Sidebar.MenuAction>
                          </Collapsible.Trigger>
                        )}
                      </>
                    ) : (
                      <Sidebar.MenuButton asChild={true} tooltip={item.name}>
                        <div className="group/menu-item flex w-full cursor-pointer items-center justify-between">
                          <Collapsible.Trigger asChild={true} className="grow">
                            <div className="hover:text-basic flex items-center gap-2 transition-colors">
                              {item.icon}
                              {item.name}
                            </div>
                          </Collapsible.Trigger>
                          <Collapsible.Trigger asChild={true}>
                            <Sidebar.MenuAction className="group-hover/menu-item:text-basic transition-transform data-[state=open]:rotate-90">
                              <ChevronRightIcon />
                              <span className="sr-only">Toggle</span>
                            </Sidebar.MenuAction>
                          </Collapsible.Trigger>
                        </div>
                      </Sidebar.MenuButton>
                    )}

                    {item.subItems != null ? (
                      <Collapsible.Content>
                        <Sidebar.MenuSub>
                          {item.subItems.name && (
                            <Sidebar.GroupLabel>
                              {item.subItems.name}
                            </Sidebar.GroupLabel>
                          )}
                          {item.subItems.items?.map((subItem) => {
                            if (React.isValidElement<any>(subItem)) {
                              return subItem;
                            }

                            const isActive =
                              router.pathname.replace("/authed", "") ===
                              subItem.href;

                            return (
                              <Sidebar.MenuSubItem
                                key={subItem.name}
                                onMouseEnter={subItem.onMouseEnter}
                              >
                                <Sidebar.MenuSubButton asChild={true}>
                                  <Link
                                    href={subItem.href}
                                    className={classNames(
                                      "hover:text-basic transition-colors",
                                      {
                                        "text-basic font-medium": isActive,
                                      }
                                    )}
                                  >
                                    <span>{subItem.name}</span>
                                  </Link>
                                </Sidebar.MenuSubButton>
                              </Sidebar.MenuSubItem>
                            );
                          })}
                        </Sidebar.MenuSub>
                      </Collapsible.Content>
                    ) : null}
                  </Sidebar.MenuItem>
                </Collapsible.Root>
              );
            })}
          </Sidebar.Menu>
        </Sidebar.Group>
      ))}
    </Sidebar.Content>
  );
};

export default MenuSidebarContent;
