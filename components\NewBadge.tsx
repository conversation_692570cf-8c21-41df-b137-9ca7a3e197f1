import { ReactNode } from "react";
import Tooltip from "./Tooltip";

const NewBadge = ({ tooltipContent }: { tooltipContent?: ReactNode }) => {
  return (
    <Tooltip value={tooltipContent} side="bottom" align="end">
      <div className="font-eyebrow-sm bg-secondary ml-0.5 flex max-h-4 items-center justify-center rounded-xs px-1.5 py-0.5 text-white">
        NEW
      </div>
    </Tooltip>
  );
};

export default NewBadge;
