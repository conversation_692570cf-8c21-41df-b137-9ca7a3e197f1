import { toast } from "sonner";
import <PERSON>ton from "~/components/ui/Button";
import C<PERSON><PERSON><PERSON> from "~/components/ui/Credenza";
import { handleTrpcError, trpc } from "~/utils";

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  webhook: {
    id: string;
    name: string;
    hostname: string;
    urlLast4: string;
  };
};

const TestWebhookModal = ({ open, setOpen, webhook }: Props) => {
  const testWebookMutation = trpc.integration.channelWebhook.test.useMutation({
    onSuccess: () => {
      toast.success("Test Data Sent", {
        description:
          "Check on your platform (Zapier, Make, etc.) that the data came through successfully.",
      });
    },
    onError: (error) => {
      handleTrpcError({ title: "Error: Something went wrong", error });
    },
  });

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header>
        <Credenza.Title>Send Test Data</Credenza.Title>
        <Credenza.Description>
          Send test data to verify your Webhook is working for platforms like
          Zapier or Make.com.
          <br />
          <br />
          Note: please only use this to verify your Webhook during the initial
          setup - if your integration is already connected to your Blog, this
          test data will publish.
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <div className="border-light-gray font-body-sm mt-5 rounded-xl border px-5 py-4">
          <div className="font-medium">{webhook.name}</div>
          <div className="text-medium-gray">{`${webhook.hostname}/•••••${webhook.urlLast4}`}</div>
        </div>
      </Credenza.Body>
      <Credenza.Footer>
        <Button
          variant="secondary"
          disabled={testWebookMutation.isPending}
          onClick={() => {
            setOpen(false);
          }}
        >
          Cancel
        </Button>
        <Button
          isLoading={testWebookMutation.isPending}
          onClick={() => {
            testWebookMutation.mutate({ id: webhook.id });
          }}
        >
          Send Test Data
        </Button>
      </Credenza.Footer>
    </Credenza.Root>
  );
};

export default TestWebhookModal;
