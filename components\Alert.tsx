import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";

import classnames from "classnames";
import { ReactNode } from "react";
import { cn } from "~/utils";

type AlertProps = {
  /** Defines the visual style which conveys the level of importance / urgency to the user */
  intent: "success" | "info" | "warning" | "danger" | "none";
  title: string | ReactNode;
  /** The main content to be displayed in the center of the alert */
  message: string | ReactNode;
  /** Controls whether the icon is shown */
  showIcon?: boolean;
  /** The X icon only shows when onClose is provided */
  onClose?: () => void;
};

const Alert = ({
  intent,
  title,
  message,
  showIcon = true,
  onClose,
}: AlertProps) => {
  const bodyClassName = classnames(
    "rounded-md px-6 py-4 w-full bg-lightest-gray-30 text-black"
  );

  const Icons = {
    success: CheckCircleIcon,
    info: InformationCircleIcon,
    warning: ExclamationCircleIcon,
    danger: ExclamationTriangleIcon,
    none: InformationCircleIcon,
  };

  const Icon = Icons[intent];

  return (
    <div className={bodyClassName}>
      <div className="font-body-sm flex shrink flex-col gap-1">
        <div
          className={cn("flex items-center gap-1", {
            "text-black": intent === "none",
            "text-danger": intent === "danger",
          })}
        >
          {showIcon && <Icon className="h-4 w-4 shrink-0" aria-hidden="true" />}
          <div className="w-full font-medium">{title}</div>
        </div>
        <div className="text-medium-dark-gray">{message}</div>
      </div>
    </div>
  );
};

export default Alert;
