import saveScrapedPosts from "apiUtils/linkedin/saveScrapedPosts";
import saveScrapedProfileData from "apiUtils/linkedin/saveScrapedProfileData";
import { NonRetriableError, slugify } from "inngest";
import sortBy from "lodash/sortBy";
import uniq from "lodash/uniq";
import { DateTime } from "luxon";
import { db } from "~/clients/Prisma";
import LinkedInScraper from "~/clients/rapid_api/LinkedInScraper";
import { removeEmptyElems } from "~/utils";
import getLinkedInProfilePermalink from "~/utils/linkedin/getLinkedInProfilePermalink";
import { inngest } from "../inngest";

// Main orchestrator function
export const linkedInScrapePostEngagements = inngest.createFunction(
  {
    id: slugify("LinkedIn Scrape Post Engagements"),
    name: "LinkedIn Scrape Post Engagements",
    retries: 2,
    onFailure: async ({ error, event, step }) => {
      console.error(`Failed to scrape post engagements: ${error.message}`);

      const data = event.data.event.data;

      await db.linkedInPostScrapingJob.update({
        where: { id: data.scrapingJobID },
        data: {
          status: "ERRORED",
          erroredAt: new Date(),
          error: error.message,
        },
      });
    },
  },
  { event: "linkedin.scrapePostEngagements" },
  async ({ event, step }) => {
    const { scrapingJobID } = event.data;

    const scrapingJob = await db.linkedInPostScrapingJob.findUniqueOrThrow({
      where: { id: scrapingJobID },
    });

    const post = await db.linkedInPost.findUniqueOrThrow({
      where: { id: scrapingJob.postID },
      select: {
        id: true,
        commentary: true,
        publishedAt: true,
        activityUrn: true,
        author: {
          select: {
            id: true,
            name: true,
            type: true,
            vanityName: true,
            profileImageUrl: true,
            integrations: {
              where: {
                workspaceID: scrapingJob.workspaceID,
              },
              select: {
                id: true,
                isLeadsScrapingEnabled: true,
                customWebhooks: {
                  select: {
                    id: true,
                    channel: true,
                  },
                },
              },
            },
          },
        },
        comments: {
          select: {
            id: true,
          },
        },
        reactions: {
          select: {
            id: true,
          },
        },
      },
    });

    const { missingProfiles, encryptedUrnAuthorUrns, comments, reactions } =
      await step.run("Get Missing Profiles", async () => {
        console.log("Scraping Post Engagements", post.id);

        const integration = post.author.integrations[0];

        if (!integration) {
          throw new NonRetriableError("No integration found");
        }

        await db.linkedInPostScrapingJob.update({
          where: { id: scrapingJobID },
          data: { startedAt: new Date(), status: "IN_PROGRESS" },
        });

        if (!post.activityUrn) {
          await saveScrapedPosts(integration.id);
        }

        const postWithActivityUrn = await db.linkedInPost.findFirstOrThrow({
          where: {
            id: scrapingJob.postID,
          },
          select: {
            activityUrn: true,
          },
        });

        const activityUrn = postWithActivityUrn.activityUrn!;

        const scraper = new LinkedInScraper();

        const [commentsResponse, reactionsResponse] = await Promise.all([
          scraper.post.getComments(activityUrn),
          scraper.post.getReactions(activityUrn),
        ]);

        if (!commentsResponse.success) {
          throw new Error(commentsResponse.errors.join(", "));
        }
        if (!reactionsResponse.success) {
          throw new Error(reactionsResponse.errors.join(", "));
        }

        const comments = commentsResponse.data;
        const reactions = reactionsResponse.data;

        const uniqueAuthorUrls = Array.from(
          new Set(
            removeEmptyElems([
              ...comments.map((c) => c.author.url),
              ...reactions.map((r) => r.author.url),
            ])
          )
        );

        console.log(`Unique author URLs`, uniqueAuthorUrls);

        const encryptedUrnAuthorUrns = removeEmptyElems(
          uniqueAuthorUrls
            .filter(
              (url) => !url.includes("/company/") && !url.endsWith("/in/")
            )
            .map((url) => {
              if (url.endsWith("/")) {
                return url.slice(0, -1).split("/").pop();
              }
              return url.split("/").pop();
            })
        );

        // We only scrape again if the profile has not been scraped in the last 3 months
        const existingProfiles = await db.linkedInScrapedAccountData.findMany({
          where: {
            encryptedUrn: {
              in: encryptedUrnAuthorUrns,
            },
            lastScrapedAt: {
              gte: DateTime.now().minus({ months: 3 }).toJSDate(),
            },
          },
        });

        const missingProfiles = encryptedUrnAuthorUrns.filter(
          (encryptedUrn) =>
            !existingProfiles.some(
              (profile) => profile.encryptedUrn === encryptedUrn
            )
        );

        console.log(`Profiles to scrape`, missingProfiles.length);

        return {
          existingProfiles,
          missingProfiles,
          encryptedUrnAuthorUrns,
          comments,
          reactions,
        };
      });

    await Promise.all(
      missingProfiles.map(async (encryptedUrn, index) => {
        const result = await step.invoke(
          `Scrape LinkedIn Profile ${index + 1} of ${missingProfiles.length}`,
          {
            function: linkedInScrapeProfile,
            data: {
              encryptedUrn,
            },
          }
        );

        return result;
      })
    );

    const { newCommentIDs, newReactionIDs } = await step.run(
      "Get Author Profiles",
      async () => {
        const authorProfiles = await db.linkedInScrapedAccountData.findMany({
          where: {
            encryptedUrn: {
              in: encryptedUrnAuthorUrns,
            },
          },
          select: {
            id: true,
            encryptedUrn: true,
            firstName: true,
            lastName: true,
            headline: true,
            summary: true,
            vanityName: true,
            city: true,
            country: true,
            profileImageUrl: true,
            profileImageExpiresAt: true,
            followerCount: true,
            jobs: {
              select: {
                id: true,
                title: true,
                startDate: true,
                endDate: true,
                description: true,
                location: true,
                organizationName: true,
                organization: {
                  select: {
                    id: true,
                    name: true,
                    vanityName: true,
                    profileImageUrl: true,
                    organizationData: true,
                  },
                },
              },
            },
          },
        });

        // Create author profile map
        const authorProfileMap = Object.fromEntries(
          removeEmptyElems(authorProfiles).map((profile) => [
            profile.encryptedUrn,
            {
              ...profile,
              currentJob: profile.jobs.find((job) => job.endDate === null),
            },
          ])
        );

        // Process comments with profiles
        const commentsWithProfiles = removeEmptyElems(
          comments.map((comment) => {
            const author =
              authorProfileMap[comment.author.encryptedUrn] || null;

            return author ? { ...comment, author } : null;
          })
        );

        await db.linkedInPostComment.createMany({
          data: commentsWithProfiles.map((comment) => ({
            externalID: comment.urn,
            text: comment.text,
            postID: scrapingJob.postID,
            authorID: comment.author.id,
            permalink: comment.permalink,
            publishedAt: comment.publishedAt,
          })),
          skipDuplicates: true,
        });

        const reactionsWithProfiles = removeEmptyElems(
          reactions.map((reaction) => {
            const author =
              authorProfileMap[reaction.author.encryptedUrn] || null;
            return author ? { ...reaction, author } : null;
          })
        );

        await db.linkedInPostReaction.createMany({
          data: reactionsWithProfiles.map((reaction) => ({
            postID: scrapingJob.postID,
            type: reaction.type,
            authorID: reaction.author.id,
          })),
          skipDuplicates: true,
        });

        const [newComments, newReactions] = await Promise.all([
          db.linkedInPostComment.findMany({
            where: {
              postID: scrapingJob.postID,
              id: {
                notIn: post.comments.map((comment) => comment.id),
              },
            },
            select: {
              id: true,
              text: true,
              permalink: true,
              publishedAt: true,
              author: {
                select: {
                  id: true,
                  encryptedUrn: true,
                  firstName: true,
                  lastName: true,
                  headline: true,
                  summary: true,
                  vanityName: true,
                  city: true,
                  country: true,
                  profileImageUrl: true,
                  profileImageExpiresAt: true,
                  followerCount: true,
                  jobs: {
                    select: {
                      id: true,
                      title: true,
                      startDate: true,
                      endDate: true,
                      description: true,
                      location: true,
                      organizationName: true,
                      organization: {
                        select: {
                          id: true,
                          name: true,
                          vanityName: true,
                          profileImageUrl: true,
                          organizationData: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          }),
          db.linkedInPostReaction.findMany({
            where: {
              postID: scrapingJob.postID,
              id: {
                notIn: post.reactions.map((reaction) => reaction.id),
              },
            },
            select: {
              id: true,
              type: true,
              author: {
                select: {
                  id: true,
                  encryptedUrn: true,
                  firstName: true,
                  lastName: true,
                  headline: true,
                  summary: true,
                  vanityName: true,
                  city: true,
                  country: true,
                  profileImageUrl: true,
                  profileImageExpiresAt: true,
                  followerCount: true,
                  jobs: {
                    select: {
                      id: true,
                      title: true,
                      startDate: true,
                      endDate: true,
                      description: true,
                      location: true,
                      organizationName: true,
                      organization: {
                        select: {
                          id: true,
                          name: true,
                          vanityName: true,
                          profileImageUrl: true,
                          organizationData: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          }),
        ]);

        console.log("New comments", newComments);
        console.log("New reactions", newReactions);

        return {
          newCommentIDs: newComments.map((comment) => comment.id),
          newReactionIDs: newReactions.map((reaction) => reaction.id),
        };
      }
    );

    const createAuthorJson = (author: {
      id: string;
      firstName: string;
      lastName: string;
      vanityName: string;
      headline: string;
      summary: string | null;
      city: string | null;
      country: string | null;
      profileImageUrl: string | null;
      followerCount: number;
      jobs: {
        id: string;
        title: string;
        startDate: Date;
        endDate: Date | null;
        description: string | null;
        location: string | null;
        organizationName: string;
        organization: {
          id: string;
          name: string;
          vanityName: string;
          profileImageUrl: string;
          organizationData: {
            industry: string;
          } | null;
        } | null;
      }[];
    }) => {
      const currentJob = sortBy(
        author.jobs.filter((job) => job.endDate === null),
        "startDate"
      ).at(-1);

      const jobJson = (job: {
        id: string;
        title: string;
        startDate: Date;
        endDate: Date | null;
        description: string | null;
        location: string | null;
        organizationName: string;
        organization: {
          id: string;
          name: string;
          vanityName: string;
          profileImageUrl: string;
          organizationData: {
            industry: string;
          } | null;
        } | null;
      }) => {
        return {
          id: job.id,
          title: job.title,
          startDate: job.startDate.toISOString(),
          endDate: job.endDate?.toISOString(),
          description: job.description,
          location: job.location,
          organizationName: job.organizationName,
          organization: job.organization
            ? {
                id: job.organization.id,
                url: getLinkedInProfilePermalink({
                  vanityName: job.organization.vanityName,
                  type: "Organization",
                }),
                name: job.organization.name,
                profileImageUrl: job.organization.profileImageUrl,
                industry: job.organization.organizationData?.industry,
              }
            : null,
        };
      };

      return {
        id: author.id,
        firstName: author.firstName,
        lastName: author.lastName,
        headline: author.headline,
        summary: author.summary,
        url: getLinkedInProfilePermalink({
          vanityName: author.vanityName,
          type: "User",
        }),
        city: author.city,
        country: author.country,
        profileImageUrl: author.profileImageUrl,
        followerCount: author.followerCount,
        currentJob: currentJob ? jobJson(currentJob) : null,
        jobs: author.jobs.map(jobJson),
      };
    };

    const channels = uniq(
      post.author.integrations.flatMap((integration) =>
        integration.customWebhooks.map((webhook) => webhook.channel)
      )
    );

    const [newComments, newReactions] = await Promise.all([
      db.linkedInPostComment.findMany({
        where: {
          id: {
            in: newCommentIDs,
          },
        },
        select: {
          id: true,
          text: true,
          permalink: true,
          publishedAt: true,
          author: {
            select: {
              id: true,
              encryptedUrn: true,
              firstName: true,
              lastName: true,
              headline: true,
              summary: true,
              vanityName: true,
              city: true,
              country: true,
              profileImageUrl: true,
              profileImageExpiresAt: true,
              followerCount: true,
              jobs: {
                select: {
                  id: true,
                  title: true,
                  startDate: true,
                  endDate: true,
                  description: true,
                  location: true,
                  organizationName: true,
                  organization: {
                    select: {
                      id: true,
                      name: true,
                      vanityName: true,
                      profileImageUrl: true,
                      organizationData: true,
                    },
                  },
                },
              },
            },
          },
        },
      }),
      db.linkedInPostReaction.findMany({
        where: {
          id: {
            in: newReactionIDs,
          },
        },
        select: {
          id: true,
          type: true,
          author: {
            select: {
              id: true,
              encryptedUrn: true,
              firstName: true,
              lastName: true,
              headline: true,
              summary: true,
              vanityName: true,
              city: true,
              country: true,
              profileImageUrl: true,
              profileImageExpiresAt: true,
              followerCount: true,
              jobs: {
                select: {
                  id: true,
                  title: true,
                  startDate: true,
                  endDate: true,
                  description: true,
                  location: true,
                  organizationName: true,
                  organization: {
                    select: {
                      id: true,
                      name: true,
                      vanityName: true,
                      profileImageUrl: true,
                      organizationData: true,
                    },
                  },
                },
              },
            },
          },
        },
      }),
    ]);

    await Promise.all([
      ...newComments.map(
        async (comment) =>
          await step.sendEvent("Send Message to Svix", {
            name: "svix.sendMessage",
            data: {
              eventType: "linkedin_engagement.created",
              applicationID: scrapingJob.workspaceID,
              channels,
              payload: {
                engagement: {
                  id: comment.id,
                  type: "Comment",
                  text: comment.text,
                  author: createAuthorJson(comment.author),
                  post: {
                    id: post.id,
                    commentary: post.commentary,
                    publishedAt: post.publishedAt,
                    url: `https://www.linkedin.com/feed/update/${post.id}`,
                    author: {
                      id: post.author.id,
                      name: post.author.name,
                      url: getLinkedInProfilePermalink({
                        vanityName: post.author.vanityName,
                        type: post.author.type,
                      }),
                      profileImageUrl: post.author.profileImageUrl,
                    },
                  },
                },
              },
            },
          })
      ),
      ...newReactions.map(
        async (reaction) =>
          await step.sendEvent("Send Message to Svix", {
            name: "svix.sendMessage",
            data: {
              applicationID: scrapingJob.workspaceID,
              eventType: "linkedin_engagement.created",
              channels,
              payload: {
                engagement: {
                  id: reaction.id,
                  type: "Like",
                  text: null,
                  author: createAuthorJson(reaction.author),
                  post: {
                    id: post.id,
                    commentary: post.commentary,
                    url: `https://www.linkedin.com/feed/update/${post.id}`,
                    publishedAt: post.publishedAt,
                    author: {
                      id: post.author.id,
                      name: post.author.name,
                      url: getLinkedInProfilePermalink({
                        vanityName: post.author.vanityName,
                        type: post.author.type,
                      }),
                      profileImageUrl: post.author.profileImageUrl,
                    },
                  },
                },
              },
            },
          })
      ),
    ]);

    await step.run("Update Job", async () => {
      await db.linkedInPostScrapingJob.update({
        where: { id: scrapingJobID },
        data: {
          status: "COMPLETED",
          completedAt: new Date(),
          totalProfilesCount: encryptedUrnAuthorUrns.length,
          scrapedProfilesCount: missingProfiles.length,
        },
      });

      await db.linkedInPost.update({
        where: { id: scrapingJob.postID },
        data: {
          lastScrapedEngagementsAt: new Date(),
        },
      });
    });

    return {
      success: true,
      scrapedProfilesCount: missingProfiles.length,
      totalProfilesCount: encryptedUrnAuthorUrns.length,
    };
  }
);

// Individual profile scraping function (to be created in a separate file)
export const linkedInScrapeProfile = inngest.createFunction(
  {
    id: slugify("LinkedIn Scrape Profile"),
    name: "LinkedIn Scrape Profile",
    throttle: {
      limit: 320,
      period: "1m",
      burst: 100,
    },
    retries: 2,
  },
  { event: "linkedin.scrapeProfile" },
  async ({ event, step }) => {
    await step.run("Scrape LinkedIn Profile", async () => {
      console.log("Scraping LinkedIn Profile", event.data.encryptedUrn);

      const { encryptedUrn } = event.data;

      const profile = await db.linkedInScrapedAccountData.findFirst({
        where: { encryptedUrn },
      });

      if (profile) {
        return { success: true, encryptedUrn, scraped: false };
      } else {
        await saveScrapedProfileData(encryptedUrn);

        return { success: true, encryptedUrn, scraped: true };
      }
    });
  }
);
