import { ArrowUpCircleIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import Link from "next/link";
import { posthog } from "posthog-js";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { useMounted } from "~/hooks";

type TrialCardProps = {
  trialPlanName: string;
  daysRemaining: number | null;
  hoursRemaining: number | null;
};

const TrialCard = ({
  trialPlanName,
  daysRemaining,
  hoursRemaining,
}: TrialCardProps) => {
  const { hasMounted } = useMounted();

  if (!hasMounted) {
    return null;
  }

  const isExpiringSoon = !!daysRemaining && daysRemaining <= 1;

  return (
    <Link
      onClick={() => posthog.capture(POSTHOG_EVENTS.trialBanner.navigated)}
      href="/settings/pricing"
      className="rounded-[5px] transition-shadow duration-300 hover:shadow-lg"
    >
      <div
        className={classNames(
          "bg-slate text-basic flex flex-col rounded-[5px] px-3 pt-2.5 pb-[11px]",
          {
            "bg-success text-white": isExpiringSoon,
          }
        )}
      >
        <span className="font-body-xs">{trialPlanName}</span>
        <div className="font-body-sm flex items-center justify-between">
          {!!daysRemaining && !!hoursRemaining && hoursRemaining >= 1 ? (
            <span>
              {hoursRemaining <= 24
                ? "Ends today"
                : `${daysRemaining} ${
                    daysRemaining === 1 ? "day" : "days"
                  } left`}
            </span>
          ) : (
            <span>Your trial has ended.</span>
          )}
          <ArrowUpCircleIcon className="h-[18px] w-[18px]" />
        </div>
      </div>
    </Link>
  );
};

export default TrialCard;
