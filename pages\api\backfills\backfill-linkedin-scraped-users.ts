import saveScrapedProfileData from "apiUtils/linkedin/saveScrapedProfileData";
import chunk from "lodash/chunk";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const users = await db.linkedInScrapedAccountData.findMany({
    where: {
      jobs: {
        none: {},
      },
    },
    select: {
      vanityName: true,
    },
  });

  const chunks = chunk(users, 30);

  let i = 0;
  for await (const chunk of chunks) {
    console.log(`Processing chunk ${i + 1} of ${chunks.length}`);
    const promises = chunk.map(async (user) => {
      const profile = await saveScrapedProfileData(user.vanityName);
    });

    await Promise.all(promises);
  }

  res.status(200).send({
    numUsers: users.length,
  });
};

export default handler;
