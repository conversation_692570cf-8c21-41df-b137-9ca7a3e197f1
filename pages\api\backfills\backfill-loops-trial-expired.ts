import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import { LoopsClient } from "loops";
import { DateTime } from "luxon";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const loops = new LoopsClient(process.env.LOOPS_API_KEY as string);

  const workspaces = await db.workspace.findMany({
    where: {
      company: {
        trialEndsAt: {
          gte: DateTime.fromISO("2024-07-11T00:00:00.000Z").toJSDate(),
          lte: DateTime.fromISO("2024-07-19T23:59:59.999Z").toJSDate(),
        },
      },
    },
    select: {
      userWorkspaces: {
        select: {
          user: {
            select: {
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      },
    },
  });

  const users = workspaces.map((workspace) => workspace.userWorkspaces).flat();
  let i = 1;
  for await (const userWorkspace of users) {
    const user = userWorkspace.user;
    console.log(`${i}/${users.length} event for ${user.email}`);
    const loopsUser = await loops.sendEvent({
      eventName: "trial_expired_n_days_ago",
      email: user.email,
      contactProperties:
        user.firstName && user.lastName
          ? {
              firstName: user.firstName,
              lastName: user.lastName,
            }
          : undefined,
    });
    i++;
  }

  res.status(StatusCodes.OK).send({
    numUsers: users.length,
  });
};

export default handler;
