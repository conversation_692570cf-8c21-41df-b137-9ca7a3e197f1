import classNames from "classnames";
import { useEffect, useRef, useState } from "react";
import ReactAudioPlayer from "react-audio-player";
import { Document, Page, pdfjs } from "react-pdf";
import { Loader, Tooltip } from "~/components";
import VideoPlayer from "~/components/ui/VideoPlayer";
import { AssetType } from "~/types/Asset";
import { trpc } from "~/utils";
import AssetButton from "./AssetButton";

type Props = {
  asset: AssetType;
  onDeleteClick?: (asset: AssetType) => void;
  onEditClick?: (asset: Props["asset"]) => void;
  refetchAssets: () => void;
  isReadOnly?: boolean;
  showNewEditButton?: boolean;
  isEditing?: boolean;
};

const AssetNewDesign = ({
  asset,
  onDeleteClick,
  onEditClick,
  refetchAssets,
  isReadOnly = false,
  showNewEditButton = false,
  isEditing = false,
}: Props) => {
  const [height, setHeight] = useState<number>(0);
  const squareRef = useRef<HTMLDivElement>();

  pdfjs.GlobalWorkerOptions.workerSrc = new URL(
    "pdfjs-dist/build/pdf.worker.min.js",
    import.meta.url
  ).toString();

  useEffect(() => {
    if (squareRef.current) {
      setHeight(squareRef.current.clientHeight);
    }
  }, [squareRef]);

  const deleteAssetMutation = trpc.asset.delete.useMutation({
    onSuccess: () => {
      refetchAssets();
    },
  });

  const downloadAsset = () => {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", asset.signedUrl, true);
    xhr.responseType = "blob";
    xhr.onload = function () {
      const urlCreator = window.URL || window.webkitURL;
      const assetUrl = urlCreator.createObjectURL(this.response);
      const tag = document.createElement("a");
      tag.href = assetUrl;
      tag.download = asset.name;
      document.body.appendChild(tag);
      tag.click();
      document.body.removeChild(tag);
    };
    xhr.send();
  };

  const mimetype = asset.metadata.mimetype;

  let Content: JSX.Element | null = (
    <img
      src={asset.signedUrl}
      draggable={false}
      className="h-auto max-h-full w-auto max-w-full"
    />
  );
  if (mimetype.startsWith("video/")) {
    Content = (
      <VideoPlayer.Root className="rounded-[10px]">
        <VideoPlayer.Content
          slot="media"
          src={asset.signedUrl}
          preload="auto"
          className="max-h-[180px] rounded-[10px]"
        />
        <VideoPlayer.ControlBar className="absolute top-0 left-0 z-1000">
          <VideoPlayer.PlayButton className=" " />
        </VideoPlayer.ControlBar>
      </VideoPlayer.Root>
    );
  } else if (mimetype === "application/pdf") {
    Content = (
      <Document file={asset.signedUrl}>
        <Page
          pageNumber={1}
          width={250}
          renderTextLayer={false}
          renderAnnotationLayer={false}
          className="overflow-clip rounded-md"
        />
      </Document>
    );
  } else if (mimetype.startsWith("audio")) {
    Content = (
      <ReactAudioPlayer
        className="text-light-gray px-2"
        src={asset.signedUrl}
        controls={true}
      />
    );
  } else if (asset.name.endsWith(".srt")) {
    Content = null;
  }

  const assetSize =
    asset.sizeMB >= 0.1
      ? `${asset.sizeMB.toFixed(1)} MB`
      : `${asset.sizeKB.toFixed(1)} KB`;

  return (
    <div className="group/asset bg-lightest-gray-30 border-medium-gray/30 relative h-auto w-auto max-w-[400px] rounded-xl border">
      <div
        // @ts-ignore TODO(francisco): not sure what the ref type is supposed to be
        ref={squareRef}
        className={classNames(
          "flex w-full max-w-[400px] items-center justify-center",
          {
            "aspect-w-16 aspect-h-16": Content != null,
          }
        )}
      >
        <div className="mx-auto flex w-full items-center justify-center overflow-hidden rounded-t-xl">
          {Content}
        </div>
        <div
          className={classNames(
            "absolute inset-1/2 inset-y-1/2 h-full w-full -translate-x-3 -translate-y-4",
            {
              hidden: !deleteAssetMutation.isPending && !isEditing,
            }
          )}
        >
          <div className="bg-opacity-80 w-fit rounded-full bg-white p-2">
            <Loader size="md" />
          </div>
        </div>
        <div className="z-10 opacity-0 transition-opacity group-hover/asset:opacity-100">
          <div className="absolute top-2 right-4 flex gap-1.5">
            {mimetype.startsWith("image/") &&
              mimetype !== "image/gif" &&
              showNewEditButton &&
              !isEditing && (
                <AssetButton
                  icon="PaintBrushIcon"
                  onClick={() => onEditClick?.(asset)}
                />
              )}
            {!isEditing && (
              <AssetButton icon="ArrowDownTrayIcon" onClick={downloadAsset} />
            )}
            {!isReadOnly && !isEditing && (
              <AssetButton
                icon="TrashIcon"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onDeleteClick) {
                    onDeleteClick(asset);
                  } else {
                    deleteAssetMutation.mutate({
                      bucket: "assets",
                      path: asset.path,
                    });
                  }
                }}
              />
            )}
          </div>
          <div className="absolute bottom-0 left-0 w-full bg-white/60 px-3 py-2 backdrop-blur-xs">
            <Tooltip value={asset.name}>
              <div className="font-body-xs overflow-clip text-ellipsis whitespace-nowrap">
                {asset.name}
              </div>
            </Tooltip>
            <div className="font-body-xs text-dark-gray">{assetSize}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssetNewDesign;
