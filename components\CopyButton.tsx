import { toast } from "sonner";

import { LinkIcon } from "@heroicons/react/24/outline";
import copy from "copy-to-clipboard";
import { cn } from "~/utils";

type Props = {
  label: string;
  value: string;
  textStyle?: "font-eyebrow" | "font-body-sm";
  onClick?: () => void;
};

const CopyButton = ({
  label,
  value,
  textStyle = "font-eyebrow",
  onClick,
}: Props) => {
  return (
    <span className="isolate inline-flex rounded-md shadow-xs">
      <div
        className={cn(
          "bg-slate scrollbar-hidden w-fit max-w-[150px] overflow-auto rounded-l-md px-3 py-1 whitespace-nowrap md:max-w-[300px]",
          textStyle
        )}
      >
        {value}
      </div>
      <button
        type="button"
        onClick={() => {
          copy(value) &&
            toast.success(`Copied to Clipboard`, {
              description: `${label} copied to clipboard`,
            });
          if (onClick) {
            onClick();
          }
        }}
        className="bg-basic hover:bg-basic/80 text-basic-light font-eyebrow-sm relative -ml-px inline-flex items-center rounded-r-md px-3 whitespace-nowrap"
      >
        <LinkIcon className="mr-1 h-3 w-3" />
        Copy {label}
      </button>
    </span>
  );
};

export default CopyButton;
