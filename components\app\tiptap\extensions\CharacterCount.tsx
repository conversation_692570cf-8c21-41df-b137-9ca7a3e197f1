import { Extension } from "@tiptap/core";
import { Plug<PERSON>, Plugin<PERSON><PERSON> } from "prosemirror-state";
import { Decoration, DecorationSet } from "prosemirror-view";
import { charCount } from "~/utils";

type CharacterCountOptions = {
  limit: number;
  isTwitter?: boolean;
};

const CharacterCount = Extension.create<CharacterCountOptions>({
  name: "characterCount",

  addStorage() {
    return {
      limit: this.options.limit,
      isTwitter: this.options.isTwitter,
    };
  },

  addProseMirrorPlugins() {
    const limit = this.options.limit;

    return [
      new Plugin({
        key: new PluginKey("characterCount"),
        props: {
          decorations: (state) => {
            const { doc } = state;
            const decorations: Decoration[] = [];
            let currentLength = 0;

            let isFirstNode = true;

            doc.nodesBetween(0, doc.content.size, (node, pos) => {
              if (!node.isText) {
                // We want to count all new lines as 1 character except the first one
                // which we ignore
                if (node.type.name === "paragraph" && !isFirstNode) {
                  currentLength += 1;
                }
                isFirstNode = false;
                return;
              }

              const textLength = charCount(node.text || "", {
                isTwitter: this.options.isTwitter,
              });

              const nodeStart = pos;
              const nodeEnd = pos + (node.text?.length || 0);

              if (currentLength < limit && currentLength + textLength > limit) {
                // This node contains the limit point
                const overflowStart = nodeStart + (limit - currentLength);
                decorations.push(
                  Decoration.inline(overflowStart, nodeEnd, {
                    class: "bg-danger/20 border-b border-danger",
                  })
                );
              } else if (currentLength >= limit) {
                // This entire node is over the limit
                decorations.push(
                  Decoration.inline(nodeStart, nodeEnd, {
                    class: "bg-danger/20 border-b border-danger",
                  })
                );
              }

              currentLength += textLength;
            });

            return DecorationSet.create(doc, decorations);
          },
        },
      }),
    ];
  },
});

export default CharacterCount;
