import { useEffect } from "react";

import { trackEvent } from "@intercom/messenger-js-sdk";
import {
  Channel,
  PostStatus,
  WorkspaceOnboardingTaskState,
} from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { Divider, TextLink } from "~/components";
import { SuggestedTimes } from "~/components/TimeInput";
import {
  FormAutosizeInput,
  FormChannelSelect,
  FormStatusSelect,
  FormTimeInput,
} from "~/components/form";
import FormDatePicker from "~/components/form/FormDatePicker";
import FormLabelSelect from "~/components/form/FormLabelSelect";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import Switch from "~/components/ui/Switch";
import { useCalendarView, useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { getDateUTC, getPostPublishAt, handleTrpcError, trpc } from "~/utils";
import { Label } from "../LabelSelect";
import TimezoneTooltip from "../TimezoneTooltip";

type Post = {
  id: string;
  title: string;
  channels: Channel[];
  campaignID: string | null;
  campaign?: Campaign;
  publishDate: Date;
  publishTime: Date | null;
  publishAt: Date | null;
  status: PostStatus;
  discordWebhookID: string | null;
  threadsIntegrationID: string | null;
  instagramIntegrationID: string | null;
  facebookIntegrationID: string | null;
  linkedInIntegrationID: string | null;
  tikTokIntegrationID: string | null;
  youTubeIntegrationID: string | null;
  twitterIntegrationID: string | null;
  slackIntegrationID: string | null;
  webflowIntegrationID: string | null;
  subscribers: {
    id: string;
    userID: string;
    user: {
      firstName: string | null;
      lastName: string | null;
    };
  }[];
  accounts: {
    id: string;
    name: string;
    profileImageUrl?: string;
    channel: Channel;
  }[];
  isIdea: false;
  labels: {
    id: string;
    name: string;
    color: string;
    backgroundColor: string;
    createdAt: Date;
  }[];
  createdAt: Date;
};

type Campaign = {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
};

type FormValues = {
  title: string;
  channel: Channel;
  channels: Channel[];
  campaign?: Campaign;
  labels: Label[];
  publishDate: Date;
  publishTime?: Date;
  status: PostStatus;
};

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  onCreateSuccess: (post: Post) => void;
  channel?: Channel;
  crossPostChannels?: Channel[];
  postContent?: {
    copy: string;
    editorState: JSONContent | null;
  }[];
  draftID?: string;
  defaultTitle?: string;
  defaultStatus?: PostStatus;
  defaultChannel?: Channel;
  defaultPublishDate?: Date | null;
  defaultCampaignID?: string | null;
  defaultLabels?: Label[];
  integrationID?: string;
};

const NewPostModal = ({
  open,
  setOpen,
  onCreateSuccess,
  channel,
  crossPostChannels,
  postContent,
  draftID,
  defaultTitle,
  defaultChannel,
  defaultStatus = PostStatus.ToDo,
  defaultPublishDate,
  defaultCampaignID,
  defaultLabels,
  integrationID,
}: Props) => {
  const router = useRouter();
  const { currentWorkspace } = useWorkspace();
  const trpcUtils = trpc.useUtils();
  const campaignsQuery = trpc.campaign.getAll.useQuery();
  const campaigns = campaignsQuery.data?.campaigns;
  const labels = trpc.label.getAll.useQuery().data?.labels || [];
  const { openPostPage, setOpenPostPage } = useCalendarView();

  const { control, handleSubmit, watch, setValue, reset, setFocus } =
    useForm<FormValues>();

  const selectedChannel = watch("channel");
  const selectedChannels = watch("channels");
  const publishDate = watch("publishDate");
  const publishTime = watch("publishTime");

  const mostRecentlyUsedQuery = trpc.post.getMostRecentlyUsed.useQuery();
  const mostRecentlyUsedTimes = (
    mostRecentlyUsedQuery.data?.publishAts || []
  ).map((publishAt) => new Date(publishAt));
  const optimalPublishTimeQuery = trpc.post.getBestTimeToPost.useQuery(
    {
      channel: selectedChannel,
      publishDate: publishDate,
    },
    {
      enabled: selectedChannel != null && publishDate != null && open,
      refetchOnWindowFocus: false,
      refetchInterval: false,
    }
  );
  const optimalPublishTimeString = optimalPublishTimeQuery.data?.publishTime;

  const suggestedTimes: SuggestedTimes[] = [];
  if (currentWorkspace?.timezone == null) {
    suggestedTimes.push({
      label: "Recommended",
      howToComponent: (
        <TextLink
          href="/settings/general"
          size="sm"
          wrapText={true}
          openInNewTab={false}
          value="Set a timezone to see 
      recommendations ->"
        />
      ),
    });
  } else if (optimalPublishTimeString) {
    suggestedTimes.push({
      label: "Recommended",
      times: [
        DateTime.fromJSDate(optimalPublishTimeString)
          .setLocale("local")
          .toJSDate(),
      ],
    });
  }

  suggestedTimes.push({
    label: "Recently Used",
    times: mostRecentlyUsedTimes,
  });

  const createPostMutation = trpc.post.create.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Error Creating Post", error });
    },
    onSuccess: (data) => {
      const currentTasks = trpcUtils.onboarding.getTasks.getData();
      if (currentTasks) {
        const updatedTasks = currentTasks.map((task) =>
          task.type === "CREATE_POST"
            ? {
                ...task,
                state: WorkspaceOnboardingTaskState.COMPLETED,
                isCompleted: true,
              }
            : task
        );
        trpcUtils.onboarding.getTasks.setData(undefined, updatedTasks);
      }
      const mostRecentlyUsedData = trpcUtils.post.getMostRecentlyUsed.getData();
      if (mostRecentlyUsedData) {
        trpcUtils.post.getMostRecentlyUsed.setData(undefined, {
          publishAts: mostRecentlyUsedData.publishAts,
          recentlyUsedChannels: mostRecentlyUsedData.recentlyUsedChannels,
          channel: data.post.channels[0],
        });
      }
      const ideaData = trpcUtils.idea.getAll.getData();
      const ideas = ideaData?.ideas;
      if (ideas) {
        trpcUtils.idea.getAll.setData(undefined, {
          ideas: ideas.filter((idea) => idea.id !== data.post.id),
        });
      }
      trpcUtils.post.getMany.invalidate();
      reset({
        title: "",
        channel: undefined,
        labels: [],
        channels: [],
        campaign: defaultCampaignID
          ? campaigns?.find((campaign) => campaign.id === defaultCampaignID)
          : undefined,
        publishDate: undefined,
        publishTime: undefined,
        status: undefined,
      });
      trackEvent("post_created", {
        channels: data.post.channels
          .map((channel) => CHANNEL_ICON_MAP[channel].label)
          .join(", "),
      });
      setOpen(false);
      if (openPostPage) {
        router.push(`/posts/${data.post.id}`);
      } else {
        onCreateSuccess(data.post);
      }
    },
  });

  const createNewPost = (values: FormValues) => {
    const {
      title,
      channel,
      channels,
      labels,
      campaign,
      publishDate,
      publishTime,
      status,
    } = values;

    const publishAt = getPostPublishAt(publishDate, publishTime);

    const publishDateUTC = getDateUTC(publishDate);

    const allChannels = [channel, ...(channels || [])];
    createPostMutation.mutate({
      title,
      channels: allChannels,
      labels,
      campaignID: campaign?.id,
      publishDate: publishDateUTC,
      publishTime,
      publishAt,
      status,
      postContent,
      draftID,
      integrationID,
    });
  };

  useEffect(() => {
    if (defaultTitle) {
      setValue("title", defaultTitle);
    }
  }, [defaultTitle]);

  useEffect(() => {
    if (defaultCampaignID) {
      const campaign = campaigns?.find(
        (campaign) => campaign.id === defaultCampaignID
      );
      if (campaign) {
        setValue("campaign", campaign);
      }
    }
  }, [campaigns]);

  useEffect(() => {
    if (defaultPublishDate) {
      const newPublishDate = DateTime.fromISO(
        DateTime.fromJSDate(defaultPublishDate).toFormat("yyyy-MM-dd")
      ).toJSDate();
      setValue("publishDate", newPublishDate);
    }
  }, [defaultPublishDate]);

  useEffect(() => {
    if (defaultStatus) {
      setValue("status", defaultStatus);
    }
  }, [defaultStatus]);

  useEffect(() => {
    if (channel) {
      setValue("channel", channel);
    }
  }, [channel]);

  useEffect(() => {
    if (crossPostChannels) {
      setValue("channels", crossPostChannels);
    }
  }, [crossPostChannels]);

  useEffect(() => {
    if (defaultChannel) {
      setValue("channel", defaultChannel);
    }
  }, [defaultChannel]);

  useEffect(() => {
    if (open) {
      setTimeout(() => {
        setFocus("title");
      }, 10);
    }
  }, [setFocus, open]);

  useEffect(() => {
    if (defaultLabels) {
      setValue("labels", defaultLabels);
    }
  }, [defaultLabels]);

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header className="sr-only">
        <Credenza.Title>Create Post</Credenza.Title>
        <Credenza.Description>
          Create a new post with title, channels, campaign and more
        </Credenza.Description>
      </Credenza.Header>
      <Credenza.Body>
        <form
          className="grid grid-cols-12 gap-x-2 gap-y-5"
          onSubmit={handleSubmit(createNewPost)}
        >
          <div className="col-span-10">
            <FormAutosizeInput
              control={control}
              name="title"
              autoFocus={true}
              placeholder="Enter Post Title"
              size="lg"
              rules={{ required: true }}
              showBorder={false}
            />
          </div>
          <div className="col-span-12 flex items-start gap-2">
            <FormDatePicker
              control={control}
              name="publishDate"
              placeholder="MM/DD/YYYY"
              rules={{ required: true }}
              displayStyle="border"
            />
            <TimezoneTooltip date={getPostPublishAt(publishDate, publishTime)}>
              <FormTimeInput
                control={control}
                name="publishTime"
                isOptional={true}
                suggestedTimes={suggestedTimes}
                displayStyle="border"
              />
            </TimezoneTooltip>
            <div className="h-6">
              <Divider orientation="vertical" />
            </div>
            <FormChannelSelect
              control={control}
              name="channel"
              ignoredChannels={selectedChannels}
              recentlyUsedChannels={
                mostRecentlyUsedQuery.data?.recentlyUsedChannels
              }
              isMulti={false}
              isReadOnly={!!channel}
              rules={{ required: true }}
            />
            <div className="hidden sm:block">
              <FormStatusSelect
                control={control}
                name="status"
                rules={{ required: true }}
              />
            </div>
            <div className="hidden sm:block">
              <FormLabelSelect
                control={control}
                isOptional={true}
                options={labels}
                name="labels"
              />
            </div>
          </div>
          <div className="col-span-12">
            <Divider padding="none" />
          </div>
          <Credenza.Footer className="col-span-12">
            <div className="flex items-center gap-2">
              <Switch
                checked={openPostPage}
                onCheckedChange={() => {
                  setOpenPostPage(!openPostPage);
                }}
              />
              <div className="font-body-sm text-medium-dark-gray">
                Open Post Page
              </div>
            </div>
            <Button type="submit" isLoading={createPostMutation.isPending}>
              Create
            </Button>
          </Credenza.Footer>
        </form>
      </Credenza.Body>
    </Credenza.Root>
  );
};

export default NewPostModal;
