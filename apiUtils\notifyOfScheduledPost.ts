import { Channel, SlackWebhookType } from "@prisma/client";
import { DateTime } from "luxon";
import knock from "~/clients/Knock";
import posthog from "~/clients/posthog";
import { db } from "~/clients/Prisma";
import slack from "~/clients/Slack";
import { GENERIC_ERROR_MESSAGE } from "~/constants";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { CHANNEL_ICON_MAP } from "~/types";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";

const notifyOfScheduledPost = async ({
  post,
  scheduledPost,
  account,
  sendNotifications = true,
  status,
  postUrl,
  socialPostID,
  error,
  rawError,
  isAuthError,
}: {
  post: {
    id: string;
    title: string;
    subscribers: {
      userID: string;
    }[];
    workspace: {
      id: string;
      name: string;
      userWorkspaces: {
        userID: string;
      }[];
      companyID: string;
      slackWebhooks?: {
        type: SlackWebhookType;
        url: string;
      }[];
    };
  };
  scheduledPost: {
    id: string;
    channel: Channel;
    publishAt: Date;
    createdByID: string;
  };
  account?: {
    name: string;
    url?: string;
  };
  sendNotifications?: boolean;
} & ScheduledPostResult) => {
  const { channel } = scheduledPost;

  const workspaceUserIDs = post.workspace.userWorkspaces.map(
    (userWorkspace) => userWorkspace.userID
  );

  const subscriberUserIDs = post.subscribers
    .filter((subscriber) => workspaceUserIDs.includes(subscriber.userID))
    .map((subscriber) => subscriber.userID);

  if (status === "Skipped") {
    console.log(`post:${post.id} ${CHANNEL_ICON_MAP[channel].label} skipped`);
  } else if (status === "Success") {
    console.log(
      `post:${post.id} ${CHANNEL_ICON_MAP[channel].label} published: ${postUrl}`
    );

    posthog.capture({
      distinctId: scheduledPost.createdByID,
      event: POSTHOG_EVENTS.post.published,
      properties: {
        postID: post.id,
        channel,
      },
      groups: {
        workspace: post.workspace.id,
        company: post.workspace.companyID,
      },
    });

    const slackWebhookUrls = post.workspace.slackWebhooks
      ?.filter((webhook) => webhook.type === "SuccessfulPost")
      ?.map((webhook) => webhook.url);

    // Moved to a transaction to prevent race condition
    // P2028: "Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting."
    await db.$transaction(async (tx) => {
      await tx.post.update({
        where: {
          id: post.id,
        },
        data: {
          status: "Posted",
          facebookLink: channel === "Facebook" ? postUrl : undefined,
          instagramLink: channel === "Instagram" ? postUrl : undefined,
          instagramPostID: channel === "Instagram" ? socialPostID : undefined,
          threadsLink: channel === "Threads" ? postUrl : undefined,
          twitterLink: channel === "Twitter" ? postUrl : undefined,
          tikTokLink: channel === "TikTok" ? postUrl : undefined,
          linkedInLink: channel === "LinkedIn" ? postUrl : undefined,
          webflowLink: channel === "Webflow" ? postUrl : undefined,
          youTubeLink: channel === "YouTubeShorts" ? postUrl : undefined,
          publishDate: new Date(
            DateTime.fromJSDate(scheduledPost.publishAt).toFormat("yyyy-MM-dd")
          ),
          publishTime: scheduledPost.publishAt,
          publishAt: scheduledPost.publishAt,
          scheduledPosts: {
            update: {
              where: {
                id: scheduledPost.id,
              },
              data: {
                status: "Completed",
              },
            },
          },
        },
      });

      // Oddly enough sometimes there's a 2nd scheduled post for the same channel
      // This is hacky but we'll cancel any other
      await tx.scheduledPost.updateMany({
        where: {
          postID: post.id,
          channel: channel,
          status: "Scheduled",
        },
        data: {
          status: "Cancelled",
        },
      });
    });

    if (sendNotifications) {
      await knock.triggerPostPostedWorkflow({
        recipients: subscriberUserIDs,
        postID: post.id,
        postName: post.title,
        channel,
        status: "success",
        channelPostUrl: postUrl,
        workspaceID: post.workspace.id,
      });
      await slack.newSuccessfulPost({
        post,
        channel,
        url: postUrl,
        account,
        webhookUrls: slackWebhookUrls || [],
      });
    }
  } else {
    console.log(
      `post:${post.id} ${CHANNEL_ICON_MAP[channel].label} errored: ${error}`
    );
    posthog.capture({
      distinctId: scheduledPost.createdByID,
      event: POSTHOG_EVENTS.post.errored,
      properties: {
        postID: post.id,
        channel,
        error,
        isAuthError: !!isAuthError,
      },
      groups: {
        workspace: post.workspace.id,
      },
    });
    try {
      await db.scheduledPost.update({
        where: {
          id: scheduledPost.id,
        },
        data: {
          status: "Errored",
          error: error || GENERIC_ERROR_MESSAGE,
          // @ts-ignore should work fine
          rawError,
          erroredAt: new Date(),
        },
      });
      if (isAuthError) {
        if (channel === "Threads") {
          await db.post.update({
            where: {
              id: post.id,
            },
            data: {
              threadsIntegration: {
                update: {
                  isReintegrationRequired: true,
                },
              },
            },
          });
        } else if (channel === "YouTubeShorts") {
          await db.post.update({
            where: {
              id: post.id,
            },
            data: {
              youTubeIntegration: {
                update: {
                  isReintegrationRequired: true,
                },
              },
            },
          });
        } else if (channel === "Instagram") {
          await db.post.update({
            where: {
              id: post.id,
            },
            data: {
              instagramIntegration: {
                update: {
                  isReintegrationRequired: true,
                },
              },
            },
          });
        } else if (channel === "Facebook") {
          await db.post.update({
            where: {
              id: post.id,
            },
            data: {
              facebookIntegration: {
                update: {
                  isReintegrationRequired: true,
                },
              },
            },
          });
        } else if (channel === "TikTok") {
          await db.post.update({
            where: {
              id: post.id,
            },
            data: {
              tikTokIntegration: {
                update: {
                  isReintegrationRequired: true,
                },
              },
            },
          });
        }
      }
    } catch (error) {
      await db.scheduledPost.update({
        where: {
          id: scheduledPost.id,
        },
        data: {
          status: "Errored",
          error: error || GENERIC_ERROR_MESSAGE,
          erroredAt: new Date(),
        },
      });
    } finally {
      if (sendNotifications) {
        await knock.triggerPostPostedWorkflow({
          recipients: subscriberUserIDs,
          postID: post.id,
          postName: post.title,
          channel,
          status: "error",
          error: error || GENERIC_ERROR_MESSAGE,
          workspaceID: post.workspace.id,
        });
        await slack.newFailedPost({
          post,
          channel,
          account,
          error: error || GENERIC_ERROR_MESSAGE,
          isAuthError: !!isAuthError,
        });
      }
    }
  }
};

export default notifyOfScheduledPost;
