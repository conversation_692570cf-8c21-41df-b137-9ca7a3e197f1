import { useEffect, useState } from "react";

import { v4 as uuidv4 } from "uuid";

import { Channel, ScheduledPostStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import assert from "assert";
import classNames from "classnames";
import { toast } from "sonner";
import { ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import { useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  handleFilesUpload,
  handleTrpcError,
  openIntercomArticle,
  trpc,
} from "~/utils";
import { EditImageModalProperties } from "~/utils/EditImageModal";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import { getDefaultAccount } from "~/utils/getDefaultAccount";
import getFileProperties from "~/utils/getFileProperties";
import createRoomID from "~/utils/liveblocks/createRoomID";
import Assets, { UploadingAsset } from "../../files/Assets";
import SlackEditorWrapper from "../../tiptap/SlackEditor";
import EditorActionsBar from "../EditorActionsBar";
import PostPreviewContainer from "../PostPreviewContainer";
import SlackPostPreview from "./SlackPostPreview";

type ScheduledPost = {
  status: ScheduledPostStatus;
  channel: Channel;
  error: string | null;
  publishAt: Date;
  integrationAccountName: string;
};

export type SlackContent = {
  id: string;
  editorState: JSONContent | null;
  copy: string;
  uploading?: boolean;
};

export type Slackintegration = {
  id: string;
  profileImageUrl: string;
  displayName: string;
  channelName: string;
  teamName: string;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    slackAssets: AssetType[];
    slackContent: SlackContent | null;
    scheduledPosts: ScheduledPost[];
    slackIntegration: Slackintegration | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const SlackPost = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const trpcUtils = trpc.useUtils();
  const [isRoomReady, setIsRoomReady] = useState<boolean>(false);

  useEffect(() => {
    if (post.slackContent && !isSyncingComplete) {
      setIsRoomReady(true);
    }
  }, [post.slackContent, isSyncingComplete]);

  const [integration, setIntegration] = useState<Slackintegration | null>(
    post.slackIntegration
  );
  const [content, setContent] = useState<SlackContent>(
    post.slackContent || {
      id: uuidv4(),
      editorState: null,
      copy: "",
    }
  );
  const [draggedOver, setDraggedOver] = useState<boolean>(false);
  const [assets, setAssets] = useState<AssetType[]>(post.slackAssets);
  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);

  const [uppy, setUppy] = useState<Uppy | null>(null);

  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);

  const updatePostMutation = trpc.post.update.useMutation();

  const slackContentQuery = trpc.slackContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const slackContentData = slackContentQuery.data;
  useEffect(() => {
    const setCrossPostData = () => {
      if (slackContentData?.slackContent != null) {
        setContent(slackContentData.slackContent);
        setAssets(slackContentData.assets);
        setIsRoomReady(true);
        clearIsSyncing();
      }
    };
    setCrossPostData();
  }, [slackContentData]);

  const upsertSlackContent = trpc.slackContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
  });
  const createAssetsMutation = trpc.slackContent.createAssets.useMutation();
  const deleteAssetMutation = trpc.slackContent.deleteAsset.useMutation();
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const updateAssetMutation = trpc.slackContent.updateAsset.useMutation();

  const connectedAccountsQuery =
    trpc.integration.slack.getWorkspaceAccounts.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });

  const integrationsQuery = trpc.post.getIntegrations.useQuery({
    id: post.id,
  });
  const postAccounts = integrationsQuery.data?.accounts ?? [];

  const connectedAccountsData = connectedAccountsQuery.data;
  useEffect(() => {
    const setInitialIntegration = () => {
      if (integration == null && connectedAccountsData?.accounts.length) {
        const defaultIntegration = getDefaultAccount({
          mainAccount: postAccounts[0],
          accounts: connectedAccountsData.accounts,
        });

        setIntegration(defaultIntegration);
        updatePostMutation.mutate(
          {
            id: post.id,
            slackIntegrationID: defaultIntegration.id,
          },
          {
            onSuccess: () => {
              trpcUtils.post.getIntegrations.refetch({
                id: post.id,
              });
            },
          }
        );
      }
    };
    setInitialIntegration();
  }, [connectedAccountsData]);

  const workspaceAccounts =
    connectedAccountsData?.accounts || (integration ? [integration] : []);

  useEffect(() => {
    if (integration) {
      updatePostMutation.mutate(
        {
          id: post.id,
          slackIntegrationID: integration.id,
        },
        {
          onSuccess: () => {
            trpcUtils.post.getIntegrations.refetch({
              id: post.id,
            });
          },
        }
      );
    }
  }, [integration]);

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `slack-${post.id}`,
        restrictions: { maxNumberOfFiles: 10 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", (file) => {
        assert(currentWorkspace);

        setUploadingAssets((uploadingAssets) => {
          return [
            ...uploadingAssets,
            {
              name: file.name!,
              size: file.size!,
              type: file.type,
              uploadProgress: 0,
              updateAssetID: file.meta.updateAssetID as string | undefined,
            },
          ];
        });

        const objectName = `${currentWorkspace.id}/${post.id}/${CHANNEL_ICON_MAP["Slack"].slug}/${content.id}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (uppyData) => {
        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["Slack"].slug
        }/${content.id}`;
        const fileNames = uppyData.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          uppyData.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              if (uppyData.successful![0].meta.updateAssetID) {
                const updateAssetID = uppyData.successful![0].meta
                  .updateAssetID as string;
                const newAsset = newAssetsWithProperties[0];

                await updateAssetMutation.mutateAsync({
                  slackAssetID: updateAssetID,
                  newAsset: {
                    id: newAsset.id,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                });

                setAssets((prevAssets) =>
                  prevAssets.map((asset) =>
                    asset.id === updateAssetID
                      ? {
                          id: asset.id,
                          name: newAsset.name,
                          path: newAsset.path,
                          sizeBytes: newAsset.sizeBytes,
                          eTag: newAsset.eTag,
                          md5Hash: newAsset.md5Hash,
                          mimetype: newAsset.mimetype,
                          width: newAsset.width,
                          height: newAsset.height,
                          metadata: newAsset.metadata,
                          url: newAsset.signedUrl,
                          sizeMB: newAsset.sizeMB,
                          sizeKB: newAsset.sizeKB,
                          createdAt: newAsset.createdAt,
                          updatedAt: newAsset.updatedAt,
                          duration: newAsset.duration,
                          signedUrl: newAsset.signedUrl,
                          urlExpiresAt: newAsset.urlExpiresAt,
                        }
                      : asset
                  )
                );
                setUploadingAssets([]);
              } else {
                const nextPosition = assets.length;

                createAssetsMutation.mutate(
                  {
                    id: content.id,
                    assets: newAssetsWithProperties.map((asset, index) => ({
                      id: asset.id,
                      assetID: asset.assetID,
                      name: asset.name,
                      path: asset.path,
                      size: asset.sizeBytes,
                      eTag: asset.eTag,
                      md5Hash: asset.md5Hash,
                      mimetype: asset.mimetype,
                      width: asset.width,
                      height: asset.height,
                      duration: asset.duration,
                      url: asset.signedUrl,
                      urlExpiresAt: asset.urlExpiresAt,
                      position: nextPosition + index,
                    })),
                  },
                  {
                    onError: (error) => {
                      handleTrpcError({ title: "Error Creating Asset", error });
                    },
                  }
                );

                setUploadingAssets([]);
                setAssets((assets) => [...assets, ...newAssetsWithProperties]);
              }
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace, content.id]);

  const getValidFiles = async (files: File[]) => {
    if (assets.length + files.length > 1) {
      toast.error("Too Many Files", {
        description: "You can add 1 image or video per post",
      });
      return [];
    }

    return files;
  };

  return (
    <div>
      <PostPreviewContainer
        channel="Slack"
        post={post}
        connectedAccount={
          integration && {
            integrationID: integration.id,
            name: integration.displayName,
          }
        }
        accountOptions={workspaceAccounts.map((workspaceAccount) => ({
          integrationID: workspaceAccount.id,
          name: `${workspaceAccount.channelName} - ${workspaceAccount.displayName}`,
        }))}
        setConnectedAccountID={(integrationID) => {
          const newIntegration = workspaceAccounts.find(
            (option) => option.id === integrationID
          );
          setIntegration(newIntegration!);
        }}
        Preview={
          <SlackPostPreview
            content={{ ...content, assets }}
            connectedAccount={integration}
          />
        }
        isReadOnly={isReadOnly}
      />
      <div
        className="group"
        onDragOverCapture={() => {
          setDraggedOver(true);
        }}
        onDragLeaveCapture={() => {
          setDraggedOver(false);
        }}
      >
        {isRoomReady && (
          <SlackEditorWrapper
            key={content?.id || "slack-editor"}
            title="Post Content"
            placeholder="Add copy for the post here"
            initialValue={content?.editorState}
            isReadOnly={isReadOnly}
            onSave={(editorState) => {
              assert(content);
              setContent({
                ...content,
                editorState,
              });

              upsertSlackContent.mutate({
                id: content.id,
                copy: content.copy,
                editorState: editorState,
                postID: post.id,
              });
            }}
            onUpdateContent={(copy) => {
              setContent({
                ...content,
                copy,
              });
            }}
            onImagePaste={(event) => {
              if (uppy) {
                handleFilesUpload({
                  items: event.clipboardData.items,
                  uppy,
                  meta: {
                    contentID: content.id,
                  },
                  getValidFiles,
                });
              }
            }}
            roomID={createRoomID({
              workspaceID: post.workspaceID,
              contentType: "post",
              postID: post.id,
              roomContentID: content.id,
            })}
          />
        )}
        <EditorActionsBar
          post={post}
          channel="Slack"
          visible={true}
          isReadOnly={isReadOnly}
          characterCount={{
            current: content.copy.length,
            max: 40000,
          }}
          openHowToTagModal={() => {
            openIntercomArticle("slack_tagging");
          }}
          uploadAssetButton={{
            acceptedMimetypes: [
              "image/jpeg",
              "image/png",
              "image/gif",
              "video/mp4",
              "video/quicktime",
            ],
            onUploadFiles: async (files) => {
              if (uppy) {
                handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID: content.id,
                  },
                  getValidFiles,
                });
              }
            },
            isLoading: content.uploading,
          }}
        />
        <div
          className={classNames({
            "mt-2": assets.length > 0 || draggedOver,
          })}
        >
          <Assets
            assets={assets}
            uploadingAssets={uploadingAssets}
            onDeleteClick={(asset) => {
              setAssets((prevAssets) =>
                prevAssets.filter(
                  (currentAsset) => currentAsset.id !== asset.id
                )
              );
              deleteAssetMutation.mutate({
                slackAssetID: asset.id,
              });
            }}
            editImageModalProperties={editImageModalProperties}
            onEditClick={(asset) => {
              if (asset.metadata.mimetype.startsWith("image/")) {
                setEditImageModalProperties({
                  title: "Edit Image",
                  channel: "Slack",
                  isOpen: true,
                  asset,
                });
              }
            }}
            onUpload={async (file, updateAssetID) => {
              if (uppy) {
                if (updateAssetID) {
                  uppy.addFile({
                    name: file.name,
                    type: file.type,
                    data: file,
                    meta: {
                      contentID: content.id,
                      updateAssetID,
                    },
                  });
                } else {
                  await handleFilesUpload({
                    files: [file],
                    uppy,
                    meta: {
                      contentID: content.id,
                    },
                    getValidFiles,
                  });
                }
              }
            }}
            onEditImageModalClose={() => setEditImageModalProperties(null)}
            refetchAssets={() => {}}
            isReadOnly={isReadOnly}
            onDrop={async (files) => {
              setDraggedOver(false);
              if (uppy) {
                await handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID: content.id,
                  },
                  getValidFiles,
                });
              }
            }}
            showDropzone={draggedOver}
            acceptedFileTypes={{
              "image/jpg": [".jpg", ".jpeg"],
              "image/png": [".png"],
              "image/gif": [".gif"],
              "image/webp": [".webp"],
              "video/mp4": [".mp4"],
              "video/quicktime": [".mov"],
            }}
            showNewEditButton={true}
          />
        </div>
      </div>
    </div>
  );
};

export default SlackPost;
