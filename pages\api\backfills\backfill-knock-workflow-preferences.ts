import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import chunk from "lodash/chunk";
import { db } from "~/clients";
import knock from "~/clients/Knock";
import { sleep } from "~/utils";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const users = await db.user.findMany();

  const failedUsers: string[] = [];
  // Setting user preferences is tier 3 e.g. 60 requests per second
  const chunks = chunk(users, 60);
  let i = 1;
  for await (const chunk of chunks) {
    console.log(`chunk ${i} of ${chunks.length}`);
    const promises = chunk.map(async (user) => {
      try {
        const preferences = await knock.users.getPreferences(user.id);
        const response = await knock.users.setPreferences(user.id, {
          ...preferences,
          workflows: {
            ...preferences.workflows,
            "post-missing-required-approvals": {
              channel_types: {
                chat: true,
                email: true,
                in_app_feed: true,
              },
            },
          },
        });
      } catch (error) {
        failedUsers.push(user.id);
        console.log(`${user.id}, not in knock`);
      }
    });

    await Promise.all(promises);
    // Add an extra second just in case
    await sleep(1000);
    i++;
  }

  res.status(StatusCodes.OK).send({ failedUsers });
};

export default handler;
