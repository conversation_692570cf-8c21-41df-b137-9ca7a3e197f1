import Document from "@tiptap/extension-document";
import History from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { ClipboardEvent, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import {
  AutoHashtag,
  AutoMention,
  CharacterCount,
  Emoji,
  Link,
  PasteHandler,
} from "./extensions";
import BoldUnicodeButton from "./ui/BoldUnicodeButton";
import FloatingToolbar from "./ui/FloatingToolbar";
import ItalicsUnicodeButton from "./ui/ItalicsUnicodeButton";

type Props = {
  title: string;
  postID?: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  onFocusChange?: (isFocused: boolean) => void;
};

const ThreadsEditor = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  onFocusChange,
}: Props) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const editor = useEditor({
    extensions: [
      Document,
      Paragraph,
      TextDirection.configure({
        types: ["heading", "paragraph"],
      }),
      Text,
      History,
      Emoji,
      Link.configure({
        isLinkShorteningEnabled: !!postID ? true : false,
        postID,
      }),
      Placeholder.configure({
        placeholder,
      }),
      AutoHashtag.configure({
        urlFn: (hashtag) => `https://www.threads.com/search?q=${hashtag}`,
      }),
      AutoMention.configure({
        baseUrl: "https://www.threads.com/@",
        regex: /(?<=^|\s)(@[A-Za-z0-9_\.]{3,30})/g,
      }),
      CharacterCount.configure({
        limit: 500,
      }),
      PasteHandler,
    ],
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      // TipTap doubles newlines for paragraph breaks (e.g., pressing Enter once creates two \n).
      // We halve the newlines to match LinkedIn's expected format while preserving paragraph structure.
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      onUpdateContent(textContent);

      setEditorState(editor.getJSON());
    },
    autofocus: true,
    onFocus: () => {
      if (onFocusChange) {
        onFocusChange(true);
      }
    },
    onBlur: () => {
      if (onFocusChange) {
        onFocusChange(false);
      }
    },
    content: editorState,
    editable: !isReadOnly,
  });

  return (
    <div
      className="py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <FloatingToolbar editor={editor} shouldHideWithLink={true}>
        <BoldUnicodeButton editor={editor} useCustomToolbarButton={true} />
        <ItalicsUnicodeButton editor={editor} useCustomToolbarButton={true} />
      </FloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}
      <EditorContent editor={editor} />
    </div>
  );
};

export default ThreadsEditor;
