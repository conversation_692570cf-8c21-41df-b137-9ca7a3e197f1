import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import { SubscriptionStatus } from "@prisma/client";
import { LoopsClient } from "loops";
import { db } from "~/clients";

const sleep = (milliseconds: number) => {
  return new Promise((resolve) => setTimeout(resolve, milliseconds));
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const loops = new LoopsClient(process.env.LOOPS_API_KEY as string);

  const users = await db.user.findMany({
    select: {
      id: true,
      email: true,
      userWorkspaces: {
        select: {
          workspace: {
            select: {
              id: true,
              name: true,
              company: {
                select: {
                  trialEndsAt: true,
                  subscription: {
                    select: {
                      status: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  });

  const isNotSubscribed = users.filter((user) => {
    const notSubscribed = user.userWorkspaces.some((userWorkspace) => {
      const subscriptionStatus =
        userWorkspace.workspace.company.subscription?.status;

      return (
        subscriptionStatus && subscriptionStatus !== SubscriptionStatus.active
      );
    });
    const isTrialing = user.userWorkspaces.some((userWorkspace) => {
      const { trialEndsAt } = userWorkspace.workspace.company;
      return trialEndsAt !== null && trialEndsAt < new Date();
    });
    const hasMultipleWorkspaces = user.userWorkspaces.length > 1;
    return (notSubscribed || isTrialing) && !hasMultipleWorkspaces;
  });

  const promises = isNotSubscribed.map(async (user) => {
    const loopsUser = await loops.updateContact(user.email, {
      userId: user.id,
      // @ts-expect-error
      mailingLists: {
        clxuu2js7002l0klabeggb8b5: true,
      },
    });
  });

  await Promise.all(promises);

  res.status(StatusCodes.OK).send({
    numUsers: isNotSubscribed.length,
    users: isNotSubscribed.map((user) => user.email),
  });
};

export default handler;
