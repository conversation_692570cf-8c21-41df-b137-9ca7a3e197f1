import { ReactElement } from "react";

import classNames from "classnames";
import { motion } from "framer-motion";
import Loader from "./Loader";
import KbdTooltip from "./app/KbdTooltip";

type Button = {
  key: string;
  label: string | ReactElement;
  onClick: () => void;
  isLoading?: boolean;
  hotKey?: string;
  selected: boolean;
};

type Kind = "bare" | "hollow" | "switch";

type Props = {
  buttons: Button[];
  kind?: Kind;
  disabled?: boolean;
};

const ButtonGroup = ({ buttons, kind = "hollow", disabled }: Props) => {
  return (
    <div
      className={classNames("inline-flex h-4 items-center text-xs", {
        "divide-dark-gray divide-x": kind === "bare",
        "gap-2": kind === "hollow",
        "bg-slate rounded-full px-1 py-4": kind === "switch",
      })}
    >
      {buttons.map((button, i) => {
        const hotKey = button.hotKey || `${i + 1}`;
        if (kind === "hollow") {
          return (
            <KbdTooltip
              key={button.key}
              hotKeys={disabled || button.isLoading ? undefined : hotKey}
              onKeyPress={() => {
                if (disabled || button.isLoading) {
                  return;
                } else {
                  button.onClick();
                }
              }}
              label={button.label}
            >
              <button
                key={button.key}
                type="button"
                disabled={disabled || button.isLoading}
                onClick={button.onClick}
                className={classNames(
                  "font-eyebrow relative rounded-md px-3 py-1.5 transition",
                  {
                    "text-secondary": button.selected,
                    "hover:bg-lightest-gray-30 hover:border-medium-gray/30":
                      !button.selected && !button.isLoading,
                    "opacity-50": button.isLoading,
                  }
                )}
              >
                <div className="flex items-center gap-2 whitespace-nowrap text-transparent">
                  {button.label}
                  {button.isLoading && <Loader color="white" size="sm" />}
                </div>
                {button.selected && (
                  <motion.span
                    layoutId="bubble"
                    className="border-medium-gray/30 bg-lightest-gray-30 absolute inset-0 rounded-md border"
                    transition={{ type: "spring", bounce: 0, duration: 0.4 }}
                  />
                )}
                <div
                  className={classNames(
                    "absolute inset-0 flex h-full w-full items-center gap-2 px-3 py-1.5 whitespace-nowrap transition-colors",
                    {
                      "text-secondary": button.selected,
                      "text-medium-dark-gray hover:text-black":
                        !button.selected && !button.isLoading,
                    }
                  )}
                >
                  {button.label}
                  {button.isLoading && <Loader color="mediumGray" size="sm" />}
                </div>
              </button>
            </KbdTooltip>
          );
        } else if (kind === "bare") {
          const bareButtonClassName = classNames({
            "pr-2": i === 0,
            "pl-2": i === buttons.length - 1,
            "px-2": i !== 0 && i !== buttons.length - 1,
            "text-basic font-medium": button.selected,
            "text-medium-gray hover:text-off-black": !button.selected,
          });

          return (
            <KbdTooltip
              key={button.key}
              hotKeys={hotKey}
              onKeyPress={button.onClick}
              label={button.label}
            >
              <button
                type="button"
                disabled={disabled}
                onClick={button.onClick}
                className={classNames("transition-colors", bareButtonClassName)}
              >
                {button.label}
              </button>
            </KbdTooltip>
          );
        } else if (kind === "switch") {
          return (
            <KbdTooltip
              key={button.key}
              hotKeys={disabled || button.isLoading ? undefined : hotKey}
              onKeyPress={() => {
                if (disabled || button.isLoading) {
                  return;
                } else {
                  button.onClick();
                }
              }}
              label={button.label}
            >
              <button
                key={button.key}
                type="button"
                disabled={disabled || button.isLoading}
                onClick={button.onClick}
                className={classNames(
                  "font-body-sm relative rounded-md px-2 py-0.5 font-medium transition-colors",
                  {
                    "text-black": button.selected,
                    "text-medium-gray hover:text-black":
                      !button.selected && !button.isLoading,
                    "opacity-50": button.isLoading,
                  }
                )}
              >
                <div className="flex items-center gap-2 whitespace-nowrap text-transparent">
                  {button.label}
                  {button.isLoading && <Loader color="white" size="sm" />}
                </div>
                {button.selected && (
                  <motion.span
                    layoutId="bubble"
                    className="box-shadow absolute inset-0 rounded-full bg-white"
                    transition={{ type: "spring", bounce: 0, duration: 0.4 }}
                  />
                )}
                <div
                  className={classNames(
                    "absolute inset-0 flex h-full w-full items-center gap-2 px-2 py-0.5 whitespace-nowrap transition-colors",
                    {
                      "text-black": button.selected,
                      "text-medium-dark-gray hover:text-black":
                        !button.selected && !button.isLoading,
                    }
                  )}
                >
                  {button.label}
                  {button.isLoading && <Loader color="mediumGray" size="sm" />}
                </div>
              </button>
            </KbdTooltip>
          );
        }
      })}
    </div>
  );
};

export default ButtonGroup;
