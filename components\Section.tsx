import { ReactNode } from "react";

import classNames from "classnames";

import { Disclosure } from "@headlessui/react";
import { ChevronRightIcon } from "@heroicons/react/24/outline";

type Size = "sm" | "lg";

type Props = {
  /** The title of the section */
  title: string | ReactNode;
  /** The children of the section that can be shown/hidden */
  children: ReactNode;
  /** The size of the label and the open/close icon */
  size?: Size;
  /** Controls if the section is expandable, if false then it will be default open */
  expandable?: boolean;
};

const Section = ({
  title,
  children,
  size = "sm",
  expandable = true,
}: Props) => {
  return (
    <Disclosure defaultOpen={!expandable}>
      {({ open }) => (
        <div>
          {expandable ? (
            <Disclosure.Button className="flex w-full items-center justify-between gap-2">
              {size === "sm" ? (
                <div className="font-eyebrow whitespace-nowrap">{title} </div>
              ) : (
                <h4 className="pb-2">{title}</h4>
              )}
              <ChevronRightIcon
                className={classNames(
                  "text-medium-gray transition-transform",
                  { "h-6 w-6": size === "lg" },
                  { "h-4 w-4": size === "sm" },
                  {
                    "rotate-90": open,
                  }
                )}
              />
            </Disclosure.Button>
          ) : (
            <>
              {size === "sm" ? (
                <div className="font-eyebrow text-medium-dark-gray pl-2 whitespace-nowrap">
                  {title}{" "}
                </div>
              ) : (
                <h4 className="pb-2">{title}</h4>
              )}
            </>
          )}
          <div
            className={classNames(
              "transition-max-height scrollbar-hidden overflow-auto",
              {
                "max-h-60": open,
                "max-h-0 overflow-clip": !open,
              }
            )}
          >
            {children}
          </div>
        </div>
      )}
    </Disclosure>
  );
};

export default Section;
