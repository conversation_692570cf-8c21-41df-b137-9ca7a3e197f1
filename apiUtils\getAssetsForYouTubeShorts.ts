import { Asset } from "@prisma/client";
import { AssetType } from "~/types/Asset";
import enhanceAssetType from "./assets/enhanceAssetType";

const getAssetsForYouTubeShorts = (post: {
  id: string;
  workspaceID: string;
  youTubeShortsContent: {
    id: string;
    asset: {
      id: string;
      asset: Asset;
    } | null;
  } | null;
}): { asset: AssetType | null } => {
  if (!post.youTubeShortsContent) {
    return {
      asset: null,
    };
  }

  if (!post.youTubeShortsContent.asset) {
    return { asset: null };
  }

  return {
    asset: {
      ...enhanceAssetType(post.youTubeShortsContent.asset.asset),
      id: post.youTubeShortsContent.asset.id,
    },
  };
};

export default getAssetsForYouTubeShorts;
