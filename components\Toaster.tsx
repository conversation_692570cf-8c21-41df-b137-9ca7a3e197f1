import { CheckCircleIcon, XCircleIcon } from "@heroicons/react/24/outline";
import { Toaster as Son<PERSON> } from "sonner";

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <Sonner
      className="toaster group"
      position="bottom-right"
      closeButton={true}
      icons={{
        success: <CheckCircleIcon className="text-success h-4 w-4" />,
        error: <XCircleIcon className="text-danger h-4 w-4" />,
      }}
      toastOptions={{
        classNames: {
          toast:
            "group z-60 toast group-[.toaster]:bg-white group-[.toaster]:font-body-sm group-[.toaster]:text-black group-[.toaster]:border-none group-[.toaster]:shadow-md",
          description:
            "group-[.toast]:font-body-sm group-[.toast]:text-medium-dark-gray",
          actionButton:
            "group-[.toast]:inline-flex group-[.toast]:items-center group-[.toast]:transition-all group-[.toast]:transform group-[.toast]:h-8! group-[.toast]:py-1 group-[.toast]:px-2 group-[.toast]:rounded-[5px] group-[.toast]:!font-body-sm group-[.toast]:font-medium group-[.toast]:w-fit group-[.toast]:bg-slate! group-[.toast]:text-basic! hover:group-[.toast]:bg-light-gray disabled:group-[.toast]:text-medium-dark-gray",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
          closeButton: "opacity-0 group-hover:opacity-100 transition-opacity",
        },
      }}
      {...props}
    />
  );
};

export default Toaster;
