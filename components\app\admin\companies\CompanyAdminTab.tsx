import { Divider } from "~/components";
import Checkbox from "~/components/Checkbox";
import { getFullName, trpc } from "~/utils";

type Props = {
  company: {
    id: string;
    userCompanies: {
      id: string;
      user: {
        id: string;
        firstName: string | null;
        lastName: string | null;
        email: string;
      };
    }[];
  };
  users: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  }[];
  usersInMultipleWorkspaces: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  }[];
};

const UserCheckbox = ({
  companyID,
  user,
  isAdmin,
}: {
  companyID: string;
  user: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  };
  isAdmin: boolean;
}) => {
  const trpcUtils = trpc.useUtils();
  const updateAdminMutation = trpc.admin.company.updateAdmin.useMutation();

  return (
    <Checkbox
      value={isAdmin}
      onChange={(value) => {
        const companyData = trpcUtils.admin.company.get.getData({
          id: companyID,
        });
        updateAdminMutation.mutate({
          companyID: companyID,
          userID: user.id,
          isAdmin: value,
        });
        if (companyData) {
          let newUserCompanies = companyData.company.userCompanies;
          if (value) {
            newUserCompanies = [
              ...newUserCompanies,
              {
                id: user.id,
                user: {
                  id: user.id,
                  firstName: user.firstName,
                  lastName: user.lastName,
                  email: user.email,
                },
              },
            ];
          } else {
            newUserCompanies = newUserCompanies.filter(
              (userCompany) => userCompany.user.id !== user.id
            );
          }

          trpcUtils.admin.company.get.setData(
            { id: companyID },
            {
              company: {
                ...companyData.company,
                userCompanies: newUserCompanies,
              },
            }
          );
        }
      }}
      label={
        <div>
          {getFullName(user)}{" "}
          <span className="text-medium-dark-gray">({user.email})</span>
        </div>
      }
    />
  );
};

const CompanyAdminTab = ({
  company,
  users,
  usersInMultipleWorkspaces,
}: Props) => {
  const companyUserIDs = company.userCompanies.map(
    (userCompany) => userCompany.user.id
  );

  const userIDsInMultipleWorkspaces = usersInMultipleWorkspaces.map(
    (user) => user.id
  );

  const remainingUsers = users.filter(
    (user) => !userIDsInMultipleWorkspaces.includes(user.id)
  );

  return (
    <div className="font-body-sm flex flex-col gap-2">
      <span className="font-body font-medium">Agency Users</span>
      {usersInMultipleWorkspaces.map((user) => (
        <UserCheckbox
          companyID={company.id}
          user={user}
          isAdmin={companyUserIDs.includes(user.id)}
        />
      ))}
      {usersInMultipleWorkspaces.length === 0 && (
        <div className="font-body-sm text-medium-dark-gray">
          No users in multiple workspaces
        </div>
      )}
      {remainingUsers.length > 0 && (
        <div>
          <Divider padding="md" />
          <div className="flex flex-col gap-2">
            <span className="font-body font-medium">Customers</span>
            {remainingUsers.map((user) => (
              <UserCheckbox
                companyID={company.id}
                user={user}
                isAdmin={companyUserIDs.includes(user.id)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyAdminTab;
