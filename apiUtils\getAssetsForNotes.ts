import { Channel } from "@prisma/client";
import getAssetsForFilePaths from "./getAssetsForFilePaths";

const getAssetsForNotes = async (post: {
  id: string;
  workspaceID: string;
  channels: Channel[];
}) => {
  const notesChannels: Channel[] = [
    "Email",
    "Website",
    "BlogPost",
    "Event",
    "SMS",
    "Youtube",
    "Discourse",
    "Press",
    "Podcast",
    "Partnerships",
    "Influencers",
    "AppNotification",
    "Strava",
    "Newsletter",
    "Gallery",
    "Farcaster",
    "Lens",
    "Other",
  ];
  if (!post.channels.some((channel) => notesChannels.includes(channel))) {
    return [];
  }

  const assetsMap = await getAssetsForFilePaths([
    {
      id: post.id,
      path: `${post.workspaceID}/${post.id}`,
    },
  ]);

  return assetsMap[post.id];
};

export default getAssetsForNotes;
