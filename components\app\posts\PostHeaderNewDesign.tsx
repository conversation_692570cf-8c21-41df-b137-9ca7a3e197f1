import {
  ArrowLeftIcon,
  ArrowUturnRightIcon,
  ChatBubbleOvalLeftIcon,
  CheckCircleIcon,
  DocumentDuplicateIcon,
  EllipsisVerticalIcon,
} from "@heroicons/react/24/outline";
import { ApprovalStatus, Channel, PostStatus } from "@prisma/client";
import { DateTime } from "luxon";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import { useState } from "react";
import { toast } from "sonner";
import { AutosizeInput, Divider, TextLink } from "~/components";
import { SuggestedTimes } from "~/components/TimeInput";
import Button from "~/components/ui/Button";
import DropdownMenu from "~/components/ui/DropdownMenu";
import Sidebar from "~/components/ui/Sidebar";
import { useBreakpoints } from "~/hooks";
import { useThreadComments, useUser, useWorkspace } from "~/providers";
import {
  capitalizeWords,
  dateFromUTC,
  getPostPublishAtMoment,
  handleTrpcError,
  openConfirmationModal,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import { Label } from "../LabelSelect";
import ShareLinkDropdown from "../ShareLinkDropdown";
import BulkDuplicatePostModal from "./BulkDuplicatePostModal";

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    approvals: {
      id: string;
      userID: string;
      status: ApprovalStatus;
    }[];
    labels: Label[];
    channels: Channel[];
    status: PostStatus;
    isIdea: boolean;
    isSharedPublicly: boolean;
  } & PostOrIdea;
  isReadOnly?: boolean;
};

const PostHeaderNewDesign = ({ post, isReadOnly = false }: Props) => {
  const router = useRouter();
  const { user } = useUser();

  const { currentWorkspace } = useWorkspace();
  const { toggleThreadCommentsSidebar, isThreadCommentsSidebarVisible } =
    useThreadComments();
  const { isDesktop } = useBreakpoints();

  const trpcUtils = trpc.useUtils();
  const [title, setTitle] = useState<string>(post.title);
  const [bulkDuplicateModalOpen, setBulkDuplicateModalOpen] =
    useState<boolean>(false);

  const publishDate = post.publishDate ? dateFromUTC(post.publishDate) : null;
  const publishTime = post.publishTime
    ? getPostPublishAtMoment(post).toJSDate()
    : null;

  const postType = post.isIdea ? "idea" : "post";

  const requestedApproval = post.approvals.find(
    (approval) => approval.userID === user?.id
  );
  const duplicatePostMutation = trpc.post.duplicate.useMutation({
    onError: (error) => {
      handleTrpcError({
        title: `Failed to Duplicate ${capitalizeWords(postType)}`,
        error,
      });
    },
  });
  const mostRecentlyUsedQuery = trpc.post.getMostRecentlyUsed.useQuery();
  const mostRecentlyUsedTimes = (
    mostRecentlyUsedQuery.data?.publishAts || []
  ).map((publishAt) => new Date(publishAt));
  const optimalPublishTimeQuery = trpc.post.getBestTimeToPost.useQuery(
    {
      channel: post.channels[0],
      publishDate,
    },
    {
      enabled: postType === "post",
      refetchOnWindowFocus: false,
      refetchInterval: false,
    }
  );
  const optimalPublishTime = optimalPublishTimeQuery.data?.publishTime;

  const suggestedTimes: SuggestedTimes[] = [];
  if (publishTime == null) {
    if (currentWorkspace?.timezone == null) {
      suggestedTimes.push({
        label: "Recommended",
        howToComponent: (
          <TextLink
            href="/settings/general"
            size="sm"
            wrapText={true}
            openInNewTab={false}
            value="Set a timezone to see 
      recommendations ->"
          />
        ),
      });
    } else if (optimalPublishTime) {
      suggestedTimes.push({
        label: "Recommended",
        times: [
          DateTime.fromJSDate(optimalPublishTime).setLocale("local").toJSDate(),
        ],
      });
    }

    suggestedTimes.push({
      label: "Recently Used",
      times: mostRecentlyUsedTimes,
    });
  }

  const deletePostMutation = trpc.post.delete.useMutation();
  const updatePostMutation = trpc.post.update.useMutation();

  const handleDeleteContent = () => {
    openConfirmationModal({
      title: `Confirm Deletion of ${capitalizeWords(postType)}`,
      body: `This action cannot be undone. Please confirm you want to delete this ${postType}.`,
      cancelButton: {
        label: "Cancel, don't delete",
      },
      primaryButton: {
        label: `Delete ${capitalizeWords(postType)}`,
        variant: "danger",
        onClick: () => {
          toast.promise(
            deletePostMutation.mutateAsync(
              {
                id: post.id,
              },
              {
                onSuccess: (data) => {
                  trpcUtils.post.getMany.refetch();
                  if (data.post.isIdea) {
                    router.push("/ideas");
                  } else {
                    router.push("/");
                  }
                },
              }
            ),
            {
              loading: `Deleting ${capitalizeWords(postType)}`,
              success: () => {
                return {
                  message: `${capitalizeWords(postType)} Deleted`,
                  description: `Successfully deleted ${postType.toLocaleLowerCase()}`,
                };
              },
              error: (error) => {
                return {
                  message: `Failed to Delete ${capitalizeWords(postType)}`,
                  description: error.message as string,
                };
              },
            }
          );
        },
      },
    });
  };

  return (
    <div className="fixed top-0 z-10 w-full bg-white md:w-[calc(100vw-224px)]">
      <div className="flex items-center justify-between px-8 pt-3 md:hidden">
        <Link href="/">
          <Image
            src="/logos/assembly_logo_text.svg"
            width={76}
            height={16}
            className="mt-1"
            alt="Assembly"
          />
        </Link>
        <div className="flex items-center gap-2 md:hidden">
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              <div className="hover:bg-basic/10 text-basic rounded-full bg-inherit p-1.5 transition-all">
                <DocumentDuplicateIcon className="h-5 w-5" />
              </div>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content position="bottom-left">
              <DropdownMenu.Group>
                <DropdownMenu.Item
                  icon="DocumentDuplicateIcon"
                  onClick={async () => {
                    const postType = post.isIdea ? "Idea" : "Post";

                    toast.promise(
                      duplicatePostMutation.mutateAsync(
                        {
                          sourcePostID: post.id,
                        },
                        {
                          onSuccess: (data) => {
                            if (post.isIdea) {
                              router.push(`/ideas/${data.post.id}`);
                            } else {
                              router.push(`/posts/${data.post.id}`);
                            }
                          },
                        }
                      ),
                      {
                        loading: `Duplicating ${postType}`,
                        success: () => {
                          return {
                            message: `${postType} Duplicated`,
                            description: `Successfully duplicated ${postType.toLocaleLowerCase()}`,
                          };
                        },
                        error: (error) => {
                          return {
                            message: `Failed to Duplicate ${postType}`,
                            description: error.message as string,
                          };
                        },
                      }
                    );
                  }}
                >
                  Duplicate {postType}
                </DropdownMenu.Item>
                {!post.isIdea && (
                  <DropdownMenu.Item
                    icon="DocumentDuplicateIcon"
                    onClick={() => setBulkDuplicateModalOpen(true)}
                  >
                    Batch Duplicate
                  </DropdownMenu.Item>
                )}
              </DropdownMenu.Group>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
          <ShareLinkDropdown
            id={post.id}
            type="post"
            isSharedPublicly={post.isSharedPublicly}
          />
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              <Button variant="ghost" size="icon">
                <EllipsisVerticalIcon className="h-5 w-5" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content position="bottom-left">
              <DropdownMenu.Group>
                <DropdownMenu.Item
                  icon="TrashIcon"
                  intent="danger"
                  onClick={handleDeleteContent}
                >
                  Delete {capitalizeWords(postType)}
                </DropdownMenu.Item>
              </DropdownMenu.Group>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
          <Sidebar.Trigger />
        </div>
      </div>
      <div className="flex items-center gap-2 px-8 pt-5 pb-3.5 lg:px-12 lg:py-3.5">
        <div className="flex w-full flex-col items-start gap-4 lg:flex-row lg:items-center">
          {!isReadOnly ? (
            <>
              {router.asPath.includes("/inbox") ? (
                <button
                  className="text-basic font-body-sm flex items-center gap-1 font-medium whitespace-nowrap"
                  onClick={() => {
                    router.push(`/${postType}s/${post.id}`);
                  }}
                >
                  <ArrowUturnRightIcon className="h-4 w-4" />
                  Go to Post Page
                </button>
              ) : (
                <button
                  className="text-basic font-body-sm hidden items-center gap-1 font-medium md:flex"
                  onClick={() => {
                    router.back();
                  }}
                >
                  <ArrowLeftIcon className="h-4 w-4" />
                  Back
                </button>
              )}
            </>
          ) : null}
          <div className="w-full max-w-[100px] lg:max-w-none">
            {requestedApproval?.status === ApprovalStatus.Approved && (
              <div className="body-sm text-success mr-2 flex items-center gap-1">
                <CheckCircleIcon className="h-4 w-4" />
                <div className="font-medium">Approved by you</div>
              </div>
            )}
            <AutosizeInput
              value={title}
              onChange={setTitle}
              isReadOnly={isReadOnly}
              onBlur={() => {
                if (title.length === 0) {
                  setTitle(post.title);
                  toast.error("Update failed", {
                    description: "Title must be at least one character long",
                  });
                } else if (post.title !== title) {
                  updatePostMutation.mutate(
                    {
                      id: post.id,
                      title,
                    },
                    {
                      onSuccess: () => {
                        if (post.isIdea) {
                          const ideasData = trpcUtils.idea.getAll.getData();

                          if (ideasData) {
                            const ideaIndex = ideasData.ideas.findIndex(
                              (idea) => idea.id === post.id
                            );

                            const newIdeasData = [...ideasData.ideas];
                            newIdeasData[ideaIndex] = {
                              ...newIdeasData[ideaIndex],
                              title,
                            };

                            trpcUtils.idea.getAll.setData(undefined, {
                              ideas: newIdeasData,
                            });
                          }
                        } else {
                          const postData = trpcUtils.post.get.getData({
                            id: post.id,
                          });

                          if (postData) {
                            trpcUtils.post.get.setData(
                              { id: post.id },
                              {
                                post: {
                                  ...postData.post,
                                  title,
                                },
                                isReadOnly,
                              }
                            );
                          }
                        }
                      },
                    }
                  );
                }
              }}
              size="sm"
              weight="medium"
            />
          </div>
        </div>
        {!isReadOnly ? (
          <div className="-mt-1.5 hidden items-center gap-0 self-start md:flex lg:mt-0 lg:gap-2">
            {isDesktop && (
              <Button
                onClick={toggleThreadCommentsSidebar}
                variant={isThreadCommentsSidebarVisible ? "secondary" : "ghost"}
                size="sm"
              >
                <ChatBubbleOvalLeftIcon className="h-4 w-4" />
                Comments
              </Button>
            )}
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <Button variant="ghost" size="icon">
                  <DocumentDuplicateIcon className="h-5 w-5" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content position="bottom-left">
                <DropdownMenu.Group>
                  <DropdownMenu.Item
                    icon="DocumentDuplicateIcon"
                    onClick={async () => {
                      const postType = post.isIdea ? "Idea" : "Post";

                      toast.promise(
                        duplicatePostMutation.mutateAsync(
                          {
                            sourcePostID: post.id,
                          },
                          {
                            onSuccess: (data) => {
                              if (post.isIdea) {
                                router.push(`/ideas/${data.post.id}`);
                              } else {
                                router.push(`/posts/${data.post.id}`);
                              }
                            },
                          }
                        ),
                        {
                          loading: `Duplicating ${postType}`,
                          success: () => {
                            return {
                              message: `${postType} Duplicated`,
                              description: `Successfully duplicated ${postType.toLocaleLowerCase()}`,
                            };
                          },
                          error: (error) => {
                            return {
                              message: `Failed to Duplicate ${postType}`,
                              description: error.message as string,
                            };
                          },
                        }
                      );
                    }}
                  >
                    Duplicate {postType}
                  </DropdownMenu.Item>
                  {!post.isIdea && (
                    <DropdownMenu.Item
                      icon="DocumentDuplicateIcon"
                      onClick={() => setBulkDuplicateModalOpen(true)}
                    >
                      Batch Duplicate
                    </DropdownMenu.Item>
                  )}
                </DropdownMenu.Group>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
            <ShareLinkDropdown
              id={post.id}
              type="post"
              isSharedPublicly={post.isSharedPublicly}
            />
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <Button variant="ghost" size="icon">
                  <EllipsisVerticalIcon className="h-5 w-5" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content position="bottom-left">
                <DropdownMenu.Group>
                  <DropdownMenu.Item
                    icon="TrashIcon"
                    intent="danger"
                    onClick={handleDeleteContent}
                  >
                    Delete {capitalizeWords(postType)}
                  </DropdownMenu.Item>
                </DropdownMenu.Group>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          </div>
        ) : null}
      </div>
      <Divider padding={"none"} className="bg-lightest-gray hidden lg:block" />
      {!post.isIdea && (
        <BulkDuplicatePostModal
          open={bulkDuplicateModalOpen}
          setOpen={setBulkDuplicateModalOpen}
          onSuccess={(posts) => {
            toast.success(`${posts.length} Posts Created`);
          }}
          post={post}
        />
      )}
    </div>
  );
};

export default PostHeaderNewDesign;
