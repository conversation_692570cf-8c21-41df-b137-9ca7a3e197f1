import { <PERSON><PERSON><PERSON>Hand<PERSON> } from "react";

import { PlusIcon } from "@heroicons/react/24/outline";
import { Badge } from "~/components";

type Props = {
  label: string;
  onClick?: MouseEventHandler<HTMLDivElement>;
  isReadOnly?: boolean;
  isUppercase?: boolean;
};

const AddNewButton = ({
  label,
  onClick,
  isReadOnly = false,
  isUppercase,
}: Props) => {
  return (
    <Badge
      intent="none"
      kind="light"
      onClick={!isReadOnly ? onClick : undefined}
      showHoverState={!isReadOnly}
      icon={!isReadOnly ? <PlusIcon className="h-3 w-3" /> : undefined}
      label={label}
      isUppercase={isUppercase}
    />
  );
};

export default AddNewButton;
