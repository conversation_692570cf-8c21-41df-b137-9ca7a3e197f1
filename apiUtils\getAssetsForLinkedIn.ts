import { Asset } from "@prisma/client";
import sortBy from "lodash/sortBy";
import { AssetType } from "~/types/Asset";
import enhanceAssetType from "./assets/enhanceAssetType";

const getAssetsForLinkedIn = (post: {
  id: string;
  workspaceID: string;
  linkedInContent: {
    assets: {
      id: string;
      position: number;
      asset: Asset;
    }[];
    coverPhoto: Asset | null;
    captions: Asset | null;
  } | null;
}): {
  assets: AssetType[];
  coverPhoto: AssetType | null;
  captions: AssetType | null;
} => {
  const linkedInContent = post.linkedInContent;

  if (!linkedInContent) {
    return {
      assets: [],
      coverPhoto: null,
      captions: null,
    };
  }

  return {
    assets: sortBy(linkedInContent.assets, "position").map((asset) => {
      return {
        ...enhanceAssetType(asset.asset),
        id: asset.id,
        position: asset.position,
      };
    }),
    coverPhoto: linkedInContent.coverPhoto
      ? enhanceAssetType(linkedInContent.coverPhoto)
      : null,
    captions: linkedInContent.captions
      ? enhanceAssetType(linkedInContent.captions)
      : null,
  };
};

export default getAssetsForLinkedIn;
