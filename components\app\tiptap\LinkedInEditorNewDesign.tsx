import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Document from "@tiptap/extension-document";
import FontFamily from "@tiptap/extension-font-family";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import TextStyle from "@tiptap/extension-text-style";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import classNames from "classnames";
import { ClipboardEvent, useEffect, useRef, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import AutoHashtag from "~/components/app/tiptap/extensions/AutoHashtag";
import CharacterCount from "~/components/app/tiptap/extensions/CharacterCount";
import LinkedInMention from "~/components/app/tiptap/extensions/LinkedInMention";
import { usePostIntegrations, useThreadComments } from "~/providers";
import { LinkedInIntegration } from "~/providers/PostIntegrationsProvider";
import { CHANNEL_ICON_MAP } from "~/types";
import { getDefaultProfilePicture, stripLiveblocksMarks } from "~/utils";
import escapeSpecialCharacters from "~/utils/linkedin/escapeSpecialCharacters";
import ThreadCommentsPopover from "../posts/ThreadCommentsPopover";
import {
  CustomCommentThreadMark,
  Emoji,
  Link,
  PasteHandler,
} from "./extensions";
import BoldUnicodeButton from "./ui/BoldUnicodeButton";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import EditorHeightTooltip from "./ui/EditorHeightTooltip";
import FloatingComposer from "./ui/FloatingComposer";
import FloatingToolbar from "./ui/FloatingToolbar";
import ItalicsUnicodeButton from "./ui/ItalicsUnicodeButton";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  title?: string;
  postID?: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  size?: "sm" | "md";
  disableTagging?: boolean;
  escapeContent?: boolean;
  isEditable?: boolean;
};

const createBaseExtensions = (
  postID: string | undefined,
  placeholder: string,
  disableTagging: boolean,
  size: "sm" | "md"
) => [
  Document,
  Paragraph.configure({
    HTMLAttributes: {
      class: size === "sm" ? "font-body-sm" : "font-body",
    },
  }),
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  Text,
  TextStyle,
  FontFamily,
  Link.configure({
    isLinkShorteningEnabled: !!postID ? true : false,
    postID,
  }),
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  Emoji,
  AutoHashtag.configure({
    urlFn: (hashtag) =>
      `https://www.linkedin.com/feed/hashtag/?keywords=${hashtag}`,
  }),
  ...(!disableTagging ? [LinkedInMention] : []),
  CharacterCount.configure({
    limit: 3000,
  }),
  PasteHandler,
];

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const LinkedInEditorFallback = ({
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  size = "md",
  isReadOnly = false,
  disableTagging = false,
  escapeContent = true,
  isEditable = false,
}: BaseProps) => {
  const cleanContent = stripLiveblocksMarks(initialValue);
  const [editorState, setEditorState] = useState<JSONContent | null>(
    cleanContent
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(cleanContent) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const editor = useEditor({
    extensions: createBaseExtensions(postID, placeholder, disableTagging, size),
    immediatelyRender: false,
    content: cleanContent,
    editable: isEditable && !isReadOnly,
    onUpdate: ({ editor }) => {
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });

      if (onUpdateContent) {
        if (escapeContent) {
          const cleanedContent = escapeSpecialCharacters(textContent);
          onUpdateContent(cleanedContent);
        } else {
          onUpdateContent(textContent);
        }
      }

      setEditorState(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: "min-h-11 sm:min-h-full",
      },
    },
  });

  if (!editor) return null;

  return isEditable && !isReadOnly ? (
    <div
      className={classNames({
        "py-0": size === "sm",
        "py-2": size === "md",
      })}
    >
      <FloatingToolbar editor={editor} shouldHideWithLink={true}>
        <BoldUnicodeButton editor={editor} useCustomToolbarButton={true} />
        <ItalicsUnicodeButton editor={editor} useCustomToolbarButton={true} />
      </FloatingToolbar>
      <div className="max-w-[622px]">
        <EditorContent editor={editor} />
      </div>
    </div>
  ) : (
    <div className="max-w-[622px]">
      <EditorContent editor={editor} />
    </div>
  );
};

const LinkedInEditor = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  size = "md",
  disableTagging = false,
  escapeContent = true,
  roomID,
}: BaseProps & { roomID: string }) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );
  const { setEditorForRoom } = useThreadComments();
  const { currentChannel } = usePostIntegrations();

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(postID, placeholder, disableTagging, size),
    ],
    immediatelyRender: false,
    editable: !isReadOnly,
    onUpdate: ({ editor }) => {
      // TipTap doubles newlines for paragraph breaks (e.g., pressing Enter once creates two \n).
      // We halve the newlines to match LinkedIn's expected format while preserving paragraph structure.
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      if (escapeContent) {
        const cleanedContent = escapeSpecialCharacters(textContent);
        onUpdateContent(cleanedContent);
      } else {
        onUpdateContent(textContent);
      }

      setEditorState(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: "min-h-11 sm:min-h-full",
      },
    },
  });

  const isEditorReady = useIsEditorReady();
  const editorContainerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (editor && currentChannel === "LinkedIn") {
      setEditorForRoom(editor, roomID);
    }

    return () => {
      setEditorForRoom(null, roomID);
    };
  }, [editor, currentChannel, setEditorForRoom, roomID]);

  if (!editor) return null;

  return (
    <div
      className="my-4"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor} shouldHideWithLink={true}>
        <ToolbarActions
          editor={editor}
          features={{
            unicodeFormatting: true,
          }}
        />
      </CustomFloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}

      {isEditorReady ? (
        <div className="relative flex w-full">
          <div
            className="relative flex w-full items-start"
            ref={editorContainerRef}
          >
            <div className="flex-1">
              <EditorContent
                editor={editor}
                className="w-full max-w-[622px] 2xl:min-w-[622px]"
              />
            </div>
          </div>
          <EditorHeightTooltip
            editor={editor}
            editorContainer={editorContainerRef.current}
          />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["LinkedIn"].label,
            }}
          />
        </div>
      ) : (
        <LinkedInEditorFallback
          initialValue={initialValue}
          size={size}
          postID={postID}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
        />
      )}
    </div>
  );
};

const LinkedInEditorWrapperNewDesign = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  size = "md",
  disableTagging,
  escapeContent,
  roomID,
  currentWorkspace,
  connectedIntegration,
}: BaseProps & {
  roomID: string | null;
  coverPhotoUrl?: string;
  captionsUrl?: string;
  currentWorkspace?: {
    id: string;
    name: string;
  } | null;
  connectedIntegration: LinkedInIntegration | null;
}) => {
  const { currentChannel } = usePostIntegrations();
  const threadCommentsContainerRef = useRef<HTMLDivElement>(null);

  if (!roomID) {
    return (
      <LinkedInEditorFallback
        postID={postID}
        initialValue={initialValue}
        placeholder={placeholder}
        onSave={onSave}
        onUpdateContent={onUpdateContent}
        size={size}
        disableTagging={disableTagging}
        escapeContent={escapeContent}
        isReadOnly={isReadOnly}
        isEditable={!isReadOnly}
      />
    );
  }

  return (
    <div className="relative flex flex-col bg-white xl:w-[644px] 2xl:w-[674px]">
      <div className="flex items-center gap-3.5">
        <img
          className="h-12 w-12 rounded-full"
          src={
            connectedIntegration?.account.profileImageUrl ??
            getDefaultProfilePicture("LinkedIn")
          }
        />
        <div className="flex flex-col" style={{ fontFamily: "system-ui" }}>
          <span className="text-base font-semibold">
            {connectedIntegration?.label || currentWorkspace?.name}
          </span>
          <span className="text-xs text-black/60">Now</span>
        </div>
      </div>
      <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
        <ClientSideSuspense fallback={null}>
          <div className="relative">
            <LinkedInEditor
              title={title}
              postID={postID}
              initialValue={initialValue}
              placeholder={placeholder}
              onSave={onSave}
              onUpdateContent={onUpdateContent}
              onImagePaste={onImagePaste}
              isReadOnly={isReadOnly}
              size={size}
              disableTagging={disableTagging}
              escapeContent={escapeContent}
              roomID={roomID}
            />
            <div ref={threadCommentsContainerRef} />
          </div>
        </ClientSideSuspense>
        {currentChannel === "LinkedIn" && (
          <ThreadCommentsPopover
            key={roomID}
            roomID={roomID}
            containerRef={threadCommentsContainerRef}
          />
        )}
      </RoomProvider>
    </div>
  );
};

export default LinkedInEditorWrapperNewDesign;
