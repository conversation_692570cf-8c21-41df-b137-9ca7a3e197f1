import assert from "assert";
import classnames from "classnames";

import { CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import colors from "~/styles/colors";
import { getFullName } from "~/utils";
import Loader from "./Loader";
import Tooltip from "./Tooltip";

type Size = "sm" | "md" | "lg";

type User = {
  firstName?: string | null;
  lastName?: string | null;
  detail?: string | null;
  profileImageUrl?: string | null;
};

type Props = {
  user: User | null;
  size?: Size;
  hiddenProfilePicture?: boolean;
  backgroundColor?: keyof typeof colors;
  fontColor?: keyof typeof colors;
  tooltipText?: string;
  showFullName?: boolean;
  showDetail?: boolean;
  isLoading?: boolean;
  indicatorTooltipText?: string;
  onXClick?: () => void;
  showCheck?: boolean;
};

const UserAvatar = ({
  user,
  size = "md",
  hiddenProfilePicture = false,
  backgroundColor = "accentPurple",
  fontColor = "white",
  tooltipText,
  showFullName = false,
  showDetail = false,
  isLoading = false,
  indicatorTooltipText,
  onXClick,
  showCheck = false,
}: Props) => {
  const sizeClassName = classnames(
    { "h-5 w-5 font-eyebrow-small": size === "sm" },
    { "h-8 w-8 font-eyebrow": size === "md" },
    { "h-20 w-20 text-4xl": size === "lg" }
  );

  if (!user?.firstName && !user?.detail) {
    return (
      <Tooltip value={tooltipText}>
        <div
          className={`${sizeClassName} bg-accent-purple rounded-full`}
          style={{ backgroundColor: colors[backgroundColor] }}
        />
      </Tooltip>
    );
  }

  const { firstName, lastName, detail, profileImageUrl } = user;

  const first = firstName || detail;

  assert(first);

  let initials = first.substring(0, 2);
  if (lastName) {
    initials = `${first.substring(0, 1)}${lastName.substring(0, 1)}`;
  }

  let UserAvatar = (
    <span
      className={`${sizeClassName} inline-flex shrink-0 items-center justify-center rounded-full`}
      style={{
        backgroundColor: colors[backgroundColor],
        color: colors[fontColor],
      }}
    >
      <div className="leading-none font-medium">{initials}</div>
    </span>
  );

  if (profileImageUrl) {
    UserAvatar = (
      <img
        className={`${sizeClassName} border-lightest-gray rounded-full border`}
        src={profileImageUrl}
        alt={`${firstName}'s UserAvatar`}
      />
    );
  }

  if (isLoading) {
    return <Loader size="md" />;
  }

  if (showFullName) {
    return (
      <div className="group flex items-center gap-2">
        <div className="relative">
          {!hiddenProfilePicture && (
            <Tooltip value={tooltipText}>
              <div className={sizeClassName}>{UserAvatar}</div>
            </Tooltip>
          )}
          {indicatorTooltipText && (
            <div className="bg-slate absolute -top-0.5 -right-0.5 rounded-full">
              <div className="text-secondary flex h-3 w-3 items-center justify-center pt-1">
                *
              </div>
            </div>
          )}
          {!!onXClick && (
            <button
              onClick={onXClick}
              className="text-danger hover:bg-danger-light absolute top-0 right-0 translate-x-1 -translate-y-1 rounded-full bg-white p-0.5 opacity-0 shadow-sm transition-all group-hover:opacity-100"
            >
              <XMarkIcon className="h-3 w-3" />
            </button>
          )}
          {showCheck && (
            <div className="bg-basic text-basic-light absolute -top-0.5 -right-0.5 rounded-full p-0.5">
              <CheckIcon className="h-2.5 w-2.5" strokeWidth={4} />
            </div>
          )}
        </div>
        {showDetail ? (
          <div className="font-body-sm flex flex-col items-start gap-0 whitespace-nowrap">
            <div className="leading-4 font-medium">{getFullName(user)}</div>
            <div className="text-medium-dark-gray">{user.detail}</div>
          </div>
        ) : (
          getFullName(user)
        )}
      </div>
    );
  } else {
    return (
      <div className="group relative flex items-center gap-2">
        {!hiddenProfilePicture && (
          <Tooltip value={tooltipText}>{UserAvatar}</Tooltip>
        )}
        {indicatorTooltipText && (
          <div className="bg-slate absolute -top-0.5 -right-0.5 rounded-full">
            <div className="text-secondary flex h-3 w-3 items-center justify-center pt-1">
              *
            </div>
          </div>
        )}
        {!!onXClick && (
          <button
            onClick={onXClick}
            className="text-danger hover:bg-danger-light absolute top-0 right-0 translate-x-1 -translate-y-1 rounded-full bg-white p-0.5 opacity-0 shadow-sm transition-all group-hover:opacity-100"
          >
            <XMarkIcon className="h-3 w-3" />
          </button>
        )}
        {showCheck && (
          <div className="bg-basic text-basic-light absolute -top-0.5 -right-0.5 rounded-full p-0.5">
            <CheckIcon className="h-2.5 w-2.5" strokeWidth={4} />
          </div>
        )}
      </div>
    );
  }
};

export default UserAvatar;
