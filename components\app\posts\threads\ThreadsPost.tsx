import { useEffect, useState } from "react";

import { v4 as uuidv4 } from "uuid";

import { ArrowDownIcon, ArrowUpIcon } from "@heroicons/react/24/outline";
import { Channel, ScheduledPostStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import { arrayMoveImmutable } from "array-move";
import assert from "assert";
import classNames from "classnames";
import { toast } from "sonner";
import { Tooltip } from "~/components";
import ThreadsEditor from "~/components/app/tiptap/ThreadsEditor";
import Button from "~/components/ui/Button";
import { MAX_VIDEO_UPLOAD_SIZE_MB, ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import { useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  findAsync,
  getImageProperties,
  getVideoProperties,
  handleFilesUpload,
  handleTrpcError,
  openIntercomChat,
  trpc,
} from "~/utils";
import { EditImageModalProperties } from "~/utils/EditImageModal";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import compressImage from "~/utils/compressImage";
import { getDefaultAccount } from "~/utils/getDefaultAccount";
import getFileProperties from "~/utils/getFileProperties";
import Assets, { UploadingAsset } from "../../files/Assets";
import EditorActionsBar from "../EditorActionsBar";
import PostPerformance from "../PostPerformance";
import PostPreviewContainer from "../PostPreviewContainer";
import ThreadsPostPreview from "./ThreadsPostPreview";

type ScheduledPost = {
  status: ScheduledPostStatus;
  channel: Channel;
  error: string | null;
  publishAt: Date;
  integrationAccountName: string;
};

export type ThreadsContent = {
  id: string;
  editorState: JSONContent | null;
  copy: string;
  threadPosition: number;
  uploading?: boolean;
};

export type ThreadsIntegration = {
  id: string;
  name: string | null;
  username: string;
  profileImageUrl: string;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    channels: Channel[];
    threadsAssets: {
      [id: string]: AssetType[];
    };
    threadsContent: ThreadsContent[];
    scheduledPosts: ScheduledPost[];
    threadsIntegration: ThreadsIntegration | null;
    threadsLink: string | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const ThreadsPost = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const trpcUtils = trpc.useUtils();

  const [threadDraggedOver, setThreadDraggedOver] = useState<{
    [id: string]: boolean;
  }>({});
  const [integrationID, setIntegrationID] = useState<string | null>(
    post.threadsIntegration?.id || null
  );
  const [threadsContent, setThreadsContent] = useState<ThreadsContent[]>(
    post.threadsContent || [
      {
        id: uuidv4(),
        editorState: null,
        copy: "",
        threadPosition: 0,
      },
    ]
  );
  const [threadsAssets, setThreadsAssets] = useState<{
    [id: string]: AssetType[];
  }>(post.threadsAssets);
  const [uploadingAssets, setUploadingAssets] = useState<{
    [id: string]: UploadingAsset[];
  }>({});
  const [threadIsFocused, setThreadIsFocused] = useState<{
    [id: string]: boolean;
  }>({});
  const [uppy, setUppy] = useState<Uppy | null>(null);
  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);

  useEffect(() => {
    const initialMaybeCompressAssets = async () => {
      await maybeCompressAssets(Object.values(threadsAssets).flat());
    };
    initialMaybeCompressAssets();
  }, [post.threadsAssets]);

  const createAssetsMutation = trpc.threadsContent.createAssets.useMutation();
  const deleteAssetMutation = trpc.threadsContent.deleteAsset.useMutation();
  const updateAssetPositionMutation =
    trpc.threadsContent.updateAssetPositions.useMutation();
  const updateAssetMutation = trpc.threadsContent.updateAsset.useMutation();

  const onSortEnd = (threadID: string, oldIndex: number, newIndex: number) => {
    setThreadsAssets((threadsAssets) => {
      const newThreadsAssets = arrayMoveImmutable(
        threadsAssets[threadID],
        oldIndex,
        newIndex
      );

      updateAssetPositionMutation.mutate({
        assets: newThreadsAssets.map((asset, index) => ({
          id: asset.id,
          position: index,
        })),
      });

      const updatedThreadsAssets = {
        ...threadsAssets,
        [threadID]: newThreadsAssets,
      };

      return updatedThreadsAssets;
    });
  };

  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const compressVideoMutation = trpc.video.compress.useMutation();
  const maybeCompressAssets = async (
    assets: {
      path: string;
      sizeBytes: number;
      sizeMB: number;
      mimetype: string;
      signedUrl: string;
      duration?: number | null;
      width?: number | null;
      height?: number | null;
    }[]
  ) => {
    const videoPromises = assets.map(async (asset) => {
      if (!asset || !asset.mimetype.startsWith("video/")) {
        return Promise.resolve(null);
      } else {
        let duration = asset.duration;
        let width = asset.width;
        let height = asset.height;
        if (width && height && duration) {
          return asset as typeof asset & {
            duration: number;
            width: number;
            height: number;
          };
        } else {
          const properties = await getVideoProperties(asset.signedUrl);
          return {
            ...asset,
            duration: properties.duration,
            width: properties.width,
            height: properties.height,
          };
        }
      }
    });
    const videosWithProperties = await Promise.all(videoPromises);

    const videoToCompress = await findAsync(
      videosWithProperties,
      async (asset) => {
        if (!asset || !asset.mimetype.startsWith("video/")) {
          return false;
        }

        const { width, duration } = asset;

        const videoBitrate = (asset.sizeBytes * 8) / duration / 1000 / 1000;

        return asset.sizeMB > 125 || width > 1920 || videoBitrate > 25;
      }
    );

    if (videoToCompress != null) {
      const { width, height } = videoToCompress;

      compressVideoMutation.mutate(
        {
          video: {
            path: videoToCompress.path,
            resolution: {
              width,
              height,
            },
            duration: videoToCompress.duration,
            sizeBytes: videoToCompress.sizeBytes,
          },
          channel: "Threads",
        },
        {
          onError: (error) => {
            handleTrpcError({ title: "Error Compressing Video", error });
          },
          onSuccess: (data) => {
            if (data.jobID != null) {
              toast.info("Optimizing Attachment", {
                description:
                  "We're automatically optimizing your attachment (files >125MB and width > 1920px are not accepted on Threads).",
              });
            }
          },
        }
      );
    }
  };

  const updatePostMutation = trpc.post.update.useMutation();

  const threadsContentQuery = trpc.threadsContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const threadsContentData = threadsContentQuery.data;
  useEffect(() => {
    const setCrossPostData = () => {
      if (threadsContentData?.threadsContent != null) {
        setThreadsContent(threadsContentData.threadsContent);
        setThreadsAssets(threadsContentData.threadsAssets);
        clearIsSyncing();
      }
    };
    setCrossPostData();
  }, [threadsContentData]);

  const upsertThreadsContentMutation = trpc.threadsContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
  });
  const updateThreadPositionsMutation =
    trpc.threadsContent.updatePositions.useMutation();
  const deleteThreadMutation = trpc.threadsContent.delete.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Failed to Delete Thread Content", error });
    },
  });

  const connectedAccountsQuery =
    trpc.integration.threads.getWorkspaceAccounts.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });
  const integrationsQuery = trpc.post.getIntegrations.useQuery({
    id: post.id,
  });
  const postAccounts = integrationsQuery.data?.accounts ?? [];

  const connectedAccountsData = connectedAccountsQuery.data;
  useEffect(() => {
    const setInitialIntegration = () => {
      if (connectedAccountsData != null) {
        if (integrationID == null && connectedAccountsData.accounts.length) {
          const defaultIntegration = getDefaultAccount({
            mainAccount: postAccounts[0],
            accounts: connectedAccountsData.accounts,
          });
          setIntegrationID(defaultIntegration.id);
          updatePostMutation.mutate(
            {
              id: post.id,
              threadsIntegrationID: defaultIntegration.id,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getIntegrations.refetch({
                  id: post.id,
                });
              },
            }
          );
        }
      }
    };
    setInitialIntegration();
  }, [connectedAccountsData]);

  const workspaceAccounts =
    connectedAccountsQuery.data?.accounts ||
    (post.threadsIntegration ? [post.threadsIntegration] : []);
  const threadsAccount = workspaceAccounts.find(
    (option) => option.id === integrationID
  );

  useEffect(() => {
    if (threadsAccount) {
      updatePostMutation.mutate(
        {
          id: post.id,
          threadsIntegrationID: threadsAccount.id,
        },
        {
          onSuccess: () => {
            trpcUtils.post.getIntegrations.refetch({
              id: post.id,
            });
          },
        }
      );
    }
  }, [threadsAccount]);

  // https://developers.facebook.com/docs/threads/overview#limitations
  const getValidFiles = (assets: AssetType[]) => async (files: File[]) => {
    const remainingFiles = files.slice(0, 20 - assets.length);

    if (remainingFiles.length < files.length) {
      toast.error("Too many files", {
        description: `You can upload up to a total of 20 images and videos`,
      });
    }

    const validFiles: File[] = [];
    for await (const file of files) {
      const fileType = file.type;
      const fileSizeMB = file.size / 1024 / 1024;

      if (["image/jpeg", "image/png"].includes(fileType)) {
        const { width, height } = await getImageProperties(file);

        if (fileSizeMB > 8) {
          const compressedFile = await compressImage(file, {
            maxSizeMB: 7,
            type: fileType as "image/jpeg" | "image/png",
          });
          validFiles.push(compressedFile);
        } else if (width / height > 10) {
          toast.error("Image is Too Wide", {
            description:
              "Threads requires images to have a maximum aspect ratio of 10:1",
          });
        } else {
          validFiles.push(file);
        }
      } else {
        // https://developers.facebook.com/docs/threads/overview#video-specifications
        const { width, height, duration } = await getVideoProperties(file);

        if (duration > 500) {
          toast.error("Video is Too Long", {
            description: "Threads requries videos to be a maximum of 5 minutes",
          });
          continue;
        } else if (width / height < 0.01 / 1) {
          toast.error("Video is Too Wide", {
            description:
              "Threads requires videos to have a minimum aspect ratio of 0.01:1 (for vertical videos) and 10:1 (for horizontal videos)",
          });
          continue;
        } else if (width / height > 10 / 1) {
          toast.error("Video is Too Tall", {
            description:
              "Threads requires videos to have a minimum aspect ratio of 0.01:1 (for vertical videos) and 10:1 (for horizontal videos)",
          });
          continue;
        } else if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
          toast.error("Video is too large", {
            description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
            action: {
              label: "Contact Support",
              onClick: () => {
                openIntercomChat(
                  `I need help compressing a ${fileSizeMB.toFixed(1)} MB video.`
                );
              },
            },
          });
          continue;
        }

        validFiles.push(file);
      }
    }

    return validFiles;
  };

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `threads-${post.id}`,
        restrictions: { maxNumberOfFiles: 20 },
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", async (file) => {
        assert(currentWorkspace);

        const contentID = file.meta.contentID as string;
        assert(contentID);

        setUploadingAssets((uploadingAssets) => ({
          ...uploadingAssets,
          [contentID]: [
            ...(uploadingAssets[contentID] || []),
            {
              name: file.name!,
              size: file.size!,
              type: file.type,
              uploadProgress: 0,
              updateAssetID: file.meta.updateAssetID as string | undefined,
            },
          ],
        }));

        const objectName = `${currentWorkspace.id}/${post.id}/threads/${contentID}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        const contentID = file.meta.contentID as string;

        setUploadingAssets((uploadingAssets) => {
          const contentAssets = uploadingAssets[contentID] || [];
          const uploadingAssetIndex = contentAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          if (uploadingAssetIndex !== -1) {
            const newContentAssets = [...contentAssets];
            newContentAssets[uploadingAssetIndex] = {
              ...newContentAssets[uploadingAssetIndex],
              uploadProgress,
            };
            return { ...uploadingAssets, [contentID]: newContentAssets };
          }
          return uploadingAssets;
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (uppyData) => {
        const contentID = uppyData.successful![0].meta.contentID as string;

        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["Threads"].slug
        }/${contentID}`;
        const fileNames = uppyData.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          uppyData.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  altText: null,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              if (uppyData.successful![0].meta.updateAssetID) {
                const updateAssetID = uppyData.successful![0].meta
                  .updateAssetID as string;
                const newAsset = newAssetsWithProperties[0];

                await updateAssetMutation.mutateAsync({
                  threadAssetID: updateAssetID,
                  newAsset: {
                    id: newAsset.id,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                });

                setThreadsAssets((prevAssets) => ({
                  ...prevAssets,
                  [contentID]: prevAssets[contentID].map((asset) =>
                    asset.id === updateAssetID
                      ? {
                          id: asset.id,
                          name: newAsset.name,
                          path: newAsset.path,
                          sizeBytes: newAsset.sizeBytes,
                          eTag: newAsset.eTag,
                          md5Hash: newAsset.md5Hash,
                          mimetype: newAsset.mimetype,
                          width: newAsset.width,
                          height: newAsset.height,
                          metadata: newAsset.metadata,
                          url: newAsset.signedUrl,
                          sizeMB: newAsset.sizeMB,
                          sizeKB: newAsset.sizeKB,
                          createdAt: newAsset.createdAt,
                          updatedAt: newAsset.updatedAt,
                          duration: newAsset.duration,
                          signedUrl: newAsset.signedUrl,
                          urlExpiresAt: newAsset.urlExpiresAt,
                        }
                      : asset
                  ),
                }));

                setUploadingAssets((prevUploadingAssets) => ({
                  ...prevUploadingAssets,
                  [contentID]: [],
                }));
              } else {
                const nextPosition = assets.length;

                createAssetsMutation.mutate(
                  {
                    id: contentID,
                    assets: newAssetsWithProperties.map((asset, index) => ({
                      id: asset.id,
                      assetID: asset.assetID,
                      name: asset.name,
                      path: asset.path,
                      size: asset.sizeBytes,
                      eTag: asset.eTag,
                      md5Hash: asset.md5Hash,
                      mimetype: asset.mimetype,
                      width: asset.width,
                      height: asset.height,
                      duration: asset.duration,
                      url: asset.signedUrl,
                      urlExpiresAt: asset.urlExpiresAt,
                      position: nextPosition + index,
                    })),
                  },
                  {
                    onSuccess: async () => {
                      await maybeCompressAssets(newAssetsWithProperties);
                    },
                    onError: (error) => {
                      handleTrpcError({ title: "Error Creating Asset", error });
                    },
                  }
                );

                setUploadingAssets((prevUploadingAssets) => {
                  // Find any uploading assets by name that have finished uploading
                  // and remove them from the uploading assets state
                  const uploadingAssets = Object.entries(
                    prevUploadingAssets
                  ).map(([contentID, assets]) => {
                    const uploadingAssets = assets.filter((asset) => {
                      return !newAssetsWithProperties.some(
                        (uploadedAsset) => uploadedAsset.name === asset.name
                      );
                    });
                    return [contentID, uploadingAssets];
                  });
                  return Object.fromEntries(uploadingAssets);
                });
                setThreadsAssets((assets) => ({
                  ...assets,
                  [contentID]: [
                    ...(assets[contentID] || []),
                    ...newAssetsWithProperties,
                  ],
                }));
              }
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, threadsAssets.length, currentWorkspace]);

  const updateThreadsContent = (
    contentID: string,
    data: {
      editorState?: JSONContent | null;
      copy?: string;
    }
  ) => {
    setThreadsContent((prevThreadsContent) => {
      const index = prevThreadsContent.findIndex(
        (thread) => thread.id === contentID
      );
      if (index < 0) {
        return prevThreadsContent;
      }

      const newThreadsContent = [...prevThreadsContent];
      newThreadsContent[index] = {
        ...newThreadsContent[index],
        ...data,
      };

      if (data.editorState) {
        const thread = newThreadsContent[index];
        upsertThreadsContentMutation.mutate({
          id: thread.id,
          copy: thread.copy,
          editorState: thread.editorState,
          postID: post.id,
          threadPosition: thread.threadPosition,
        });
      }

      return newThreadsContent;
    });
  };

  return (
    <div>
      <PostPerformance channel="Threads" url={post.threadsLink} />
      <PostPreviewContainer
        channel="Threads"
        post={post}
        connectedAccount={
          threadsAccount && {
            integrationID: threadsAccount.id,
            name: threadsAccount.username,
          }
        }
        accountOptions={workspaceAccounts.map((account) => ({
          integrationID: account.id,
          name: `@${account.username}`,
        }))}
        setConnectedAccountID={setIntegrationID}
        Preview={
          <ThreadsPostPreview
            threadsContent={threadsContent.map((thread) => ({
              id: thread.id,
              copy: thread.copy,
              assets: threadsAssets[thread.id] || [],
            }))}
            connectedAccount={threadsAccount}
          />
        }
        isReadOnly={isReadOnly}
      />
      {threadsContent.map((thread, index) => {
        const assets = threadsAssets[thread.id] || [];
        const threadUploadingAssets = uploadingAssets[thread.id] || [];

        const isDraggedOver = threadDraggedOver[thread.id] || false;

        const addThread = ({
          threadID,
          copy,
          nextThreadCopy,
          nextThreadEditorState,
        }: {
          threadID: string;
          copy?: string;
          nextThreadCopy?: string;
          nextThreadEditorState?: JSONContent | null;
        }) => {
          const index = threadsContent.findIndex((t) => t.id === threadID);

          const newThread = {
            id: uuidv4(),
            editorState: nextThreadEditorState || null,
            copy: nextThreadCopy || "",
            threadPosition: index,
          };

          const withAddedThread = [
            ...threadsContent.slice(0, index + 1),
            newThread,
            ...threadsContent.slice(index + 1, threadsContent.length),
          ];

          if (copy) {
            upsertThreadsContentMutation.mutate({
              id: threadID,
              copy: copy,
              editorState: thread.editorState,
              postID: post.id,
              threadPosition: index,
            });

            withAddedThread[index] = {
              ...withAddedThread[index],
              copy,
            };
          }

          upsertThreadsContentMutation.mutate({
            id: newThread.id,
            copy: newThread.copy,
            editorState: newThread.editorState,
            postID: post.id,
            threadPosition: index + 1,
          });

          setThreadsContent(withAddedThread);
        };

        const deleteThread = () => {
          setThreadsContent((prevThreadsContent) => {
            const index = prevThreadsContent.findIndex(
              (t) => t.id === thread.id
            );

            deleteThreadMutation.mutate({
              id: thread.id,
            });
            const withRemovedThread = [
              ...prevThreadsContent.slice(0, index),
              ...prevThreadsContent.slice(index + 1, prevThreadsContent.length),
            ];

            return withRemovedThread;
          });
        };

        return (
          <div
            key={thread.id}
            onDragOverCapture={() => {
              setThreadDraggedOver((prevThreadDraggedOver) => ({
                ...prevThreadDraggedOver,
                [thread.id]: true,
              }));
            }}
            onDragLeaveCapture={() => {
              setThreadDraggedOver((prevThreadDraggedOver) => ({
                ...prevThreadDraggedOver,
                [thread.id]: false,
              }));
            }}
            onDrop={() => {
              setThreadDraggedOver((prevThreadDraggedOver) => ({
                ...prevThreadDraggedOver,
                [thread.id]: false,
              }));
            }}
            className={classNames("my-2 bg-white", {
              "border-lightest-gray mb-4 overflow-clip border-b":
                index !== threadsContent.length - 1,
            })}
          >
            <div className="group">
              <ThreadsEditor
                key={thread?.id || "threads-editor"}
                postID={post.id}
                title={`Thread (${index + 1}/${threadsContent.length})`}
                placeholder="Add copy for the post here"
                initialValue={thread?.editorState}
                isReadOnly={isReadOnly}
                onFocusChange={(isFocused) => {
                  if (isFocused) {
                    setThreadIsFocused((prevThreadIsFocused) => {
                      Object.keys(prevThreadIsFocused).forEach((key) => {
                        prevThreadIsFocused[key] = false;
                      });
                      prevThreadIsFocused[thread.id] = true;
                      return { ...prevThreadIsFocused };
                    });
                  }
                }}
                onSave={(editorState) => {
                  updateThreadsContent(thread.id, {
                    editorState,
                  });
                }}
                onImagePaste={(event) => {
                  if (uppy) {
                    handleFilesUpload({
                      items: event.clipboardData.items,
                      uppy,
                      meta: {
                        contentID: thread.id,
                      },
                      getValidFiles: getValidFiles(assets),
                    });
                  }
                }}
                onUpdateContent={(copy) => {
                  updateThreadsContent(thread.id, {
                    copy,
                  });
                }}
              />
              <EditorActionsBar
                post={post}
                channel="Threads"
                visible={
                  Object.values(threadIsFocused).every((v) => !v)
                    ? index === 0
                    : threadIsFocused[thread.id]
                }
                isReadOnly={isReadOnly}
                addNewButton={{
                  label: "Add Thread",
                  onClick: () => {
                    addThread({ threadID: thread.id });
                  },
                }}
                characterCount={{
                  current: thread.copy.length,
                  max: 500,
                }}
                uploadAssetButton={{
                  acceptedMimetypes: [
                    "image/jpeg",
                    "image/png",
                    "video/mp4",
                    "video/quicktime",
                  ],
                  acceptMultiple: true,
                  onUploadFiles: async (files) => {
                    if (uppy) {
                      await handleFilesUpload({
                        files,
                        uppy,
                        meta: {
                          contentID: thread.id,
                        },
                        getValidFiles: getValidFiles(assets),
                      });
                    }
                  },
                  isLoading: threadUploadingAssets.length > 0,
                }}
                deleteButton={{
                  onClick: () => {
                    deleteThread();
                  },
                  isHidden: threadsContent.length === 1,
                }}
                additionalOptions={
                  threadsContent.length > 1 ? (
                    <div className="flex h-5 items-center gap-2">
                      {index > 0 && (
                        <Tooltip value="Move Up">
                          <Button
                            size="sm"
                            onClick={() => {
                              const newThreadsContent = arrayMoveImmutable(
                                threadsContent,
                                index,
                                index - 1
                              ).map((thread, index) => ({
                                ...thread,
                                threadPosition: index,
                              }));

                              updateThreadPositionsMutation.mutate({
                                threadsContent: newThreadsContent.map(
                                  (thread) => ({
                                    id: thread.id,
                                    threadPosition: thread.threadPosition,
                                  })
                                ),
                              });

                              setThreadsContent(newThreadsContent);
                            }}
                            variant="ghost"
                          >
                            <ArrowUpIcon className="h-4 w-4" />
                          </Button>
                        </Tooltip>
                      )}
                      {index < threadsContent.length - 1 && (
                        <Tooltip value="Move Down">
                          <Button
                            size="sm"
                            onClick={() => {
                              const newThreadsContent = arrayMoveImmutable(
                                threadsContent,
                                index,
                                index + 1
                              ).map((thread, index) => ({
                                ...thread,
                                threadPosition: index,
                              }));

                              updateThreadPositionsMutation.mutate({
                                threadsContent: newThreadsContent.map(
                                  (thread) => ({
                                    id: thread.id,
                                    threadPosition: thread.threadPosition,
                                  })
                                ),
                              });

                              setThreadsContent(newThreadsContent);
                            }}
                            variant="ghost"
                          >
                            <ArrowDownIcon className="h-4 w-4" />
                          </Button>
                        </Tooltip>
                      )}
                    </div>
                  ) : null
                }
              />
            </div>
            <div
              className={classNames("pt-1", {
                "mt-2 pb-4": assets.length > 0 || isDraggedOver,
              })}
            >
              <Assets
                assets={assets}
                uploadingAssets={threadUploadingAssets}
                onDeleteClick={(asset) => {
                  setThreadsAssets((prevAssets) => ({
                    ...prevAssets,
                    [thread.id]: prevAssets[thread.id].filter(
                      (currentAsset) => currentAsset.id !== asset.id
                    ),
                  }));
                  deleteAssetMutation.mutate({
                    threadAssetID: asset.id,
                  });
                }}
                editImageModalProperties={editImageModalProperties}
                onEditClick={(asset) => {
                  if (asset.metadata.mimetype.startsWith("image/")) {
                    setEditImageModalProperties({
                      title: "Edit Image",
                      channel: "Threads",
                      maxSizeMB: 8,
                      isOpen: true,
                      asset,
                    });
                  }
                }}
                onEditImageModalClose={() => setEditImageModalProperties(null)}
                onUpload={async (file, updateAssetID) => {
                  if (uppy) {
                    if (updateAssetID) {
                      uppy.addFile({
                        name: file.name,
                        type: file.type,
                        data: file,
                        meta: {
                          contentID: thread.id,
                          updateAssetID,
                        },
                      });
                    } else {
                      await handleFilesUpload({
                        files: [file],
                        uppy,
                        meta: {
                          contentID: thread.id,
                        },
                        getValidFiles: getValidFiles(assets),
                      });
                    }
                  }
                }}
                showNewEditButton={true}
                refetchAssets={() => {}}
                isReadOnly={isReadOnly}
                onDrop={async (files) => {
                  setThreadDraggedOver((prevThreadDraggedOver) => ({
                    ...prevThreadDraggedOver,
                    [thread.id]: false,
                  }));
                  if (uppy) {
                    await handleFilesUpload({
                      files,
                      uppy,
                      meta: {
                        contentID: thread.id,
                      },
                      getValidFiles: getValidFiles(assets),
                    });
                  }
                }}
                showDropzone={isDraggedOver}
                acceptedFileTypes={{
                  "image/jpg": [".jpg", ".jpeg"],
                  "image/png": [".png"],
                  "image/gif": [".gif"],
                  "image/webp": [".webp"],
                  "video/mp4": [".mp4"],
                  "video/quicktime": [".mov"],
                }}
                onSortEnd={(oldIndex, newIndex) => {
                  onSortEnd(thread.id, oldIndex, newIndex);
                }}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ThreadsPost;
