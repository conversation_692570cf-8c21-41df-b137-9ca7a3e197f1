import { Mark } from "@tiptap/core";

// Creating a custom extension to convert the Liveblocks comment span into a custom mark so we can add custom styles to it
const CustomCommentThreadMark = Mark.create({
  name: "liveblocksCommentMark",

  addAttributes() {
    return {
      threadId: { default: null },
      orphan: { default: null },
    };
  },

  parseHTML() {
    return [{ tag: "mark[data-lb-thread-id]" }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "mark",
      {
        ...HTMLAttributes,
        "data-lb-thread-id": HTMLAttributes.threadId,
        class: "lb-root lb-tiptap-thread-mark",
      },
      0,
    ];
  },

  inclusive() {
    return false;
  },
});

export default CustomCommentThreadMark;
