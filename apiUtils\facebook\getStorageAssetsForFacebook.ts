import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForFacebook = async (post: {
  id: string;
  facebookContent: {
    id: string;
  } | null;
  workspaceID: string;
}): Promise<{
  assets: AssetType[];
  coverPhoto: AssetType | null;
}> => {
  const { facebookContent } = post;
  if (!facebookContent) {
    return { assets: [], coverPhoto: null };
  }

  const assetsMap = await getAssetsForFilePaths([
    {
      id: facebookContent.id,
      path: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Facebook"].slug}/${facebookContent.id}`,
    },
    {
      id: "cover_photo",
      path: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Facebook"].slug}/${facebookContent.id}/cover_photo`,
    },
  ]);

  let assets = assetsMap[facebookContent.id];
  let coverPhoto = assetsMap["cover_photo"][0];

  return {
    assets,
    coverPhoto,
  };
};

export default getStorageAssetsForFacebook;
