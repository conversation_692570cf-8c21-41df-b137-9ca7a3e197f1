// Deletes files recursively from Supabase storage path
// Using the Supabase Storage API: https://supabase.com/docs/reference/javascript/storage-from-remove

import supabaseAdminClient from "~/clients/supabaseAdminClient";

// Returns a promise that resolves to the number of files copied
export const deleteStorageFiles = async (basePath: string) => {
  const { data: files, error: filesError } = await supabaseAdminClient.storage
    .from("assets")
    .list(basePath);

  if (filesError) {
    throw filesError;
  }

  let count = 0;

  for (const file of files) {
    // If the file is a directory, recursively copy the files in that directory
    if (file.metadata == null) {
      const subdirectorySourceBasePath = `${basePath}/${file.name}`;

      const subdirectoryCount = await deleteStorageFiles(
        subdirectorySourceBasePath
      );

      count += subdirectoryCount;
    }
  }

  // Delete the all of the files now that the directories are empty
  const { data: deleteData, error: deleteError } =
    await supabaseAdminClient.storage
      .from("assets")
      .remove(files.map((file) => `${basePath}/${file.name}`));

  count += files.length;

  return count;
};
