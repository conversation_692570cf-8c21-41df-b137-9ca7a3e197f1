import axios from "axios";
import ffmpeg from "fluent-ffmpeg";
import fs from "fs";
import path from "path";
import stream from "stream";
import { promisify } from "util";

const compressVideo = async (video: { url: string; mimetype: string }) => {
  const startTime = Date.now();
  console.log("Start compressing video at:", new Date(startTime).toISOString());
  console.log("Compressing Video", Date.now() / 1000);

  const ffmpegPath = require("@ffmpeg-installer/ffmpeg").path;
  ffmpeg.setFfmpegPath(ffmpegPath);

  const videoPath = path.join("/tmp", "video.mov");
  const outputPath = videoPath
    .replace(".mp4", "_compressed.mp4")
    .replace(".mov", "_compressed.mov");

  const pipeline = promisify(stream.pipeline);

  await pipeline(
    (await axios.get(video.url, { responseType: "stream" })).data,
    fs.createWriteStream(videoPath)
  );

  await new Promise((resolve, reject) => {
    ffmpeg()
      .input(videoPath)
      .inputFormat(video.mimetype.split("/")[1]) // Assuming mimetype is in the format 'video/format'
      .withOptions([
        "-v verbose",
        // "-vcodec libx264",
        // "-acodec aac",
        // "-fs 125M",
        "-preset veryfast",
      ])
      .output(outputPath)
      .on("start", (commandLine) => {
        console.log("Video downloading and compression started", commandLine);
        console.log("Started at:", new Date());
      })
      .on("end", async () => {
        console.log(
          "Video downloading and compression finished at:",
          new Date()
        );

        resolve(() => {});
      })
      .on("progress", (progress) => {
        console.log(progress);
        console.log(`Processing @ ${progress.timemark}`);
      })
      .on("error", (err, stdout, stderr) => {
        console.error("Error compressing video:", err, stdout, stderr);
        reject(err);
      })
      .run();
  });

  const compressedVideoBuffer = fs.readFileSync(outputPath);
  const compressedVideo = new Blob([compressedVideoBuffer], {
    type: video.mimetype,
  });

  fs.unlinkSync(outputPath);

  const endTime = Date.now();
  console.log(
    "Finished compressing video at:",
    new Date(endTime).toISOString()
  );
  const compressionDuration = (endTime - startTime) / 1000; // Duration in seconds
  console.log(`Compression took ${compressionDuration} seconds.`);

  return { compressedVideo, compressionDuration };
};

export default compressVideo;
