import axios from "axios";
import { StatusCodes } from "http-status-codes";
import { createErrorResponse } from "../../Response";
import catchTikTokError from "../catchTikTokError";

// Mock dependencies
jest.mock("../../Response", () => ({
  createErrorResponse: jest.fn().mockImplementation((...args) => ({
    success: false,
    errors: args[0],
    statusCode: args[1],
    error: args[2],
    isAuthError: args[3] || false,
  })),
}));

// Mock console.log to avoid cluttering test output
console.log = jest.fn();

jest.mock("axios", () => ({
  isAxiosError: jest.fn(),
}));

describe("catchTikTokError", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should handle axios error with string error message", () => {
    const axiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.BAD_REQUEST,
        data: { error: "Invalid request parameter" },
        statusText: "Bad Request",
      },
    };
    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchTikTokError(axiosError, "Default error message");

    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Invalid request parameter"],
      StatusCodes.BAD_REQUEST,
      "Invalid request parameter",
      false
    );
    expect(result.success).toBe(false);
    expect(result.errors).toEqual(["Invalid request parameter"]);
  });

  it("should handle axios error with object error message", () => {
    const errorObject = { message: "Complex error object" };
    const axiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        data: { error: errorObject },
        statusText: "Server Error",
      },
    };
    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchTikTokError(axiosError, "Default error message");

    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Complex error object"],
      StatusCodes.INTERNAL_SERVER_ERROR,
      errorObject,
      false
    );
    expect(result.success).toBe(false);
    expect(result.errors).toEqual(["Complex error object"]);
  });

  it("should handle axios unauthorized error and set isAuthError flag", () => {
    // Arrange
    const axiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.UNAUTHORIZED,
        data: { error: "Authentication failed" },
        statusText: "Unauthorized",
      },
    };
    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    // Act
    const result = catchTikTokError(axiosError, "Default error message");

    // Assert
    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Authentication failed"],
      StatusCodes.UNAUTHORIZED,
      "Authentication failed",
      true
    );
    expect(result.success).toBe(false);
    expect(result.isAuthError).toBe(true);
  });

  it("should use statusText when error data is not available", () => {
    // Arrange
    const axiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.BAD_GATEWAY,
        data: {},
        statusText: "Bad Gateway",
      },
    };
    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    // Act
    const result = catchTikTokError(axiosError, "Default error message");

    // Assert
    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Bad Gateway"],
      StatusCodes.BAD_GATEWAY,
      "Bad Gateway",
      false
    );
  });

  it("should use default status code when response status is missing", () => {
    // Arrange
    const axiosError = {
      isAxiosError: true,
      response: {
        data: { error: "Some error" },
        statusText: "Error",
      },
    };
    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    // Act
    const result = catchTikTokError(axiosError, "Default error message");

    // Assert
    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Some error"],
      StatusCodes.UNPROCESSABLE_ENTITY,
      "Some error",
      false
    );
  });

  it("should handle non-axios errors with default message", () => {
    // Arrange
    const error = new Error("Some random error");
    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(false);

    // Act
    const result = catchTikTokError(error, "Default error message");

    // Assert
    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Default error message"],
      StatusCodes.UNPROCESSABLE_ENTITY
    );
    expect(result.success).toBe(false);
    expect(result.errors).toEqual(["Default error message"]);
  });
});
