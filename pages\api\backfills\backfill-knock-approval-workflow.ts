import { StatusCodes } from "http-status-codes";
import chunk from "lodash/chunk";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";
import knock from "~/clients/Knock";
import { sleep } from "~/utils";

// localhost:3000/api/backfills/backfill-knock-approval-workflow

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const users = await db.user.findMany();

  const updatedUsers: string[] = [];
  const failedUsers: string[] = [];

  // Setting user preferences is tier 3 e.g. 60 requests per second
  const chunks = chunk(users, 60);
  let i = 1;

  for await (const chunk of chunks) {
    console.log(`Processing chunk ${i} of ${chunks.length}`);

    const promises = chunk.map(async (user) => {
      try {
        // Get current preferences
        const preferences = await knock.users.getPreferences(user.id);

        console.log(preferences);
        // Only update if the new key doesn't exist
        if (!preferences.workflows?.["requested-approval"]) {
          const response = await knock.users.setPreferences(user.id, {
            ...preferences,
            workflows: {
              ...preferences.workflows,
              "requested-approval": {
                channel_types: {
                  chat: false,
                  email: true,
                  in_app_feed: true,
                },
              },
            },
          });

          updatedUsers.push(user.id);
        }
      } catch (error) {
        failedUsers.push(user.id);
        console.log(`Failed to update preferences for user ${user.id}:`, error);
      }
    });

    await Promise.all(promises);
    // Add an extra second just in case to avoid rate limiting
    await sleep(1000);
    i++;
  }

  res.status(StatusCodes.OK).send({
    updatedUsers: updatedUsers,
    failedUsers,
    totalUpdated: updatedUsers.length,
    totalFailed: failedUsers.length,
    totalUsers: users.length,
  });
};

export default handler;
