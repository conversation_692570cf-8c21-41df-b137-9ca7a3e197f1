import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import {
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { cn } from "~/utils";

const alertVariants = cva(
  "relative bg-lightest-gray-30 font-body-sm w-full rounded-lg px-4 py-3 [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-[18px] [&>svg]:text-foreground [&>svg~*]:pl-4",
  {
    variants: {
      variant: {
        default: "text-black",
        danger: "text-danger",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant = "default", ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className, "px-6 py-4")}
    {...props}
  />
));
Alert.displayName = "Alert";

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5 ref={ref} className={cn("font-medium", className)} {...props} />
));
AlertTitle.displayName = "AlertTitle";

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("pt-1 [&_p]:leading-relaxed", className)}
    {...props}
  />
));
AlertDescription.displayName = "AlertDescription";

const Icons = {
  default: InformationCircleIcon,
  danger: ExclamationTriangleIcon,
  warning: ExclamationCircleIcon,
  info: InformationCircleIcon,
};

type AlertIconProps = {
  variant?: keyof typeof Icons;
  className?: string;
};

const AlertIcon = ({ variant = "default", className }: AlertIconProps) => {
  const Icon = Icons[variant];
  return (
    <Icon
      className={cn(
        "h-4 w-4",
        {
          "text-danger": variant === "danger",
          "text-warning": variant === "warning",
          "text-secondary": variant === "info",
        },
        className
      )}
      aria-hidden="true"
    />
  );
};
AlertIcon.displayName = "AlertIcon";

const Root = Alert;
const Title = AlertTitle;
const Description = AlertDescription;
const Icon = AlertIcon;

export default { Root, Title, Description, Icon };
