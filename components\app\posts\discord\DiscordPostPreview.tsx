import uniq from "lodash/uniq";

import { DateTime } from "luxon";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { useWorkspace } from "~/providers";
import DiscordAssetPreview from "./DiscordAssetPreview";

type DiscordWebhook = {
  id: string;
  username: string;
  profileImageUrl: string;
};

type Props = {
  discordContent: {
    copy?: string;
    assets: {
      signedUrl: string;
      metadata: {
        mimetype: string;
      };
      createdAt: Date;
    }[];
  } | null;
  connectedAccount: DiscordWebhook | null | undefined;
};

const DiscordPostPreview = ({ discordContent, connectedAccount }: Props) => {
  const { currentWorkspace } = useWorkspace();
  const nonEmpty =
    discordContent &&
    ((discordContent.copy && discordContent.copy != "") ||
      discordContent.assets.length > 0);

  const copy = discordContent?.copy;

  if (!nonEmpty) {
    return (
      <div className="font-body text-medium-dark-gray mt-1">
        Preview will show when post content is added below
      </div>
    );
  } else {
    return (
      <div className="font-discord flex min-h-[300px] items-center justify-center text-base">
        <div className="scrollbar-hidden h-fit max-h-[600px] w-[540px] space-y-4 overflow-auto rounded-lg bg-[#313338] py-3">
          <div className="font-smoothing flex items-start gap-2 px-4 leading-4">
            <img
              className="h-10 w-10 rounded-full"
              src={
                connectedAccount?.profileImageUrl || "/discord/default_user.png"
              }
            />
            <div className="flex flex-col items-start">
              <div className="flex items-center gap-1">
                <span className="font-bold text-[#F4F5F6]">
                  {connectedAccount?.username || currentWorkspace?.name}
                </span>
                <div className="mr-1 rounded-[3px] bg-[#5865F2] px-1 text-[10px] leading-[15px] font-semibold text-white">
                  BOT
                </div>
                <div
                  key={DateTime.now().toFormat("h:mm a")}
                  className="text-sm text-[#A2A4AA]"
                >
                  Today at {DateTime.now().toFormat("h:mm a")}
                </div>
              </div>
              <div className="leading-[22px]">
                {copy?.split("\n").map((line, index) => {
                  if (line === "") {
                    return <div key={index} className="py-2" />;
                  } else {
                    let parsedLine = line;

                    const underlineRegex = /__.+__/g;
                    const underlines = uniq(line.match(underlineRegex)) || [];

                    underlines.forEach((underline) => {
                      const text = underline.replaceAll("_", "");
                      parsedLine = parsedLine.replaceAll(
                        underline,
                        `<span className="underline">${text}</span>`
                      );
                    });

                    const tagRegex = /@everyone|@here/g;
                    const tags = uniq(line.match(tagRegex)) || [];

                    tags.forEach((tag) => {
                      parsedLine = parsedLine.replaceAll(
                        tag,
                        `<span className="bg-[#3D426B] hover:bg-[#5865F2] text-[#C9CDFB] hover:text-white transition-colors rounded-[3px] font-semibold">${tag}</span>`
                      );
                    });

                    const mentionRegex = /<@[!&]?([0-9]+)>/g;
                    const mentions = uniq(line.match(mentionRegex)) || [];
                    mentions.forEach((tag) => {
                      parsedLine = parsedLine.replaceAll(
                        tag,
                        `<span className="bg-[#3D426B] hover:bg-[#5865F2] text-[#C9CDFB] hover:text-white transition-colors rounded-[3px] font-semibold">${tag
                          .replace("<", "")
                          .replace(">", "")}</span>`
                      );
                    });

                    const channelRegex = /<#([0-9]+)>/g;
                    const channels = uniq(line.match(channelRegex)) || [];
                    channels.forEach((tag) => {
                      parsedLine = parsedLine.replaceAll(
                        tag,
                        `<span className="bg-[#3D426B] hover:bg-[#5865F2] text-[#C9CDFB] hover:text-white transition-colors rounded-[3px] font-semibold">${tag
                          .replace("<", "")
                          .replace(">", "")}</span>`
                      );
                    });

                    const timestampRegex = /<t:([0-9]+)?:?([a-zA-Z])?>/g;
                    const timestamps = uniq(line.match(timestampRegex)) || [];
                    timestamps.forEach((tag) => {
                      const match = timestampRegex.exec(tag);
                      if (match) {
                        const timestamp = match[1];
                        const format = match[2]
                          ?.replace("t", "t")
                          .replace("T", "tt")
                          .replace("D", "DDD")
                          .replace("d", "D")
                          .replace("f", "LLLL d, yyyy h:mm a")
                          .replace("F", "cccc, LLLL d, yyyy h:mm a");

                        const date = DateTime.fromMillis(
                          parseInt(timestamp) * 1000
                        );

                        if (format === "R") {
                          parsedLine = parsedLine.replaceAll(
                            tag,
                            `<span className="bg-[#485058]/50 text-[#DBDEE1] rounded-[3px] font-semibold">${date.toRelative()}</span>`
                          );
                        } else {
                          parsedLine = parsedLine.replaceAll(
                            tag,
                            `<span className="bg-[#485058]/50 text-[#DBDEE1] rounded-[3px] font-semibold">${date.toFormat(
                              format || "DDD HH:mm"
                            )}</span>`
                          );
                        }
                      }
                    });

                    return (
                      <div key={index} dir="auto">
                        <ReactMarkdown
                          className="break-words text-[#E3E4E8]"
                          linkTarget="_blank"
                          children={parsedLine}
                          remarkPlugins={[remarkGfm]}
                          rehypePlugins={[rehypeRaw]}
                          components={{
                            h1: ({ node, ...props }) => {
                              return (
                                <div
                                  className="my-2 text-[24px] font-bold text-[#FFFFFF]"
                                  {...props}
                                />
                              );
                            },
                            h2: ({ node, ...props }) => {
                              return (
                                <div
                                  className="my-2 text-[20px] font-bold text-[#FFFFFF]"
                                  {...props}
                                />
                              );
                            },
                            h3: ({ node, ...props }) => {
                              return (
                                <div
                                  className="my-2 text-[16px] font-bold text-[#FFFFFF]"
                                  {...props}
                                />
                              );
                            },
                            blockquote: ({ node, ...props }) => {
                              return (
                                <div className="flex items-center">
                                  <blockquote
                                    className="border-l-4 border-[#4E5058] pl-4"
                                    {...props}
                                  />
                                </div>
                              );
                            },
                            ol: ({ node, ...props }) => {
                              return (
                                <ol
                                  {...props}
                                  className="list-inside list-decimal"
                                />
                              );
                            },
                            ul: ({ node, ...props }) => {
                              // @ts-expect-error children is not defined on the node
                              const children = props.children[1].props.children;
                              return (
                                <ol {...props} className="list-inside">
                                  <li>- {children}</li>
                                </ol>
                              );
                            },
                            strong: ({ node, ...props }) => {
                              return <span className="font-bold" {...props} />;
                            },
                            a: ({ node, ...props }) => {
                              return (
                                <a
                                  className="text-[#00AAFC] hover:underline hover:decoration-[#00AAFC]"
                                  {...props}
                                />
                              );
                            },
                          }}
                        />
                      </div>
                    );
                  }
                })}
              </div>
              <DiscordAssetPreview assets={discordContent.assets} />
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default DiscordPostPreview;
