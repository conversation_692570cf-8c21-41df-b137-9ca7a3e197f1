import { EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import { MapPinIcon, MusicalNoteIcon } from "@heroicons/react/24/solid";
import Uppy from "@uppy/core";
import classNames from "classnames";
import uniq from "lodash/uniq";
import { useState } from "react";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import VideoPreviewContainer from "~/components/app/posts/VideoPreviewContainer";
import { useWorkspace } from "~/providers";
import { AssetType } from "~/types/Asset";
import { getDefaultProfilePicture } from "~/utils";
import { UploadingAsset } from "../../files/Assets";

type InstagramAccount = {
  id: string;
  account: {
    name: string | null;
    profileImageUrl: string;
  };
};

type Props = {
  instagramContent: {
    id: string;
    copy?: string;
    location: {
      name: string;
    } | null;
  };
  assets: AssetType[];
  coverPhotoUrl?: string;
  connectedIntegration: InstagramAccount | null;
  isReadOnly: boolean;
  uppy?: Uppy | null;
  uploadingAssets: UploadingAsset[];
  getValidFiles: (files: File[]) => Promise<File[]>;
  onDrop: (files: File[]) => void;
  onDeleteClick: (asset: AssetType) => void;
  isDraggedOver: boolean;
};

const InstagramReelsPreviewNewDesign = ({
  instagramContent,
  assets,
  coverPhotoUrl,
  connectedIntegration,
  isReadOnly,
  uppy,
  uploadingAssets,
  getValidFiles,
  onDrop,
  onDeleteClick,
  isDraggedOver,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const [showMore, setShowMore] = useState<boolean>(false);

  const accountName =
    connectedIntegration?.account.name || currentWorkspace?.name;
  const profilePictureUrl =
    connectedIntegration?.account.profileImageUrl ??
    getDefaultProfilePicture("Instagram");

  const copy = instagramContent?.copy;

  const video = assets[0];

  return (
    <VideoPreviewContainer
      video={video}
      coverPhotoUrl={coverPhotoUrl}
      getValidFiles={getValidFiles}
      acceptedFileTypes={{
        "video/mp4": [".mp4"],
        "video/quicktime": [".mov"],
      }}
      contentID={instagramContent.id}
      uploadingAssets={uploadingAssets}
      isDraggedOver={isDraggedOver}
      isReadOnly={isReadOnly}
      uppy={uppy}
      onDrop={onDrop}
      onDeleteClick={onDeleteClick}
    >
      {showMore && (
        <div
          className={classNames(
            "absolute inset-0 rounded-xl bg-black transition-all",
            {
              "bg-opacity-0": !showMore,
              "bg-opacity-40": showMore,
            }
          )}
        />
      )}
      <div className="absolute right-3 bottom-14 flex flex-col gap-6">
        <svg
          aria-label="Like"
          color="rgb(255, 255, 255)"
          fill="rgb(255, 255, 255)"
          height="24"
          role="img"
          viewBox="0 0 24 24"
          width="24"
        >
          <title>Like</title>
          <path d="M16.792 3.904A4.989 4.989 0 0 1 21.5 9.122c0 3.072-2.652 4.959-5.197 7.222-2.512 2.243-3.865 3.469-4.303 3.752-.477-.309-2.143-1.823-4.303-3.752C5.141 14.072 2.5 12.167 2.5 9.122a4.989 4.989 0 0 1 4.708-5.218 4.21 4.21 0 0 1 3.675 1.941c.84 1.175.98 1.763 1.12 1.763s.278-.588 1.11-1.766a4.17 4.17 0 0 1 3.679-1.938m0-2a6.04 6.04 0 0 0-4.797 2.127 6.052 6.052 0 0 0-4.787-2.127A6.985 6.985 0 0 0 .5 9.122c0 3.61 2.55 5.827 5.015 7.97.283.246.569.494.853.747l1.027.918a44.998 44.998 0 0 0 3.518 3.018 2 2 0 0 0 2.174 0 45.263 45.263 0 0 0 3.626-3.115l.922-.824c.293-.26.59-.519.885-.774 2.334-2.025 4.98-4.32 4.98-7.94a6.985 6.985 0 0 0-6.708-7.218Z"></path>
        </svg>
        <svg
          aria-label="Comment"
          color="rgb(255, 255, 255)"
          fill="rgb(255, 255, 255)"
          height="24"
          role="img"
          viewBox="0 0 24 24"
          width="24"
        >
          <title>Comment</title>
          <path
            d="M20.656 17.008a9.993 9.993 0 1 0-3.59 3.615L22 22Z"
            fill="none"
            stroke="currentColor"
            strokeLinejoin="round"
            strokeWidth="2"
          ></path>
        </svg>
        <svg
          aria-label="Share Post"
          color="rgb(255, 255, 255)"
          fill="rgb(255, 255, 255)"
          height="24"
          role="img"
          viewBox="0 0 24 24"
          width="24"
        >
          <title>Share Post</title>
          <line
            fill="none"
            stroke="currentColor"
            strokeLinejoin="round"
            strokeWidth="2"
            x1="22"
            x2="9.218"
            y1="3"
            y2="10.083"
          ></line>
          <polygon
            fill="none"
            points="11.698 20.334 22 3.001 2 3.001 9.218 10.084 11.698 20.334"
            stroke="currentColor"
            strokeLinejoin="round"
            strokeWidth="2"
          ></polygon>
        </svg>
        <EllipsisHorizontalIcon className="h-6 w-6" />
      </div>
      <div className="font-body-sm absolute bottom-3 left-3">
        <div className="flex flex-col gap-3">
          <div className="flex items-center gap-3">
            <img className="h-6 w-6 rounded-full" src={profilePictureUrl} />
            <div className="font-semibold">{accountName?.replace("@", "")}</div>
          </div>
          <div
            className={classNames(
              "transition-max-height mr-9 h-full cursor-pointer leading-tight duration-300",
              {
                "max-h-[18px] overflow-clip": !showMore,
                "scrollbar-hidden max-h-[300px] overflow-auto": showMore,
              }
            )}
            onClick={() => setShowMore(!showMore)}
          >
            {copy?.split("\n").map((line, index) => {
              if (line === "") {
                return <div key={index} className="py-1" />;
              } else {
                let parsedLine = line;

                const usernameRegex = /(@[A-Za-z0-9_\.]{1,28})/g;
                const usernameTags = uniq(line.match(usernameRegex) || []);

                usernameTags.forEach((usernameWithAt) => {
                  const username = usernameWithAt.replace("@", "");

                  parsedLine = parsedLine.replaceAll(
                    usernameWithAt,
                    `[${usernameWithAt}](https://instagram.com/${username})`
                  );
                });

                const hashtags = uniq(line.match(/(#[\w\d_]+)/g)) || [];

                hashtags.forEach((hashtag, index) => {
                  const tag = hashtag.replace("#", "");

                  const tagRegex = new RegExp(`${hashtag}\\b`, "g");

                  parsedLine = parsedLine.replaceAll(
                    tagRegex,
                    `[${hashtag}](https://www.instagram.com/explore/tags/${tag})`
                  );
                });

                return (
                  <div key={index} className="flex items-center" dir="auto">
                    <ReactMarkdown
                      className="mr-0 w-fit break-words"
                      linkTarget="_blank"
                      children={parsedLine}
                      components={{
                        h1: ({ node, ...props }) => {
                          return (
                            <span>
                              # <span {...props} />
                            </span>
                          );
                        },
                        h2: ({ node, ...props }) => {
                          return (
                            <span>
                              ## <span {...props} />
                            </span>
                          );
                        },
                        h3: ({ node, ...props }) => {
                          return (
                            <span>
                              ### <span {...props} />
                            </span>
                          );
                        },
                        em: "span",
                        ol: ({ node, ...props }) => {
                          return (
                            <ol
                              {...props}
                              className="list-inside list-decimal"
                            />
                          );
                        },
                        ul: ({ node, ...props }) => {
                          // @ts-expect-error children is not defined on the node
                          const children = props.children[1].props.children;
                          return (
                            <ol {...props} className="list-inside">
                              <li>- {children}</li>
                            </ol>
                          );
                        },
                        strong: ({ node, ...props }) => {
                          return (
                            <span
                              {...props}
                              className="font-medium text-white"
                            />
                          );
                        },
                        code: ({ node, ...props }) => {
                          return (
                            <span>
                              `<span {...props} />`
                            </span>
                          );
                        },
                        a: ({ node, ...props }) => {
                          return (
                            <a
                              onClick={(e) => {
                                e.stopPropagation();
                              }}
                              className="font-semibold text-white"
                              {...props}
                            />
                          );
                        },
                      }}
                    />
                    {!showMore && index === 0 && (
                      <button
                        className="text-light-gray whitespace-nowrap"
                        onClick={() => setShowMore(true)}
                      >
                        ...
                      </button>
                    )}
                  </div>
                );
              }
            })}
          </div>
          <div className="font-body-xs flex items-center gap-1">
            <div className="flex items-center gap-0.5 rounded-full bg-black/20 px-1">
              <MusicalNoteIcon className="h-3 w-3" />
              <div>Original audio</div>
            </div>
            {instagramContent.location && (
              <div className="flex items-center gap-0.5 rounded-full bg-black/20 px-1">
                <MapPinIcon className="h-3 w-3" />
                {instagramContent.location.name}
              </div>
            )}
          </div>
        </div>
      </div>
    </VideoPreviewContainer>
  );
};

export default InstagramReelsPreviewNewDesign;
