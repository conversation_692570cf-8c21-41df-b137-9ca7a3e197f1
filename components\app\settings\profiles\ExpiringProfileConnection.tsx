import Alert from "~/components/Alert";

type Props = {
  isReintegrationRequired: boolean;
};

const ExpiringProfileConnection = ({ isReintegrationRequired }: Props) => {
  if (!isReintegrationRequired) {
    return null;
  }

  return (
    <Alert
      intent="none"
      title="Some profile(s) require reconnecting"
      message={
        <span>
          Periodically, social profiles need to be reconnected - either because
          the initial connection expired, or due to password / permission
          changes.
        </span>
      }
    />
  );
};

export default ExpiringProfileConnection;
