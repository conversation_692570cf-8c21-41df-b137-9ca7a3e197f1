import assert from "assert";
import { useRouter } from "next/router";
import { useEffect } from "react";
import { toast } from "sonner";
import { Divider, TextLink } from "~/components";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import { useWindowTitle } from "~/hooks";
import { openIntercomChat, trpc } from "~/utils";
import { setCookie } from "~/utils/cookies";

type Props = {
  type: "scheduling" | "engagement";
};

const handleTwitterLogin = async (
  authInfo: {
    url: string;
    oAuthToken: string;
    oAuthTokenSecret: string;
  },
  accountID?: string
) => {
  setCookie(
    "TWITTER_INTEGRATION",
    {
      oAuthToken: authInfo.oAuthToken,
      oAuthTokenSecret: authInfo.oAuthTokenSecret,
      accountID,
      referrer: window.location.pathname,
    },
    {
      path: "/",
      secure: true,
      sameSite: "lax",
      maxAge: 5 * 60, // 5 minutes in seconds
    }
  );

  window.location.href = authInfo.url;
};

const TwitterConnectPage = ({ type }: Props) => {
  useWindowTitle("𝕏 Integration");
  const router = useRouter();
  const generateAuthLinkQuery =
    trpc.integration.twitter.generateAuthLink.useQuery();

  useEffect(() => {
    if (!router.isReady) return;

    const query = router.query;
    router.replace(router.pathname.replace("authed/", ""));

    if (query.error_message) {
      toast.error("Could not connect profile", {
        description: query.error_message as string,
      });
    }
  }, [router.isReady]);

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      <ConnectProfile.Root>
        <ConnectProfile.Header
          channel="Twitter"
          description="Connect your 𝕏 account to Assembly."
        />
        <ConnectProfile.Content>
          <Button
            size="sm"
            onClick={() => {
              const data = generateAuthLinkQuery.data;

              assert(data);
              const { authInfo } = data;

              handleTwitterLogin(authInfo);
            }}
          >
            Connect 𝕏
          </Button>
        </ConnectProfile.Content>
        <Divider className="my-2 md:my-4" />
        <ConnectProfile.Footer>
          <div className="space-y-1.5">
            <p className="font-medium">
              Important: Login to the correct 𝕏 profile before connecting.
            </p>
            <p className="text-medium-dark-gray">
              You will need to switch to the account you want to connect on 𝕏
              first. You can do so{" "}
              <TextLink
                href="https://x.com/account/switch"
                size="sm"
                value="here"
              />
              .
            </p>
          </div>
          <div className="space-y-1.5">
            <p className="font-medium">Need Help?</p>
            <p className="text-medium-dark-gray">
              Check out our help center article for a video walkthrough on how
              to connect. If you're still having issues, please{" "}
              <Button
                size="sm"
                variant="link"
                onClick={() => {
                  openIntercomChat(
                    "I'm having trouble connecting my 𝕏 profile: {details here}"
                  );
                }}
              >
                chat with us
              </Button>
              .
            </p>
          </div>
        </ConnectProfile.Footer>
      </ConnectProfile.Root>
    </SettingsLayout.Root>
  );
};

export default TwitterConnectPage;
export { handleTwitterLogin };
