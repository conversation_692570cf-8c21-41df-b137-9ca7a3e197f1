const introducingAssemblyPostDetails = {
  root: {
    type: "root",
    format: "",
    indent: 0,
    version: 1,
    children: [
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "(1 / 5) ",
            type: "text",
            style: "",
            detail: 0,
            format: 1,
            version: 1,
          },
          {
            mode: "normal",
            text: "Introducing Assembly ✨",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "Assembly is a marketer’s organizational best friend - built by marketers, for marketers. ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "Centralize your marketing roadmap, plan campaigns from idea to execution, and manage the marketing workflow from end to end with cross-functional teams, all in one place.",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "[PRODUCT GIF]",
            type: "text",
            style: "",
            detail: 0,
            format: 2,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "(2 / 5)",
            type: "text",
            style: "",
            detail: 0,
            format: 1,
            version: 1,
          },
          {
            mode: "normal",
            text: " Why Assembly? ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "Marketers are the central glue in a company that help companies acquire and retain customers and act as the face of the brand. Our role is naturally very cross-functional, working across creative, product, sales, engineering, and etc. ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "Yet today’s current workflow often includes dozens of docs, spreadsheets, department specific task managers, and more, which means that marketers are spending more time trying to find ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
          {
            mode: "normal",
            text: "launchtimelinev3_.xlsx ",
            type: "text",
            style: "",
            detail: 0,
            format: 2,
            version: 1,
          },
          {
            mode: "normal",
            text: "and chasing down approvals, than they are being strategic about marketing. ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "(3 / 5) ",
            type: "text",
            style: "",
            detail: 0,
            format: 1,
            version: 1,
          },
          {
            mode: "normal",
            text: "Assembly is built to change this. A couple things you can do with Assembly ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        tag: "ol",
        type: "list",
        start: 1,
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            type: "listitem",
            value: 1,
            format: "",
            indent: 0,
            version: 1,
            children: [
              {
                mode: "normal",
                text: "Create one Marketing Calendar Source of Truth ",
                type: "text",
                style: "",
                detail: 0,
                format: 1,
                version: 1,
              },
              {
                mode: "normal",
                text: "- Use the Calendar View to easily organize campaigns + keep everyone on the same page across ",
                type: "text",
                style: "",
                detail: 0,
                format: 0,
                version: 1,
              },
              {
                mode: "normal",
                text: "all",
                type: "text",
                style: "",
                detail: 0,
                format: 2,
                version: 1,
              },
              {
                mode: "normal",
                text: " marketing channels (social media, email, paid, blog posts, etc). ",
                type: "text",
                style: "",
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
            direction: "ltr",
          },
          {
            type: "listitem",
            value: 2,
            format: "",
            indent: 0,
            version: 1,
            children: [
              {
                mode: "normal",
                text: "Campaign Planning + Management",
                type: "text",
                style: "",
                detail: 0,
                format: 1,
                version: 1,
              },
              {
                mode: "normal",
                text: " - Plan campaigns from end to end - from brief creation, channel-specific posts, copy and asset management, to approvals. Create tentative campaign plans for discussion and manage your campaign checklist in one place.",
                type: "text",
                style: "",
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
            direction: "ltr",
          },
        ],
        listType: "number",
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "[CALENDAR ASSET]",
            type: "text",
            style: "",
            detail: 0,
            format: 2,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "(4 / 5) ",
            type: "text",
            style: "",
            detail: 0,
            format: 1,
            version: 1,
          },
        ],
        direction: null,
      },
      {
        tag: "ol",
        type: "list",
        start: 3,
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            type: "listitem",
            value: 3,
            format: "",
            indent: 0,
            version: 1,
            children: [
              {
                mode: "normal",
                text: "Flag Blockers and Streamline Approvals -",
                type: "text",
                style: "",
                detail: 0,
                format: 1,
                version: 1,
              },
              {
                mode: "normal",
                text: " Sometimes a simple edit or approval needed can delay the launch of a campaign. Easily manage a campaign’s progress and flag any blockers or approvals needed with Assembly’s Smart Notifications.",
                type: "text",
                style: "",
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
            direction: "ltr",
          },
          {
            type: "listitem",
            value: 4,
            format: "",
            indent: 0,
            version: 1,
            children: [
              {
                mode: "normal",
                text: "Shareable Meeting Updates -",
                type: "text",
                style: "",
                detail: 0,
                format: 1,
                version: 1,
              },
              {
                mode: "normal",
                text: " Save hours prepping for Weekly cross-functional meetings. Use Assembly’s Play view to create shareable progress updates and tasks in a few clicks, instead of updating this manually in spreadsheets and presentations.",
                type: "text",
                style: "",
                detail: 0,
                format: 0,
                version: 1,
              },
            ],
            direction: "ltr",
          },
        ],
        listType: "number",
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "[APPROVALS ASSET]",
            type: "text",
            style: "",
            detail: 0,
            format: 2,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "(5 / 5) ",
            type: "text",
            style: "",
            detail: 0,
            format: 1,
            version: 1,
          },
          {
            mode: "normal",
            text: "Our vision is to streamline operations for marketing teams and build delightful experiences for marketers. ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "We hope you’ll try us out :) ",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [
          {
            mode: "normal",
            text: "meetassembly.com",
            type: "text",
            style: "",
            detail: 0,
            format: 0,
            version: 1,
          },
        ],
        direction: "ltr",
      },
      {
        type: "paragraph",
        format: "",
        indent: 0,
        version: 1,
        children: [],
        direction: "ltr",
      },
    ],
    direction: "ltr",
  },
};

export default introducingAssemblyPostDetails;
