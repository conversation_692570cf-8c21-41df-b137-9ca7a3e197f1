import {
  Asset,
  InstagramContentType,
  InstagramIntegrationSource,
} from "@prisma/client";
import assert from "assert";
import invert from "lodash/invert";
import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import { db } from "~/clients/Prisma";
import Instagram from "~/clients/facebook/instagram/Instagram";
import { inngest } from "~/clients/inngest/inngest";
import { GENERIC_ERROR_MESSAGE } from "~/constants";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";
import { removeEmptyElems } from "~/utils";
import uploadAndPublishPosts from "./uploadAndPublishPosts";
import uploadInstagramAssets from "./uploadInstagramAssets";

type Post = {
  id: string;
  instagramIntegration: {
    id: string;
    account: {
      id: string;
      externalID: string;
    };
    accessToken: string;
    integrationSource: InstagramIntegrationSource;
  } | null;
  instagramContent: {
    id: string;
    type: InstagramContentType;
    coverPhoto: Asset | null;
    location: {
      pageID: string;
    } | null;
    assets: {
      id: string;
      asset: Asset;
      position: number;
      containerID: string | null;
      containerExpiresAt: Date | null;
      mimetype: string;
      tags: {
        id: string;
        username: string;
        x: number | null;
        y: number | null;
      }[];
    }[];
    copy: string;
    reelShareToFeed: boolean;
    collaborators: string[];
    engagements: {
      id: string;
      postDelaySeconds: number;
    }[];
  } | null;
  title: string;
  workspaceID: string;
  createdByID: string;
};

const UNSUPPORTED_FORMAT_ERROR_CODE = "2207026";

const publishUploadedPosts = async (
  post: Post
): Promise<ScheduledPostResult> => {
  const { instagramContent, instagramIntegration } = post;

  if (!instagramContent) {
    return {
      status: "NonRetriableError",
      error: "No Instagram copy to post",
    };
  }

  if (!instagramIntegration) {
    return {
      status: "NonRetriableError",
      error: "No Instagram integration",
    };
  }

  if (instagramContent.assets.length === 0) {
    console.log(
      `Skip Uploading post:${post.id}: No assets found so handled by execute-inband-scheduled-posts`
    );
    return {
      status: "NonRetriableError",
      error: "No media to post",
    };
  }

  const assets = instagramContent.assets;
  // Make sure all assets have a containerID and containerExpiresAt is further out than
  // 5 minutes (just to be safe) in the future
  if (
    !assets.every(
      (asset) =>
        asset.containerID &&
        asset.containerExpiresAt &&
        DateTime.fromJSDate(asset.containerExpiresAt).diffNow().as("minutes") >
          5
    )
  ) {
    console.log(`Skip Uploading post:${post.id}: Assets need to be uploaded`);
    return {
      status: "Skipped",
    };
  }

  const instagram = new Instagram(instagramIntegration.accessToken, {
    platform: instagramIntegration.integrationSource,
  });
  const instagramAccountID = instagramIntegration.account.externalID;

  const assetIDToContainerID = assets.reduce(
    (acc, asset) => {
      acc[asset.id] = asset.containerID as string;
      return acc;
    },
    {} as { [assetID: string]: string }
  );
  const sortedContainerIDs = removeEmptyElems(
    sortBy(assets, "position").map((asset) => asset.containerID)
  );

  const containerIDToAssetID = invert(assetIDToContainerID);

  let permalink: string | undefined = undefined;
  let instagramPostID: string | undefined = undefined;
  let error: string | null = null;
  let rawError: {} | undefined | null = null;
  let isAuthError: boolean = false;

  if (instagramContent.type === InstagramContentType.Story) {
    const assetToStoryIDs: { [assetID: string]: string } = {};
    for await (const containerID of sortedContainerIDs) {
      let response = await instagram.publishPost({
        instagramAccountID,
        creationID: containerID,
        type: instagramContent.type,
      });

      // Retry before giving up and calling it an error
      if (!response.success) {
        // Clear the containerID and containerExpiresAt so that we can retry
        await db.instagramAsset.updateMany({
          where: {
            instagramContentID: instagramContent.id,
          },
          data: {
            containerID: null,
            containerExpiresAt: null,
          },
        });
        return await uploadAndPublishPosts({
          ...post,
          instagramContent: {
            ...instagramContent,
            assets: assets.map((asset) => {
              return {
                ...asset,
                containerID: null,
                containerExpiresAt: null,
              };
            }),
          },
        });
      }

      if (response.data.permalink !== "") {
        permalink = response.data.permalink;
      }

      assetToStoryIDs[containerIDToAssetID[containerID]] = response.data.id;
    }

    if (error == null) {
      await db.$transaction(async (tx) => {
        for await (const assetID of Object.keys(assetToStoryIDs)) {
          await tx.instagramAsset.update({
            where: {
              id: assetID,
            },
            data: {
              storyID: assetToStoryIDs[assetID],
            },
          });
        }
      });
    }
  } else {
    let creationID = assetIDToContainerID[assets[0].id];

    // If we're posting a carousel then create the carousel container first
    if (
      instagramContent.type === InstagramContentType.Feed &&
      assets.length > 1
    ) {
      const carouselResponse = await instagram.createCarouselContainer({
        instagramAccountID,
        caption: instagramContent.copy,
        collaborators: instagramContent.collaborators,
        containerIDs: sortedContainerIDs,
        locationID: instagramContent.location?.pageID,
      });

      if (!carouselResponse.success) {
        const error = carouselResponse.errors.join(". ");
        isAuthError = carouselResponse.isAuthError;

        return {
          status:
            error.includes(UNSUPPORTED_FORMAT_ERROR_CODE) || isAuthError
              ? "NonRetriableError"
              : "Error",
          error,
          isAuthError,
        };
      } else {
        assert(carouselResponse.data);
        creationID = carouselResponse.data.id;
      }
    }

    let response = await instagram.publishPost({
      instagramAccountID,
      creationID,
      type: instagramContent.type,
    });

    // Retry before giving up and calling it an error
    if (!response.success) {
      const originalErrors = response.errors.join(". ");
      isAuthError = response.isAuthError;

      if (
        originalErrors.includes(UNSUPPORTED_FORMAT_ERROR_CODE) ||
        isAuthError
      ) {
        return {
          status: "NonRetriableError",
          error: originalErrors,
          isAuthError,
        };
      }

      console.log(`Retrying post:${post.id}: ${originalErrors}`);

      // https://developers.facebook.com/docs/instagram-platform/instagram-graph-api/reference/error-codes/
      const reuploadErrorCode = "2207032";
      const unkownUploadError = "2207053";
      if (
        originalErrors.includes(reuploadErrorCode) ||
        originalErrors.includes(unkownUploadError)
      ) {
        const uploadResponse = await uploadInstagramAssets(post, true);

        if (uploadResponse.status === "Success") {
          const updatedInstagramContent = await db.instagramContent.findUnique({
            where: {
              id: post.instagramContent!.id,
            },
            include: {
              location: {
                select: {
                  pageID: true,
                },
              },
              coverPhoto: true,
              assets: {
                include: {
                  tags: true,
                  asset: true,
                },
              },
              engagements: {
                select: {
                  id: true,
                  postDelaySeconds: true,
                },
              },
            },
          });

          return await publishUploadedPosts({
            ...post,
            instagramContent: updatedInstagramContent,
          });
        } else {
          isAuthError = !!uploadResponse.isAuthError;

          return {
            status: isAuthError ? "NonRetriableError" : "Error",
            error: originalErrors,
            isAuthError,
          };
        }
      }

      response = await instagram.publishPost({
        instagramAccountID,
        creationID,
        type: instagramContent.type,
      });

      if (response.errors) {
        error = response.errors.join(". ");
        rawError = response.rawError;
        isAuthError = response.isAuthError;
      }
    }

    if (response.data) {
      const igPost = response.data;
      permalink = igPost.permalink;
      const { username, ...rest } = igPost;
      instagramPostID = igPost.id;
      console.log("Published IG Post", rest.id, rest.permalink);
      await db.instagramPost.create({
        data: {
          ...rest,
          accountID: instagramIntegration.account.id,
        },
      });

      try {
        const { engagements } = instagramContent;

        const events = engagements.map((engagement) => {
          return {
            name: "instagram.postEngagement" as const,
            data: {
              externalPostID: instagramPostID as string,
              engagementID: engagement.id,
              delayUntil: DateTime.now()
                .plus({
                  seconds: engagement.postDelaySeconds,
                })
                .toJSDate(),
              permalink: permalink as string,
            },
          };
        });

        if (events.length) {
          await inngest.send(events);
        }
      } catch (error) {
        console.error(error);
      }
    }
  }

  if (error == null && permalink != null) {
    return {
      status: "Success",
      postUrl: permalink,
      socialPostID: instagramPostID,
    };
  } else {
    return {
      status: isAuthError ? "NonRetriableError" : "Error",
      error: error || GENERIC_ERROR_MESSAGE,
      rawError,
      isAuthError,
    };
  }
};

export default publishUploadedPosts;
