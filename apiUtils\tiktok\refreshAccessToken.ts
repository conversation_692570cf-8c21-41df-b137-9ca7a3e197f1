import { db } from "~/clients";
import TikTok from "~/clients/tiktok/TikTok";

type Integration = {
  id: string;
  refreshToken: string;
};

const refreshAccessToken = async (
  integration: Integration
): Promise<{
  status: "Error" | "Success";
}> => {
  const { data, errors, isAuthError } = await TikTok.refreshAccessToken(
    integration.refreshToken
  );

  if (data) {
    const tiktok = new TikTok(data.accessToken);
    const { data: userData, errors: userErrors } = await tiktok.getMe();
    if (!userData) {
      console.error(`Error ${integration.id}: ${userErrors.join(".")}`);
      return {
        status: "Error",
      };
    }
    const { account, stats } = userData;

    const tikTokIntegration = await db.tikTokIntegration.update({
      where: {
        id: integration.id,
      },
      data: {
        accessToken: data.accessToken,
        expiresAt: data.expiresAt,
        refreshToken: data.refreshToken,
        refreshTokenExpiresAt: data.refreshTokenExpiresAt,
        scope: data.scope,
      },
    });

    await db.tikTokAccount.update({
      where: {
        id: tikTokIntegration.accountID,
      },
      data: {
        name: account.name,
        username: account.username,
        profileImageUrl: account.profileImageUrl,
        bioDescription: account.bioDescription,
        isVerified: account.isVerified,
      },
    });

    await db.tikTokAccountStats.create({
      data: {
        accountID: tikTokIntegration.accountID,
        followerCount: stats.followerCount,
        followingCount: stats.followingCount,
        likeCount: stats.likeCount,
      },
    });

    return {
      status: "Success",
    };
  } else {
    console.error(`Error ${integration.id}: ${errors.join(".")}`);

    if (isAuthError) {
      await db.tikTokIntegration.update({
        where: {
          id: integration.id,
        },
        data: {
          isReintegrationRequired: true,
        },
      });
    }

    return {
      status: "Error",
    };
  }
};

export default refreshAccessToken;
