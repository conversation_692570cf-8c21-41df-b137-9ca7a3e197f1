const currentUrl = window.location.href;

const sleep = (milliseconds) => {
  return new Promise((resolve) => setTimeout(resolve, milliseconds));
};

const scrollToItem = (items, index) => {
  const item = items[index];
  if (!item) {
    return null;
  } else {
    item.scrollIntoView({ behavior: "smooth", block: "center" });
  }
};

if (
  currentUrl.startsWith("https://www.linkedin.com") &&
  currentUrl.endsWith("/recent-activity/all/")
) {
  const version = 7;

  const username = currentUrl
    .replace("https://www.linkedin.com/in/", "")
    .replace("/recent-activity/all/", "");

  const results = [];

  const initialItems = Array.from(
    document.querySelector(".scaffold-finite-scroll__content ul").childNodes
  )
    .filter((n) => n.tagName && "li" === n.tagName.toLowerCase())
    .slice(0, 30);

  let index = 1;
  for await (const item of initialItems) {
    item.scrollIntoView({ behavior: "smooth", block: "center" });
    if (index % 3 === 0) {
      await sleep(1000);
    }
    index++;
  }

  const followerCount = document
    .querySelector(
      "[href='/mynetwork/network-manager/people-follow/followers/'] div"
    )
    .innerHTML.trim()
    .replace(",", "");

  const items = Array.from(
    document.querySelector(".scaffold-finite-scroll__content ul").childNodes
  )
    .filter((n) => n.tagName && "li" === n.tagName.toLowerCase())
    .slice(0, 30);

  items.forEach(async (item) => {
    const post = item.getElementsByClassName("feed-shared-update-v2")[0];
    if (!post) {
      return;
    }
    console.log(post.getAttribute("data-urn"));
    const repostElem = post.getElementsByClassName(
      "update-components-header__text-wrapper"
    )[0];

    if (repostElem) {
      return;
    }

    const commentaryElement = post.querySelector(
      ".update-components-update-v2__commentary"
    );

    const commentary = commentaryElement?.innerText.slice(0, 30) || "";

    const hrefs = commentaryElement
      ? Array.from(commentaryElement.querySelectorAll("a")).map((a) => a.href)
      : [];
    const personTagCount = hrefs.filter((href) => href.includes("/in/")).length;
    const hashtagCount = hrefs.filter((href) =>
      href.includes("/feed/hashtag/")
    ).length;
    const organizationTagCount = hrefs.filter((href) =>
      href.includes("/company/")
    ).length;

    const urn = post
      .getAttribute("data-urn")
      .replace("urn:li:", "")
      .replace("activity:", "a:")
      .replace("share:", "s:")
      .replace("ugcPost:", "u:");

    const impressionElement = post.getElementsByClassName(
      "ca-entry-point__num-views"
    )[0];

    if (!impressionElement) {
      return;
    }
    const rawImpressionCount =
      impressionElement?.getElementsByTagName("strong")[0].textContent || "0";
    const impressionCount = rawImpressionCount
      .trim()
      .replace(" impressions", "")
      .replace(" impression", "")
      .replaceAll(",", "");

    const likeElement =
      post.getElementsByClassName(
        "social-details-social-counts__reactions-count"
      )[0] ||
      post.getElementsByClassName(
        "social-details-social-counts__social-proof-fallback-number"
      )[0];
    const rawLikeCount = likeElement?.textContent || "0";
    const likeCount = rawLikeCount.trim().replaceAll(",", "");

    let type = "Text";
    const isMultiImage =
      post.querySelector(".update-components-image--multi-image") !== null;
    const isImage =
      post.querySelector(".update-components-image--single-image") !== null;
    const isVideo =
      post.querySelector(".update-components-linkedin-video") !== null;
    const isQuotePost =
      post.querySelector(".update-components-mini-update-v2") !== null;
    const isArticle = post.querySelector(".update-components-article") !== null;
    const isDocument = post.querySelector(".carousel-track") !== null;
    const isPoll = post.querySelector(".update-components-poll") !== null;
    if (isMultiImage) {
      type = "MultiImage";
    } else if (isImage) {
      type = "Image";
    } else if (isVideo) {
      type = "Video";
    } else if (isQuotePost) {
      type = "QuotePost";
    } else if (isArticle) {
      type = "Article";
    } else if (isDocument) {
      type = "Document";
    } else if (isPoll) {
      type = "Poll";
    }

    const rawCommentCount =
      post
        .getElementsByClassName("social-details-social-counts__comments")[0]
        ?.getElementsByTagName("span")[0].textContent || "0";
    const commentCount = rawCommentCount
      .trim()
      .replaceAll(",", "")
      .replaceAll(" comments", "")
      .replaceAll(" comment", "");

    const socialShareElements = post.getElementsByClassName(
      "social-details-social-counts__item--with-social-proof"
    );
    const lastElement =
      socialShareElements.length > 0
        ? socialShareElements[socialShareElements.length - 1]
        : null;
    const rawShareCount =
      lastElement?.getElementsByTagName("span")[0].textContent || "0";
    const shareCount = rawShareCount.includes("comment")
      ? "0"
      : rawShareCount
          .trim()
          .replaceAll(",", "")
          .replaceAll(" reposts", "")
          .replaceAll(" repost", "");

    results.push([
      urn,
      impressionCount,
      likeCount,
      commentCount,
      shareCount,
      personTagCount,
      organizationTagCount,
      hashtagCount,
      commentary,
      type,
    ]);
  });

  console.log("done parsing");

  const params = new URLSearchParams({
    v: version,
    u: username,
    f: followerCount,
    p: JSON.stringify(results.slice(0, 17)),
  });

  console.log(params.toString());

  window.location.href = `http://app.meetassembly.com/settings/linkedin/import?${params}`;
} else {
  alert("This can only be run on your activity page");
}
