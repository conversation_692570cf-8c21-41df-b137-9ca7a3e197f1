import { useEffect, useState } from "react";
import { CHANNEL_ICON_MAP } from "~/types";

import { v4 as uuidv4 } from "uuid";

import { ArrowDownIcon, ArrowUpIcon } from "@heroicons/react/24/outline";
import { Channel, PostStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/react";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import { arrayMoveImmutable } from "array-move";
import assert from "assert";
import classNames from "classnames";
import sortBy from "lodash/sortBy";
import { useRouter } from "next/router";
import posthog from "posthog-js";
import { toast } from "sonner";
import { Divider, Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import Tabs from "~/components/ui/Tabs";
import { MAX_VIDEO_UPLOAD_SIZE_MB, ONE_YEAR_IN_SECONDS } from "~/constants";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import tusOptions from "~/constants/tusOptions";
import { useWorkspace } from "~/providers";
import { AssetType } from "~/types/Asset";
import {
  charCount,
  findAsync,
  formatTextAsTipTapEditorState,
  getImageProperties,
  handleFilesUpload,
  handleTrpcError,
  openIntercomArticle,
  openIntercomChat,
  removeEmptyElems,
  trpc,
} from "~/utils";
import { EditImageModalProperties } from "~/utils/EditImageModal";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import compressImage from "~/utils/compressImage";
import { getDefaultAccount } from "~/utils/getDefaultAccount";
import getFileProperties from "~/utils/getFileProperties";
import getVideoProperties from "~/utils/getVideoProperties";
import Assets, { UploadingAsset } from "../../files/Assets";
import TwitterEditor from "../../tiptap/TwitterEditor";
import EditorActionsBar from "../EditorActionsBar";
import PostEngagements from "../PostEngagements";
import PostPerformance from "../PostPerformance";
import PostPreviewContainer from "../PostPreviewContainer";
import { ScheduledPost } from "../SchedulePostAccountSelect";
import AltTextModal from "./AltTextModal";
import TagUsersModal from "./TagUsersModal";
import TweetThreadPreview from "./TweetThreadPreview";

export type TweetContent = {
  id: string;
  copy: string;
  editorState: JSONContent | null;
  mediaTaggedUsernames: string[];
};

export type TwitterIntegrationAccount = {
  integrationID: string;
  username: string;
  profileImageUrl: string;
  name: string;
  verified: boolean;
  verifiedType: string;
  isBasicVerifiedOverride: boolean;
};

export type TweetAsset = AssetType & { altText: string | null };

type Props = {
  post: {
    id: string;
    campaignID: string | null;
    channels: Channel[];
    title: string;
    status: PostStatus;
    scheduledPosts: ScheduledPost[];
    tweets: TweetContent[];
    tweetAssets: { [tweetID: string]: TweetAsset[] };
    twitterIntegrationID: string | null;
    twitterIntegration: TwitterIntegrationAccount | null;
    twitterLink: string | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const TweetThread = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const router = useRouter();
  const { currentWorkspace } = useWorkspace();
  const trpcUtils = trpc.useUtils();

  const [autoFocuseEnabled, setAutoFocusEnabled] = useState<boolean>(false);

  const [tweetDraggedOver, setTweetDraggedOver] = useState<{
    [id: string]: boolean;
  }>({});
  const [tweetUploadingAsset, setTweetUploadingAsset] = useState<{
    [id: string]: UploadingAsset[];
  }>({});
  const [tweetIsFocused, setTweetIsFocused] = useState<{
    [id: string]: boolean;
  }>({});

  const [twitterIntegration, setTwitterIntegration] =
    useState<TwitterIntegrationAccount | null>(post.twitterIntegration);
  const [tweets, setTweets] = useState<TweetContent[]>(
    post.tweets.length > 0
      ? sortBy(post.tweets, "threadPosition")
      : [
          {
            id: uuidv4(),
            editorState: null,
            copy: "",
            mediaTaggedUsernames: [],
          },
        ]
  );
  const [tweetAssets, setTweetAssets] = useState<{
    [tweetID: string]: TweetAsset[];
  }>(post.tweetAssets);

  const [uppy, setUppy] = useState<Uppy | null>(null);

  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);

  useEffect(() => {
    const initialMaybeCompressAssets = async () => {
      const assets = removeEmptyElems(Object.values(post.tweetAssets).flat());
      await maybeCompressAssets(assets);
    };

    initialMaybeCompressAssets();
  }, [post.tweetAssets]);

  const createAssetsMutation = trpc.tweetContent.createAssets.useMutation();
  const deleteAssetMutation = trpc.tweetContent.deleteAsset.useMutation();
  const updateAssetPositionMutation =
    trpc.tweetContent.updateAssetPositions.useMutation();
  const onSortEnd = (tweetID: string, oldIndex: number, newIndex: number) => {
    setTweetAssets((tweetAssets) => {
      const newTweetAssets = arrayMoveImmutable(
        tweetAssets[tweetID],
        oldIndex,
        newIndex
      );

      updateAssetPositionMutation.mutate({
        assets: newTweetAssets.map((asset, index) => ({
          id: asset.id,
          position: index,
        })),
      });

      const updatedTweetAssets = {
        ...tweetAssets,
        [tweetID]: newTweetAssets,
      };

      return updatedTweetAssets;
    });
  };

  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const compressVideoMutation = trpc.video.compress.useMutation();

  const maybeCompressAssets = async (
    assets: {
      path: string;
      sizeBytes: number;
      sizeMB: number;
      mimetype: string;
      signedUrl: string;
      duration?: number | null;
      width?: number | null;
      height?: number | null;
    }[]
  ) => {
    const videoPromises = assets.map(async (asset) => {
      if (!asset || !asset.mimetype.startsWith("video/")) {
        return Promise.resolve(null);
      } else {
        let duration = asset.duration;
        let width = asset.width;
        let height = asset.height;
        if (width && height && duration) {
          return asset as typeof asset & {
            duration: number;
            width: number;
            height: number;
          };
        } else {
          const properties = await getVideoProperties(asset.signedUrl);
          return {
            ...asset,
            duration: properties.duration,
            width: properties.width,
            height: properties.height,
          };
        }
      }
    });
    const videosWithProperties = await Promise.all(videoPromises);

    const videoToCompress = await findAsync(
      videosWithProperties,
      async (asset) => {
        if (!asset || !asset.mimetype.startsWith("video/")) {
          return false;
        }

        // https://developer.twitter.com/en/docs/twitter-api/v1/media/upload-media/uploading-media/media-best-practices
        // Even though it says 1280x1024 I've seen 3840 x 2160 (4k) videos work
        // Although they will get downsampled on Twitter

        const { width } = asset;

        return asset.sizeMB > 125 || width > 3840;
      }
    );

    if (videoToCompress != null) {
      const { width, height } = videoToCompress;

      compressVideoMutation.mutate(
        {
          video: {
            path: videoToCompress.path,
            resolution: {
              width,
              height,
            },
          },
          channel: "Twitter",
        },
        {
          onError: (error) => {
            handleTrpcError({ title: "Error Compressing Video", error });
          },
          onSuccess: (data) => {
            if (data.jobID != null) {
              toast.info("Optimizing Attachment", {
                description:
                  "We're automatically optimizing your attachment (files >125MB and width > 3840px are not accepted on Twitter).",
              });
            }
          },
        }
      );
    }
  };

  const updatePostMutation = trpc.post.update.useMutation();
  const engagementsQuery = trpc.tweetContent.getEngagements.useQuery({
    postID: post.id,
  });
  const engagements = engagementsQuery.data?.engagements || [];
  const createEngagementMutation =
    trpc.tweetContent.createEngagement.useMutation();
  const updateEngagementMutation =
    trpc.tweetContent.updateEngagement.useMutation();
  const deleteEngagementMutation =
    trpc.tweetContent.deleteEngagement.useMutation();
  const updateAssetMutation = trpc.tweetContent.updateAsset.useMutation();

  const connectedAccountsQuery =
    trpc.integration.twitter.getConnectedAccounts.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });
  const integrationsQuery = trpc.post.getIntegrations.useQuery({
    id: post.id,
  });
  const postAccounts = integrationsQuery.data?.accounts ?? [];

  const connectedAccountData = connectedAccountsQuery.data;
  useEffect(() => {
    const setDefaultAccount = () => {
      if (
        !isReadOnly &&
        post.twitterIntegrationID == null &&
        post.status !== "Posted" &&
        connectedAccountData != null &&
        connectedAccountData.accounts.length > 0
      ) {
        const defaultAccount = getDefaultAccount({
          mainAccount: postAccounts[0],
          accounts: connectedAccountData.accounts,
        });
        setTwitterIntegration(defaultAccount);
        updatePostMutation.mutate(
          {
            id: post.id,
            twitterIntegrationID: defaultAccount.integrationID,
          },
          {
            onSuccess: () => {
              trpcUtils.post.getIntegrations.refetch({
                id: post.id,
              });
            },
          }
        );
      }
    };
    setDefaultAccount();
  }, [connectedAccountData]);

  const accountOptions =
    connectedAccountsQuery.data?.accounts.map((account) => ({
      ...account,
      name: `@${account.username}`,
    })) || [];

  const tweetContentQuery = trpc.tweetContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const tweetContentData = tweetContentQuery.data;
  useEffect(() => {
    const setCrossPostData = async () => {
      if (
        tweetContentData &&
        isSyncingComplete &&
        !tweetContentQuery.isFetching
      ) {
        if (tweetContentData.tweets.length === 0) {
          setTweets([
            {
              id: uuidv4(),
              editorState: null,
              copy: "",
              mediaTaggedUsernames: [],
            },
          ]);
        } else {
          setTweets(tweetContentData.tweets);
          clearIsSyncing();
        }

        const assets = removeEmptyElems(
          Object.values(tweetContentData.assets).flat()
        );
        setTweetAssets(tweetContentData.assets);
        await maybeCompressAssets(assets);
      }
    };
    setCrossPostData();
  }, [tweetContentData, isSyncingComplete]);

  const upsertTweetMutation = trpc.tweetContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
    onError: (error) => {
      handleTrpcError({ title: "Failed to Save Tweet", error });
    },
  });
  const updateTweetPositionsMutation =
    trpc.tweetContent.updatePositions.useMutation();
  const deleteTweetMutation = trpc.tweetContent.delete.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Failed to Delete Tweet", error });
    },
  });

  const getValidFiles = (assets: AssetType[]) => async (files: File[]) => {
    let currentFiles = [...files].slice(0, 4);
    const validFiles: File[] = [];

    const numAssets = assets.length;
    if (numAssets >= 4) {
      toast.error("Too Many Attachments", {
        description: "You can add a total of 4 attachments per tweet",
      });
      return [];
    }

    currentFiles = currentFiles.slice(0, 4 - numAssets);

    if (currentFiles.length !== files.length) {
      toast.error("Too Many Attachments", {
        description: "You can add a total of 4 attachments per tweet",
      });
    }

    const assetNames = assets.map((asset) => asset.name);

    for await (const file of currentFiles) {
      const fileType = file.type;
      const fileSizeMB = file.size / 1024 / 1024;
      const fileIsImage = fileType.startsWith("image/");
      const fileIsGif = fileType === "image/gif";
      const fileIsVideo = fileType.startsWith("video/");
      const fileIsHeic = fileType === "image/heic";

      if (assetNames.includes(file.name)) {
        toast.error(`Duplicate Attachment: "${file.name}"`, {
          description: "Each attachment must have a unique name",
        });
        continue;
      }

      if (fileIsHeic) {
        toast.error("HEIC files are not supported", {
          description:
            "Please convert your HEIC file to a JPEG or PNG and reupload",
        });
        continue;
      }

      if (fileIsGif) {
        const { width, height } = await getImageProperties(file);
        if (fileSizeMB > 15) {
          toast.error("GIF size must be less than 15MB");
          continue;
        } else if (width > 1280) {
          toast.error("GIF is too wide", {
            description: "GIFs must be smaller than 1280x1280 pixels",
          });
          continue;
        } else if (height > 1280) {
          toast.error("GIF is too tall", {
            description: "GIFs must be smaller than 1280x1280 pixels",
          });
          continue;
        }
      }

      if (fileIsVideo) {
        const { width, height, duration } = await getVideoProperties(file);
        if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
          toast.error("Video is too large", {
            description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
            action: {
              label: "Contact Support",
              onClick: () => {
                openIntercomChat(
                  `I need help compressing a video for Twitter, I have a ${fileSizeMB.toFixed(
                    1
                  )}MB video that I need to compress to under ${MAX_VIDEO_UPLOAD_SIZE_MB}MB`
                );
              },
            },
          });
          continue;
        } else if (duration < 0.5) {
          toast.error("Video is too short", {
            description:
              "Twitter requires videos to be at least 0.5 seconds long",
          });
          continue;
        } else if (duration > 140) {
          toast.error("Video is Too Long", {
            description:
              "Twitter requires videos to be at most 2 minutes & 20 seconds long",
          });
          continue;
        } else if (width / height > 3) {
          toast.error("Video is too wide", {
            description: "Video aspect ratio must be between 1:3 and 3:1",
          });
          continue;
        } else if (height / width > 3) {
          toast.error("Video is too tall", {
            description: "Video aspect ratio must be between 1:3 and 3:1",
          });
          continue;
        }
      }

      if (fileIsImage && !fileIsGif) {
        const { pixels } = await getImageProperties(file);

        // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/multiimage-post-api?view=li-lms-2023-02&tabs=http#multiimage
        if (pixels >= 36_152_320) {
          toast.error("Image is Too Large", {
            description: "An image can have a maximum of 36,152,319 pixels",
          });
          continue;
        }

        if (fileSizeMB > 5) {
          const compressedFile = await compressImage(file, {
            maxSizeMB: 5,
          });
          toast.info("Attachment Optimized", {
            description: `${file.name} was optimized (files >5MB are not accepted on Twitter)`,
          });
          posthog.capture(POSTHOG_EVENTS.image.compressed, {
            originalSizeMB: file.size / 1_024 / 1_024,
            compressedSizeMB: compressedFile.size / 1_024 / 1_024,
            channel: "Twitter",
          });
          validFiles.push(compressedFile);
        } else {
          validFiles.push(file);
        }
      } else {
        validFiles.push(file);
      }
    }

    return validFiles;
  };

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `twitter-${post.id}`,
        restrictions: { maxNumberOfFiles: 4 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", (file) => {
        assert(currentWorkspace);

        const tweetID = file.meta.tweetID as string;
        assert(tweetID);

        setTweetUploadingAsset((prev) => {
          return {
            ...prev,
            [tweetID]: [
              ...(prev[tweetID] || []),
              {
                name: file.name!,
                size: file.size!,
                type: file.type,
                uploadProgress: 0,
                updateAssetID: file.meta.updateAssetID as string | undefined,
              },
            ],
          };
        });
        const objectName = `${currentWorkspace.id}/${post.id}/${CHANNEL_ICON_MAP["Twitter"].slug}/${tweetID}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        const tweetID = file.meta.tweetID as string;

        setTweetUploadingAsset((prev) => {
          const tweetAssets = prev[tweetID] || [];
          const assetIndex = tweetAssets.findIndex(
            (asset) => asset.name === file.name
          );
          if (assetIndex !== -1) {
            const newTweetAssets = [...tweetAssets];
            newTweetAssets[assetIndex] = {
              ...newTweetAssets[assetIndex],
              uploadProgress,
            };
            return { ...prev, [tweetID]: newTweetAssets };
          }
          return prev;
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (result) => {
        const tweetID = result.successful![0].meta.tweetID as string;

        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["Twitter"].slug
        }/${tweetID}`;
        const fileNames = result.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          result.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  altText: null,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              if (result.successful![0].meta.updateAssetID) {
                const updateAssetID = result.successful![0].meta
                  .updateAssetID as string;
                const newAsset = newAssetsWithProperties[0];
                await updateAssetMutation.mutateAsync(
                  {
                    tweetAssetID: updateAssetID,
                    newAsset: {
                      id: newAsset.id,
                      name: newAsset.name,
                      path: newAsset.path,
                      size: newAsset.sizeBytes,
                      eTag: newAsset.eTag,
                      md5Hash: newAsset.md5Hash,
                      mimetype: newAsset.mimetype,
                      width: newAsset.width,
                      height: newAsset.height,
                      duration: newAsset.duration,
                      url: newAsset.signedUrl,
                      urlExpiresAt: newAsset.urlExpiresAt,
                    },
                  },
                  {
                    onSuccess: () => {
                      trpcUtils.tweetContent.get.invalidate({
                        postID: post.id,
                      });

                      setTweetAssets((prevAssets) => {
                        const tweetAssets = prevAssets[tweetID];
                        return {
                          ...prevAssets,
                          [tweetID]: tweetAssets.map((asset) =>
                            asset.id === updateAssetID
                              ? {
                                  id: newAsset.id,
                                  altText: asset.altText,
                                  name: newAsset.name,
                                  path: newAsset.path,
                                  sizeBytes: newAsset.sizeBytes,
                                  eTag: newAsset.eTag,
                                  md5Hash: newAsset.md5Hash,
                                  mimetype: newAsset.mimetype,
                                  width: newAsset.width,
                                  height: newAsset.height,
                                  metadata: newAsset.metadata,
                                  url: newAsset.signedUrl,
                                  sizeMB: newAsset.sizeMB,
                                  sizeKB: newAsset.sizeKB,
                                  createdAt: newAsset.createdAt,
                                  updatedAt: newAsset.updatedAt,
                                  duration: newAsset.duration,
                                  signedUrl: newAsset.signedUrl,
                                  urlExpiresAt: newAsset.urlExpiresAt,
                                }
                              : asset
                          ),
                        };
                      });
                    },
                  }
                );
                setTweetUploadingAsset((prevUploadingAssets) => {
                  const uploadingAssets = { ...prevUploadingAssets };
                  delete uploadingAssets[tweetID];
                  return uploadingAssets;
                });
              } else {
                const nextPosition = assets.length;

                createAssetsMutation.mutate(
                  {
                    id: tweetID,
                    assets: newAssetsWithProperties.map((asset, index) => ({
                      id: asset.id,
                      assetID: asset.assetID,
                      name: asset.name,
                      path: asset.path,
                      size: asset.sizeBytes,
                      eTag: asset.eTag,
                      md5Hash: asset.md5Hash,
                      mimetype: asset.mimetype,
                      width: asset.width,
                      height: asset.height,
                      duration: asset.duration,
                      url: asset.signedUrl,
                      urlExpiresAt: asset.urlExpiresAt,
                      position: nextPosition + index,
                    })),
                  },
                  {
                    onSuccess: async () => {
                      await maybeCompressAssets(newAssetsWithProperties);
                    },
                    onError: (error) => {
                      handleTrpcError({ title: "Error Creating Asset", error });
                    },
                  }
                );

                setTweetUploadingAsset((tweetUploadingAsset) => {
                  // Find any uploading assets by name that have finished uploading
                  // and remove them from the uploading assets state
                  const uploadingAssets = Object.entries(
                    tweetUploadingAsset
                  ).map(([tweetID, assets]) => {
                    const uploadingAssets = assets.filter((asset) => {
                      return !newAssetsWithProperties.some(
                        (uploadedAsset) => uploadedAsset.name === asset.name
                      );
                    });
                    return [tweetID, uploadingAssets];
                  });
                  return Object.fromEntries(uploadingAssets);
                });
                setTweetAssets((assets) => ({
                  ...assets,
                  [tweetID]: [
                    ...(assets[tweetID] || []),
                    ...newAssetsWithProperties,
                  ],
                }));
              }
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace]);

  const updateTweets = (
    tweetID: string,
    data: {
      editorState?: JSONContent;
      copy?: string;
    }
  ) => {
    setTweets((prevTweets) => {
      const index = prevTweets.findIndex((tweet) => tweet.id === tweetID);
      if (index < 0) {
        return prevTweets;
      }

      const newTweets = [...prevTweets];
      newTweets[index] = {
        ...newTweets[index],
        ...data,
      };

      if (data.editorState) {
        const tweet = newTweets[index];
        upsertTweetMutation.mutate({
          id: tweet.id,
          threadPosition: index,
          copy: tweet.copy,
          editorState: data.editorState,
          postID: post.id,
        });
      }

      return newTweets;
    });
  };

  return (
    <div>
      <PostPerformance channel="Twitter" url={post.twitterLink} />
      <PostPreviewContainer
        channel="Twitter"
        post={post}
        connectedAccount={
          twitterIntegration
            ? {
                integrationID: twitterIntegration.integrationID,
                name: `@${twitterIntegration.username}`,
              }
            : null
        }
        accountOptions={accountOptions}
        setConnectedAccountID={(integrationID) => {
          const account = accountOptions.find(
            (account) => account.integrationID === integrationID
          );
          setTwitterIntegration(account || null);
          updatePostMutation.mutate(
            {
              id: post.id,
              twitterIntegrationID: account?.integrationID || null,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getIntegrations.refetch({
                  id: post.id,
                });
              },
            }
          );
        }}
        Preview={
          <TweetThreadPreview
            tweets={tweets.map((tweet) => {
              return {
                ...tweet,
                assets: tweetAssets[tweet.id],
              };
            })}
            connectedAccount={twitterIntegration}
          />
        }
        isReadOnly={isReadOnly}
      />
      <Tabs.Root defaultValue="post_content">
        <Tabs.List>
          <Tabs.Trigger value="post_content">Post Content</Tabs.Trigger>
          <Tabs.Trigger value="auto_engagements">
            {engagements.length
              ? `Auto Engagement (${engagements.length})`
              : "Auto Engagement"}
          </Tabs.Trigger>
        </Tabs.List>
        <Tabs.Content value="post_content">
          <div>
            {tweets.map((tweet, index) => {
              const assets = tweetAssets[tweet.id] || [];

              const numAssets = assets?.length;
              const isDraggedOver = tweetDraggedOver[tweet.id] || false;

              const addTweet = ({
                tweetID,
                nextTweetCopy,
              }: {
                tweetID: string;
                nextTweetCopy?: string;
              }) => {
                setAutoFocusEnabled(true);
                const index = tweets.findIndex((t) => t.id === tweetID);

                const newTweet = {
                  id: uuidv4(),
                  editorState: formatTextAsTipTapEditorState(nextTweetCopy),
                  copy: nextTweetCopy || "",
                  mediaTaggedUsernames: [],
                };

                upsertTweetMutation.mutate({
                  id: newTweet.id,
                  threadPosition: index + 1,
                  copy: newTweet.copy,
                  editorState: newTweet.editorState,
                  postID: post.id,
                });

                setTweets((prevTweets) => {
                  const withAddedTweet = [
                    ...prevTweets.slice(0, index + 1),
                    newTweet,
                    ...prevTweets.slice(index + 1, prevTweets.length),
                  ];
                  return withAddedTweet;
                });
              };

              const deleteTweet = () => {
                setTweets((prevTweets) => {
                  const index = prevTweets.findIndex((t) => t.id === tweet.id);

                  deleteTweetMutation.mutate({
                    id: tweet.id,
                  });
                  const withRemovedTweet = [
                    ...prevTweets.slice(0, index),
                    ...prevTweets.slice(index + 1, prevTweets.length),
                  ];

                  return withRemovedTweet;
                });
              };

              return (
                <div
                  key={tweet.id}
                  onDragOverCapture={() => {
                    setTweetDraggedOver((prevTweetDraggedOver) => ({
                      ...prevTweetDraggedOver,
                      [tweet.id]: true,
                    }));
                  }}
                  onDragLeaveCapture={() => {
                    setTweetDraggedOver((prevTweetDraggedOver) => ({
                      ...prevTweetDraggedOver,
                      [tweet.id]: false,
                    }));
                  }}
                  onDrop={() => {
                    setTweetDraggedOver((prevTweetDraggedOver) => ({
                      ...prevTweetDraggedOver,
                      [tweet.id]: false,
                    }));
                  }}
                  className={classNames("my-2 bg-white", {
                    "border-lightest-gray mb-4 overflow-clip border-b":
                      index !== tweets.length - 1,
                  })}
                >
                  <div className="group">
                    <TwitterEditor
                      key={tweet.id}
                      postID={post.id}
                      title={`Tweet (${index + 1}/${tweets.length})`}
                      initialValue={tweet.editorState}
                      placeholder="Add copy for the post here"
                      autoFocus={autoFocuseEnabled}
                      onImagePaste={(event) =>
                        handleFilesUpload({
                          items: event.clipboardData?.items,
                          uppy: uppy!,
                          meta: { tweetID: tweet.id },
                          getValidFiles: getValidFiles(assets),
                        })
                      }
                      onFocusChange={(isFocused) => {
                        if (isFocused) {
                          setTweetIsFocused((prevTweetIsFocused) => {
                            Object.keys(prevTweetIsFocused).forEach((key) => {
                              prevTweetIsFocused[key] = false;
                            });
                            prevTweetIsFocused[tweet.id] = true;
                            return { ...prevTweetIsFocused };
                          });
                        }
                      }}
                      isReadOnly={isReadOnly}
                      onSave={(editorState) => {
                        updateTweets(tweet.id, { editorState });
                      }}
                      onUpdateContent={(copy) => {
                        updateTweets(tweet.id, {
                          copy,
                        });
                      }}
                      tweetID={tweet.id}
                      defaultMaxLength={
                        twitterIntegration?.verified ||
                        twitterIntegration?.isBasicVerifiedOverride
                          ? 4000
                          : 280
                      }
                      previousTweetID={index > 0 ? tweets[index - 1].id : null}
                      addTweet={(nextTweetCopy) => {
                        addTweet({
                          tweetID: tweet.id,
                          nextTweetCopy: nextTweetCopy.trim(),
                        });
                      }}
                      deleteTweet={() => {
                        if (tweets.length === 1) {
                          return;
                        } else {
                          deleteTweet();
                        }
                      }}
                    />
                    <EditorActionsBar
                      post={post}
                      channel="Twitter"
                      visible={
                        Object.values(tweetIsFocused).every((v) => !v)
                          ? index === 0
                          : tweetIsFocused[tweet.id]
                      }
                      addNewButton={{
                        label: "Add Tweet",
                        onClick: () => {
                          addTweet({ tweetID: tweet.id });
                        },
                      }}
                      characterCount={{
                        current: charCount(tweet.copy, {
                          isTwitter: true,
                        }),
                        max:
                          twitterIntegration?.verified ||
                          twitterIntegration?.isBasicVerifiedOverride
                            ? 4000
                            : 280,
                        twitterAccount: twitterIntegration,
                      }}
                      uploadAssetButton={{
                        acceptedMimetypes: [
                          "image/jpeg",
                          "image/png",
                          "image/gif",
                          "video/quicktime",
                          "video/mp4",
                        ],
                        acceptMultiple: true,
                        onUploadFiles: async (files) => {
                          if (uppy) {
                            handleFilesUpload({
                              files,
                              uppy,
                              meta: {
                                tweetID: tweet.id,
                              },
                              getValidFiles: getValidFiles(assets),
                            });
                          }
                        },
                        isLoading:
                          (tweetUploadingAsset[tweet.id]?.length || 0) > 0,
                      }}
                      openHowToTagModal={() => {
                        openIntercomArticle("twitter_tagging");
                      }}
                      deleteButton={{
                        onClick: () => {
                          deleteTweet();
                        },
                        isHidden: tweets.length === 1,
                      }}
                      additionalOptions={
                        <div className="flex h-5 items-center gap-2">
                          <div className="flex items-center gap-1">
                            <div className="font-body-sm text-medium-dark-gray">
                              Media Options:
                            </div>
                            <div className="mt-1 flex items-center">
                              <TagUsersModal
                                tweet={tweet}
                                isDisabled={assets.length === 0}
                              />
                              <AltTextModal
                                assets={assets}
                                setTweetAssets={(assetsAltText) => {
                                  setTweetAssets((prevAssets) => {
                                    const selectedTweetAssets =
                                      prevAssets[tweet.id];

                                    const newAssets = {
                                      ...prevAssets,
                                      [tweet.id]: selectedTweetAssets.map(
                                        (asset) => ({
                                          ...asset,
                                          altText:
                                            assetsAltText[asset.id] || null,
                                        })
                                      ),
                                    };

                                    return newAssets;
                                  });
                                }}
                              />
                            </div>
                          </div>
                          {tweets.length > 1 && (
                            <Divider orientation="vertical" />
                          )}
                          {index > 0 && (
                            <Tooltip value="Move Up">
                              <Button
                                size="xs"
                                onClick={() => {
                                  const newTweets = arrayMoveImmutable(
                                    tweets,
                                    index,
                                    index - 1
                                  ).map((tweet, index) => ({
                                    ...tweet,
                                    threadPosition: index,
                                  }));

                                  updateTweetPositionsMutation.mutate({
                                    tweets: newTweets.map((tweet) => ({
                                      id: tweet.id,
                                      threadPosition: tweet.threadPosition,
                                    })),
                                  });

                                  setTweets(newTweets);
                                }}
                                variant="ghost"
                              >
                                <ArrowUpIcon className="h-4 w-4" />
                              </Button>
                            </Tooltip>
                          )}
                          {index < tweets.length - 1 && (
                            <Tooltip value="Move Down">
                              <Button
                                size="xs"
                                onClick={() => {
                                  const newTweets = arrayMoveImmutable(
                                    tweets,
                                    index,
                                    index + 1
                                  ).map((tweet, index) => ({
                                    ...tweet,
                                    threadPosition: index,
                                  }));

                                  updateTweetPositionsMutation.mutate({
                                    tweets: newTweets.map((tweet) => ({
                                      id: tweet.id,
                                      threadPosition: tweet.threadPosition,
                                    })),
                                  });

                                  setTweets(newTweets);
                                }}
                                variant="ghost"
                              >
                                <ArrowDownIcon className="h-4 w-4" />
                              </Button>
                            </Tooltip>
                          )}
                        </div>
                      }
                      isReadOnly={isReadOnly}
                    />
                  </div>
                  <div
                    className={classNames("pt-1", {
                      "mt-2 pb-4": numAssets > 0 || isDraggedOver,
                    })}
                  >
                    <Assets
                      assets={assets}
                      uploadingAssets={tweetUploadingAsset[tweet.id]}
                      onDeleteClick={(asset) => {
                        setTweetAssets((prevAssets) => ({
                          ...prevAssets,
                          [tweet.id]: prevAssets[tweet.id].filter(
                            (currentAsset) => currentAsset.id !== asset.id
                          ),
                        }));

                        deleteAssetMutation.mutate({
                          tweetAssetID: asset.id,
                        });
                      }}
                      editImageModalProperties={editImageModalProperties}
                      onEditClick={(asset) => {
                        if (asset.metadata.mimetype.startsWith("image/")) {
                          setEditImageModalProperties({
                            title: "Edit Image",
                            channel: "Twitter",
                            maxSizeMB: 5,
                            isOpen: true,
                            asset,
                          });
                        }
                      }}
                      onUpload={async (file, updateAssetID) => {
                        if (uppy) {
                          if (updateAssetID) {
                            uppy.addFile({
                              name: file.name,
                              type: file.type,
                              data: file,
                              meta: {
                                tweetID: tweet.id,
                                updateAssetID,
                              },
                            });
                          } else {
                            await handleFilesUpload({
                              files: [file],
                              uppy,
                              meta: {
                                tweetID: tweet.id,
                              },
                              getValidFiles: getValidFiles(assets),
                            });
                          }
                        }
                      }}
                      onEditImageModalClose={() =>
                        setEditImageModalProperties(null)
                      }
                      refetchAssets={() => {}}
                      isReadOnly={isReadOnly}
                      onDrop={async (files) => {
                        setTweetDraggedOver((prevTweetDraggedOver) => ({
                          ...prevTweetDraggedOver,
                          [tweet.id]: false,
                        }));
                        if (uppy) {
                          await handleFilesUpload({
                            files,
                            uppy,
                            meta: { tweetID: tweet.id },
                            getValidFiles: getValidFiles(assets),
                          });
                        }
                      }}
                      showDropzone={isDraggedOver}
                      acceptedFileTypes={{
                        "image/jpg": [".jpg", ".jpeg"],
                        "image/png": [".png"],
                        "image/gif": [".gif"],
                        "video/mp4": [".mp4"],
                        "video/quicktime": [".mov"],
                      }}
                      onSortEnd={(oldIndex, newIndex) => {
                        onSortEnd(tweet.id, oldIndex, newIndex);
                      }}
                      showNewEditButton={true}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </Tabs.Content>
        <Tabs.Content value="auto_engagements">
          <PostEngagements
            channel="Twitter"
            engagements={engagements}
            onEngagementCreate={async (type) => {
              const engagementID = uuidv4();

              await trpcUtils.tweetContent.getEngagements.cancel();

              assert(twitterIntegration);

              createEngagementMutation.mutate({
                engagementID,
                contentID: tweets[0].id,
                type,
                integrationID: twitterIntegration.integrationID,
              });

              // Optimistically update the restponse to the engagementsQuery
              trpcUtils.tweetContent.getEngagements.setData(
                {
                  postID: post.id,
                },
                {
                  engagements: [
                    ...engagements,
                    {
                      id: engagementID,
                      type,
                      copy: null,
                      editorState: null,
                      postDelaySeconds: 0,
                      integrationID: twitterIntegration.integrationID,
                      integration: {
                        account: {
                          username: twitterIntegration.username,
                        },
                      },
                      createdAt: new Date(),
                    },
                  ],
                }
              );
            }}
            onEngagementUpdate={(engagementID, engagement) => {
              updateEngagementMutation.mutate(
                {
                  engagementID,
                  ...engagement,
                },
                {
                  onSuccess: () => {
                    trpcUtils.tweetContent.getEngagements.invalidate();
                  },
                }
              );
            }}
            onEngagementDelete={(engagementID) => {
              deleteEngagementMutation.mutate(
                {
                  engagementID,
                },
                {
                  onSuccess: () => {
                    trpcUtils.tweetContent.getEngagements.invalidate();
                  },
                }
              );

              // Optimistically update the restponse to the engagementsQuery to remove this engagement
              trpcUtils.tweetContent.getEngagements.setData(
                {
                  postID: post.id,
                },
                {
                  engagements: engagements.filter(
                    (engagement) => engagement.id !== engagementID
                  ),
                }
              );
            }}
            accountOptions={
              isReadOnly
                ? engagements.map((engagement) => ({
                    value: engagement.integrationID,
                    label: `@${engagement.integration.account.username}`,
                  }))
                : accountOptions.map((account) => ({
                    value: account.integrationID,
                    label: account.name,
                    disabled:
                      currentWorkspace?.subscription &&
                      !currentWorkspace.subscription.isAutoEngagementEnabled &&
                      twitterIntegration != null &&
                      account.integrationID !==
                        twitterIntegration.integrationID,
                    disabledReason: (
                      <div>
                        <div className="font-body-sm mb-1.5">
                          Auto-engagements from accounts other than the posting
                          account is only available on{" "}
                          <span className="font-medium">Standard</span>{" "}
                          workspace plans and above.
                        </div>
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => {
                            router.push("/settings/pricing");
                          }}
                        >
                          See Plans
                        </Button>
                      </div>
                    ),
                  }))
            }
            Editor={({
              initialValue,
              onSave,
              onUpdateContent,
              placeholder,
            }) => (
              <TwitterEditor
                postID={post.id}
                initialValue={initialValue}
                placeholder={placeholder}
                onSave={onSave}
                onUpdateContent={onUpdateContent}
                size="sm"
                isReadOnly={isReadOnly}
              />
            )}
            isReadOnly={isReadOnly}
          />
        </Tabs.Content>
      </Tabs.Root>
    </div>
  );
};

export default TweetThread;
