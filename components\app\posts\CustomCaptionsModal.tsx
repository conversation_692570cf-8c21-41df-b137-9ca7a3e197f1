import classNames from "classnames";
import truncate from "lodash/truncate";
import { useState } from "react";
import { toast } from "sonner";
import supabasePublic from "~/clients/supabasePublic";
import { AddNewButton, Badge, IconButton } from "~/components";
import Credenza from "~/components/ui/Credenza";
import { AssetType } from "~/types/Asset";
import FileDropzone from "../files/FileDropzone";

type Props = {
  contentID: string;
  captions: AssetType | null;
  getValidFiles?: (files: File[]) => Promise<File[]>;
  onUpload: (fileName: string, onSave: () => void) => void;
  onDelete: (asset: { id: string; path: string }) => void;
  storagePath: string;
  open: boolean;
  setOpen: (open: boolean) => void;
};

const CustomCaptionsModal = ({
  captions,
  getValidFiles = async (files) => files,
  onUpload,
  onDelete,
  storagePath,
  open,
  setOpen,
}: Props) => {
  const [uploadingFile, setUploadingFile] = useState<{
    name: string;
    size: number;
    uploadProgress: number;
  } | null>(null);

  return (
    <div>
      {!captions ? (
        <AddNewButton label="Custom Captions" onClick={() => setOpen(true)} />
      ) : (
        <Badge
          intent="secondary"
          kind="hollow"
          label={truncate(captions.name, { length: 20 })}
          onClick={() => setOpen(true)}
        />
      )}
      <Credenza.Root open={open} onOpenChange={setOpen}>
        <Credenza.Header>
          <Credenza.Title>Add .SRT File</Credenza.Title>
          <Credenza.Description>
            In order to add custom captions to your LinkedIn video, upload a
            custom .SRT file below.
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <div className="flex gap-4">
            {captions ? (
              <div className="group relative col-span-3 h-full">
                <div className="border-light-gray rounded-md border p-4">
                  <div className="font-body-sm font-medium">
                    {captions.name}
                  </div>
                  <div className="font-body-xs text-medium-dark-gray">
                    {(captions.sizeBytes / 1024).toFixed(1)} KB
                  </div>
                </div>
                <div className="z-20 opacity-0 transition-opacity group-hover:opacity-100">
                  <div className="absolute top-2 right-2 flex gap-1.5">
                    <IconButton
                      icon="TrashIcon"
                      intent="danger"
                      onClick={() => {
                        onDelete(captions);
                      }}
                    />
                  </div>
                </div>
              </div>
            ) : null}
            <div
              className={classNames("w-full", {
                "h-64": !captions,
                "h-auto": !!captions,
              })}
            >
              <FileDropzone
                uploadProgress={uploadingFile?.uploadProgress}
                onDrop={async (files) => {
                  if (files.length > 1) {
                    toast.error("Too Many Files", {
                      description: "You can only upload one .srt file",
                    });
                    return;
                  }

                  const file = files[0];
                  if (!file) return;

                  if (!file.name.endsWith(".srt")) {
                    toast.error("Invalid File Type", {
                      description: "File must be in the .srt file format",
                    });
                    return;
                  }

                  const fileSizeMB = file.size / 1024 / 1024;
                  if (fileSizeMB > 1) {
                    toast.error("File Size is Too Big", {
                      description: "File size must be smaller than 1MB",
                    });
                    return;
                  }

                  setUploadingFile({
                    name: file.name,
                    size: file.size,
                    uploadProgress: 0,
                  });

                  const validFiles = await getValidFiles([file]);
                  if (validFiles.length === 0) {
                    setUploadingFile(null);
                    return;
                  }

                  const path = `${storagePath}/${file.name}`;
                  const response = await supabasePublic.storage
                    .from("assets")
                    .upload(path, file);

                  if (response.error) {
                    toast.error("Error Uploading File", {
                      description: response.error.message,
                    });
                    setUploadingFile(null);
                  } else {
                    onUpload(file.name, () => {
                      setUploadingFile(null);
                      setOpen(false);
                    });
                  }
                }}
                label="Drop .SRT file here to upload"
                acceptedFileTypes={{
                  "text/plain": [".srt"],
                }}
              />
            </div>
          </div>
        </Credenza.Body>
      </Credenza.Root>
    </div>
  );
};

export default CustomCaptionsModal;
