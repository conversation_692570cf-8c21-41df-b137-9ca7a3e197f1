import { Channel, TikTokPrivacyLevel } from "@prisma/client";
import { JSONContent } from "@tiptap/react";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import assert from "assert";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import Select from "~/components/Select";
import { MAX_VIDEO_UPLOAD_SIZE_MB, ONE_YEAR_IN_SECONDS } from "~/constants";
import tusOptions from "~/constants/tusOptions";
import {
  usePostIntegrations,
  useThreadComments,
  useWorkspace,
} from "~/providers";
import { TikTokIntegration } from "~/providers/PostIntegrationsProvider";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  findAsync,
  handleFilesUpload,
  handleTrpcError,
  openIntercomArticle,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import getFileProperties from "~/utils/getFileProperties";
import getVideoProperties from "~/utils/getVideoProperties";
import createRoomID from "~/utils/liveblocks/createRoomID";
import { UploadingAsset } from "../../files/Assets";
import TikTokEditorWrapperNewDesign from "../../tiptap/TikTokEditorNewDesign";
import EditorActionsBar from "../EditorActionsBar";
import {
  PostOptionNewDesign,
  PostOptionsNewDesign,
} from "../PostOptionsNewDesign";
import PostPerformance from "../PostPerformance";
import { ScheduledPost } from "../SchedulePostAccountSelect";
import TikTokPostPreviewNewDesign from "./TikTokPostPreviewNewDesign";

export type TikTokContent = {
  id: string;
  editorState: JSONContent | null;
  copy: string;
  privacyLevel: TikTokPrivacyLevel | null;
  isCommentDisabled: boolean | null;
  isDuetDisabled: boolean | null;
  isStitchDisabled: boolean | null;
  isPromotingBusiness: boolean | null;
  isPaidPartnership: boolean | null;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    scheduledPosts: ScheduledPost[];
    tikTokAssets: AssetType[];
    tikTokContent: TikTokContent | null;
    tikTokIntegration: TikTokIntegration | null;
    tikTokLink: string | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const TikTokPostNewDesign = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const trpcUtils = trpc.useUtils();
  const [isRoomReady, setIsRoomReady] = useState<boolean>(false);

  const {
    currentChannel,
    selectedIntegrations,
    connectedIntegrations,
    getChannelIntegration,
  } = usePostIntegrations();

  const connectedTikTokIntegrations = connectedIntegrations.TikTok;

  const [integration, setIntegration] = useState<TikTokIntegration | null>(
    post.tikTokIntegration || null
  );

  useEffect(() => {
    if (post.tikTokContent && !isSyncingComplete) {
      setIsRoomReady(true);
    }
  }, [post.tikTokContent, isSyncingComplete]);

  const [contentAssets, setContentAssets] = useState<AssetType[]>(
    post.tikTokAssets
  );
  const [uploadingAsset, setUploadingAsset] = useState<UploadingAsset | null>(
    null
  );
  const { currentWorkspace } = useWorkspace();

  const [draggedOver, setDraggedOver] = useState<boolean>(false);

  const [tikTokContent, setTikTokContent] = useState<TikTokContent>(
    post.tikTokContent || {
      id: uuidv4(),
      editorState: null,
      copy: "",
      privacyLevel: null,
      isCommentDisabled: null,
      isDuetDisabled: null,
      isStitchDisabled: null,
      isPromotingBusiness: null,
      isPaidPartnership: null,
    }
  );

  const updatePostMutation = trpc.post.update.useMutation();

  useEffect(() => {
    if (selectedIntegrations.TikTok && connectedTikTokIntegrations.length) {
      const selectedIntegration = connectedTikTokIntegrations.find(
        (integration) => integration.id === selectedIntegrations.TikTok
      );

      if (
        selectedIntegration &&
        (!integration || integration.id !== selectedIntegration.id)
      ) {
        const tikTokIntegration = getChannelIntegration(
          "TikTok",
          selectedIntegrations.TikTok
        );
        if (tikTokIntegration) {
          setIntegration(tikTokIntegration);
          updatePostMutation.mutate(
            {
              id: post.id,
              tikTokIntegrationID: tikTokIntegration.id,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getIntegrations.refetch({ id: post.id });
              },
            }
          );
        }
      }
    }
  }, [selectedIntegrations, connectedTikTokIntegrations]);

  const [uppy, setUppy] = useState<Uppy | null>(null);

  const tikTokContentQuery = trpc.tikTokContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const tikTokContentData = tikTokContentQuery.data;
  useEffect(() => {
    const setCrossPostData = () => {
      if (tikTokContentData?.tikTokContent != null) {
        setTikTokContent(tikTokContentData.tikTokContent);
        setContentAssets(tikTokContentData.assets);
        setIsRoomReady(true);
        clearIsSyncing();
      }
    };
    setCrossPostData();
  }, [tikTokContentData]);

  const upsertTikTokMutation = trpc.tikTokContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
    onError: (error) => {
      handleTrpcError({ title: "Error Updating TikTok Post", error });
    },
  });

  const createAssetsMutation = trpc.tikTokContent.createAssets.useMutation();
  const deleteAssetMutation = trpc.tikTokContent.deleteAsset.useMutation();
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const compressVideoMutation = trpc.video.compress.useMutation();
  const maybeCompressAssets = async (
    assets: {
      path: string;
      sizeBytes: number;
      sizeMB: number;
      mimetype: string;
      signedUrl: string;
      duration?: number | null;
      width?: number | null;
      height?: number | null;
    }[]
  ) => {
    const videoPromises = assets.map(async (asset) => {
      if (!asset || !asset.mimetype.startsWith("video/")) {
        return Promise.resolve(null);
      } else {
        let duration = asset.duration;
        let width = asset.width;
        let height = asset.height;
        if (width && height && duration) {
          return asset as typeof asset & {
            duration: number;
            width: number;
            height: number;
          };
        } else {
          const properties = await getVideoProperties(asset.signedUrl);
          return {
            ...asset,
            duration: properties.duration,
            width: properties.width,
            height: properties.height,
          };
        }
      }
    });
    const videosWithProperties = await Promise.all(videoPromises);

    const videoToCompress = await findAsync(
      videosWithProperties,
      async (asset) => {
        if (!asset || !asset.mimetype.startsWith("video/")) {
          return false;
        }

        return asset.sizeMB > 125;
      }
    );

    if (videoToCompress != null) {
      const { width, height } = videoToCompress;

      compressVideoMutation.mutate(
        {
          video: {
            path: videoToCompress.path,
            resolution: {
              width,
              height,
            },
            duration: videoToCompress.duration,
            sizeBytes: videoToCompress.sizeBytes,
          },
          channel: "TikTok",
        },
        {
          onError: (error) => {
            handleTrpcError({ title: "Error Compressing Video", error });
          },
          onSuccess: (data) => {
            if (data.jobID != null) {
              toast.info("Optimizing Attachment", {
                description:
                  "We're automatically optimizing your attachment (files >125MB are not accepted on YouTube).",
              });
            }
          },
        }
      );
    }
  };

  const getValidFiles = async (files: File[]) => {
    if (contentAssets.length + files.length > 1) {
      toast.error("Too Many Files", {
        description: "Only one file can be uploaded to TikTok",
      });
      return [];
    }

    const assetNames = contentAssets.map((asset) => asset.name);

    const validFiles: File[] = [];
    for await (const file of files) {
      const fileType = file.type;
      const fileSizeMB = file.size / 1024 / 1024;

      if (assetNames.includes(file.name)) {
        toast.error(`Duplicate Attachment: "${file.name}"`, {
          description: "Each attachment must have a unique name",
        });
        continue;
      }

      if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
        toast.error(`File Too Large: "${file.name}"`, {
          description: `Maximum file size is ${MAX_VIDEO_UPLOAD_SIZE_MB}MB`,
        });
        continue;
      }

      const { duration, height, width } = await getVideoProperties(file);

      if (duration > 600) {
        toast.error("Video is Too Long", {
          description: "TikTok requires videos to be a maximum of 10 minutes",
        });
        continue;
      }

      if (width < 360 || height < 360) {
        toast.error("Video is Too Small", {
          description:
            "TikTok requires videos to be at least 360px for both height and width",
        });
        continue;
      }

      if (width > 4096 || height > 4096) {
        toast.error("Video is Too Large", {
          description:
            "TikTok requires videos to be a maximum of 4096px for both height and width",
        });
        continue;
      }

      validFiles.push(file);
    }

    return validFiles;
  };

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `tiktok-${post.id}`,
        restrictions: { maxNumberOfFiles: 1 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", (file) => {
        assert(currentWorkspace);

        setUploadingAsset({
          name: file.name!,
          size: file.size!,
          type: file.type,
          uploadProgress: 0,
        });

        const objectName = `${currentWorkspace.id}/${post.id}/tiktok/${tikTokContent.id}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAsset((prevAsset) => {
          if (prevAsset && prevAsset.name === file.name) {
            return {
              ...prevAsset,
              uploadProgress,
            };
          }
          return prevAsset;
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (data) => {
        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["TikTok"].slug
        }/${tikTokContent.id}`;
        const fileNames = data.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          data.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              const nextPosition = assets.length;

              createAssetsMutation.mutate(
                {
                  id: tikTokContent.id,
                  assets: newAssetsWithProperties.map((asset, index) => ({
                    id: asset.id,
                    assetID: asset.assetID,
                    name: asset.name,
                    path: asset.path,
                    size: asset.sizeBytes,
                    eTag: asset.eTag,
                    md5Hash: asset.md5Hash,
                    mimetype: asset.mimetype,
                    width: asset.width,
                    height: asset.height,
                    duration: asset.duration,
                    url: asset.signedUrl,
                    urlExpiresAt: asset.urlExpiresAt,
                    position: nextPosition + index,
                  })),
                },
                {
                  onSuccess: async () => {
                    await maybeCompressAssets(newAssetsWithProperties);
                  },
                  onError: (error) => {
                    handleTrpcError({ title: "Error Creating Asset", error });
                  },
                }
              );

              setUploadingAsset(null);
              setContentAssets((assets) => [
                ...assets,
                ...newAssetsWithProperties,
              ]);
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace, tikTokContent.id]);

  const { setRoomIDs } = useThreadComments();

  const currentRoomID = createRoomID({
    workspaceID: post.workspaceID,
    contentType: "post",
    postID: post.id,
    roomContentID: tikTokContent.id,
  });

  useEffect(() => {
    if (tikTokContent.id && currentChannel === "TikTok") {
      setRoomIDs([currentRoomID]);
    }
  }, [tikTokContent.id, currentRoomID, setRoomIDs, currentChannel]);

  return (
    <div
      className="flex max-w-[622px] flex-col items-center"
      // This avoids triggering the dropzone when dragging internal post content like existing images
      onDragOver={(event) => {
        if (
          event.dataTransfer.types.includes("Files") &&
          !event.dataTransfer.types.includes("text/html")
        ) {
          event.preventDefault();
          setDraggedOver(true);
        }
      }}
      onDragLeave={(event) => {
        const currentTarget = event.currentTarget;
        const relatedTarget = event.relatedTarget as Node | null;

        if (!relatedTarget || !currentTarget.contains(relatedTarget)) {
          setDraggedOver(false);
        }
      }}
      onDrop={() => {
        setDraggedOver(false);
      }}
    >
      <PostPerformance channel="TikTok" url={post.tikTokLink} />
      <TikTokPostPreviewNewDesign
        tikTokContent={tikTokContent}
        assets={contentAssets}
        connectedIntegration={integration}
        isReadOnly={isReadOnly}
        uppy={uppy}
        uploadingAssets={uploadingAsset ? [uploadingAsset] : []}
        getValidFiles={getValidFiles}
        onDrop={() => setDraggedOver(false)}
        onDeleteClick={(asset) => {
          setContentAssets((prevAssets) =>
            prevAssets.filter((currentAsset) => currentAsset.id !== asset.id)
          );
          deleteAssetMutation.mutate({
            tikTokAssetID: asset.id,
          });
        }}
        isDraggedOver={draggedOver}
      />
      <div className="mt-12 w-full">
        <span className="font-body-sm font-medium">Caption</span>
        {isRoomReady && (
          <TikTokEditorWrapperNewDesign
            key={tikTokContent.id}
            postID={post.id}
            initialValue={tikTokContent.editorState}
            isReadOnly={isReadOnly}
            placeholder="Add your caption and video attachment here"
            onImagePaste={(event) => {
              if (uppy) {
                handleFilesUpload({
                  items: event.clipboardData.items,
                  uppy,
                  meta: {
                    contentID: tikTokContent.id,
                  },
                  getValidFiles,
                });
              }
            }}
            onSave={(editorState) => {
              assert(tikTokContent);
              setTikTokContent((prevTikTokContent) => ({
                ...prevTikTokContent,
                editorState,
              }));

              upsertTikTokMutation.mutate({
                id: tikTokContent.id,
                copy: tikTokContent.copy,
                editorState: editorState,
                postID: post.id,
              });
            }}
            onUpdateContent={(copy) => {
              setTikTokContent((prevTikTokContent) => ({
                ...prevTikTokContent,
                copy,
              }));
            }}
            roomID={currentRoomID}
          />
        )}
        <EditorActionsBar
          post={post}
          channel="TikTok"
          visible={true}
          isReadOnly={isReadOnly}
          characterCount={{
            current: tikTokContent.copy.length,
            max: 2200,
          }}
          uploadAssetButton={{
            acceptedMimetypes: ["video/mp4", "video/quicktime"],
            onUploadFiles: async (files) => {
              if (uppy) {
                handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID: tikTokContent.id,
                  },
                  getValidFiles,
                });
              }
            },
            isLoading: !!uploadingAsset,
          }}
          openHowToTagModal={() => {
            openIntercomArticle("tiktok_tagging");
          }}
        />
        <PostOptionsNewDesign>
          <PostOptionNewDesign title="Privacy">
            <Select
              options={[
                {
                  label: "Public",
                  value: "PUBLIC_TO_EVERYONE",
                },
                {
                  label: "Followers",
                  value: "FOLLOWER_OF_CREATOR",
                },
                {
                  label: "Friend's Followers",
                  value: "MUTUAL_FOLLOW_FRIENDS",
                },
                {
                  label: "Self",
                  value: "SELF_ONLY",
                },
              ]}
              value={tikTokContent.privacyLevel}
              onChange={(value: TikTokPrivacyLevel) => {
                setTikTokContent((prevTikTokContent) => ({
                  ...prevTikTokContent,
                  privacyLevel: value,
                }));
                upsertTikTokMutation.mutate({
                  id: tikTokContent.id,
                  copy: tikTokContent.copy,
                  editorState: tikTokContent.editorState,
                  postID: post.id,
                  privacyLevel: value,
                });
              }}
            />
          </PostOptionNewDesign>
          <PostOptionNewDesign title="Comments">
            <Select
              options={[
                {
                  label: "Disabled",
                  value: true,
                },
                {
                  label: "Enabled",
                  value: false,
                },
              ]}
              value={tikTokContent.isCommentDisabled}
              onChange={(value: boolean) => {
                setTikTokContent((prevTikTokContent) => ({
                  ...prevTikTokContent,
                  isCommentDisabled: value,
                }));
                upsertTikTokMutation.mutate({
                  id: tikTokContent.id,
                  copy: tikTokContent.copy,
                  editorState: tikTokContent.editorState,
                  postID: post.id,
                  isCommentDisabled: value,
                });
              }}
            />
          </PostOptionNewDesign>
          <PostOptionNewDesign title="Duets">
            <Select
              options={[
                {
                  label: "Disabled",
                  value: true,
                },
                {
                  label: "Enabled",
                  value: false,
                },
              ]}
              value={tikTokContent.isDuetDisabled}
              onChange={(value: boolean) => {
                setTikTokContent((prevTikTokContent) => ({
                  ...prevTikTokContent,
                  isDuetDisabled: value,
                }));
                upsertTikTokMutation.mutate({
                  id: tikTokContent.id,
                  copy: tikTokContent.copy,
                  editorState: tikTokContent.editorState,
                  postID: post.id,
                  isDuetDisabled: value,
                });
              }}
            />
          </PostOptionNewDesign>
          <PostOptionNewDesign title="Stitch">
            <Select
              options={[
                {
                  label: "Disabled",
                  value: true,
                },
                {
                  label: "Enabled",
                  value: false,
                },
              ]}
              value={tikTokContent.isStitchDisabled}
              onChange={(value: boolean) => {
                setTikTokContent((prevTikTokContent) => ({
                  ...prevTikTokContent,
                  isStitchDisabled: value,
                }));
                upsertTikTokMutation.mutate({
                  id: tikTokContent.id,
                  copy: tikTokContent.copy,
                  editorState: tikTokContent.editorState,
                  postID: post.id,
                  isStitchDisabled: value,
                });
              }}
            />
          </PostOptionNewDesign>
          <PostOptionNewDesign title="Sponsored?">
            <Select
              options={[
                {
                  label: "True",
                  value: true,
                },
                {
                  label: "False",
                  value: false,
                },
              ]}
              value={tikTokContent.isPromotingBusiness}
              onChange={(value: boolean) => {
                setTikTokContent((prevTikTokContent) => ({
                  ...prevTikTokContent,
                  isPromotingBusiness: value,
                }));
                upsertTikTokMutation.mutate({
                  id: tikTokContent.id,
                  copy: tikTokContent.copy,
                  editorState: tikTokContent.editorState,
                  postID: post.id,
                  isPromotingBusiness: value,
                });
              }}
            />
          </PostOptionNewDesign>
          {tikTokContent.isPromotingBusiness && (
            <PostOptionNewDesign title="Partnership?">
              <Select
                options={[
                  {
                    label: "True",
                    value: true,
                  },
                  {
                    label: "False",
                    value: false,
                  },
                ]}
                value={tikTokContent.isPaidPartnership}
                onChange={(value: boolean) => {
                  setTikTokContent((prevTikTokContent) => ({
                    ...prevTikTokContent,
                    isPaidPartnership: value,
                  }));
                  upsertTikTokMutation.mutate({
                    id: tikTokContent.id,
                    copy: tikTokContent.copy,
                    editorState: tikTokContent.editorState,
                    postID: post.id,
                    isPaidPartnership: value,
                  });
                }}
              />
            </PostOptionNewDesign>
          )}
        </PostOptionsNewDesign>
      </div>
    </div>
  );
};

export default TikTokPostNewDesign;
