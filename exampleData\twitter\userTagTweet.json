{"data": {"referenced_tweets": [{"type": "quoted", "id": "1635762188190138371"}], "text": "My friend @phil<PERSON>_r<PERSON><PERSON> is making some amazing videos for his clients\n\nIf anyone is looking for something like this I'm happy to make an intro https://t.co/XpntGHsh4P", "author_id": "**********", "public_metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 5, "quote_count": 0, "impression_count": 333}, "entities": {"mentions": [{"start": 10, "end": 25, "username": "phil<PERSON>_ruff<PERSON>", "id": "*********"}], "urls": [{"start": 145, "end": 168, "url": "https://t.co/XpntGHsh4P", "expanded_url": "https://twitter.com/philip_ruffini/status/1635762188190138371", "display_url": "twitter.com/philip_ruffini…"}]}, "edit_history_tweet_ids": ["1635801986602786818"], "created_at": "2023-03-15T00:35:59.000Z", "id": "1635801986602786818"}, "includes": {"users": [{"id": "**********", "name": "<PERSON><PERSON>", "username": "francistogram"}, {"id": "*********", "name": "<PERSON>", "username": "phil<PERSON>_ruff<PERSON>"}], "tweets": [{"text": "Did 5M+ views in less than 45 days on a brand-new company account\n\nLet me know if you want to upgrade your short-form content https://t.co/Pjo54teJS0", "author_id": "*********", "public_metrics": {"retweet_count": 1, "reply_count": 4, "like_count": 25, "quote_count": 1, "impression_count": 2660}, "attachments": {"media_keys": ["7_1635758680061755395"]}, "edit_history_tweet_ids": ["1635762188190138371"], "created_at": "2023-03-14T21:57:51.000Z", "entities": {"urls": [{"start": 126, "end": 149, "url": "https://t.co/Pjo54teJS0", "expanded_url": "https://twitter.com/philip_ruffini/status/1635762188190138371/video/1", "display_url": "pic.twitter.com/Pjo54teJS0", "media_key": "7_1635758680061755395"}]}, "id": "1635762188190138371"}]}}