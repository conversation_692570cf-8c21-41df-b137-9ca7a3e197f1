import { Asset } from "@prisma/client";
import { AssetType } from "~/types/Asset";
import enhanceAssetType from "./assets/enhanceAssetType";

const getAssetsForThreads = (post: {
  id: string;
  workspaceID: string;
  threadsContent: {
    id: string;
    assets: {
      id: string;
      position: number;
      asset: Asset;
    }[];
  }[];
}): {
  [id: string]: AssetType[];
} => {
  const assetsForThreads: {
    [id: string]: AssetType[];
  } = {};

  post.threadsContent.forEach((content) => {
    const assets = content.assets
      .map((asset) => {
        return {
          ...enhanceAssetType(asset.asset),
          id: asset.id,
          position: asset.position,
        };
      })
      .sort((a, b) => a.position - b.position);

    assetsForThreads[content.id] = assets;
  });

  return assetsForThreads;
};

export default getAssetsForThreads;
