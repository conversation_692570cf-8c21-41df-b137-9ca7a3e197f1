import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import assert from "assert";
import { DateTime } from "luxon";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const date = DateTime.fromFormat("June 07 2022", "LLLL dd yyyy").toJSDate();

  const posts = await db.post.findMany({
    orderBy: {
      publishAt: "desc",
    },
    where: {
      isIdea: false,
      publishDate: {
        not: null,
      },
      publishAt: {
        not: null,
      },
    },
    select: {
      id: true,
      status: true,
      publishAt: true,
      publishDate: true,
    },
  });

  const postsWithDelta = posts.filter((post) => {
    const publishAt = DateTime.fromJSDate(post.publishAt as Date);
    const publishDate = DateTime.fromJSDate(post.publishDate as Date);
    const delta = publishAt.diff(publishDate, "days").toObject().days as number;
    return Math.abs(delta) > 1;
  });

  for await (const post of postsWithDelta) {
    assert(post.publishAt != null);
    const publishDate = new Date(
      DateTime.fromJSDate(post.publishAt, {
        zone: "America/Chicago",
      }).toLocaleString(DateTime.DATE_SHORT) as string
    );
    console.log(`Updating ${post.id} to ${publishDate} from ${post.publishAt}`);
    await db.post.update({
      where: {
        id: post.id,
      },
      data: {
        publishDate,
      },
    });
  }

  res.status(StatusCodes.OK).send({ postsWithDelta });
};

export default handler;
