import Blockquote from "@tiptap/extension-blockquote";
import Bold from "@tiptap/extension-bold";
import BulletList from "@tiptap/extension-bullet-list";
import Code from "@tiptap/extension-code";
import Document from "@tiptap/extension-document";
import History from "@tiptap/extension-history";
import Italic from "@tiptap/extension-italic";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import TaskItem from "@tiptap/extension-task-item";
import TaskList from "@tiptap/extension-task-list";
import Text from "@tiptap/extension-text";
import Underline from "@tiptap/extension-underline";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import { Divider } from "~/components";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>eHandler } from "./extensions";
import FloatingToolbar from "./ui/FloatingToolbar";
import TextFormatButtons from "./ui/TextFormatButtons";
import TextStyleButtons from "./ui/TextStyleButtons";

type Props = {
  initialValue?: JSONContent | null;
  placeholder: string;
  isReadOnly?: boolean;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (copy: string) => void;
};

const NotesEditor = ({
  initialValue,
  placeholder,
  isReadOnly,
  onSave,
  onUpdateContent,
}: Props) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue || null
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const editor = useEditor({
    extensions: [
      Document,
      Paragraph,
      TextDirection.configure({
        types: ["heading", "paragraph"],
      }),
      Text,
      History,
      BulletList,
      OrderedList,
      ListItem,
      Bold,
      Italic,
      Code,
      Underline,
      Blockquote,
      TaskList,
      TaskItem,
      Emoji,
      Link,
      Placeholder.configure({
        placeholder,
      }),
      PasteHandler,
    ],
    onCreate({ editor }) {
      const html = editor.getHTML();

      onUpdateContent(html);
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();

      onUpdateContent(html);
      setEditorState(editor.getJSON());
    },
    content: editorState,
    immediatelyRender: false,
    editable: !isReadOnly,
  });

  return (
    <div>
      {editor && (
        <FloatingToolbar editor={editor}>
          {({ onHide }) => (
            <>
              <TextFormatButtons editor={editor} onHide={onHide} />
              <Divider orientation="vertical" />
              <TextStyleButtons editor={editor} />
            </>
          )}
        </FloatingToolbar>
      )}
      <EditorContent editor={editor} />
    </div>
  );
};

export default NotesEditor;
