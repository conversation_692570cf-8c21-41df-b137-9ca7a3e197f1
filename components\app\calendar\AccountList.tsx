import { Channel } from "@prisma/client";
import classNames from "classnames";
import AccountChannelAvatar from "../AccountChannelAvatar";
import ChannelIcon from "../ChannelIcon";

export type Account = {
  id: string;
  name: string;
  profileImageUrl?: string;
  channel: Channel;
};

type Props = {
  numAccountsBeforeOverflow: number;
  accounts: Account[];
  isIconOnly?: boolean;
};

const AccountList = ({
  accounts,
  numAccountsBeforeOverflow,
  isIconOnly = false,
}: Props) => {
  const showMore = accounts.length > numAccountsBeforeOverflow;
  const displayedAccounts = accounts.slice(
    0,
    showMore ? numAccountsBeforeOverflow - 1 : numAccountsBeforeOverflow
  );

  return (
    <div
      className={classNames("flex items-center", {
        "gap-2": !isIconOnly,
        "gap-1": isIconOnly,
      })}
    >
      {displayedAccounts.map((account) => {
        if (isIconOnly) {
          return (
            <ChannelIcon
              key={account.id}
              channel={account.channel}
              width={16}
            />
          );
        } else {
          return <AccountChannelAvatar key={account.id} account={account} />;
        }
      })}
      {showMore && (
        <div className="text-secondary font-eyebrow whitespace-nowrap">
          +{accounts.length - (numAccountsBeforeOverflow - 1)} more
        </div>
      )}
    </div>
  );
};

export default AccountList;
