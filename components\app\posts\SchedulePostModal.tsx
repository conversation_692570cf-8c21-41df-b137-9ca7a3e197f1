import { useEffect, useState } from "react";

import { ClockIcon } from "@heroicons/react/24/outline";
import { trackEvent } from "@intercom/messenger-js-sdk";
import {
  Channel,
  ScheduledPostStatus,
  WorkspaceOnboardingTaskState,
} from "@prisma/client";
import assert from "assert";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import differenceBy from "lodash/differenceBy";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { Alert, Checkbox } from "~/components";
import { SuggestedTimes } from "~/components/TimeInput";
import { FormDatePicker, FormTimeInput } from "~/components/form";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { CHANNEL_ICON_MAP } from "~/types";
import {
  dateFromUTC,
  getPostPublishAt,
  getPostPublishAtMoment,
  handleTrpcError,
  openIntercomChat,
  removeEmptyElems,
  trpc,
} from "~/utils";
import ChannelIcon from "../ChannelIcon";
import TimezoneTooltip from "../TimezoneTooltip";

type ScheduledPost = {
  id: string;
  status: ScheduledPostStatus;
  channel: Channel;
  error: string | null;
  publishAt: Date;
  integrationAccountName: string;
};

type FormValues = {
  postDate: Date;
  postTime: Date;
};

const SchedulePostModal = ({
  post,
  open,
  setOpen,
  accounts,
  suggestedTimes,
  shouldPostImmediately = false,
}: {
  post: {
    id: string;
    title: string;
    isIdea: boolean;
    publishDate: Date | null;
    publishTime: Date | null;
    publishAt: Date | null;
  };
  open: boolean;
  setOpen: (open: boolean) => void;
  accounts: {
    id?: string;
    name?: string;
    channel: Channel;
    scheduledPost: ScheduledPost | null;
  }[];
  suggestedTimes: SuggestedTimes[];
  shouldPostImmediately?: boolean;
}) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();

  const [selectedAccounts, setSelectedAccounts] = useState<
    {
      id: string;
      channel: Channel;
    }[]
  >([]);

  useEffect(() => {
    if (
      accounts.every(
        (account) =>
          account.scheduledPost == null ||
          account.scheduledPost.status === "Cancelled"
      )
    ) {
      const defaultAccounts = removeEmptyElems(
        accounts.map((account) => {
          if (account.id == null) {
            return null;
          }

          return {
            id: account.id,
            channel: account.channel,
          };
        })
      );

      setSelectedAccounts(defaultAccounts);
    } else {
      const scheduledAccounts = accounts.filter(
        (account) =>
          !!account.scheduledPost &&
          account.scheduledPost.status !== "Cancelled"
      );

      setSelectedAccounts(
        scheduledAccounts.map((account) => ({
          id: account.id as string,
          channel: account.channel,
        }))
      );
    }
  }, [accounts]);

  const { control, handleSubmit, watch, reset, setValue } =
    useForm<FormValues>();

  const publishDate = watch("postDate");
  const publishTime = watch("postTime");

  const scheduledAccounts = accounts.filter(
    (account) =>
      !!account.scheduledPost && account.scheduledPost.status !== "Cancelled"
  );

  const missingScheduledAccount = differenceBy(
    scheduledAccounts,
    selectedAccounts,
    "id"
  );

  const scheduledPosts = removeEmptyElems(
    scheduledAccounts.map((account) => account.scheduledPost)
  ).filter((scheduledPost) => scheduledPost.status !== "Cancelled");
  const scheduledPost = scheduledPosts[0];

  useEffect(() => {
    if (scheduledPost && scheduledPost.status === "Scheduled") {
      setValue("postDate", new Date(scheduledPost.publishAt));
      setValue("postTime", new Date(scheduledPost.publishAt));
    }
  }, [scheduledPost]);

  useEffect(() => {
    if (post.publishAt) {
      setValue("postDate", new Date(post.publishAt));
      setValue("postTime", new Date(post.publishAt));
    }
  }, [post.publishAt]);

  useEffect(() => {
    // TODO(francisco): change this to use publishAt instead of publishDate and publishTime
    if (!scheduledPost && !post.isIdea) {
      const publishDate = post.publishDate;
      assert(publishDate);
      const postDate = post.publishAt
        ? new Date(post.publishAt)
        : dateFromUTC(publishDate);

      const postTime = post.publishAt
        ? getPostPublishAtMoment({ ...post, publishDate }).toJSDate()
        : undefined;

      setValue("postDate", postDate);
      if (postTime) {
        setValue("postTime", postTime);
      }
    }
  }, [post]);

  const schedulePostMutation = trpc.post.schedulePost.useMutation({
    onSuccess: (data, variables) => {
      trackEvent("post_scheduled", {
        channels: selectedAccounts
          .map((account) => CHANNEL_ICON_MAP[account.channel].label)
          .join(", "),
      });
      trpcUtils.post.getIntegrations.refetch({
        id: post.id,
      });
      trpcUtils.post.getMany.invalidate();
      const previousData = trpcUtils.post.getMany.getData();
      if (previousData) {
        const previousPosts = previousData.posts;
        const updatedPosts = previousPosts.map((previousPost) => {
          if (previousPost.id === post.id) {
            const publishAt = variables.publishAt as Date;
            return {
              ...previousPost,
              status: "Scheduled" as const,
              publishAt,
            };
          } else {
            return previousPost;
          }
        });
        trpcUtils.post.getMany.setData(undefined, {
          posts: updatedPosts,
        });
      }
      reset({
        postDate: undefined,
        postTime: undefined,
      });
      setOpen(false);
      trpcUtils.subscriber.getForContent.refetch({ id: post.id, type: "post" });
    },
  });

  const handleSchedulePost = (values: FormValues) => {
    const postTime = DateTime.fromJSDate(values.postTime);
    const publishAt = DateTime.fromJSDate(values.postDate)
      .startOf("day")
      .set({
        hour: postTime.get("hour"),
        minute: postTime.get("minute"),
      })
      .toJSDate();

    schedulePostMutation.mutate(
      {
        id: post.id,
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        publishAt,
        accounts: selectedAccounts,
        shouldPostImmediately,
      },
      {
        onSuccess: () => {
          const currentTasks = trpcUtils.onboarding.getTasks.getData();
          if (currentTasks) {
            const updatedTasks = currentTasks.map((task) =>
              task.type === "SCHEDULE_POST"
                ? {
                    ...task,
                    state: WorkspaceOnboardingTaskState.COMPLETED,
                    isCompleted: true,
                  }
                : task
            );
            trpcUtils.onboarding.getTasks.setData(undefined, updatedTasks);
          }
          if (post.isIdea) {
            const ideaData = trpcUtils.idea.getAll.getData();
            const ideas = ideaData?.ideas;
            if (ideas) {
              trpcUtils.idea.getAll.setData(undefined, {
                ideas: ideas.filter((idea) => idea.id !== post.id),
              });
            }
            router.push(`/posts/${post.id}`);
          } else {
            trpcUtils.post.get.invalidate({ id: post.id });

            const currentPostData = trpcUtils.post.get.getData({ id: post.id });
            if (currentPostData) {
              trpcUtils.post.get.setData(
                {
                  id: post.id,
                },
                {
                  post: {
                    ...currentPostData.post,
                    publishTime: publishAt,
                    publishDate: publishAt,
                    publishAt: publishAt,
                    isIdea: false,
                    status: "Scheduled",
                  },
                  isReadOnly: currentPostData.isReadOnly,
                }
              );
            }
          }
        },
        onError: (error) => {
          handleTrpcError({
            title: "Unable to Schedule Post",
            error,
            upgradeModalProps: (subscription) => {
              const { scheduledPostLimit, tier, isTrial } = subscription;

              if (isTrial) {
                return {
                  title: <span>Choose a Plan</span>,
                  body: (
                    <span>
                      Your free trial comes with up to {scheduledPostLimit}{" "}
                      scheduled posts per month. To continue scheduling
                      additional posts, please upgrade to a paid plan that best
                      suits your needs.
                    </span>
                  ),
                  primaryButton: {
                    label: "Upgrade",
                    onClick: () => {
                      router.push("/settings/pricing");
                    },
                  },
                  flow: "Schedule Post",
                };
              } else if (subscription.tier === "Basic") {
                return {
                  title: (
                    <span>
                      Upgrade to{" "}
                      <span className="text-secondary">Standard</span>?
                    </span>
                  ),
                  body: (
                    <span>
                      Your Basic plan includes {scheduledPostLimit} scheduled
                      posts every month. You've already scheduled{" "}
                      {scheduledPostLimit}/{scheduledPostLimit} posts this month
                      - please upgrade to Standard to schedule up to 200 posts.
                    </span>
                  ),
                  primaryButton: {
                    label: "Upgrade",
                    onClick: () => {
                      router.push("/settings/pricing");
                    },
                  },
                  flow: "Schedule Post",
                };
              } else {
                return {
                  title: "Contact Support to Upgrade",
                  body: (
                    <span>
                      Your {tier} plan includes {scheduledPostLimit} scheduled
                      posts every month. You've already used{" "}
                      {scheduledPostLimit}/{scheduledPostLimit} scheduled posts
                      this month - please contact support to increase your
                      limit.
                    </span>
                  ),
                  primaryButton: {
                    label: "Contact Support",
                    onClick: () => {
                      openIntercomChat(
                        "I've hit my scheduled post limit and would like to upgrade my plan."
                      );
                    },
                  },
                  flow: "Schedule Post",
                };
              }
            },
          });
        },
      }
    );
  };

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header>
        <Credenza.Title>{post.title}</Credenza.Title>
      </Credenza.Header>
      <Credenza.Body>
        {accounts.length === 1 ? (
          <div className="font-body text-medium-dark-gray mb-6">
            Schedule to post automatically from{" "}
            {accounts.map((account) => (
              <span key={account.id} className="text-secondary">
                {account.name}
              </span>
            ))}
          </div>
        ) : (
          <div>
            <div className="font-body font-medium">
              Select which channels to schedule
            </div>
            <div className="pt-4 pb-9">
              <div className="flex flex-col gap-4">
                {accounts.map((account) => {
                  const isSelected =
                    !!account.id &&
                    selectedAccounts
                      .map((account) => account.id)
                      .includes(account.id);
                  const isDisabled = account.id == null;
                  return (
                    <div className="w-full" key={account.channel}>
                      <button
                        disabled={!account.id}
                        className={classNames(
                          "flex items-center outline-hidden"
                        )}
                        onClick={() => {
                          if (isSelected) {
                            setSelectedAccounts(
                              selectedAccounts.filter(
                                (selectedAccount) =>
                                  selectedAccount.id !== account.id
                              )
                            );
                          } else {
                            assert(account.id != null);
                            setSelectedAccounts([
                              ...selectedAccounts,
                              {
                                id: account.id,
                                channel: account.channel,
                              },
                            ]);
                          }
                        }}
                      >
                        <div className="flex w-full items-center gap-2">
                          <Checkbox
                            disabled={isDisabled}
                            value={
                              !!account.id &&
                              selectedAccounts
                                .map((account) => account.id)
                                .includes(account.id)
                            }
                            onChange={() => {
                              if (isSelected) {
                                setSelectedAccounts(
                                  selectedAccounts.filter(
                                    (selectedAccount) =>
                                      selectedAccount.id !== account.id
                                  )
                                );
                              } else {
                                assert(account.id != null);
                                setSelectedAccounts([
                                  ...selectedAccounts,
                                  {
                                    id: account.id,
                                    channel: account.channel,
                                  },
                                ]);
                              }
                            }}
                          />
                          <ChannelIcon
                            key={account.channel}
                            channel={account.channel}
                            width={18}
                          />
                          <div className="text-secondary">{account.name}</div>
                        </div>
                        {isDisabled && (
                          <div className="font-body text-medium-gray whitespace-nowrap">
                            No {CHANNEL_ICON_MAP[account.channel].label} account
                            connected
                          </div>
                        )}
                      </button>
                    </div>
                  );
                })}
              </div>
              <AnimatePresence>
                {missingScheduledAccount.length > 0 && (
                  <motion.div
                    className="overflow-clip"
                    initial={{ height: 0, marginTop: 0, opacity: 0 }}
                    animate={{
                      height: "auto",
                      marginTop: 16,
                      opacity: 100,
                    }}
                    exit={{ height: 0, marginTop: 0, opacity: 0 }}
                  >
                    <Alert
                      intent="none"
                      title="Unchecking a channel will unschedule the post for that channel"
                      message="Cross-posts must be scheduled at the same time. Removing a channel unschedules the post for that channel."
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        )}

        {shouldPostImmediately ? (
          <div className="font-body">
            <div className="font-medium">Post Time</div>
            <div className="flex items-center gap-1">
              <ClockIcon className="h-4 w-4" />
              <div>Immediately</div>
            </div>
            <Credenza.Footer>
              <Button
                variant="secondary"
                disabled={schedulePostMutation.isPending}
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                isLoading={schedulePostMutation.isPending}
                onClick={() => {
                  const now = new Date();

                  handleSchedulePost({
                    postDate: now,
                    postTime: now,
                  });
                }}
              >
                Post
              </Button>
            </Credenza.Footer>
          </div>
        ) : (
          <form onSubmit={handleSubmit(handleSchedulePost)}>
            <div className="font-body font-medium">Choose time to schedule</div>
            <div className="mt-4 grid grid-cols-12 gap-4">
              <div className="col-span-4 md:col-span-3">
                <FormDatePicker
                  control={control}
                  name="postDate"
                  label="Post Date"
                  minDate={new Date()}
                  placeholder="MM/DD/YYYY"
                  rules={{ required: true }}
                  disabled={shouldPostImmediately}
                />
              </div>
              <TimezoneTooltip
                date={
                  shouldPostImmediately
                    ? null
                    : getPostPublishAt(publishDate, publishTime)
                }
              >
                <div className="col-span-4 md:col-span-3">
                  <FormTimeInput
                    control={control}
                    name="postTime"
                    label="Post Time"
                    tooltipText="Post times can only be scheduled every 5 minutes"
                    rules={{ required: true }}
                    suggestedTimes={suggestedTimes}
                  />
                </div>
              </TimezoneTooltip>
            </div>
            <Credenza.Footer>
              <Button
                variant="secondary"
                disabled={schedulePostMutation.isPending}
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" isLoading={schedulePostMutation.isPending}>
                Schedule
              </Button>
            </Credenza.Footer>
          </form>
        )}
      </Credenza.Body>
    </Credenza.Root>
  );
};

export default SchedulePostModal;
