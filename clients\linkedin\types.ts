import { LinkedInPostType } from "@prisma/client";

// https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/organizations/organization-access-control-by-role?view=li-lms-2023-02&tabs=http#organization-roles
export type OrganizationRole =
  | "ADMINISTRATOR"
  | "DIRECT_SPONSORED_CONTENT_POSTER"
  | "RECRUITING_POSTER"
  | "LEAD_CAPTURE_ADMINISTRATOR"
  | "LEAD_GEN_FORMS_MANAGER"
  | "ANALYST"
  | "CURATOR"
  | "CONTENT_ADMINISTRATOR";

// https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/organizations/organization-access-control-by-role?view=li-lms-2023-02&tabs=http#role-state
export type OrganizationRoleState =
  | "APPROVED"
  | "REJECTED"
  | "REQUESTED"
  | "REVOKED";

export type Post = {
  id: string;
  publishedAt: Date;
  postCreatedAt: Date;
  lastModifiedAt: Date;
  visibility: string;
  commentary: string | null;
  authorUrn: string;
  article: {
    description: string;
    thumbnailUrn: string;
    url: string;
    title: string;
  } | null;
  celebrationUrn: string | null;
  eventUrn: string | null;
  videoUrn: string | null;
  jobUrn: string | null;
  documentUrn: string | null;
  imageUrns: string[];
  isAdvertisement: boolean;
  personTags: string[];
  personTagCount: number;
  organizationTags: string[];
  organizationTagCount: number;
  hashtags: string[];
  hashtagCount: number;
  quotedPostUrn: string | null;
  type: LinkedInPostType;
  rawResponse: object;
};

export type RawPost = {
  id: string;
  publishedAt: number | null;
  createdAt: number;
  lastModifiedAt: number;
  visibility: string;
  commentary: string;
  isReshareDisabledByAuthor: boolean;
  author: string;
  content?: {
    poll?: {
      question: string;
      options: {
        isVotedByViewer: boolean;
        voteCount: number;
        text: string;
      }[];
      settings: {
        duration: string;
        voteSelectionType: string;
        isVoterVisibleToAuthor: boolean;
      };
      uniqueVotersCount: number;
    };
    multiImage?: {
      images: {
        id: string; // urn:li:image:...
        altText?: string;
      }[];
    };
    media?: {
      id: string; // urn:li:image:... or urn:li:video:...
      title?: string;
      altText?: string;
    };
    reference?: {
      id: string; // urn:li:job:... or urn:li:event:...
    };
    article?: {
      description: string;
      thumbnail: string; // urn:li:image:...
      source: string;
      title: string;
    };
  };
  reshareContext?: {
    parent: string; // urn:li:ugcPost:...
    root: string; // urn:li:ugcPost:...
  };
  adContext?: {
    dscStatus: string;
    dscAdType: string;
    isDsc: boolean;
    dscAdAccount: string;
  };
};

export type RawLike = {
  id: string;
  actor: string;
  agent?: string;
  lastModified: {
    actor: string;
    impersonator?: string;
    time: number;
  };
  object: string;
  created: {
    actor: string;
    impersonator?: string;
    time: number;
  };
};
