import { FocusEvent, MouseEvent } from "react";

import classnames from "classnames";

type TextAreaInputProps = {
  /** Note that this must be a string, and cannot be null or undefined. */
  value: string;
  /** Called when the value of the textareainput changes */
  onChange: (value: string) => void;
  /** The number of text rows */
  rows: number;
  /** The placeholder text that will be displayed when the input is empty */
  placeholder?: string;
  /** Called when the input is clicked */
  onClick?: (e: MouseEvent<HTMLTextAreaElement>) => void;
  /** Called when the input is focused */
  onFocus?: (e: FocusEvent<HTMLTextAreaElement>) => void;
  /** Called when the input is blurred */
  onBlur?: (e: FocusEvent<HTMLTextAreaElement>) => void;
  /** Whether the input is disabled or not */
  disabled?: boolean;
  /** Whether the input is readonly or not */
  readOnly?: boolean;
  /** Whether the input is invalid or not */
  isInvalid?: boolean;
};

const TextareaInput = ({
  value,
  onChange,
  rows,
  placeholder,
  onClick,
  onFocus,
  onBlur,
  disabled = false,
  readOnly = false,
  isInvalid,
}: TextAreaInputProps) => {
  const className = classnames(
    "shadow-xs py-3 font-body-sm px-4 w-full bg-surface rounded-md border outline-hidden transition-colors",
    /** Invalid Styles */
    {
      "border-lightest-gray focus:border-secondary hover:border-secondary/50":
        !isInvalid,
    },
    { "border-danger": isInvalid },
    /** Disabled Styles */
    { "cursor-not-allowed": disabled },
    /** ReadOnly Styles */
    { "border-none shadow-none": readOnly }
  );

  return (
    <textarea
      value={value || ""}
      onChange={(e) => onChange(e.target.value)}
      rows={rows}
      className={className}
      onClick={onClick}
      onFocus={onFocus}
      onBlur={onBlur}
      placeholder={placeholder}
      readOnly={readOnly}
      disabled={disabled}
    />
  );
};

export default TextareaInput;
