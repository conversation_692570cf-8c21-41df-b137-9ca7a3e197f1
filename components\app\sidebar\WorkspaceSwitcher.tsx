import classNames from "classnames";
import sortBy from "lodash/sortBy";
import Link from "next/link";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import Avatar from "~/components/ui/Avatar";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { useFilters, useUser, useWorkspace } from "~/providers";
import { trpc } from "~/utils";

const WorkspaceSwitcher = () => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();
  const { currentWorkspace, workspaces, setCurrentWorkspace } = useWorkspace();
  const { setFilters } = useFilters();
  const { user, signOut } = useUser();

  const [workspaceProfileUrl, setWorkspaceProfileUrl] = useState<
    string | undefined
  >(currentWorkspace?.logoUrl || undefined);

  useEffect(() => {
    setWorkspaceProfileUrl(currentWorkspace?.logoUrl || undefined);
  }, [currentWorkspace?.id]);

  const workspaceName = currentWorkspace?.name;
  const initials = workspaceName?.includes(" ")
    ? workspaceName
        .split(" ")
        .map((word) => word[0])
        .join("")
        .slice(0, 2)
    : workspaceName?.slice(0, 2);

  const sortedWorkspaces = sortBy(workspaces, (workspace) =>
    workspace.name.toLocaleLowerCase()
  );

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <Avatar.Root className="border-light-gray h-8 w-8 border">
          <Avatar.Image src={workspaceProfileUrl} />
          <Avatar.Fallback
            delayMs={500}
            className="font-eyebrow text-basic rounded-full"
          >
            {initials}
          </Avatar.Fallback>
        </Avatar.Root>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="md:min-w-60" align="start">
        <DropdownMenu.Group>
          <DropdownMenu.Label>Workspaces</DropdownMenu.Label>
          {user?.agency && (
            <Link href="/agency/settings/calendar">
              <DropdownMenu.Item
                icon="UsersIcon"
                onMouseEnter={() => {
                  trpcUtils.agency.getWorkspaces.prefetch();
                }}
              >
                Agency Admin
              </DropdownMenu.Item>
            </Link>
          )}
          <DropdownMenu.Sub>
            <DropdownMenu.SubTrigger icon="RectangleStackIcon">
              Switch Workspace
            </DropdownMenu.SubTrigger>
            <DropdownMenu.Portal>
              <DropdownMenu.SubContent>
                <DropdownMenu.Label>Workspaces</DropdownMenu.Label>
                {sortedWorkspaces.map((workspace) => (
                  <DropdownMenu.Item
                    key={workspace.id}
                    className={classNames({
                      "text-secondary": currentWorkspace?.id === workspace.id,
                    })}
                    onClick={() => {
                      setCurrentWorkspace(workspace);
                      setFilters([]);
                      if (typeof window !== "undefined") {
                        window.location.href = "/";
                      }
                    }}
                  >
                    {workspace.name}
                  </DropdownMenu.Item>
                ))}
              </DropdownMenu.SubContent>
            </DropdownMenu.Portal>
          </DropdownMenu.Sub>
        </DropdownMenu.Group>
        <DropdownMenu.Separator />
        <DropdownMenu.Group>
          <DropdownMenu.Label>Settings</DropdownMenu.Label>
          <Link href="/settings/members">
            <DropdownMenu.Item
              icon="UsersIcon"
              onMouseEnter={() => {
                trpcUtils.workspace.getUsers.prefetch();
                trpcUtils.invite.getAllPending.prefetch();
              }}
            >
              Manage Users
            </DropdownMenu.Item>
          </Link>
          <Link href="/settings/general">
            <DropdownMenu.Item
              icon="Cog6ToothIcon"
              onMouseEnter={() => {
                trpcUtils.workspace.getCurrent.prefetch();
              }}
            >
              Workspace Settings
            </DropdownMenu.Item>
          </Link>
        </DropdownMenu.Group>
        <DropdownMenu.Separator />
        <DropdownMenu.Group>
          <DropdownMenu.Item
            icon="ArrowLeftOnRectangleIcon"
            onClick={async () => await signOut()}
          >
            Logout of Assembly
          </DropdownMenu.Item>
        </DropdownMenu.Group>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};

export default WorkspaceSwitcher;
