import { db } from "~/clients";

const updatePostLabels = async (
  post: {
    id: string;
    workspaceID: string;
    postLabels: {
      id: string;
    }[];
  },
  labels: {
    id: string;
    name: string;
    color: string;
    backgroundColor: string;
  }[]
) => {
  if (labels.length === 0) {
    return;
  }

  const labelNames = labels.map((label) => label.name);

  const workspaceID = post.workspaceID;

  const existingLabelRecords = await db.label.findMany({
    where: {
      workspaceID,
      name: {
        in: labelNames,
        mode: "insensitive",
      },
    },
  });

  const labelsToCreate = labels.filter(
    (label) =>
      !existingLabelRecords.some(
        (labelRecord) =>
          labelRecord.name.toUpperCase() === label.name.toUpperCase()
      )
  );

  await db.label.createMany({
    data: labelsToCreate.map((label) => ({
      ...label,
      workspaceID,
    })),
    skipDuplicates: true,
  });

  const labelRecords = await db.label.findMany({
    where: {
      workspaceID,
      name: {
        in: labelNames,
        mode: "insensitive",
      },
    },
  });

  const labelsToDelete = post.postLabels.filter(
    (postLabel) => !labels.map((label) => label.id).includes(postLabel.id)
  );

  if (labelsToDelete.length > 0) {
    await db.assetLabel.deleteMany({
      where: {
        id: {
          in: labelsToDelete.map((label) => label.id),
        },
      },
    });
  }

  await db.assetLabel.createMany({
    data: labelRecords.map((label) => ({
      postID: post.id,
      labelID: label.id,
    })),
    skipDuplicates: true,
  });
};

export default updatePostLabels;
