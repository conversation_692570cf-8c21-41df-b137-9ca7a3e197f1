import uniqBy from "lodash/uniqBy";
import { db } from "~/clients/Prisma";
import LinkedInScraper from "~/clients/rapid_api/LinkedInScraper";
import { removeEmptyElems } from "~/utils";

const sanitizeText = <T extends string | null>(text: T): T => {
  if (text === null || text === undefined) {
    return null as T;
  }
  // Remove null bytes and other problematic characters, then trim
  const sanitized = text.replace(/\0/g, "").trim();
  // Return empty string if sanitization resulted in empty string, otherwise return sanitized
  return (sanitized === "" ? text : sanitized) as T;
};

const saveScrapedProfileData = async (vanityName: string) => {
  const scraper = new LinkedInScraper();

  console.log(`Saving profile data for ${vanityName}`);

  const profileResponse = await scraper.profile.getProfile(vanityName);

  if (!profileResponse.success) {
    throw new Error("Failed to get profile data");
  }

  const profileData = profileResponse.data;

  const organizationLinkedIDs = removeEmptyElems(
    profileData.positions.map((position) => position.company.id)
  );

  const organizations = await db.linkedInAccount.findMany({
    where: {
      linkedInID: {
        in: organizationLinkedIDs,
      },
      type: "Organization",
    },
    select: {
      id: true,
      vanityName: true,
      organizationData: true,
    },
  });

  const positionsMissingOrganizations = profileData.positions.filter(
    (position) =>
      position.company.id != null &&
      !organizations.some(
        (organization) =>
          organization.vanityName === position.company.vanityName &&
          organization.organizationData != null
      )
  );

  const missingCompanies = uniqBy(
    positionsMissingOrganizations.map((position) => position.company),
    "vanityName"
  );

  console.log(`${profileData.username} - missing companies`, missingCompanies);

  const profile = await db.linkedInScrapedAccountData.upsert({
    where: {
      vanityName: profileData.username,
    },
    update: {
      vanityName: profileData.username,
      externalID: profileData.id,
      encryptedUrn: profileData.encryptedUrn,

      profileImageUrl: profileData.profileImageUrl,
      profileImageExpiresAt: profileData.profileImageExpiresAt,

      firstName: profileData.firstName,
      lastName: profileData.lastName,

      headline: profileData.headline,
      summary: profileData.summary,
      city: profileData.city,
      country: profileData.country,

      lastScrapedAt: new Date(),

      followerCount: profileData.followerCount,
    },
    create: {
      vanityName: profileData.username,
      externalID: profileData.id,
      encryptedUrn: profileData.encryptedUrn,

      profileImageUrl: profileData.profileImageUrl,
      profileImageExpiresAt: profileData.profileImageExpiresAt,

      firstName: profileData.firstName,
      lastName: profileData.lastName,

      headline: profileData.headline,
      summary: profileData.summary,
      city: profileData.city,
      country: profileData.country,

      followerCount: profileData.followerCount,
    },
  });

  for (const company of missingCompanies) {
    const linkedInID = company.id;
    const urn = `urn:li:organization:${linkedInID}`;

    if (linkedInID == null) {
      continue;
    }

    const organizationAccount = await db.linkedInAccount.upsert({
      where: {
        urn,
      },
      update: {
        name: company.name,
        type: "Organization",
        vanityName: company.vanityName,
        profileImageUrl: company.profileImageUrl,
        profileImageExpiresAt: company.profileImageExpiresAt,
      },
      create: {
        linkedInID,
        urn,
        name: company.name,
        type: "Organization",
        vanityName: company.vanityName,
        profileImageUrl: company.profileImageUrl,
        profileImageExpiresAt: company.profileImageExpiresAt,
      },
    });

    await db.linkedInOrganizationData.upsert({
      where: {
        linkedInAccountID: organizationAccount.id,
      },
      update: {
        industry: company.industry,
        staffCountRange: company.staffCountRange,
      },
      create: {
        linkedInAccountID: organizationAccount.id,
        industry: company.industry,
        staffCountRange: company.staffCountRange,
      },
    });
  }

  const allOrganizations = await db.linkedInAccount.findMany({
    where: {
      linkedInID: {
        in: organizationLinkedIDs,
      },
      type: "Organization",
    },
    select: {
      id: true,
      linkedInID: true,
      vanityName: true,
    },
  });

  await db.linkedInAccountJob.createMany({
    data: profileData.positions.map((position) => {
      const organization = allOrganizations.find(
        (organization) => organization.linkedInID === position.company.id
      );

      const organizationName = sanitizeText(position.company.name);

      return {
        accountID: profile.id,
        organizationName,
        organizationID: organization?.id,
        title: sanitizeText(position.title),
        startDate: position.startDate,
        endDate: position.endDate,
        description: sanitizeText(position.description),
        location: sanitizeText(position.location),
      };
    }),
  });

  console.log(`Saved profile data for ${profileData.username}`);
};

export default saveScrapedProfileData;
