import { Twitter<PERSON>pi } from "twitter-api-v2";
import decrypt from "./decrypt";

const getAuthedTwitterClient = (twitterIntegration: {
  accessToken: string;
  accessSecret: string;
}) => {
  const appKey = `${process.env.TWITTER_API_KEY}`;
  const appSecret = `${process.env.TWITTER_API_SECRET}`;

  const accessToken = decrypt(twitterIntegration.accessToken);
  const accessSecret = decrypt(twitterIntegration.accessSecret);

  const client = new TwitterApi({
    appKey,
    appSecret,
    accessToken,
    accessSecret,
  });

  return client;
};

export default getAuthedTwitterClient;
