import assert from "assert";
import { StatusCodes } from "http-status-codes";
import type { NextApiRequest, NextApiResponse } from "next";
import { getUserWorkspace } from "server/routers/utils";
import { db } from "~/clients/Prisma";
import createSupabaseServerClient from "~/clients/createSupabaseServerClient";
import LinkedIn from "~/clients/linkedin/LinkedIn";
import { getFullName } from "~/utils";
import { getCookie } from "~/utils/cookies";

const handleError = (
  res: NextApiResponse,
  message: string,
  referrer: string
) => {
  const params = new URLSearchParams({
    error_message: message,
  });

  return res.redirect(StatusCodes.MOVED_TEMPORARILY, `${referrer}?${params}`);
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res
      .status(StatusCodes.METHOD_NOT_ALLOWED)
      .json({ message: "Method not allowed" });
  }

  const linkedIntegrationCookies = getCookie(
    "LINKEDIN_INTEGRATION",
    req.headers.cookie
  );

  const { code, state: queryState, error_description } = req.query;

  if (
    !linkedIntegrationCookies ||
    queryState !== linkedIntegrationCookies.state
  ) {
    return handleError(
      res,
      "Session expired please try again",
      "/settings/profiles"
    );
  }

  const { accountUrn, referrer } = linkedIntegrationCookies;

  const supabase = await createSupabaseServerClient(req.headers.cookie);

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const workspace = await getUserWorkspace({
    user,
    currentWorkspaceID: getCookie("CURRENT_WORKSPACE_ID", req.headers.cookie),
  });

  if (error_description) {
    return handleError(res, error_description as string, referrer);
  }

  if (!user || !workspace.id) {
    return handleError(res, "User not logged in", referrer);
  }

  if (!accountUrn) {
    const params = new URLSearchParams({
      auth_code: code as string,
    });

    return res.redirect(
      StatusCodes.MOVED_TEMPORARILY,
      `${referrer}/choose?${params}`
    );
  } else {
    try {
      const authorizationResponse = await LinkedIn.authorize(code as string);

      const auth = authorizationResponse.data;

      if (!auth) {
        return handleError(
          res,
          authorizationResponse.errors.join(","),
          referrer
        );
      }

      const linkedin = new LinkedIn(auth.accessToken);

      const { data: linkedInUser } = await linkedin.getMe();
      const { data: organizations } = await linkedin.getOrganizations();

      assert(linkedInUser);
      assert(organizations);

      await db.linkedInIntegration.updateMany({
        where: {
          integratorAccount: {
            linkedInID: linkedInUser.id,
          },
        },
        data: {
          scope: auth.scope,
          accessToken: auth.accessToken,
          accessTokenExpiresAt: auth.expiresAt,
          refreshToken: auth.refreshToken,
          refreshTokenExpiresAt: auth.refreshTokenExpiresAt,
        },
      });

      const profiles = [
        {
          role: "ADMINISTRATOR" as const,
          state: "APPROVED" as const,
          linkedInID: linkedInUser.id,
          urn: linkedInUser.urn,
          vanityName: linkedInUser.vanityName,
          name: getFullName(linkedInUser),
          type: "User" as const,
          profileImageUrl: linkedInUser.profileImageUrl,
          profileImageExpiresAt: linkedInUser.profileImageExpiresAt,
        },
        ...organizations.map((org) => {
          return {
            role: org.role,
            state: org.state,
            linkedInID: org.id,
            urn: org.urn,
            name: org.name,
            vanityName: org.vanityName || "Organization",
            type: "Organization" as const,
            profileImageUrl: org.profileImageUrl,
            profileImageExpiresAt: org.profileImageExpiresAt,
          };
        }),
      ];

      await Promise.all(
        profiles.map((data) =>
          db.linkedInAccount.upsert({
            where: { urn: data.urn },
            update: {
              linkedInID: data.linkedInID,
              vanityName: data.vanityName,
              name: data.name,
              type: data.type,
              profileImageUrl: data.profileImageUrl,
              profileImageExpiresAt: data.profileImageExpiresAt,
            },
            create: {
              linkedInID: data.linkedInID,
              urn: data.urn,
              name: data.name,
              type: data.type,
              vanityName: data.vanityName,
              profileImageUrl: data.profileImageUrl,
              profileImageExpiresAt: data.profileImageExpiresAt,
            },
          })
        )
      );

      const profile = profiles.find((profile) => profile.urn === accountUrn);

      if (!profile) {
        const matchingUrnAccount = await db.linkedInAccount.findFirst({
          where: {
            urn: accountUrn,
          },
        });

        if (!matchingUrnAccount) {
          return handleError(
            res,
            "Switch to an account with access to the LinkedIn account first and then try again",
            referrer
          );
        } else {
          if (matchingUrnAccount.type === "User") {
            return handleError(
              res,
              `Switch to ${matchingUrnAccount.name} on LinkedIn first and then try again`,
              referrer
            );
          } else {
            return handleError(
              res,
              `Switch to an account with access to ${matchingUrnAccount.name} on LinkedIn first and then try again`,
              referrer
            );
          }
        }
      } else {
        const linkedInIntegration = await db.linkedInIntegration.findFirst({
          where: {
            workspaceID: workspace.id,
            account: {
              urn: profile.urn,
            },
          },
        });

        if (!linkedInIntegration) {
          return handleError(
            res,
            "This profile isn't connected to your workspace",
            referrer
          );
        } else {
          await db.linkedInIntegration.update({
            where: {
              id: linkedInIntegration.id,
            },
            data: {
              scope: auth.scope,
              accessToken: auth.accessToken,
              accessTokenExpiresAt: auth.expiresAt,
              refreshToken: auth.refreshToken,
              refreshTokenExpiresAt: auth.refreshTokenExpiresAt,
            },
          });
        }
      }

      const params = new URLSearchParams({
        account_name: profile.name,
        channel: "LinkedIn",
        flow: "reconnect_account",
      });

      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `${referrer}?${params}`
      );
    } catch (error) {
      console.error("LinkedIn auth error:", error);
      return handleError(res, "Error connecting LinkedIn account", referrer);
    }
  }
}
