import {
  ArrowPathRoundedSquareIcon,
  ChatBubbleLeftEllipsisIcon,
  EllipsisVerticalIcon,
  HandThumbUpIcon,
  HeartIcon,
} from "@heroicons/react/24/outline";
import { Channel, EngagementType } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import sortBy from "lodash/sortBy";
import { useRouter } from "next/router";
import { ReactNode, useState } from "react";
import { Alert, Select, TextLink, Tooltip } from "~/components";
import Avatar from "~/components/ui/Avatar";
import Button from "~/components/ui/Button";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { CHANNEL_ICON_MAP } from "~/types";
import { openConfirmationModal } from "~/utils";

type LikesCardProps = {
  likes: Engagement[];
  integrationOptions: {
    value: string;
    label: string;
    engagementPermissions?: EngagementType[];
    profilePicture: string;
    disabled?: boolean;
    disabledReason?: string | ReactNode;
  }[];
  onLikesEdit: () => void;
  onLikesDelete: () => void;
  isReadOnly?: boolean;
};

const LikesCard = ({
  likes,
  integrationOptions,
  onLikesEdit,
  onLikesDelete,
  isReadOnly,
}: LikesCardProps) => {
  return (
    <div className="group border-light-gray font-body-sm bg-lightest-gray-30 flex flex-col gap-2 rounded-[10px] border px-4 py-6">
      <div className="flex justify-between">
        <div className="flex items-center gap-1">
          <div className="flex items-center gap-2">
            <div className="bg-slate flex h-6 w-6 items-center justify-center rounded-full p-1">
              <HandThumbUpIcon className="text-basic h-3.5 w-3.5" />
            </div>
            <div className="font-medium">Auto Likes</div>
          </div>
          {!isReadOnly && (
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <div className="hover:bg-lightest-gray/50 text-basic rounded-full bg-inherit p-1 opacity-0 transition-all group-hover:opacity-100">
                  <EllipsisVerticalIcon className="h-4 w-4" />
                </div>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                <DropdownMenu.Group>
                  <DropdownMenu.Item
                    icon="PencilIcon"
                    intent="none"
                    onClick={() => onLikesEdit()}
                  >
                    Edit Likes
                  </DropdownMenu.Item>
                  <DropdownMenu.Item
                    icon="TrashIcon"
                    intent="danger"
                    onClick={() =>
                      openConfirmationModal({
                        title: `Delete Likes?`,
                        body: `This action cannot be undone. Please confirm you want to delete these likes.`,
                        cancelButton: {
                          label: "Cancel, don't delete",
                        },
                        primaryButton: {
                          label: `Delete Likes`,
                          variant: "danger",
                          onClick: () => {
                            onLikesDelete();
                          },
                        },
                      })
                    }
                  >
                    Delete Likes
                  </DropdownMenu.Item>
                </DropdownMenu.Group>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          )}
        </div>
        <span className="text-medium-dark-gray font-medium">
          within 10 mins after posting
        </span>
      </div>
      <div className="ml-8 flex items-center gap-1.5">
        <div className="font-body-sm">
          {likes.map((like, index) => {
            return (
              <span key={like.id} className="text-secondary">
                <span>
                  {
                    integrationOptions.find(
                      (option) => option.value === like.integrationID
                    )?.label
                  }
                </span>
                {index !== likes.length - 1 && ", "}
              </span>
            );
          })}
        </div>
      </div>
    </div>
  );
};

type EngagementCardProps = {
  engagement: Engagement;
  onEngagementUpdate: (
    engagementID: string,
    engagement:
      | {
          copy: string | null;
          editorState: JSONContent;
        }
      | {
          integrationID: string;
        }
      | {
          postDelaySeconds: number;
        }
  ) => void;
  onEngagementDelete: (engagementID: string) => void;
  integrationOptions: {
    value: string;
    label: string;
    engagementPermissions?: EngagementType[];
    profilePicture: string;
    disabled?: boolean;
    disabledReason?: string | ReactNode;
  }[];
  Editor: ({
    engagementID,
    initialValue,
    onSave,
    onUpdateContent,
    placeholder,
  }: {
    engagementID: string;
    initialValue: JSONContent | null;
    onSave: (editorState: JSONContent) => void;
    onUpdateContent: (textContent: string) => void;
    placeholder: string;
  }) => JSX.Element;
  accountChooserDisabled?: boolean;
  isReadOnly?: boolean;
};

const EngagementCard = ({
  engagement,
  onEngagementUpdate,
  onEngagementDelete,
  integrationOptions,
  Editor,
  accountChooserDisabled = false,
  isReadOnly,
}: EngagementCardProps) => {
  const router = useRouter();
  const [integrationID, setIntegrationID] = useState<string>(
    engagement.integrationID
  );
  const [copy, setCopy] = useState<string | null>(engagement.copy);
  const [postDelaySeconds, setPostDelaySeconds] = useState<number>(
    engagement.postDelaySeconds
  );
  const isComment = engagement.type === "Comment";

  return (
    <div className="group border-light-gray font-body-sm bg-lightest-gray-30 flex flex-col gap-2 rounded-[10px] border px-4 py-6">
      <div className="flex flex-col justify-between gap-2.5 lg:flex-row lg:gap-0">
        <div className="flex items-center gap-1">
          <div className="flex items-center gap-2">
            <Avatar.Root className="h-6 w-6">
              <Avatar.Image
                src={
                  integrationOptions.find(
                    (option) => option.value === integrationID
                  )?.profilePicture
                }
              />
              <Avatar.Fallback className="bg-secondary font-sans text-[6px] leading-none text-white">
                {integrationOptions[0].label}
              </Avatar.Fallback>
            </Avatar.Root>
            <div className="font-medium">
              {isComment ? "Auto Comment" : "Auto Repost"}
            </div>
          </div>
          {!isReadOnly && (
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <div className="hover:bg-lightest-gray/50 text-basic rounded-full bg-inherit p-1 opacity-0 transition-all group-hover:opacity-100">
                  <EllipsisVerticalIcon className="h-4 w-4" />
                </div>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                <DropdownMenu.Group>
                  <DropdownMenu.Item
                    icon="TrashIcon"
                    intent="danger"
                    onClick={() =>
                      openConfirmationModal({
                        title: `Delete ${isComment ? "Comment" : "Repost"}?`,
                        body: `This action cannot be undone. Please confirm you want to delete this ${
                          isComment ? "comment" : "repost"
                        }.`,
                        cancelButton: {
                          label: "Cancel, don't delete",
                        },
                        primaryButton: {
                          label: `Delete ${isComment ? "Comment" : "Repost"}`,
                          variant: "danger",
                          onClick: () => {
                            onEngagementDelete(engagement.id);
                          },
                        },
                      })
                    }
                  >
                    {isComment ? "Delete Comment" : "Delete Repost"}
                  </DropdownMenu.Item>
                </DropdownMenu.Group>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          )}
        </div>
        <div className="flex flex-wrap items-center gap-1.5">
          {isReadOnly || accountChooserDisabled ? (
            <div className="font-body-sm text-secondary">
              {
                integrationOptions.find(
                  (option) => option.value === integrationID
                )?.label
              }
            </div>
          ) : (
            <Select
              value={integrationID}
              options={integrationOptions.filter(
                (option) =>
                  option.engagementPermissions == null ||
                  option.engagementPermissions.includes(engagement.type)
              )}
              disabled={isReadOnly || accountChooserDisabled}
              onChange={(newIntegrationID) => {
                onEngagementUpdate(engagement.id, {
                  integrationID: newIntegrationID,
                });
                setIntegrationID(newIntegrationID);
              }}
              actionButton={{
                icon: "PlusIcon",
                label: "Connect another profile",
                onClick: () => {
                  router.push("/settings/profiles");
                },
              }}
            />
          )}
          <Select
            value={postDelaySeconds}
            disabled={isReadOnly}
            hideSelectedOption={true}
            options={[
              {
                value: 0,
                label: "immediately",
              },
              {
                value: 5 * 60,
                label: "5 minutes",
              },
              {
                value: 10 * 60,
                label: "10 minutes",
              },
              {
                value: 30 * 60,
                label: "30 minutes",
              },
              {
                value: 60 * 60,
                label: "1 hour",
              },
              {
                value: 4 * 60 * 60,
                label: "4 hours",
              },
              {
                value: 8 * 60 * 60,
                label: "8 hours",
              },
              {
                value: 12 * 60 * 60,
                label: "12 hours",
              },
              {
                value: 24 * 60 * 60,
                label: "24 hours",
              },
            ]}
            onChange={(delay) => {
              onEngagementUpdate(engagement.id, {
                postDelaySeconds: delay,
              });
              setPostDelaySeconds(delay);
            }}
          />
          <span className="text-medium-dark-gray font-medium">
            after posting
          </span>
        </div>
      </div>
      <div className="lg:ml-8">
        {Editor({
          engagementID: engagement.id,
          initialValue: engagement.editorState,
          onSave: (editorState) => {
            onEngagementUpdate(engagement.id, {
              copy,
              editorState,
            });
          },
          onUpdateContent: (textContent) => {
            setCopy(textContent);
          },
          placeholder: isComment
            ? "Write the content of your comment here..."
            : "(Optional) Write a caption for your repost here...",
        })}
      </div>
    </div>
  );
};

type Engagement = {
  id: string;
  type: EngagementType;
  copy: string | null;
  editorState: JSONContent | null;
  integrationID: string;
  postDelaySeconds: number;
  createdAt: Date;
};

type Props = {
  engagements?: Engagement[];
  onEngagementCreate: (engagementType: EngagementType) => void;
  onEngagementUpdate: (
    engagementID: string,
    engagement:
      | {
          copy: string | null;
          editorState: JSONContent;
        }
      | {
          integrationID: string;
        }
      | {
          postDelaySeconds: number;
        }
  ) => void;
  onEngagementDelete: (engagementID: string) => void;
  disableReposts?: boolean;
  enableLikes?: boolean;
  integrationOptions: {
    value: string;
    label: string;
    engagementPermissions?: EngagementType[];
    profilePicture: string;
  }[];
  Editor: ({
    engagementID,
    initialValue,
    onSave,
    onUpdateContent,
    placeholder,
  }: {
    engagementID: string;
    initialValue: JSONContent | null;
    onSave: (editorState: JSONContent) => void;
    onUpdateContent: (textContent: string) => void;
    placeholder: string;
  }) => JSX.Element;
  channel: Channel;
  accountChooserDisabled?: boolean;
  isReadOnly?: boolean;
  infoBanner?: {
    title: string;
    message: string | ReactNode;
  } | null;
};

const PostEngagementsNewDesign = ({
  engagements = [],
  onEngagementCreate,
  onEngagementUpdate,
  onEngagementDelete,
  disableReposts = false,
  enableLikes,
  integrationOptions,
  Editor,
  channel,
  accountChooserDisabled,
  isReadOnly,
  infoBanner,
}: Props) => {
  const router = useRouter();

  const likes = engagements.filter((engagement) => engagement.type === "Like");
  const otherEngagements = engagements.filter(
    (engagement) => engagement.type !== "Like"
  );

  const hasSchedulingProfile = integrationOptions.some(
    (option) =>
      option.engagementPermissions == null ||
      (option.engagementPermissions.includes("Like") &&
        option.engagementPermissions.includes("Comment") &&
        option.engagementPermissions.includes("Repost"))
  );

  return (
    <div>
      <div
        className={classNames("font-body-sm font-medium", {
          "pb-5": engagements.length > 0,
          "pb-1.5": engagements.length === 0,
        })}
      >
        Automations
      </div>
      <div className="flex flex-col">
        <AnimatePresence initial={false}>
          {engagements.length === 0 && (
            <motion.div
              key="engagemnt-description"
              className="font-body-sm text-medium-dark-gray"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 100 }}
              exit={{ height: 0, opacity: 0 }}
            >
              Automations like auto-engagements create automated actions after
              your post goes live to boost performance.
            </motion.div>
          )}
          {!hasSchedulingProfile ? (
            <motion.div
              key="no-accounts"
              className="font-body-sm text-medium-dark-gray"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 100 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <TextLink
                href="/settings/profiles"
                value={`Connect a ${CHANNEL_ICON_MAP[channel].label} scheduling profile`}
              />{" "}
              to Assembly in order to use auto-engagement features.
            </motion.div>
          ) : (
            <>
              {sortBy(otherEngagements, "createdAt").map(
                (engagement, index) => {
                  return (
                    <motion.div
                      key={engagement.id}
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 100 }}
                      exit={{ height: 0, opacity: 0 }}
                    >
                      <EngagementCard
                        engagement={engagement}
                        onEngagementUpdate={onEngagementUpdate}
                        onEngagementDelete={onEngagementDelete}
                        integrationOptions={integrationOptions}
                        Editor={Editor}
                        accountChooserDisabled={accountChooserDisabled}
                        isReadOnly={isReadOnly}
                      />
                      {index < otherEngagements.length - 1 ||
                      likes.length > 0 ? (
                        <div className="bg-light-gray ml-3 flex h-4 w-px justify-start" />
                      ) : null}
                    </motion.div>
                  );
                }
              )}
              {likes.length > 0 && (
                <motion.div
                  key="likes-card"
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 100 }}
                  exit={{ height: 0, opacity: 0 }}
                >
                  <LikesCard
                    likes={likes}
                    integrationOptions={integrationOptions.filter(
                      (option) =>
                        option.engagementPermissions == null ||
                        option.engagementPermissions.includes("Like")
                    )}
                    onLikesEdit={() => {
                      onEngagementCreate("Like");
                    }}
                    onLikesDelete={() => {
                      onEngagementDelete(likes[0].id);
                    }}
                  />
                </motion.div>
              )}
              {!isReadOnly && (
                <div className="mt-5">
                  {!!infoBanner ? (
                    <Alert
                      intent="none"
                      title={infoBanner.title}
                      message={infoBanner.message}
                    />
                  ) : (
                    <div className="flex flex-wrap items-center gap-2 lg:flex-nowrap">
                      {enableLikes != null && (
                        <Tooltip
                          value={
                            <>
                              {enableLikes === false ? (
                                <div>
                                  <div className="font-body-sm mb-1.5">
                                    Auto-likes are only available on{" "}
                                    <span className="font-medium">
                                      Standard
                                    </span>{" "}
                                    workspace plans and above.
                                  </div>
                                  <Button
                                    variant="secondary"
                                    size="sm"
                                    onClick={() => {
                                      router.push("/settings/pricing");
                                    }}
                                  >
                                    See Plans
                                  </Button>
                                </div>
                              ) : likes.length > 0 ? (
                                "You can only add one set of auto-likes per post. Please edit the existing “Likes” section to make any changes."
                              ) : null}
                            </>
                          }
                        >
                          <Button
                            onClick={() => {
                              onEngagementCreate("Like");
                            }}
                            disabled={likes.length > 0 || enableLikes === false}
                            variant="ghost"
                            size="sm"
                          >
                            <HeartIcon className="h-4 w-4" />
                            Auto Likes
                          </Button>
                        </Tooltip>
                      )}
                      <Button
                        onClick={() => {
                          onEngagementCreate("Comment");
                        }}
                        variant="ghost"
                        size="sm"
                      >
                        <ChatBubbleLeftEllipsisIcon className="h-4 w-4" />
                        Auto Comment
                      </Button>
                      {!disableReposts && (
                        <Button
                          onClick={() => {
                            onEngagementCreate("Repost");
                          }}
                          variant="ghost"
                          size="sm"
                        >
                          <ArrowPathRoundedSquareIcon className="h-4 w-4" />
                          Auto Repost
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default PostEngagementsNewDesign;
