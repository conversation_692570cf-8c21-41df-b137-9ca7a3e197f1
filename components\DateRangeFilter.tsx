import { ArrowsRightLeftIcon } from "@heroicons/react/24/outline";

import { DateTime } from "luxon";
import { ReactNode } from "react";
import { cn } from "~/utils";
import DatePicker from "./DatePicker";
import Label from "./Label";
import Tooltip from "./Tooltip";
import Popover from "./ui/Popover";

export type Preset = {
  startDate: Date;
  endDate: Date;
  label: string;
  disabledReason?: string | ReactNode;
};

type Props<T extends boolean = false> = {
  startDate?: Date;
  onStartDateChange: (date: T extends true ? Date | null : Date) => void;
  endDate?: Date;
  onEndDateChange: (date: T extends true ? Date | null : Date) => void;
  isClearable?: T;
  minDate?: Date;
  maxDate?: Date;
  presets?: Preset[];
};

const DateRangeFilter = <T extends boolean = false>({
  startDate,
  onStartDateChange,
  endDate,
  onEndDateChange,
  isClearable,
  minDate,
  maxDate,
  presets,
}: Props<T>) => {
  let filterText = "Date Filter";
  if (startDate && endDate == null) {
    const startDateTime = DateTime.fromJSDate(startDate);
    filterText = startDateTime.hasSame(DateTime.now(), "day")
      ? "After today"
      : `After ${startDateTime.toFormat("M/d")}`;
  } else if (startDate == null && endDate) {
    filterText = `Before ${DateTime.fromJSDate(endDate).toFormat("M/d/yy")}`;
  } else if (startDate && endDate) {
    filterText = `${DateTime.fromJSDate(startDate).toFormat(
      "M/d/yy"
    )} - ${DateTime.fromJSDate(endDate).toFormat("M/d/yy")}`;
  }

  const matchingPreset = presets?.find(
    (preset) =>
      startDate &&
      DateTime.fromJSDate(preset.startDate).toFormat("yyyy-MM-dd") ===
        DateTime.fromJSDate(startDate).toFormat("yyyy-MM-dd") &&
      endDate &&
      DateTime.fromJSDate(preset.endDate).toFormat("yyyy-MM-dd") ===
        DateTime.fromJSDate(endDate).toFormat("yyyy-MM-dd")
  );

  const handleStartDateChange = (date: Date | null) => {
    if (date === null && !isClearable) return;
    onStartDateChange(date as T extends true ? Date | null : Date);
  };

  const handleEndDateChange = (date: Date | null) => {
    if (date === null && !isClearable) return;
    onEndDateChange(date as T extends true ? Date | null : Date);
  };

  return (
    <Popover.Root>
      <Popover.Trigger className="font-body-sm bg-slate text-basic hover:bg-basic hover:text-basic-light data-[state=open]:bg-basic data-[state=open]:text-basic-light flex items-center gap-1 rounded-md px-1.5 py-1 transition-colors">
        <ArrowsRightLeftIcon className="h-4 w-4" />
        {matchingPreset ? (
          <span>
            {matchingPreset.label} ({filterText})
          </span>
        ) : (
          <span>{filterText}</span>
        )}
      </Popover.Trigger>
      <Popover.Content>
        <div className="grid w-52 grid-cols-2 gap-4">
          <Label value="From">
            <DatePicker
              value={startDate}
              onChange={handleStartDateChange}
              isClearable={isClearable}
              minDate={minDate}
              maxDate={maxDate}
            />
          </Label>
          <Label value="To">
            <DatePicker
              value={endDate}
              onChange={handleEndDateChange}
              isClearable={isClearable}
              minDate={minDate}
              maxDate={maxDate}
            />
          </Label>
        </div>
        {presets && (
          <div className="flex flex-col items-start gap-1.5 pt-4">
            {presets.map((preset) => (
              <Tooltip key={preset.label} value={preset.disabledReason}>
                <button
                  disabled={!!preset.disabledReason}
                  className={cn(
                    "font-body-sm w-full rounded-md px-2 py-0.5 text-start",
                    !!preset.disabledReason && "opacity-30",
                    !preset.disabledReason && "hover:bg-lightest-gray-30"
                  )}
                  onClick={() => {
                    onStartDateChange(preset.startDate);
                    onEndDateChange(preset.endDate);
                  }}
                >
                  <span className="mr-3">{preset.label}</span>
                  <span className="text-medium-gray">
                    {DateTime.fromJSDate(preset.startDate).toFormat("LL/dd")} -{" "}
                    {DateTime.fromJSDate(preset.endDate).toFormat("LL/dd")}
                  </span>
                </button>
              </Tooltip>
            ))}
          </div>
        )}
      </Popover.Content>
    </Popover.Root>
  );
};

export default DateRangeFilter;
