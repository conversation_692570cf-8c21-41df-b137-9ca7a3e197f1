import { ReactNode } from "react";

import Loader from "./Loader";

import colors from "~/styles/colors";

type Props = {
  blocking?: boolean;
  children: ReactNode | string;
  loaderColor?: keyof typeof colors;
};

const UILoader = ({ blocking, children, loaderColor = "basic" }: Props) => {
  if (!blocking) {
    return <>{children}</>;
  } else {
    return (
      <div className="pointer-events-none relative w-full opacity-50">
        {blocking && (
          <div className="absolute top-1/2 left-1/2 -translate-x-6 -translate-y-6">
            <Loader size="lg" color={loaderColor} />
          </div>
        )}
        {children}
      </div>
    );
  }
};

export default UILoader;
