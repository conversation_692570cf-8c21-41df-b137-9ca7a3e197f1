import completeOnboardingTask from "apiUtils/completeOnboardingTask";
import countSocialProfiles from "apiUtils/countSocialProfiles";
import addSubscriptionItem from "apiUtils/stripe/addSubscriptionItem";
import { google } from "googleapis";
import { StatusCodes } from "http-status-codes";
import type { NextApiRequest, NextApiResponse } from "next";
import getUserWorkspaceWithSocialProfileUsage from "server/routers/utils/getUserWorkspaceWithSocialProfileUsage";
import { db, prisma, slack } from "~/clients";
import createSupabaseServerClient from "~/clients/createSupabaseServerClient";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { getCookie } from "~/utils/cookies";

const handleError = (
  res: NextApiResponse,
  message: string,
  referrer: string
) => {
  const params = new URLSearchParams({
    error_message: message,
    channel: "YouTube",
  });

  return res.redirect(StatusCodes.MOVED_TEMPORARILY, `${referrer}?${params}`);
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res
      .status(StatusCodes.METHOD_NOT_ALLOWED)
      .json({ message: "Method not allowed" });
  }

  const { code } = req.query;

  const supabase = await createSupabaseServerClient(req.headers.cookie);

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const workspace = await getUserWorkspaceWithSocialProfileUsage({
    user,
    currentWorkspaceID: getCookie("CURRENT_WORKSPACE_ID", req.headers.cookie),
  });

  const youtubeIntegrationCookie = getCookie(
    "YOUTUBE_INTEGRATION",
    req.headers.cookie
  );

  if (!youtubeIntegrationCookie) {
    return handleError(
      res,
      "Session expired, please try again",
      "/settings/profiles"
    );
  }

  const { accountID, referrer } = youtubeIntegrationCookie;

  if (!user || !workspace) {
    return handleError(res, "User not logged in", referrer);
  }

  try {
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/youtube/callback`
    );

    const { tokens } = await oauth2Client.getToken(code as string);
    oauth2Client.setCredentials(tokens);

    google.options({
      auth: oauth2Client,
    });

    const youtube = google.youtube("v3");

    const response = await youtube.channels.list({
      mine: true,
      part: ["id", "snippet", "contentDetails"],
    });

    const account = response.data.items?.[0];

    if (!account) {
      return handleError(res, "No account found", referrer);
    }

    const externalID = account.id as string;
    const channelName = account.snippet?.title as string;
    const channelUsername = (account.snippet?.customUrl as string).replace(
      "@",
      ""
    );
    const channelProfileImageUrl = account.snippet?.thumbnails?.high
      ?.url as string;

    const existingYouTubeIntegration = await db.youTubeIntegration.findFirst({
      where: {
        accountID: externalID,
      },
      include: {
        user: true,
        channel: true,
      },
    });

    if (accountID && accountID !== externalID) {
      const expectedYouTubeIntegration = await db.youTubeIntegration.findFirst({
        where: {
          accountID,
        },
        select: {
          channel: {
            select: {
              username: true,
            },
          },
        },
      });

      if (expectedYouTubeIntegration) {
        return handleError(
          res,
          `Please choose @${expectedYouTubeIntegration.channel.username} when connecting and then try again`,
          referrer
        );
      } else {
        return handleError(
          res,
          "Please choose the account you want to reconnect and try again",
          referrer
        );
      }
    }

    let youTubeIntegration = existingYouTubeIntegration;

    const youTubeChannel = await db.youTubeChannel.upsert({
      where: {
        externalID: externalID,
      },
      update: {
        name: channelName,
        username: channelUsername,
        profileImageUrl: channelProfileImageUrl,
      },
      create: {
        externalID: externalID,
        name: channelName,
        username: channelUsername,
        profileImageUrl: channelProfileImageUrl,
      },
    });

    if (existingYouTubeIntegration) {
      if (existingYouTubeIntegration.workspaceID !== workspace.id) {
        return handleError(
          res,
          `@${existingYouTubeIntegration.channel.username} already connected to another workspace`,
          referrer
        );
      }

      youTubeIntegration = await db.youTubeIntegration.update({
        where: {
          id: existingYouTubeIntegration.id,
        },
        data: {
          accountID: externalID,

          accessToken: tokens.access_token as string,
          expiresAt: new Date(tokens.expiry_date as number),
          refreshToken: tokens.refresh_token as string,
          idToken: tokens.id_token as string,
          scope: (tokens.scope as string).split(" "),
          isReintegrationRequired: false,

          channelID: youTubeChannel.id,
        },
        include: {
          user: true,
          channel: true,
        },
      });
    } else {
      const { subscription } = workspace;

      if (subscription) {
        const numSocialProfiles = countSocialProfiles(workspace);

        if (numSocialProfiles >= subscription.socialProfileLimit) {
          await addSubscriptionItem({
            workspaceID: workspace.id,
            itemName: "ADDITIONAL_SOCIAL_PROFILE",
            quantity: numSocialProfiles - subscription.socialProfileLimit + 1,
          });
        }
      }

      youTubeIntegration = await db.youTubeIntegration.create({
        data: {
          accountID: externalID,

          accessToken: tokens.access_token as string,
          expiresAt: new Date(tokens.expiry_date as number),
          refreshToken: tokens.refresh_token as string,
          idToken: tokens.id_token as string,
          scope: (tokens.scope as string).split(" "),

          userID: user.id,
          workspaceID: workspace.id,

          channelID: youTubeChannel.id,
        },
        include: {
          user: true,
          channel: true,
        },
      });

      posthog.capture({
        distinctId: user.id,
        event: POSTHOG_EVENTS.socialAccount.connected,
        properties: {
          channel: "YouTube",
        },
        groups: {
          workspace: workspace.id,
          company: workspace.company.id,
        },
      });

      await slack.newConnectedAccount({
        username: `@${channelUsername}`,
        channel: "YouTubeShorts",
        url: `https://www.youtube.com/@${channelUsername}`,
        workspace,
        connectedBy: youTubeIntegration.user,
      });

      const deletedYouTubeIntegrations =
        await prisma.youTubeIntegration.findMany({
          where: {
            workspaceID: workspace.id,
            accountID: youTubeIntegration.accountID,
            deletedTimeStamp: {
              not: 0,
            },
          },
        });

      await db.post.updateMany({
        where: {
          youTubeIntegrationID: {
            in: deletedYouTubeIntegrations.map((integration) => integration.id),
          },
        },
        data: {
          youTubeIntegrationID: youTubeIntegration.id,
        },
      });

      if (workspace._count.youTubeIntegrations === 0) {
        await db.postDefault.upsert({
          where: {
            workspaceID: workspace.id,
          },
          create: {
            workspaceID: workspace.id,
            youTubeIntegrationID: youTubeIntegration.id,
          },
          update: {
            youTubeIntegrationID: youTubeIntegration.id,
          },
        });
      }
    }

    await completeOnboardingTask({
      workspaceID: workspace.id,
      userID: user.id,
      taskType: "CONNECT_ACCOUNT",
      state: "COMPLETED",
    });

    const params = new URLSearchParams({
      account_name: `@${youTubeChannel.username}`,
      channel: "YouTube",
      flow: accountID
        ? "reconnect_account"
        : existingYouTubeIntegration
          ? "existing_account"
          : "new_account",
    });

    if (referrer.startsWith("/settings/profiles")) {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/profiles?${params}`
      );
    } else {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/youtube?${params}`
      );
    }
  } catch (error) {
    console.error("YouTube auth error:", error);
    return handleError(res, "Error connecting YouTube account", referrer);
  }
}
