import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";
import { inngest } from "~/clients/inngest/inngest";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const scheduledPosts = await db.scheduledPost.findMany({
    where: {
      channel: "Instagram",
      status: "Scheduled",
      publishAt: {
        gte: new Date(),
      },
    },
    select: {
      id: true,
      postID: true,
      publishAt: true,
    },
  });

  await inngest.send(
    scheduledPosts.map((post) => ({
      name: "post.scheduled",
      data: {
        postID: post.postID,
        channel: "Instagram",
        delayUntil: post.publishAt,
      },
    }))
  );

  res.status(200).send({
    numPosts: scheduledPosts.length,
  });
};

export default handler;
