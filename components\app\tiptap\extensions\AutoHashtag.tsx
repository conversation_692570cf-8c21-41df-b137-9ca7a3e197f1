import { Extension } from "@tiptap/core";
import { Plugin, Plugin<PERSON><PERSON> } from "prosemirror-state";
import { Decoration, DecorationSet } from "prosemirror-view";
import { HASHTAG_REGEX } from "~/constants";

type HashtagOptions = {
  urlFn: (hashtag: string) => string;
  hashtagRegex?: RegExp;
};

const AutoHashtag = Extension.create<HashtagOptions>({
  name: "autoHashtag",

  addOptions() {
    return {
      urlFn: (hashtag: string) => "",
      hashtagRegex: HASHTAG_REGEX,
    };
  },

  addProseMirrorPlugins() {
    const urlFn = this.options.urlFn;

    if (!urlFn) {
      throw new Error("urlFn is required");
    }

    return [
      new Plugin({
        key: new PluginKey("autoHashtag"),
        props: {
          decorations: (state) => {
            const { doc } = state;
            const decorations: Decoration[] = [];

            doc.descendants((node, pos) => {
              if (!node.isText) return;

              const text = node.text || "";
              let match;

              while ((match = this.options.hashtagRegex!.exec(text)) !== null) {
                const start = pos + match.index;
                const end = start + match[0].length;
                const hashtag = match[0].substring(1);

                decorations.push(
                  Decoration.inline(start, end, {
                    class: "text-secondary cursor-pointer",
                    onclick: `window.open('${urlFn(hashtag)}', '_blank')`,
                  })
                );
              }
            });

            return DecorationSet.create(doc, decorations);
          },
        },
      }),
    ];
  },
});

export default AutoHashtag;
