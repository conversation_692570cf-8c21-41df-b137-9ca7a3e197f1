import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForYouTubeShorts = async (post: {
  id: string;
  workspaceID: string;
  youTubeShortsContent: {
    id: string;
  } | null;
}): Promise<{ asset: AssetType | null }> => {
  const youTubeShortsContent = post.youTubeShortsContent;

  if (!youTubeShortsContent) {
    return {
      asset: null,
    };
  }

  const assetsMap = await getAssetsForFilePaths([
    {
      id: youTubeShortsContent.id,
      path: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["YouTubeShorts"].slug}/${youTubeShortsContent.id}`,
    },
  ]);

  return {
    asset: assetsMap[youTubeShortsContent.id][0],
  };
};

export default getStorageAssetsForYouTubeShorts;
