import { KnockWorkflow } from "~/types";

const defaultKnockPreferences: {
  [workflow in KnockWorkflow]: {
    channel_types: {
      in_app_feed: boolean;
      email: boolean;
      chat: boolean;
    };
  };
} = {
  "tagged-in-comment": {
    channel_types: {
      in_app_feed: true,
      email: true,
      chat: true,
    },
  },
  "comment-on-assigned-approval-content": {
    channel_types: {
      in_app_feed: true,
      email: true,
      chat: false,
    },
  },

  "requested-approval": {
    channel_types: {
      in_app_feed: true,
      email: true,
      chat: true,
    },
  },
  "approval-due-today": {
    channel_types: {
      chat: false,
      email: true,
      in_app_feed: true,
    },
  },
  "content-approved": {
    channel_types: {
      in_app_feed: true,
      email: true,
      chat: true,
    },
  },
  "post-missing-required-approvals": {
    channel_types: {
      in_app_feed: true,
      email: true,
      chat: true,
    },
  },

  "post-posted": {
    channel_types: {
      in_app_feed: true,
      email: true,
      chat: false,
    },
  },
  "post-errored": {
    channel_types: {
      in_app_feed: true,
      email: true,
      chat: true,
    },
  },
};

export default defaultKnockPreferences;
