import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const getAuthUser = async (ctx: {
  req: NextApiRequest;
  res: NextApiResponse;
}) => {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return ctx.req.cookies[name];
        },
        set(name: string, value: string, options: CookieOptions) {
          ctx.res.setHeader(
            "Set-Cookie",
            `${name}=${value}; Path=${options.path || "/"}`
          );
        },
        remove(name: string, options: CookieOptions) {
          ctx.res.setHeader(
            "Set-Cookie",
            `${name}=; Path=${options.path || "/"}; Max-Age=0`
          );
        },
      },
    }
  );

  const { data } = await supabase.auth.getUser();

  const publicUser = await db.user.findFirstOrThrow({
    where: {
      email: data.user?.email,
    },
  });

  return {
    ...publicUser,
  };
};

export default getAuthUser;
