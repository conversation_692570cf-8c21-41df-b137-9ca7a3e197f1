import axios from "axios";
import { StatusCodes } from "http-status-codes";
import { createErrorResponse } from "../../Response";
import catchFacebookError from "../catchFacebookError";

jest.mock("../../Response", () => ({
  createErrorResponse: jest
    .fn()
    .mockImplementation((messages, status, data, isAuthError) => ({
      success: false,
      messages,
      status,
      data,
      isAuthError,
    })),
}));

console.error = jest.fn();

jest.mock("axios", () => ({
  isAxiosError: jest.fn(),
}));

describe("catchFacebookError", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should handle standard Facebook API errors", () => {
    const mockAxiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.BAD_REQUEST,
        data: {
          error: {
            message: "Test error message",
            type: "StandardException",
            code: 100,
            fbtrace_id: "A-ruqVbGipmrXbej9wlGpuu",
          },
        },
      },
    };

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchFacebookError(mockAxiosError);

    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Test error message"],
      StatusCodes.BAD_REQUEST,
      mockAxiosError.response.data,
      false
    );
    expect(result.success).toBe(false);
  });

  it("should handle Facebook API errors with user title and message", () => {
    const mockAxiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.BAD_REQUEST,
        data: {
          error: {
            message: "Original message",
            error_user_title: "User title",
            error_user_msg: "User message",
            type: "RegularException",
          },
        },
      },
    };

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchFacebookError(mockAxiosError);

    expect(createErrorResponse).toHaveBeenCalledWith(
      ["User title. User message"],
      StatusCodes.BAD_REQUEST,
      mockAxiosError.response.data,
      false
    );
  });

  it("should handle OAuth exceptions", () => {
    const errorMessage =
      "Error validating access token: The user has not authorized application 1908652879642937.";
    const mockAxiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.UNAUTHORIZED,
        data: {
          error: {
            message: errorMessage,
            type: "OAuthException",
            code: 190,
            fbtrace_id: "A-ruqVbGipmrXbej9wlGpuu",
            is_transient: false,
          },
        },
      },
    };

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchFacebookError(mockAxiosError);

    expect(createErrorResponse).toHaveBeenCalledWith(
      [errorMessage],
      StatusCodes.UNAUTHORIZED,
      mockAxiosError.response.data,
      true
    );
  });

  it("should handle transient OAuth exceptions", () => {
    const errorMessage =
      "An unexpected error has occurred. Please retry your request later.";
    const mockAxiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.UNAUTHORIZED,
        data: {
          error: {
            message: errorMessage,
            type: "OAuthException",
            code: 190,
            is_transient: true,
          },
        },
      },
    };

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchFacebookError(mockAxiosError);

    expect(createErrorResponse).toHaveBeenCalledWith(
      [errorMessage],
      StatusCodes.UNAUTHORIZED,
      mockAxiosError.response.data,
      false
    );
  });

  it("should handle when no is_transient value is returned", () => {
    const errorMessage = "An unknown error has occurred.";

    const mockAxiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.BAD_REQUEST,
        data: {
          error: {
            message: errorMessage,
            type: "OAuthException",
            code: 190,
          },
        },
      },
    };

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchFacebookError(mockAxiosError);

    expect(createErrorResponse).toHaveBeenCalledWith(
      [errorMessage],
      StatusCodes.BAD_REQUEST,
      mockAxiosError.response.data,
      true
    );
  });

  it("should replace private account error message", () => {
    const mockAxiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.BAD_REQUEST,
        data: {
          error: {
            message: "Original message",
            error_user_title:
              "Cannot load user with a private account or invalid username",
            error_user_msg: "User message",
          },
        },
      },
    };

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchFacebookError(mockAxiosError);

    expect(createErrorResponse).toHaveBeenCalledWith(
      [
        "Cannot tag or collaborate with a user with a private account or invalid username. User message",
      ],
      StatusCodes.BAD_REQUEST,
      mockAxiosError.response.data,
      false
    );
  });

  it('should handle code 1 errors with "An unknown error occurred"', () => {
    const mockAxiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.BAD_REQUEST,
        data: {
          error: {
            message: "An unknown error occurred",
            error_user_title: "Error title",
            error_user_msg: "An unknown error occurred",
            code: 1,
          },
        },
      },
    };

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchFacebookError(mockAxiosError);

    expect(createErrorResponse).toHaveBeenCalledWith(
      [
        "Error title. Possibly a temporary issue due to downtime. Wait and retry the operation",
      ],
      StatusCodes.BAD_REQUEST,
      mockAxiosError.response.data,
      false
    );
  });

  it("should handle non-axios errors", () => {
    const error = new Error("Generic error");

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(false);

    const result = catchFacebookError(error);

    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Error making query"],
      StatusCodes.UNPROCESSABLE_ENTITY
    );
  });

  it("should handle errors thrown during error processing", () => {
    const mockAxiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.BAD_REQUEST,
        // Missing data property to trigger an error
      },
    };

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchFacebookError(mockAxiosError);

    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Error making query"],
      StatusCodes.UNPROCESSABLE_ENTITY
    );
    expect(console.error).toHaveBeenCalledWith(
      "Error catching facebook error",
      expect.any(Error)
    );
  });

  it("should handle missing error type", () => {
    const mockAxiosError = {
      isAxiosError: true,
      response: {
        status: StatusCodes.BAD_REQUEST,
        data: {
          error: {
            message: "Test error message",
            // Missing type property
          },
        },
      },
    };

    (axios.isAxiosError as unknown as jest.Mock).mockReturnValue(true);

    const result = catchFacebookError(mockAxiosError);

    expect(createErrorResponse).toHaveBeenCalledWith(
      ["Test error message"],
      StatusCodes.BAD_REQUEST,
      mockAxiosError.response.data,
      false
    );
  });
});
