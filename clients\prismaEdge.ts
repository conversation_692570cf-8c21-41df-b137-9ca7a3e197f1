import { PrismaClient } from "@prisma/client/edge";

const globalForPrisma = global as unknown as { prismaEdge: PrismaClient };

declare global {
  // allow global `var` declarations
  // eslint-disable-next-line no-var
  var prismaEdge: PrismaClient | undefined;
}

export const prismaEdge =
  globalForPrisma.prismaEdge ||
  new PrismaClient({
    log: ["error"],
  });

if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prismaEdge = prismaEdge;
}

export default prismaEdge;
