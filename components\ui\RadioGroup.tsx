import * as RadioGroupPrimitive from "@radix-ui/react-radio-group";
import * as React from "react";

import { cn } from "~/utils";

const Root = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Root
      className={cn("flex flex-wrap gap-x-3 gap-y-2", className)}
      {...props}
      ref={ref}
    />
  );
});
Root.displayName = RadioGroupPrimitive.Root.displayName;

const Item = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>
>(({ className, value, children, ...props }, ref) => {
  return (
    <div className={cn("flex items-center gap-1.5", className)}>
      <RadioGroupPrimitive.Item
        id={value}
        value={value}
        ref={ref}
        className="border-medium-gray enabled:hover:border-secondary data-[state=checked]:border-secondary aspect-square h-3 w-3 rounded-full border transition-colors disabled:cursor-not-allowed disabled:opacity-50"
        {...props}
      >
        <RadioGroupPrimitive.Indicator className="flex grow-0 items-center justify-center">
          <div className="bg-secondary h-1.5 w-1.5 rounded-full transition-opacity data-[state=checked]:opacity-100 data-[state=unchecked]:opacity-0" />
        </RadioGroupPrimitive.Indicator>
      </RadioGroupPrimitive.Item>
      <label
        htmlFor={value}
        className={cn("font-body-sm w-full cursor-pointer text-black", {
          "cursor-not-allowed opacity-50": props.disabled,
        })}
      >
        {children}
      </label>
    </div>
  );
});
Item.displayName = RadioGroupPrimitive.Item.displayName;

export default { Root, Item };
