// Copies files recursively from Supabase storage path to another
// Using the Supabase Storage API: https://supabase.com/docs/reference/javascript/storage-from-remove

import supabaseAdminClient from "~/clients/supabaseAdminClient";
import { ONE_YEAR_IN_SECONDS } from "~/constants";

import { Channel } from "@prisma/client";
import imageSize from "image-size";
import { db } from "~/clients";
import FreeConvert from "~/clients/FreeConvert";
import { CHANNEL_ICON_MAP } from "~/types";
import { getKeys } from "~/utils";
import getAssetBuffer from "./getAssetBuffer";

// Returns a promise that resolves to the number of files copied
export const copyStorageFiles = async ({
  sourceBasePath,
  destinationBasePath,
  copySourceFolderStructure = false,
  copySubdirectories = true,
  folderRenames,
  maxFiles,
  acceptedMimetypes,
  userID,
}: {
  sourceBasePath: string;
  destinationBasePath: string;
  copySourceFolderStructure?: boolean;
  copySubdirectories?: boolean;
  folderRenames?: { [fromName: string]: string };
  maxFiles?: number;
  acceptedMimetypes?: {
    [mimetype: string]: {
      maxSizeMb?: number;
      minDimensions?: {
        width?: number;
        height?: number;
        aspectRatio?: number;
      };
      maxDimensions?: {
        width?: number;
        height?: number;
        aspectRatio?: number;
      };
      minDurationSec?: number;
      maxDurationSec?: number;
    };
  };
  userID: string;
}) => {
  const { data: files, error: filesError } = await supabaseAdminClient.storage
    .from("assets")
    .list(sourceBasePath);

  if (filesError) {
    throw filesError;
  }

  let count = 0;

  for (const file of files) {
    // If the file is a directory, recursively copy the files in that directory
    if (file.metadata == null) {
      const subdirectoryName =
        folderRenames && file.name in folderRenames
          ? folderRenames[file.name]
          : file.name;
      const subdirectorySourceBasePath = `${sourceBasePath}/${file.name}`;
      const subdirectoryDestinationBasePath = `${destinationBasePath}/${subdirectoryName}`;

      if (copySubdirectories) {
        const subdirectoryCount = await copyStorageFiles({
          sourceBasePath: subdirectorySourceBasePath,
          destinationBasePath: copySourceFolderStructure
            ? subdirectoryDestinationBasePath
            : destinationBasePath,
          copySubdirectories,
          copySourceFolderStructure,
          folderRenames,
          maxFiles: maxFiles ? maxFiles - count : undefined,
          acceptedMimetypes,
          userID,
        });

        count += subdirectoryCount;
      }
    } else {
      const filePath = `${sourceBasePath}/${file.name}`;

      // If acceptedMimetypes is set, check if the file is an accepted type
      if (acceptedMimetypes) {
        const { data } = await supabaseAdminClient.storage
          .from("assets")
          .createSignedUrl(filePath, ONE_YEAR_IN_SECONDS);

        // Set to 4 since we don't have the video length right now
        // and the minimum is 3 seconds
        let videoLength = 4;
        const fileMimeType = file.metadata?.mimetype as string;
        const isVideo = fileMimeType.includes("video");
        const isImage =
          fileMimeType.includes("image") && !fileMimeType.includes("image/gif");
        let width = 0;
        let height = 0;

        // Means the mimetype is not accepted
        if (!Object.keys(acceptedMimetypes).includes(fileMimeType)) {
          continue;
        }

        const restrictions = acceptedMimetypes[fileMimeType];
        if (isVideo) {
        } else {
          const buffer = await getAssetBuffer(data?.signedUrl as string);
          const dimensions = imageSize(buffer);
          width = dimensions.width as number;
          height = dimensions.height as number;
        }

        let accepted = true;
        const maxSizeMb = restrictions.maxSizeMb ?? Infinity;

        const validSize = file.metadata.size / 1_000_000 <= maxSizeMb;

        if (isVideo) {
          const validLength =
            videoLength >= (restrictions.minDurationSec ?? 0) &&
            videoLength <= (restrictions.maxDurationSec ?? Infinity);

          accepted = validLength;
        } else {
          const validDimensions =
            width >= (restrictions.minDimensions?.width ?? 0) &&
            width <= (restrictions.maxDimensions?.width ?? Infinity) &&
            height >= (restrictions.minDimensions?.height ?? 0) &&
            height <= (restrictions.maxDimensions?.height ?? Infinity) &&
            (restrictions.minDimensions?.aspectRatio ?? 0) <= width / height &&
            (restrictions.maxDimensions?.aspectRatio ?? Infinity) >=
              width / height;

          accepted = validDimensions;
        }

        if (!accepted) {
          continue;
        }

        if (!validSize) {
          if (isImage) {
            const freeConvert = new FreeConvert();
            const jobResponse = await freeConvert.createCompressImageJob({
              image: {
                url: data!.signedUrl,
                filename: file.name,
                mimetype: fileMimeType,
              },
              compression: {
                type: "size",
                maxFileSizeMB: maxSizeMb,
              },
            });

            if (jobResponse.errors) {
              throw new Error(`Error compressing image: ${jobResponse.errors}`);
            }

            const job = jobResponse.data;

            const postID = destinationBasePath.split("/")[1] as string;
            const channelSlug = destinationBasePath.split("/")[2] as Channel;
            const channel = getKeys(CHANNEL_ICON_MAP).find(
              (channel) => CHANNEL_ICON_MAP[channel].slug === channelSlug
            ) as Channel;

            const freeConvertJob = await db.freeConvertJob.create({
              data: {
                id: job.id,
                fileID: file.id,
                path: `${destinationBasePath}/${file.name}`,
                postID,
                status: job.status,
                channel,
                fileName: file.name,
                startedAt: job.startedAt,
                originalSize: file.metadata.size,
                createdByID: userID,
              },
            });
          } else {
            continue;
          }
        }
      }

      // If the file is not a directory, copy the file
      const { data: copyData, error: copyError } =
        await supabaseAdminClient.storage
          .from("assets")
          .copy(
            `${sourceBasePath}/${file.name}`,
            `${destinationBasePath}/${file.name}`
          );

      // Supabase copying seems to not work in dev
      if (copyError && process.env.NODE_ENV !== "development") {
        throw copyError;
      }

      count++;

      if (count >= (maxFiles ?? Infinity)) {
        break;
      }
    }
  }

  return count;
};
