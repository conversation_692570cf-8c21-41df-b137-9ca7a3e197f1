import { Asset } from "@prisma/client";
import { AssetType } from "~/types/Asset";
import enhanceAssetType from "./assets/enhanceAssetType";

const getAssetsForWebflow = async (post: {
  id: string;
  workspaceID: string;
  webflowContent: {
    id: string;
    fields: {
      id: string;
      assets: {
        id: string;
        position: number;
        asset: Asset;
      }[];
    }[];
  } | null;
}): Promise<{ [fieldID: string]: AssetType[] }> => {
  const webflowContent = post.webflowContent;

  if (!webflowContent) {
    return {};
  }

  enhanceAssetType;

  return Object.fromEntries(
    webflowContent.fields.map((field) => {
      return [
        field.id,
        field.assets
          .sort((a1, a2) => a1.position - a2.position)
          .map((asset) => {
            return {
              ...enhanceAssetType(asset.asset),
              id: asset.id,
              position: asset.position,
            };
          }),
      ];
    })
  );
};

export default getAssetsForWebflow;
