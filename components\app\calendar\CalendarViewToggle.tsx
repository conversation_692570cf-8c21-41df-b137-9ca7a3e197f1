import { CalendarIcon, ListBulletIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import { CalendarView } from "~/providers/CalendarViewProvider";

type Props = {
  view: CalendarView;
  setView: (view: CalendarView) => void;
};

const CalendarViewToggle = ({ view, setView }: Props) => {
  return (
    <button
      className="flex overflow-hidden rounded-md"
      onClick={() => setView(view === "calendar" ? "list" : "calendar")}
    >
      <div
        className={classNames("p-1.5 transition-colors", {
          "bg-basic text-basic-light": view === "calendar",
          "bg-slate hover:bg-light-gray text-black": view === "list",
        })}
        aria-label="Calendar view"
      >
        <CalendarIcon className="h-4 w-4" />
      </div>
      <div
        className={classNames("p-1.5 transition-colors", {
          "bg-basic text-basic-light": view === "list",
          "bg-slate hover:bg-light-gray text-black": view === "calendar",
        })}
        aria-label="List view"
      >
        <ListBulletIcon className="h-4 w-4" />
      </div>
    </button>
  );
};

export default CalendarViewToggle;
