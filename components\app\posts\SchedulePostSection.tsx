import { useState } from "react";

import {
  ArrowPathIcon,
  ArrowRightOnRectangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import { useViewportSize } from "@mantine/hooks";
import { Channel, PostStatus, ScheduledPostStatus } from "@prisma/client";
import assert from "assert";
import { AnimatePresence, motion } from "framer-motion";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import { toast } from "sonner";
import { DatePicker, Divider, Loader, TextLink, Tooltip } from "~/components";
import AnimateChangeInHeight from "~/components/AnimateChangeInHeight";
import TimeInput, { SuggestedTimes } from "~/components/TimeInput";
import Button from "~/components/ui/Button";
import DropdownMenu from "~/components/ui/DropdownMenu";
import Skeleton from "~/components/ui/Skeleton";
import { EDITOR_CHANNELS } from "~/constants";
import { useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import {
  cn,
  dateFromUTC,
  formatPostDate,
  getDateUTC,
  getPostPublishAt,
  getPostPublishAtMoment,
  getTimezoneName,
  handleTrpcError,
  joinWithAnd,
  removeEmptyElems,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import ChannelIcon from "../ChannelIcon";
import TimezoneTooltip from "../TimezoneTooltip";
import SchedulePostModal from "./SchedulePostModal";

export type ScheduledPost = {
  id: string;
  status: ScheduledPostStatus;
  channel: Channel;
  error: string | null;
  publishAt: Date;
  integrationAccountName: string;
};

export type ConnectedAccount = {
  integrationID: string;
  name: string;
};

type Props = {
  post: {
    id: string;
    status: PostStatus;
    channels: Channel[];
    title: string;
  } & PostOrIdea;
  isReadOnly: boolean;
};

const SchedulePostSection = ({ post, isReadOnly }: Props) => {
  const channel = post.channels[0];
  const { width: viewportWidth } = useViewportSize();

  const isMobile = viewportWidth < 768;

  const { currentWorkspace } = useWorkspace();

  const router = useRouter();
  const trpcUtils = trpc.useUtils();

  const publishDate = post.publishDate ? dateFromUTC(post.publishDate) : null;
  const publishTime = post.publishTime
    ? getPostPublishAtMoment(post).toJSDate()
    : null;

  const updatePostMutation = trpc.post.update.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Failed to Update Post", error });
    },
    onSuccess: (data) => {
      if (post.isIdea) {
        trpcUtils.idea.getAll.refetch();
      } else {
        trpcUtils.post.getMany.refetch();
      }
    },
  });
  const [schedulePostModalOpen, setSchedulePostModalOpen] =
    useState<boolean>(false);
  const [shouldPostImmediately, setShouldPostImmediately] =
    useState<boolean>(false);

  const integrationsQuery = trpc.post.getIntegrations.useQuery(
    {
      id: post.id,
    },
    {
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    }
  );

  const mostRecentlyUsedQuery = trpc.post.getMostRecentlyUsed.useQuery();
  const mostRecentlyUsedTimes = (
    mostRecentlyUsedQuery.data?.publishAts || []
  ).map((publishAt) => new Date(publishAt));
  const optimalPublishTimeQuery = trpc.post.getBestTimeToPost.useQuery(
    {
      channel,
      publishDate,
    },
    {
      enabled: publishDate != null,
      refetchOnWindowFocus: false,
      refetchInterval: false,
    }
  );
  const optimalPublishTime = optimalPublishTimeQuery.data?.publishTime;

  const convertToPostMutation = trpc.idea.convertToPost.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Error Convert Idea to Post", error });
    },
    onSuccess: (data) => {
      toast.success("Added to Calendar", {
        description: "Idea converted into a post on the calendar",
      });
      trpcUtils.idea.getAll.invalidate();
      trpcUtils.post.getMany.invalidate();
      router.push(`/posts/${data.post.id}`);
    },
  });

  const suggestedTimes: SuggestedTimes[] = [];
  if (publishTime == null) {
    if (currentWorkspace?.timezone == null) {
      suggestedTimes.push({
        label: "Recommended",
        howToComponent: (
          <TextLink
            href="/settings/general"
            size="sm"
            wrapText={true}
            openInNewTab={false}
            value="Set a timezone to see 
      recommendations ->"
          />
        ),
      });
    } else if (optimalPublishTime) {
      suggestedTimes.push({
        label: "Recommended",
        times: [
          DateTime.fromJSDate(optimalPublishTime).setLocale("local").toJSDate(),
        ],
      });
    }

    suggestedTimes.push({
      label: "Recently Used",
      times: mostRecentlyUsedTimes,
    });
  }

  const unschedulePostMutation = trpc.post.unschedulePost.useMutation({
    onSuccess: () => {
      trpcUtils.post.getIntegrations.refetch({
        id: post.id,
      });
      const previousData = trpcUtils.post.getMany.getData();
      if (previousData) {
        const previousPosts = previousData.posts;
        const updatedPosts = previousPosts.map((previousPost) => {
          if (previousPost.id === post.id) {
            return {
              ...previousPost,
              status: "Finalized" as const,
            };
          } else {
            return previousPost;
          }
        });
        trpcUtils.post.getMany.setData(undefined, {
          posts: updatedPosts,
        });
      }
      const currentPostData = trpcUtils.post.get.getData({ id: post.id });
      if (currentPostData) {
        const currentPost = currentPostData.post;
        const { publishDate, publishTime, publishAt } = currentPost;
        assert(publishDate && publishTime && publishAt);
        trpcUtils.post.get.setData(
          {
            id: post.id,
          },
          {
            post: {
              ...currentPost,
              publishDate,
              publishTime,
              publishAt,
              isIdea: false,
              status: "Finalized",
            },
            isReadOnly: currentPostData.isReadOnly,
          }
        );
      }
    },
    onError: (error) => {
      handleTrpcError({ title: "Unable to Unschedule Post", error });
    },
  });

  const updatePublishDate = (newPublishDate: Date) => {
    assert(publishDate != null);
    if (
      formatPostDate({
        publishDate: publishDate,
        publishTime: publishTime,
      }) !=
      formatPostDate({
        publishDate: newPublishDate,
        publishTime: publishTime,
      })
    ) {
      const startOfDate = DateTime.fromJSDate(newPublishDate)
        .startOf("day")
        .toJSDate();
      const currentPost = trpcUtils.post.get.getData({ id: post.id })?.post;
      if (currentPost) {
        trpcUtils.post.get.setData(
          {
            id: post.id,
          },
          {
            post: {
              ...currentPost,
              publishDate: getDateUTC(startOfDate),
              isIdea: false,
            },
            isReadOnly,
          }
        );
      }

      const publishAt = getPostPublishAt(newPublishDate, publishTime);
      const publishDateUTC = getDateUTC(newPublishDate);

      updatePostMutation.mutate(
        {
          id: post.id,
          publishDate: publishDateUTC,
          publishAt,
        },
        {
          onSuccess: () => {
            trpcUtils.post.get.invalidate({
              id: post.id,
            });
          },
        }
      );
    }
  };

  const updatePublishTime = (newPublishTime: Date | null) => {
    assert(publishDate != null);
    if (
      formatPostDate({
        publishDate: publishDate,
        publishTime: publishTime,
      }) !=
      formatPostDate({
        publishDate: publishDate,
        publishTime: newPublishTime,
      })
    ) {
      const currentPost = trpcUtils.post.get.getData({ id: post.id })?.post;
      const publishAt = getPostPublishAt(publishDate, newPublishTime);

      if (currentPost) {
        trpcUtils.post.get.setData(
          {
            id: post.id,
          },
          {
            post: {
              ...currentPost,
              publishDate: currentPost.publishDate!,
              publishTime: newPublishTime,
              publishAt,
              isIdea: false,
            },
            isReadOnly,
          }
        );
      }

      updatePostMutation.mutate(
        {
          id: post.id,
          publishTime: newPublishTime,
          publishAt,
        },
        {
          onSuccess: () => {
            trpcUtils.post.get.invalidate({
              id: post.id,
            });
          },
        }
      );
    }
  };

  const accounts = integrationsQuery.data?.accounts ?? [];
  const scheduledAccounts = accounts.filter(
    (account) => !!account.scheduledPost
  );
  const noConnectedAccounts =
    accounts.filter((account) => account.id == null).length === accounts.length;

  const scheduledPosts = removeEmptyElems(
    scheduledAccounts.map((account) => account.scheduledPost)
  ).filter((scheduledPost) => scheduledPost.status !== "Cancelled");

  if (integrationsQuery.isLoading) {
    if (!isMobile) {
      if (post.status === "Scheduled" || post.status === "Posted") {
        return (
          <div className="border-light-gray mb-5 space-y-2 rounded-md border p-4">
            <div className="flex items-center gap-1.5">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-4 w-40 rounded-full" />
            </div>
            <div className="flex items-center gap-1.5">
              <Skeleton className="h-4 w-4 rounded-xs" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        );
      } else {
        return (
          <div className="mt-1">
            <div className="flex h-16 w-full items-start">
              <div className="w-[72px]">
                <Skeleton className="h-4 w-8" />
              </div>
              <div className="flex w-full flex-col gap-3 pr-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-8 w-32 rounded-md" />
              </div>
            </div>
            <Divider padding="md" />
          </div>
        );
      }
    } else {
      return <Loader size="sm" />;
    }
  }

  if (scheduledPosts.length > 0) {
    const isProcessing = scheduledPosts.some(
      (scheduledPost) =>
        scheduledPost.status === "Scheduled" &&
        DateTime.fromJSDate(scheduledPost.publishAt) < DateTime.now()
    );
    const isScheduled =
      scheduledPosts.every(
        (scheduledPost) => scheduledPost.status === "Scheduled"
      ) && !isProcessing;
    const erroredPosts = scheduledPosts.filter(
      (scheduledPost) => scheduledPost.status === "Errored"
    );
    const isErrored = erroredPosts.length > 0 && !isProcessing;
    const isPosted = scheduledPosts.every(
      (scheduledPost) => scheduledPost.status === "Completed"
    );

    const publishAt = scheduledPosts[0]?.publishAt;

    const nonscheduledAccounts = accounts.filter(
      (account) =>
        account.scheduledPost == null ||
        account.scheduledPost.status === "Cancelled"
    );

    const numAccounts = scheduledPosts.length + nonscheduledAccounts.length;

    return (
      <div
        className={cn({
          "border-light-gray font-body-sm mb-5 rounded-md border p-4":
            !isMobile,
          "md:space-y-2": isScheduled,
        })}
      >
        <div
          className={cn("flex items-center gap-1.5 font-medium", {
            "hidden md:flex": isScheduled,
            "text-basic": isScheduled || isPosted,
            "text-danger": isErrored,
            "text-medium-dark-gray": isProcessing,
          })}
        >
          {isScheduled && <ClockIcon className="h-4 w-4" />}
          {isProcessing && <ArrowPathIcon className="h-4 w-4" />}
          {isErrored && <XCircleIcon className="h-4 w-4" />}
          {isPosted && <CheckCircleIcon className="h-4 w-4" />}
          {isProcessing ? (
            `Posting now (${DateTime.fromJSDate(publishAt).toFormat("h:mm a")}
            ${getTimezoneName()})...`
          ) : (
            <div>
              {DateTime.fromJSDate(publishAt).toFormat(
                isMobile ? "M/d 'at' h:mma" : "ccc M/d/yy 'at' h:mma"
              )}{" "}
              {!isMobile && getTimezoneName()}
            </div>
          )}
        </div>
        {isScheduled && (
          <div>
            {!isMobile && numAccounts > 1 ? (
              <>
                {numAccounts <= 4 ? (
                  <div className="space-y-2">
                    <AnimatePresence>
                      <div className="flex flex-col gap-2">
                        {scheduledPosts.map((scheduledPost) => (
                          <motion.div
                            key={scheduledPost.id}
                            initial={{
                              height: 0,
                              opacity: 0,
                            }}
                            animate={{
                              height: "auto",
                              opacity: 1,
                            }}
                            exit={{
                              height: 0,
                              opacity: 0,
                            }}
                            className="flex items-center gap-1.5"
                          >
                            <ChannelIcon
                              channel={scheduledPost.channel}
                              width={15}
                            />
                            <div className="text-secondary">
                              {scheduledPost.integrationAccountName}
                            </div>
                          </motion.div>
                        ))}
                        {nonscheduledAccounts.map((account) => (
                          <motion.div
                            key={channel}
                            initial={{
                              height: 0,
                              opacity: 0,
                            }}
                            animate={{
                              height: "auto",
                              opacity: 1,
                            }}
                            exit={{
                              height: 0,
                              opacity: 0,
                            }}
                            className="flex items-center gap-1.5"
                          >
                            <ChannelIcon channel={account.channel} width={15} />
                            <div className="text-medium-dark-gray flex items-center gap-2 whitespace-nowrap">
                              <div className="max-w-[125px] overflow-hidden text-ellipsis">
                                {account.name || "No account"}
                              </div>
                              <div className="flex items-center gap-1">
                                <ExclamationTriangleIcon className="text-warning h-4 w-4" />
                                <div>Not Scheduled</div>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </AnimatePresence>
                  </div>
                ) : (
                  <Tooltip
                    value={
                      <div className="flex flex-col gap-2 p-1">
                        {scheduledPosts.map((scheduledPost) => (
                          <div
                            key={scheduledPost.id}
                            className="flex items-center gap-1.5"
                          >
                            <ChannelIcon
                              channel={scheduledPost.channel}
                              width={15}
                            />
                            <div className="text-secondary">
                              {scheduledPost.integrationAccountName}
                            </div>
                          </div>
                        ))}
                        {nonscheduledAccounts.map((account) => (
                          <div className="flex items-center gap-1.5">
                            <ChannelIcon channel={account.channel} width={15} />
                            <div className="text-medium-dark-gray flex items-center gap-2 whitespace-nowrap">
                              <div className="max-w-[125px] overflow-hidden text-ellipsis">
                                {account.name || "No account"}
                              </div>
                              <div className="flex items-center gap-1">
                                <ExclamationTriangleIcon className="text-warning h-4 w-4" />
                                <div>Not Scheduled</div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    }
                  >
                    <div className="flex w-fit items-center gap-1.5">
                      {scheduledPosts.map((scheduledPost) => (
                        <ChannelIcon
                          key={scheduledPost.channel}
                          channel={scheduledPost.channel}
                          width={15}
                        />
                      ))}
                      {nonscheduledAccounts.map((account) => (
                        <div className="opacity-50" key={account.channel}>
                          <ChannelIcon channel={account.channel} width={15} />
                        </div>
                      ))}
                    </div>
                  </Tooltip>
                )}
              </>
            ) : null}
            {
              <DropdownMenu.Root>
                <DropdownMenu.Trigger>
                  <div className="font-body-sm bg-slate text-basic hover:bg-light-gray disabled:text-medium-dark-gray inline-flex h-8 w-fit transform items-center rounded-[5px] px-2 py-1 font-medium transition-all md:mt-1">
                    {isMobile ? (
                      <>
                        {DateTime.fromJSDate(publishAt).toFormat(
                          "M/d 'at' h:mma"
                        )}
                      </>
                    ) : (
                      "Edit Scheduling"
                    )}
                  </div>
                </DropdownMenu.Trigger>
                <DropdownMenu.Content align="start">
                  <DropdownMenu.Group>
                    <DropdownMenu.Item
                      icon="PencilSquareIcon"
                      onClick={() => {
                        setShouldPostImmediately(false);
                        setSchedulePostModalOpen(true);
                      }}
                    >
                      Edit Scheduling
                    </DropdownMenu.Item>
                    <DropdownMenu.Item
                      intent="danger"
                      icon="BackspaceIcon"
                      onClick={() => {
                        unschedulePostMutation.mutate({
                          id: post.id,
                        });
                      }}
                    >
                      Unschedule Post
                    </DropdownMenu.Item>
                  </DropdownMenu.Group>
                </DropdownMenu.Content>
              </DropdownMenu.Root>
            }
          </div>
        )}
        {isProcessing && (
          <div className="text-medium-dark-gray">
            It can take a few minutes for some channels to post
          </div>
        )}
        {isErrored && (
          <div className="text-medium-dark-gray">
            There were errors on{" "}
            {joinWithAnd(
              erroredPosts.map((post) => CHANNEL_ICON_MAP[post.channel].label)
            )}
            . Check the channel tab for details
          </div>
        )}
        {isPosted && (
          <div className="text-medium-dark-gray">Successfully posted</div>
        )}
        <SchedulePostModal
          post={post}
          open={schedulePostModalOpen}
          setOpen={setSchedulePostModalOpen}
          accounts={accounts}
          suggestedTimes={suggestedTimes}
        />
      </div>
    );
  }

  return (
    <div>
      {!isReadOnly && (
        <>
          {post.isIdea ? (
            <Button
              size="sm"
              isLoading={convertToPostMutation.isPending}
              onClick={() =>
                convertToPostMutation.mutate({
                  id: post.id,
                  publishDate: DateTime.now().startOf("day").toJSDate(),
                })
              }
            >
              <ArrowRightOnRectangleIcon className="h-4 w-4" />
              Add to Calendar
            </Button>
          ) : (
            <AnimateChangeInHeight>
              <div className="flex h-fit w-full items-start">
                <dt className="font-eyebrow-sm text-medium-gray mt-1 hidden w-[72px] md:block">
                  Date:
                </dt>
                <dd className="md:space-y-1.5">
                  <TimezoneTooltip
                    date={
                      publishDate
                        ? getPostPublishAt(publishDate, publishTime)
                        : null
                    }
                  >
                    <div className="hidden w-fit items-center gap-4 md:flex">
                      <div className="w-fit max-w-[70px]">
                        <DatePicker
                          value={publishDate}
                          isDisabled={isReadOnly}
                          isClearable={false}
                          onChange={updatePublishDate}
                          showBorder={false}
                        />
                      </div>
                      <div className="w-24">
                        <TimeInput
                          value={publishTime}
                          disabled={isReadOnly}
                          onChange={updatePublishTime}
                          showBorder={false}
                          suggestedTimes={suggestedTimes}
                        />
                      </div>
                    </div>
                  </TimezoneTooltip>
                  <AnimatePresence>
                    {post.channels.some((channel) =>
                      EDITOR_CHANNELS.includes(channel)
                    ) && (
                      <motion.div
                        className="flex items-center gap-2"
                        initial={{
                          opacity: 0,
                        }}
                        animate={{
                          opacity: 1,
                        }}
                        exit={{
                          height: 0,
                          opacity: 0,
                        }}
                      >
                        <Tooltip
                          value={
                            noConnectedAccounts
                              ? "No accounts have been connected to Assembly for this post's channel(s)."
                              : null
                          }
                        >
                          <Button
                            size="sm"
                            disabled={noConnectedAccounts}
                            onClick={() => {
                              setShouldPostImmediately(false);
                              setSchedulePostModalOpen(true);
                            }}
                          >
                            {!isMobile && <ClockIcon className="h-4 w-4" />}
                            {isMobile ? "Schedule" : "Schedule Post"}
                          </Button>
                        </Tooltip>
                        <Tooltip
                          value={
                            noConnectedAccounts
                              ? "No accounts have been connected to Assembly for this post's channel(s)."
                              : null
                          }
                        >
                          <Button
                            size="sm"
                            variant="secondary"
                            disabled={noConnectedAccounts}
                            onClick={() => {
                              setShouldPostImmediately(true);
                              setSchedulePostModalOpen(true);
                            }}
                          >
                            Post Now
                          </Button>
                        </Tooltip>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </dd>
              </div>
            </AnimateChangeInHeight>
          )}
        </>
      )}
      <SchedulePostModal
        post={post}
        open={schedulePostModalOpen}
        setOpen={setSchedulePostModalOpen}
        accounts={accounts}
        suggestedTimes={suggestedTimes}
        shouldPostImmediately={shouldPostImmediately}
      />
      <Divider padding="md" className="hidden md:block" />
    </div>
  );
};

export default SchedulePostSection;
