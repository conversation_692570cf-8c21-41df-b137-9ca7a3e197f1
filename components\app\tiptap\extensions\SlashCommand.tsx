// Influenced by https://github.com/ueberdosis/tiptap-templates/blob/main/templates/next-block-editor-app/src/extensions/SlashCommand/SlashCommand.ts

import {
  Bars3BottomLeftIcon,
  CodeBracketIcon,
  H1Icon,
  H2Icon,
  H3Icon,
  PhotoIcon,
} from "@heroicons/react/24/outline";
import { Editor, Extension, Range } from "@tiptap/core";
import { PluginKey } from "@tiptap/pm/state";
import { ReactRenderer } from "@tiptap/react";
import Suggestion from "@tiptap/suggestion";
import classNames from "classnames";
import Image from "next/image";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import tippy from "tippy.js";

type CommandListRef = {
  onKeyDown: (props: { event: KeyboardEvent }) => boolean;
};

type CommandListProps = {
  items: any[];
  command: any;
};

const CommandList = forwardRef<CommandListRef, CommandListProps>(
  (props, ref) => {
    const [selectedIndex, setSelectedIndex] = useState(0);

    useEffect(() => {
      setSelectedIndex(selectedIndex % props.items.length || 0);
    }, [props.items.length]);

    const upHandler = () => {
      setSelectedIndex(
        (selectedIndex + props.items.length - 1) % props.items.length
      );
    };

    const downHandler = () => {
      setSelectedIndex((selectedIndex + 1) % props.items.length);
    };

    const enterHandler = () => {
      const item = props.items[selectedIndex];
      if (item) {
        props.command(item);
      }
    };

    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }) => {
        if (event.key === "ArrowUp") {
          upHandler();
          return true;
        }

        if (event.key === "ArrowDown") {
          downHandler();
          return true;
        }

        if (event.key === "Enter") {
          enterHandler();
          return true;
        }

        return false;
      },
    }));

    return (
      <div className="flex w-40 flex-col items-start gap-1 rounded-lg bg-white px-2 py-1 shadow-xl">
        {props.items.map((item, index) => {
          return (
            <button
              className={classNames(
                "font-body-sm hover:bg-lightest-gray flex w-full items-center gap-2 rounded-md px-2 py-1 transition-colors",
                {
                  "bg-lightest-gray": selectedIndex === index,
                }
              )}
              key={item.label}
              onClick={() => props.command(item)}
            >
              {item.icon}
              {item.label}
            </button>
          );
        })}
      </div>
    );
  }
);

const SlashCommand = Extension.create({
  name: "slash-menu",

  priority: 200,

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        char: "/",
        allowSpaces: true,
        startOfLine: true,
        pluginKey: new PluginKey(this.name),
        decorationClass: "bg-slate/80 px-1 rounded-md",
        allow: ({ state, range }) => {
          const $from = state.doc.resolve(range.from);
          const isRootDepth = $from.depth === 1;
          const isParagraph = $from.parent.type.name === "paragraph";
          const isStartOfNode = $from.parent.textContent?.charAt(0) === "/";
          const isInColumn = this.editor.isActive("column");

          const afterContent = $from.parent.textContent?.substring(
            $from.parent.textContent?.indexOf("/")
          );
          const isValidAfterContent = !afterContent?.endsWith("  ");

          return (
            ((isRootDepth && isParagraph && isStartOfNode) ||
              (isInColumn && isParagraph && isStartOfNode)) &&
            isValidAfterContent
          );
        },
        command: ({
          editor,
          range,
          props: commandProps,
        }: {
          editor: Editor;
          range: Range;
          props: any;
        }) => {
          if (typeof commandProps === "function") {
            commandProps({ editor, range });
          } else if (commandProps?.command) {
            commandProps.command({ editor, range });
          }
        },
        items: ({ query, editor }) => {
          return [
            {
              icon: <Bars3BottomLeftIcon className="h-4 w-4" />,
              command: ({
                editor,
                range,
              }: {
                editor: Editor;
                range: Range;
              }) => {
                editor
                  .chain()
                  .focus()
                  .deleteRange(range)
                  .command(({ commands }) => {
                    // Only attempt to lift task items if the schema includes them
                    if (editor.schema.nodes.taskItem) {
                      commands.lift("taskItem");
                    }
                    // Only attempt to lift list items if the schema includes them
                    if (editor.schema.nodes.listItem) {
                      commands.liftListItem("listItem");
                    }
                    return true;
                  })
                  .setParagraph()
                  .run();
              },
              isDisabled: () => !editor.can().setParagraph(),
              isActive: () =>
                editor.isActive("paragraph") &&
                !editor.isActive("orderedList") &&
                !editor.isActive("bulletList") &&
                !editor.isActive("taskList"),
              label: "Text",
              type: "option",
            },
            {
              label: "Heading 1",
              icon: <H1Icon className="h-4 w-4" />,
              command: ({
                editor,
                range,
              }: {
                editor: Editor;
                range: Range;
              }) => {
                editor
                  .chain()
                  .focus()
                  .deleteRange(range)
                  .setNode("heading", { level: 1 })
                  .run();
              },
            },
            {
              label: "Heading 2",
              icon: <H2Icon className="h-4 w-4" />,
              command: ({
                editor,
                range,
              }: {
                editor: Editor;
                range: Range;
              }) => {
                editor
                  .chain()
                  .focus()
                  .deleteRange(range)
                  .setNode("heading", { level: 2 })
                  .run();
              },
            },
            {
              label: "Heading 3",
              icon: <H3Icon className="h-4 w-4" />,
              command: ({
                editor,
                range,
              }: {
                editor: Editor;
                range: Range;
              }) => {
                editor
                  .chain()
                  .focus()
                  .deleteRange(range)
                  .setNode("heading", { level: 3 })
                  .run();
              },
            },
            {
              label: "Heading 4",
              icon: (
                <Image
                  src="/editor/heading4.png"
                  alt="Heading 4"
                  width={16}
                  height={16}
                />
              ),
              command: ({
                editor,
                range,
              }: {
                editor: Editor;
                range: Range;
              }) => {
                editor
                  .chain()
                  .focus()
                  .deleteRange(range)
                  .setNode("heading", { level: 4 })
                  .run();
              },
            },
            {
              label: "Heading 5",
              icon: (
                <Image
                  src="/editor/heading5.png"
                  alt="Heading 5"
                  width={16}
                  height={16}
                />
              ),
              command: ({
                editor,
                range,
              }: {
                editor: Editor;
                range: Range;
              }) => {
                editor
                  .chain()
                  .focus()
                  .deleteRange(range)
                  .setNode("heading", { level: 5 })
                  .run();
              },
            },
            {
              label: "Heading 6",
              icon: (
                <Image
                  src="/editor/heading6.png"
                  alt="Heading 6"
                  width={16}
                  height={16}
                />
              ),
              command: ({
                editor,
                range,
              }: {
                editor: Editor;
                range: Range;
              }) => {
                editor
                  .chain()
                  .focus()
                  .deleteRange(range)
                  .setNode("heading", { level: 6 })
                  .run();
              },
            },
            {
              label: "Image",
              icon: <PhotoIcon className="h-4 w-4" />,
              command: ({
                editor,
                range,
              }: {
                editor: Editor;
                range: Range;
              }) => {
                editor
                  .chain()
                  .focus()
                  .deleteRange(range)
                  .setImageUpload({})
                  .run();
              },
            },
            {
              label: "Code Block",
              icon: <CodeBracketIcon className="h-4 w-4" />,
              command: ({
                editor,
                range,
              }: {
                editor: Editor;
                range: Range;
              }) => {
                editor.chain().focus().deleteRange(range).setCodeBlock().run();
              },
            },
          ]
            .filter((item) =>
              item.label.toLowerCase().startsWith(query.toLowerCase())
            )
            .slice(0, 10);
        },
        render: () => {
          let component: any;
          let popup: any;

          return {
            onStart: (props: any) => {
              component = new ReactRenderer(CommandList, {
                props,
                editor: props.editor,
              });

              popup = tippy("body", {
                getReferenceClientRect: props.clientRect,
                appendTo: () => document.body,
                content: component.element,
                showOnCreate: true,
                interactive: true,
                trigger: "manual",
                placement: "bottom-start",
              });
            },
            onUpdate(props: any) {
              component.updateProps(props);

              popup[0].setProps({
                getReferenceClientRect: props.clientRect,
              });
            },
            onKeyDown(props: { event: KeyboardEvent }) {
              if (props.event.key === "Escape") {
                popup[0].hide();
                return true;
              }

              return component.ref?.onKeyDown(props) || false;
            },
            onExit() {
              popup[0].destroy();
              component.destroy();
            },
          };
        },
      }),
    ];
  },
});

export default SlashCommand;
