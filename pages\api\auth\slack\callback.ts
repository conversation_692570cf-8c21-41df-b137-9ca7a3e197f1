import { WebClient as SlackWebClient } from "@slack/web-api";
import completeOnboardingTask from "apiUtils/completeOnboardingTask";
import countSocialProfiles from "apiUtils/countSocialProfiles";
import addSubscriptionItem from "apiUtils/stripe/addSubscriptionItem";
import axios from "axios";
import { StatusCodes } from "http-status-codes";
import type { NextApiRequest, NextApiResponse } from "next";
import getUserWorkspaceWithSocialProfileUsage from "server/routers/utils/getUserWorkspaceWithSocialProfileUsage";
import { db } from "~/clients/Prisma";
import slackWebhookClient from "~/clients/Slack";
import createSupabaseServerClient from "~/clients/createSupabaseServerClient";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { getCookie } from "~/utils/cookies";

const SLACK_ACCESS_ENDPOINT = "https://slack.com/api/oauth.v2.access";

const handleError = (
  res: NextApiResponse,
  message: string,
  referrer: string
) => {
  const params = new URLSearchParams({
    error_message: message,
    channel: "Slack",
  });

  return res.redirect(StatusCodes.MOVED_TEMPORARILY, `${referrer}?${params}`);
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res
      .status(StatusCodes.METHOD_NOT_ALLOWED)
      .json({ message: "Method not allowed" });
  }

  const { code, state, error_description } = req.query;

  const supabase = await createSupabaseServerClient(req.headers.cookie);

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const workspace = await getUserWorkspaceWithSocialProfileUsage({
    user,
    currentWorkspaceID: getCookie("CURRENT_WORKSPACE_ID", req.headers.cookie),
  });

  const slackIntegrationCookie = getCookie(
    "SLACK_INTEGRATION",
    req.headers.cookie
  );

  if (!slackIntegrationCookie) {
    return handleError(
      res,
      "Session expired please try again",
      "/settings/profiles"
    );
  }

  const referrer = slackIntegrationCookie.referrer;

  if (error_description) {
    return handleError(res, error_description as string, referrer);
  }

  if (!code || typeof code !== "string") {
    return handleError(res, "Invalid authorization code", referrer);
  }

  if (slackIntegrationCookie.state !== state) {
    return handleError(res, "Session expired please try again", referrer);
  }

  if (!user || !workspace) {
    return handleError(res, "User not logged in", referrer);
  }

  try {
    const response = await axios.post(
      SLACK_ACCESS_ENDPOINT,
      {
        client_id: process.env.NEXT_PUBLIC_SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        redirect_uri: `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/slack/callback`,
        code,
      },
      {
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
      }
    );

    const data = response.data as
      | {
          ok: false;
          error: string;
        }
      | {
          ok: true;
          error: null;
          authed_user: {
            id: string;
            access_token: string;
            scope: string;
          };
          incoming_webhook: {
            channel: string;
            channel_id: string;
          };
          team: {
            id: string;
            name: string;
          };
        };

    if (data.ok === true) {
      const slack = new SlackWebClient(data.authed_user.access_token);
      const { user: userInfo } = await slack.users.info({
        user: data.authed_user.id,
      });

      if (userInfo == null || userInfo.profile == null) {
        return handleError(res, "Slack user not found", referrer);
      }

      const displayName = userInfo.profile?.display_name;
      const fullName = userInfo.profile?.real_name;
      const profileImageUrl = userInfo.profile?.image_512;

      if (displayName == null || fullName == null || profileImageUrl == null) {
        return handleError(res, "Slack user not found", referrer);
      }

      const existingSlackIntegration = await db.slackUserIntegration.findFirst({
        where: {
          teamID: data.team.id,
          channelID: data.incoming_webhook.channel_id,
          slackUserID: data.authed_user.id,
        },
      });

      if (
        existingSlackIntegration &&
        existingSlackIntegration.workspaceID !== workspace.id
      ) {
        return handleError(
          res,
          `@${existingSlackIntegration?.displayName} is already integrated on another Assembly workspace. Message support if this is a mistake.`,
          referrer
        );
      }

      if (!existingSlackIntegration) {
        if (workspace.subscription?.socialProfileLimit) {
          const numSocialProfiles = countSocialProfiles(workspace);

          if (numSocialProfiles >= workspace.subscription.socialProfileLimit) {
            await addSubscriptionItem({
              workspaceID: workspace.id,
              itemName: "ADDITIONAL_SOCIAL_PROFILE",
              quantity:
                numSocialProfiles -
                workspace.subscription.socialProfileLimit +
                1,
            });
          }
        }
      }
      const slackIntegation = await db.slackUserIntegration.upsert({
        where: {
          teamID_channelID_slackUserID_deletedTimeStamp: {
            teamID: data.team.id,
            channelID: data.incoming_webhook.channel_id,
            slackUserID: data.authed_user.id,
            deletedTimeStamp: 0,
          },
        },
        create: {
          slackUserID: data.authed_user.id,
          displayName,
          fullName,
          profileImageUrl,
          channelID: data.incoming_webhook.channel_id,
          channelName: data.incoming_webhook.channel,
          teamID: data.team.id,
          teamName: data.team.name,
          accessToken: data.authed_user.access_token,
          scope: data.authed_user.scope.split(","),
          workspaceID: workspace.id,
          createdByID: user.id,
        },
        update: {
          displayName,
          fullName,
          profileImageUrl,
        },
        include: {
          createdBy: true,
        },
      });

      posthog.capture({
        distinctId: user.id,
        event: POSTHOG_EVENTS.socialAccount.connected,
        properties: {
          channel: "Slack",
        },
        groups: {
          workspace: workspace.id,
          company: workspace.company.id,
        },
      });

      await slackWebhookClient.newConnectedAccount({
        username: `@${slackIntegation.displayName} - ${slackIntegation.channelName}`,
        channel: "Slack",
        workspace,
        connectedBy: slackIntegation.createdBy,
      });

      const deletedSlackIntegrations = await db.slackUserIntegration.findMany({
        where: {
          workspaceID: workspace.id,
          channelID: slackIntegation.channelID,
          slackUserID: slackIntegation.slackUserID,
          deletedTimeStamp: {
            not: 0,
          },
        },
      });

      await db.post.updateMany({
        where: {
          slackIntegrationID: {
            in: deletedSlackIntegrations.map((integration) => integration.id),
          },
        },
        data: {
          slackIntegrationID: slackIntegation.id,
        },
      });

      if (workspace._count.slackIntegrations === 0) {
        await db.postDefault.upsert({
          where: {
            workspaceID: workspace.id,
          },
          create: {
            workspaceID: workspace.id,
            slackIntegrationID: slackIntegation.id,
          },
          update: {
            slackIntegrationID: slackIntegation.id,
          },
        });
      }

      await completeOnboardingTask({
        workspaceID: workspace.id,
        userID: user.id,
        taskType: "CONNECT_ACCOUNT",
        state: "COMPLETED",
      });

      const params = new URLSearchParams({
        account_name: `@${slackIntegation.displayName} - ${slackIntegation.channelName}`,
        channel: "Slack",
        flow: existingSlackIntegration ? "existing_account" : "new_account",
      });

      if (referrer.startsWith("/settings/profiles")) {
        return res.redirect(
          StatusCodes.MOVED_TEMPORARILY,
          `/settings/profiles?${params}`
        );
      } else {
        return res.redirect(
          StatusCodes.MOVED_TEMPORARILY,
          `/settings/slack/scheduling?${params}`
        );
      }
    } else {
      return handleError(
        res,
        data.error || "Error connecting Slack account",
        "/settings/profiles"
      );
    }
  } catch (error) {
    console.error("Slack auth error:", error);
    return handleError(
      res,
      "Error connecting Slack account",
      "/settings/profiles"
    );
  }
}
