import {
  AtSymbolIcon,
  FaceSmileIcon,
  PhotoIcon,
  PlusIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { CheckBadgeIcon } from "@heroicons/react/24/solid";
import { Channel } from "@prisma/client";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { ReactNode, useMemo } from "react";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import { toast } from "sonner";
import { Divider, Loader, Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import colors from "~/styles/colors";
import { cn, openIntercomArticle, openIntercomChat, trpc } from "~/utils";

type Props = {
  post?: {
    id: string;
    channels: Channel[];
  };
  channel?: Channel;
  addNewButton?: {
    label: string;
    onClick: () => void;
  };
  uploadAssetButton?: {
    acceptedMimetypes: string[];
    onUploadFiles: (files: File[]) => Promise<void>;
    acceptMultiple?: boolean;
    hideTooltip?: boolean;
    isLoading?: boolean;
  };
  deleteButton?: {
    onClick: () => void;
    isHidden?: boolean;
  };
  additionalOptions?: ReactNode | null;
  openHowToTagModal?: () => void;
  characterCount?: {
    current: number;
    max: number;
    twitterAccount?: {
      integrationID: string;
      username: string;
      verifiedType: string;
      isBasicVerifiedOverride: boolean;
    } | null;
  };
  visible?: boolean;
  isReadOnly?: boolean;
};

const EditorActionsBar = ({
  post,
  channel,
  addNewButton,
  uploadAssetButton,
  deleteButton,
  additionalOptions,
  openHowToTagModal,
  characterCount,
  visible = false,
  isReadOnly,
}: Props) => {
  const refreshVerificationMutation =
    trpc.integration.twitter.refreshVerification.useMutation({
      onSuccess: (data) => {
        if (data.verified) {
          toast.success("Updated to X Premium", {
            description: `You can now schedule long-form Tweets for this account.`,
          });
        } else {
          toast.error("Not an X Premium Account", {
            description: `If you believe this is an error, please message us.`,
            action: {
              label: "Message Us",
              onClick: () => {
                openIntercomChat(
                  `I've paid for X Premium Basic and want to enable longer tweets for @${
                    characterCount!.twitterAccount!.username
                  }`
                );
              },
            },
          });
        }
      },
    });

  // Create a random number for the ID of the file input
  const randomID = useMemo(() => Math.random().toString(36), []);

  const imageMimetypes = uploadAssetButton?.acceptedMimetypes.filter(
    (mimetype) => mimetype.startsWith("image/")
  );
  const videoMimetypes = uploadAssetButton?.acceptedMimetypes.filter(
    (mimetype) => mimetype.startsWith("video/")
  );
  const otherMimetypes = uploadAssetButton?.acceptedMimetypes.filter(
    (mimetype) =>
      !mimetype.startsWith("image/") && !mimetype.startsWith("video/")
  );

  const twitterAccount = characterCount?.twitterAccount;
  const twitterAccountIsVerified =
    twitterAccount?.verifiedType !== "none" ||
    twitterAccount?.isBasicVerifiedOverride;

  return (
    <AnimatePresence>
      <div
        className={classNames(
          "flex h-6 w-fit items-center gap-1 pb-1 transition-opacity group-hover:opacity-100",
          {
            "opacity-0": !visible,
            "opacity-100": visible,
            hidden: isReadOnly,
          }
        )}
      >
        {addNewButton && (
          <Button size="xs" variant="ghost" onClick={addNewButton.onClick}>
            <PlusIcon className="h-3 w-3" />
            {addNewButton.label}
          </Button>
        )}
        {addNewButton && <Divider orientation="vertical" />}
        {uploadAssetButton && (
          <form action="/upload" className="relative mr-6 h-4">
            <input
              disabled={uploadAssetButton.isLoading}
              id={`file-upload-${randomID}`}
              type="file"
              multiple={uploadAssetButton.acceptMultiple}
              accept={uploadAssetButton.acceptedMimetypes.join(",")}
              className="hidden"
              onChange={async (event) => {
                const files = Array.from(event.target.files || []);

                // Clears the input value so that the user can upload the same file again
                event.target.value = "";

                uploadAssetButton.onUploadFiles(files);
              }}
            />
            <Tooltip
              value={
                uploadAssetButton.hideTooltip ? null : (
                  <div className="space-y-3">
                    {!!imageMimetypes && imageMimetypes.length > 0 && (
                      <div>
                        <div className="font-eyebrow-sm">Images</div>
                        <div className="font-body-xs text-medium-dark-gray">
                          {imageMimetypes
                            ?.map((mimetype) => mimetype.replace("image/", ""))
                            .join(", ")}
                        </div>
                      </div>
                    )}
                    {!!videoMimetypes && videoMimetypes.length > 0 && (
                      <div>
                        <div className="font-eyebrow-sm">Videos</div>
                        <div className="font-body-xs text-medium-dark-gray">
                          {videoMimetypes
                            ?.map((mimetype) => mimetype.replace("video/", ""))
                            .join(", ")}
                        </div>
                      </div>
                    )}
                    {!!otherMimetypes && otherMimetypes.length > 0 && (
                      <div>
                        <div className="font-eyebrow-sm">Other</div>
                        <div className="font-body-xs text-medium-dark-gray">
                          {otherMimetypes
                            ?.map((mimetype) => mimetype.split("/")[1])
                            .join(", ")}
                        </div>
                      </div>
                    )}
                  </div>
                )
              }
            >
              <label
                htmlFor={`file-upload-${randomID}`}
                className={classNames(
                  "font-body-sm absolute -top-px left-0 flex items-center gap-1 rounded-sm px-1 py-0.5 font-medium whitespace-nowrap transition-colors",
                  {
                    "hover:bg-basic/10 text-basic cursor-pointer":
                      !uploadAssetButton.isLoading,
                    "text-medium-gray cursor-disabled":
                      uploadAssetButton.isLoading,
                  }
                )}
              >
                {uploadAssetButton.isLoading ? (
                  <Loader size="sm" />
                ) : (
                  <PhotoIcon className="h-4 w-4" />
                )}
              </label>
            </Tooltip>
          </form>
        )}
        <Tooltip value="How to add emojis">
          <Button
            size="xs"
            variant="ghost"
            onClick={() => openIntercomArticle("adding_emojis")}
          >
            <FaceSmileIcon className="h-4 w-4" />
          </Button>
        </Tooltip>
        {openHowToTagModal && (
          <Tooltip value="How to tag">
            <Button size="xs" variant="ghost" onClick={openHowToTagModal}>
              <AtSymbolIcon className="h-4 w-4" />
            </Button>
          </Tooltip>
        )}
        {deleteButton && !deleteButton.isHidden && (
          <Tooltip value="Delete">
            <button
              className="hover:bg-danger-light hover:text-danger text-basic rounded-sm px-1 py-0.5 transition-colors"
              onClick={deleteButton.onClick}
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </Tooltip>
        )}
        {!!additionalOptions && <Divider orientation="vertical" />}
        {additionalOptions}
        {characterCount && <Divider orientation="vertical" />}
        {characterCount && (
          <div className="flex items-center gap-1.5">
            <div className="flex items-center gap-1">
              <div className="h-4 w-4">
                <CircularProgressbar
                  value={characterCount.current / characterCount.max}
                  maxValue={1}
                  strokeWidth={10}
                  styles={buildStyles({
                    pathColor:
                      characterCount.current <= characterCount.max
                        ? colors.accentBlue
                        : colors.danger,
                  })}
                />
              </div>
              <div
                className={classNames("font-body-sm font-medium", {
                  "text-medium-gray":
                    characterCount.current <= characterCount.max,
                  "text-danger": characterCount.current > characterCount.max,
                })}
              >
                {characterCount.current.toLocaleString()}
              </div>
            </div>
            <AnimatePresence>
              {channel === "Twitter" &&
                twitterAccountIsVerified &&
                characterCount.current > 280 && (
                  <motion.div
                    className="overflow-clip"
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                  >
                    <Tooltip
                      value={`This long-form Tweet is >280 characters and will post with a "Show more" after the first 280 characters or 10 lines.`}
                    >
                      <div className="font-eyebrow-sm text-twitter-blue border-twitter-blue rounded-xs border px-1">
                        LF
                      </div>
                    </Tooltip>
                  </motion.div>
                )}
            </AnimatePresence>
            {twitterAccount && (
              <Tooltip
                value={
                  !twitterAccountIsVerified ? (
                    <div className="space-y-2">
                      <div>
                        Subscribe to{" "}
                        <span className="font-medium">X Premium</span>? Refresh
                        your connection to enable long-form Tweets.
                      </div>
                      <div>
                        If you're on{" "}
                        <span className="font-medium">X Premium Basic</span>,
                        send us a message and we'll manually enable long-form
                        for you.
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          isLoading={refreshVerificationMutation.isPending}
                          onClick={() =>
                            refreshVerificationMutation.mutate({
                              integrationID: twitterAccount.integrationID,
                            })
                          }
                        >
                          Refresh Connection
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() =>
                            openIntercomChat(
                              `I've paid for X Premium Basic and want to enable longer tweets for @${twitterAccount.username}`
                            )
                          }
                        >
                          Message Us
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <span className="text-secondary">
                        @{twitterAccount.username}
                      </span>{" "}
                      is an X Premium subscriber and can post long-form Tweets.
                    </div>
                  )
                }
              >
                <CheckBadgeIcon
                  className={cn("h-4 w-4 cursor-pointer", {
                    "text-medium-gray":
                      twitterAccount.verifiedType === "none" &&
                      !twitterAccount.isBasicVerifiedOverride,

                    "text-twitter-blue":
                      twitterAccount.verifiedType !== "none" ||
                      twitterAccount.isBasicVerifiedOverride,
                  })}
                />
              </Tooltip>
            )}
          </div>
        )}
      </div>
    </AnimatePresence>
  );
};

export default EditorActionsBar;
