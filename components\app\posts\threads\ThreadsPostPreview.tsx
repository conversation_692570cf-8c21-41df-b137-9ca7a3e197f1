import uniq from "lodash/uniq";

import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import AnimateChangeInHeight from "~/components/AnimateChangeInHeight";
import { urlRegex } from "~/constants";
import { useWorkspace } from "~/providers";
import { getDefaultProfilePicture } from "~/utils";
import LinkPreview from "../LinkPreview";
import ThreadsAssets from "./ThreadsAssets";

type ThreadsAccount = {
  id: string;
  username: string;
  profileImageUrl: string;
};

type Props = {
  threadsContent: {
    id: string;
    copy: string;
    assets: {
      id: string;
      signedUrl: string;
      width?: number | null;
      height?: number | null;
      metadata: {
        mimetype: string;
      };
    }[];
  }[];
  connectedAccount: ThreadsAccount | null | undefined;
};

const ThreadsPostPreview = ({ threadsContent, connectedAccount }: Props) => {
  const { currentWorkspace } = useWorkspace();
  const nonEmpty =
    threadsContent.filter((thread) => thread.copy != "" || thread.assets.length)
      .length > 0;

  if (!nonEmpty) {
    return (
      <div className="font-body text-medium-dark-gray mt-1">
        Preview will show when post content is added below
      </div>
    );
  } else {
    return (
      <div className="flex min-h-[300px] items-center justify-center">
        <div className="scrollbar-hidden h-fit max-h-[600px] space-y-2 overflow-auto rounded-lg bg-white px-6 py-3 text-[14px] text-black">
          <div className="flex flex-col gap-2">
            {threadsContent.map((thread, index) => {
              let copy = thread.copy.slice(0, 500);

              const websites = uniq(copy.match(urlRegex)) || [];
              const firstWebsite = websites.length ? websites[0] : null;

              const firstHashtagMatch = thread.copy.match(/#[^\s\.&]+/);
              if (firstHashtagMatch) {
                const firstHashtag = firstHashtagMatch[0];
                const firstHashtagIndex = thread.copy.indexOf(firstHashtag);
                copy =
                  thread.copy.slice(0, firstHashtagIndex) +
                  `[${firstHashtag.slice(
                    1
                  )}](https://www.threads.com/search?q=${firstHashtag.slice(
                    1
                  )})` +
                  thread.copy.slice(firstHashtagIndex + firstHashtag.length);
              }

              return (
                <div
                  key={thread.id}
                  className="font-smoothing flex h-full w-[540px] flex-col gap-2.5 leading-4"
                >
                  <div className="grid grid-cols-12 gap-3">
                    <div className="col-span-1 flex h-full flex-col items-center gap-1 overflow-clip pb-0">
                      <img
                        className="h-8 w-8 shrink-0 rounded-full border-[0.5px] border-[#CDCDCD]"
                        src={
                          connectedAccount?.profileImageUrl ??
                          getDefaultProfilePicture("Threads")
                        }
                      />
                      {index !== threadsContent.length - 1 && (
                        <div className="mt-0.5 w-[2px] grow rounded-md bg-[#E5E5E5]" />
                      )}
                    </div>
                    <div className="col-span-11 flex flex-col gap-1">
                      <span className="font-semibold">
                        {connectedAccount?.username || currentWorkspace?.name}
                      </span>
                      <div key={index} className="flex flex-col">
                        <AnimateChangeInHeight>
                          <div className="leading-[21px]">
                            {copy.split("\n").map((line, index) => {
                              if (line === "") {
                                return <div key={index} className="py-1.5" />;
                              } else {
                                let parsedLine = line;

                                const usernameRegex =
                                  /(@[A-Za-z0-9_\.]{3,30})/g;
                                const usernameTags = uniq(
                                  line.match(usernameRegex) || []
                                );

                                usernameTags.forEach((usernameWithAt) => {
                                  const username = usernameWithAt.replace(
                                    "@",
                                    ""
                                  );

                                  parsedLine = parsedLine.replaceAll(
                                    usernameWithAt,
                                    `[${usernameWithAt}](https://www.threads.com/${username})`
                                  );
                                });

                                return (
                                  <div key={index} dir="auto">
                                    <ReactMarkdown
                                      className="break-words"
                                      linkTarget="_blank"
                                      children={parsedLine}
                                      remarkPlugins={[remarkGfm]}
                                      rehypePlugins={[rehypeRaw]}
                                      components={{
                                        h1: ({ node, ...props }) => {
                                          return (
                                            <span>
                                              # <span {...props} />
                                            </span>
                                          );
                                        },
                                        h2: ({ node, ...props }) => {
                                          return (
                                            <span>
                                              ## <span {...props} />
                                            </span>
                                          );
                                        },
                                        h3: ({ node, ...props }) => {
                                          return (
                                            <span>
                                              ### <span {...props} />
                                            </span>
                                          );
                                        },
                                        em: "span",
                                        ol: ({ node, ...props }) => {
                                          return (
                                            <ol
                                              {...props}
                                              className="list-inside list-decimal"
                                            />
                                          );
                                        },
                                        ul: ({ node, ...props }) => {
                                          const children =
                                            // @ts-expect-error children is not defined on the node
                                            props.children[1].props.children;
                                          return (
                                            <ol
                                              {...props}
                                              className="list-inside"
                                            >
                                              <li>- {children}</li>
                                            </ol>
                                          );
                                        },
                                        strong: ({ node, ...props }) => {
                                          return (
                                            <span
                                              className="font-bold"
                                              {...props}
                                            />
                                          );
                                        },
                                        code: ({ node, ...props }) => {
                                          return (
                                            <span>
                                              `<span {...props} />`
                                            </span>
                                          );
                                        },
                                        a: ({ node, ...props }) => {
                                          return (
                                            <a
                                              className="text-[#0096F6] hover:underline hover:decoration-[#00AAFC]"
                                              {...props}
                                            />
                                          );
                                        },
                                      }}
                                    />
                                  </div>
                                );
                              }
                            })}
                          </div>
                        </AnimateChangeInHeight>
                        {thread.assets.length === 0 && (
                          <LinkPreview url={firstWebsite} channel="Threads" />
                        )}
                        <ThreadsAssets assets={thread.assets} />
                        <div className="flex items-center gap-8 pt-3">
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M1.84375 7.54044V7.54043V7.53125C1.84375 5.45396 3.24485 3.75 5.34375 3.75C6.30585 3.75 7.0621 4.19695 7.64852 4.80257L8.0013 4.46097L7.64852 4.80257C8.01255 5.17852 8.31501 5.61962 8.56473 6.06138L8.99999 6.83137L9.43527 6.06139C9.685 5.61962 9.98746 5.17852 10.3515 4.80258C10.9379 4.19694 11.6942 3.75 12.6562 3.75C14.7551 3.75 16.1562 5.45395 16.1562 7.53125V7.54043V7.54053C16.1563 8.03206 16.1549 8.68056 15.8678 9.48838L15.8678 9.48839C15.5791 10.3005 15.0049 11.2656 13.8696 12.4008L13.8696 12.4009C12.0837 14.1869 10.123 15.6187 9.37979 16.1404L9.66705 16.5496L9.37979 16.1404C9.15087 16.301 8.85009 16.3011 8.62105 16.1404C7.87771 15.6188 5.9164 14.187 4.13037 12.4009L4.13036 12.4008C2.99504 11.2656 2.4209 10.3005 2.13222 9.48839C1.84508 8.68055 1.84374 8.03207 1.84375 7.54044Z"
                              stroke="#424242"
                            />
                          </svg>
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M14.876 13.2177V13.2803L14.8914 13.341L15.5941 16.1033L12.834 15.4003L12.7733 15.3848H12.7106H12.7104H12.7102H12.7102H12.71H12.7098H12.7096H12.7094H12.7092H12.709H12.7089H12.7087H12.7085H12.7083H12.7081H12.7079H12.7077H12.7075H12.7073H12.7071H12.7068H12.7066H12.7064H12.7062H12.706H12.7058H12.7056H12.7054H12.7052H12.705H12.7048H12.7046H12.7043H12.7041H12.7039H12.7037H12.7035H12.7033H12.7031H12.7028H12.7026H12.7024H12.7022H12.702H12.7018H12.7015H12.7013H12.7011H12.7009H12.7007H12.7004H12.7002H12.7H12.6998H12.6995H12.6993H12.6991H12.6989H12.6986H12.6984H12.6982H12.6979H12.6977H12.6975H12.6972H12.697H12.6968H12.6965H12.6963H12.6961H12.6958H12.6956H12.6954H12.6951H12.6949H12.6947H12.6944H12.6942H12.6939H12.6937H12.6935H12.6932H12.693H12.6927H12.6925H12.6923H12.692H12.6918H12.6915H12.6913H12.691H12.6908H12.6906H12.6903H12.6901H12.6898H12.6896H12.6893H12.6891H12.6888H12.6886H12.6883H12.6881H12.6878H12.6876H12.6873H12.6871H12.6868H12.6866H12.6863H12.686H12.6858H12.6855H12.6853H12.685H12.6848H12.6845H12.6843H12.684H12.6837H12.6835H12.6832H12.683H12.6827H12.6824H12.6822H12.6819H12.6817H12.6814H12.6811H12.6809H12.6806H12.6804H12.6801H12.6798H12.6796H12.6793H12.679H12.6788H12.6785H12.6782H12.678H12.6777H12.6774H12.6772H12.6769H12.6766H12.6764H12.6761H12.6758H12.6756H12.6753H12.675H12.6748H12.6745H12.6742H12.6739H12.6737H12.6734H12.6731H12.6729H12.6726H12.6723H12.672H12.6718H12.6715H12.6712H12.671H12.6707H12.6704H12.6701H12.6699H12.6696H12.6693H12.669H12.6688H12.6685H12.6682H12.6679H12.6676H12.6674H12.6671H12.6668H12.6665H12.6663H12.666H12.6657H12.6654H12.6651H12.6649H12.6646H12.6643H12.664H12.6638H12.6635H12.6632H12.6629H12.6626H12.6623H12.6621H12.6618H12.6615H12.6612H12.6609H12.6607H12.6604H12.6601H12.6598H12.6595H12.6593H12.659H12.6587H12.6584H12.6581H12.6578H12.6576H12.6573H12.657H12.6567H12.6564H12.6561H12.6559H12.6556H12.6553H12.655H12.6547H12.6544H12.6542H12.6539H12.6536H12.6533H12.653H12.6527H12.6524H12.6522H12.6519H12.6516H12.6513H12.651H12.6507H12.6504H12.6502H12.6499H12.6496H12.6493H12.649H12.6487H12.6485H12.6482H12.6479H12.6476H12.6473H12.647H12.6467H12.6465H12.6462H12.6459H12.6456H12.6453H12.645H12.6447H12.6445H12.6442H12.6439H12.6436H12.6433H12.643H12.6427H12.6425H12.6422H12.6419H12.6416H12.6413H12.641H12.6407H12.6405H12.6402H12.6399H12.6396H12.6393H12.639H12.6387H12.6385H12.6382H12.6379H12.6376H12.6373H12.637H12.6368H12.6365H12.6362H12.6359H12.6356H12.6353H12.6351H12.6348H12.6345H12.6342H12.6339H12.6336H12.6334H12.6331H12.6328H12.6325H12.6322H12.6319H12.6317H12.6314H12.6311H12.6308H12.6305H12.6302H12.63H12.6297H12.6294H12.6291H12.6288H12.6286H12.6283H12.628H12.6277H12.6274H12.6272H12.6269H12.6266H12.6263H12.626H12.6258H12.6255H12.6252H12.6249H12.6247H12.6244H12.6241H12.6238H12.6235H12.6233H12.623H12.6227H12.6224H12.6222H12.6219H12.6216H12.6213H12.6211H12.6208H12.6205H12.6202H12.62H12.6197H12.6194H12.6191H12.6189H12.6186H12.6183H12.6181H12.6178H12.6175H12.6172H12.617H12.6167H12.6164H12.6162H12.6159H12.6156H12.6154H12.6151H12.6148H12.6146H12.6143H12.614H12.6138H12.6135H12.6132H12.613H12.6127H12.6124H12.6122H12.6119H12.6116H12.6114H12.6111H12.6108H12.6106H12.6103H12.61H12.6098H12.6095H12.6093H12.609H12.6087H12.6085H12.6082H12.608H12.6077H12.6074H12.6072H12.6069H12.6067H12.6064H12.6062H12.6059H12.6057H12.6054H12.6051H12.6049H12.6046H12.6044H12.6041H12.6039H12.6036H12.6034H12.6031H12.6029H12.6026H12.6024H12.6021H12.6019H12.6016H12.6014H12.6011H12.6009H12.6006H12.6004H12.6001H12.5999H12.5997H12.5994H12.5992H12.5989H12.5987H12.5984H12.5982H12.598H12.5977H12.5975H12.5972H12.597H12.5968H12.5965H12.5963H12.5961H12.5958H12.5956H12.5953H12.5951H12.5949H12.5946H12.5944H12.5942H12.5939H12.5937H12.5935H12.5933H12.593H12.5928H12.5926H12.5923H12.5921H12.5919H12.5917H12.5914H12.5912H12.591H12.5908H12.5905H12.5903H12.5901H12.5899H12.5897H12.5894H12.5892H12.589H12.5888H12.5886H12.5883H12.5881H12.5879H12.5877H12.5875H12.5873H12.5871H12.5868H12.5866H12.5864H12.5862H12.586H12.5858H12.5856H12.5854H12.5852H12.585H12.5848H12.5845H12.5843H12.5841H12.5839H12.5837H12.5835H12.5833H12.5831H12.5829H12.5827H12.5825H12.5823H12.5821H12.5819H12.5818H12.5816H12.5814H12.5812H12.581H12.5808H12.5806H12.4485L12.3337 15.45C11.2216 16.0815 9.89022 16.3879 8.45458 16.2967C5.22146 16.0535 2.55482 13.4476 2.22146 10.2077C1.79798 5.90336 5.40187 2.29782 9.70252 2.72147C12.9422 3.05524 15.5456 5.69356 15.7875 8.9612L15.7874 8.9612L15.7879 8.96666C15.9089 10.3898 15.5764 11.7207 14.9411 12.8409L14.876 12.9557V13.0876V13.088V13.0884V13.0887V13.0891V13.0895V13.0899V13.0902V13.0906V13.091V13.0914V13.0917V13.0921V13.0925V13.0929V13.0932V13.0936V13.094V13.0943V13.0947V13.0951V13.0954V13.0958V13.0962V13.0965V13.0969V13.0973V13.0976V13.098V13.0983V13.0987V13.0991V13.0994V13.0998V13.1001V13.1005V13.1008V13.1012V13.1015V13.1019V13.1023V13.1026V13.103V13.1033V13.1037V13.104V13.1044V13.1047V13.1051V13.1054V13.1057V13.1061V13.1064V13.1068V13.1071V13.1075V13.1078V13.1081V13.1085V13.1088V13.1092V13.1095V13.1098V13.1102V13.1105V13.1109V13.1112V13.1115V13.1119V13.1122V13.1125V13.1129V13.1132V13.1135V13.1138V13.1142V13.1145V13.1148V13.1152V13.1155V13.1158V13.1161V13.1165V13.1168V13.1171V13.1174V13.1178V13.1181V13.1184V13.1187V13.119V13.1194V13.1197V13.12V13.1203V13.1206V13.121V13.1213V13.1216V13.1219V13.1222V13.1225V13.1228V13.1232V13.1235V13.1238V13.1241V13.1244V13.1247V13.125V13.1253V13.1256V13.1259V13.1262V13.1266V13.1269V13.1272V13.1275V13.1278V13.1281V13.1284V13.1287V13.129V13.1293V13.1296V13.1299V13.1302V13.1305V13.1308V13.1311V13.1314V13.1317V13.132V13.1323V13.1326V13.1328V13.1331V13.1334V13.1337V13.134V13.1343V13.1346V13.1349V13.1352V13.1355V13.1358V13.136V13.1363V13.1366V13.1369V13.1372V13.1375V13.1378V13.1381V13.1383V13.1386V13.1389V13.1392V13.1395V13.1397V13.14V13.1403V13.1406V13.1409V13.1411V13.1414V13.1417V13.142V13.1423V13.1425V13.1428V13.1431V13.1434V13.1436V13.1439V13.1442V13.1445V13.1447V13.145V13.1453V13.1455V13.1458V13.1461V13.1464V13.1466V13.1469V13.1472V13.1474V13.1477V13.148V13.1482V13.1485V13.1488V13.149V13.1493V13.1496V13.1498V13.1501V13.1503V13.1506V13.1509V13.1511V13.1514V13.1516V13.1519V13.1522V13.1524V13.1527V13.1529V13.1532V13.1535V13.1537V13.154V13.1542V13.1545V13.1547V13.155V13.1552V13.1555V13.1558V13.156V13.1563V13.1565V13.1568V13.157V13.1573V13.1575V13.1578V13.158V13.1583V13.1585V13.1588V13.159V13.1593V13.1595V13.1597V13.16V13.1602V13.1605V13.1607V13.161V13.1612V13.1615V13.1617V13.1619V13.1622V13.1624V13.1627V13.1629V13.1632V13.1634V13.1636V13.1639V13.1641V13.1644V13.1646V13.1648V13.1651V13.1653V13.1655V13.1658V13.166V13.1663V13.1665V13.1667V13.167V13.1672V13.1674V13.1677V13.1679V13.1681V13.1684V13.1686V13.1688V13.1691V13.1693V13.1695V13.1698V13.17V13.1702V13.1705V13.1707V13.1709V13.1711V13.1714V13.1716V13.1718V13.1721V13.1723V13.1725V13.1727V13.173V13.1732V13.1734V13.1736V13.1739V13.1741V13.1743V13.1745V13.1748V13.175V13.1752V13.1754V13.1757V13.1759V13.1761V13.1763V13.1765V13.1768V13.177V13.1772V13.1774V13.1776V13.1779V13.1781V13.1783V13.1785V13.1787V13.179V13.1792V13.1794V13.1796V13.1798V13.18V13.1803V13.1805V13.1807V13.1809V13.1811V13.1813V13.1816V13.1818V13.182V13.1822V13.1824V13.1826V13.1828V13.183V13.1833V13.1835V13.1837V13.1839V13.1841V13.1843V13.1845V13.1847V13.185V13.1852V13.1854V13.1856V13.1858V13.186V13.1862V13.1864V13.1866V13.1868V13.187V13.1873V13.1875V13.1877V13.1879V13.1881V13.1883V13.1885V13.1887V13.1889V13.1891V13.1893V13.1895V13.1897V13.1899V13.1902V13.1904V13.1906V13.1908V13.191V13.1912V13.1914V13.1916V13.1918V13.192V13.1922V13.1924V13.1926V13.1928V13.193V13.1932V13.1934V13.1936V13.1938V13.194V13.1942V13.1944V13.1946V13.1948V13.195V13.1952V13.1954V13.1956V13.1958V13.196V13.1962V13.1964V13.1966V13.1968V13.197V13.1972V13.1974V13.1976V13.1978V13.198V13.1982V13.1984V13.1986V13.1988V13.199V13.1992V13.1994V13.1996V13.1998V13.2V13.2002V13.2004V13.2006V13.2008V13.201V13.2012V13.2014V13.2016V13.2017V13.2019V13.2021V13.2023V13.2025V13.2027V13.2029V13.2031V13.2033V13.2035V13.2037V13.2039V13.2041V13.2043V13.2045V13.2047V13.2049V13.2051V13.2052V13.2054V13.2056V13.2058V13.206V13.2062V13.2064V13.2066V13.2068V13.207V13.2072V13.2074V13.2076V13.2078V13.208V13.2081V13.2083V13.2085V13.2087V13.2089V13.2091V13.2093V13.2095V13.2097V13.2099V13.2101V13.2103V13.2104V13.2106V13.2108V13.211V13.2112V13.2114V13.2116V13.2118V13.212V13.2122V13.2124V13.2126V13.2127V13.2129V13.2131V13.2133V13.2135V13.2137V13.2139V13.2141V13.2143V13.2145V13.2147V13.2148V13.215V13.2152V13.2154V13.2156V13.2158V13.216V13.2162V13.2164V13.2166V13.2167V13.2169V13.2171V13.2173V13.2175Z"
                              stroke="#424242"
                            />
                          </svg>
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g clipPath="url(#clip0_3234_2692)">
                              <path
                                d="M6.41257 1.23531C6.63491 0.971277 7.02918 0.937481 7.29321 1.15982L9.96509 3.40982C10.1022 3.52528 10.1831 3.69404 10.1873 3.87324C10.1915 4.05243 10.1186 4.2248 9.98706 4.34656L7.31518 6.81971C7.06186 7.05419 6.66643 7.03892 6.43196 6.7856C6.19748 6.53228 6.21275 6.13685 6.46607 5.90237L7.96721 4.51289H5.20312C3.68435 4.51289 2.45312 5.74411 2.45312 7.26289V9.51289V11.7629C2.45312 13.2817 3.68435 14.5129 5.20312 14.5129C5.54831 14.5129 5.82812 14.7927 5.82812 15.1379C5.82812 15.4831 5.54831 15.7629 5.20312 15.7629C2.99399 15.7629 1.20312 13.972 1.20312 11.7629V9.51289V7.26289C1.20312 5.05375 2.99399 3.26289 5.20312 3.26289H7.85003L6.48804 2.11596C6.22401 1.89362 6.19022 1.49934 6.41257 1.23531Z"
                                fill="#424242"
                              />
                              <path
                                d="M11.5874 17.7904C11.3651 18.0545 10.9708 18.0883 10.7068 17.8659L8.03491 15.6159C7.89781 15.5005 7.81687 15.3317 7.81267 15.1525C7.80847 14.9733 7.8814 14.801 8.01294 14.6792L10.6848 12.206C10.9381 11.9716 11.3336 11.9868 11.568 12.2402C11.8025 12.4935 11.7872 12.8889 11.5339 13.1234L10.0328 14.5129H12.7969C14.3157 14.5129 15.5469 13.2816 15.5469 11.7629V9.51285V7.26285C15.5469 5.74407 14.3157 4.51285 12.7969 4.51285C12.4517 4.51285 12.1719 4.23303 12.1719 3.88785C12.1719 3.54268 12.4517 3.26285 12.7969 3.26285C15.006 3.26285 16.7969 5.05372 16.7969 7.26285V9.51285V11.7629C16.7969 13.972 15.006 15.7629 12.7969 15.7629H10.15L11.512 16.9098C11.776 17.1321 11.8098 17.5264 11.5874 17.7904Z"
                                fill="#424242"
                              />
                            </g>
                            <defs>
                              <clipPath id="clip0_3234_2692">
                                <rect width="18" height="18" fill="white" />
                              </clipPath>
                            </defs>
                          </svg>
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M15.1136 5.02708L15.1136 5.02707C15.2906 4.72673 15.4053 4.53113 15.4762 4.38093C15.5312 4.26439 15.5378 4.21854 15.5386 4.2114C15.5313 4.14649 15.4976 4.0874 15.4454 4.04803C15.4387 4.04509 15.3958 4.02746 15.2674 4.01568C15.102 4.0005 14.8753 4 14.5267 4H3.71406C3.25441 4 2.94368 4.00048 2.72025 4.02025C2.54286 4.03594 2.48425 4.06037 2.47582 4.06343C2.42449 4.11094 2.39663 4.17865 2.39964 4.24853C2.40347 4.25664 2.42792 4.31525 2.54287 4.45126C2.68766 4.62257 2.90805 4.84162 3.23455 5.16516M15.1136 5.02708L3.23455 5.16516M15.1136 5.02708L9.52685 14.509C9.2899 14.9111 9.12899 15.1833 8.99641 15.3689C8.89098 15.5165 8.8393 15.5553 8.83233 15.5611C8.76499 15.5809 8.69241 15.57 8.63397 15.5314M15.1136 5.02708L8.63397 15.5314M3.23455 5.16516L6.46789 8.36914L6.46789 8.36914C6.47554 8.37672 6.48309 8.3842 6.49057 8.39159C6.61663 8.5164 6.71895 8.6177 6.80373 8.73554L6.80374 8.73554C6.8785 8.83947 6.9415 8.95136 6.99158 9.06916L6.9916 9.06921C7.0484 9.20283 7.08195 9.34286 7.1233 9.51544C7.12574 9.52563 7.12821 9.53593 7.13071 9.54635M3.23455 5.16516L7.13071 9.54635M7.13071 9.54635C7.13071 9.54635 7.13071 9.54635 7.13071 9.54636M7.13071 9.54635L7.13071 9.54636M7.13071 9.54636L8.27748 14.3223C8.38646 14.7761 8.46074 15.0834 8.53325 15.2996C8.59095 15.4717 8.62905 15.5239 8.63397 15.5314M7.13071 9.54636L8.63397 15.5314"
                              stroke="#424242"
                            />
                            <path
                              d="M7 9L14.7764 4.5528"
                              stroke="#424242"
                              strokeLinecap="round"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }
};

export default ThreadsPostPreview;
