import {
  BoldIcon,
  CodeBracketIcon,
  ItalicIcon,
  UnderlineIcon,
} from "@heroicons/react/24/outline";
import { Editor } from "@tiptap/react";
import ToolbarButton from "./ToolbarButton";

type TextStyleButtonsProps = {
  editor: Editor;
  features?: {
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    code?: boolean;
  };
};

const TextStyleButtons = ({ editor, features = {} }: TextStyleButtonsProps) => {
  const {
    bold = true,
    italic = true,
    underline = true,
    code = true,
  } = features;

  return (
    <>
      {bold && (
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleBold().run()}
          isActive={editor.isActive("bold")}
          isDisabled={!editor.can().toggleBold()}
        >
          <BoldIcon className="h-4 w-4" />
        </ToolbarButton>
      )}
      {italic && (
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleItalic().run()}
          isActive={editor.isActive("italic")}
          isDisabled={!editor.can().toggleItalic()}
        >
          <ItalicIcon className="h-4 w-4" />
        </ToolbarButton>
      )}
      {underline && editor.schema.nodes.underline && (
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          isActive={editor.isActive("underline")}
          isDisabled={!editor.can().toggleUnderline()}
        >
          <UnderlineIcon className="h-4 w-4" />
        </ToolbarButton>
      )}
      {code && (
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleCode().run()}
          isActive={editor.isActive("code")}
          isDisabled={!editor.can().toggleCode()}
        >
          <CodeBracketIcon className="h-4 w-4" />
        </ToolbarButton>
      )}
    </>
  );
};

export default TextStyleButtons;
