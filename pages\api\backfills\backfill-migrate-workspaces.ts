import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const originalWorkspaceID = "71656e42-da19-4e8d-bac4-bb75fb5b7424";
  const futureWorkspaceID = "dedd6a83-256a-4bbf-b041-0778436ed759";

  const linkedInProfileUsernames = ["christineorchard"];

  const linkedInIntegrations = await db.linkedInIntegration.findMany({
    where: {
      workspaceID: originalWorkspaceID,
      account: {
        vanityName: {
          in: linkedInProfileUsernames,
        },
      },
    },
  });

  const twitterUserNames: string[] = [];

  const twitterIntegrations = await db.twitterIntegration.findMany({
    where: {
      workspaceID: originalWorkspaceID,
      account: {
        username: {
          in: twitterUserNames,
        },
      },
    },
  });

  const posts = await db.post.findMany({
    where: {
      OR: [
        {
          linkedInIntegrationID: {
            in: linkedInIntegrations.map((integration) => integration.id),
          },
        },
        {
          twitterIntegrationID: {
            in: twitterIntegrations.map((integration) => integration.id),
          },
        },
      ],
      workspaceID: originalWorkspaceID,
    },
  });

  await db.post.updateMany({
    where: {
      id: {
        in: posts.map((post) => post.id),
      },
    },
    data: {
      workspaceID: futureWorkspaceID,
    },
  });

  await db.scheduledPost.updateMany({
    where: {
      postID: {
        in: posts.map((post) => post.id),
      },
    },
    data: {
      workspaceID: futureWorkspaceID,
    },
  });

  await db.linkedInIntegration.updateMany({
    where: {
      id: {
        in: linkedInIntegrations.map((integration) => integration.id),
      },
    },
    data: {
      workspaceID: futureWorkspaceID,
    },
  });

  await db.twitterIntegration.updateMany({
    where: {
      id: {
        in: twitterIntegrations.map((integration) => integration.id),
      },
    },
    data: {
      workspaceID: futureWorkspaceID,
    },
  });

  res.status(200).send({
    posts: posts.length,
    linkedInIntegrations: linkedInIntegrations.length,
    twitterIntegrations: twitterIntegrations.length,
  });
};

export default handler;
