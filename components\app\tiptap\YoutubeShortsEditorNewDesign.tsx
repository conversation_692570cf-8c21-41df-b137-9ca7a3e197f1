import {
  useIsEditor<PERSON><PERSON><PERSON>,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Document from "@tiptap/extension-document";
import History from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { ClipboardEvent, useEffect, useRef, useState } from "react";
import { useAutosave } from "react-autosave";
import { usePostIntegrations, useThreadComments } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { stripLiveblocksMarks } from "~/utils";
import ThreadCommentsPopover from "../posts/ThreadCommentsPopover";
import {
  <PERSON>Hashtag,
  CharacterCount,
  CustomCommentThreadMark,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "./extensions";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  postID?: string;
  initialValue: JSONContent | null;
  placeholder: string;
  maxLength: number;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  roomID: string;
};

const createBaseExtensions = (
  postID: string | undefined,
  placeholder: string,
  maxLength: number
) => [
  Document,
  Paragraph,
  Text,
  Emoji,
  History,
  Link.configure({
    isLinkShorteningEnabled: !!postID ? true : false,
    postID,
  }),
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  AutoHashtag.configure({
    urlFn: (hashtag) => `https://www.youtube.com/hashtag/${hashtag}/shorts`,
  }),
  CharacterCount.configure({
    limit: maxLength,
  }),
  PasteHandler,
];

type YoutubeShortsEditorProps = Omit<BaseProps, "roomID">;

type YoutubeShortsEditorFallbackProps = {
  content: JSONContent | null;
  postID?: string;
  placeholder: string;
  maxLength: number;
};

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const YoutubeShortsEditorFallback = ({
  content,
  postID,
  placeholder,
  maxLength,
}: YoutubeShortsEditorFallbackProps) => {
  const cleanContent = stripLiveblocksMarks(content);

  const editor = useEditor({
    extensions: createBaseExtensions(postID, placeholder, maxLength),
    immediatelyRender: false,
    content: cleanContent,
    editable: false,
  });

  if (!editor) return null;

  return <EditorContent editor={editor} />;
};

const YoutubeEditor = ({
  postID,
  initialValue,
  placeholder,
  maxLength,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  roomID,
}: YoutubeShortsEditorProps & { roomID: string }) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );
  const { setEditorForRoom } = useThreadComments();
  const { currentChannel } = usePostIntegrations();

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(postID, placeholder, maxLength),
    ],
    immediatelyRender: false,
    editable: !isReadOnly,
    onUpdate: ({ editor }) => {
      // TipTap doubles newlines for paragraph breaks (e.g., pressing Enter once creates two \n).
      // We halve the newlines to match LinkedIn's expected format while preserving paragraph structure.
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      onUpdateContent(textContent);

      const json = editor.getJSON();
      setEditorState(json);
    },
  });

  const isEditorReady = useIsEditorReady();

  useEffect(() => {
    if (editor && currentChannel === "YouTubeShorts") {
      setEditorForRoom(editor, roomID);
    }

    return () => {
      setEditorForRoom(null, roomID);
    };
  }, [editor, currentChannel, setEditorForRoom, roomID]);

  if (!editor) return null;

  return (
    <div
      className="py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor} shouldHideWithLink={true}>
        <ToolbarActions
          editor={editor}
          features={{
            unicodeFormatting: true,
          }}
        />
      </CustomFloatingToolbar>

      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["YouTubeShorts"].label,
            }}
          />
        </>
      ) : (
        <YoutubeShortsEditorFallback
          content={initialValue}
          placeholder={placeholder}
          maxLength={maxLength}
        />
      )}
    </div>
  );
};

const YouTubeShortsEditorWrapperNewDesign = ({
  postID,
  initialValue,
  placeholder,
  maxLength,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  roomID,
}: BaseProps) => {
  const { currentChannel } = usePostIntegrations();
  const threadCommentsContainerRef = useRef<HTMLDivElement>(null);

  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense
        fallback={
          <div className="py-2">
            <YoutubeShortsEditorFallback
              content={initialValue}
              placeholder={placeholder}
              maxLength={maxLength}
            />
          </div>
        }
      >
        <div className="relative">
          <YoutubeEditor
            postID={postID}
            initialValue={initialValue}
            placeholder={placeholder}
            maxLength={maxLength}
            onSave={onSave}
            onUpdateContent={onUpdateContent}
            onImagePaste={onImagePaste}
            isReadOnly={isReadOnly}
            roomID={roomID}
          />
          <div ref={threadCommentsContainerRef} />
        </div>
      </ClientSideSuspense>
      {currentChannel === "YouTubeShorts" && (
        <ThreadCommentsPopover
          key={roomID}
          roomID={roomID}
          containerRef={threadCommentsContainerRef}
        />
      )}
    </RoomProvider>
  );
};

export default YouTubeShortsEditorWrapperNewDesign;
