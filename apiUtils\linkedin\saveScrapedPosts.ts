import { db } from "~/clients";
import LinkedIn from "~/clients/linkedin/LinkedIn";
import LinkedInScraper from "~/clients/rapid_api/LinkedInScraper";

const saveScrapedPosts = async (integrationID: string) => {
  const integration = await db.linkedInIntegration.findFirstOrThrow({
    where: {
      id: integrationID,
    },
    include: {
      account: true,
    },
  });

  if (integration == null) {
    throw new Error("Integration not found");
  }

  const vanityName = integration.account.vanityName;
  const type = integration.account.type;

  const scraper = new LinkedInScraper();
  const result = await scraper.profile.getPosts(vanityName, type);

  if (!result.success) {
    throw new Error("Failed to get profile and posts");
  }

  const posts = result.data.posts;

  const nonRepostedPosts = posts.filter((post) => post.type !== "Repost");

  const postActivityUrns = nonRepostedPosts.map((post) => post.activityUrn);

  const linkedIn = new LinkedIn(integration.accessToken);

  const postUrnPostResponse =
    await linkedIn.getPostUrnsByActivityUrns(postActivityUrns);

  if (!postUrnPostResponse.success) {
    throw new Error(
      `Failed to get post urns for ${vanityName}: ${postUrnPostResponse.errors.join(
        ", "
      )}`
    );
  }

  const postUrnMap = postUrnPostResponse.data;

  const foundPosts = nonRepostedPosts.filter(
    (post) => postUrnMap[post.activityUrn]
  );

  const postPromises = foundPosts.map(async (post) => {
    const id = postUrnMap[post.activityUrn];

    await db.linkedInPost.upsert({
      where: {
        id,
      },
      create: {
        id: postUrnMap[post.activityUrn],
        activityUrn: post.activityUrn,
        type: post.type,
        authorUrn: integration.account.urn,
        commentary: post.commentary,
        personTagCount: post.personTagCount,
        organizationTagCount: post.organizationTagCount,
        hashtagCount: post.hashtagCount,
        publishedAt: post.publishedAt,
        postCreatedAt: post.publishedAt,
        rawResponse: post,
        isScrapedData: true,
        visibility: "PUBLIC",
        isAdvertisement: false,
      },
      update: {
        activityUrn: post.activityUrn,
        commentary: post.commentary,
      },
    });
  });

  await Promise.all(postPromises);
};

export default saveScrapedPosts;
