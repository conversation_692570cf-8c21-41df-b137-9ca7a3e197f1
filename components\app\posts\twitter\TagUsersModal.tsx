import { UserCircleIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { AnimatePresence, motion } from "framer-motion";
import uniq from "lodash/uniq";
import { useEffect, useState } from "react";
import { TextInput, TextLink, Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { handleTrpcError, trpc } from "~/utils";

type Props = {
  tweet: {
    id: string;
    mediaTaggedUsernames: string[];
  };
  isDisabled: boolean;
};

const TagUsersModal = ({ tweet, isDisabled }: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const [username, setUsername] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [taggedUsernames, setTaggedUsernames] = useState<string[]>(
    tweet.mediaTaggedUsernames
  );

  const addCollaboratorMutation = trpc.tweetContent.tagUser.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Unable to Add Collaborator", error });
    },
  });
  const removeCollaboratorMutation =
    trpc.tweetContent.removeTaggedUser.useMutation();

  useEffect(() => {
    setTaggedUsernames(tweet.mediaTaggedUsernames);
  }, [tweet.id]);

  return (
    <div>
      {taggedUsernames.length === 0 ? (
        <Tooltip
          value={isDisabled ? "Add assets to tag users" : "Tag users in media"}
        >
          <Button
            variant="ghost"
            size="sm"
            disabled={isDisabled}
            onClick={() => setOpen(true)}
          >
            <UserCircleIcon className="h-4 w-4" />
          </Button>
        </Tooltip>
      ) : (
        <Tooltip
          value={isDisabled ? "Add assets to tag users" : "Tag users in media"}
        >
          <Button
            variant="ghost"
            size="sm"
            disabled={isDisabled}
            onClick={() => setOpen(true)}
          >
            <UserCircleIcon className="h-4 w-4" />
            {taggedUsernames.length === 1
              ? taggedUsernames[0]
              : `${taggedUsernames.length} Users`}
          </Button>
        </Tooltip>
      )}
      <Credenza.Root open={open} onOpenChange={setOpen}>
        <Credenza.Header>
          <Credenza.Title>Tag Users</Credenza.Title>
          <Credenza.Description>
            Tagging only works for public accounts. If the user you're tagging
            has disabled photo-tagging enabled, the Tweet will post without
            their tag.
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <div className="mt-5 flex items-center gap-2">
            <div className="w-40">
              <TextInput
                placeholder="@twitter-handle"
                value={username}
                onChange={setUsername}
              />
            </div>
            <Button
              size="sm"
              onClick={() => {
                const cleanedUsername = username.replace("@", "");
                if (taggedUsernames.length < 10) {
                  if (cleanedUsername.length === 0) {
                    setError("Please enter a Twitter handle.");
                  } else {
                    addCollaboratorMutation.mutate({
                      id: tweet.id,
                      username: cleanedUsername,
                    });
                    setError(null);
                    setUsername("");
                    setTaggedUsernames(
                      uniq([...taggedUsernames, cleanedUsername])
                    );
                  }
                } else {
                  setError(
                    "You can only mention up to 10 accounts on Twitter media."
                  );
                }
              }}
            >
              Save
            </Button>
          </div>
          {error != null && (
            <div className="text-danger font-body-sm mt-2">{error}</div>
          )}
          <AnimatePresence>
            <div className="mt-5 flex w-full items-center gap-2 overflow-x-auto">
              {taggedUsernames.map((username) => {
                return (
                  <motion.div
                    key={username}
                    initial={{ opacity: 0, width: "fit" }}
                    animate={{ opacity: 1, width: "fit" }}
                    exit={{ opacity: 0, width: 0 }}
                    className="w-content box-shadow font-body-sm text-secondary scrollbar-hidden mb-3 flex items-center gap-2 rounded-lg px-4 py-2.5 whitespace-nowrap"
                  >
                    <TextLink
                      value={username}
                      color="secondary"
                      size="sm"
                      href={`https://www.twitter.com/${username}`}
                    />
                    <button
                      className="hover:bg-lightest-gray/50 rounded-full p-0.5 transition-colors"
                      onClick={() => {
                        setError(null);
                        removeCollaboratorMutation.mutate({
                          id: tweet.id,
                          username,
                        });
                        setTaggedUsernames(
                          taggedUsernames.filter(
                            (currentUsernames) => currentUsernames !== username
                          )
                        );
                      }}
                    >
                      <XMarkIcon className="text-medium-gray h-4 w-4" />
                    </button>
                  </motion.div>
                );
              })}
            </div>
          </AnimatePresence>
        </Credenza.Body>
      </Credenza.Root>
    </div>
  );
};

export default TagUsersModal;
