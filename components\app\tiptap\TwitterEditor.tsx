import Document from "@tiptap/extension-document";
import History from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { ClipboardEvent, useEffect, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import AutoHashtag from "~/components/app/tiptap/extensions/AutoHashtag";
import AutoMention from "~/components/app/tiptap/extensions/AutoMention";
import CharacterCount from "~/components/app/tiptap/extensions/CharacterCount";
import { charCount, formatTextAsTipTapEditorState } from "~/utils";
import { BackspaceOnEmpty, Emoji, Link, PasteHandler } from "./extensions";
import BoldUnicodeButton from "./ui/BoldUnicodeButton";
import FloatingToolbar from "./ui/FloatingToolbar";
import ItalicsUnicodeButton from "./ui/ItalicsUnicodeButton";

type Props = {
  title?: string;
  postID?: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  onFocusChange?: (isFocused: boolean) => void;
  size?: "sm" | "md";
  autoFocus?: boolean;
  previousTweetID?: string | null;
  tweetID?: string;
  addTweet?: (nextTweetCopy: string) => void;
  deleteTweet?: () => void;
  defaultMaxLength?: number;
};

const TwitterEditor = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  onFocusChange,
  size = "md",
  autoFocus = false,
  addTweet,
  deleteTweet,
  defaultMaxLength = 280,
}: Props) => {
  const [textContent, setTextContent] = useState<string | null>(null);
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const maxLength =
    defaultMaxLength +
    (textContent?.length || 0) -
    charCount(textContent || "", {
      isTwitter: true,
    });

  const editor = useEditor({
    extensions: [
      Document,
      Paragraph.configure({
        HTMLAttributes: {
          class: size === "sm" ? "font-body-sm" : "font-body",
        },
      }),
      TextDirection.configure({
        types: ["heading", "paragraph"],
      }),
      Text,
      History,
      Emoji,
      Link.configure({
        isLinkShorteningEnabled: !!postID ? true : false,
        postID,
      }),
      BackspaceOnEmpty.configure({
        onBackspace: () => {
          if (deleteTweet) {
            deleteTweet();
          }
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      AutoHashtag.configure({
        urlFn: (hashtag) => `https://x.com/hashtag/${hashtag}`,
      }),
      CharacterCount.configure({
        limit: maxLength,
        isTwitter: true,
      }),
      AutoMention.configure({
        baseUrl: "https://x.com/",
        regex: /(^|[\s.,])(@[a-zA-Z0-9_]{1,15})/g,
      }),
      PasteHandler,
    ],
    onCreate: ({ editor }) => {
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      setTextContent(textContent);
    },
    autofocus: autoFocus,
    onFocus: () => {
      if (onFocusChange) {
        onFocusChange(true);
      }
    },
    onBlur: () => {
      if (onFocusChange) {
        onFocusChange(false);
      }
    },
    onUpdate: ({ editor }) => {
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });

      // Add an empty tweet after this one
      if (addTweet && textContent.endsWith("\n\n\n")) {
        const newTextContent = textContent.trimEnd();

        editor
          .chain()
          .blur()
          .setContent(formatTextAsTipTapEditorState(newTextContent))
          .run();

        setTextContent(newTextContent);
        onUpdateContent(newTextContent);
        addTweet("");
        setEditorState(formatTextAsTipTapEditorState(newTextContent));
      } else if (addTweet && textContent.includes("\n\n\n\n\n")) {
        const splits = textContent.split("\n\n\n\n\n");
        const newTextContent = splits[0].trimEnd();
        const nextTextContent = splits[1].trimEnd();

        const newEditorState = formatTextAsTipTapEditorState(newTextContent);

        editor.chain().blur().setContent(newEditorState).run();

        setTextContent(newTextContent);
        onUpdateContent(newTextContent);
        setEditorState(newEditorState);
        addTweet(nextTextContent);
      } else {
        setTextContent(textContent);
        onUpdateContent(textContent);
        setEditorState(editor.getJSON());
      }
    },
    content: editorState,
    immediatelyRender: false,
    editable: !isReadOnly,
  });

  useEffect(() => {
    if (editor) {
      editor.setOptions({
        extensions: editor.options.extensions.map((extension) => {
          if (extension.name === "characterCount") {
            return CharacterCount.configure({
              limit: maxLength,
            });
          }
          return extension;
        }),
      });
    }
  }, [editor, maxLength]);

  return (
    <div
      className="py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <FloatingToolbar editor={editor} shouldHideWithLink={true}>
        <BoldUnicodeButton editor={editor} useCustomToolbarButton={true} />
        <ItalicsUnicodeButton editor={editor} useCustomToolbarButton={true} />
      </FloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}
      <EditorContent editor={editor} />
    </div>
  );
};

export default TwitterEditor;
