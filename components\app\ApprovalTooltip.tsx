import {
  CheckBadgeIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { ApprovalStatus } from "@prisma/client";
import classNames from "classnames";
import { Tooltip, UserAvatar } from "~/components";

type User = {
  id: string;
  firstName: string | null;
  lastName: string | null;
};

export type Approval = {
  id: string;
  user: User;
  status: ApprovalStatus;
  isBlocking: boolean;
  approvedAt: Date | null;
};

interface Props {
  approvals: Approval[];
}

const ApprovalTooltip = ({ approvals }: Props) => {
  const approvedApprovals = approvals.filter(
    (approval) => approval.status === "Approved"
  );
  const hasBlockingUnapproved = approvals.some(
    (approval) => approval.isBlocking && approval.status !== "Approved"
  );

  const approvalsCount = approvedApprovals.length;
  const totalApprovals = approvals.length;

  return (
    <Tooltip
      align="end"
      side="bottom"
      className="px-3.5 py-3"
      value={
        <div className="flex flex-col gap-3">
          <span className="font-body-sm font-medium">Approvals</span>
          {approvals.map((approval) => (
            <div key={approval.id} className="flex items-center gap-2">
              <UserAvatar
                user={approval.user}
                backgroundColor="secondary"
                showCheck={approval.status === "Approved"}
              />
              <div className="flex items-center gap-2">
                <span className="font-body-sm">
                  {approval.user.firstName} {approval.user.lastName}
                </span>
                {approval.isBlocking && approval.status !== "Approved" && (
                  <div className="bg-warning w-fit rounded-xs px-[3.75px] py-[3.19px] text-white">
                    <ExclamationTriangleIcon className="h-4 w-4" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      }
    >
      <div
        className={classNames(
          "font-eyebrow flex w-fit items-center gap-0.5 rounded-xs px-1 py-[5px]",
          {
            "bg-lightest-gray-50 text-medium-dark-gray": !hasBlockingUnapproved,
            "bg-warning text-white": hasBlockingUnapproved,
          }
        )}
      >
        {hasBlockingUnapproved ? (
          <ExclamationTriangleIcon className="h-3.5 w-3.5" />
        ) : (
          <CheckBadgeIcon className="h-4 w-4" />
        )}
        {approvalsCount}/{totalApprovals}
      </div>
    </Tooltip>
  );
};

export default ApprovalTooltip;
