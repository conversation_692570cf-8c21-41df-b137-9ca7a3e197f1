import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Document from "@tiptap/extension-document";
import History from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { ClipboardEvent, ReactNode, useState } from "react";
import { useAutosave } from "react-autosave";
import HelpTooltip from "~/components/HelpTooltip";
import { CHANNEL_ICON_MAP } from "~/types";
import { stripLiveblocksMarks } from "~/utils";
import { Threads } from "./Threads";
import {
  AutoHashtag,
  CharacterCount,
  CustomCommentThreadMark,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>e<PERSON><PERSON><PERSON>,
} from "./extensions";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  title: string;
  postID?: string;
  tooltipContent: ReactNode;
  initialValue: JSONContent | null;
  placeholder: string;
  maxLength: number;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  roomID: string;
};

type YoutubeShortsEditorProps = Omit<BaseProps, "roomID">;

const createBaseExtensions = (
  postID: string | undefined,
  placeholder: string,
  maxLength: number
) => [
  Document,
  Paragraph,
  Text,
  Emoji,
  History,
  Link.configure({
    isLinkShorteningEnabled: !!postID ? true : false,
    postID,
  }),
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  AutoHashtag.configure({
    urlFn: (hashtag) => `https://www.youtube.com/hashtag/${hashtag}/shorts`,
  }),
  CharacterCount.configure({
    limit: maxLength,
  }),
  PasteHandler,
];

type YoutubeShortsEditorFallbackProps = {
  content: JSONContent | null;
  postID?: string;
  placeholder: string;
  maxLength: number;
};

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const YoutubeShortsEditorFallback = ({
  content,
  postID,
  placeholder,
  maxLength,
}: YoutubeShortsEditorFallbackProps) => {
  const cleanContent = stripLiveblocksMarks(content);

  const editor = useEditor({
    extensions: createBaseExtensions(postID, placeholder, maxLength),
    immediatelyRender: false,
    content: cleanContent,
    editable: false,
  });

  if (!editor) return null;

  return <EditorContent editor={editor} />;
};

const YoutubeEditor = ({
  title,
  postID,
  tooltipContent,
  initialValue,
  placeholder,
  maxLength,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
}: YoutubeShortsEditorProps) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(postID, placeholder, maxLength),
    ],
    immediatelyRender: false,
    editable: !isReadOnly,
    onUpdate: ({ editor }) => {
      // TipTap doubles newlines for paragraph breaks (e.g., pressing Enter once creates two \n).
      // We halve the newlines to match LinkedIn's expected format while preserving paragraph structure.
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      onUpdateContent(textContent);

      const json = editor.getJSON();
      setEditorState(json);
    },
  });

  const isEditorReady = useIsEditorReady();

  if (!editor) return null;

  return (
    <div
      className="py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <div className="flex items-center gap-1">
        <div className="font-eyebrow text-dark-gray">{title}</div>
        <HelpTooltip value={tooltipContent} />
      </div>

      <CustomFloatingToolbar editor={editor} shouldHideWithLink={true}>
        <ToolbarActions
          editor={editor}
          features={{
            unicodeFormatting: true,
          }}
        />
      </CustomFloatingToolbar>

      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["YouTubeShorts"].label,
            }}
          />
          <ClientSideSuspense fallback={null}>
            <Threads editor={editor} />
          </ClientSideSuspense>
        </>
      ) : (
        <YoutubeShortsEditorFallback
          content={initialValue}
          placeholder={placeholder}
          maxLength={maxLength}
        />
      )}
    </div>
  );
};

const YouTubeShortsEditorWrapper = ({
  title,
  postID,
  tooltipContent,
  initialValue,
  placeholder,
  maxLength,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  roomID,
}: BaseProps) => {
  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense
        fallback={
          <div className="py-2">
            <div className="flex items-center gap-1">
              <div className="font-eyebrow text-dark-gray">{title}</div>
              <HelpTooltip value={tooltipContent} />
            </div>
            <YoutubeShortsEditorFallback
              content={initialValue}
              placeholder={placeholder}
              maxLength={maxLength}
            />
          </div>
        }
      >
        <YoutubeEditor
          title={title}
          postID={postID}
          tooltipContent={tooltipContent}
          initialValue={initialValue}
          placeholder={placeholder}
          maxLength={maxLength}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
          onImagePaste={onImagePaste}
          isReadOnly={isReadOnly}
        />
      </ClientSideSuspense>
    </RoomProvider>
  );
};

export default YouTubeShortsEditorWrapper;
