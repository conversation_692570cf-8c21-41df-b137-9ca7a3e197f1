import sum from "lodash/sum";

export type WorkspaceSocialProfileUsage = {
  _count: {
    userWorkspaces: number;
    twitterIntegrations: number;
    instagramIntegrations: number;
    threadsIntegrations: number;
    linkedInIntegrations: number;
    discordWebhooks: number;
    tikTokIntegrations: number;
    slackIntegrations: number;
    facebookIntegrations: number;
    youTubeIntegrations: number;
    webflowIntegrations: number;
  };
};

type Company = {
  workspaces: WorkspaceSocialProfileUsage[];
};

const countSocialProfiles = (
  companyOrWorkspace: Company | WorkspaceSocialProfileUsage
) => {
  if (Object.keys(companyOrWorkspace).includes("workspaces")) {
    const company = companyOrWorkspace as Company;
    return sum([
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.twitterIntegrations
            : 0),
        0
      ),
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.instagramIntegrations
            : 0),
        0
      ),
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.threadsIntegrations
            : 0),
        0
      ),
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.linkedInIntegrations
            : 0),
        0
      ),
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.discordWebhooks
            : 0),
        0
      ),
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.tikTokIntegrations
            : 0),
        0
      ),
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.facebookIntegrations
            : 0),
        0
      ),
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.youTubeIntegrations
            : 0),
        0
      ),
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.slackIntegrations
            : 0),
        0
      ),
      company.workspaces.reduce(
        (acc, workspace) =>
          acc +
          (workspace._count.userWorkspaces > 0
            ? workspace._count.webflowIntegrations
            : 0),
        0
      ),
    ]);
  } else {
    const workspace = companyOrWorkspace as WorkspaceSocialProfileUsage;

    return sum([
      workspace._count.twitterIntegrations,
      workspace._count.instagramIntegrations,
      workspace._count.threadsIntegrations,
      workspace._count.linkedInIntegrations,
      workspace._count.discordWebhooks,
      workspace._count.tikTokIntegrations,
      workspace._count.facebookIntegrations,
      workspace._count.youTubeIntegrations,
      workspace._count.slackIntegrations,
      workspace._count.webflowIntegrations,
    ]);
  }
};

export default countSocialProfiles;
