import { useHover } from "@mantine/hooks";

type Props = {
  assets: {
    id: string;
    signedUrl: string;
    width?: number | null;
    height?: number | null;
    metadata: {
      mimetype: string;
    };
  }[];
};

const ThreadsAssets = ({ assets }: Props) => {
  const numAssets = assets.length || 0;
  const { hovered, ref } = useHover();

  if (numAssets === 0) {
    return null;
  } else if (numAssets === 1) {
    const asset = assets[0];
    const isVideo = asset.metadata.mimetype.startsWith("video");
    if (isVideo) {
      return (
        <div className="max-h-[430px] w-fit overflow-hidden" ref={ref}>
          <video
            playsInline={true}
            controls={hovered}
            className="h-auto max-h-[430px] rounded-[8px] object-contain"
            src={asset.signedUrl}
          />
        </div>
      );
    } else {
      return (
        <div className="max-h-[430px] w-full overflow-hidden">
          <img
            className="h-auto max-h-[430px] rounded-[8px] object-contain"
            src={assets[0].signedUrl}
          />
        </div>
      );
    }
  } else if (numAssets === 2) {
    const calculateWidths = (width1: number, width2: number) => {
      const totalWidth = width1 + width2;
      let ratio = width1 / totalWidth;

      // Clamp ratio between 3/8 (0.375) and 9/16 (0.5625)
      ratio = Math.max(0.375, Math.min(0.5625, ratio));

      return {
        firstWidth: `${ratio * 100}%`,
        secondWidth: `${(1 - ratio) * 100}%`,
      };
    };

    const ratio1 = (assets[0].width || 1000) / (assets[0].height || 1000);
    const ratio2 = (assets[1].width || 1000) / (assets[1].height || 1000);
    const width1 = ratio1 * 1000;
    const width2 = ratio2 * 1000;

    const { firstWidth, secondWidth } = calculateWidths(width1, width2);

    return (
      <div className="flex max-h-[362px] w-full gap-1">
        <div
          className={`relative overflow-hidden rounded-[8px]`}
          style={{ width: firstWidth }}
        >
          {assets[0].metadata.mimetype.startsWith("video") ? (
            <video
              src={assets[0].signedUrl}
              className="h-full w-full object-cover"
              controls
            />
          ) : (
            <img
              src={assets[0].signedUrl}
              className="h-full w-full object-cover"
            />
          )}
        </div>
        <div
          className={`relative overflow-hidden rounded-[8px]`}
          style={{ width: secondWidth }}
        >
          {assets[1].metadata.mimetype.startsWith("video") ? (
            <video
              src={assets[1].signedUrl}
              className="h-full w-full object-cover"
              controls
            />
          ) : (
            <img
              src={assets[1].signedUrl}
              className="h-full w-full object-cover"
            />
          )}
        </div>
      </div>
    );
  } else {
    const firstAsset = assets[0];
    const firstWidth = firstAsset.width || 1000;
    const firstHeight = firstAsset.height || 1000;
    const firstRatio = firstWidth / firstHeight;

    const minHeight = 235;
    const maxWidth = 313;
    const maxHeight = 280;

    let height: number;
    let width: number;

    if (firstRatio > maxWidth / maxHeight) {
      // Asset is wider than the max aspect ratio
      width = maxWidth;
      height = Math.max(Math.min(maxWidth / firstRatio, maxHeight), minHeight);
    } else {
      // Asset is taller than or equal to the max aspect ratio
      height = Math.max(
        Math.min(maxHeight, firstHeight, maxWidth / firstRatio),
        minHeight
      );
      width = height * firstRatio;
    }

    // Ensure we don't exceed maxWidth
    width = Math.min(width, maxWidth);

    return (
      <div
        className="scrollbar-hidden relative w-full overflow-x-auto"
        style={{ height }}
      >
        <div className="flex flex-nowrap">
          {assets.map((asset, index) => (
            <div
              key={asset.id}
              className="mr-2 max-w-[313px] min-w-[210px] shrink-0 overflow-hidden last:mr-0"
              style={{
                height: `${height}px`,
              }}
            >
              {asset.metadata.mimetype.startsWith("video") ? (
                <video
                  src={asset.signedUrl}
                  className="h-full w-full rounded-[8px] object-cover"
                  controls={true}
                />
              ) : (
                <img
                  src={asset.signedUrl}
                  className="h-full w-full rounded-[8px] object-cover"
                />
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }
};

export default ThreadsAssets;
