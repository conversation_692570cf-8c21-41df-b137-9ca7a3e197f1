import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import { generateApiKey } from "generate-api-key";
import chunk from "lodash/chunk";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const users = await db.user.findMany({
    select: {
      id: true,
      firstName: true,
      referralID: true,
    },
    where: {
      firstName: {
        not: null,
      },
    },
  });

  const chunks = chunk(users, 50);
  for await (const chunk of chunks) {
    const promises = chunk.map(async (user) => {
      const randomKey = generateApiKey({
        method: "string",
        length: 6,
        pool: "abcdefghijklmnopqrstuvwxyz0123456789",
      }) as string;

      const referralID = `${user.firstName?.replaceAll(
        /[^a-zA-Z]/g,
        ""
      )}-${randomKey}`.toLocaleLowerCase();
      console.log(user.firstName, referralID);
      await db.user.update({
        where: {
          id: user.id,
        },
        data: {
          referralID,
        },
      });
    });
    await Promise.all(promises);
  }

  res.status(StatusCodes.OK).send({
    numUsers: users.length,
  });
};

export default handler;
