import * as Icons from "@heroicons/react/24/outline";
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu";
import * as React from "react";

import { CheckIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";

const Root = DropdownMenuPrimitive.Root;

const Group = DropdownMenuPrimitive.Group;

const Portal = DropdownMenuPrimitive.Portal;

const Sub = DropdownMenuPrimitive.Sub;

const RadioGroup = DropdownMenuPrimitive.RadioGroup;

const Trigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Trigger>
>(({ className, children, ...props }, ref) => {
  return (
    <DropdownMenuPrimitive.Trigger
      ref={ref}
      asChild
      className={classNames(className, "cursor-pointer")}
      style={{
        WebkitAppearance: "none",
      }}
      {...props}
    >
      {children}
    </DropdownMenuPrimitive.Trigger>
  );
});
Trigger.displayName = DropdownMenuPrimitive.Trigger.displayName;

const SubTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
    icon: keyof typeof Icons;
    inset?: boolean;
  }
>(({ className, icon, inset, children, ...props }, ref) => {
  const Icon = Icons[icon];

  return (
    <DropdownMenuPrimitive.SubTrigger
      ref={ref}
      className={classNames(
        "font-body-sm focus:bg-slate data-[state=open]:bg-slate flex cursor-pointer items-center rounded-md px-1.5 py-1 outline-hidden select-none",
        inset && "pl-8",
        className
      )}
      {...props}
    >
      <div className="mr-4 flex items-center gap-2">
        <Icon className="h-4 w-4" />
        {children}
      </div>
      <ChevronRightIcon className="ml-auto h-4 w-4" />
    </DropdownMenuPrimitive.SubTrigger>
  );
});
SubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName;

const SubContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.SubContent
    ref={ref}
    className={classNames(
      "animate-in slide-in-from-left-1 z-50 max-h-96 touch-pan-y overflow-y-auto overscroll-contain rounded-lg bg-white p-3 shadow-md",
      className
    )}
    {...props}
  />
));
SubContent.displayName = DropdownMenuPrimitive.SubContent.displayName;

const Content = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content> & {
    position?: "bottom-right" | "bottom-left";
  }
>(({ className, position = "bottom-right", sideOffset = 4, ...props }, ref) => (
  <DropdownMenuPrimitive.Portal>
    <DropdownMenuPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={classNames(
        "w-content fixed z-50 overflow-hidden rounded-[10px] bg-white p-2 whitespace-nowrap shadow-[0px_4px_15px_0px_#0000001A] md:p-4",
        "data-[state=open]:animate-in data-[state=closed]:animate-out",
        "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
        "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
        "data-[side=bottom]:slide-in-from-top-2",
        "data-[side=left]:slide-in-from-right-2",
        "data-[side=right]:slide-in-from-left-2",
        "data-[side=top]:slide-in-from-bottom-2",
        className,
        {
          "left-0": position === "bottom-right",
          "right-0": position === "bottom-left",
        }
      )}
      {...props}
    />
  </DropdownMenuPrimitive.Portal>
));
Content.displayName = DropdownMenuPrimitive.Content.displayName;

const Item = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
    icon?: keyof typeof Icons;
    isHidden?: boolean;
    isDisabled?: boolean;
    intent?: "danger" | "none" | "success";
    inset?: boolean;
  }
>(
  (
    {
      className,
      icon,
      intent,
      children,
      isHidden,
      isDisabled,
      inset,
      ...props
    },
    ref
  ) => {
    const Icon = icon ? Icons[icon] : null;

    return (
      <DropdownMenuPrimitive.Item
        ref={ref}
        disabled={isDisabled}
        className={classNames(
          "font-body-sm focus:bg-slate relative flex cursor-pointer items-center gap-2 rounded-md py-1 outline-hidden transition-colors select-none data-disabled:pointer-events-none data-disabled:opacity-50",
          inset && "pl-8",
          {
            hidden: !!isHidden,

            "px-1.5": !!icon,
            "px-2": !icon,

            "text-success": intent === "success",
            "text-danger": intent === "danger",
            "text-off-black": intent === "none" || intent == null,

            "opacity-50": isDisabled,
          },
          className
        )}
        {...props}
      >
        {Icon && <Icon className="h-4 w-4" />}
        {children}
      </DropdownMenuPrimitive.Item>
    );
  }
);
Item.displayName = DropdownMenuPrimitive.Item.displayName;

const CheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <DropdownMenuPrimitive.CheckboxItem
    ref={ref}
    className={classNames(
      "focus:bg-slate relative flex cursor-pointer items-center rounded-md py-1 pr-2 pl-8 text-sm outline-hidden transition-colors select-none data-disabled:pointer-events-none data-disabled:opacity-50",
      className
    )}
    checked={checked}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <CheckIcon className="h-4 w-4" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.CheckboxItem>
));
CheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName;

const RadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>
>(({ className, children, ...props }, ref) => (
  <DropdownMenuPrimitive.RadioItem
    ref={ref}
    className={classNames(
      "focus:bg-slate relative flex cursor-pointer items-center rounded-md py-1 pr-2 pl-8 text-sm outline-hidden transition-colors select-none data-disabled:pointer-events-none data-disabled:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <div className="h-2 w-2 rounded-full fill-current" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.RadioItem>
));
RadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;

const Label = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
    inset?: boolean;
  }
>(({ className, inset, ...props }, ref) => (
  <DropdownMenuPrimitive.Label
    ref={ref}
    className={classNames(
      "font-eyebrow-sm text-medium-dark-gray px-2 py-1",
      inset && "pl-8",
      className
    )}
    {...props}
  />
));
Label.displayName = DropdownMenuPrimitive.Label.displayName;

const Separator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.Separator
    ref={ref}
    className={classNames("bg-light-gray mx-2 my-1 h-px", className)}
    {...props}
  />
));
Separator.displayName = DropdownMenuPrimitive.Separator.displayName;

const Shortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={classNames(
        "ml-auto text-xs tracking-widest opacity-60",
        className
      )}
      {...props}
    />
  );
};
Shortcut.displayName = "DropdownMenuShortcut";

export default {
  CheckboxItem,
  Content,
  Group,
  Label,
  Portal,
  RadioGroup,
  Item,
  RadioItem,
  Root,
  Sub,
  Shortcut,
  Separator,
  SubContent,
  SubTrigger,
  Trigger,
};
