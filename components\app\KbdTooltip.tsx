import { ReactElement } from "react";
import Hotkeys from "react-hot-keys";
import Kbd from "../Kbd";
import Tooltip from "../Tooltip";

type Props = {
  label?: string | ReactElement;
  hotKeys?: string | string[];
  hotKeyLabel?: string;
  keyName?: string;
  onKeyPress: (shortcut: string, e: KeyboardEvent) => void;
  children: ReactElement;
};

const KbdTooltip = ({
  label,
  hotKeys,
  hotKeyLabel,
  keyName,
  onKeyPress,
  children,
}: Props) => {
  if (hotKeys == null) {
    return <>{children}</>;
  }

  const hotKeysString = Array.isArray(hotKeys) ? hotKeys.join(", ") : hotKeys;

  const hotKeysArray =
    hotKeyLabel != null ? hotKeyLabel.split("+") : hotKeysString.split("+");

  return (
    <Hotkeys keyName={keyName || hotKeysString} onKeyDown={onKeyPress}>
      <Tooltip
        value={
          <div className="flex items-center gap-1.5">
            <div className="font-eyebrow-sm">{label}</div>
            <div className="flex items-center gap-0.5">
              {hotKeysArray.map((hotKey, index) => {
                return <Kbd key={hotKey}>{hotKey}</Kbd>;
              })}
            </div>
          </div>
        }
        delayDuration={700}
      >
        {children}
      </Tooltip>
    </Hotkeys>
  );
};

export default KbdTooltip;
