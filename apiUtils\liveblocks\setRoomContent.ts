import { withProsemirrorDocument } from "@liveblocks/node-prosemirror";
import { liveblocks } from "server/routers/liveblocks";
import getOrCreateRoom from "./getOrCreateRoom";

// Sets the content of a Liveblocks room with error handling
const setRoomContent = async ({
  roomID,
  content,
  userID,
}: {
  roomID: string;
  content: any;
  userID: string;
}): Promise<void> => {
  try {
    await getOrCreateRoom({
      roomID,
      userID,
    });

    await withProsemirrorDocument(
      {
        client: liveblocks,
        roomId: roomID,
      },
      async (api) => {
        await api.setContent(content);
      }
    );
  } catch (error) {
    console.error("Error setting content for room", error);
    throw error;
  }
};

export default setRoomContent;
