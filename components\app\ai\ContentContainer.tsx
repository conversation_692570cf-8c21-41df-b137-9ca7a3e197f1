import { JSONContent } from "@tiptap/core";
import { AnimatePresence, motion } from "framer-motion";
import { DateTime } from "luxon";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useState } from "react";
import { toast } from "sonner";
import { Divider, Select } from "~/components";
import Button from "~/components/ui/Button";
import { handleTrpcError, trpc } from "~/utils";
import ChannelIcon from "../ChannelIcon";
import { AIChannel } from "./GeneratedContent";

type Props = {
  title: string | null;
  draftID?: string;
  channel: AIChannel;
  isLoading: boolean;
  content: {
    copy: string;
    editorState: JSONContent | null;
  }[];
  navigateOnSuccess?: boolean;
  connectedAccount: {
    integrationID: string;
    name: string;
  } | null;
  accountOptions: {
    value: string;
    label: string;
  }[];
  onEditClick?: () => void;
  setConnectedAccountID: (accountID: string | null) => void;
  children: React.ReactNode;
};

const ContentContainer = ({
  title,
  draftID,
  channel,
  isLoading,
  content,
  navigateOnSuccess = true,
  connectedAccount,
  accountOptions,
  onEditClick,
  setConnectedAccountID,
  children,
}: Props) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();
  const [newPostID, setNewPostID] = useState<string | null>(null);

  const createPostMutation = trpc.post.create.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Error Creating Post", error });
    },
    onSuccess: (data) => {
      trpcUtils.post.getMany.invalidate();
      if (navigateOnSuccess) {
        router.push(`/posts/${data.post.id}`);
      } else {
        setNewPostID(data.post.id);
      }
    },
  });

  return (
    <div className="border-light-gray rounded-lg border px-6 pt-6">
      <div className="flex items-center justify-between">
        <ChannelIcon channel={channel} width={24} />
        <AnimatePresence>
          {newPostID == null ? (
            <motion.div
              className="flex items-center gap-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              {accountOptions.length > 0 && (
                <Select
                  value={connectedAccount?.integrationID}
                  options={accountOptions}
                  onChange={setConnectedAccountID}
                />
              )}
              <Button
                variant="ghost"
                size="sm"
                disabled={createPostMutation.isPending}
                onClick={onEditClick}
              >
                Edit Copy
              </Button>
              <Button
                disabled={isLoading}
                size="sm"
                isLoading={createPostMutation.isPending}
                onClick={() => {
                  if (title == null) {
                    toast.error("Error Creating Post", {
                      description: "Please enter a title for the post",
                    });
                    return;
                  }

                  createPostMutation.mutate({
                    title,
                    channels: [channel],
                    labels: [],
                    status: "ToDo",
                    draftID: draftID,
                    postContent: content,
                    publishDate: DateTime.now().startOf("day").toJSDate(),
                    integrationID: connectedAccount?.integrationID,
                  });
                }}
              >
                Add to Calendar
              </Button>
            </motion.div>
          ) : (
            <motion.div
              className="flex items-center gap-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <div className="font-body-sm text-medium-dark-gray font-medium">
                Added to Calendar
              </div>
              <Link
                href={`/posts/${newPostID}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-basic font-body-sm group relative inline-flex items-center justify-start overflow-hidden rounded-full py-1 pr-6 pl-2 font-medium transition-all duration-150 ease-in-out"
              >
                <span className="bg-basic/10 absolute bottom-0 left-0 h-0 w-full transition-all duration-150 ease-in-out group-hover:h-full"></span>
                <span className="absolute right-1.5 pl-2.5">
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      stroke-linejoin="round"
                      strokeWidth="2"
                      d="M14 5l7 7m0 0l-7 7m7-7H3"
                    ></path>
                  </svg>
                </span>
                <span className="relative w-full text-left transition-colors duration-200 ease-in-out">
                  View Post
                </span>
              </Link>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      <Divider padding="md" />
      {children}
    </div>
  );
};

export default ContentContainer;
