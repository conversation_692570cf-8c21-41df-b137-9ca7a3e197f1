import { db } from "~/clients";
import colors from "~/styles/colors";

const createScaffoldingLabels = async (workspaceID: string) => {
  await db.label.createMany({
    data: [
      {
        name: "Feature Launch",
        workspaceID,
        color: colors.accentGreen,
        backgroundColor: colors.accentGreenLight,
      },
      {
        name: "Case Study",
        workspaceID,
        color: colors.accentPurple,
        backgroundColor: colors.accentPurpleLight,
      },
      {
        name: "Testimonial",
        workspaceID,
        color: colors.accentOrange,
        backgroundColor: colors.accentOrangeLight,
      },
      {
        name: "Use Case",
        workspaceID,
        color: colors.accentOrange,
        backgroundColor: colors.accentOrangeLight,
      },
      {
        name: "Education",
        workspaceID,
        color: colors.accentBrown,
        backgroundColor: colors.accentBrownLight,
      },
      {
        name: "Tips",
        workspaceID,
        color: colors.accentYellow,
        backgroundColor: colors.accentYellowLight,
      },
    ],
  });
};

export default createScaffoldingLabels;
