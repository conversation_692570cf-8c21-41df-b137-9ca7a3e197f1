import {
  Asset,
  InstagramContentType,
  InstagramIntegrationSource,
} from "@prisma/client";
import getAssetsForInstagram from "apiUtils/getAssetsForInstagram";
import assert from "assert";
import { DateTime } from "luxon";
import Instagram from "~/clients/facebook/instagram/Instagram";
import { db } from "~/clients/Prisma";

type Post = {
  id: string;
  instagramIntegration: {
    account: {
      externalID: string;
    };
    accessToken: string;
    integrationSource: InstagramIntegrationSource;
  } | null;
  instagramContent: {
    id: string;
    type: InstagramContentType;
    coverPhoto: Asset | null;
    location: {
      pageID: string;
    } | null;
    assets: {
      id: string;
      position: number;
      containerID: string | null;
      containerExpiresAt: Date | null;
      asset: Asset;
      tags: {
        id: string;
        username: string;
        x: number | null;
        y: number | null;
      }[];
    }[];
    copy: string;
    collaborators: string[];
    reelShareToFeed: boolean;
  } | null;
  title: string;
  workspaceID: string;
  createdByID: string;
};

const uploadInstagramAssets = async (
  post: Post,
  forceUpload = false
): Promise<
  | {
      status: "Success";
      error?: never;
      isAuthError?: never;
      assetIDToContainerID: { [assetID: string]: string };
    }
  | {
      status: "Error";
      error: string;
      isAuthError?: boolean;
      assetIDToContainerID?: never;
    }
  | {
      status: "NonRetriableError";
      error: string;
      isAuthError?: boolean;
      assetIDToContainerID?: never;
    }
  | {
      status: "Skipped";
      error?: never;
      isAuthError?: never;
      assetIDToContainerID?: never;
    }
> => {
  const { instagramContent, instagramIntegration } = post;

  if (!instagramContent) {
    console.error(`IG Upload Error post:${post.id}: No Instagram Copy to Post`);
    return {
      status: "NonRetriableError",
      error: "No Instagram content to post",
    };
  }

  if (!instagramIntegration) {
    console.error(`IG Upload Error post:${post.id}: No Instagram integration`);
    return {
      status: "NonRetriableError",
      error: "No Instagram integration",
    };
  }

  let igAssets = instagramContent.assets;

  if (instagramContent.assets.length === 0) {
    const { assets: instagramAssets } = await getAssetsForInstagram(post);

    // If the length is 1 we can safely assume the order is correct
    if (instagramAssets.length === 1) {
      const asset = instagramAssets[0];
      const newIgAsset = await db.instagramAsset.create({
        data: {
          position: 0,
          mimetype: asset.metadata.mimetype,
          fileName: asset.name,
          asset: {
            create: {
              id: asset.id,
              name: asset.name,
              bucket: "assets",
              path: asset.path,
              eTag: asset.eTag,
              mimetype: asset.mimetype,
              url: asset.signedUrl,
              size: asset.sizeBytes,
              urlExpiresAt: asset.urlExpiresAt,
              workspaceID: post.workspaceID,
              userID: post.createdByID,
            },
          },
          instagramContent: {
            connect: {
              id: instagramContent.id,
            },
          },
        },
        include: {
          asset: true,
          tags: true,
        },
      });
      igAssets = [newIgAsset];
    } else if (instagramAssets.length > 1) {
      // If the length is greater than 1 then we can't be certain about the order and we should error

      return {
        status: "Error",
        error:
          "Unable to determine order of assets. Please reupload <NAME_EMAIL>",
      };
    } else {
      return {
        status: "Error",
        error: "No media to post",
      };
    }
  }

  // If all assets have a containerID and containerExpiresAt is further than
  // 10 minutes (just to be safe) in the future then we can skip this task
  if (
    !forceUpload &&
    igAssets.every(
      (asset) =>
        asset.containerID &&
        asset.containerExpiresAt &&
        DateTime.fromJSDate(asset.containerExpiresAt).diffNow().as("minutes") >
          10
    )
  ) {
    console.log(
      `Skip Uploading post:${post.id}: All assets are ready to upload`
    );
    return {
      status: "Skipped",
    };
  }

  const instagram = new Instagram(instagramIntegration.accessToken, {
    platform: instagramIntegration.integrationSource,
  });
  const instagramAccountID = instagramIntegration.account.externalID;

  const { assets, coverPhoto } = await getAssetsForInstagram(post);

  const contentType = instagramContent.type;
  const reelShareToFeed = instagramContent.reelShareToFeed;

  console.log(
    `Uploading Assets post:${post.id}: caption: ${instagramContent.copy}`
  );

  const uploadResponse = await instagram.uploadAssets({
    instagramAccountID,
    contentType,
    locationID: instagramContent.location?.pageID,
    assets: assets.map((asset) => ({
      id: asset.id,
      name: asset.name,
      signedUrl: asset.signedUrl,
      type: asset.metadata.mimetype.includes("video") ? "video" : "image",
      userTags: asset.tags,
      coverPhotoUrl: coverPhoto?.signedUrl,
    })),
    collaborators: instagramContent.collaborators,
    reelShareToFeed,
    caption: instagramContent.copy,
  });

  if (uploadResponse.errors) {
    const error = uploadResponse.errors.join(". ");
    console.error(`IG Upload Error post:${post.id}: ${error}`);

    const isAuthError = uploadResponse.isAuthError;

    return {
      status: isAuthError ? "NonRetriableError" : "Error",
      error,
      isAuthError,
    };
  }

  assert(uploadResponse.data);
  const assetIDToContainerID = uploadResponse.data;

  await db.$transaction(async (tx) => {
    for await (const asset of assets) {
      await tx.instagramAsset.update({
        where: {
          id: asset.id,
        },
        data: {
          containerID: assetIDToContainerID[asset.id],
          containerUploadedAt: new Date(),
          containerExpiresAt: DateTime.now().plus({ days: 1 }).toJSDate(),
        },
      });
    }
  });

  console.log(`Uploaded IG Assets Successfully post:${post.id}`);

  return {
    status: "Success",
    assetIDToContainerID,
  };
};

export default uploadInstagramAssets;
