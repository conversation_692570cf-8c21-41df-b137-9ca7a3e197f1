import { XMarkIcon } from "@heroicons/react/24/outline";
import { AnimatePresence, motion } from "framer-motion";
import uniq from "lodash/uniq";
import { useState } from "react";
import { AddNewButton, Badge, TextInput, TextLink } from "~/components";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { handleTrpcError, joinWithAnd, trpc } from "~/utils";

type Props = {
  postID: string;
  instagramContent: {
    id: string;
    collaborators: string[];
  };
  updateCollaborators: (collaborators: string[]) => void;
};

const CollaborateUsersModal = ({
  postID,
  instagramContent,
  updateCollaborators,
}: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const [username, setUsername] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const collaborators = instagramContent.collaborators;

  const trpcUtils = trpc.useUtils();

  const addCollaboratorMutation =
    trpc.instagramContent.addCollaborator.useMutation({
      onError: (error) => {
        handleTrpcError({ title: "Unable to Add Collaborator", error });
      },
    });
  const removeCollaboratorMutation =
    trpc.instagramContent.removeCollaborator.useMutation();

  const setCollaborators = (collaborators: string[]) => {
    const uniqueCollaborators = uniq(collaborators);
    const data = trpcUtils.instagramContent.get.getData({
      postID,
    });
    updateCollaborators(uniqueCollaborators);
    trpcUtils.instagramContent.get.invalidate({ postID });
  };

  const handleAddUser = () => {
    const cleanedUsername = username.replace("@", "");
    if (collaborators.length < 3) {
      if (cleanedUsername.length === 0) {
        setError("Please enter an Instagram handle.");
      } else if (cleanedUsername.length < 3) {
        setError("Instagram handles must be at least 3 characters.");
      } else {
        addCollaboratorMutation.mutate({
          instagramContentID: instagramContent.id,
          collaborator: cleanedUsername,
        });
        setError(null);
        setUsername("");
        setCollaborators([...collaborators, cleanedUsername]);
      }
    } else {
      setError("You can only add up to 3 collaborators on Instagram.");
    }
  };

  return (
    <div>
      {collaborators.length === 0 ? (
        <AddNewButton label="Add Collaborators" onClick={() => setOpen(true)} />
      ) : (
        <Badge intent="secondary" kind="hollow" onClick={() => setOpen(true)}>
          <span className="text-medium-dark-gray">Collaborators: </span>
          <span>{joinWithAnd(collaborators)}</span>
        </Badge>
      )}
      <Credenza.Root open={open} onOpenChange={setOpen}>
        <Credenza.Header>
          <Credenza.Title>Set Collaborators</Credenza.Title>
          <Credenza.Description>
            Collaborators must be public profiles. Once posted, collaborators
            will receive a DM in Instagram to accept the collaboration request
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <div className="flex items-center gap-2">
            <div className="w-40">
              <TextInput
                placeholder="@instagram-handle"
                value={username}
                onChange={setUsername}
                onEnterPress={() => handleAddUser()}
              />
            </div>
            <Button size="sm" onClick={() => handleAddUser()}>
              Save
            </Button>
          </div>
          {error != null && (
            <div className="text-danger font-body-sm mt-2">{error}</div>
          )}
          <AnimatePresence>
            <div className="mt-5 flex items-center gap-2">
              {collaborators.map((collaborator) => {
                return (
                  <motion.div
                    key={collaborator}
                    initial={{ opacity: 0, width: "auto" }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                    className="box-shadow font-body-sm text-secondary mb-3 flex items-center gap-2 overflow-clip rounded-lg px-4 py-2.5"
                  >
                    <TextLink
                      value={collaborator}
                      color="secondary"
                      size="sm"
                      href={`https://www.instagram.com/${collaborator}`}
                    />
                    <motion.button
                      className="hover:bg-lightest-gray/50 rounded-full p-0.5 transition-colors"
                      onClick={() => {
                        setError(null);
                        removeCollaboratorMutation.mutate({
                          instagramContentID: instagramContent.id,
                          collaborator,
                        });
                        setCollaborators(
                          collaborators.filter(
                            (currentCollaborator) =>
                              currentCollaborator !== collaborator
                          )
                        );
                      }}
                    >
                      <XMarkIcon className="text-medium-gray h-4 w-4" />
                    </motion.button>
                  </motion.div>
                );
              })}
            </div>
          </AnimatePresence>
        </Credenza.Body>
      </Credenza.Root>
    </div>
  );
};

export default CollaborateUsersModal;
