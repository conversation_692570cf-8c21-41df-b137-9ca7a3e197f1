import { useRouter } from "next/router";

import { Channel } from "@prisma/client";

import assert from "assert";
import { useEffect, useMemo, useState } from "react";
import { IconButton, Table, UserAvatar } from "~/components";
import Button from "~/components/ui/Button";
import { CHANNEL_ICON_MAP } from "~/types";
import { getFullName, trpc } from "~/utils";
import CampaignSelect from "../CampaignSelect";
import LabelSelect from "../LabelSelect";
import AccountList, { Account } from "../calendar/AccountList";
import NewIdeaModal from "./NewIdeaModal";

type Label = {
  id: string;
  name: string;
  color: string;
  backgroundColor: string;
};

type User = {
  firstName: string | null;
  lastName: string | null;
};

type Campaign = {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
};

type Idea = {
  id: string;
  title: string;
  channels: Channel[];
  accounts: Account[];
  campaignID: string | null;
  labels: Label[];
  campaign: Campaign | null;
  subscribers: { id: string; user: User }[];
  createdAt: Date;
  createdBy: {
    id: string;
    firstName: string | null;
    lastName: string | null;
  };
};

type Props = {
  ideas: Idea[] | undefined;
};

const IdeasList = ({ ideas: initialIdeas }: Props) => {
  const [ideas, setIdeas] = useState<Idea[] | undefined>(initialIdeas);
  const [newIdeaModalOpen, setNewIdeaModalOpen] = useState<boolean>(false);
  const router = useRouter();
  const trpcUtils = trpc.useUtils();

  useEffect(() => {
    if (ideas === undefined) {
      setIdeas(initialIdeas);
    }
  }, [initialIdeas]);

  const ideaUpdateMutation = trpc.post.update.useMutation();
  const updateLabelsMutation = trpc.label.updateLabels.useMutation();
  const labelQuery = trpc.label.getAll.useQuery();
  const labelOptions = labelQuery.data?.labels || [];

  const updateIdea = (
    ideaID: string,
    updatedValues: {
      campaign?: Campaign | null;
      labels?: Label[];
    }
  ) => {
    setIdeas((prevIdeas) => {
      assert(prevIdeas != null);

      const ideaIndex = prevIdeas.findIndex((idea) => idea.id == ideaID);

      const newIdeas = [...prevIdeas];
      const idea = { ...newIdeas[ideaIndex], ...updatedValues };

      if (updatedValues.labels != null) {
        updateLabelsMutation.mutate(
          {
            contentID: ideaID,
            contentType: "post",
            labels: updatedValues.labels,
          },
          {
            onSuccess: () => {
              trpcUtils.idea.getAll.invalidate();
              trpcUtils.label.getAll.refetch();
            },
          }
        );
      } else {
        const { campaign, ...rest } = updatedValues;
        const campaignID = campaign ? campaign.id : campaign;
        ideaUpdateMutation.mutate(
          {
            id: ideaID,
            campaignID,
            ...rest,
          },
          {
            onSuccess: () => {
              trpcUtils.idea.getAll.invalidate();
            },
          }
        );
      }

      newIdeas[ideaIndex] = idea;

      return newIdeas;
    });
  };

  const columns = useMemo(
    () => [
      {
        id: "idea_name",
        name: "Idea Name",
        maxWidth: "320px",
        searchable: true,
        sortable: true,
        selector: (row: Idea) => row.title,
      },
      {
        id: "campaign",
        name: "Campaign",
        minWidth: "320px",
        filterable: true,
        filterValue: (row: Idea) => row.campaign?.name,
        selector: (row: Idea) => row.campaign?.name || "",
        cell: (row: Idea) => {
          return (
            <CampaignSelect
              value={row.campaign?.id}
              onChange={(campaign) => {
                updateIdea(row.id, { campaign });
              }}
              defaultCampaigns={row.campaign ? [row.campaign] : undefined}
              dropdownPosition="left"
            />
          );
        },
      },
      {
        id: "channels",
        name: "Channel",
        minWidth: "150px",
        filterable: true,
        filterValue: (row: Idea) => CHANNEL_ICON_MAP[row.channels[0]].label,
        selector: (row: Idea) => row.channels.join(", "),
        cell: (row: Idea) => {
          return (
            <AccountList
              accounts={row.accounts}
              numAccountsBeforeOverflow={3}
            />
          );
        },
      },
      {
        id: "labels",
        name: "Labels",
        width: "200px",
        selector: (row: Idea) =>
          row.labels.map((label) => label.name).join(", "),
        cell: (row: Idea) => {
          const updateLabels = (
            labels: {
              id: string;
              name: string;
              color: string;
              backgroundColor: string;
              createdAt: Date;
            }[]
          ) => {
            updateIdea(row.id, { labels });
          };

          return (
            <LabelSelect
              value={row.labels}
              onChange={updateLabels}
              isReadOnly={false}
              isCreateable={true}
              options={labelOptions}
            />
          );
        },
      },
      {
        id: "created_by",
        name: "Created By",
        compact: true,
        right: true,
        filterable: true,
        selector: (row: Idea) => getFullName(row.createdBy),
        cell: (row: Idea) => {
          return (
            <UserAvatar
              key={row.createdBy.id}
              user={row.createdBy}
              tooltipText={getFullName(row.createdBy)}
              backgroundColor="darkGray"
            />
          );
        },
      },
      {
        id: "created_at",
        name: "Created At",
        omit: true,
        selector: (row: Idea) => new Date(row.createdAt).getTime(),
      },
    ],
    [labelOptions]
  );

  return (
    <div className="scrollbar-hidden overflow-y-auto pr-4">
      <Table
        title="Ideas & Drafts"
        data={ideas}
        defaultSortFieldId="created_at"
        showAllRows={true}
        actionContent={
          <IconButton
            icon="PlusIcon"
            intent="basic"
            borderRadius="md"
            hotKeyOptions={{
              keys: "I",
              label: "Create New Idea",
            }}
            onClick={() => setNewIdeaModalOpen(true)}
          />
        }
        noDataComponent={
          <div className="flex h-full w-[700px] flex-col items-center justify-center gap-4 text-center">
            <h3>Add your first idea here</h3>
            <div className="font-body text-medium-dark-gray">
              Ideas are posts that don't have a date associated with them yet.
              <br />
              It's a place to keep content ideas that haven't been scheduled
              onto the calendar yet.
            </div>
            <Button onClick={() => setNewIdeaModalOpen(true)}>
              Add an Idea
            </Button>
          </div>
        }
        columns={columns}
        onRowClicked={(row, event) => {
          if (event.ctrlKey || event.metaKey) {
            window.open(`/ideas/${row.id}`, "_blank");
          } else {
            router.push(`/ideas/${row.id}`);
          }
        }}
      />
      <NewIdeaModal
        open={newIdeaModalOpen}
        setOpen={setNewIdeaModalOpen}
        onCreateSuccess={(idea) => {
          setIdeas((prevIdeas) => {
            assert(prevIdeas != null);

            return [idea, ...prevIdeas];
          });
        }}
      />
    </div>
  );
};

export default IdeasList;
