import { Channel, InstagramContentType } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import { arrayMoveImmutable } from "array-move";
import assert from "assert";
import classNames from "classnames";
import posthog from "posthog-js";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { Divider, TextLink } from "~/components";
import Select from "~/components/Select";
import Tabs from "~/components/ui/Tabs";
import { MAX_VIDEO_UPLOAD_SIZE_MB, ONE_YEAR_IN_SECONDS } from "~/constants";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import tusOptions from "~/constants/tusOptions";
import { useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  findAsync,
  formatPercentage,
  getImageProperties,
  getVideoProperties,
  handleFilesUpload,
  handleTrpcError,
  openConfirmationModal,
  openIntercomArticle,
  openIntercomChat,
  trpc,
} from "~/utils";
import { EditImageModalProperties } from "~/utils/EditImageModal";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import compressImage from "~/utils/compressImage";
import getAssetsWithProperties from "~/utils/getAssetsWithProperties";
import { getDefaultAccount } from "~/utils/getDefaultAccount";
import getFileProperties from "~/utils/getFileProperties";
import createRoomID from "~/utils/liveblocks/createRoomID";
import Assets, { UploadingAsset } from "../../files/Assets";
import InstagramEditorWrapper from "../../tiptap/InstagramEditor";
import CoverPhotoModal from "../CoverPhotoModal";
import EditorActionsBar from "../EditorActionsBar";
import PostEngagements from "../PostEngagements";
import { PostOption, PostOptions } from "../PostOptions";
import PostPerformance from "../PostPerformance";
import PostPreviewContainer from "../PostPreviewContainer";
import { ScheduledPost } from "../SchedulePostAccountSelect";
import AddLocationModal from "./AddLocationModal";
import CollaborateUsersModal from "./CollaborateUsersModal";
import InstagramFeedPreview from "./InstagramFeedPreview";
import InstagramGrid from "./InstagramGrid";
import InstagramReelsPreview from "./InstagramReelsPreview";
import InstagramStoryPreview from "./InstagramStoryPreview";
import TagUsersModal from "./TagUsersModal";

export type InstagramContent = {
  id: string;
  editorState: JSONContent | null;
  type: InstagramContentType;
  collaborators: string[];
  location: {
    pageID: string;
    name: string;
  } | null;
  reelShareToFeed: boolean;
  crossPostToFacebook: boolean;
  copy: string;
};

export type InstagramAsset = AssetType & {
  tags: {
    id: string;
    username: string;
    x: number | null;
    y: number | null;
  }[];
};

export type InstagramIntegration = {
  id: string;
  scope: string[];
  account: {
    name: string | null;
    username: string;
    externalID: string;
    profileImageUrl: string;
  };
};

export type InstagramPost = {
  permalink: string;
  impressionCount: number;
  reachCount: number;
  savedCount: number;
  likeCount: number;
  commentCount: number;
  profileVisitCount: number;
  shareCount: number;
  followCount: number;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    scheduledPosts: ScheduledPost[];
    instagramContent: InstagramContent | null;
    instagramIntegration: InstagramIntegration | null;
    instagramPost: InstagramPost | null;
    instagramAssets: {
      assets: InstagramAsset[];
      coverPhoto: AssetType | null;
    };
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const InstagramPost = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const trpcUtils = trpc.useUtils();

  const [contentAssets, setContentAssets] = useState<InstagramAsset[]>(
    post.instagramAssets.assets
  );

  const [isRoomReady, setIsRoomReady] = useState<boolean>(false);

  useEffect(() => {
    if (post.instagramContent && !isSyncingComplete) {
      setIsRoomReady(true);
    }
  }, [post.instagramContent, isSyncingComplete]);

  useEffect(() => {
    const initialMaybeCompressAssets = async () => {
      await maybeCompressAssets(post.instagramAssets.assets);
    };
    initialMaybeCompressAssets();
  }, []);

  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);
  const [coverPhoto, setCoverPhoto] = useState<AssetType | null>(
    post.instagramAssets.coverPhoto
  );
  const [coverPhotoModalOpen, setCoverPhotoModalOpen] =
    useState<boolean>(false);
  const { currentWorkspace } = useWorkspace();

  const [draggedOver, setDraggedOver] = useState<boolean>(false);

  const [instagramContent, setInstagramContent] = useState<InstagramContent>(
    post.instagramContent || {
      id: uuidv4(),
      editorState: null,
      collaborators: [],
      location: null,
      crossPostToFacebook: false,
      reelShareToFeed: true,
      type: InstagramContentType.Feed,
      copy: "",
    }
  );

  const [integration, setIntegration] = useState<{
    id: string;
    integrationAccountID: string;
    name: string;
    scope: string[];
    profileImageUrl: string;
  } | null>(
    post.instagramIntegration
      ? {
          id: post.instagramIntegration.id,
          integrationAccountID: post.instagramIntegration.account.externalID,
          name: `@${post.instagramIntegration.account.username}`,
          scope: post.instagramIntegration.scope,
          profileImageUrl: post.instagramIntegration.account.profileImageUrl,
        }
      : null
  );

  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);

  const updatePostMutation = trpc.post.update.useMutation();
  const engagementsQuery = trpc.instagramContent.getEngagements.useQuery({
    postID: post.id,
  });
  const engagements = engagementsQuery.data?.engagements || [];
  const createEngagementMutation =
    trpc.instagramContent.createEngagement.useMutation();
  const updateEngagementMutation =
    trpc.instagramContent.updateEngagement.useMutation();
  const deleteEngagementMutation =
    trpc.instagramContent.deleteEngagement.useMutation();

  const saveLocationMutation = trpc.instagramContent.saveLocation.useMutation();
  const workspaceIntegrationsQuery =
    trpc.integration.instagram.getIntegrations.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });

  const integrationsQuery = trpc.post.getIntegrations.useQuery({
    id: post.id,
  });
  const postAccounts = integrationsQuery.data?.accounts ?? [];

  const workspaceIntegrations = workspaceIntegrationsQuery.data;
  useEffect(() => {
    const setInitialIntegration = () => {
      if (integration == null && workspaceIntegrations?.integrations.length) {
        const defaultIntegration = getDefaultAccount({
          mainAccount: postAccounts[0],
          accounts: workspaceIntegrations.integrations,
        });
        setIntegration({
          id: defaultIntegration.id,
          integrationAccountID: defaultIntegration.account.externalID,
          name: `@${defaultIntegration.account.username}`,
          scope: defaultIntegration.scope,
          profileImageUrl: defaultIntegration.account.profileImageUrl,
        });
        updatePostMutation.mutate(
          {
            id: post.id,
            instagramIntegrationID: defaultIntegration.id,
          },
          {
            onSuccess: () => {
              trpcUtils.post.getIntegrations.refetch({
                id: post.id,
              });
            },
          }
        );
      }
    };
    setInitialIntegration();
  }, [workspaceIntegrations]);

  const instagramContentQuery = trpc.instagramContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const instagramContentData = instagramContentQuery.data;
  useEffect(() => {
    const setCrossPostData = async () => {
      if (instagramContentData?.instagramContent != null) {
        setContentAssets(instagramContentData.instagramAssets.assets);
        setCoverPhoto(instagramContentData.instagramAssets.coverPhoto);
        setInstagramContent(instagramContentData.instagramContent);
        await maybeCompressAssets(instagramContentData.instagramAssets.assets);
        setIsRoomReady(true);
        clearIsSyncing();
      }
    };
    setCrossPostData();
  }, [instagramContentData]);

  useEffect(() => {
    const newIGContent = instagramContentData?.instagramContent;
    if (newIGContent) {
      setInstagramContent((content) => ({
        ...content,
        collaborators: newIGContent.collaborators,
      }));
    }
  }, [instagramContentData?.instagramContent?.collaborators]);

  const upsertInstagramMutation = trpc.instagramContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
  });
  const compressVideoMutation = trpc.video.compress.useMutation();
  const maybeCompressAssets = async (
    assets: {
      path: string;
      sizeBytes: number;
      sizeMB: number;
      mimetype: string;
      signedUrl: string;
      duration?: number | null;
      width?: number | null;
      height?: number | null;
    }[]
  ) => {
    const videoPromises = assets.map(async (asset) => {
      if (!asset || !asset.mimetype.startsWith("video/")) {
        return Promise.resolve(null);
      } else {
        let duration = asset.duration;
        let width = asset.width;
        let height = asset.height;
        if (width && height && duration) {
          return asset as typeof asset & {
            duration: number;
            width: number;
            height: number;
          };
        } else {
          const properties = await getVideoProperties(asset.signedUrl);
          return {
            ...asset,
            duration: properties.duration,
            width: properties.width,
            height: properties.height,
          };
        }
      }
    });
    const videosWithProperties = await Promise.all(videoPromises);

    const videoToCompress = await findAsync(
      videosWithProperties,
      async (asset) => {
        if (!asset || !asset.mimetype.startsWith("video/")) {
          return false;
        }

        const { width, duration } = asset;

        const videoBitrate = (asset.sizeBytes * 8) / duration / 1000 / 1000;

        return asset.sizeMB > 125 || width > 1920 || videoBitrate > 25;
      }
    );

    if (videoToCompress != null) {
      const { width, height } = videoToCompress;

      compressVideoMutation.mutate(
        {
          video: {
            path: videoToCompress.path,
            resolution: {
              width,
              height,
            },
            duration: videoToCompress.duration,
            sizeBytes: videoToCompress.sizeBytes,
          },
          channel: "Instagram",
        },
        {
          onError: (error) => {
            handleTrpcError({ title: "Error Compressing Video", error });
          },
          onSuccess: (data) => {
            if (data.jobID != null) {
              toast.info("Optimizing Attachment", {
                description:
                  "We're automatically optimizing your attachment (files >125MB and width > 1920px are not accepted on Instagram).",
              });
            }
          },
        }
      );
    }
  };

  const createAssetsMutation = trpc.instagramContent.createAssets.useMutation();
  const deleteAssetMutation = trpc.instagramContent.deleteAsset.useMutation();
  const updateAssetPositionMutation =
    trpc.instagramContent.updateAssetPositions.useMutation();
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const updateAssetMutation = trpc.instagramContent.updateAsset.useMutation();
  const upsertCoverPhotoMutation =
    trpc.instagramContent.upsertCoverPhoto.useMutation();
  const deleteCoverPhotoMutation =
    trpc.instagramContent.deleteCoverPhoto.useMutation();
  const deleteAssetTagsMutation =
    trpc.instagramContent.deleteAssetTags.useMutation();

  const onSortEnd = (oldIndex: number, newIndex: number) => {
    setContentAssets((assets) => {
      const newAssets = arrayMoveImmutable(assets, oldIndex, newIndex);

      updateAssetPositionMutation.mutate({
        assets: newAssets.map((asset, index) => ({
          id: asset.id,
          position: index,
        })),
      });

      return newAssets;
    });
  };

  const accountOptions =
    workspaceIntegrationsQuery.data?.integrations.map((integration) => {
      return {
        id: integration.id,
        integrationAccountID: integration.account.externalID,
        name: `@${integration.account.username}`,
        scope: integration.scope,
        profileImageUrl: integration.account.profileImageUrl,
      };
    }) ||
    (post.instagramIntegration
      ? [
          {
            id: post.instagramIntegration.id,
            integrationAccountID: post.instagramIntegration.account.externalID,
            name: `@${post.instagramIntegration.account.username}`,
            scope: post.instagramIntegration.scope,
            profileImageUrl: post.instagramIntegration.account.profileImageUrl,
          },
        ]
      : []);

  const numSetOptions = [
    instagramContent.type === "Reel" && coverPhoto != null,
    contentAssets.map((asset) => asset.tags.length > 0).includes(true),
    instagramContent.collaborators.length > 0,
    instagramContent.type === "Feed" && instagramContent.location != null,
  ].filter((option) => option).length;

  const getValidFiles = async (files: File[]) => {
    if (instagramContent.type === "Feed" || instagramContent.type === "Story") {
      if (contentAssets.length + files.length > 10) {
        toast.error("Too Many Attachments", {
          description:
            "You can add a maximum of 10 photos and videos to a post.",
        });
        return [];
      }
    } else if (instagramContent.type === "Reel") {
      const alreadyUploadingVideo = uploadingAssets.some((asset) =>
        asset.type.startsWith("video/")
      );

      if (contentAssets.length + files.length > 1 || alreadyUploadingVideo) {
        openConfirmationModal({
          title: "Only one video file per Reels post",
          body: "In order to post to Reels, each post can only have one attachment, in the form of a .mp4 or .mov file.",
          cancelButton: null,
          primaryButton: {
            label: "Got it",
          },
        });
        return [];
      }
    }

    const assetNames = contentAssets.map((asset) =>
      asset.name.replace(".png", ".jpg")
    );

    const validFiles: File[] = [];
    // https://developers.facebook.com/docs/instagram-api/reference/ig-user/media#image-specifications
    for await (const file of files) {
      const fileType = file.type;
      const fileSizeMB = file.size / 1024 / 1024;

      if (
        assetNames.includes(file.name) ||
        assetNames.includes(file.name.replace(".png", ".jpeg"))
      ) {
        toast.error(`${file.name} already exists`, {
          description: "Each attachment must have a unique name",
        });
        continue;
      }

      if (["image/jpeg", "image/png"].includes(fileType)) {
        const { width, height, pixels } = await getImageProperties(file);

        if (instagramContent.type === "Feed" && 0.8 - width / height > 0.01) {
          setEditImageModalProperties({
            title: "Photo is too tall for instagram",
            channel: "Instagram",
            maxSizeMB: 8,
            asset: {
              name: file.name,
              signedUrl: URL.createObjectURL(file),
              sizeMB: fileSizeMB,
            },
            isOpen: true,
            error:
              "Your image is too tall. Instagram requires images to have a maximum aspect ratio of 4:5 (for vertical images) and 1.91:1 (for horizontal images).",
          });
          continue;
        } else if (
          instagramContent.type === "Feed" &&
          9 / 16 - width / height > 0.01
        ) {
          setEditImageModalProperties({
            title: "Photo is too tall for instagram",
            channel: "Instagram",
            maxSizeMB: 8,
            asset: {
              name: file.name,
              signedUrl: URL.createObjectURL(file),
              sizeMB: fileSizeMB,
            },
            isOpen: true,
            error:
              "Your image is too tall. Instagram requires images to have a maximum aspect ratio of 9:16 (for vertical images) and 1.91:1 (for horizontal images).",
          });
          continue;
        } else if (width / height > 1.91 + 0.01) {
          setEditImageModalProperties({
            title: "Photo is too wide for instagram",
            channel: "Instagram",
            maxSizeMB: 8,
            asset: {
              name: file.name,
              signedUrl: URL.createObjectURL(file),
              sizeMB: fileSizeMB,
            },
            isOpen: true,
            error:
              "Your image is too wide. Instagram requires images to have a maximum aspect ratio of 4:5 (for vertical images) and 1.91:1 (for horizontal images).",
          });

          continue;
        } else if (pixels >= 36_152_320) {
          toast.error("Image is Too Large", {
            description: "An image can have a maximum of 36,152,319 pixels",
          });
          continue;
        }

        if (fileSizeMB > 8) {
          const compressedFile = await compressImage(file, {
            maxSizeMB: 7,
            type: "image/jpeg",
          });

          toast.info("Attachment Optimized", {
            description: `"${file.name}" was optimized (files >8MB are not accepted on Instagram)`,
          });
          posthog.capture(POSTHOG_EVENTS.image.compressed, {
            originalSizeMB: file.size / 1_024 / 1_024,
            compressedSizeMB: compressedFile.size / 1_024 / 1_024,
            channel: "Instagram",
            channelPostType: instagramContent.type,
          });
          validFiles.push(compressedFile);
        } else if (fileType === "image/png") {
          const jpegFile = await compressImage(file, {
            maxSizeMB: 8,
            type: "image/jpeg",
          });

          toast.info("Image Converted to JPEG", {
            description: `"${file.name}" was converted to jpeg (png files are not accepted on Instagram)`,
          });

          validFiles.push(jpegFile);
        } else {
          validFiles.push(file);
        }
      } else {
        const { duration, height, width } = await getVideoProperties(file);

        if (duration < 3) {
          toast.error("Video is Too Short", {
            description:
              "Instagram requries videos to be at least 3 seconds long",
          });
          continue;
        }

        if (width / height < 0.01 / 1) {
          toast.error("Video is Too Wide", {
            description:
              "Instagram requires videos to have a minimum aspect ratio of 0.01:1 (for vertical videos) and 10:1 (for horizontal videos)",
          });
          continue;
        } else if (width / height > 10 / 1) {
          toast.error("Video is Too Tall", {
            description:
              "Instagram requires videos to have a minimum aspect ratio of 0.01:1 (for vertical videos) and 10:1 (for horizontal videos)",
          });
          continue;
        }

        if (instagramContent.type === "Feed") {
          if (width / height < 0.8) {
            if (contentAssets.length + files.length > 1) {
              toast.error("Video is Too Tall", {
                description:
                  "Instagram requires videos to have a minimum aspect ratio of 4:5 (for vertical videos) and 16:9 (for horizontal videos)",
              });
              continue;
            } else {
              setInstagramContent((prevInstagramContent) => {
                upsertInstagramMutation.mutate({
                  id: prevInstagramContent.id,
                  copy: prevInstagramContent.copy,
                  type: "Reel",
                  crossPostToFacebook: prevInstagramContent.crossPostToFacebook,
                  reelShareToFeed: prevInstagramContent.reelShareToFeed,
                  editorState: prevInstagramContent.editorState,
                  postID: post.id,
                });

                return {
                  ...prevInstagramContent,
                  type: "Reel",
                };
              });
            }
          } else if (width / height > 16 / 9) {
            toast.error("Video is Too Wide", {
              description:
                "Instagram requires videos to have a minimum aspect ratio of 4:5 (for vertical videos) and 16:9 (for horizontal videos)",
            });
            continue;
          } else if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
            toast.error("Video is too large", {
              description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
              action: {
                label: "Contact Support",
                onClick: () => {
                  openIntercomChat(
                    `I need help compressing a ${fileSizeMB.toFixed(
                      1
                    )} MB video.`
                  );
                },
              },
            });
            continue;
          } else if (duration > 60) {
            toast.error("Video is Too Long", {
              description:
                "Instagram requries videos to be shorter than 60 seconds",
            });
            continue;
          }
        } else if (instagramContent.type === "Reel") {
          if (width / height <= 0.01) {
            toast.error("Video is Too Wide", {
              description:
                "Instagram requires Reels to have a minimum aspect ratio of 0.01:1 (for vertical videos) and 10:1 (for horizontal videos)",
            });
            continue;
          } else if (width / height >= 10 / 1) {
            toast.error("Video is Too Tall", {
              description:
                "Instagram requires Reels to have a minimum aspect ratio of 0.01:1 (for vertical videos) and 10:1 (for horizontal videos)",
            });
            continue;
          } else if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
            toast.error("Video is too large", {
              description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
              action: {
                label: "Contact Support",
                onClick: () => {
                  openIntercomChat(
                    `I need help compressing a ${fileSizeMB.toFixed(
                      1
                    )} MB video.`
                  );
                },
              },
            });
            continue;
          } else if (duration > 15 * 60) {
            toast.error("Video is Too Long", {
              description:
                "Instagram requries Reels to be shorter than 15 minutes",
            });
            continue;
          }
        } else if (instagramContent.type === "Story") {
          if (width / height <= 0.01) {
            toast.error("Video is Too Wide", {
              description:
                "Instagram requires Stories to have a minimum aspect ratio of 0.01:1 (for vertical videos) and 10:1 (for horizontal videos)",
            });
            continue;
          } else if (width / height >= 10 / 1) {
            toast.error("Video is Too Tall", {
              description:
                "Instagram requires Stories to have a minimum aspect ratio of 0.01:1 (for vertical videos) and 10:1 (for horizontal videos)",
            });
            continue;
          } else if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
            toast.error(
              `Max upload video size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB`,
              {
                description:
                  "Please contact support if you need help reducing your video's size.",
              }
            );
            continue;
          } else if (duration < 3) {
            toast.error("Video is Too Short", {
              description:
                "Instagram requries Stories to be longer than 3 seconds",
            });
            continue;
          } else if (duration > 60) {
            toast.error("Video is Too Long", {
              description:
                "Instagram requries Stories to be shorter than 60 seconds",
            });
            continue;
          }
        }

        validFiles.push(file);
      }
    }

    return validFiles;
  };

  const [uppy, setUppy] = useState<Uppy | null>(null);

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `instagram-${post.id}`,
        allowMultipleUploads: true,
        restrictions: {
          maxNumberOfFiles:
            instagramContent.type === "Feed" ||
            instagramContent.type === "Story"
              ? 10
              : 1,
        },
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", async (file) => {
        assert(currentWorkspace);

        const contentID = file.meta.contentID;
        assert(contentID);

        assert(instagramContent);

        setUploadingAssets((uploadingAssets) => {
          return [
            ...uploadingAssets,
            {
              name: file.name!,
              size: file.size!,
              type: file.type,
              uploadProgress: 0,
              updateAssetID: file.meta.updateAssetID as string | undefined,
            },
          ];
        });

        const objectName = `${currentWorkspace.id}/${post.id}/instagram/${contentID}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: ONE_YEAR_IN_SECONDS,
        };

        return file;
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (uppyData) => {
        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["Instagram"].slug
        }/${instagramContent.id}`;
        const fileNames = uppyData.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          uppyData.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              if (uppyData.successful![0].meta.updateAssetID) {
                const updateAssetID = uppyData.successful![0].meta
                  .updateAssetID as string;
                const newAsset = newAssetsWithProperties[0];

                await updateAssetMutation.mutateAsync({
                  instagramAssetID: updateAssetID,
                  newAsset: {
                    id: newAsset.id,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                });

                setContentAssets((prevAssets) =>
                  prevAssets.map((asset) =>
                    asset.id === updateAssetID
                      ? {
                          id: asset.id,
                          tags: asset.tags,
                          name: newAsset.name,
                          path: newAsset.path,
                          sizeBytes: newAsset.sizeBytes,
                          eTag: newAsset.eTag,
                          md5Hash: newAsset.md5Hash,
                          mimetype: newAsset.mimetype,
                          width: newAsset.width,
                          height: newAsset.height,
                          metadata: newAsset.metadata,
                          url: newAsset.signedUrl,
                          sizeMB: newAsset.sizeMB,
                          sizeKB: newAsset.sizeKB,
                          createdAt: newAsset.createdAt,
                          updatedAt: newAsset.updatedAt,
                          duration: newAsset.duration,
                          signedUrl: newAsset.signedUrl,
                          urlExpiresAt: newAsset.urlExpiresAt,
                        }
                      : asset
                  )
                );
                setUploadingAssets([]);
              } else {
                const nextPosition = contentAssets.length;

                createAssetsMutation.mutate(
                  {
                    id: instagramContent.id,
                    assets: newAssetsWithProperties.map((asset, index) => ({
                      id: asset.id,
                      assetID: asset.assetID,
                      name: asset.name,
                      path: asset.path,
                      size: asset.sizeBytes,
                      eTag: asset.eTag,
                      md5Hash: asset.md5Hash,
                      mimetype: asset.mimetype,
                      width: asset.width,
                      height: asset.height,
                      duration: asset.duration,
                      url: asset.signedUrl,
                      urlExpiresAt: asset.urlExpiresAt,
                      position: nextPosition + index,
                    })),
                  },
                  {
                    onSuccess: async () => {
                      await maybeCompressAssets(newAssetsWithProperties);
                    },
                    onError: (error) => {
                      handleTrpcError({ title: "Error Creating Asset", error });
                    },
                  }
                );

                setUploadingAssets([]);
                setContentAssets((assets) => [
                  ...assets,
                  ...newAssetsWithProperties.map((asset) => ({
                    ...asset,
                    tags: [],
                  })),
                ]);
              }
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [currentWorkspace, instagramContent, post.id]);

  const updateContentType = (type: InstagramContentType) => {
    setInstagramContent((prevInstagramContent) => {
      upsertInstagramMutation.mutate({
        id: prevInstagramContent.id,
        editorState: prevInstagramContent.editorState,
        copy: prevInstagramContent.copy,
        crossPostToFacebook: prevInstagramContent.crossPostToFacebook,
        reelShareToFeed: prevInstagramContent.reelShareToFeed,
        type,
        postID: post.id,
      });

      return {
        ...prevInstagramContent,
        type,
      };
    });
  };

  return (
    <div
      onDragOverCapture={() => {
        if (!coverPhotoModalOpen) {
          setDraggedOver(true);
        }
      }}
      onDragLeaveCapture={() => {
        setDraggedOver(false);
      }}
    >
      {post.instagramPost && (
        <PostPerformance
          channel="Instagram"
          url={post.instagramPost.permalink}
          stats={[
            {
              label: "Likes",
              value: post.instagramPost.likeCount,
            },
            {
              label: "Comments",
              value: post.instagramPost.commentCount,
            },
            {
              label: "Profile Visits",
              value: post.instagramPost.profileVisitCount,
            },
            {
              label: "Shares",
              value: post.instagramPost.shareCount,
            },
            {
              label: "Saves",
              value: post.instagramPost.savedCount,
            },
            {
              label: "Follows",
              value: post.instagramPost.followCount,
            },
            {
              label: "Impressions",
              value: post.instagramPost.impressionCount,
            },
            {
              label: "Engagement",
              value: formatPercentage(
                post.instagramPost.likeCount +
                  post.instagramPost.commentCount +
                  post.instagramPost.profileVisitCount +
                  post.instagramPost.shareCount +
                  post.instagramPost.savedCount +
                  post.instagramPost.followCount,
                post.instagramPost.impressionCount
              ),
            },
          ]}
        />
      )}
      <PostPreviewContainer
        channel="Instagram"
        post={post}
        connectedAccount={
          integration
            ? {
                integrationID: integration.id,
                name: integration.name,
              }
            : null
        }
        accountOptions={accountOptions.map((account) => ({
          integrationID: account.id,
          name: account.name,
        }))}
        setConnectedAccountID={(instagramIntegrationID: string) => {
          const selectedAccount = accountOptions.find(
            (account) => account.id === instagramIntegrationID
          );
          assert(selectedAccount);
          setIntegration(selectedAccount);
          trpcUtils.instagramContent.getEngagements.setData(
            {
              postID: post.id,
            },
            {
              engagements: engagements.map((engagement) => ({
                ...engagement,
                integrationID: instagramIntegrationID,
              })),
            }
          );
          updatePostMutation.mutate(
            {
              id: post.id,
              instagramIntegrationID,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getIntegrations.refetch({
                  id: post.id,
                });
                trpcUtils.integration.instagram.getGridPosts.refetch({
                  integrationID: instagramIntegrationID,
                });
              },
            }
          );
        }}
        additionalContent={
          <Select
            options={[
              {
                value: "Feed",
                label: "Feed",
              },
              {
                value: "Reel",
                label: "Reel",
              },
              {
                value: "Story",
                label: "Story",
              },
            ]}
            disabled={isReadOnly}
            value={instagramContent.type}
            onChange={async (value) => {
              const newType = value as InstagramContentType;

              if (newType === "Reel") {
                if (contentAssets.length > 1) {
                  return openConfirmationModal({
                    title: "Too many attachments",
                    body: "To convert this post from a Feed post to Reels, please ensure the post only has one attachment, in the form of a .mp4 or .mov file.",
                    cancelButton: null,
                    primaryButton: {
                      label: "Got It",
                    },
                  });
                } else if (
                  contentAssets.length === 1 &&
                  !contentAssets[0].metadata.mimetype.includes("video")
                ) {
                  return openConfirmationModal({
                    title: "No videos associated with post",
                    body: `To convert this post from a ${instagramContent.type} post to Reels, please ensure the post only has one attachment, in the form of a .mp4 or .mov file.`,
                    cancelButton: null,
                    primaryButton: {
                      label: "Got It",
                    },
                  });
                }
              } else if (newType === "Feed") {
                const asset = contentAssets[0];

                const isVideo = asset?.metadata.mimetype.includes("video");
                const isImage = asset?.metadata.mimetype.includes("image");

                if (isVideo) {
                  const { duration, height, width } = await getVideoProperties(
                    asset.signedUrl
                  );

                  if (asset.sizeMB > 125) {
                    return openConfirmationModal({
                      title: "Video is too large",
                      body: "To convert this post from a Reel to a Feed post, please ensure the video is smaller than 125MB.",
                      cancelButton: null,
                      primaryButton: {
                        label: "Got It",
                      },
                    });
                  } else if (duration > 60) {
                    return openConfirmationModal({
                      title: "Video is too long",
                      body: "To convert this post from a Reel to a Feed post, please ensure the video is shorter than 60 seconds.",
                      cancelButton: null,
                      primaryButton: {
                        label: "Got It",
                      },
                    });
                  } else if (width / height < 0.8) {
                    return openConfirmationModal({
                      title: "Video is too tall",
                      body: "To convert this post from a Reel to a Feed post, please ensure the video is between 4:5 and 16:9 aspect ratio.",
                      cancelButton: null,
                      primaryButton: {
                        label: "Got It",
                      },
                    });
                  } else if (width / height > 16 / 9) {
                    return openConfirmationModal({
                      title: "Video is Too Wide",
                      body: "To convert this post from a Reel to a Feed post, please ensure the video is between 4:5 and 16:9 aspect ratio.",
                      cancelButton: null,
                      primaryButton: {
                        label: "Got It",
                      },
                    });
                  }
                  setContentAssets((prevContentAssets) => {
                    // Since you can't tag on a video in reels we'll clear out the user tags
                    return prevContentAssets.map((prevContentAsset) => {
                      deleteAssetTagsMutation.mutate(
                        {
                          assetID: prevContentAsset.id,
                        },
                        {
                          onError: (error) => {
                            handleTrpcError({
                              title: "Error Deleting Asset",
                              error,
                            });
                          },
                        }
                      );
                      return {
                        ...prevContentAsset,
                        tags: [],
                      };
                    });
                  });
                } else if (isImage) {
                  // Nothing to check here
                }
              } else if (newType === "Story") {
                const videoAssets = contentAssets.filter((asset) =>
                  asset.metadata.mimetype.includes("video")
                );
                const imageAssets = contentAssets.filter((asset) =>
                  asset.metadata.mimetype.includes("image")
                );

                if (contentAssets.length > 10) {
                  return openConfirmationModal({
                    title: "Too many attachments",
                    body: "To convert this post from a Feed post to Story, please ensure the post only has one attachment.",
                    cancelButton: null,
                    primaryButton: {
                      label: "Got It",
                    },
                  });
                } else if (videoAssets.some((asset) => asset.sizeMB > 100)) {
                  return openConfirmationModal({
                    title: "Video is Too Large",
                    body: "To convert this post to a Story, please ensure that the video size is smaller than 100MB.",
                    cancelButton: null,
                    primaryButton: {
                      label: "Got It",
                    },
                  });
                } else if (
                  videoAssets.some((asset) => {
                    const duration = asset.duration;
                    return duration != null && duration > 60;
                  })
                ) {
                  return openConfirmationModal({
                    title: "Video is Too Long",
                    body: "To convert this post to a Story, please ensure that the video is shorter than 60 seconds.",
                    cancelButton: null,
                    primaryButton: {
                      label: "Got It",
                    },
                  });
                } else if (imageAssets.some((asset) => asset.sizeMB > 8)) {
                  return openConfirmationModal({
                    title: "Image is Too Large",
                    body: "To convert this post to a Story, please ensure that the image size is smaller than 8MB.",
                    cancelButton: null,
                    primaryButton: {
                      label: "Got It",
                    },
                  });
                }
              }

              updateContentType(newType);
            }}
          />
        }
        Preview={
          <div className="flex h-full w-full items-center justify-center gap-20">
            <div>
              {instagramContent.type === "Reel" ? (
                <InstagramReelsPreview
                  key={`post-preview-reel-${instagramContent.type}`}
                  instagramContent={{
                    ...instagramContent,
                    assets: contentAssets,
                  }}
                  coverPhotoUrl={coverPhoto?.signedUrl}
                  connectedAccount={integration}
                />
              ) : instagramContent.type === "Feed" ? (
                <InstagramFeedPreview
                  key={`post-preview-feed-${instagramContent.type}`}
                  instagramContent={{
                    ...instagramContent,
                    assets: contentAssets,
                  }}
                  connectedAccount={integration}
                />
              ) : (
                <InstagramStoryPreview
                  key={`post-preview-story-${instagramContent.type}`}
                  instagramContent={{
                    ...instagramContent,
                    assets: contentAssets,
                  }}
                  connectedAccount={integration}
                />
              )}
            </div>
            <div
              className={classNames("hidden md:block", {
                hidden:
                  instagramContent.type == "Story" || post.isIdea || isReadOnly,
                "h-content":
                  instagramContent.type == "Feed" ||
                  instagramContent.type == "Reel",
              })}
            >
              <div className="h-96">
                <Divider orientation="vertical" />
              </div>
            </div>
            <div
              className={classNames("flex hidden items-center md:block", {
                hidden:
                  instagramContent.type == "Story" || post.isIdea || isReadOnly,
              })}
            >
              {!post.isIdea && (
                <InstagramGrid
                  post={post}
                  asset={contentAssets[0]}
                  integrationID={integration?.id || null}
                />
              )}
            </div>
          </div>
        }
        isReadOnly={isReadOnly}
      />
      <Tabs.Root defaultValue="post_content">
        <Tabs.List>
          <Tabs.Trigger value="post_content">Post Content</Tabs.Trigger>
          {instagramContent.type !== "Story" && (
            <Tabs.Trigger value="post_options">
              {numSetOptions > 0
                ? `Post Options (${numSetOptions})`
                : "Post Options"}
            </Tabs.Trigger>
          )}
          {instagramContent.type !== "Story" && (
            <Tabs.Trigger value="auto_engagements">
              {engagements.length > 0
                ? `Auto Engagements (${engagements.length})`
                : "Auto Engagements"}
            </Tabs.Trigger>
          )}
        </Tabs.List>
        <Tabs.Content value="post_content">
          <div className="group">
            {instagramContent.type !== "Story" && isRoomReady && (
              <InstagramEditorWrapper
                key={instagramContent.id}
                postID={post.id}
                initialValue={instagramContent.editorState}
                placeholder={
                  instagramContent.type === "Feed"
                    ? "Add your caption and images / video attachments here"
                    : "Add your caption and video attachment here"
                }
                isReadOnly={isReadOnly}
                onImagePaste={(event) => {
                  if (uppy) {
                    handleFilesUpload({
                      items: event.clipboardData.items,
                      uppy,
                      meta: {
                        contentID: instagramContent.id,
                      },
                      getValidFiles,
                    });
                  }
                }}
                onSave={(editorState) => {
                  setInstagramContent((prevInstagramContent) => {
                    upsertInstagramMutation.mutate({
                      id: prevInstagramContent.id,
                      copy: prevInstagramContent.copy,
                      type: prevInstagramContent.type,
                      crossPostToFacebook:
                        prevInstagramContent.crossPostToFacebook,
                      reelShareToFeed: prevInstagramContent.reelShareToFeed,
                      editorState: editorState,
                      postID: post.id,
                    });

                    return {
                      ...prevInstagramContent,
                      editorState,
                    };
                  });
                }}
                onUpdateContent={(copy) => {
                  setInstagramContent((prevInstagramContent) => ({
                    ...prevInstagramContent,
                    copy,
                  }));
                }}
                roomID={createRoomID({
                  workspaceID: post.workspaceID,
                  contentType: "post",
                  postID: post.id,
                  roomContentID: instagramContent.id,
                })}
              />
            )}
            <EditorActionsBar
              post={post}
              channel="Instagram"
              visible={true}
              isReadOnly={isReadOnly}
              characterCount={{
                current: instagramContent.copy.length,
                max: 2200,
              }}
              uploadAssetButton={{
                acceptedMimetypes:
                  instagramContent.type === "Feed" ||
                  instagramContent.type === "Story"
                    ? [
                        "image/jpeg",
                        "image/png",
                        "video/mp4",
                        "video/quicktime",
                      ]
                    : ["video/mp4", "video/quicktime"],
                acceptMultiple: instagramContent.type !== "Reel",
                onUploadFiles: async (files) => {
                  if (uppy) {
                    handleFilesUpload({
                      files,
                      uppy,
                      meta: {
                        contentID: instagramContent.id,
                      },
                      getValidFiles,
                    });
                  }
                },
                isLoading: uploadingAssets.length > 0,
              }}
              openHowToTagModal={() => {
                openIntercomArticle("instagram_tagging");
              }}
            />
            <div
              className={classNames({
                "mt-2": contentAssets.length > 0,
              })}
            >
              <Assets
                assets={contentAssets}
                uploadingAssets={uploadingAssets}
                onDeleteClick={(asset) => {
                  setContentAssets((prevContentAssets) =>
                    prevContentAssets.filter(
                      (contentAsset) => contentAsset.id !== asset.id
                    )
                  );
                  deleteAssetMutation.mutate({
                    instagramAssetID: asset.id,
                  });
                }}
                editImageModalProperties={editImageModalProperties}
                onEditClick={(asset) => {
                  if (asset.metadata.mimetype.startsWith("image/")) {
                    setEditImageModalProperties({
                      title: "Edit Image",
                      channel: "Instagram",
                      maxSizeMB: 8,
                      isOpen: true,
                      asset,
                    });
                  }
                }}
                onUpload={async (file, updateAssetID) => {
                  if (uppy) {
                    if (updateAssetID) {
                      uppy.addFile({
                        name: file.name,
                        type: file.type,
                        data: file,
                        meta: {
                          contentID: instagramContent.id,
                          updateAssetID,
                        },
                      });
                    } else {
                      // no update
                      await handleFilesUpload({
                        files: [file],
                        uppy,
                        meta: {
                          contentID: instagramContent.id,
                        },
                        getValidFiles,
                      });
                    }
                  }
                }}
                validationRules={{
                  minAspectRatio: { width: 4, height: 5 },
                  maxAspectRatio: { width: 191, height: 100 },
                  maxPixels: {
                    value: 36_152_320,
                  },
                }}
                cropSelectPresetOptions={[
                  [undefined, "Custom"],
                  [1, "Square (1:1)"],
                  [0.8, "Portrait (4:5)"],
                  [1.91, "Landscape (1.91:1)"],
                ]}
                onEditImageModalClose={() => setEditImageModalProperties(null)}
                refetchAssets={() => {}}
                isReadOnly={isReadOnly}
                showDropzone={draggedOver}
                onDrop={async (files) => {
                  setDraggedOver(false);
                  if (uppy) {
                    await handleFilesUpload({
                      files,
                      uppy,
                      meta: {
                        contentID: instagramContent.id,
                      },
                      getValidFiles,
                    });
                  }
                }}
                acceptedFileTypes={
                  instagramContent.type === "Feed" ||
                  instagramContent.type === "Story"
                    ? {
                        "image/jpg": [".jpg", ".jpeg"],
                        "image/png": [".png"],
                        "video/mp4": [".mp4"],
                        "video/quicktime": [".mov"],
                      }
                    : {
                        "video/mp4": [".mp4"],
                        "video/quicktime": [".mov"],
                      }
                }
                onSortEnd={onSortEnd}
                showNewEditButton={true}
              />
            </div>
          </div>
        </Tabs.Content>
        <Tabs.Content value="post_options">
          <PostOptions>
            {(instagramContent.type === "Feed" ||
              instagramContent.type === "Reel") && (
              <PostOption
                title="Add Location"
                description="Enter a location for your post"
                disabled={instagramContent.type === "Reel"}
                disabledMessage={"Location tagging is not supported for reels"}
              >
                <AddLocationModal
                  location={instagramContent.location}
                  onSaveLocation={(location) => {
                    saveLocationMutation.mutate(
                      {
                        id: instagramContent.id,
                        location,
                      },
                      {
                        onError: (error) => {
                          handleTrpcError({
                            title: "Failed to save location",
                            error,
                          });
                        },
                      }
                    );
                    trpcUtils.post.get.invalidate({
                      id: post.id,
                    });
                    setInstagramContent((prevInstagramContent) => ({
                      ...prevInstagramContent,
                      location,
                    }));
                  }}
                />
              </PostOption>
            )}
            {instagramContent.type === "Reel" && (
              <PostOption
                title="Share Reel to Feed"
                description="Choose if this reel should be shared on your feed"
              >
                <Select
                  value={instagramContent.reelShareToFeed}
                  options={[
                    {
                      value: true,
                      label: "Share to Feed",
                    },
                    {
                      value: false,
                      label: "Don't share to Feed",
                    },
                  ]}
                  onChange={(value) => {
                    setInstagramContent((prevInstagramContent) => {
                      upsertInstagramMutation.mutate(
                        {
                          id: prevInstagramContent.id,
                          copy: prevInstagramContent.copy,
                          type: prevInstagramContent.type,
                          reelShareToFeed: value,
                          crossPostToFacebook:
                            prevInstagramContent.crossPostToFacebook,
                          editorState: prevInstagramContent.editorState,
                          postID: post.id,
                        },
                        {
                          onSuccess: () => {
                            trpcUtils.integration.instagram.getGridPosts.refetch(
                              {
                                integrationID: integration!.id,
                              }
                            );
                          },
                        }
                      );

                      return {
                        ...prevInstagramContent,
                        reelShareToFeed: value,
                      };
                    });
                  }}
                />
              </PostOption>
            )}
            {instagramContent.type === "Reel" && (
              <PostOption
                title="Customize Cover Photo"
                description="Upload a custom cover photo"
              >
                <CoverPhotoModal
                  contentID={instagramContent.id}
                  video={contentAssets[0]}
                  storagePath={`${currentWorkspace?.id}/${post.id}/instagram/${instagramContent.id}/cover_photo`}
                  coverPhoto={coverPhoto}
                  open={coverPhotoModalOpen}
                  setOpen={setCoverPhotoModalOpen}
                  onUpload={(fileName, onSave) => {
                    getNewAssetsMutation.mutate(
                      {
                        path: `${currentWorkspace!.id}/${post.id}/instagram/${
                          instagramContent.id
                        }/cover_photo`,
                        fileNames: [fileName],
                      },
                      {
                        onSuccess: async (data) => {
                          const { assets } = data;
                          const newAssetsWithProperties = (
                            await getAssetsWithProperties(assets)
                          ).map((asset) => ({
                            ...asset,
                            id: uuidv4(),
                            assetID: asset.id,
                          }));

                          const coverPhotoAsset = newAssetsWithProperties[0];

                          setCoverPhoto(coverPhotoAsset);

                          upsertCoverPhotoMutation.mutate({
                            id: instagramContent.id,
                            asset: {
                              id: coverPhotoAsset.id,
                              name: coverPhotoAsset.name,
                              path: coverPhotoAsset.path,
                              eTag: coverPhotoAsset.eTag,
                              md5Hash: coverPhotoAsset.md5Hash,
                              size: coverPhotoAsset.sizeBytes,
                              mimetype: coverPhotoAsset.mimetype,
                              width: coverPhotoAsset.width,
                              height: coverPhotoAsset.height,
                              url: coverPhotoAsset.signedUrl,
                              urlExpiresAt: coverPhotoAsset.urlExpiresAt,
                            },
                          });

                          onSave();
                        },
                        onError: (error) => {
                          handleTrpcError({
                            title: "Failed to upload cover photo",
                            error,
                          });
                        },
                      }
                    );
                  }}
                  onDelete={(asset) => {
                    deleteCoverPhotoMutation.mutate({
                      instagramContentID: instagramContent.id,
                      path: asset.path,
                    });
                    setCoverPhoto(null);
                  }}
                  getValidFiles={async (files: File[]) => {
                    if (files.length > 1) {
                      toast.error("Too many files", {
                        description: "Please only upload one image.",
                      });
                      return [];
                    } else {
                      const file = files[0];

                      if (
                        file.type !== "image/jpeg" &&
                        file.type !== "image/png"
                      ) {
                        toast.error("Invalid file type", {
                          description: "Please upload a JPG or PNG file.",
                        });
                        return [];
                      }

                      const fileSizeMB = file.size / 1024 / 1024;
                      if (fileSizeMB > 8) {
                        const compressedFile = await compressImage(file, {
                          maxSizeMB: 8,
                          type: "image/jpeg",
                        });
                        toast.info("Attachment Optimized", {
                          description: `${file.name} was optimized (files >8MB are not accepted on Instagram)`,
                        });
                        posthog.capture(POSTHOG_EVENTS.image.compressed, {
                          originalSizeMB: file.size / 1024 / 1024,
                          compressedSizeKB: compressedFile.size / 1024 / 1024,
                          channel: "Instagram",
                          channelPostType: "Cover Photo",
                        });
                        return [compressedFile];
                      } else if (file.type === "image/png") {
                        const jpegFile = await compressImage(file, {
                          maxSizeMB: 8,
                          type: "image/jpeg",
                        });

                        toast.info("Attachment Converted to JPEG", {
                          description: `${file.name} was converted to JPEG (PNG files are not accepted on Instagram)`,
                        });
                        posthog.capture(POSTHOG_EVENTS.image.compressed, {
                          originalSizeMB: file.size / 1024 / 1024,
                          compressedSizeKB: jpegFile.size / 1024 / 1024,
                          channel: "Instagram",
                          channelPostType: "Cover Photo",
                        });

                        return [jpegFile];
                      } else {
                        return files;
                      }
                    }
                  }}
                />
              </PostOption>
            )}
            <PostOption
              title="Tag Users"
              description="Tag users in your post"
              disabled={contentAssets.length === 0}
              disabledMessage="Add an image or video to tag users"
            >
              <TagUsersModal
                postID={post.id}
                assets={contentAssets}
                contentType={instagramContent.type}
                onTagCreate={(tag) => {
                  const assetIndex = contentAssets.findIndex(
                    (asset) => asset.id === tag.assetID
                  );
                  assert(assetIndex !== -1, "Asset not found");
                  const asset = contentAssets[assetIndex];
                  const newAsset = {
                    ...asset,
                    tags: [...asset.tags, tag],
                  };
                  const newAssets = [...contentAssets];
                  newAssets[assetIndex] = newAsset;
                  setContentAssets(newAssets);
                }}
                onTagDelete={(tag) => {
                  const assetIndex = contentAssets.findIndex(
                    (asset) => asset.id === tag.assetID
                  );
                  assert(assetIndex !== -1, "Asset not found");
                  const asset = contentAssets[assetIndex];
                  const newAsset = {
                    ...asset,
                    tags: asset.tags.filter(
                      (assetTag) => assetTag.id !== tag.id
                    ),
                  };
                  const newAssets = [...contentAssets];
                  newAssets[assetIndex] = newAsset;
                  setContentAssets(newAssets);
                }}
              />
            </PostOption>
            <PostOption
              title="Add Collaborators"
              description="Add collaborators to your post"
            >
              <CollaborateUsersModal
                postID={post.id}
                instagramContent={instagramContent}
                updateCollaborators={(collaborators) => {
                  setInstagramContent((prevInstagramContent) => ({
                    ...prevInstagramContent,
                    collaborators,
                  }));
                }}
              />
            </PostOption>
          </PostOptions>
        </Tabs.Content>
        <Tabs.Content value="auto_engagements">
          {instagramContent.type !== "Story" && (
            <PostEngagements
              channel="Instagram"
              engagements={engagements}
              onEngagementCreate={async (type) => {
                const engagementID = uuidv4();

                await trpcUtils.instagramContent.getEngagements.cancel();

                createEngagementMutation.mutate({
                  engagementID,
                  contentID: instagramContent.id,
                  type,
                  integrationID: integration!.id,
                });

                // Optimistically update the restponse to the engagementsQuery
                trpcUtils.instagramContent.getEngagements.setData(
                  {
                    postID: post.id,
                  },
                  {
                    engagements: [
                      ...engagements,
                      {
                        id: engagementID,
                        type,
                        copy: null,
                        editorState: null,
                        integrationID: integration!.id,
                        integration: {
                          id: integration!.id,
                          account: {
                            username: integration!.name.replace("@", ""),
                            profileImageUrl: integration!.profileImageUrl,
                          },
                        },
                        postDelaySeconds: 0,
                        createdAt: new Date(),
                      },
                    ],
                  }
                );
              }}
              accountChooserDisabled={true}
              onEngagementUpdate={(engagementID, engagement) => {
                updateEngagementMutation.mutate(
                  {
                    engagementID,
                    ...engagement,
                  },
                  {
                    onSuccess: () => {
                      trpcUtils.instagramContent.getEngagements.invalidate();
                    },
                  }
                );
              }}
              onEngagementDelete={(engagementID) => {
                deleteEngagementMutation.mutate(
                  {
                    engagementID,
                  },
                  {
                    onSuccess: () => {
                      trpcUtils.instagramContent.getEngagements.invalidate();
                    },
                  }
                );

                // Optimistically update the restponse to the engagementsQuery to remove this engagement
                trpcUtils.instagramContent.getEngagements.setData(
                  {
                    postID: post.id,
                  },
                  {
                    engagements: (
                      engagementsQuery.data?.engagements || []
                    ).filter((engagement) => engagement.id !== engagementID),
                  }
                );
              }}
              disableReposts={true}
              accountOptions={
                isReadOnly
                  ? engagements.map((engagement) => ({
                      value: engagement.integrationID,
                      label: `@${engagement.integration.account.username}`,
                    }))
                  : accountOptions.map((account) => ({
                      value: account.id,
                      label: account.name,
                    }))
              }
              infoBanner={
                !integration?.scope.includes("instagram_manage_comments") &&
                !integration?.scope.includes(
                  "instagram_business_manage_comments"
                )
                  ? {
                      title: "Additional Permissions Needed",
                      message: (
                        <>
                          We need some additional permissions from Instagram in
                          order to post comments.{" "}
                          <TextLink
                            value="Click here"
                            href="/settings/profiles"
                            size="sm"
                            openInNewTab={false}
                          />{" "}
                          to reintegrate.
                        </>
                      ),
                    }
                  : undefined
              }
              Editor={({
                initialValue,
                onSave,
                onUpdateContent,
                placeholder,
                engagementID,
              }) => (
                <div className="mb-4">
                  <InstagramEditorWrapper
                    postID={post.id}
                    initialValue={initialValue}
                    placeholder={placeholder}
                    onSave={onSave}
                    onUpdateContent={onUpdateContent}
                    size="sm"
                    showHashtagCount={true}
                    isReadOnly={isReadOnly}
                    roomID={null}
                  />
                </div>
              )}
              isReadOnly={isReadOnly}
            />
          )}
        </Tabs.Content>
      </Tabs.Root>
    </div>
  );
};

export default InstagramPost;
