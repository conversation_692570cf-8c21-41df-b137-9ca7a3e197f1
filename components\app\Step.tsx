import { ReactNode } from "react";

type Props = {
  stepNumber: number;
  title: string;
  description?: string | ReactNode;
  children?: ReactNode;
};

const Step = ({ stepNumber, title, description, children }: Props) => {
  return (
    <div>
      <div className="flex items-center gap-2">
        <div className="text-secondary font-eyebrow">
          {stepNumber < 10 ? `0${stepNumber}` : stepNumber}
        </div>
        <div className="font-body-sm font-medium">{title}</div>
      </div>
      {description && (
        <div className="font-body-sm text-medium-dark-gray">{description}</div>
      )}
      {children && <div className="mt-2">{children}</div>}
    </div>
  );
};

export default Step;
