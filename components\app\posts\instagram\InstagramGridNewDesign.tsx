import { ArrowPathIcon } from "@heroicons/react/24/outline";
import { useEffect } from "react";
import TextLink from "~/components/TextLink";
import Tooltip from "~/components/Tooltip";
import Button from "~/components/ui/Button";
import { getPostPublishAtMoment, trpc } from "~/utils";

type Props = {
  post: {
    id: string;
    title: string;
  };
  asset: {
    id: string;
    signedUrl: string;
    metadata: {
      mimetype: string;
    };
  } | null;
  integrationID: string | null;
};

const InstagramGridNewDesign = ({ post, asset, integrationID }: Props) => {
  const postID = post.id;
  const gridQuery = trpc.integration.instagram.getGridPosts.useQuery({
    integrationID,
  });
  const refetchGridMutation =
    trpc.integration.instagram.refetchGridPosts.useMutation({
      onSuccess: () => {
        gridQuery.refetch();
      },
    });

  useEffect(() => {
    gridQuery.refetch();
  }, [integrationID, asset]);

  if (integrationID == null) {
    const arrayOfTwelve = Array.from(Array(12).keys());

    return (
      <div className="scrollbar-hidden h-96 overflow-clip rounded-md bg-white">
        <div className="border-lightest-gray/50 sticky top-0 z-10 flex items-center justify-between border-b bg-white px-3 py-2">
          <span className="h-3"></span>
        </div>
        <div className="bg-lightest-gray/50 relative grid w-[273px] resize-none grid-cols-3 gap-px">
          {arrayOfTwelve.map((index) => {
            return (
              <div key={index} className="aspect-122/163 h-full w-full">
                <div className="h-full w-full bg-white" />
              </div>
            );
          })}
          <div className="font-body-sm absolute inset-y-1/3 left-5 text-center whitespace-nowrap">
            <div className="font-medium">No account connected</div>
            <div className="text-medium-dark-gray">
              <TextLink
                value="Click here"
                size="sm"
                href="/settings/profiles"
                openInNewTab={false}
              />{" "}
              to connect your Instagram
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="scrollbar-hidden border-lightest-gray h-[538px] overflow-scroll rounded-t-2xl border-x border-t bg-white">
      <div className="border-lightest-gray sticky top-0 z-10 flex items-center justify-between border-b bg-white p-4">
        <span className="font-body-xs">Grid View</span>
        <Button
          onClick={() => {
            refetchGridMutation.mutate({ integrationID });
          }}
          variant="ghost"
          size="xs"
          isLoading={refetchGridMutation.isPending}
        >
          <ArrowPathIcon className="h-3 w-3" />
          Refresh
        </Button>
      </div>
      <div className="grid w-full resize-none grid-cols-3 gap-px">
        {gridQuery.data?.posts.map((post) => {
          const isCurrentPost = post.id === postID;

          if (isCurrentPost && asset) {
            if (asset.metadata.mimetype.includes("image")) {
              return (
                <div
                  key={`${post.id}-${asset.id}`}
                  className="relative aspect-122/163 h-full w-full"
                >
                  <img
                    className="h-full w-full object-cover"
                    src={asset.signedUrl}
                    alt=""
                  />
                  <div className="bg-basic-light border-basic absolute top-1 left-1 h-1.5 w-1.5 rounded-full border-[0.5px]" />
                </div>
              );
            } else {
              <div
                key={`${post.id}-${asset.id}`}
                className="relative aspect-122/163 h-full w-full"
              >
                <video className="h-full w-full object-cover">
                  <source src={asset.signedUrl} />
                </video>
                <div className="bg-basic-light border-basic absolute top-1 left-1 h-1.5 w-1.5 rounded-full border-[0.5px]" />
              </div>;
            }
          }

          const { firstAsset } = post;
          if (firstAsset != null) {
            if (firstAsset.metadata.mimetype.includes("image")) {
              return (
                <Tooltip
                  key={post.id}
                  value={
                    <div>
                      <span className="text-medium-dark-gray font-body-sm">
                        {getPostPublishAtMoment(post).toFormat("MMM d")}
                      </span>{" "}
                      {post.title}
                    </div>
                  }
                  sideOffset={-25}
                >
                  <a
                    href={`/posts/${post.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative aspect-122/163"
                  >
                    <img
                      className="h-full w-full object-cover"
                      src={firstAsset.signedUrl}
                      alt=""
                    />
                    <div className="bg-basic-light border-basic absolute top-1 left-1 h-1.5 w-1.5 rounded-full border-[0.5px]" />
                  </a>
                </Tooltip>
              );
            } else {
              return (
                <Tooltip
                  key={post.id}
                  value={
                    <div>
                      <span className="text-medium-dark-gray font-body-sm">
                        {getPostPublishAtMoment(post).toFormat("MMM d")}
                      </span>{" "}
                      {post.title}
                    </div>
                  }
                  sideOffset={-25}
                >
                  <a
                    href={`/posts/${post.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative aspect-122/163"
                  >
                    <video className="h-full w-full object-cover">
                      <source src={firstAsset.signedUrl} />
                    </video>
                    <div className="bg-basic-light border-basic absolute top-1 left-1 h-1.5 w-1.5 rounded-full border-[0.5px]" />
                    <div className="absolute top-0.5 right-1 h-1.5 w-1.5">
                      <svg
                        aria-label="Clip"
                        color="rgb(255, 255, 255)"
                        fill="rgb(255, 255, 255)"
                        height="8"
                        role="img"
                        viewBox="0 0 24 24"
                        width="8"
                      >
                        <title>Clip</title>
                        <path
                          d="m12.823 1 2.974 5.002h-5.58l-2.65-4.971c.206-.013.419-.022.642-.027L8.55 1Zm2.327 0h.298c3.06 0 4.468.754 5.64 1.887a6.007 6.007 0 0 1 1.596 2.82l.07.295h-4.629L15.15 1Zm-9.667.377L7.95 6.002H1.244a6.01 6.01 0 0 1 3.942-4.53Zm9.735 12.834-4.545-2.624a.909.909 0 0 0-1.356.668l-.008.12v5.248a.91.91 0 0 0 1.255.84l.109-.053 4.545-2.624a.909.909 0 0 0 .1-1.507l-.1-.068-4.545-2.624Zm-14.2-6.209h21.964l.015.36.003.189v6.899c0 3.061-.755 4.469-1.888 5.64-1.151 1.114-2.5 1.856-5.33 1.909l-.334.003H8.551c-3.06 0-4.467-.755-5.64-1.889-1.114-1.15-1.854-2.498-1.908-5.33L1 15.45V8.551l.003-.189Z"
                          fillRule="evenodd"
                        ></path>
                      </svg>
                    </div>
                  </a>
                </Tooltip>
              );
            }
          }

          return (
            <Tooltip
              key={post.id}
              value={
                <div>
                  <span className="text-medium-dark-gray font-body-sm">
                    {getPostPublishAtMoment(post).toFormat("MMM d")}
                  </span>{" "}
                  {post.title}
                </div>
              }
              sideOffset={-25}
            >
              <a
                href={`/posts/${post.id}`}
                target="_blank"
                rel="noopener noreferrer"
                className="aspect-122/163"
              >
                <div className="bg-lightest-gray relative h-full w-full overflow-clip">
                  <div className="font-body-xs absolute bottom-1 px-1">
                    <div className="text-medium-dark-gray one-line-ellipses max-w-[72px]">
                      {post.title}
                    </div>
                    <div className="text-medium-gray">No asset</div>
                  </div>
                  <div className="bg-basic-light border-basic absolute top-1 left-1 h-1.5 w-1.5 rounded-full border-[0.5px]" />
                </div>
              </a>
            </Tooltip>
          );
        })}
        {gridQuery.data?.instagramPosts.map((post) => {
          const isCarousel = post.mediaType === "CAROUSEL_ALBUM";

          return (
            <a
              key={post.permalink}
              className="relative aspect-122/163"
              href={post.permalink}
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                className="h-full w-full object-cover"
                src={post.thumbnailUrl}
              />
              {isCarousel && (
                <div className="absolute top-0.5 right-1.5 h-1.5 w-1.5">
                  <svg
                    aria-label="Carousel"
                    color="rgb(255, 255, 255)"
                    fill="rgb(255, 255, 255)"
                    height="10"
                    role="img"
                    viewBox="0 0 48 48"
                    width="10"
                  >
                    <title>Carousel</title>
                    <path d="M34.8 29.7V11c0-2.9-2.3-5.2-5.2-5.2H11c-2.9 0-5.2 2.3-5.2 5.2v18.7c0 2.9 2.3 5.2 5.2 5.2h18.7c2.8-.1 5.1-2.4 5.1-5.2zM39.2 15v16.1c0 4.5-3.7 8.2-8.2 8.2H14.9c-.6 0-.9.7-.5 1.1 1 1.1 2.4 1.8 4.1 1.8h13.4c5.7 0 10.3-4.6 10.3-10.3V18.5c0-1.6-.7-3.1-1.8-4.1-.5-.4-1.2 0-1.2.6z"></path>
                  </svg>
                </div>
              )}
            </a>
          );
        })}
      </div>
    </div>
  );
};

export default InstagramGridNewDesign;
