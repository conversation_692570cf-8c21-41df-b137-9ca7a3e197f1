import { useState } from "react";

import { useRouter } from "next/router";

import { toast } from "sonner";
import { AutosizeInput, Divider } from "~/components";
import DropdownMenu from "~/components/ui/DropdownMenu";

import {
  ArrowLeftIcon,
  ArrowUturnRightIcon,
  EllipsisVerticalIcon,
} from "@heroicons/react/24/outline";
import { openConfirmationModal, trpc } from "~/utils";
import ShareLinkDropdown from "../ShareLinkDropdown";

type Props = {
  campaign: {
    id: string;
    name: string;
    isSharedPublicly: boolean;
    arePostsSharedPublicly: boolean;
  };
  isReadOnly: boolean;
};

const CampaignHeader = ({ campaign, isReadOnly }: Props) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();
  const [name, setName] = useState<string>(campaign.name);

  const deleteCampaignMutation = trpc.campaign.delete.useMutation({
    onSuccess: () => {
      trpcUtils.campaign.getAll.refetch();
      trpcUtils.post.getMany.refetch();
    },
  });

  const updateCampaignMutation = trpc.campaign.update.useMutation();

  const updateName = () => {
    updateCampaignMutation.mutate({
      id: campaign.id,
      name,
    });
  };

  const handleDeleteCampaign = () => {
    openConfirmationModal({
      title: `Confirm Deletion of Campaign`,
      body: `This action cannot be undone. Please confirm you want to delete this campaign.`,
      cancelButton: {
        label: "Cancel, don't delete",
      },
      secondaryButton: {
        label: "Delete Campaign and Posts",
        onClick: () => {
          router.push("/");
          toast.promise(
            deleteCampaignMutation.mutateAsync({
              id: campaign.id,
              deletePosts: true,
            }),
            {
              loading: "Deleting Campaign",
              success: () => {
                return {
                  message: "Campaign Deleted",
                  description: "Successfully deleted campaign",
                };
              },
              error: (error) => {
                return {
                  message: "Error Deleting Campaign",
                  description: error.message as string,
                };
              },
            }
          );
        },
        variant: "warning",
      },
      primaryButton: {
        label: "Delete Just Campaign",
        variant: "danger",
        onClick: () => {
          router.push("/");
          toast.promise(
            deleteCampaignMutation.mutateAsync({
              id: campaign.id,
              deletePosts: false,
            }),
            {
              loading: "Deleting Campaign",
              success: () => {
                return {
                  message: "Campaign Deleted",
                  description: "Successfully deleted campaign",
                };
              },
              error: (error) => {
                return {
                  message: "Error Deleting Campaign",
                  description: error.message as string,
                };
              },
            }
          );
        },
      },
    });
  };

  return (
    <div className="sticky top-0 z-10 bg-white pt-2">
      <div className="flex h-8 items-center gap-2">
        {!isReadOnly ? (
          <>
            {router.asPath.includes("/inbox") ? (
              <button
                className="text-basic font-body-sm flex items-center gap-1 font-medium"
                onClick={() => {
                  router.push(`/campaigns/${campaign.id}`);
                }}
              >
                <ArrowUturnRightIcon className="h-4 w-4" />
                Go to Campaign Page
              </button>
            ) : (
              <button
                className="text-basic font-body-sm flex items-center gap-1 font-medium"
                onClick={() => {
                  router.back();
                }}
              >
                <ArrowLeftIcon className="h-4 w-4" />
                Back
              </button>
            )}
          </>
        ) : null}
        <Divider orientation="vertical" />
        <div className="flex w-full items-center justify-between">
          <AutosizeInput
            value={name}
            onChange={setName}
            isReadOnly={isReadOnly}
            onBlur={() => {
              if (campaign.name !== name) {
                updateName();
              }
            }}
            size="sm"
            weight="medium"
          />
          {!isReadOnly ? (
            <div className="flex items-center gap-2">
              <ShareLinkDropdown
                id={campaign.id}
                type="campaign"
                isSharedPublicly={campaign.isSharedPublicly}
                arePostsSharedPublicly={campaign.arePostsSharedPublicly}
              />
              <DropdownMenu.Root>
                <DropdownMenu.Trigger>
                  <div className="hover:bg-lightest-gray/50 rounded-full bg-inherit p-1.5 text-black transition-all">
                    <EllipsisVerticalIcon className="h-5 w-5" />
                  </div>
                </DropdownMenu.Trigger>
                <DropdownMenu.Content>
                  <DropdownMenu.Group>
                    <DropdownMenu.Item
                      icon="TrashIcon"
                      intent="danger"
                      onClick={handleDeleteCampaign}
                    >
                      Delete Campaign
                    </DropdownMenu.Item>
                  </DropdownMenu.Group>
                </DropdownMenu.Content>
              </DropdownMenu.Root>
            </div>
          ) : null}
        </div>
      </div>
      <Divider padding="md" />
    </div>
  );
};

export default CampaignHeader;
