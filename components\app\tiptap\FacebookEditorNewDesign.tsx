import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Document from "@tiptap/extension-document";
import { History } from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { ClipboardEvent, useEffect, useRef, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import { usePostIntegrations, useThreadComments } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { stripLiveblocksMarks } from "~/utils";
import ThreadCommentsPopover from "../posts/ThreadCommentsPopover";
import {
  <PERSON>Hashtag,
  CharacterCount,
  CustomCommentThreadMark,
  <PERSON><PERSON><PERSON>,
  Facebook<PERSON>ention,
  Link,
  <PERSON>eHandler,
} from "./extensions";
import BoldUnicodeButton from "./ui/BoldUnicodeButton";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import FloatingToolbar from "./ui/FloatingToolbar";
import ItalicsUnicodeButton from "./ui/ItalicsUnicodeButton";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  title?: string;
  postID?: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  isEditable?: boolean;
  size?: "sm" | "md";
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
};

const createBaseExtensions = (
  postID: string | undefined,
  placeholder: string,
  size: "sm" | "md"
) => [
  Document,
  Paragraph.configure({
    HTMLAttributes: {
      class: size === "sm" ? "font-body-sm" : "font-body",
    },
  }),
  Text,
  History,
  Emoji,
  Link.configure({
    isLinkShorteningEnabled: !!postID ? true : false,
    postID,
  }),
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  AutoHashtag.configure({
    urlFn: (hashtag) => `https://www.facebook.com/hashtag/${hashtag}`,
  }),
  FacebookMention.configure({}),
  CharacterCount.configure({
    limit: 2200,
  }),
  PasteHandler,
];

// Loads the content in the editor and displays it as a fallback when the editor, comment threads are loading
const FacebookEditorFallback = ({
  initialValue,
  size = "md",
  postID,
  placeholder,
  onSave,
  onUpdateContent,
  isReadOnly = false,
  isEditable = false,
}: BaseProps) => {
  const cleanContent = stripLiveblocksMarks(initialValue);

  const [editorState, setEditorState] = useState<JSONContent | null>(
    cleanContent
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(cleanContent) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const editor = useEditor({
    extensions: createBaseExtensions(postID, placeholder, size),
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      onUpdateContent(textContent);
      setEditorState(editor.getJSON());
    },
    content: cleanContent,
    editable: isEditable && !isReadOnly,
  });

  if (!editor) return null;

  return isEditable && !isReadOnly ? (
    <>
      <FloatingToolbar editor={editor} shouldHideWithLink={true}>
        <BoldUnicodeButton editor={editor} useCustomToolbarButton={true} />
        <ItalicsUnicodeButton editor={editor} useCustomToolbarButton={true} />
      </FloatingToolbar>
      <EditorContent editor={editor} />
    </>
  ) : (
    <EditorContent editor={editor} />
  );
};

const FacebookEditor = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  size = "md",
  roomID,
}: BaseProps & { roomID: string }) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );
  const { setEditorForRoom } = useThreadComments();
  const { currentChannel } = usePostIntegrations();

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(postID, placeholder, size),
    ],
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      // TipTap doubles newlines for paragraph breaks (e.g., pressing Enter once creates two \n).
      // We halve the newlines to match LinkedIn's expected format while preserving paragraph structure.
      const textContent = editor.getText().replace(/\n+/g, (match) => {
        return "\n".repeat(Math.ceil(match.length / 2));
      });
      onUpdateContent(textContent);
      setEditorState(editor.getJSON());
    },
    editable: !isReadOnly,
  });

  const isEditorReady = useIsEditorReady();

  useEffect(() => {
    if (editor && currentChannel === "Facebook") {
      setEditorForRoom(editor, roomID);
    }

    return () => {
      setEditorForRoom(null, roomID);
    };
  }, [editor, currentChannel, setEditorForRoom, roomID]);

  if (!editor) return null;

  return (
    <div
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor} shouldHideWithLink={true}>
        <ToolbarActions
          editor={editor}
          features={{
            unicodeFormatting: true,
          }}
        />
      </CustomFloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}

      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["Facebook"].label,
            }}
          />
        </>
      ) : (
        <FacebookEditorFallback
          initialValue={initialValue}
          size={size}
          postID={postID}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
        />
      )}
    </div>
  );
};

const FacebookEditorWrapperNewDesign = ({
  title,
  postID,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  size = "md",
  roomID,
}: BaseProps & { roomID: string | null }) => {
  const { currentChannel } = usePostIntegrations();
  const threadCommentsContainerRef = useRef<HTMLDivElement>(null);

  if (!roomID) {
    return (
      <div className="py-4">
        <FacebookEditorFallback
          title={title}
          postID={postID}
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
          isReadOnly={isReadOnly}
          isEditable={!isReadOnly}
          size={size}
        />
      </div>
    );
  }

  return (
    <div className="py-4">
      <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
        <ClientSideSuspense
          fallback={
            <FacebookEditorFallback
              initialValue={initialValue}
              size={size}
              postID={postID}
              placeholder={placeholder}
              onSave={onSave}
              onUpdateContent={onUpdateContent}
            />
          }
        >
          <div className="relative">
            <FacebookEditor
              title={title}
              postID={postID}
              initialValue={initialValue}
              placeholder={placeholder}
              onSave={onSave}
              onUpdateContent={onUpdateContent}
              onImagePaste={onImagePaste}
              isReadOnly={isReadOnly}
              isEditable={!isReadOnly}
              size={size}
              roomID={roomID}
            />
            <div ref={threadCommentsContainerRef} />
          </div>
        </ClientSideSuspense>
        {currentChannel === "Facebook" && (
          <ThreadCommentsPopover
            key={roomID}
            roomID={roomID}
            containerRef={threadCommentsContainerRef}
          />
        )}
      </RoomProvider>
    </div>
  );
};

export default FacebookEditorWrapperNewDesign;
