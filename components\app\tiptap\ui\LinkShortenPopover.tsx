import {
  ArrowTopRightOnSquareIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import truncate from "lodash/truncate";
import { useEffect, useState } from "react";
import Button from "~/components/ui/Button";
import Switch from "~/components/ui/Switch";
import {
  handleTrpcError,
  openIntercomArticle,
  openIntercomChat,
  trpc,
} from "~/utils";

type LinkShortenPopoverProps = {
  postID: string;
  href: string;
  linkNode: Node;
  onShortenLink: (shortUrl: string) => void;
};

const LinkShortenPopover = ({
  postID,
  href,
  linkNode,
  onShortenLink,
}: LinkShortenPopoverProps) => {
  const trpcUtils = trpc.useUtils();
  const [shortLink, setShortLink] = useState<{
    id: string;
    shortLink: string;
    url: string;
  } | null>(null);

  const postLinksQuery = trpc.dub.getPostLinks.useQuery({
    postID,
  });
  const links = postLinksQuery.data?.dubLinks;
  const createLinkMutation = trpc.dub.create.useMutation();

  useEffect(() => {
    if (links) {
      const link = links.find((link) => link.shortLink === href);

      setShortLink(link || null);
    }
  }, [href, links]);

  return (
    <div className="font-body-sm h-fit w-96 space-y-1.5 rounded-md bg-white p-3 shadow-xl">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Switch
            checked={shortLink !== null}
            onCheckedChange={() => {
              if (shortLink) {
                onShortenLink(shortLink.url);
              } else {
                createLinkMutation.mutate(
                  {
                    url: href,
                    postID,
                  },
                  {
                    onSuccess: (data) => {
                      onShortenLink(data.shortLink.shortLink);
                      trpcUtils.dub.getPostLinks.refetch();
                      setShortLink(data.shortLink);
                    },
                    onError: (error) => {
                      handleTrpcError({
                        title: "Failed to shorten link",
                        error,
                      });
                    },
                  }
                );
              }
            }}
          />
          <span className="font-medium">Shorten Link?</span>
        </div>
        <a
          href={shortLink ? shortLink.url : href}
          target="_blank"
          rel="noopener noreferrer"
        >
          <ArrowTopRightOnSquareIcon className="text-basic h-4 w-4" />
        </a>
      </div>
      <div className="text-medium-dark-gray flex items-center gap-1">
        Use our link shortener to get click level analytics{" "}
        <InformationCircleIcon
          className="hover:text-basic h-4 w-4 cursor-pointer transition-colors"
          onClick={() => {
            openIntercomArticle("shortlink_analytics");
          }}
        />
      </div>
      {!!shortLink && (
        <div>
          <a
            className="text-secondary hover:underline"
            href={shortLink.url}
            target="_blank"
            rel="noopener noreferrer"
          >
            {truncate(shortLink.url, { length: 50 })}
          </a>
        </div>
      )}
      <div className="flex items-center gap-2">
        <span className="text-medium-dark-gray">
          Want to use a custom domain?
        </span>
        <Button
          size="sm"
          variant="link"
          onClick={() => {
            openIntercomChat(
              "I'd like to learn more about setting up a custom domain for the link shortener"
            );
          }}
        >
          Chat with us
        </Button>
      </div>
    </div>
  );
};

export default LinkShortenPopover;
