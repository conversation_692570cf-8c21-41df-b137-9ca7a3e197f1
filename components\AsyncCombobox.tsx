import { FocusEvent, useState } from "react";

import { XMarkIcon } from "@heroicons/react/24/outline";
import { default as classnames, default as classNames } from "classnames";

import { useDebouncedCallback, useFocusWithin } from "@mantine/hooks";
import { Option } from "./Combobox";
import { Command, CommandEmpty, CommandGroup, CommandItem } from "./Command";
import Loader from "./Loader";
import TextInput from "./TextInput";
import Popover from "./ui/Popover";

type Value<T> = {
  value: T;
  label: string | number | JSX.Element;
};

type ComboboxProps<T> = {
  /** The currently selected value of the input */
  value: Value<T> | null;
  /** The options that the select input contains */
  options: Option<T>[];
  /** The placeholder of the input */
  placeholder?: string;
  /** Called when the value of the select input is changed */
  onChange: (value: Value<T> | null) => void;
  /** Called when the search input is changed */
  onSearchChange: (search: string) => void;
  /** Whether the options are loading */
  isLoading?: boolean;
  /** The position of the anchor of the dropdown */
  dropdownPosition?: "left" | "right";
  /** Called when the input is blurred */
  onBlur?: (e: FocusEvent<HTMLButtonElement>) => void;
};

const AsyncCombobox = ({
  value,
  options,
  onChange,
  placeholder = "Search...",
  onSearchChange,
  isLoading,
  onBlur,
  dropdownPosition = "left",
}: ComboboxProps<any>) => {
  const { ref: inputRef, focused: inputFocused } = useFocusWithin();
  const { ref: popoverRef, focused: popoverFocused } = useFocusWithin();
  const [search, setSearch] = useState<string>("");

  const debouncedSearch = useDebouncedCallback(onSearchChange, 300);

  const isFocused = inputFocused || popoverFocused;

  if (value) {
    return (
      <div className="font-body-sm bg-lightest-gray-30 flex w-fit items-center gap-2 rounded-md px-2.5 py-2">
        {value.label}
        <XMarkIcon
          className="text-medium-gray hover:text-danger h-3 w-3 cursor-pointer transition-colors"
          onClick={() => {
            onChange(null);
          }}
        />
      </div>
    );
  }

  return (
    <div ref={inputRef}>
      <div className="relative">
        <TextInput
          kind="outline"
          placeholder={placeholder}
          value={search}
          onChange={(value) => {
            setSearch(value);
            debouncedSearch(value);
          }}
        />
        {isLoading && (
          <div className="absolute top-1/2 right-3 -translate-y-1/2">
            <Loader size="sm" />
          </div>
        )}
      </div>
      <Popover.Root
        open={(options.length > 0 || search.length > 0) && isFocused}
      >
        <Popover.Anchor>
          <div className="h-0 w-0" />
        </Popover.Anchor>
        <Popover.Content
          onOpenAutoFocus={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          ref={popoverRef}
          align={dropdownPosition === "left" ? "start" : "end"}
          className={classNames(
            "w-content bg-surface w-fit rounded-lg py-1 shadow-xl focus:outline-hidden"
          )}
        >
          <Command shouldFilter={false}>
            <CommandEmpty>
              {search.length === 0 && options.length === 0 && (
                <div className="text-medium-gray font-eyebrow px-4 py-2 text-left whitespace-nowrap">
                  No options found
                </div>
              )}
            </CommandEmpty>
            <CommandGroup className="scrollbar-hidden max-h-60 max-w-[450px] overflow-scroll">
              {options.map((option) => {
                return (
                  <CommandItem
                    key={option.value}
                    className={classnames(
                      "relative rounded-md py-1.5 select-none",
                      {
                        "hover:bg-slate aria-selected:bg-slate cursor-pointer":
                          !option.disabled,
                        "text-medium-gray cursor-not-allowed opacity-50":
                          option.disabled,
                      }
                    )}
                    disabled={option.disabled}
                    onSelect={() => {
                      onChange(option);
                    }}
                  >
                    <div className="flex flex-col items-start">
                      {option.label}
                      <div className="text-medium-gray">{option.detail}</div>
                    </div>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </Command>
        </Popover.Content>
      </Popover.Root>
    </div>
  );
};

export default AsyncCombobox;
