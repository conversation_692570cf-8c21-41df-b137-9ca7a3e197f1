import { NextApiRequest, NextApiResponse } from "next";

import getAuthUser from "apiUtils/getAuthUser";

import { StatusCodes } from "http-status-codes";
import jwt from "jsonwebtoken";
import { db, PRISMA_CACHE_TAGS } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const authUser = await getAuthUser({ req, res });

  if (!authUser) {
    return res
      .status(StatusCodes.NOT_FOUND)
      .send({ errors: [{ message: "User not found" }] });
  }

  const user = await db.user.findFirstOrThrow({
    where: {
      id: authUser.id,
    },
    cacheStrategy: {
      ttl:
        // If the user was created in the last 24 hours, cache for 0 seconds otherwise cache for 24 hours
        authUser.createdAt > new Date(Date.now() - 24 * 60 * 60 * 1000)
          ? 0
          : 24 * 60 * 60,
      tags: [PRISMA_CACHE_TAGS.findFirstOrThrow_authUser],
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      updatedAt: true,
      createdAt: true,
      referralSource: true,
      userCompanies: {
        select: {
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
    },
  });

  const agency =
    user.userCompanies.length > 0 ? user.userCompanies[0].company : null;

  const intercomUserJwt = jwt.sign(
    {
      user_id: user.id,
      email: user.email,
    },
    process.env.INTERCOM_VERIFICATION_TOKEN as string
  );

  /******** IMPORTANT ********
   * This endpoint is used to query the current user
   * from Supabase and pass that to the frontend
   * for that reason DO NOT pass any API keys as they
   * should never be exposed to the frontend
   */
  const cleanedUser = {
    id: user.id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    intercomUserJwt,
    referralSource: user.referralSource,
    updatedAt: user.updatedAt,
    createdAt: user.createdAt,
    agency: agency
      ? {
          id: agency.id,
          name: agency.name,
        }
      : null,
  };

  res.status(StatusCodes.OK).send({ data: cleanedUser });
};

export default handler;
