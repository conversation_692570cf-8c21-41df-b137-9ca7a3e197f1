import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForInstagram = async (post: {
  id: string;
  instagramContent: {
    id: string;
  } | null;
  workspaceID: string;
}): Promise<{
  assets: AssetType[];
  coverPhoto: AssetType | null;
}> => {
  const { instagramContent } = post;
  if (!instagramContent) {
    return { assets: [], coverPhoto: null };
  }

  const assetsMap = await getAssetsForFilePaths([
    {
      id: instagramContent.id,
      path: `${post.workspaceID}/${post.id}/instagram/${instagramContent.id}`,
    },
    {
      id: "cover_photo",
      path: `${post.workspaceID}/${post.id}/instagram/${instagramContent.id}/cover_photo`,
    },
  ]);

  let assets = assetsMap[instagramContent.id];
  let coverPhoto = assetsMap["cover_photo"][0];

  return {
    assets,
    coverPhoto,
  };
};

export default getStorageAssetsForInstagram;
