import classNames from "classnames";
import { AnimatePresence, EasingDefinition, motion } from "framer-motion";
import React, { useEffect, useRef, useState } from "react";

interface AnimateChangeInHeightProps {
  children: React.ReactNode;
  className?: string;
  transitionDuration?: number;
  transitionEase?: EasingDefinition;
}

// https://github.com/framer/motion/discussions/1884#discussioncomment-5861808
const AnimateChangeInHeight: React.FC<AnimateChangeInHeightProps> = ({
  children,
  className,
  transitionDuration = 0.1,
  transitionEase = "easeInOut",
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [height, setHeight] = useState<number | "auto">("auto");

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        // We only have one entry, so we can use entries[0].
        const observedHeight = entries[0].contentRect.height;
        setHeight(observedHeight);
      });

      resizeObserver.observe(containerRef.current);

      return () => {
        // Cleanup the observer when the component is unmounted
        resizeObserver.disconnect();
      };
    }
  }, []);

  return (
    <AnimatePresence>
      <motion.div
        className={classNames(className, "overflow-clip")}
        style={{ height }}
        animate={{ height }}
        transition={{ duration: transitionDuration, ease: transitionEase }}
      >
        <div ref={containerRef}>{children}</div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AnimateChangeInHeight;
