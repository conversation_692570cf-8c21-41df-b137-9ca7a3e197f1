import { ArrowsRightLeftIcon, PlusIcon } from "@heroicons/react/24/outline";
import { Channel, PostStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import classNames from "classnames";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { Divider, Tooltip } from "~/components";
import ButtonGroup from "~/components/ButtonGroup";
import Button from "~/components/ui/Button";
import { EDITOR_CHANNELS } from "~/constants";
import { AssetType } from "~/types/Asset";
import { handleTrpcError, trpc } from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import ChannelIcon from "../ChannelIcon";
import ChannelSelect from "./ChannelSelect";
import OtherChannelPost from "./OtherChannelPost";
import { ScheduledPost } from "./SchedulePostAccountSelect";
import SyncContentModal from "./SyncContentModal";
import DiscordPost, {
  DiscordContent,
  DiscordWebhook,
} from "./discord/DiscordPost";
import FacebookPost, {
  FacebookContent,
  FacebookIntegration,
} from "./facebook/FacebookPost";
import type {
  InstagramAsset,
  InstagramContent,
  InstagramIntegration,
  InstagramPost as InstagramPostType,
} from "./instagram/InstagramPost";
import InstagramPost from "./instagram/InstagramPost";
import LinkedInPost, {
  LinkedInAssetsType,
  LinkedInContent,
  LinkedInIntegration,
} from "./linkedin/LinkedInPost";
import SlackPost, { SlackContent, Slackintegration } from "./slack/SlackPost";
import ThreadsPost, {
  ThreadsContent,
  ThreadsIntegration,
} from "./threads/ThreadsPost";
import TikTokPost, {
  TikTokContent,
  TikTokIntegration,
} from "./tiktok/TikTokPost";
import TweetThread, {
  TweetAsset,
  TweetContent,
  TwitterIntegrationAccount,
} from "./twitter/TweetThread";
import WebflowPost, {
  WebflowContent,
  WebflowIntegration,
} from "./webflow/WebflowPost";
import YouTubeShortsPost, {
  YouTubeIntegration,
  YouTubeShortsContent,
} from "./youtube_shorts/YouTubeShortsPost";

type Props = {
  post: {
    id: string;
    title: string;
    status: PostStatus;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    otherChannelEditorState: JSONContent | null;
    scheduledPosts: ScheduledPost[];
    discordAssets: AssetType[];
    discordContent: DiscordContent | null;
    discordWebhook: DiscordWebhook | null;
    instagramIntegration: InstagramIntegration | null;
    instagramContent: InstagramContent | null;
    instagramAssets: {
      assets: InstagramAsset[];
      coverPhoto: AssetType | null;
    };
    instagramPost: InstagramPostType | null;
    instagramLink: string | null;
    facebookAssets: { assets: AssetType[]; coverPhoto: AssetType | null };
    facebookContent: FacebookContent | null;
    facebookIntegration: FacebookIntegration | null;
    facebookLink: string | null;
    linkedInAssets: LinkedInAssetsType;
    linkedInContent: LinkedInContent | null;
    linkedInIntegration: LinkedInIntegration | null;
    linkedInPost: {
      id: string;
      publishedAt: Date | null;
      stats: {
        likeCount: number;
        commentCount: number;
        shareCount: number;
        impressionCount: number | null;
        impressionStatRecordedAt: Date | null;
      };
    } | null;
    linkedInLink: string | null;
    twitterIntegrationID: string | null;
    twitterIntegration: TwitterIntegrationAccount | null;
    tweetAssets: { [tweetID: string]: TweetAsset[] };
    tweets: TweetContent[];
    twitterLink: string | null;
    tikTokContent: TikTokContent | null;
    tikTokIntegration: TikTokIntegration | null;
    tikTokAssets: AssetType[];
    tikTokLink: string | null;
    threadsAssets: {
      [id: string]: AssetType[];
    };
    threadsContent: ThreadsContent[];
    threadsIntegration: ThreadsIntegration | null;
    threadsLink: string | null;
    slackAssets: AssetType[];
    slackContent: SlackContent | null;
    slackIntegration: Slackintegration | null;
    webflowAssets: { [webflowFieldID: string]: AssetType[] };
    webflowContent: WebflowContent | null;
    webflowIntegration: WebflowIntegration | null;
    youTubeShortsAssets: {
      asset: AssetType | null;
    };
    youTubeShortsContent: YouTubeShortsContent | null;
    youTubeIntegration: YouTubeIntegration | null;
    youTubeLink: string | null;
    assets: AssetType[];
  } & PostOrIdea;
  isReadOnly: boolean;
};

type SyncingState = "Syncing" | "Complete";

const CopyAndDesigns = ({ post, isReadOnly }: Props) => {
  const trpcUtils = trpc.useUtils();
  const [syncContentModalOpen, setSyncContentModalOpen] =
    useState<boolean>(false);

  const [channelSyncState, setChannelSyncState] = useState<
    Partial<Record<Channel, SyncingState>>
  >({});

  const setSyncState = (channel: Channel, state: SyncingState) => {
    setChannelSyncState((prevChannelSyncState) => {
      const newChannelSyncState = { ...prevChannelSyncState };
      newChannelSyncState[channel] = state;
      return newChannelSyncState;
    });
  };

  const [currentChannel, setChannel] = useState<Channel>(post.channels[0]);
  const [isOverflowed, setIsOverflowed] = useState<boolean>(false);

  const channelSelectRef = useRef<HTMLDivElement>(null);
  const crossPostRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isOverflowed) {
      setIsOverflowed(
        channelSelectRef.current != null &&
          crossPostRef.current != null &&
          channelSelectRef.current.getBoundingClientRect().right >
            crossPostRef.current.getBoundingClientRect().left
      );
    }
  }, [post.channels]);

  const mostRecentlyUsedQuery = trpc.post.getMostRecentlyUsed.useQuery();
  const updatePostMutation = trpc.post.update.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Failed to Update Post", error });
    },
    onSuccess: (data) => {
      if (post.isIdea) {
        trpcUtils.idea.getAll.refetch();
      } else {
        trpcUtils.post.getMany.refetch();
      }
    },
  });
  const syncTweetContentMutation =
    trpc.tweetContent.syncFromMainChannel.useMutation({
      onSuccess: () => {
        setSyncState("Twitter", "Complete");
        trpcUtils.tweetContent.get.refetch({
          postID: post.id,
        });
      },
    });
  const syncLinkedInContentMutation =
    trpc.linkedInContent.syncFromMainChannel.useMutation({
      onSuccess: () => {
        setSyncState("LinkedIn", "Complete");
        trpcUtils.linkedInContent.get.refetch({
          postID: post.id,
        });
      },
    });
  const syncInstagramContentMutation =
    trpc.instagramContent.syncFromMainChannel.useMutation({
      onSuccess: () => {
        setSyncState("Instagram", "Complete");
        trpcUtils.instagramContent.get.refetch({
          postID: post.id,
        });
      },
    });
  const syncFacebookContentMutation =
    trpc.facebookContent.syncFromMainChannel.useMutation({
      onSuccess: () => {
        setSyncState("Facebook", "Complete");
        trpcUtils.facebookContent.get.refetch({
          postID: post.id,
        });
      },
    });
  const syncTikTokContentMutation =
    trpc.tikTokContent.syncFromMainChannel.useMutation({
      onSuccess: () => {
        setSyncState("TikTok", "Complete");
        trpcUtils.tikTokContent.get.refetch({
          postID: post.id,
        });
      },
    });
  const syncThreadsContentMutation =
    trpc.threadsContent.syncFromMainChannel.useMutation({
      onSuccess: () => {
        setSyncState("Threads", "Complete");
        trpcUtils.threadsContent.get.refetch({
          postID: post.id,
        });
      },
    });
  const syncSlackContentMutation =
    trpc.slackContent.syncFromMainChannel.useMutation({
      onSuccess: () => {
        setSyncState("Slack", "Complete");
        trpcUtils.slackContent.get.refetch({
          postID: post.id,
        });
      },
    });
  const syncYouTubeShortsContentMutation =
    trpc.youtubeShortsContent.syncFromMainChannel.useMutation({
      onSuccess: () => {
        setSyncState("YouTubeShorts", "Complete");
        trpcUtils.youtubeShortsContent.get.refetch({
          postID: post.id,
        });
      },
    });
  const syncDiscordContentMutation =
    trpc.discordContent.syncFromMainChannel.useMutation({
      onSuccess: () => {
        setSyncState("Discord", "Complete");
        trpcUtils.discordContent.get.refetch({
          postID: post.id,
        });
      },
    });

  const hasEditor = EDITOR_CHANNELS.includes(currentChannel);
  const hasTwoEditorChannels =
    post.channels.filter((channel) => EDITOR_CHANNELS.includes(channel))
      .length > 1;

  useEffect(() => {
    if (!post.channels.includes(currentChannel)) {
      setChannel(post.channels[0]);
    }
  }, [post.channels]);

  const showChannel = (channel: Channel) => {
    return post.channels.includes(channel);
  };

  const mainChannel = post.channels[0];

  const updateChannels = async (newChannels: Channel[]) => {
    const removedChannels = post.channels.filter(
      (channel) => !newChannels.includes(channel)
    );
    setChannelSyncState((prevChannelSyncState) => {
      const newChannelSyncState = { ...prevChannelSyncState };
      removedChannels.forEach((channel) => {
        delete newChannelSyncState[channel];
      });

      return newChannelSyncState;
    });

    const addedChannels = newChannels.filter(
      (channel) => !post.channels.includes(channel)
    );
    syncChannels(addedChannels);

    const newChannelsInOrder = [
      ...post.channels.filter((channel) => newChannels.includes(channel)),
      ...addedChannels,
    ];

    const currentPostData = trpcUtils.post.get.getData({ id: post.id });
    const currentPost = currentPostData?.post;
    if (currentPost) {
      await trpcUtils.post.get.cancel({
        id: post.id,
      });

      trpcUtils.post.get.setData(
        { id: post.id },
        {
          post: {
            ...currentPost,
            channels: newChannelsInOrder,
          },
          isReadOnly,
        }
      );
      trpcUtils.post.getIntegrations.refetch({
        id: post.id,
      });
    }

    updatePostMutation.mutate(
      {
        id: post.id,
        channels: newChannelsInOrder,
      },
      {
        onSuccess: () => {
          trpcUtils.post.getMany.invalidate();
          trpcUtils.post.getIntegrations.refetch({
            id: post.id,
          });
        },
      }
    );
  };

  const clearSyncingChannel = (channel: Channel) => {
    setChannelSyncState((prevChannelSyncState) => {
      const newChannelSyncState = { ...prevChannelSyncState };
      delete newChannelSyncState[channel];
      return newChannelSyncState;
    });
  };

  const syncChannels = (channels: Channel[]) => {
    const syncableChannels = channels.filter((channel) =>
      EDITOR_CHANNELS.includes(channel)
    );

    if (syncableChannels.length > 0) {
      setChannelSyncState((prevChannelSyncState) => {
        const newChannelSyncState = { ...prevChannelSyncState };
        syncableChannels.forEach((channel) => {
          newChannelSyncState[channel] = "Syncing";
        });
        return newChannelSyncState;
      });

      if (syncableChannels.includes("Facebook")) {
        syncFacebookContentMutation.mutate({ postID: post.id });
      }
      if (syncableChannels.includes("Twitter")) {
        syncTweetContentMutation.mutate({ postID: post.id });
      }
      if (syncableChannels.includes("Instagram")) {
        syncInstagramContentMutation.mutate({ postID: post.id });
      }
      if (syncableChannels.includes("LinkedIn")) {
        syncLinkedInContentMutation.mutate({ postID: post.id });
      }
      if (syncableChannels.includes("Discord")) {
        syncDiscordContentMutation.mutate({ postID: post.id });
      }
      if (syncableChannels.includes("TikTok")) {
        syncTikTokContentMutation.mutate({ postID: post.id });
      }
      if (syncableChannels.includes("Slack")) {
        syncSlackContentMutation.mutate({ postID: post.id });
      }
      if (syncableChannels.includes("Threads")) {
        syncThreadsContentMutation.mutate({ postID: post.id });
      }
      if (syncableChannels.includes("YouTubeShorts")) {
        syncYouTubeShortsContentMutation.mutate({ postID: post.id });
      }
    }
  };

  return (
    <div>
      <div className="relative mb-4 flex w-full flex-col items-start gap-2">
        <div ref={channelSelectRef} className="w-fit">
          <ButtonGroup
            kind="hollow"
            buttons={post.channels.map((channel) => ({
              key: channel,
              label: (
                <ChannelIcon
                  key={channel}
                  channel={channel}
                  width={16}
                  showLabel={!isOverflowed}
                />
              ),
              isLoading: channelSyncState[channel] === "Syncing",
              onClick: () => {
                if (post.channels.includes(channel)) {
                  setChannel(channel);
                }
              },
              selected: channel === currentChannel,
            }))}
          />
        </div>
        {!isReadOnly && (
          <div
            ref={crossPostRef}
            className="flex h-5 items-center gap-1 bg-white md:absolute md:top-0 md:right-0"
          >
            <ChannelSelect
              triggerButton={
                <div className="translate-y-0.5">
                  <Button size="sm" variant="ghost">
                    <PlusIcon className="h-4 w-4" />
                    Cross-Post
                  </Button>
                </div>
              }
              disabledChannel={post.channels.length === 1 ? mainChannel : null}
              value={post.channels}
              isMulti={true}
              recentlyUsedChannels={
                mostRecentlyUsedQuery.data?.recentlyUsedChannels
              }
              onChange={async (newChannels) => {
                await updateChannels(newChannels);
              }}
            />
            <Divider orientation="vertical" />
            <Tooltip
              value={
                hasTwoEditorChannels
                  ? null
                  : "Add a cross-post channel to sync content across channels."
              }
            >
              <Button
                size="sm"
                variant="ghost"
                disabled={!hasTwoEditorChannels}
                onClick={() => setSyncContentModalOpen(true)}
              >
                <ArrowsRightLeftIcon className="h-4 w-4" />
                Sync Content
              </Button>
            </Tooltip>
          </div>
        )}
        <SyncContentModal
          open={syncContentModalOpen}
          setOpen={setSyncContentModalOpen}
          post={post}
          defaultSourceChannel={currentChannel}
        />
      </div>
      <div key="preview-wrapper" className="w-full">
        {showChannel("LinkedIn") && (
          <motion.div
            key="LinkedIn"
            className={classNames({
              hidden: currentChannel !== "LinkedIn",
            })}
          >
            <LinkedInPost
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.LinkedIn === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("LinkedIn")}
            />
          </motion.div>
        )}
        {showChannel("Discord") && (
          <motion.div
            key="Discord"
            className={classNames({
              hidden: currentChannel !== "Discord",
            })}
          >
            <DiscordPost
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Discord === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Discord")}
            />
          </motion.div>
        )}
        {showChannel("Twitter") && (
          <motion.div
            key="Twitter"
            className={classNames({
              hidden: currentChannel !== "Twitter",
            })}
          >
            <TweetThread
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Twitter === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Twitter")}
            />
          </motion.div>
        )}
        {showChannel("Instagram") && (
          <motion.div
            key="Instagram"
            className={classNames({
              hidden: currentChannel !== "Instagram",
            })}
          >
            <InstagramPost
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Instagram === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Instagram")}
            />
          </motion.div>
        )}
        {showChannel("Facebook") && (
          <motion.div
            key="Facebook"
            className={classNames({
              hidden: currentChannel !== "Facebook",
            })}
          >
            <FacebookPost
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Facebook === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Facebook")}
            />
          </motion.div>
        )}
        {showChannel("TikTok") && (
          <motion.div
            key="TikTok"
            className={classNames({
              hidden: currentChannel !== "TikTok",
            })}
          >
            <TikTokPost
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.TikTok === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("TikTok")}
            />
          </motion.div>
        )}
        {showChannel("Threads") && (
          <motion.div
            key="Threads"
            className={classNames({
              hidden: currentChannel !== "Threads",
            })}
          >
            <ThreadsPost
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Threads === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Threads")}
            />
          </motion.div>
        )}
        {showChannel("Slack") && (
          <motion.div
            key="Slack"
            className={classNames({
              hidden: currentChannel !== "Slack",
            })}
          >
            <SlackPost
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Slack === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Slack")}
            />
          </motion.div>
        )}
        {showChannel("YouTubeShorts") && (
          <motion.div
            key="YouTubeShorts"
            className={classNames({
              hidden: currentChannel !== "YouTubeShorts",
            })}
          >
            <YouTubeShortsPost
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.YouTubeShorts === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("YouTubeShorts")}
            />
          </motion.div>
        )}
        {showChannel("Webflow") && (
          <motion.div
            key="Webflow"
            className={classNames({
              hidden: currentChannel !== "Webflow",
            })}
          >
            <WebflowPost post={post} isReadOnly={isReadOnly} />
          </motion.div>
        )}
      </div>
      {!hasEditor && <OtherChannelPost post={post} isReadOnly={isReadOnly} />}
    </div>
  );
};

export default CopyAndDesigns;
