import { Extension } from "@tiptap/core";
import classNames from "classnames";
import { Plug<PERSON>, Plugin<PERSON>ey } from "prosemirror-state";
import { Decoration, DecorationSet } from "prosemirror-view";

type AutoMentionOptions = {
  baseUrl: string | null;
  regex: RegExp;
  className?: string;
};

const AutoMention = Extension.create<AutoMentionOptions>({
  name: "autoMention",

  addProseMirrorPlugins() {
    const baseUrl = this.options.baseUrl;
    const textLimit = this.editor.storage?.characterCount?.limit;
    const className = this.options.className || "text-secondary";

    return [
      new Plugin({
        key: new PluginKey("autoMention"),
        props: {
          decorations: (state) => {
            const { doc } = state;
            const decorations: Decoration[] = [];

            doc.descendants((node, pos) => {
              if (!node.isText) return;

              const text = node.text || "";
              let match;

              while ((match = this.options.regex.exec(text)) !== null) {
                const start = pos + match.index;
                const mention = match[0];
                const end = start + mention.length;
                const isOverLimit = end > (textLimit || Infinity) + 1; // +1 for the # symbol

                decorations.push(
                  Decoration.inline(start, end, {
                    class: classNames(className, {
                      "px-0.5 rounded-sm": !isOverLimit,
                      "cursor-pointer": baseUrl,
                    }),
                    onclick: baseUrl
                      ? `window.open('${baseUrl}${mention.substring(
                          1
                        )}', '_blank')`
                      : undefined,
                  })
                );
              }
            });

            return DecorationSet.create(doc, decorations);
          },
        },
      }),
    ];
  },
});

export default AutoMention;
