import { TRPCError } from "@trpc/server";
import catchZenstackDeleteError from "apiUtils/catchZenstackDeleteError";
import decrypt from "apiUtils/decrypt";
import removeSubscriptionItem from "apiUtils/stripe/removeSubscriptionItem";
import { TwitterApi } from "twitter-api-v2";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const disconnectTwitterIntegration = async ({
  integrationID,
  workspaceID,
  userID,
}: {
  integrationID: string;
  workspaceID: string;
  userID: string;
}) => {
  const twitterIntegration = await db.twitterIntegration.findFirst({
    where: {
      id: integrationID,
      workspaceID,
    },
    select: {
      id: true,
      accessToken: true,
      accessSecret: true,
      refreshToken: true,
      account: {
        select: {
          username: true,
        },
      },
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
    },
  });

  if (!twitterIntegration) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "No integration found",
    });
  } else {
    const workspace = twitterIntegration.workspace;

    const twitter = new TwitterApi({
      clientId: process.env.NEXT_PUBLIC_TWITTER_CLIENT_ID as string,
    });

    await twitter.revokeOAuth2Token(
      decrypt(twitterIntegration.accessToken),
      "access_token"
    );
    await twitter.revokeOAuth2Token(
      decrypt(twitterIntegration.refreshToken),
      "refresh_token"
    );

    const postsToUpdate = await db.post.findMany({
      where: {
        twitterIntegrationID: twitterIntegration.id,
        scheduledPosts: {
          some: {
            status: "Scheduled",
            channel: "Twitter",
            publishAt: {
              gt: new Date(),
            },
          },
        },
      },
      select: {
        id: true,
      },
    });
    const postIDs = postsToUpdate.map((post) => post.id);

    // Delete all engagements for the future scheduled posts
    await db.twitterPostEngagement.deleteMany({
      where: {
        integrationID: twitterIntegration.id,
        content: {
          post: {
            id: {
              in: postIDs,
            },
          },
        },
      },
    });

    // Cancel all scheduled posts for just Twitter
    await db.scheduledPost.updateMany({
      where: {
        channel: "Twitter",
        status: "Scheduled",
        publishAt: {
          gt: new Date(),
        },
        post: {
          id: {
            in: postIDs,
          },
        },
      },
      data: {
        status: "Cancelled",
      },
    });

    const updatedPosts = await db.post.findMany({
      where: {
        id: {
          in: postIDs,
        },
      },
      select: {
        id: true,
        _count: {
          select: {
            scheduledPosts: {
              where: {
                status: "Scheduled",
              },
            },
          },
        },
      },
    });

    const postsWithoutScheduledPosts = updatedPosts.filter(
      (post) => post._count.scheduledPosts === 0
    );
    await db.post.updateMany({
      where: {
        id: {
          in: postsWithoutScheduledPosts.map((post) => post.id),
        },
      },
      data: {
        status: "Finalized",
      },
    });

    const postDefaults = await db.postDefault.findFirst({
      where: {
        workspaceID: workspaceID,
      },
      select: {
        twitterIntegrationID: true,
        workspace: {
          select: {
            twitterIntegrations: {
              where: {
                id: {
                  not: twitterIntegration.id,
                },
              },
              select: {
                id: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    if (postDefaults?.twitterIntegrationID === twitterIntegration.id) {
      const oldestTwitterIntegration =
        postDefaults.workspace.twitterIntegrations.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
        )[0];

      await db.postDefault.update({
        where: {
          workspaceID: workspaceID,
        },
        data: {
          twitterIntegrationID:
            oldestTwitterIntegration != null
              ? oldestTwitterIntegration.id
              : null,
        },
      });
    }

    await catchZenstackDeleteError(async () => {
      await db.twitterIntegration.update({
        where: {
          id: twitterIntegration.id,
        },
        data: {
          deletedTimeStamp: Math.floor(new Date().getTime() / 1000),
          deletedByID: userID,
        },
      });
    });

    await removeSubscriptionItem({
      workspaceID,
      itemName: "ADDITIONAL_SOCIAL_PROFILE",
      quantity: 1,
    });

    posthog.capture({
      distinctId: userID,
      event: POSTHOG_EVENTS.socialAccount.disconnected,
      properties: {
        channel: "Twitter",
      },
      groups: {
        workspace: workspaceID,
        company: workspace.companyID,
      },
    });

    return twitterIntegration.account;
  }
};

export default disconnectTwitterIntegration;
