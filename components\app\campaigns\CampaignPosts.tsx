import { useEffect, useState } from "react";

import sortBy from "lodash/sortBy";
import { useRouter } from "next/router";
import { dateFromUTC, getDateUTC, getPostPublishAt, trpc } from "~/utils";

import { MinusIcon, PlusIcon } from "@heroicons/react/24/outline";
import { Channel, PostStatus } from "@prisma/client";
import { DateTime } from "luxon";
import { toast } from "sonner";
import { Badge, DatePicker, Divider, Loader } from "~/components";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { CHANNEL_ICON_MAP } from "~/types";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import ChannelIcon from "../ChannelIcon";
import { NewPostModal, StatusBadge } from "../posts";

type Post = {
  id: string;
  status: PostStatus;
  title: string;
  channels: Channel[];
} & PostOrIdea;

type Props = {
  campaignID: string;
  posts: Post[];
  isReadOnly: boolean;
};

const CampaignPosts = ({
  campaignID,
  posts: initialPosts,
  isReadOnly,
}: Props) => {
  const [posts, setPosts] = useState<Post[]>(initialPosts);

  const router = useRouter();
  const [newPostModalOpen, setNewPostModalOpen] = useState<boolean>(false);

  const postOptionsQuery = trpc.post.getMany.useQuery();
  const postUpateMutation = trpc.post.update.useMutation();

  useEffect(() => {
    setPosts(initialPosts);
  }, [initialPosts]);

  const linkPostMutation = trpc.campaign.linkPost.useMutation({
    onSuccess: (data) => {
      postOptionsQuery.refetch();
      toast.success("Post Linked to Campaign");
      setPosts(data.posts);
    },
  });

  const postOptions =
    postOptionsQuery.data?.posts.filter((post) => post.campaignID == null) ||
    [];

  return (
    <div className="mt-4">
      <div className="mb-2 flex items-center justify-between">
        <div className="h4">Campaign Posts</div>
        {!isReadOnly && (
          <DropdownMenu.Root>
            <DropdownMenu.Trigger disabled={linkPostMutation.isPending}>
              <div
                className={
                  "bg-basic hover:bg-basic/80 disabled:opacity/80 inline-flex rounded-md p-1 font-medium text-white transition-all"
                }
              >
                {linkPostMutation.isPending ? (
                  <Loader size="md" color="white" />
                ) : (
                  <PlusIcon className="h-6 w-6" />
                )}
              </div>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content position="bottom-left">
              <DropdownMenu.Group>
                <DropdownMenu.Item
                  icon="PlusIcon"
                  onClick={() => setNewPostModalOpen(true)}
                >
                  Create New Post
                </DropdownMenu.Item>
                <DropdownMenu.Sub>
                  <DropdownMenu.SubTrigger icon="LinkIcon">
                    Link Existing Post
                  </DropdownMenu.SubTrigger>
                  <DropdownMenu.Portal>
                    <DropdownMenu.SubContent>
                      {postOptions.map((post) => {
                        return (
                          <DropdownMenu.Item
                            onClick={() =>
                              linkPostMutation.mutate({
                                id: campaignID,
                                postID: post.id,
                              })
                            }
                          >
                            {post.title}
                          </DropdownMenu.Item>
                        );
                      })}
                      <DropdownMenu.Item icon="MinusIcon">
                        Email
                      </DropdownMenu.Item>
                    </DropdownMenu.SubContent>
                  </DropdownMenu.Portal>
                </DropdownMenu.Sub>
              </DropdownMenu.Group>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        )}
      </div>
      <Divider padding="none" />
      <div>
        {!posts ? (
          <div className="flex justify-center">
            <Loader size="md" />
          </div>
        ) : (
          <div>
            {posts.length === 0 ? (
              <div className="text-medium-gray mt-4 text-sm">
                There are currently no posts associated with the campaign. Add
                one to get started
              </div>
            ) : (
              <div>
                {sortBy(posts, "publishDate").map((post) => {
                  return (
                    <div
                      onClick={(e) => {
                        const isCalendarInput = e.target
                          // @ts-ignore
                          .getAttribute("data-form-type")
                          ?.includes("date");

                        const isCalendarDay =
                          // @ts-ignore
                          e.target.getAttribute("role") === "option";

                        if (isCalendarInput || isCalendarDay) {
                          return;
                        } else {
                          if (post.isIdea) {
                            router.push(`/ideas/${post.id}`);
                          } else {
                            router.push(`/posts/${post.id}`);
                          }
                        }
                      }}
                      key={post.id}
                      className="border-light-gray cursor-pointer border-b text-sm"
                    >
                      <div className="hover:bg-lightest-gray-30 flex items-center justify-between rounded-md py-2 transition-colors">
                        <div className="flex items-center gap-8">
                          <div className="flex w-52 items-center gap-8">
                            <div className="w-20">
                              {!post.isIdea ? (
                                <DatePicker
                                  value={dateFromUTC(post.publishDate)}
                                  showBorder={false}
                                  dateFormat="ccc, MM/dd/yyyy"
                                  onChange={(date) => {
                                    const startOfDay = DateTime.fromJSDate(date)
                                      .startOf("day")
                                      .toJSDate();

                                    const publishDate = getDateUTC(startOfDay);
                                    const publishAt = post.publishAt
                                      ? getPostPublishAt(
                                          startOfDay,
                                          post.publishAt
                                        )
                                      : null;

                                    postUpateMutation.mutate({
                                      id: post.id,
                                      publishDate,
                                      publishAt,
                                    });
                                    setPosts((posts) => {
                                      return posts.map((p) => {
                                        if (
                                          p.id === post.id &&
                                          p.isIdea == false
                                        ) {
                                          return {
                                            ...p,
                                            publishDate,
                                            publishAt: publishAt || null,
                                          };
                                        } else {
                                          return p;
                                        }
                                      });
                                    });
                                  }}
                                />
                              ) : (
                                <MinusIcon className="h-4 w-4" />
                              )}
                            </div>
                            <StatusBadge
                              kind="badge"
                              status={post.isIdea ? "Idea" : post.status}
                            />
                          </div>
                          <div>{post.title}</div>
                        </div>
                        <div className="">
                          {post.channels.map((channel) => {
                            return (
                              <Badge
                                key={channel}
                                icon={
                                  <ChannelIcon channel={channel} width={16} />
                                }
                                intent="none"
                                kind="solid"
                                isUppercase={true}
                                label={CHANNEL_ICON_MAP[channel].label}
                              />
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </div>
      <NewPostModal
        open={newPostModalOpen}
        setOpen={setNewPostModalOpen}
        onCreateSuccess={(post) => {
          setPosts([...posts, post]);
          toast.success("Post Created", {
            id: post.id,
            description: "Click to view the post",
            action: {
              label: "View Post",
              onClick: () => router.push(`/posts/${post.id}`),
            },
          });
        }}
        defaultCampaignID={campaignID}
      />
    </div>
  );
};

export default CampaignPosts;
