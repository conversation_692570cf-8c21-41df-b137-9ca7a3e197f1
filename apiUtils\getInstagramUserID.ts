import axios from "axios";

const getInstagramUserID = async (username: string): Promise<string | null> => {
  let config = {
    method: "get",
    maxBodyLength: Infinity,
    url: `https://www.instagram.com/${username}/`,
    headers: {
      Cookie:
        "csrftoken=gY378Uqgy4vvkUDCBTuv6HIacLeXMsAl; ig_did=4C0BD0FA-BE79-4858-82E6-3B6A4B9B7024; ig_nrcb=1; mid=ZKyhTQAEAAGqIkfEqnOn-PZq-NVQ",
    },
  };

  const response = await axios.request(config);

  const html = response.data;

  const userIDRegex = /profilePage_(\d+)/;

  const userID = html.match(userIDRegex)?.[1];

  return userID;
};

export default getInstagramUserID;
