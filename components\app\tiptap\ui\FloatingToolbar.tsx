import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Editor } from "@tiptap/react";
import { ReactNode, useCallback, useRef } from "react";

const isHeadingLevelDisabled = (editor: Editor, level: number) => {
  try {
    return (
      !editor.schema.nodes.heading ||
      !editor.extensionManager.extensions.find(
        (ext) => ext.name === "heading" && ext.options?.levels?.includes(level)
      )
    );
  } catch (error) {
    return false;
  }
};

type FloatingToolbarProps = {
  editor: Editor | null;
  shouldHideWithLink?: boolean;
  children:
    | ReactNode
    | ((props: { onHide: (callback: () => void) => void }) => ReactNode);
};

const FloatingToolbar = ({
  editor,
  shouldHideWithLink,
  children,
}: FloatingToolbarProps) => {
  const hideCallbacks = useRef<Set<() => void>>(new Set());

  const registerHideCallback = useCallback((callback: () => void) => {
    hideCallbacks.current.add(callback);
    return () => {
      hideCallbacks.current.delete(callback);
    };
  }, []);

  if (!editor) {
    return null;
  }

  return (
    <BubbleMenu
      editor={editor}
      shouldShow={({ editor }) => {
        const selection = editor.state.selection;
        const isNodeSelection = selection.constructor.name === "NodeSelection";

        // Only show for text selections e.g. this ignores image selections
        if (isNodeSelection) {
          return false;
        }

        if (shouldHideWithLink) {
          const selectedContent = selection.content();
          const hasLink = selectedContent?.content.content.some((node) =>
            node.content.content.some((n) =>
              n.marks?.some((m) => m.type.name === "link")
            )
          );

          if (hasLink) {
            return false;
          }
        }

        const shouldShow = !editor.state.selection.empty;
        if (!shouldShow) {
          hideCallbacks.current.forEach((callback) => callback());
        }
        return shouldShow;
      }}
      updateDelay={100}
    >
      <div
        onMouseDown={(e) => e.preventDefault()}
        className="relative flex h-9 items-center gap-1 rounded-lg bg-white px-2 py-2 shadow-xl"
      >
        {typeof children === "function"
          ? children({ onHide: registerHideCallback })
          : children}
      </div>
    </BubbleMenu>
  );
};

export default FloatingToolbar;
