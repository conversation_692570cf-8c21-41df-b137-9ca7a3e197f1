import { db } from "~/clients";
import Threads from "~/clients/facebook/Threads";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";

const publishThreadsContainer = async ({
  post,
  containerID,
  threadPosition,
}: {
  post: {
    id: string;
    workspace: {
      id: string;
    };
    threadsIntegration: {
      accessToken: string;
      account: {
        threadsUserID: string;
      };
    };
  };
  containerID: string;
  threadPosition: number;
}): Promise<ScheduledPostResult> => {
  console.log(`Post:${post.id}: Publishing Threads Containers ${containerID}`);

  const threadNumber = threadPosition + 1;

  const threads = new Threads({
    accessToken: post.threadsIntegration.accessToken,
    threadsUserID: post.threadsIntegration.account.threadsUserID,
  });

  console.log(
    `Post:${post.id} Publishing Thread: #${threadNumber} Container:${containerID}`
  );
  const publishResponse = await threads.publishContainer({
    containerID,
  });

  if (publishResponse.data == null) {
    const responseError = publishResponse.errors.join(", ");
    const error = `Post #${threadNumber} in Thread: ${responseError}`;
    console.log(`Post:${post.id} Publishing Error ${error}`);
    const isAuthError = publishResponse.isAuthError;

    return {
      status: isAuthError ? "NonRetriableError" : "Error",
      error,
      rawError: publishResponse.rawError,
      isAuthError,
    };
  } else {
    const mediaID = publishResponse.data.mediaID;
    const mediaResponse = await threads.getThreadsPost({ mediaID });
    if (mediaResponse.data == null) {
      const responseError = mediaResponse.errors.join(", ");
      const error = `Thread #${threadNumber} ${responseError}`;
      console.log(`Post:${post.id} Threads Querying Media Error ${error}`);
      return {
        status: "Error",
        error: mediaResponse.errors.join(", "),
        rawError: mediaResponse.rawError,
      };
    }

    const threadsUrl = mediaResponse.data.post.permalink;
    const threadsPost = mediaResponse.data.post;

    if (threadPosition === 0) {
      await db.threadsPost.create({
        data: {
          id: threadsPost.id,
          mediaProductType: threadsPost.mediaProductType,
          mediaType: threadsPost.mediaType,
          permalink: threadsPost.permalink,
          threadsUser: {
            connect: {
              username: threadsPost.threadsUser.username,
            },
          },
          text: threadsPost.text,
          isQuotePost: threadsPost.isQuotePost,
          shortcode: threadsPost.shortcode,
          mediaUrl: threadsPost.mediaUrl,
          childrenIDs: threadsPost.childrenIDs,
          publishedAt: threadsPost.publishedAt,
        },
      });
    }

    return {
      status: "Success",
      postUrl: threadsUrl,
      socialPostID: threadsPost.id,
    };
  }
};

export default publishThreadsContainer;
