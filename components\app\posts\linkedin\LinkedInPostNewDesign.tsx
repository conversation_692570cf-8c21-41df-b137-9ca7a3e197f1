import { useEffect, useState } from "react";

import { v4 as uuidv4 } from "uuid";

import { ScheduledPost } from "../SchedulePostAccountSelect";

import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { Channel } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import Uppy from "@uppy/core";
import Tus from "@uppy/tus";
import assert from "assert";
import { uniq } from "lodash";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import posthog from "posthog-js";
import { toast } from "sonner";
import { AutosizeInput, Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import {
  MAX_VIDEO_UPLOAD_SIZE_MB,
  ONE_YEAR_IN_SECONDS,
  linkedinMentionRegex,
  urlRegex,
} from "~/constants";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import tusOptions from "~/constants/tusOptions";
import {
  usePostIntegrations,
  useThreadComments,
  useWorkspace,
} from "~/providers";
import { LinkedInIntegration } from "~/providers/PostIntegrationsProvider";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import {
  charCount,
  findAsync,
  formatPercentage,
  getImageProperties,
  getVideoProperties,
  handleFilesUpload,
  handleTrpcError,
  openIntercomArticle,
  openIntercomChat,
  trpc,
} from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import compressImage from "~/utils/compressImage";
import getAssetsWithProperties from "~/utils/getAssetsWithProperties";
import getFileProperties from "~/utils/getFileProperties";
import createRoomID from "~/utils/liveblocks/createRoomID";
import SelectModal from "../../SelectModal";
import { UploadingAsset } from "../../files/AssetsNewDesign";
import LinkedInEditorWrapperNewDesign from "../../tiptap/LinkedInEditorNewDesign";
import CoverPhotoModal from "../CoverPhotoModal";
import CustomCaptionsModal from "../CustomCaptionsModal";
import EditorActionsBar from "../EditorActionsBar";
import LinkPreview from "../LinkPreview";
import PostEngagementsNewDesign from "../PostEngagementsNewDesign";
import {
  PostOptionNewDesign,
  PostOptionsNewDesign,
} from "../PostOptionsNewDesign";
import PostPerformance from "../PostPerformance";
import LinkedInAssetPreviewNewDesign from "./LinkedInAssetPreviewNewDesign";

export type LinkedInContent = {
  id: string;
  editorState: JSONContent | null;
  copy: string;
  documentTitle: string | null;
};

export type LinkedInAssetsType = {
  assets: AssetType[];
  coverPhoto: AssetType | null;
  captions: AssetType | null;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    scheduledPosts: ScheduledPost[];
    linkedInAssets: LinkedInAssetsType;
    linkedInContent: LinkedInContent | null;
    linkedInIntegration: LinkedInIntegration | null;
    linkedInPost: {
      id: string;
      publishedAt: Date | null;
      stats: {
        likeCount: number;
        commentCount: number;
        shareCount: number;
        impressionCount: number | null;
        impressionStatRecordedAt: Date | null;
      };
    } | null;
    linkedInLink: string | null;
  } & PostOrIdea;
  isReadOnly: boolean;
  isSyncingComplete: boolean;
  clearIsSyncing: () => void;
};

const LinkedInPostNewDesign = ({
  post,
  isReadOnly,
  isSyncingComplete,
  clearIsSyncing,
}: Props) => {
  const router = useRouter();
  const { currentWorkspace } = useWorkspace();
  const trpcUtils = trpc.useUtils();
  const [isRoomReady, setIsRoomReady] = useState<boolean>(false);

  useEffect(() => {
    if (post.linkedInContent && !isSyncingComplete) {
      setIsRoomReady(true);
    }
  }, [post.linkedInContent, isSyncingComplete]);

  const [draggedOver, setDraggedOver] = useState<boolean>(false);
  const [likeSelectModalOpen, setLikeSelectModalOpen] =
    useState<boolean>(false);
  const [integration, setIntegration] = useState<LinkedInIntegration | null>(
    post.linkedInIntegration || null
  );
  const [linkedInContent, setLinkedInContent] = useState<LinkedInContent>(
    post.linkedInContent || {
      id: uuidv4(),
      editorState: null,
      copy: "",
      documentTitle: "",
    }
  );
  const [assets, setAssets] = useState<AssetType[]>(post.linkedInAssets.assets);
  const [uploadingAssets, setUploadingAssets] = useState<UploadingAsset[]>([]);
  const [coverPhotoModalOpen, setCoverPhotoModalOpen] =
    useState<boolean>(false);
  const [captionsModalOpen, setCaptionsModalOpen] = useState<boolean>(false);
  const [coverPhoto, setCoverPhoto] = useState<AssetType | null>(
    post.linkedInAssets.coverPhoto
  );
  const [captions, setCaptions] = useState<AssetType | null>(
    post.linkedInAssets.captions
  );

  const isPDF = assets?.[0]?.metadata.mimetype === "application/pdf";
  const isVideo = assets?.[0]?.metadata.mimetype.includes("video");

  const engagementsQuery = trpc.linkedInContent.getEngagements.useQuery({
    postID: post.id,
  });
  const engagements = engagementsQuery.data?.engagements || [];
  const createEngagementMutation =
    trpc.linkedInContent.createEngagement.useMutation();
  const updateEngagementMutation =
    trpc.linkedInContent.updateEngagement.useMutation();
  const deleteEngagementsMutation =
    trpc.linkedInContent.deleteEngagements.useMutation();
  const upsertEngagementLikesMutation =
    trpc.linkedInContent.upsertEngagementLikes.useMutation();
  const updateAssetMutation = trpc.linkedInContent.updateAsset.useMutation();

  const linkedInContentQuery = trpc.linkedInContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      enabled: isSyncingComplete,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );

  const linkedInContentData = linkedInContentQuery.data;
  useEffect(() => {
    const setCrossPostData = () => {
      if (linkedInContentData?.linkedInContent != null) {
        setLinkedInContent(linkedInContentData.linkedInContent);
        setAssets(linkedInContentData.assets);
        setCoverPhoto(linkedInContentData.coverPhoto);
        setCaptions(linkedInContentData.captions);
        setIsRoomReady(true);
        clearIsSyncing();
      }
    };

    setCrossPostData();
  }, [linkedInContentData]);

  const upsertLinkedInContentMutation = trpc.linkedInContent.upsert.useMutation(
    {
      onSuccess: () => {
        trpcUtils.post.get.invalidate({
          id: post.id,
        });
      },
    }
  );

  const copyWithoutMentions = linkedInContent?.copy?.replaceAll(
    linkedinMentionRegex,
    ""
  );
  const websites = uniq(copyWithoutMentions?.match(urlRegex)) || [];

  const firstWebsite = websites.length ? websites[0] : null;

  const createAssetsMutation = trpc.linkedInContent.createAssets.useMutation();
  const upsertCaptionsMutation =
    trpc.linkedInContent.upsertCaptions.useMutation();
  const deleteCaptionsMutation =
    trpc.linkedInContent.deleteCaptions.useMutation();
  const upsertCoverPhotoMutation =
    trpc.linkedInContent.upsertCoverPhoto.useMutation();
  const deleteCoverPhotoMutation =
    trpc.linkedInContent.deleteCoverPhoto.useMutation();
  const getNewAssetsMutation = trpc.asset.getForFilePath.useMutation();
  const compressVideoMutation = trpc.video.compress.useMutation();
  const maybeCompressAssets = async (
    assets: {
      path: string;
      sizeBytes: number;
      sizeMB: number;
      mimetype: string;
      signedUrl: string;
      duration?: number | null;
      width?: number | null;
      height?: number | null;
    }[]
  ) => {
    const videoPromises = assets.map(async (asset) => {
      if (!asset || !asset.mimetype.startsWith("video/")) {
        return Promise.resolve(null);
      } else {
        let duration = asset.duration;
        let width = asset.width;
        let height = asset.height;
        if (width && height && duration) {
          return asset as typeof asset & {
            duration: number;
            width: number;
            height: number;
          };
        } else {
          const properties = await getVideoProperties(asset.signedUrl);
          return {
            ...asset,
            duration: properties.duration,
            width: properties.width,
            height: properties.height,
          };
        }
      }
    });
    const videosWithProperties = await Promise.all(videoPromises);

    const videoToCompress = await findAsync(
      videosWithProperties,
      async (asset) => {
        if (!asset || !asset.mimetype.startsWith("video/")) {
          return false;
        }

        return asset.sizeMB > 125;
      }
    );

    if (videoToCompress != null) {
      const { width, height } = videoToCompress;

      compressVideoMutation.mutate(
        {
          video: {
            path: videoToCompress.path,
            resolution: {
              width,
              height,
            },
            duration: videoToCompress.duration,
            sizeBytes: videoToCompress.sizeBytes,
          },
          channel: "Instagram",
        },
        {
          onError: (error) => {
            handleTrpcError({ title: "Error Compressing Video", error });
          },
          onSuccess: (data) => {
            if (data.jobID != null) {
              toast.info("Optimizing Attachment", {
                description:
                  "We're automatically optimizing your attachment (files >125MB are not accepted on LinkedIn).",
              });
            }
          },
        }
      );
    }
  };

  const {
    connectedIntegrations,
    selectedIntegrations,
    currentChannel,
    getChannelIntegration,
  } = usePostIntegrations();
  const connectedLinkedInIntegrations = connectedIntegrations.LinkedIn || [];
  const schedulingIntegrations =
    connectedLinkedInIntegrations.filter(
      (integration) => integration.type === "Scheduling"
    ) || [];
  const engagementIntegrations = integration
    ? [
        ...schedulingIntegrations,
        ...(connectedLinkedInIntegrations.filter(
          (integration) => integration.type === "Engagement"
        ) || []),
      ]
    : [];
  const updatePostMutation = trpc.post.update.useMutation();

  useEffect(() => {
    if (selectedIntegrations.LinkedIn && connectedLinkedInIntegrations.length) {
      const selectedIntegration = connectedLinkedInIntegrations.find(
        (integration) => integration.id === selectedIntegrations.LinkedIn
      );

      if (
        selectedIntegration &&
        (!integration || integration.id !== selectedIntegration.id)
      ) {
        const linkedInIntegration = getChannelIntegration(
          "LinkedIn",
          selectedIntegration.id
        );
        if (linkedInIntegration) {
          setIntegration(linkedInIntegration);
          updatePostMutation.mutate(
            {
              id: post.id,
              linkedInIntegrationID: linkedInIntegration.id,
            },
            {
              onSuccess: () => {
                trpcUtils.post.getIntegrations.refetch({
                  id: post.id,
                });
              },
            }
          );
        }
      }
    }
  }, [selectedIntegrations, connectedLinkedInIntegrations]);

  const getValidFiles = async (files: File[]) => {
    let currentFiles = [...files];
    const validFiles: File[] = [];

    const hasPDF = files.some((file) => file.type === "application/pdf");
    const hasVideo = files.some((file) => file.type.startsWith("video/"));

    const currentAssetIsPDFVideo = assets.some(
      (asset) =>
        asset.metadata.mimetype === "application/pdf" ||
        asset.metadata.mimetype.startsWith("video/")
    );

    const uploadingAssetIsPDFVideo = uploadingAssets.some(
      (asset) =>
        asset.type.startsWith("video/") || asset.type === "application/pdf"
    );

    const currentAssetIsImage = assets.some(
      (asset) =>
        asset.metadata.mimetype.startsWith("image/") &&
        asset.metadata.mimetype !== "image/gif"
    );

    if (currentAssetIsPDFVideo || uploadingAssetIsPDFVideo) {
      toast.error("Too Many Attachments", {
        description:
          "You can add up to a total of 20 images or GIFs, 1 video, or 1 PDF per post",
      });

      return [];
    }

    if (currentAssetIsImage) {
      currentFiles = files
        .filter((file) => file.type.startsWith("image/"))
        .slice(0, 20 - assets.length);
      if (currentFiles.length !== files.length) {
        toast.error("Too Many Attachments", {
          description:
            "You can add up to a total of 20 images or GIFs, 1 video, or 1 PDF per post",
        });

        if (currentFiles.length === 0) {
          return [];
        }
      }
    }

    if (files.length > 1 && (hasPDF || hasVideo)) {
      toast.error("Too Many Attachments", {
        description:
          "You can add up to a total of 20 images or GIFs, 1 video, or 1 PDF per post",
      });

      currentFiles = [files[0]];
    }

    const assetNames = assets.map((asset) => asset.name);

    for await (const file of currentFiles) {
      const fileType = file.type;
      const fileSizeMB = file.size / 1024 / 1024;

      const fileIsGif = fileType === "image/gif";
      const fileIsVideo = fileType.startsWith("video/");
      const fileIsPDF = fileType === "application/pdf";

      if (assetNames.includes(file.name)) {
        toast.error(`Duplicate Attachment: "${file.name}"`, {
          description: "Each attachment must have a unique name",
        });
        continue;
      }

      if (["image/jpeg", "image/png"].includes(fileType)) {
        const { pixels } = await getImageProperties(file);

        // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/multiimage-post-api?view=li-lms-2023-02&tabs=http#multiimage
        if (pixels >= 36_152_320) {
          toast.error("Image is Too Large", {
            description: "An image can have a maximum of 36,152,319 pixels",
          });
          continue;
        }

        if (fileSizeMB > 5) {
          const compressedFile = await compressImage(file, {
            maxSizeMB: 5,
          });
          toast.info("Attachment Optimized", {
            description: `${file.name} was optimized (files >5MB are not accepted on LinkedIn)`,
          });
          posthog.capture(POSTHOG_EVENTS.image.compressed, {
            originalSizeMB: file.size / 1024 / 1024,
            compressedSizeKB: compressedFile.size / 1024 / 1024,
            channel: "LinkedIn",
          });
          validFiles.push(compressedFile);
        } else {
          validFiles.push(file);
        }
      } else if (fileIsGif) {
        if (fileSizeMB > 15) {
          toast.error("GIF Size is Too Big", {
            description: "GIF size must be smaller than 15MB",
          });
          continue;
        }

        validFiles.push(file);
      } else if (fileIsVideo) {
        const { duration, width, height } = await getVideoProperties(file);

        if (fileSizeMB <= 0.075) {
          toast.error("Video Size is Too Small", {
            description: "Video size must be at least 75KB",
          });
          continue;
        } else if (fileSizeMB > MAX_VIDEO_UPLOAD_SIZE_MB) {
          toast.error("Video is too large", {
            description: `Max file size is ${MAX_VIDEO_UPLOAD_SIZE_MB} MB. Contact support for help compressing your video.`,
            action: {
              label: "Contact Support",
              onClick: () => {
                openIntercomChat(
                  `I need help compressing a ${fileSizeMB.toFixed(1)} MB video.`
                );
              },
            },
          });
          continue;
        } else if (duration < 3) {
          toast.error("Video is too short", {
            description: "Minimum video length is 3 seconds",
          });
          continue;
        } else if (duration > 30 * 60) {
          toast.error("Video is too long", {
            description: "Maximum video length is 30 minutes",
          });
          continue;
        }

        validFiles.push(file);
      } else if (fileIsPDF) {
        if (fileSizeMB > 100) {
          toast.error("PDF Size is Too Big", {
            description: "PDF size must be smaller than 100MB",
          });
          continue;
        }

        validFiles.push(file);
      }
    }

    return validFiles;
  };

  const [uppy, setUppy] = useState<Uppy | null>(null);

  useEffect(() => {
    const initUppy = async () => {
      const options = await tusOptions();
      const uppyInstance = new Uppy({
        id: `linkedin-${post.id}`,
        restrictions: { maxNumberOfFiles: 20 },
        allowMultipleUploads: true,
        autoProceed: true,
      }).use(Tus, options);

      uppyInstance.on("file-added", (file) => {
        assert(currentWorkspace);

        setUploadingAssets((uploadingAssets) => [
          ...uploadingAssets,
          {
            id: file.id,
            name: file.name!,
            size: file.size!,
            type: file.type,
            uploadProgress: 0,
            updateAssetID: file.meta.updateAssetID as string | undefined,
            width: 0,
            height: 0,
          },
        ]);

        const objectName = `${currentWorkspace.id}/${post.id}/linkedin/${linkedInContent.id}/${file.name}`;

        file.meta = {
          ...file.meta,
          bucketName: "assets",
          objectName,
          contentType: file.type,
          cacheControl: `${ONE_YEAR_IN_SECONDS}`,
        };

        return file;
      });

      uppyInstance.on("upload-start", async (data) => {
        for (const file of data) {
          if (
            file.type.startsWith("image/") ||
            file.type.startsWith("video/")
          ) {
            const assetProperties = file.type.startsWith("video/")
              ? await getVideoProperties(file.data)
              : await getImageProperties(file.data);

            setUploadingAssets((uploadingAssets) => {
              const uploadingAsset = uploadingAssets.find(
                (asset) => asset.name === file.name
              );
              if (uploadingAsset) {
                return uploadingAssets.map((asset) =>
                  asset.name === file.name
                    ? {
                        ...asset,
                        width: assetProperties.width,
                        height: assetProperties.height,
                      }
                    : asset
                );
              }
              return uploadingAssets;
            });
          }
        }
      });

      uppyInstance.on("upload-progress", (file, progress) => {
        const uploadProgress = Math.round(
          (progress.bytesUploaded / progress.bytesTotal!) * 100
        );

        if (!file) {
          return;
        }

        setUploadingAssets((uploadingAssets) => {
          const uploadingAssetIndex = uploadingAssets.findIndex(
            (uploadingAsset) => uploadingAsset.name === file.name
          );
          const uploadingAsset = uploadingAssets[uploadingAssetIndex];
          if (
            !!uploadingAsset &&
            uploadProgress >= uploadingAsset.uploadProgress
          ) {
            const newUploadingAssets = [...uploadingAssets];
            newUploadingAssets[uploadingAssetIndex] = {
              ...uploadingAsset,
              uploadProgress,
            };
            return newUploadingAssets;
          } else {
            return uploadingAssets;
          }
        });
      });

      uppyInstance.on("error", (error) => {
        toast.error("Error Uploading File", {
          description: error.message,
        });
      });

      uppyInstance.on("complete", async (uppyData) => {
        const path = `${currentWorkspace!.id}/${post.id}/${
          CHANNEL_ICON_MAP["LinkedIn"].slug
        }/${linkedInContent.id}`;
        const fileNames = uppyData.successful!.map((file) => file.name!);

        const filesWithProperties = await getFileProperties(
          uppyData.successful!.map((file) => ({
            name: file.name!,
            data: file.data,
            mimetype: file.meta.type as string,
          }))
        );

        getNewAssetsMutation.mutate(
          {
            path,
            fileNames,
          },
          {
            onSuccess: async (data) => {
              const { assets } = data;
              const newAssetsWithProperties = assets.map((asset) => {
                const assetProperties = filesWithProperties.find(
                  (file) => file.name === asset.name
                );

                assert(assetProperties);

                return {
                  ...asset,
                  id: uuidv4(),
                  assetID: asset.id,
                  md5Hash: assetProperties.md5Hash,
                  width: assetProperties.width,
                  height: assetProperties.height,
                  duration: assetProperties.duration,
                };
              });

              if (uppyData.successful![0].meta.updateAssetID) {
                const updateAssetID = uppyData.successful![0].meta
                  .updateAssetID as string;
                const newAsset = newAssetsWithProperties[0];

                await updateAssetMutation.mutateAsync({
                  linkedInAssetID: updateAssetID,
                  newAsset: {
                    id: newAsset.id,
                    name: newAsset.name,
                    path: newAsset.path,
                    size: newAsset.sizeBytes,
                    eTag: newAsset.eTag,
                    md5Hash: newAsset.md5Hash,
                    mimetype: newAsset.mimetype,
                    width: newAsset.width,
                    height: newAsset.height,
                    duration: newAsset.duration,
                    url: newAsset.signedUrl,
                    urlExpiresAt: newAsset.urlExpiresAt,
                  },
                });

                setAssets((prevAssets) =>
                  prevAssets.map((asset) =>
                    asset.id === updateAssetID
                      ? {
                          id: asset.id,
                          name: newAsset.name,
                          path: newAsset.path,
                          sizeBytes: newAsset.sizeBytes,
                          eTag: newAsset.eTag,
                          md5Hash: newAsset.md5Hash,
                          mimetype: newAsset.mimetype,
                          width: newAsset.width,
                          height: newAsset.height,
                          metadata: newAsset.metadata,
                          url: newAsset.signedUrl,
                          sizeMB: newAsset.sizeMB,
                          sizeKB: newAsset.sizeKB,
                          createdAt: newAsset.createdAt,
                          updatedAt: newAsset.updatedAt,
                          duration: newAsset.duration,
                          signedUrl: newAsset.signedUrl,
                          urlExpiresAt: newAsset.urlExpiresAt,
                        }
                      : asset
                  )
                );
                setUploadingAssets([]);
              } else {
                const nextPosition = assets.length;

                createAssetsMutation.mutate(
                  {
                    id: linkedInContent.id,
                    assets: newAssetsWithProperties.map((asset, index) => ({
                      id: asset.id,
                      assetID: asset.assetID,
                      name: asset.name,
                      path: asset.path,
                      size: asset.sizeBytes,
                      eTag: asset.eTag,
                      md5Hash: asset.md5Hash,
                      mimetype: asset.mimetype,
                      width: asset.width,
                      height: asset.height,
                      duration: asset.duration,
                      url: asset.signedUrl,
                      urlExpiresAt: asset.urlExpiresAt,
                      position: nextPosition + index,
                    })),
                  },
                  {
                    onSuccess: async () => {
                      await maybeCompressAssets(newAssetsWithProperties);
                      // Set the document title if there is a PDF
                      const pdfAsset = newAssetsWithProperties.find(
                        (asset) => asset.mimetype === "application/pdf"
                      );
                      if (pdfAsset && !linkedInContent.documentTitle) {
                        const fileName = pdfAsset.name;
                        const fileNameWithoutExtension = fileName.replace(
                          /\.[^/.]+$/,
                          ""
                        );

                        setLinkedInContent((prevContent) => ({
                          ...prevContent,
                          documentTitle: fileNameWithoutExtension,
                        }));

                        upsertLinkedInContentMutation.mutate({
                          id: linkedInContent.id,
                          copy: linkedInContent.copy,
                          editorState: linkedInContent.editorState,
                          documentTitle: fileNameWithoutExtension,
                          postID: post.id,
                        });
                      }
                    },
                    onError: (error) => {
                      handleTrpcError({ title: "Error Creating Asset", error });
                    },
                  }
                );

                setUploadingAssets([]);
                setAssets((assets) => [...assets, ...newAssetsWithProperties]);
              }
            },
          }
        );
      });

      setUppy(uppyInstance);
    };

    initUppy();

    // Cleanup function
    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, [post.id, currentWorkspace, linkedInContent.id]);

  const linkedInPost = post.linkedInPost;

  const { setRoomIDs } = useThreadComments();

  const currentRoomID = createRoomID({
    workspaceID: post.workspaceID,
    contentType: "post",
    postID: post.id,
    roomContentID: linkedInContent.id,
  });

  useEffect(() => {
    if (linkedInContent.id && currentChannel === "LinkedIn") {
      setRoomIDs([currentRoomID]);
    }
  }, [linkedInContent.id, currentRoomID, setRoomIDs, currentChannel]);

  return (
    <div
      className="flex"
      // This avoids triggering the dropzone when dragging internal post content like existing images
      onDragOver={(event) => {
        if (
          !coverPhotoModalOpen &&
          event.dataTransfer.types.includes("Files") &&
          !event.dataTransfer.types.includes("text/html")
        ) {
          event.preventDefault();
          setDraggedOver(true);
        }
      }}
      onDragLeave={(event) => {
        const currentTarget = event.currentTarget;
        const relatedTarget = event.relatedTarget as Node | null;

        if (!relatedTarget || !currentTarget.contains(relatedTarget)) {
          setDraggedOver(false);
        }
      }}
      onDrop={() => {
        setDraggedOver(false);
      }}
    >
      <div className="flex w-full flex-col gap-7">
        <PostPerformance
          channel="LinkedIn"
          url={post.linkedInLink}
          stats={
            linkedInPost && [
              {
                label: "Likes",
                value: linkedInPost.stats.likeCount,
              },
              {
                label: "Comments",
                value: linkedInPost.stats.commentCount,
              },
              {
                label: "Shares",
                value: linkedInPost.stats.shareCount,
              },
              {
                label: "Impressions",
                value: (
                  <div className="flex items-center gap-1">
                    {linkedInPost.stats.impressionCount}
                    {linkedInPost.stats.impressionStatRecordedAt &&
                      linkedInPost.publishedAt &&
                      DateTime.fromJSDate(linkedInPost.publishedAt) >
                        DateTime.now().minus({ month: 1 }) &&
                      DateTime.fromJSDate(
                        linkedInPost.stats.impressionStatRecordedAt
                      ) < DateTime.now().minus({ days: 7 }) && (
                        <Tooltip
                          value={
                            <div className="space-y-1">
                              <div>
                                This impressions / engagement data was last
                                updated on{" "}
                                <span className="text-secondary">
                                  {DateTime.fromJSDate(
                                    linkedInPost.stats.impressionStatRecordedAt
                                  ).toFormat("M/d/yy")}
                                </span>
                                .
                                <br />
                                <br />
                                Since this is a recent post that is more than a
                                week out of date, we recommend rerunning the
                                bookmarklet to update this stat.
                              </div>
                              <Button
                                variant="secondary"
                                size="sm"
                                onClick={() => {
                                  router.push(
                                    "/settings/linkedin#creator-profile-analytics"
                                  );
                                }}
                              >
                                Bookmarklet Instructions
                              </Button>
                            </div>
                          }
                        >
                          <ExclamationTriangleIcon className="text-medium-dark-gray h-4 w-4" />
                        </Tooltip>
                      )}
                  </div>
                ),
              },

              {
                label: "Engagement",
                value:
                  linkedInPost.stats.impressionCount != null &&
                  linkedInPost.stats.impressionCount > 0
                    ? formatPercentage(
                        linkedInPost.stats.likeCount +
                          linkedInPost.stats.commentCount +
                          linkedInPost.stats.shareCount,
                        linkedInPost.stats.impressionCount
                      )
                    : null,
              },
            ]
          }
        />
        <div className="w-full">
          <div className="min-h-[289px]">
            {isRoomReady && (
              <LinkedInEditorWrapperNewDesign
                key={linkedInContent.id}
                postID={post.id}
                initialValue={linkedInContent.editorState}
                placeholder="Add copy and upload media for the post here"
                isReadOnly={isReadOnly}
                onImagePaste={(event) => {
                  if (uppy) {
                    handleFilesUpload({
                      items: event.clipboardData.items,
                      uppy,
                      meta: {
                        contentID: linkedInContent.id,
                      },
                      getValidFiles,
                    });
                  }
                }}
                onSave={(editorState) => {
                  setLinkedInContent((linkedInContent) => {
                    assert(linkedInContent);
                    return {
                      ...linkedInContent,
                      editorState,
                    };
                  });

                  upsertLinkedInContentMutation.mutate({
                    id: linkedInContent.id,
                    copy: linkedInContent.copy,
                    editorState: editorState,
                    documentTitle: linkedInContent.documentTitle,
                    postID: post.id,
                  });
                }}
                onUpdateContent={(copy) => {
                  if (!linkedInContent) {
                    setLinkedInContent({
                      id: uuidv4(),
                      editorState: null,
                      copy,
                      documentTitle: null,
                    });
                  } else {
                    setLinkedInContent((linkedInContent) => ({
                      ...linkedInContent,
                      copy,
                    }));
                  }
                }}
                roomID={currentRoomID}
                currentWorkspace={currentWorkspace ?? null}
                connectedIntegration={integration}
              />
            )}
            {assets.length === 0 && uploadingAssets.length === 0 && (
              <LinkPreview url={firstWebsite} channel="LinkedIn" />
            )}
            <LinkedInAssetPreviewNewDesign
              linkedInContent={linkedInContent}
              coverPhotoUrl={coverPhoto?.signedUrl}
              isReadOnly={isReadOnly}
              draggedOver={draggedOver}
              setDraggedOver={setDraggedOver}
              uploadingAssets={uploadingAssets}
              getValidFiles={getValidFiles}
              uppy={uppy}
              assets={assets}
              setAssets={setAssets}
            />
            <EditorActionsBar
              channel="LinkedIn"
              visible={true}
              isReadOnly={isReadOnly}
              characterCount={{
                max: 3000,
                current: charCount(linkedInContent.copy, {
                  regexTransformers: [
                    {
                      from: linkedinMentionRegex,
                    },
                  ],
                }),
              }}
              uploadAssetButton={{
                acceptedMimetypes: [
                  "image/jpeg",
                  "image/png",
                  "image/gif",
                  "video/mp4",
                  "video/quicktime",
                  "application/pdf",
                ],
                acceptMultiple: true,
                onUploadFiles: async (files) => {
                  if (uppy) {
                    handleFilesUpload({
                      files,
                      uppy,
                      meta: {
                        contentID: linkedInContent.id,
                      },
                      getValidFiles,
                    });
                  }
                },
                isLoading: uploadingAssets.length > 0,
              }}
              openHowToTagModal={() => {
                openIntercomArticle("linkedin_tagging");
              }}
            />
          </div>
          <div className="max-w-[622px] xl:w-[622px]">
            {(isVideo || isPDF) && (
              <PostOptionsNewDesign>
                {isVideo && (
                  <>
                    <PostOptionNewDesign title="Cover Photo">
                      <CoverPhotoModal
                        open={coverPhotoModalOpen}
                        setOpen={setCoverPhotoModalOpen}
                        contentID={linkedInContent.id}
                        video={assets[0]}
                        coverPhoto={coverPhoto}
                        getValidFiles={async (files) => {
                          const file = files[0];

                          if (!file) {
                            return [];
                          }

                          const fileSizeMB = file.size / 1024 / 1024;
                          if (fileSizeMB > 5) {
                            const compressedFile = await compressImage(file, {
                              maxSizeMB: 5,
                            });
                            toast.success("Attachment Optimized", {
                              description: `${file.name} was optimized (files >5MB are not accepted on LinkedIn)`,
                            });
                            posthog.capture(POSTHOG_EVENTS.image.compressed, {
                              originalSizeMB: file.size / 1024 / 1024,
                              compressedSizeKB:
                                compressedFile.size / 1024 / 1024,
                              channel: "LinkedIn",
                              channelPostType: "Cover Photo",
                            });
                            return [compressedFile];
                          } else {
                            return files;
                          }
                        }}
                        onUpload={(fileName, onSave) => {
                          getNewAssetsMutation.mutate(
                            {
                              path: `${currentWorkspace!.id}/${post.id}/${
                                CHANNEL_ICON_MAP["LinkedIn"].slug
                              }/${linkedInContent.id}/cover_photo`,
                              fileNames: [fileName],
                            },
                            {
                              onSuccess: async (data) => {
                                const { assets } = data;
                                const newAssetsWithProperties = (
                                  await getAssetsWithProperties(assets)
                                ).map((asset) => ({
                                  ...asset,
                                  id: uuidv4(),
                                  assetID: asset.id,
                                }));

                                const coverPhotoAsset =
                                  newAssetsWithProperties[0];

                                setCoverPhoto(coverPhotoAsset);

                                upsertCoverPhotoMutation.mutate({
                                  id: linkedInContent.id,
                                  asset: {
                                    id: coverPhotoAsset.id,
                                    name: coverPhotoAsset.name,
                                    path: coverPhotoAsset.path,
                                    size: coverPhotoAsset.sizeBytes,
                                    eTag: coverPhotoAsset.eTag,
                                    md5Hash: coverPhotoAsset.md5Hash,
                                    mimetype: coverPhotoAsset.mimetype,
                                    width: coverPhotoAsset.width,
                                    height: coverPhotoAsset.height,
                                    url: coverPhotoAsset.signedUrl,
                                    urlExpiresAt: DateTime.now()
                                      .plus({ seconds: ONE_YEAR_IN_SECONDS })
                                      .toJSDate(),
                                  },
                                });

                                onSave();
                              },
                              onError: (error) => {
                                handleTrpcError({
                                  title: "Failed to upload cover photo",
                                  error,
                                });
                              },
                            }
                          );
                        }}
                        onDelete={(asset) => {
                          setCoverPhoto(null);
                          deleteCoverPhotoMutation.mutate({
                            linkedInContentID: linkedInContent.id,
                            path: asset.path,
                          });
                        }}
                        storagePath={`${currentWorkspace?.id}/${post.id}/linkedin/${linkedInContent.id}/cover_photo`}
                      />
                    </PostOptionNewDesign>
                    <PostOptionNewDesign title="Custom Captions">
                      <CustomCaptionsModal
                        open={captionsModalOpen}
                        setOpen={setCaptionsModalOpen}
                        contentID={linkedInContent.id}
                        captions={captions}
                        onUpload={(fileName, onSave) => {
                          const path = `${currentWorkspace!.id}/${
                            post.id
                          }/linkedin/${linkedInContent.id}/captions`;

                          getNewAssetsMutation.mutate(
                            {
                              path,
                              fileNames: [fileName],
                            },
                            {
                              onSuccess: async (data) => {
                                const { assets } = data;
                                const newAssetsWithProperties = (
                                  await getAssetsWithProperties(assets)
                                ).map((asset) => ({
                                  ...asset,
                                  id: uuidv4(),
                                  assetID: asset.id,
                                }));

                                const captionsAsset =
                                  newAssetsWithProperties[0];

                                setCaptions(captionsAsset);

                                upsertCaptionsMutation.mutate({
                                  id: linkedInContent.id,
                                  asset: {
                                    id: captionsAsset.id,
                                    name: captionsAsset.name,
                                    path: captionsAsset.path,
                                    size: captionsAsset.sizeBytes,
                                    eTag: captionsAsset.eTag,
                                    md5Hash: captionsAsset.md5Hash,
                                    mimetype: captionsAsset.mimetype,
                                    url: captionsAsset.signedUrl,
                                    urlExpiresAt: DateTime.now()
                                      .plus({ seconds: ONE_YEAR_IN_SECONDS })
                                      .toJSDate(),
                                  },
                                });

                                onSave();
                              },
                              onError: (error) => {
                                handleTrpcError({
                                  title: "Failed to upload captions",
                                  error,
                                });
                              },
                            }
                          );
                        }}
                        onDelete={() => {
                          setCaptions(null);
                          deleteCaptionsMutation.mutate({
                            linkedInContentID: linkedInContent.id,
                          });
                        }}
                        storagePath={`${currentWorkspace?.id}/${post.id}/linkedin/${linkedInContent.id}/captions`}
                      />
                    </PostOptionNewDesign>
                  </>
                )}
                {isPDF && (
                  <PostOptionNewDesign title="PDF Title">
                    <div className="ml-1">
                      <AutosizeInput
                        size="md"
                        placeholder="Enter a custom name for the file preview bar (uses the file name by default)"
                        value={linkedInContent.documentTitle || ""}
                        onChange={(value) => {
                          setLinkedInContent((linkedInContent) => ({
                            ...linkedInContent,
                            documentTitle: value,
                          }));
                        }}
                        onBlur={() => {
                          upsertLinkedInContentMutation.mutate({
                            id: linkedInContent.id,
                            copy: linkedInContent.copy,
                            editorState: linkedInContent.editorState,
                            documentTitle: linkedInContent.documentTitle,
                            postID: post.id,
                          });
                        }}
                        onEnterPress={() => {
                          upsertLinkedInContentMutation.mutate({
                            id: linkedInContent.id,
                            copy: linkedInContent.copy,
                            editorState: linkedInContent.editorState,
                            documentTitle: linkedInContent.documentTitle,
                            postID: post.id,
                          });
                        }}
                      />
                    </div>
                  </PostOptionNewDesign>
                )}
              </PostOptionsNewDesign>
            )}
            <div className="my-12">
              <div>
                <PostEngagementsNewDesign
                  engagements={engagements}
                  enableLikes={
                    currentWorkspace?.subscription == null ||
                    currentWorkspace.subscription.isAutoEngagementEnabled
                  }
                  channel="LinkedIn"
                  onEngagementCreate={async (type) => {
                    const engagementID = uuidv4();

                    await trpcUtils.linkedInContent.getEngagements.cancel();

                    if (type === "Like") {
                      setLikeSelectModalOpen(true);
                    } else {
                      createEngagementMutation.mutate({
                        engagementID,
                        contentID: linkedInContent.id,
                        type,
                        integrationID: integration!.id,
                      });

                      // Optimistically update the restponse to the engagementsQuery
                      trpcUtils.linkedInContent.getEngagements.setData(
                        {
                          postID: post.id,
                        },
                        {
                          engagements: [
                            ...engagements,
                            {
                              id: engagementID,
                              type,
                              copy: null,
                              editorState: null,
                              integrationID: integration!.id,
                              integration: {
                                id: integration!.id,
                                account: {
                                  name: integration?.account.name || "",
                                  profileImageUrl:
                                    integration?.account.profileImageUrl || "",
                                },
                              },
                              postDelaySeconds: 0,
                              createdAt: new Date(),
                            },
                          ],
                        }
                      );
                    }
                  }}
                  onEngagementUpdate={(engagementID, engagement) => {
                    updateEngagementMutation.mutate(
                      {
                        engagementID,
                        ...engagement,
                      },
                      {
                        onSuccess: () => {
                          trpcUtils.linkedInContent.getEngagements.invalidate();
                        },
                      }
                    );
                  }}
                  onEngagementDelete={(engagementID) => {
                    let engagementIDs = [engagementID];
                    const engagement = engagements.find(
                      (engagement) => engagement.id === engagementID
                    );

                    if (engagement?.type === "Like") {
                      engagementIDs = engagements
                        .filter((engagement) => engagement.type === "Like")
                        .map((like) => like.id);
                    }

                    deleteEngagementsMutation.mutate(
                      {
                        engagementIDs,
                      },
                      {
                        onSuccess: () => {
                          trpcUtils.linkedInContent.getEngagements.invalidate();
                        },
                      }
                    );

                    // Optimistically update the restponse to the engagementsQuery to remove this engagement
                    trpcUtils.linkedInContent.getEngagements.setData(
                      {
                        postID: post.id,
                      },
                      {
                        engagements: engagements.filter(
                          (engagement) => !engagementIDs.includes(engagement.id)
                        ),
                      }
                    );
                  }}
                  integrationOptions={
                    isReadOnly
                      ? engagements.map((engagement) => ({
                          value: engagement.integrationID,
                          label: engagement.integration.account.name,
                          profilePicture:
                            engagement.integration.account.profileImageUrl,
                        }))
                      : engagementIntegrations.map((account) => ({
                          value: account.id,
                          label: account.account.name,
                          engagementPermissions: account.engagementPermissions,
                          profilePicture: account.account.profileImageUrl,
                          disabled:
                            !(
                              currentWorkspace?.subscription == null ||
                              currentWorkspace.subscription
                                .isAutoEngagementEnabled
                            ) && account.id !== integration?.id,
                          disabledReason: (
                            <div>
                              <div className="font-body-sm mb-1.5">
                                Auto-engagements from profiles other than the
                                posting profile is only available on{" "}
                                <span className="font-medium">Standard</span>{" "}
                                workspace plans and above.
                              </div>
                              <Button
                                variant="secondary"
                                size="sm"
                                onClick={() => {
                                  router.push("/settings/pricing");
                                }}
                              >
                                See Plans
                              </Button>
                            </div>
                          ),
                        }))
                  }
                  Editor={({
                    initialValue,
                    onSave,
                    onUpdateContent,
                    placeholder,
                    engagementID,
                  }) => (
                    <LinkedInEditorWrapperNewDesign
                      key={linkedInContent.id}
                      postID={post.id}
                      initialValue={initialValue}
                      placeholder={placeholder}
                      onSave={onSave}
                      onUpdateContent={onUpdateContent}
                      size="sm"
                      isReadOnly={isReadOnly}
                      escapeContent={false}
                      roomID={null}
                      connectedIntegration={integration}
                    />
                  )}
                  isReadOnly={isReadOnly}
                />
                <SelectModal
                  open={likeSelectModalOpen}
                  setOpen={setLikeSelectModalOpen}
                  isMulti={true}
                  defaultSelected={
                    engagements.filter(
                      (engagement) => engagement.type === "Like"
                    ).length
                      ? engagements
                          .filter((engagement) => engagement.type === "Like")
                          .map((like) => ({ id: like.integrationID }))
                      : engagementIntegrations
                          .filter(
                            (account) =>
                              account.id !== integration?.id &&
                              account.engagementPermissions.includes("Like")
                          )
                          .map((account) => ({
                            id: account.id,
                          }))
                  }
                  title="Add Automatic Likes"
                  subtitle="Schedule automatic likes on LinkedIn from your connected profiles. Likes will automatically trickle in over a period of 10 minutes - no action needed from the user."
                  options={engagementIntegrations
                    .filter((integration) =>
                      integration.engagementPermissions.includes("Like")
                    )
                    .map((integration) => ({
                      id: integration.id,
                      name: integration.account.name,
                      profileImageUrl: integration.account.profileImageUrl,
                    }))}
                  onSelect={(accounts) => {
                    const currentLikes = engagements.filter(
                      (engagement) => engagement.type === "Like"
                    );

                    const likes = accounts.map((account) => {
                      const currentLike = currentLikes.find(
                        (like) => like.integrationID === account.id
                      );

                      if (currentLike) {
                        return currentLike;
                      } else {
                        return {
                          id: uuidv4(),
                          type: "Like" as const,
                          contentID: linkedInContent.id,
                          integrationID: account.id,
                          createdAt: new Date(),
                        };
                      }
                    });

                    upsertEngagementLikesMutation.mutate({
                      likes: likes.map((like) => ({
                        id: like.id,
                        contentID: linkedInContent.id,
                        integrationID: like.integrationID,
                      })),
                    });

                    // Optimistically update the restponse to the engagementsQuery
                    trpcUtils.linkedInContent.getEngagements.setData(
                      {
                        postID: post.id,
                      },
                      {
                        engagements: [
                          ...engagements.filter(
                            (engagement) => engagement.type !== "Like"
                          ),
                          ...likes.map((like) => ({
                            id: like.id,
                            type: like.type,
                            copy: null,
                            editorState: null,
                            integrationID: like.integrationID,
                            integration: {
                              id: like.integrationID,
                              account: {
                                name:
                                  connectedLinkedInIntegrations.find(
                                    (account) =>
                                      account.id === like.integrationID
                                  )?.label || "",
                                profileImageUrl:
                                  connectedLinkedInIntegrations.find(
                                    (account) =>
                                      account.id === like.integrationID
                                  )?.account.profileImageUrl || "",
                              },
                            },
                            postDelaySeconds: 0,
                            createdAt: like.createdAt,
                          })),
                        ],
                      }
                    );

                    setLikeSelectModalOpen(false);
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinkedInPostNewDesign;
