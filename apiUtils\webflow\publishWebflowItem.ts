import getAssetsForWebflow from "apiUtils/getAssetsForWebflow";
import showdown from "showdown";
import { db } from "~/clients";
import Webflow from "~/clients/Webflow";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";

const publishWebflowItem = async ({
  postID,
  isDraft,
  isPreview,
}: {
  postID: string;
  isDraft?: boolean;
  isPreview?: boolean;
}): Promise<ScheduledPostResult> => {
  const post = await db.post.findFirst({
    where: {
      id: postID,
    },
    include: {
      webflowContent: {
        include: {
          fields: {
            include: {
              assets: {
                include: {
                  asset: true,
                },
              },
            },
          },
        },
      },
      webflowIntegration: true,
    },
  });

  const webflowContent = post?.webflowContent;

  if (!post || !webflowContent) {
    return {
      status: "Error",
      error: "No content to publish",
    };
  }

  const webflowIntegration = post.webflowIntegration;

  if (!webflowContent.title) {
    return {
      status: "Error",
      error: "No content title to publish",
    };
  } else if (!webflowContent.body && !webflowContent.bodyHTML) {
    return {
      status: "Error",
      error: "No content to publish",
    };
  } else if (!webflowIntegration) {
    return {
      status: "Error",
      error: "No Webflow integration",
    };
  }

  const webflow = new Webflow(webflowIntegration.accessToken);

  let baseUrl = webflowIntegration.blogBaseUrl;
  if (isDraft) {
    const [site, pages] = await Promise.all([
      webflow.getSite(webflowIntegration.siteID),
      webflow.listPages(webflowIntegration.siteID),
    ]);

    if (!site.data || !pages.data) {
      const errors = site.errors || pages.errors;
      console.error("Error querying Webflow site / pages", errors);
      return {
        status: "Error",
        error: errors?.join(". ") || "Error querying Webflow site / pages",
      };
    }

    const siteShortName = site.data.shortName;
    const page = pages.data.find(
      (page) => page.collectionID === webflowIntegration.collectionID
    );

    if (!page) {
      return {
        status: "Error",
        error: "No page found in Webflow connected to this collection",
      };
    }

    const params = new URLSearchParams({
      locale: "en",
      pageId: page.id,
      mode: "edit",
    });
    baseUrl = `https://${siteShortName}.design.webflow.com/?${params}`;
  }

  const assets = await getAssetsForWebflow(post);

  const slugField = webflowContent.fields.find(
    (field) => field.slug === "slug"
  );

  const slugValue = slugField?.value;
  if (!slugField || slugValue == null || slugValue === "") {
    return {
      status: "Error",
      error: "No slug to publish blog to",
    };
  }
  const slug = isPreview ? `${slugValue}-preview` : slugValue;

  const converter = new showdown.Converter();

  const fields = webflowContent.fields.map((field) => {
    if (field.slug === "slug") {
      return {
        ...field,
        value: slug,
      };
    }
    if (field.type === "File" || field.type === "Image") {
      const asset = assets[field.id][0];
      return {
        ...field,
        value: asset != null ? asset.signedUrl : null,
      };
    } else if (field.type === "MultiImage") {
      const fieldAssets = assets[field.id];
      return {
        ...field,
        value:
          fieldAssets.length > 0
            ? fieldAssets.map((asset) => ({
                url: asset.signedUrl,
              }))
            : null,
      };
    } else if (field.value === "") {
      return {
        ...field,
        value: null,
      };
    } else if (field.type === "DateTime") {
      return {
        ...field,
        value: field.value,
      };
    } else if (field.type === "RichText") {
      return {
        ...field,
        value: converter.makeHtml(field.value as string),
      };
    } else if (field.type === "MultiReference") {
      return {
        ...field,
        value: field.value ? field.value.split(",") : null,
      };
    } else {
      return field;
    }
  });

  const fieldsWithValue = fields.filter((field) => field.value != null);

  const collectionResponse = await webflow.getCollectionDetails(
    webflowIntegration.collectionID
  );

  if (collectionResponse.errors) {
    return {
      status: "Error",
      error: collectionResponse.errors.join(". "),
      rawError: collectionResponse.rawError,
    };
  }

  const blogContentFieldSlug = collectionResponse.data.fields.find(
    (field) => field.id === webflowIntegration.blogContentFieldID
  )?.slug;

  if (!blogContentFieldSlug) {
    return {
      status: "Error",
      error: "Blog content field ID is no longer present in the fields",
    };
  }

  // Full Width Classnames: w-richtext-align-fullwidth w-richtext-figure-type-image
  // Centered Classnames: w-richtext-align-center w-richtext-figure-type-image

  const bodyHTML = webflowContent.bodyHTML
    ? webflowContent.bodyHTML
    : converter
        .makeHtml(webflowContent.body!)
        .replaceAll(
          "<img src=",
          '<figure class="w-richtext-align-center w-richtext-figure-type-image"><div><img src='
        )
        .replaceAll(/alt="(.*)" \/>/g, 'alt="$1" /></div></figure>');

  const publishResponse = await webflow.createCollectionItem({
    collectionID: webflowIntegration.collectionID,
    cmsLocaleID: webflowIntegration.cmsLocaleID,
    item: {
      name: webflowContent.title,
      data: fieldsWithValue.reduce(
        (acc, field) => {
          acc[field.slug] = field.value as
            | string
            | string[]
            | { url: string }[];
          return acc;
        },
        {
          [blogContentFieldSlug]: bodyHTML,
        } as { [key: string]: string | string[] | { url: string }[] }
      ),
    },
    isDraft,
  });

  if (publishResponse.errors) {
    return {
      status: "Error",
      error: publishResponse.errors.join(". "),
      rawError: publishResponse.rawError,
    };
  } else {
    let postUrl = `${baseUrl}/${slug}`;
    if (isDraft) {
      postUrl = `${baseUrl}&itemId=${publishResponse.data.id}`;
    }

    return {
      status: "Success",
      socialPostID: publishResponse.data.id,
      postUrl,
    };
  }
};

export default publishWebflowItem;
