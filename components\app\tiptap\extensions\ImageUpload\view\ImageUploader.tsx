import { ArrowUpTrayIcon, PhotoIcon } from "@heroicons/react/24/outline";
import { ChangeEvent, useCallback } from "react";
import Loader from "~/components/Loader";
import Button from "~/components/ui/Button";
import { cn } from "~/utils";
import { useDropZone, useFileUpload, useUploader } from "./hooks";

export const ImageUploader = ({
  onUpload,
  customUpload,
}: {
  onUpload: (url: string) => void;
  customUpload?: (file: File) => Promise<string>;
}) => {
  const { loading, uploadFile } = useUploader({ onUpload, customUpload });
  const { handleUploadClick, ref } = useFileUpload();
  const { draggedInside, onDrop, onDragEnter, onDragLeave } = useDropZone({
    uploader: uploadFile,
  });

  const onFileChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) =>
      e.target.files ? uploadFile(e.target.files[0]) : null,
    [uploadFile]
  );

  if (loading) {
    return (
      <div className="bg-opacity-80 flex min-h-40 items-center justify-center rounded-lg p-8">
        <Loader size="md" />
      </div>
    );
  }

  const wrapperClass = cn(
    "flex flex-col items-center justify-center px-8 py-10 rounded-lg bg-opacity-80",
    draggedInside && "bg-light-gray"
  );

  return (
    <div
      className={wrapperClass}
      onDrop={onDrop}
      onDragOver={onDragEnter}
      onDragLeave={onDragLeave}
      contentEditable={false}
    >
      <PhotoIcon className="mb-4 h-12 w-12 text-black opacity-20 dark:text-white" />
      <div className="flex flex-col items-center justify-center gap-2">
        <div className="text-center text-sm font-medium text-neutral-400 dark:text-neutral-500">
          {draggedInside ? "Drop image here" : "Drag and drop or"}
        </div>
        <div>
          <Button
            disabled={draggedInside}
            onClick={handleUploadClick}
            size="sm"
            variant="secondary"
          >
            <ArrowUpTrayIcon className="h-4 w-4" />
            Upload an image
          </Button>
        </div>
      </div>
      <input
        className="h-0 w-0 overflow-hidden opacity-0"
        ref={ref}
        type="file"
        accept=".jpg,.jpeg,.png,.webp,.gif"
        onChange={onFileChange}
      />
    </div>
  );
};

export default ImageUploader;
