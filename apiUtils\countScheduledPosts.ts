// Takes in a company with all scheduled posts and counts all the ones scheduled in this month

type Company = {
  workspaces: {
    _count: {
      scheduledPosts: number;
    };
  }[];
};

const countScheduledPosts = (company: Company) => {
  const monthlyScheduledPosts = company.workspaces.reduce((acc, workspace) => {
    return acc + workspace._count.scheduledPosts;
  }, 0);

  return monthlyScheduledPosts;
};

export default countScheduledPosts;
