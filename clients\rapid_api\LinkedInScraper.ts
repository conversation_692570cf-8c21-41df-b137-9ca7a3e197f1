import { LinkedInPostType } from "@prisma/client";
import catchAxiosError from "apiUtils/catchAxiosError";
import axios, { AxiosError, AxiosInstance } from "axios";
import { StatusCodes } from "http-status-codes";
import { HASHTAG_REGEX } from "~/constants";
import { sleep } from "~/utils";
import getLinkedInProfilePermalink from "~/utils/linkedin/getLinkedInProfilePermalink";
import {
  Response,
  createErrorResponse,
  createSuccessResponse,
} from "../Response";

type ErrorResponse = {
  success: false;
  message: string;
  data: null;
};

type SuccessResponse<T> = T & {
  success: true;
};

const parseLinkedInScraperResponse = <T>(
  data: any
): ErrorResponse | SuccessResponse<T> => {
  if ("success" in data && data.success === false) {
    return {
      success: false,
      message: data.message,
      data: null,
    };
  }

  return {
    success: true,
    ...data,
  } as SuccessResponse<T>;
};

class LinkedInScraper {
  client: AxiosInstance;
  profile: ProfileService;
  post: PostService;

  constructor() {
    this.client = axios.create({
      baseURL: "https://real-time-people-company-data.p.rapidapi.com",
      headers: {
        "x-rapidapi-key": process.env.RAPID_API_KEY,
        "x-rapidapi-host": "real-time-people-company-data.p.rapidapi.com",
      },
    });

    this.client.interceptors.response.use((response) => {
      return {
        ...response,
        data: parseLinkedInScraperResponse(response.data),
      };
    });

    this.profile = new ProfileService(this.client);
    this.post = new PostService(this.client);
  }
}

type RawPost = {
  urn: string; // 7307454738248146944
  resharedPost?: {
    text: string;
  };
  isBrandPartnership: boolean;
  text: string;
  totalReactionCount?: number;
  commentsCount?: number;
  repostsCount?: number;
  postUrl: string; // https://www.linkedin.com/feed/update/urn:li:activity:7307454738248146944/
  shareUrl: string; // https://www.linkedin.com/posts/adamselipsky_adam-selipsky-the-exit-interview-amazon-activity-7202377267048570880-MVTf?utm_source=social_share_send&utm_medium=member_desktop_web&rcm=ACoAAFeZgg0BJrzshYXxMZVZYQISC_QgERk6unk
  postedDate: string; // 2025-03-17 17:36:15.542 +0000 UTC
  postedDateTimestamp: number; // 1742232975542
  reposted?: true;
  document: {};
  celebration: {};
  // Thiis is a link
  article: {
    title?: string;
    subtitle?: string;
    newsletter: {};
  };
  poll: {
    title?: string;
    uniqueVotersCount?: number;
    remainingDuration?: string; // 2w left
    options: {
      text: string;
      voteCount?: number; // undefined implies no votes yet
    }[];
  };
  author: {
    firstName?: string;
    lastName?: string;
    username?: string;
  };
  mentions?: {
    firstName: string;
    lastName: string;
    publicIdentifier: string;
  }[];
  companyMentions?: {
    id: string;
    name: string;
    publicIdentifier: string;
    url: string;
  }[];
  image?: {
    url: string;
    width: number;
    height: number;
  }[];
  video?: {
    url: string;
    poster: string;
    duration: number;
  }[];
};

type Post = {
  activityUrn: string;
  url: string;
  shareUrl: string;
  likeCount: number;
  commentCount: number;
  shareCount: number;
  personTagCount: number;
  organizationTagCount: number;
  hashtagCount: number;
  commentary: string;
  type: LinkedInPostType;
  publishedAt: Date;
  rawResponse: any;
};

type RawProfileData = {
  id: number;
  urn: string;
  username: string;
  firstName: string;
  lastName: string;
  headline: string;
  summary: string;
  isPremium: boolean;
  isCreator: boolean;
  profilePicture: string;
  geo: {
    country: string;
    city?: string;
    full: string;
    countryCode: string;
  };
  fullPositions: {
    companyId: number;
    companyName: string;
    companyUsername: string;
    companyURL: string;
    companyLogo: string;
    companyIndustry: string;
    companyStaffCountRange: string;
    title: string;
    location: string;
    description: string;
    employmentType: string;
    start: {
      year: number;
      month: number;
      day: number;
    };
    end: {
      year: number;
      month: number;
      day: number;
    };
  }[];
};

type ProfileData = {
  id: string;
  username: string;
  encryptedUrn: string;
  firstName: string;
  lastName: string;
  headline: string;
  summary: string;
  url: string;
  isCreator: boolean;
  isPremium: boolean;
  followerCount: number;
  connectionCount: number;
  profileImageUrl: string | null;
  profileImageExpiresAt: Date | null;
  city: string | null;
  country: string;
  positions: {
    company: {
      name: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
      industry: string;
      staffCountRange: string;
    } & (
      | {
          id: string;
          vanityName: string;
          url: string;
        }
      | {
          id: null;
          vanityName: null;
          url: null;
        }
    );
    title: string;
    description: string | null;
    location: string;
    employmentType: string;
    startDate: Date;
    endDate: Date | null;
  }[];
};

class ProfileService {
  constructor(private readonly client: AxiosInstance) {}

  private convertRawPosts(rawPosts: RawPost[], username: string): Post[] {
    const getPostType = (post: RawPost): LinkedInPostType => {
      if (post.reposted) {
        if (
          Object.keys(post.author).length === 0 ||
          post.author.username !== username ||
          post.resharedPost == null
        ) {
          return LinkedInPostType.Repost;
        } else {
          return LinkedInPostType.QuotePost;
        }
      } else if (post.article.title) {
        return LinkedInPostType.Article;
      } else if (post.poll?.title != null) {
        return LinkedInPostType.Poll;
      } else if (post.video && post.video.length > 0) {
        return LinkedInPostType.Video;
      } else if (post.image) {
        if (post.image.length === 1) {
          return LinkedInPostType.Image;
        } else {
          return LinkedInPostType.MultiImage;
        }
      } else if (Object.keys(post.document).length > 0) {
        return LinkedInPostType.Document;
      } else if (Object.keys(post.celebration).length > 0) {
        return LinkedInPostType.Celebration;
      }

      return LinkedInPostType.Text;
    };

    return rawPosts.map((post) => {
      const hashtagCount = (post.text.match(HASHTAG_REGEX) ?? []).length;

      return {
        activityUrn: `urn:li:activity:${post.urn}`,
        url: post.postUrl,
        shareUrl: post.shareUrl,
        likeCount: post.totalReactionCount ?? 0,
        commentCount: post.commentsCount ?? 0,
        shareCount: post.repostsCount ?? 0,
        personTagCount: post.mentions?.length ?? 0,
        organizationTagCount: post.companyMentions?.length ?? 0,
        hashtagCount,
        commentary: post.text,
        type: getPostType(post),
        publishedAt: new Date(post.postedDateTimestamp),
        rawResponse: post,
      };
    });
  }

  private convertRawProfileData(
    rawProfileData: RawProfileData
  ): Omit<ProfileData, "followerCount" | "connectionCount"> {
    const profileImageExpiresAt = getImageExpiresAt(
      rawProfileData.profilePicture
    );

    const positions =
      rawProfileData.fullPositions?.filter(
        (position) => position.companyId != 0 && position.companyUsername != ""
      ) || [];

    return {
      id: rawProfileData.id.toString(),
      encryptedUrn: rawProfileData.urn,
      username: rawProfileData.username,
      firstName: rawProfileData.firstName,
      lastName: rawProfileData.lastName,
      headline: rawProfileData.headline,
      summary: rawProfileData.summary,
      url: getLinkedInProfilePermalink({
        vanityName: rawProfileData.username,
        type: "User",
      }),
      isCreator: rawProfileData.isCreator,
      isPremium: rawProfileData.isPremium,
      profileImageUrl: rawProfileData.profilePicture,
      profileImageExpiresAt,
      city: rawProfileData.geo.city ?? null,
      country: rawProfileData.geo.country,
      positions: positions.map((position) => {
        const companyId =
          position.companyId == 0 ? null : position.companyId.toString();
        const companyUsername =
          position.companyUsername == "" ? null : position.companyUsername;
        const companyURL = position.companyURL;

        const company = {
          name: position.companyName,
          profileImageUrl: position.companyLogo,
          profileImageExpiresAt: getImageExpiresAt(position.companyLogo),
          industry: position.companyIndustry,
          staffCountRange: position.companyStaffCountRange,
          ...(companyId === null
            ? {
                id: null,
                vanityName: null,
                url: null,
              }
            : {
                id: companyId as string,
                vanityName: companyUsername as string,
                url: companyURL as string,
              }),
        };

        return {
          company,
          title: position.title,
          description: position.description == "" ? null : position.description,
          location: position.location,
          employmentType: position.employmentType,
          startDate: new Date(
            position.start.year,
            position.start.month - 1,
            position.start.day + 1
          ),
          endDate:
            position.end.year != 0
              ? new Date(
                  position.end.year,
                  position.end.month - 1,
                  position.end.day + 1
                )
              : null,
        };
      }),
    };
  }

  // https://rapidapi.com/rockapis-rockapis-default/api/linkedin-data-api/playground/apiendpoint_1d2ead16-5039-4883-ac25-390fa57edf94
  async getProfile(
    username: string,
    retryCount = 0
  ): Promise<Response<ProfileData>> {
    try {
      const response = await this.client.get(`/data-connection-count`, {
        params: {
          username,
        },
      });

      const data = response.data;

      if (data.success === false) {
        const retryMessages = [
          "The request failed. You will not be charged for this request",
          "This profile can't be accessed",
        ];

        if (retryMessages.includes(data.message) && retryCount < 2) {
          return this.getProfile(username, retryCount + 1);
        }

        return createErrorResponse([data.message], StatusCodes.BAD_REQUEST);
      }

      const profileData = data.data as RawProfileData;

      const cleanedData = this.convertRawProfileData(profileData);

      return createSuccessResponse(
        {
          ...cleanedData,
          followerCount: data.follower,
          connectionCount: data.connection,
        },
        response.status
      );
    } catch (error) {
      if (
        error instanceof AxiosError &&
        error.status === StatusCodes.TOO_MANY_REQUESTS
      ) {
        if (retryCount < 2) {
          await sleep(3000);
          return this.getProfile(username, retryCount + 1);
        }
      }

      return catchAxiosError(error, (data) => data.message);
    }
  }

  // https://rapidapi.com/rockapis-rockapis-default/api/linkedin-data-api/playground/apiendpoint_2baef840-d733-4a30-afda-eac3e28aa7fb
  async getFollowerCount(
    username: string,
    retryCount = 0
  ): Promise<
    Response<{
      connectionCount: number;
      followerCount: number;
    }>
  > {
    try {
      const { data, status } = await this.client.get(`/connection-count`, {
        params: {
          username,
        },
      });

      if (data.success === false) {
        const retryMessages = [
          "The request failed. You will not be charged for this request",
          "This profile can't be accessed",
        ];

        if (retryMessages.includes(data.message) && retryCount < 2) {
          return this.getFollowerCount(username, retryCount + 1);
        }

        return createErrorResponse([data.message], StatusCodes.BAD_REQUEST);
      }

      const followerData = data as {
        connection: number;
        follower: number;
      };

      return createSuccessResponse(
        {
          connectionCount: followerData.connection,
          followerCount: followerData.follower,
        },
        status
      );
    } catch (error) {
      return catchAxiosError(error, (data) => data.message);
    }
  }

  // User: https://rapidapi.com/rockapis-rockapis-default/api/linkedin-data-api/playground/apiendpoint_d6b1fa6c-9d08-4ad2-ab07-32eaa03e2e57
  // Company: https://rapidapi.com/rockapis-rockapis-default/api/linkedin-data-api/playground/apiendpoint_3edd0762-b6a9-4ee4-9231-c574bedc7019
  async getPosts(
    username: string,
    accountType: "User" | "Organization" = "User",
    pagination: {
      page: number;
      token?: string | null;
    } = {
      page: 0,
    }
  ): Promise<Response<{ posts: Post[]; paginationToken: string }>> {
    const url =
      accountType === "User" ? `/get-profile-posts` : `/get-company-posts`;

    const { data, status } = await this.client.get(url, {
      params: {
        username,
        start: pagination.page,
        paginationToken: pagination.token,
      },
    });

    console.log(`${username}: ${JSON.stringify(data)}`);

    if (data.success === false) {
      return createErrorResponse([data.message], StatusCodes.BAD_REQUEST);
    }

    const rawPostsData = data.data as RawPost[];

    const cleanedPosts = this.convertRawPosts(rawPostsData, username);

    return createSuccessResponse(
      {
        posts: cleanedPosts,
        paginationToken: data.paginationToken,
      },
      status
    );
  }

  // https://rapidapi.com/rockapis-rockapis-default/api/linkedin-data-api/playground/apiendpoint_ad69011c-e92e-4ce5-aa3b-86e03041a494
  async getProfileAndPosts(
    username: string,
    retryCount = 0
  ): Promise<
    Response<{
      posts: Post[];
      profile: ProfileData;
    }>
  > {
    try {
      const { data, status } = await this.client.get(
        `/profile-data-connection-count-posts`,
        {
          params: {
            username,
          },
          timeout: 10_000,
        }
      );

      if (data.success === false) {
        const retryMessages = [
          "The request failed. You will not be charged for this request",
          "This profile can't be accessed",
        ];

        if (retryMessages.includes(data.message) && retryCount < 2) {
          return this.getProfileAndPosts(username, retryCount + 1);
        }

        return createErrorResponse([data.message], StatusCodes.BAD_REQUEST);
      }

      const rawData = data as {
        follower: number;
        connection: number;
        posts: RawPost[];
        data: RawProfileData;
      };

      const cleanedPosts = this.convertRawPosts(rawData.posts, username);

      return createSuccessResponse(
        {
          posts: cleanedPosts,
          profile: {
            ...this.convertRawProfileData(rawData.data),
            followerCount: rawData.follower,
            connectionCount: rawData.connection,
          },
        },
        status
      );
    } catch (error) {
      if (error instanceof AxiosError && error.code === "ECONNABORTED") {
        if (retryCount < 2) {
          return this.getProfileAndPosts(username, retryCount + 2);
        } else {
          return createErrorResponse(
            ["Request timed out"],
            StatusCodes.REQUEST_TIMEOUT
          );
        }
      }

      return catchAxiosError(error, (data) => data.message);
    }
  }
}

type RawComment = {
  isPinned: boolean;
  isEdited: boolean;
  threadUrn: string;
  urn: string;
  createdAt: number;
  createdAtString: string;
  permalink: string;
  text: string;
  author: {
    name: string;
    urn: string;
    id: string;
    username: string;
    linkedinUrl: string;
    title: string;
  };
  totalSocialActivityCounts: {
    numComments: number;
    likeCount: number;
    appreciationCount: number;
    empathyCount: number;
    InterestCount: number;
    praiseCount: number;
    funnyCount: number;
    maybeCount: number;
    totalReactionCount: number;
    numShares: number;
  };
};

type Comment = {
  isPinned: boolean;
  isEdited: boolean;
  threadUrn: string;
  urn: string;
  permalink: string;
  text: string;
  author: {
    id: string;
    name: string;
    encryptedUrn: string;
    url: string;
    title: string;
  };
  publishedAt: Date;

  commentCount: number;
  likeCount: number;
  appreciationCount: number;
  empathyCount: number;
  interestCount: number;
  praiseCount: number;
  funnyCount: number;
  maybeCount: number;
  totalReactionCount: number;
  shareCount: number;
};

type RawReaction = {
  id: number;
  urn: string;
  fullName: string;
  profileType: "user" | "company";
  profileUrl: string;
  reactionType:
    | "LIKE"
    | "PRAISE"
    | "EMPATHY"
    | "INTEREST"
    | "APPRECIATION"
    | "ENTERTAINMENT";
  profilePicture:
    | {
        width: number;
        height: number;
        url: string;
      }[]
    | null;
  headline: string;
};

type Reaction = {
  type:
    | "LIKE"
    | "PRAISE"
    | "EMPATHY"
    | "INTEREST"
    | "APPRECIATION"
    | "ENTERTAINMENT";
  author: {
    id: number;
    encryptedUrn: string;
    name: string;
    url: string;
    headline: string;
    type: "user" | "company";
  };
};

class PostService {
  constructor(private readonly client: AxiosInstance) {}

  // https://rapidapi.com/rockapis-rockapis-default/api/linkedin-data-api/playground/apiendpoint_32b2d880-fbcc-4494-a7c5-cf754d20dbb4
  async getComments(
    urn: string,
    page = 1,
    retryCount = 0
  ): Promise<Response<Comment[]>> {
    let urnID = urn.startsWith("urn") ? urn.split(":")[3] : urn;

    const { data, status } = await this.client.get(
      `/get-profile-posts-comments`,
      {
        params: { urn: urnID, sort: "mostRecent", page },
      }
    );

    const rawData = data as {
      success: boolean;
      message: string;
      data: RawComment[];
      total: number;
      totalPage: number;
    };

    if (data.success === false) {
      if (retryCount < 2) {
        return this.getComments(urn, page, retryCount + 1);
      } else {
        return createErrorResponse([data.message], StatusCodes.BAD_REQUEST);
      }
    }

    const comments =
      rawData.data?.map((comment) => ({
        isPinned: comment.isPinned,
        isEdited: comment.isEdited,
        threadUrn: comment.threadUrn,
        urn: comment.urn,
        permalink: comment.permalink,
        text: comment.text,
        author: {
          id: comment.author.id,
          name: comment.author.name,
          encryptedUrn: comment.author.urn,
          url: comment.author.linkedinUrl,
          title: comment.author.title,
        },
        commentCount: comment.totalSocialActivityCounts.numComments,
        likeCount: comment.totalSocialActivityCounts.likeCount,
        appreciationCount: comment.totalSocialActivityCounts.appreciationCount,
        empathyCount: comment.totalSocialActivityCounts.empathyCount,
        interestCount: comment.totalSocialActivityCounts.InterestCount,
        praiseCount: comment.totalSocialActivityCounts.praiseCount,
        funnyCount: comment.totalSocialActivityCounts.funnyCount,
        maybeCount: comment.totalSocialActivityCounts.maybeCount,
        totalReactionCount:
          comment.totalSocialActivityCounts.totalReactionCount,
        shareCount: comment.totalSocialActivityCounts.numShares,
        publishedAt: new Date(comment.createdAt),
      })) ?? [];

    if (page === 1 && rawData.totalPage > 1) {
      // Create promises for all remaining pages
      const remainingPagePromises = Array.from(
        { length: rawData.totalPage - 1 },
        (_, i) => this.getComments(urn, i + 2, retryCount)
      );

      try {
        const results = await Promise.all(remainingPagePromises);
        const allComments = [
          ...comments,
          ...results.flatMap((result) => (result.success ? result.data : [])),
        ];
        return createSuccessResponse(allComments, StatusCodes.OK);
      } catch (error) {
        // If any page fails, continue with just the first page
        console.error("Error fetching additional comment pages:", error);
      }
    }

    return createSuccessResponse(comments, status);
  }

  // https://rapidapi.com/rockapis-rockapis-default/api/linkedin-data-api/playground/apiendpoint_05186403-0154-462c-ab21-a10657f13a58
  async getReactions(
    urn: string,
    page = 1,
    retryCount = 0
  ): Promise<Response<Reaction[]>> {
    const urnID = urn.startsWith("urn") ? urn.split(":")[3] : urn;

    const { data, status } = await this.client.post(`/get-post-reactions`, {
      urn: urnID,
      page,
    });

    const rawData = data as {
      success: boolean;
      message: string;
      data: {
        currentPage: number;
        total: number;
        totalPages: number;
        items: RawReaction[];
      };
    };

    if (data.success === false) {
      if (retryCount < 2) {
        return this.getReactions(urn, page, retryCount + 1);
      } else {
        return createErrorResponse([data.message], StatusCodes.BAD_REQUEST);
      }
    }

    const reactions =
      rawData.data.items?.map((reaction) => ({
        id: reaction.id,
        type: reaction.reactionType,
        author: {
          id: reaction.id,
          encryptedUrn: reaction.urn,
          name: reaction.fullName,
          url: reaction.profileUrl,
          headline: reaction.headline,
          type: reaction.profileType,
        },
      })) ?? [];

    if (page === 1 && rawData.data.totalPages > 1) {
      const remainingPagePromises = Array.from(
        { length: rawData.data.totalPages - 1 },
        (_, i) => this.getReactions(urn, i + 2, retryCount)
      );

      try {
        const results = await Promise.all(remainingPagePromises);
        const allReactions = [
          ...reactions,
          ...results.flatMap((result) => (result.success ? result.data : [])),
        ];
        return createSuccessResponse(allReactions, StatusCodes.OK);
      } catch (error) {
        console.error("Error fetching additional reaction pages:", error);
      }
    }
    return createSuccessResponse(reactions, status);
  }
}

const getImageExpiresAt = (url: string): Date | null => {
  try {
    const params = new URLSearchParams(url.split("?")[1]);
    return new Date(params.get("e") ? parseInt(params.get("e")!) * 1000 : 0);
  } catch (error) {
    return null;
  }
};

export default LinkedInScraper;
