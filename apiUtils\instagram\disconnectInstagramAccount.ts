import { TRPCError } from "@trpc/server";
import catchZenstackDeleteError from "apiUtils/catchZenstackDeleteError";
import removeSubscriptionItem from "apiUtils/stripe/removeSubscriptionItem";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const disconnectInstagramAccount = async ({
  integrationID,
  workspaceID,
  userID,
}: {
  integrationID: string;
  workspaceID: string;
  userID: string;
}) => {
  const instagramIntegration = await db.instagramIntegration.findFirst({
    where: {
      id: integrationID,
      workspaceID,
    },
    select: {
      id: true,
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
      facebookIntegration: {
        select: {
          id: true,
          isIntegrated: true,
        },
      },
    },
  });

  if (!instagramIntegration) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "No integration found",
    });
  }

  const workspace = instagramIntegration.workspace;

  const postsToUpdate = await db.post.findMany({
    where: {
      instagramIntegrationID: integrationID,
      scheduledPosts: {
        some: {
          status: "Scheduled",
          channel: "Instagram",
          publishAt: {
            gt: new Date(),
          },
        },
      },
    },
    select: {
      id: true,
    },
  });
  const postIDs = postsToUpdate.map((post) => post.id);

  // Cancel all scheduled posts for just Instagram
  await db.scheduledPost.updateMany({
    where: {
      channel: "Instagram",
      status: "Scheduled",
      publishAt: {
        gt: new Date(),
      },
      post: {
        id: {
          in: postIDs,
        },
      },
    },
    data: {
      status: "Cancelled",
    },
  });

  const updatedPosts = await db.post.findMany({
    where: {
      id: {
        in: postIDs,
      },
    },
    select: {
      id: true,
      _count: {
        select: {
          scheduledPosts: {
            where: {
              status: "Scheduled",
            },
          },
        },
      },
    },
  });

  const postsWithoutScheduledPosts = updatedPosts.filter(
    (post) => post._count.scheduledPosts === 0
  );
  await db.post.updateMany({
    where: {
      id: {
        in: postsWithoutScheduledPosts.map((post) => post.id),
      },
    },
    data: {
      status: "Finalized",
    },
  });

  const postDefaults = await db.postDefault.findFirst({
    where: {
      workspaceID: workspaceID,
    },
    select: {
      instagramIntegrationID: true,
      workspace: {
        select: {
          instagramIntegrations: {
            where: {
              id: {
                not: integrationID,
              },
            },
            select: {
              id: true,
              createdAt: true,
            },
          },
        },
      },
    },
  });

  if (postDefaults?.instagramIntegrationID === integrationID) {
    const oldestInstagramIntegration =
      postDefaults.workspace.instagramIntegrations.sort(
        (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
      )[0];

    await db.postDefault.update({
      where: {
        workspaceID: workspaceID,
      },
      data: {
        instagramIntegrationID:
          oldestInstagramIntegration != null
            ? oldestInstagramIntegration.id
            : null,
      },
    });
  }

  await catchZenstackDeleteError(async () => {
    await db.instagramIntegration.update({
      where: {
        id: integrationID,
      },
      data: {
        deletedTimeStamp: Math.floor(new Date().getTime() / 1000),
        deletedByID: userID,
      },
    });
  });

  // If the FB integration isn't integrated then it's just used for IG and can be deleted
  const facebookIntegration = instagramIntegration.facebookIntegration;
  if (facebookIntegration != null && !facebookIntegration.isIntegrated) {
    await catchZenstackDeleteError(async () => {
      await db.facebookIntegration.update({
        where: {
          id: facebookIntegration.id,
        },
        data: {
          deletedTimeStamp: Math.floor(new Date().getTime() / 1000),
          deletedByID: userID,
        },
      });
    });
  }

  await removeSubscriptionItem({
    workspaceID,
    itemName: "ADDITIONAL_SOCIAL_PROFILE",
    quantity: 1,
  });

  posthog.capture({
    distinctId: userID,
    event: POSTHOG_EVENTS.socialAccount.disconnected,
    properties: {
      channel: "Instagram",
    },
    groups: {
      workspace: workspaceID,
      company: workspace.companyID,
    },
  });

  return instagramIntegration;
};

export default disconnectInstagramAccount;
