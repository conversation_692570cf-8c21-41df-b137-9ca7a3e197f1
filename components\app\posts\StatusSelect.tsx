import { AddNewButton, Combobox } from "~/components";

import { PostStatus } from "@prisma/client";
import { POST_STATUSES } from "~/types";
import StatusBadge from "./StatusBadge";

type Props = {
  value: PostStatus | null;
  onChange: (status: PostStatus) => void;
};

const StatusSelect = ({ value, onChange }: Props) => {
  const options = POST_STATUSES.map((status) => {
    return {
      value: status,
      label: <StatusBadge kind="label" status={status} />,
      component: (
        <StatusBadge kind="badge" status={status} showHoverState={true} />
      ),
    };
  });

  return (
    <Combobox
      placeholder={<AddNewButton label="Status" />}
      hideSelectedOption={true}
      value={value}
      onChange={onChange}
      options={options}
    />
  );
};

export default StatusSelect;
