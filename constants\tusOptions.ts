import { TusOptions } from "@uppy/tus";
import supabasePublic from "~/clients/supabasePublic";

const projectID = "mletdxmpsjlbfpnymbcu";
const isDev = process.env.NODE_ENV === "development";

const tusOptionsAsync = async (): Promise<TusOptions<any, any>> => {
  const {
    data: { session },
  } = await supabasePublic.auth.getSession();

  return {
    endpoint: isDev
      ? `http://localhost:54321/storage/v1/upload/resumable`
      : `https://${projectID}.supabase.co/storage/v1/upload/resumable`,
    headers: {
      authorization: `Bearer ${session?.access_token}`,
      "x-upsert": "true", // optionally set upsert to true to overwrite existing files
    },
    chunkSize: 10 * 1024 * 1024,
    allowedMetaFields: [
      "bucketName",
      "objectName",
      "contentType",
      "cacheControl",
    ],
    storeFingerprintForResuming: false,
  };
};

export default tusOptionsAsync;
