import { useState } from "react";
import { AddN<PERSON><PERSON><PERSON>on, Async<PERSON><PERSON>obox, Badge } from "~/components";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { trpc } from "~/utils";

type Props = {
  location: {
    name: string;
    pageID: string;
  } | null;
  onSaveLocation: (
    location: {
      name: string;
      pageID: string;
    } | null
  ) => void;
};

const AddLocationModal = ({ location, onSaveLocation }: Props) => {
  const [currentLocation, setCurrentLocation] = useState<{
    name: string;
    pageID: string;
  } | null>(location);
  const [open, setOpen] = useState<boolean>(false);
  const [query, setQuery] = useState<string>("");
  const [error, setError] = useState<string | null>(null);

  const searchPagesQuery = trpc.integration.facebook.searchPages.useQuery({
    query,
    mustHaveLocation: true,
  });

  const options =
    searchPagesQuery.data?.pages.map((page: any) => ({
      value: page.id,
      label: page.name,
      detail: page.address,
    })) ?? [];

  return (
    <div>
      {location == null ? (
        <AddNewButton
          label="Add Location"
          onClick={() => {
            setCurrentLocation(location);
            setOpen(true);
          }}
        />
      ) : (
        <Badge
          intent="secondary"
          kind="hollow"
          onClick={() => {
            setCurrentLocation(location);
            setOpen(true);
          }}
        >
          <span className="text-medium-dark-gray">Location:</span>
          <span className="text-secondary">{location.name}</span>
        </Badge>
      )}
      <Credenza.Root open={open} onOpenChange={setOpen}>
        <Credenza.Header>
          <Credenza.Title>Add Location</Credenza.Title>
          <Credenza.Description>
            Enter a location for your post
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <AsyncCombobox
            value={
              currentLocation
                ? {
                    value: currentLocation.pageID,
                    label: currentLocation.name,
                  }
                : null
            }
            isLoading={searchPagesQuery.isLoading}
            options={options}
            placeholder="Ex: Los Angeles, CA or Madison Square Garden"
            onSearchChange={setQuery}
            onChange={(value) => {
              setCurrentLocation(
                value
                  ? {
                      name: value.label.toString(),
                      pageID: value.value.toString(),
                    }
                  : null
              );
            }}
          />
          {error != null && (
            <div className="text-danger font-body-sm mt-2">{error}</div>
          )}
        </Credenza.Body>
        <Credenza.Footer>
          <Button
            onClick={() => {
              onSaveLocation(currentLocation);
              setOpen(false);
            }}
          >
            Save
          </Button>
        </Credenza.Footer>
      </Credenza.Root>
    </div>
  );
};

export default AddLocationModal;
