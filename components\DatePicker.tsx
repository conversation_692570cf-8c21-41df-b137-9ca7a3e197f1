import classnames from "classnames";
import * as React from "react";
import { DayPicker } from "react-day-picker";
import Popover from "~/components/ui/Popover";

import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { DateTime } from "luxon";
import { trpc } from "~/utils";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

const Calendar = ({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) => {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={classnames("p-3", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4 font-eyebrow",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "font-body-sm font-medium",
        nav: "space-x-1 flex items-center",
        nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex",
        head_cell:
          "text-medium-dark-gray rounded-md w-9 font-normal text-[0.8rem]",
        row: "flex w-full mt-2",
        cell: "text-center font-body-sm p-0 relative [&:has([aria-selected])]:bg-slate transition-colors first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20 rounded-md",
        day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100 rounded-md hover:bg-slate hover:text-black transition-colors",
        day_selected:
          "text-white bg-basic hover:bg-basic focus:bg-primary outline-hidden",
        day_today: "text-secondary",
        day_outside: "text-medium-gray",
        day_disabled:
          "cursor pointer text-medium-gray opacity-50 hover:bg-white hover:text-medium-gray",
        day_range_middle:
          "aria-selected:bg-slate aria-selected:text-slate-foreground",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: ({ ...props }) => (
          <div className="hover:bg-lightest-gray-30 rounded-md p-1.5">
            <ChevronLeftIcon className="h-4 w-4" />
          </div>
        ),
        IconRight: ({ ...props }) => (
          <div className="hover:bg-lightest-gray-30 rounded-md p-1.5">
            <ChevronRightIcon className="h-4 w-4" />
          </div>
        ),
      }}
      {...props}
    />
  );
};
Calendar.displayName = "Calendar";

type SingleProps = {
  mode?: "single";
  value?: Date | string | null;
} & (
  | {
      isClearable: true;
      onChange: (date: Date | null) => void;
    }
  | {
      isClearable?: false;
      onChange: (date: Date) => void;
    }
);

type Props = {
  mode?: "single";
  /** The placeholder text that will be displayed when the input is empty */
  placeholder?: string;
  /** Called when the input is blurred */
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  /** Give the DatePicker a disabled prop */
  isDisabled?: boolean;
  /** Give the DatePicker an invalid prop */
  isInvalid?: boolean;
  /** If the DatePicker can be clearned */
  isClearable?: boolean;
  /** The date format string that is used to display a date to the use.
   *  This should be a Luxon format https://moment.github.io/luxon/#/formatting?id=table-of-tokens */
  dateFormat?: string;
  /** If the bottom border is shown always or just on hover */
  showBorder?: boolean;
  /** The minimum date that can be selected */
  minDate?: Date;
  /** The maximum date that can be selected */
  maxDate?: Date;
  displayStyle?: "underline" | "border";
  /** Used to filter calendar dates, like 'no mondays' */
  filterDate?: (date: Date) => boolean;
} & SingleProps;

const DatePicker = ({
  value,
  onChange,
  mode = "single",
  placeholder = "MM/DD/YYYY",
  onBlur,
  isDisabled = false,
  isClearable = false,
  dateFormat = "MM/dd/yy",
  showBorder = true,
  minDate,
  maxDate,
  displayStyle = "underline",
  filterDate,
}: Props) => {
  const dateValue = new Date(value || "");
  const { data } = trpc.user.getUserAndPreferences.useQuery();
  const user = data?.user;

  return (
    <Popover.Root>
      <Popover.Trigger
        className="group flex w-full items-start"
        disabled={isDisabled}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <div
          className={classnames(
            "data-[state=open]:border-secondary flex w-full items-center gap-1 transition-colors",
            {
              "focus-within:border-secondary group-hover:border-light-gray border-b":
                displayStyle === "underline",
              "group-hover:border-basic rounded-full border-[0.25px] px-3 py-0.5":
                displayStyle === "border",
              "border-light-gray": showBorder,
              "border-transparent": !showBorder,
            }
          )}
        >
          <div
            className={classnames(
              "font-eyebrow focus:border-secondary w-full bg-transparent pl-0.5 text-left whitespace-nowrap outline-hidden",
              {
                "mt-1 pb-1": displayStyle === "underline",
                "text-black": !!value,
                "text-medium-dark-gray": !value,
              }
            )}
          >
            {value
              ? DateTime.fromJSDate(new Date(value)).toFormat(dateFormat)
              : placeholder}
          </div>
        </div>
      </Popover.Trigger>
      <Popover.Content
        className="w-auto p-0"
        onClick={(e) => e.stopPropagation()}
      >
        <Calendar
          defaultMonth={!!value ? dateValue : undefined}
          mode={mode}
          required={!isClearable}
          weekStartsOn={user?.startOfWeek === "Sunday" ? 0 : 1}
          selected={!!value ? dateValue : undefined}
          disabled={
            minDate
              ? {
                  from: new Date(0),
                  to: DateTime.fromJSDate(minDate)
                    .minus({ days: 1 })
                    .toJSDate(),
                }
              : undefined
          }
          onSelect={(date) => {
            if (mode === "single" && !isClearable) {
              onChange(date as Date);
            } else if (mode === "single" && isClearable) {
              // @ts-expect-error
              onChange(date || null);
            }
          }}
        />
      </Popover.Content>
    </Popover.Root>
  );
};

export default DatePicker;
