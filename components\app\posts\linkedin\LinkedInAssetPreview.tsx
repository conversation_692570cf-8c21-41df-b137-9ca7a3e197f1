import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { useElementSize } from "@mantine/hooks";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { Player } from "video-react";
import getVideoProperties from "~/utils/getVideoProperties";

type Props = {
  documentTitle: string | null;
  assets: {
    name: string;
    signedUrl: string;
    metadata: {
      mimetype: string;
    };
    width?: number | null;
    height?: number | null;
    createdAt: Date;
  }[];
  coverPhotoUrl?: string;
  captionsUrl?: string;
};

const LinkedInAssetPreview = ({
  documentTitle,
  assets,
  coverPhotoUrl,
  captionsUrl,
}: Props) => {
  const { ref, height } = useElementSize();

  const [numPages, setNumPages] = useState<number>(1);
  const [isHovering, setIsHovering] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);

  const [videoDimensions, setVideoDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);

  const numAssets = assets.length;

  useEffect(() => {
    const getVideoDimensions = async () => {
      const videoAsset = assets[0];
      if (videoAsset && videoAsset.metadata.mimetype.startsWith("video")) {
        const { width, height } = await getVideoProperties(
          videoAsset.signedUrl
        );

        setVideoDimensions({
          width,
          height,
        });
      }
    };

    getVideoDimensions();
  }, [numAssets]);

  if (numAssets === 0) {
    return null;
  } else if (numAssets === 1) {
    const asset = assets[0];
    const isVideo = asset.metadata.mimetype.startsWith("video");
    const isPDF = asset.metadata.mimetype === "application/pdf";
    if (isVideo) {
      if (
        videoDimensions == null ||
        videoDimensions.width / videoDimensions.height > 0.8
      ) {
        return <Player src={asset.signedUrl} poster={coverPhotoUrl}></Player>;
      } else {
        return (
          <div className="relative h-full overflow-clip">
            <div className="absolute">
              <video
                className="-translate-y-[15%] scale-110 blur-xl"
                src={asset.signedUrl}
                poster={coverPhotoUrl}
              />
            </div>
            <div className="mx-auto w-[70%]">
              <Player src={asset.signedUrl} poster={coverPhotoUrl}></Player>
            </div>
          </div>
        );
      }
    } else if (isPDF) {
      pdfjs.GlobalWorkerOptions.workerSrc = new URL(
        "pdfjs-dist/build/pdf.worker.min.js",
        import.meta.url
      ).toString();

      return (
        <div
          className="group relative flex items-center justify-center"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
        >
          <div>
            <AnimatePresence>
              {isHovering && pageNumber === 1 && (
                <motion.div
                  className={classNames(
                    "font-body absolute top-0 z-10 flex w-[540px] items-center justify-start bg-black/60 px-4 text-left font-medium text-white opacity-0 transition-opacity group-hover:opacity-100"
                  )}
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 40 }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.4 }}
                >
                  {documentTitle || asset.name.split(".")[0]}
                  <span className="font-body-sm ml-1">• {numPages} pages</span>
                </motion.div>
              )}
            </AnimatePresence>
            <button
              disabled={pageNumber === 1}
              onClick={() => setPageNumber(pageNumber - 1)}
              className={classNames(
                "absolute inset-y-1/2 left-0 z-10 flex h-8 w-8 items-center justify-center rounded-full bg-black/60 opacity-0 transition-all hover:bg-black",
                { "group-hover:opacity-100": pageNumber !== 1 }
              )}
            >
              <ChevronLeftIcon className="h-4 w-4 text-white" />
            </button>
            <button
              disabled={pageNumber === numPages}
              onClick={() => setPageNumber(pageNumber + 1)}
              className={classNames(
                "absolute inset-y-1/2 right-0 z-10 flex h-8 w-8 items-center justify-center rounded-full bg-black/60 opacity-0 transition-all hover:bg-black",
                {
                  "group-hover:opacity-100": pageNumber !== numPages,
                }
              )}
            >
              <ChevronRightIcon className="h-4 w-4 text-white" />
            </button>
          </div>
          <Document
            file={assets[0].signedUrl}
            onLoadSuccess={(document) => {
              setNumPages(document.numPages);
            }}
          >
            <Page
              pageNumber={pageNumber}
              width={540}
              renderTextLayer={false}
              renderAnnotationLayer={false}
            />
          </Document>
        </div>
      );
    } else {
      return (
        <img
          className="mx-auto max-h-[713px] w-auto"
          src={assets[0].signedUrl}
        />
      );
    }
  } else if (numAssets === 2) {
    // Calculate aspect ratios and relative widths
    const aspectRatios = assets.map(
      (asset) => (asset.width || 1) / (asset.height || 1)
    );
    const totalAspectRatio = aspectRatios.reduce(
      (sum, ratio) => sum + ratio,
      0
    );

    // Calculate proportional widths
    const widthPercentages = aspectRatios.map(
      (ratio) => (ratio / totalAspectRatio) * 100
    );

    return (
      <div className="flex w-[540px] gap-0.5">
        {assets.map((asset, index) => (
          <div
            key={index}
            className="relative"
            style={{
              width: `${widthPercentages[index]}%`,
              maxHeight: "500px",
            }}
          >
            <img src={asset.signedUrl} className="h-auto w-full" alt="" />
          </div>
        ))}
      </div>
    );
  } else {
    const firstImage = assets[0];
    const remainingImages = assets.slice(1, 4);
    const missingImages = assets.slice(4).length;
    const firstImageAspectRatio =
      (firstImage.width || 1) / (firstImage.height || 1);
    const aspectRatios = remainingImages.map(
      (asset) => (asset.width || 1) / (asset.height || 1)
    );
    if (firstImageAspectRatio > 4 / 3) {
      // Vertical layout: First image on top, rest below
      const widestAspectRatio = Math.max(...aspectRatios);
      return (
        <div className="flex w-[540px] flex-col gap-0.5">
          <div className="w-full">
            <img src={assets[0].signedUrl} className="h-auto w-full" />
          </div>
          <div className="flex gap-0.5">
            {remainingImages.map((image, index) => {
              return (
                <div
                  key={image.signedUrl}
                  className="relative flex flex-1 items-center justify-center overflow-hidden"
                  style={{
                    aspectRatio: widestAspectRatio,
                  }}
                >
                  <img src={image.signedUrl} className="h-auto w-full" />
                  {index === remainingImages.length - 1 &&
                    missingImages > 0 && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                        <span className="text-4xl font-medium text-white">
                          +{missingImages}
                        </span>
                      </div>
                    )}
                </div>
              );
            })}
          </div>
        </div>
      );
    } else {
      return (
        <div className="flex gap-0.5">
          <div ref={ref} className="max-w-[67%]">
            <img
              src={firstImage.signedUrl}
              className="h-auto max-h-[540px] w-auto"
            />
          </div>
          <div className="flex flex-1 flex-col gap-0.5" style={{ height }}>
            {remainingImages.map((image, index) => (
              <div
                className="relative flex items-center justify-center"
                key={image.signedUrl}
                style={{ height: height / remainingImages.length }}
              >
                <img
                  src={image.signedUrl}
                  className="h-full w-full object-cover"
                />
                {index === remainingImages.length - 1 && missingImages > 0 && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                    <span className="text-4xl font-medium text-white">
                      +{missingImages}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      );
    }
  }
};

export default LinkedInAssetPreview;
