import { convert } from "html-to-text";

const getTextFromUrl = async (url: string) => {
  const response = await fetch(url);

  const html = await response.text();

  return convert(html, {
    baseElements: {
      selectors: ["main"],
    },
    limits: {
      maxBaseElements: 1,
    },
    selectors: [
      {
        selector: "a",
        options: { ignoreHref: true },
      },
      { selector: "img", format: "skip" },
      {
        selector: "*.w-condition-invisible",
        format: "skip",
      },
    ],
  });
};

export default getTextFromUrl;
