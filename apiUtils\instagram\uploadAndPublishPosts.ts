import {
  Asset,
  InstagramContentType,
  InstagramIntegrationSource,
} from "@prisma/client";
import assert from "assert";
import invert from "lodash/invert";
import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import { db } from "~/clients/Prisma";
import Instagram from "~/clients/facebook/instagram/Instagram";
import { inngest } from "~/clients/inngest/inngest";
import { GENERIC_ERROR_MESSAGE } from "~/constants";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";
import { removeEmptyElems } from "~/utils";
import uploadInstagramAssets from "./uploadInstagramAssets";

type Post = {
  id: string;
  instagramIntegration: {
    id: string;
    accessToken: string;
    integrationSource: InstagramIntegrationSource;
    account: {
      id: string;
      externalID: string;
    };
  } | null;
  instagramContent: {
    id: string;
    coverPhoto: Asset | null;
    location: {
      pageID: string;
    } | null;
    assets: {
      id: string;
      position: number;
      asset: Asset;
      containerID: string | null;
      containerExpiresAt: Date | null;
      mimetype: string;
      tags: {
        id: string;
        username: string;
        x: number | null;
        y: number | null;
      }[];
    }[];
    copy: string;
    type: InstagramContentType;
    reelShareToFeed: boolean;
    collaborators: string[];
    engagements: {
      id: string;
      postDelaySeconds: number;
    }[];
  } | null;
  title: string;
  workspaceID: string;
  createdByID: string;
};

const uploadAndPublishPosts = async (
  post: Post
): Promise<ScheduledPostResult> => {
  const { instagramContent, instagramIntegration } = post;

  if (!instagramContent) {
    return {
      status: "Error",
      error: "No Instagram copy to post",
    };
  }

  if (!instagramIntegration) {
    return {
      status: "Error",
      error: "No Instagram integration",
    };
  }

  let assets = instagramContent.assets;

  if (instagramContent.assets.length === 0) {
    return {
      status: "Error",
      error: "No assets to upload for Instagram post",
    };
  }

  // Make sure at least one of the assets doesn't have a containerID or containerExpiresAt
  // is further out than 5 minutes (just to be safe) in the future
  if (
    assets.every(
      (asset) =>
        asset.containerID &&
        asset.containerExpiresAt &&
        DateTime.fromJSDate(asset.containerExpiresAt).diffNow().as("minutes") >
          5
    )
  ) {
    console.log(`Skip Uploading post:${post.id}: Assets already uploaded`);
    return {
      status: "Skipped",
    };
  }

  const instagram = new Instagram(instagramIntegration.accessToken, {
    platform: instagramIntegration.integrationSource,
  });
  const instagramAccountID = instagramIntegration.account.externalID;

  console.log(`Uploading post:${post.id}: Uploading assets`);
  const uploadResponse = await uploadInstagramAssets({
    ...post,
    instagramContent: { ...instagramContent, assets },
  });
  if (
    uploadResponse.status === "Error" ||
    uploadResponse.status === "NonRetriableError"
  ) {
    return {
      status: uploadResponse.status,
      error: uploadResponse.error,
    };
  } else if (uploadResponse.status === "Skipped") {
    return {
      status: "Skipped",
    };
  }

  console.log(`post:${post.id}: assets uploaded`);

  const assetIDToContainerID = uploadResponse.assetIDToContainerID;
  const containerIDToAssetID = invert(assetIDToContainerID);
  const sortedContainerIDs = removeEmptyElems(
    sortBy(assets, "position").map((asset) => assetIDToContainerID[asset.id])
  );

  let permalink: string | undefined = undefined;
  let instagramPostID: string | undefined = undefined;
  let error: string | null = null;
  let rawError: {} | undefined | null = null;
  if (instagramContent.type === InstagramContentType.Story) {
    const assetToStoryIDs: { [assetID: string]: string } = {};
    console.log(`post:${post.id}: publishing containers ${sortedContainerIDs}`);
    for await (const containerID of sortedContainerIDs) {
      let response = await instagram.publishPost({
        instagramAccountID,
        creationID: containerID,
        type: instagramContent.type,
      });

      // Retry before giving up and calling it an error
      if (response.errors) {
        console.log(`Retrying post:${post.id}: ${response.errors.join(". ")}`);

        response = await instagram.publishPost({
          instagramAccountID,
          creationID: containerID,
          type: instagramContent.type,
        });

        if (response.errors) {
          error = response.errors.join(". ");
          rawError = response.rawError;
          break;
        }
      }

      if (response.data.permalink !== "") {
        permalink = response.data.permalink;
      }

      assetToStoryIDs[containerIDToAssetID[containerID]] = response.data.id;
    }

    if (error == null) {
      await db.$transaction(async (tx) => {
        for await (const assetID of Object.keys(assetToStoryIDs)) {
          await tx.instagramAsset.update({
            where: {
              id: assetID,
            },
            data: {
              storyID: assetToStoryIDs[assetID],
            },
          });
        }
      });
    }
  } else {
    let creationID = assetIDToContainerID[assets[0].id];

    // If we're posting a carousel then create the carousel container first
    if (
      instagramContent.type === InstagramContentType.Feed &&
      assets.length > 1
    ) {
      const carouselResponse = await instagram.createCarouselContainer({
        instagramAccountID,
        caption: instagramContent.copy,
        collaborators: instagramContent.collaborators,
        containerIDs: sortedContainerIDs,
        locationID: instagramContent.location?.pageID,
      });

      if (carouselResponse.errors) {
        const error = carouselResponse.errors.join(". ");

        return {
          status: "Error",
          error,
        };
      } else {
        assert(carouselResponse.data);
        creationID = carouselResponse.data.id;
      }
    }

    let response = await instagram.publishPost({
      instagramAccountID,
      creationID,
      type: instagramContent.type,
    });

    // Retry before giving up and calling it an error
    if (response.errors) {
      console.log(`Retrying post:${post.id}: ${response.errors.join(". ")}`);
      response = await instagram.publishPost({
        instagramAccountID,
        creationID,
        type: instagramContent.type,
      });

      if (response.errors) {
        error = response.errors.join(". ");
        rawError = response.rawError;
      }
    }

    if (response.data) {
      const igPost = response.data;
      permalink = igPost.permalink;
      const { username, ...rest } = igPost;
      instagramPostID = igPost.id;
      console.log("Published IG Post", rest.id, rest.permalink);
      await db.instagramPost.create({
        data: {
          ...rest,
          accountID: instagramIntegration.account.id,
        },
      });

      try {
        const { engagements } = instagramContent;

        const events = engagements.map((engagement) => {
          return {
            name: "instagram.postEngagement" as const,
            data: {
              externalPostID: instagramPostID as string,
              engagementID: engagement.id,
              delayUntil: DateTime.now()
                .plus({
                  seconds: engagement.postDelaySeconds,
                })
                .toJSDate(),
              permalink: permalink as string,
            },
          };
        });

        if (events.length) {
          await inngest.send(events);
        }
      } catch (error) {
        console.error(error);
      }
    }
  }

  if (error == null && permalink != null) {
    return {
      status: "Success",
      postUrl: permalink,
    };
  } else {
    return {
      status: "Error",
      error: error || GENERIC_ERROR_MESSAGE,
      rawError,
    };
  }
};

export default uploadAndPublishPosts;
