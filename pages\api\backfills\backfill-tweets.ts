import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

import { removeEmptyElems } from "~/utils";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const posts = await db.post.findMany({
    where: {
      twitterLink: {
        not: null,
      },
      tweetID: null,
    },
    select: {
      id: true,
      twitterLink: true,
      workspaceID: true,
    },
  });

  const tweetIDs = removeEmptyElems(
    posts.map((post) => post.twitterLink!.split("/").pop())
  );

  const tweets = await db.tweet.findMany({
    where: {
      id: {
        in: tweetIDs,
      },
    },
  });

  const foundPosts = posts.filter((post) =>
    tweets.some((tweet) => tweet.id === post.twitterLink!.split("/").pop()!)
  );

  for await (const post of foundPosts) {
    const tweetID = post.twitterLink!.split("/").pop()!;

    console.log(post.id, tweetID);
    await db.post.update({
      where: {
        id: post.id,
      },
      data: {
        tweetID,
      },
    });
  }

  res.status(200).send({
    numPosts: foundPosts.length,
  });
};

export default handler;
