import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";
import LinkedIn from "~/clients/linkedin/LinkedIn";
import LinkedInScraper from "~/clients/rapid_api/LinkedInScraper";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const integrations = await db.linkedInIntegration.findMany({
    where: {
      account: {
        vanityName: {
          in: ["and<PERSON><PERSON><PERSON><PERSON><PERSON>", "danielschieferdecker"],
        },
      },
    },
    select: {
      id: true,
      accessToken: true,
      account: {
        select: {
          vanityName: true,
          type: true,
          urn: true,
        },
      },
    },
  });

  const scraper = new LinkedInScraper();

  for await (const integration of integrations) {
    const linkedIn = new LinkedIn(integration.accessToken);

    let paginationToken = null;

    for (let page = 0; page < 5; page++) {
      const postsResponse = await scraper.profile.getPosts(
        integration.account.vanityName,
        integration.account.type,
        {
          page,
          token: paginationToken,
        }
      );

      if (postsResponse.success === false) {
        console.log(
          `${integration.account.vanityName}: ${postsResponse.errors}`
        );
        break;
      }

      console.log(
        `${integration.account.vanityName}: page ${
          page + 1
        } of 5, paginationToken: ${paginationToken}`
      );

      const posts = postsResponse.data.posts;
      paginationToken = postsResponse.data.paginationToken;

      const nonRepostedPosts = posts.filter((post) => post.type !== "Repost");

      const postActivityUrns = nonRepostedPosts.map((post) => post.activityUrn);

      const postUrnPostResponse =
        await linkedIn.getPostUrnsByActivityUrns(postActivityUrns);

      if (postUrnPostResponse.success === false) {
        console.log(
          `${integration.account.vanityName}: ${postUrnPostResponse.errors}`
        );
        break;
      }

      const postUrnMap = postUrnPostResponse.data;

      const foundPosts = nonRepostedPosts.filter(
        (post) => postUrnMap[post.activityUrn]
      );

      const postPromises = foundPosts.map(async (post) => {
        const id = postUrnMap[post.activityUrn];

        await db.linkedInPost.upsert({
          where: {
            id,
          },
          create: {
            id,
            activityUrn: post.activityUrn,
            type: post.type,
            authorUrn: integration.account.urn,
            commentary: post.commentary,
            personTagCount: post.personTagCount,
            organizationTagCount: post.organizationTagCount,
            hashtagCount: post.hashtagCount,
            publishedAt: post.publishedAt,
            postCreatedAt: post.publishedAt,
            rawResponse: post,
            isScrapedData: true,
            visibility: "PUBLIC",
            isAdvertisement: false,
          },
          update: {
            activityUrn: post.activityUrn,
            commentary: post.commentary,
          },
        });
      });

      await Promise.all(postPromises);
    }
  }

  res.status(200).send({
    numIntegrations: integrations.length,
  });
};

export default handler;
