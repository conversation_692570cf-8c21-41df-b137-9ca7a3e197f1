import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";
import { db } from "~/clients";

const emails = [
  "<EMAIL>",
  "kather<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "mi<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com",
];

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { count } = await db.userWorkspace.deleteMany({
    where: {
      user: {
        email: {
          in: emails,
        },
      },
    },
  });

  res.status(StatusCodes.OK).send({ count });
};

export default handler;
