import getLinkMetadata from "./getLinkMetadata";

const scrapeInstagramInfo = async (
  username: string
): Promise<{
  name: string | null;
  username: string;
  followerCount: number | null;
  followingCount: number | null;
  numPosts: number | null;
  profileImageUrl: string | null;
  isPrivate: boolean;
} | null> => {
  const metadata = await getLinkMetadata(
    `https://www.instagram.com/${username}/`
  );

  console.log("Instagram Metadata", metadata);

  if (!metadata || metadata.title === "Instagram") {
    return null;
  }

  const name =
    metadata.title
      ?.split(" (")[0]
      .trim()
      .replace(" • Instagram photos and videos", "") || null;
  const statsRegex =
    /([\d,MK]+) Followers, ([\d,K]+) Following, ([\d,K]+) Posts/;
  const stats = metadata.description?.match(statsRegex);

  // For stats we have to remove commas and convert K to 000 and M to 000000
  const stringFollowers =
    stats?.[1].replace(/,/g, "")?.replace("K", "000")?.replace("M", "000000") ||
    null;
  const followerCount =
    stringFollowers != "0" && stringFollowers != null
      ? parseInt(stringFollowers)
      : null;
  const isPrivate = stringFollowers != null ? stringFollowers === "0" : true;

  const stringFollowing =
    stats?.[2].replace(/,/g, "")?.replace("K", "000") || null;
  const followingCount = stringFollowing ? parseInt(stringFollowing) : null;
  const stringPosts = stats?.[3].replace(/,/g, "")?.replace("K", "000") || null;
  const numPosts = stringPosts ? parseInt(stringPosts) : null;

  const profileImageUrl = metadata.image || null;

  return {
    name,
    username,
    followerCount,
    followingCount,
    numPosts,
    profileImageUrl,
    isPrivate,
  };
};

export default scrapeInstagramInfo;
