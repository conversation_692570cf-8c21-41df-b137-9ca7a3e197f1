import completeOnboardingTask from "apiUtils/completeOnboardingTask";
import countSocialProfiles from "apiUtils/countSocialProfiles";
import encrypt from "apiUtils/encrypt";
import addSubscriptionItem from "apiUtils/stripe/addSubscriptionItem";
import normalizeTwitterUser from "apiUtils/twitter/normalizeTwitterUser";
import { StatusCodes } from "http-status-codes";
import { DateTime } from "luxon";
import type { NextApiRequest, NextApiResponse } from "next";
import getUserWorkspaceWithSocialProfileUsage from "server/routers/utils/getUserWorkspaceWithSocialProfileUsage";
import { TwitterApi } from "twitter-api-v2";
import { slack } from "~/clients";
import prisma, { db } from "~/clients/Prisma";
import createSupabaseServerClient from "~/clients/createSupabaseServerClient";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { getCookie } from "~/utils/cookies";

const handleError = (
  res: NextApiResponse,
  message: string,
  referrer: string
) => {
  const params = new URLSearchParams({
    error_message: message,
    channel: "𝕏",
  });

  const url = `${referrer}?${params}`;

  return res.redirect(StatusCodes.MOVED_TEMPORARILY, url);
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res
      .status(StatusCodes.METHOD_NOT_ALLOWED)
      .json({ message: "Method not allowed" });
  }

  const twitterIntegrationCookies = getCookie(
    "TWITTER_INTEGRATION",
    req.headers.cookie
  );

  const { oauth_token: oauthToken, oauth_verifier: oauthVerifier } = req.query;

  if (!twitterIntegrationCookies) {
    return handleError(
      res,
      "Session expired, please try again",
      "/settings/profiles"
    );
  }

  const { oAuthTokenSecret, accountID, referrer } = twitterIntegrationCookies;

  if (oauthToken !== twitterIntegrationCookies.oAuthToken) {
    return handleError(
      res,
      "Authorization request expired please try again",
      referrer
    );
  }

  const supabase = await createSupabaseServerClient(req.headers.cookie);

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const workspace = await getUserWorkspaceWithSocialProfileUsage({
    user,
    currentWorkspaceID: getCookie("CURRENT_WORKSPACE_ID", req.headers.cookie),
  });

  if (!user || !workspace) {
    return handleError(res, "User not logged in", referrer);
  }

  try {
    const client = new TwitterApi({
      appKey: process.env.TWITTER_API_KEY!,
      appSecret: process.env.TWITTER_API_SECRET!,
      accessToken: oauthToken,
      accessSecret: oAuthTokenSecret,
    });

    const response = await client.login(oauthVerifier as string);
    const accessToken = encrypt(response.accessToken);
    const accessSecret = encrypt(response.accessSecret);

    const loggedClient = response.client;

    const { data: twitterUser } = await loggedClient.v2.me({
      "user.fields": [
        "id",
        "profile_image_url",
        "username",
        "verified",
        "verified_type",
        "url",
        "description",
        "location",
        "public_metrics",
        "protected",
        "created_at",
      ],
    });

    const normalizedUser = normalizeTwitterUser(twitterUser);

    if (accountID && normalizedUser.id !== accountID) {
      const expectedTwitterAccount = await db.twitterAccount.findFirst({
        where: {
          id: accountID,
        },
      });

      if (expectedTwitterAccount) {
        return handleError(
          res,
          `Switch to @${expectedTwitterAccount.username} on Twitter first and then try again`,
          referrer
        );
      } else {
        return handleError(
          res,
          "Please login with the account you want to connect first before integrating",
          referrer
        );
      }
    }

    const existingTwitterAccount = await db.twitterAccount.findFirst({
      where: {
        id: twitterUser.id,
      },
      include: {
        integrations: true,
      },
    });

    const existingIntegration = existingTwitterAccount?.integrations[0];

    if (
      existingIntegration &&
      existingIntegration.workspaceID !== workspace.id
    ) {
      return handleError(
        res,
        `@${existingTwitterAccount?.username} is already integrated on another Assembly workspace. Message support if this is a mistake.`,
        referrer
      );
    }

    const twitterAccount = await db.twitterAccount.upsert({
      where: {
        id: normalizedUser.id,
      },
      create: {
        id: normalizedUser.id,
        username: normalizedUser.username,
        description: normalizedUser.description,
        name: normalizedUser.name,
        profileImageUrl: normalizedUser.profileImageUrl,
        verified: normalizedUser.verified,
        verifiedType: normalizedUser.verifiedType,
        accountCreatedAt: normalizedUser.accountCreatedAt,
      },
      update: {
        username: normalizedUser.username,
        description: normalizedUser.description,
        name: normalizedUser.name,
        profileImageUrl: normalizedUser.profileImageUrl,
        verified: normalizedUser.verified,
        verifiedType: normalizedUser.verifiedType,
        accountCreatedAt: normalizedUser.accountCreatedAt,
      },
    });

    if (existingIntegration) {
      await db.twitterIntegration.update({
        where: {
          id: existingIntegration.id,
        },
        data: {
          accessToken,
          accessSecret,
          refreshToken: accessSecret,
          expiresAt: DateTime.now().plus({ years: 100 }).toJSDate(),
          accountID: twitterAccount.id,
          userID: user.id,
          workspaceID: workspace.id,
          reintegrationNeeded: false,
        },
      });
    } else {
      const { subscription } = workspace;

      if (subscription) {
        const numSocialProfiles = countSocialProfiles(workspace);

        if (numSocialProfiles >= subscription.socialProfileLimit) {
          await addSubscriptionItem({
            workspaceID: workspace.id,
            itemName: "ADDITIONAL_SOCIAL_PROFILE",
            quantity: numSocialProfiles - subscription.socialProfileLimit + 1,
          });
        }
      }

      const twitterIntegration = await db.twitterIntegration.create({
        data: {
          accessToken,
          accessSecret,
          refreshToken: accessSecret,
          expiresAt: DateTime.now().plus({ years: 100 }).toJSDate(),
          accountID: twitterAccount.id,
          userID: user.id,
          workspaceID: workspace.id,
        },
        include: {
          user: true,
        },
      });

      // Update old posts to point to the new integration
      const deletedIntegrations = await prisma.twitterIntegration.findMany({
        where: {
          accountID: twitterAccount.id,
          workspaceID: workspace.id,
          deletedTimeStamp: {
            not: 0,
          },
        },
      });

      if (deletedIntegrations.length > 0) {
        await db.post.updateMany({
          where: {
            twitterIntegrationID: {
              in: deletedIntegrations.map((integration) => integration.id),
            },
          },
          data: {
            twitterIntegrationID: twitterIntegration.id,
          },
        });
      }

      posthog.capture({
        distinctId: user.id,
        event: POSTHOG_EVENTS.socialAccount.connected,
        properties: {
          channel: "Twitter",
        },
        groups: {
          workspace: workspace.id,
          company: workspace.company.id,
        },
      });

      await slack.newConnectedAccount({
        username: `@${twitterAccount.username}`,
        channel: "Twitter",
        url: `https://twitter.com/${twitterAccount.username}`,
        workspace,
        connectedBy: twitterIntegration.user,
      });

      if (workspace._count.twitterIntegrations === 0) {
        await db.postDefault.upsert({
          where: {
            workspaceID: workspace.id,
          },
          create: {
            workspaceID: workspace.id,
            twitterIntegrationID: twitterIntegration.id,
          },
          update: {
            twitterIntegrationID: twitterIntegration.id,
          },
        });
      }
    }

    await completeOnboardingTask({
      workspaceID: workspace.id,
      userID: user.id,
      taskType: "CONNECT_ACCOUNT",
      state: "COMPLETED",
    });

    const params = new URLSearchParams({
      account_name: `@${normalizedUser.username}`,
      channel: "Twitter",
      flow: accountID
        ? "reconnect_account"
        : existingIntegration
          ? "existing_account"
          : "new_account",
    });

    if (referrer.includes("/settings/profiles")) {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/profiles?${params}`
      );
    } else {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/x?${params}`
      );
    }
  } catch (error) {
    console.error("Twitter auth error:", error);
    return handleError(res, "Error connecting Twitter account", referrer);
  }
}
