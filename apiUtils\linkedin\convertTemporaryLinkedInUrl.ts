import LinkedIn from "~/clients/linkedin/LinkedIn";
import getLinkedInProfilePermalink from "~/utils/linkedin/getLinkedInProfilePermalink";

export const convertTemporaryLinkedInUrl = async (
  temporaryUrl: string
): Promise<string | null> => {
  try {
    // Extract the ID from the temporary URL
    const match = temporaryUrl.match(/\/in\/([^\/]+)/);
    if (!match) return null;

    const tempId = match[1];

    // Initialize LinkedIn client with your access token
    const linkedin = new LinkedIn(process.env.LINKEDIN_ACCESS_TOKEN as string);

    // Use the LinkedIn API to get profile information
    const response = await linkedin.getPersonByVanityName({
      vanityName: tempId,
      organizationUrn: "urn:li:organization:92694597", // You'll need to replace this with your organization URN
    });

    if (response.success && response.data) {
      return getLinkedInProfilePermalink({
        vanityName: response.data.vanityName,
        type: "User",
      });
    }

    return null;
  } catch (error) {
    console.error("Error converting LinkedIn URL:", error);
    return null;
  }
};
