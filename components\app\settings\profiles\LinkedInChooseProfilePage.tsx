import { EngagementType } from "@prisma/client";
import { useRouter } from "next/router";
import { useQueryState } from "nuqs";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Checkbox, Divider } from "~/components";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import Avatar from "~/components/ui/Avatar";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import RadioGroup from "~/components/ui/RadioGroup";
import Skeleton from "~/components/ui/Skeleton";
import { useWindowTitle } from "~/hooks";
import { handleTrpcError, trpc } from "~/utils";

type Props = {
  type: "scheduling" | "engagement";
};

type Profile = {
  urn: string;
  name: string;
  vanityName: string;
  profileImageUrl: string;
  isDisabled?: boolean;
  disabledMessage?: string;
};

const LinkedInChooseProfilePage = ({ type }: Props) => {
  useWindowTitle("LinkedIn Integration");
  const router = useRouter();
  const trpcUtils = trpc.useUtils();
  const [code] = useQueryState("auth_code");
  const [selectedProfileUrn, setSelectedProfileUrn] = useState<string | null>(
    null
  );

  const authorizedProfilesQuery =
    trpc.integration.linkedin.getAuthorizedProfiles.useQuery(
      {
        code,
      },
      {
        enabled: !!code,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        refetchInterval: false,
      }
    );

  useEffect(() => {
    if (authorizedProfilesQuery.isError) {
      toast.error("Error Authorizing LinkedIn Profile", {
        description: authorizedProfilesQuery.error.message,
      });
      router.push(`/settings/profiles/${type}/linkedin`);
    }
  }, [authorizedProfilesQuery.isError]);

  const integratedProfileName = authorizedProfilesQuery.data?.name;
  const profiles = authorizedProfilesQuery.data?.profiles ?? [];

  const connectProfileMutation =
    trpc.integration.linkedin.connectProfile.useMutation({
      onSuccess: (data) => {
        toast.success("LinkedIn Profile Connected", {
          description: `Connected ${data.account.name}`,
        });
        trpcUtils.workspace.getSchedulingProfiles.refetch();
        router.push(`/settings/profiles`);
      },
      onError: (error) => {
        handleTrpcError({
          title: "Error Connecting Profile",
          error,
        });
      },
    });

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      {!selectedProfileUrn ? (
        <ChooseProfile
          type={type}
          profiles={profiles}
          isLoading={authorizedProfilesQuery.isPending}
          integratedProfileName={integratedProfileName}
          onCancel={() => {
            router.push(`/settings/profiles/${type}/linkedin`);
          }}
          onConnect={(selectedProfileUrn) => {
            if (type === "engagement") {
              setSelectedProfileUrn(selectedProfileUrn);
            } else {
              const {
                accessToken,
                accessTokenExpiresAt,
                refreshToken,
                refreshTokenExpiresAt,
              } = authorizedProfilesQuery.data!;

              connectProfileMutation.mutate({
                profileUrn: selectedProfileUrn,
                accessToken,
                accessTokenExpiresAt,
                refreshToken,
                refreshTokenExpiresAt,
                type: "Scheduling",
              });
            }
          }}
          isConnecting={connectProfileMutation.isPending}
        />
      ) : (
        <EngagementPermissioms
          onCancel={() => {
            setSelectedProfileUrn(null);
          }}
          onConnect={(engagementPermissions) => {
            const {
              accessToken,
              accessTokenExpiresAt,
              refreshToken,
              refreshTokenExpiresAt,
            } = authorizedProfilesQuery.data!;

            connectProfileMutation.mutate({
              profileUrn: selectedProfileUrn,
              accessToken,
              accessTokenExpiresAt,
              refreshToken,
              refreshTokenExpiresAt,
              type: "Engagement",
              engagementPermissions,
            });
          }}
          isConnecting={connectProfileMutation.isPending}
        />
      )}
    </SettingsLayout.Root>
  );
};

type ChooseProfileProps = {
  type: "scheduling" | "engagement";
  profiles: Profile[];
  isLoading: boolean;
  integratedProfileName?: string;
  onCancel: () => void;
  onConnect: (selectedProfileUrn: string) => void;
  isConnecting: boolean;
};

const ChooseProfile = ({
  type,
  profiles,
  isLoading,
  integratedProfileName,
  onCancel,
  onConnect,
  isConnecting,
}: ChooseProfileProps) => {
  const [selectedProfileUrn, setSelectedProfileUrn] = useState<string | null>(
    null
  );

  return (
    <ConnectProfile.Root>
      <ConnectProfile.Header
        channel="LinkedIn"
        title="Select which profile to connect"
        description="Select a profile you'd like to connect to Assembly"
      />
      <ConnectProfile.Content className="mt-4 space-y-2">
        {isLoading || profiles.length === 0 ? (
          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div key={i}>
                <div className="flex w-full items-center justify-between p-2">
                  <div className="flex items-center">
                    <Skeleton className="border-medium-gray h-3 w-3 rounded-full border bg-white" />
                    <div className="ml-5 flex items-center gap-2">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                  </div>
                  <Skeleton className="h-4 w-28" />
                </div>
                <Divider className="my-2 md:my-4" />
              </div>
            ))}
          </div>
        ) : (
          <RadioGroup.Root
            className="max-h-[460px] flex-col flex-nowrap gap-0 overflow-y-auto"
            orientation="vertical"
            onValueChange={(value) => {
              setSelectedProfileUrn(value);
            }}
          >
            {profiles.map((profile) => (
              <div key={profile.urn}>
                <RadioGroup.Item
                  value={profile.urn}
                  disabled={profile.isDisabled}
                  className="w-full"
                >
                  <div className="flex items-center justify-between">
                    <div className="ml-5 flex w-full items-center gap-x-2 transition-all">
                      <Avatar.Root className="h-8 w-8">
                        <Avatar.Image src={profile.profileImageUrl} />
                        <Avatar.Fallback>{profile.vanityName}</Avatar.Fallback>
                      </Avatar.Root>
                      <div className="font-body-sm">
                        <p className="font-medium">{profile.name}</p>
                        <p className="text-medium-dark-gray">
                          {profile.vanityName}
                        </p>
                      </div>
                    </div>
                    {profile.disabledMessage && (
                      <p className="text-dark-gray font-body-sm whitespace-nowrap">
                        {profile.disabledMessage}
                      </p>
                    )}
                  </div>
                </RadioGroup.Item>
                <Divider className="my-2 md:my-4" />
              </div>
            ))}
          </RadioGroup.Root>
        )}
        {profiles.length > 0 && (
          <div className="text-medium-dark-gray font-body-sm">
            Profiles available to{" "}
            <span className="font-medium">{integratedProfileName}</span> on
            LinkedIn to connect.
          </div>
        )}
      </ConnectProfile.Content>
      <div className="mt-2 flex flex-row justify-end md:gap-2.5">
        <Button variant="secondary" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          isLoading={isConnecting}
          onClick={() => {
            if (!selectedProfileUrn) {
              toast.error("Please select a profile", {
                description: "Select a profile to connect",
              });
              return;
            }
            onConnect(selectedProfileUrn);
          }}
        >
          {type === "scheduling" ? "Connect" : "Continue"}
        </Button>
      </div>
    </ConnectProfile.Root>
  );
};

type EngagementPermissionsProps = {
  onCancel: () => void;
  onConnect: (permissions: EngagementType[]) => void;
  isConnecting: boolean;
};

const EngagementPermissioms = ({
  onCancel,
  onConnect,
  isConnecting,
}: EngagementPermissionsProps) => {
  const [selectedPermissions, setSelectedPermissions] = useState<
    EngagementType[]
  >(["Like", "Comment", "Repost"]);

  return (
    <ConnectProfile.Root>
      <ConnectProfile.Header
        channel="LinkedIn"
        title="Select Auto-Engagement Permissions"
        description="Select which types of auto-engagements you’d like to be able to do from this profile."
      />
      <ConnectProfile.Content>
        <div className="flex flex-col gap-2">
          {Object.values(EngagementType).map((permission) => (
            <Checkbox
              key={permission}
              label={permission}
              value={selectedPermissions.includes(permission)}
              onChange={(checked) => {
                if (checked) {
                  setSelectedPermissions([...selectedPermissions, permission]);
                } else {
                  setSelectedPermissions(
                    selectedPermissions.filter((p) => p !== permission)
                  );
                }
              }}
            />
          ))}
        </div>
      </ConnectProfile.Content>
      <div className="mt-2 flex flex-row justify-end md:gap-2.5">
        <Button variant="secondary" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          isLoading={isConnecting}
          onClick={() => {
            if (selectedPermissions.length === 0) {
              toast.error("Please select at least one permission", {
                description: "Select at least one permission to connect",
              });
              return;
            }
            onConnect(selectedPermissions);
          }}
        >
          Connect
        </Button>
      </div>
    </ConnectProfile.Root>
  );
};

export default LinkedInChooseProfilePage;
