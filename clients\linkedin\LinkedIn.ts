import {
  LinkedInOrganizationRole,
  LinkedInOrganziationState,
  LinkedInPostType,
} from "@prisma/client";
import getAssetBuffer from "apiUtils/getAssetBuffer";
import getLinkMetadata from "apiUtils/getLinkMetadata";
import assert from "assert";
import axios, { AxiosInstance } from "axios";
import { fileTypeFromStream } from "file-type";
import FormData from "form-data";
import got from "got";
import { StatusCodes } from "http-status-codes";
import maxBy from "lodash/maxBy";
import sortBy from "lodash/sortBy";
import uniq from "lodash/uniq";
import { DateTime } from "luxon";
// @ts-ignore, no types for this https://github.com/t6tn4k/node-split
import { splitSync } from "node-split";
import { linkedinMentionRegex, urlRegex } from "~/constants";
import { RequireAtLeastOne } from "~/types/RequireAtLeastOne";
import { removeEmptyElems } from "~/utils";
import {
  Response,
  createErrorResponse,
  createSuccessResponse,
} from "../Response";
import catchLinkedInError from "./catchLinkedInError";
import { Post, RawLike, RawPost } from "./types";

type ImageElement = {
  data: {
    "com.linkedin.digitalmedia.mediaartifact.StillImage": {
      displaySize: {
        height: number;
        width: number;
      };
    };
  };
  identifiers: {
    identifier: string;
  }[];
};

// https://learn.microsoft.com/en-us/linkedin/marketing/community-management/organizations/organization-lookup-api?view=li-lms-2023-11&tabs=http#schema-information
type OrganizationType =
  | "PUBLIC_COMPANY"
  | "EDUCATIONAL"
  | "SELF_EMPLOYED"
  | "GOVERNMENT_AGENCY"
  | "NON_PROFIT"
  | "SELF_OWNED"
  | "PRIVATELY_HELD"
  | "PARTNERSHIP";
type PrimaryOrganizationType = "SCHOOL" | "BRAND" | "NONE";

class LinkedIn {
  BASE_URL = "https://api.linkedin.com/rest";
  V2_URL = "https://api.linkedin.com/v2";

  accessToken: string;
  client_rest_V202411: AxiosInstance;
  client_rest_V202506: AxiosInstance;

  client_v2: AxiosInstance;
  unversionedClient: AxiosInstance;

  constructor(accessToken: string) {
    this.client_rest_V202411 = axios.create({
      baseURL: this.BASE_URL,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "X-Restli-Protocol-Version": "2.0.0",
        "LinkedIn-Version": "202411", // November 2024
      },
    });

    this.client_rest_V202506 = axios.create({
      baseURL: this.BASE_URL,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "X-Restli-Protocol-Version": "2.0.0",
        "LinkedIn-Version": "202506", // June 2025
      },
    });

    this.client_v2 = axios.create({
      baseURL: this.V2_URL,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "X-Restli-Protocol-Version": "2.0.0",
      },
    });

    // https://stackoverflow.com/questions/59898687/fetching-comments-of-comments-with-the-linkedin-api
    this.unversionedClient = axios.create({
      baseURL: this.BASE_URL,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "LinkedIn-Version": "202411", // November 2024
      },
    });
    this.accessToken = accessToken;
  }

  static authorize = async (
    code: string
  ): Promise<
    Response<{
      accessToken: string;
      expiresAt: Date;
      refreshToken: string;
      refreshTokenExpiresAt: Date;
      scope: string[];
    }>
  > => {
    const params = new URLSearchParams({
      client_id: `${process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID}`,
      client_secret: `${process.env.LINKEDIN_CLIENT_SECRET}`,
      redirect_uri: `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/linkedin/callback`,
      code,
      grant_type: "authorization_code",
    });

    try {
      const now = DateTime.now();

      const { status, data } = await axios.post(
        `https://www.linkedin.com/oauth/v2/accessToken?${params}`
      );

      const authorization = {
        accessToken: data.access_token,
        expiresAt: now.plus({ seconds: data.expires_in }).toJSDate(),
        refreshToken: data.refresh_token,
        refreshTokenExpiresAt: now
          .plus({ seconds: data.refresh_token_expires_in })
          .toJSDate(),
        scope: data.scope.split(","),
      };

      return createSuccessResponse(authorization, StatusCodes.OK);
    } catch (error) {
      return catchLinkedInError(error, "Error authorizing user");
    }
  };

  static refreshAccessToken = async (
    refreshToken: string
  ): Promise<
    Response<{
      accessToken: string;
      expiresAt: Date;
      refreshToken: string;
      refreshTokenExpiresAt: Date;
      scope: string[];
    }>
  > => {
    const params = new URLSearchParams({
      client_id: `${process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID}`,
      client_secret: `${process.env.LINKEDIN_CLIENT_SECRET}`,
      refresh_token: refreshToken,
      grant_type: "refresh_token",
    });

    try {
      const { status, data } = await axios.post(
        `https://www.linkedin.com/oauth/v2/accessToken?${params}`
      );

      const now = DateTime.now();

      const authorization = {
        accessToken: data.access_token,
        expiresAt: now.plus({ seconds: data.expires_in }).toJSDate(),
        refreshToken: data.refresh_token,
        refreshTokenExpiresAt: now
          .plus({ seconds: data.refresh_token_expires_in })
          .toJSDate(),
        scope: data.scope.split(","),
      };

      return createSuccessResponse(authorization, StatusCodes.OK);
    } catch (error) {
      return catchLinkedInError(error, "Error refreshing access token");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/shared/authentication/token-introspection
  introspectToken = async (
    accessToken: string
  ): Promise<
    Response<{
      active: boolean;
      scope: string[];
      authorizedAt: Date;
      createdAt: Date;
      expiresAt: Date;
      status: "revoked" | "expired" | "active";
      authType: "2L" | "3L" | "Enterprise_User";
    }>
  > => {
    try {
      const { status, data } = await axios.post(
        `https://www.linkedin.com/oauth/v2/introspectToken`,
        {
          client_id: `${process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID}`,
          client_secret: `${process.env.LINKEDIN_CLIENT_SECRET}`,
          token: accessToken,
        },
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const rawData = data as {
        active: boolean;
        client_id: string;
        authorized_at: number;
        created_at: number;
        status: "revoked" | "expired" | "active";
        expires_at: number;
        scope: string;
        auth_type: "2L" | "3L" | "Enterprise_User";
      };

      const introspection = {
        active: rawData.active,
        scope: rawData.scope.split(","),
        authorizedAt: new Date(rawData.authorized_at * 1000),
        createdAt: new Date(rawData.created_at * 1000),
        expiresAt: new Date(rawData.expires_at * 1000),
        status: rawData.status,
        authType: rawData.auth_type,
      };

      return createSuccessResponse(introspection, status);
    } catch (error) {
      return catchLinkedInError(error, "Error introspecting token");
    }
  };

  _getProfileImageUrl = (
    elements: ImageElement[] | null
  ): { url: string | null; expiresAt: Date | null } => {
    if (!elements || elements.length === 0)
      return { url: null, expiresAt: null };

    const profilePictureElement = maxBy(
      elements,
      (element) =>
        element.data["com.linkedin.digitalmedia.mediaartifact.StillImage"]
          .displaySize.width
    );
    assert(profilePictureElement);

    const profileImageUrl = profilePictureElement?.identifiers[0].identifier;
    assert(profileImageUrl);

    const expiresAtRegex = /e=(\d+)/;
    const expiresAtMatch = expiresAtRegex.exec(profileImageUrl);
    assert(expiresAtMatch);
    const expiresAt = DateTime.fromSeconds(
      parseInt(expiresAtMatch[1])
    ).toJSDate();

    return { url: profileImageUrl, expiresAt };
  };

  // This one has to be March 2023 because it seems like they stopped returning the full
  // profile from the projection
  getMe = async (): Promise<
    Response<{
      id: string;
      urn: string;
      firstName: string;
      lastName: string;
      headline: string;
      vanityName: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
    }>
  > => {
    try {
      // https://learn.microsoft.com/en-us/linkedin/shared/integrations/people/profile-api#retrieve-current-members-profile
      const { status, data } = await this.client_v2.get(`/me`, {
        params: {
          projection:
            "(id,localizedFirstName,localizedLastName,localizedHeadline,vanityName,profilePicture(displayImage~digitalmediaAsset:playableStreams))",
        },
      });

      const profilePicture = data.profilePicture;

      const { url: profileImageUrl, expiresAt: profileImageExpiresAt } =
        this._getProfileImageUrl(
          profilePicture ? profilePicture["displayImage~"].elements : null
        );

      const user = {
        id: data.id,
        urn: `urn:li:person:${data.id}`,
        firstName: data.localizedFirstName,
        lastName: data.localizedLastName,
        headline: data.localizedHeadline,
        vanityName: data.vanityName,
        profileImageUrl: profileImageUrl,
        profileImageExpiresAt: profileImageExpiresAt,
      };

      return createSuccessResponse(user, status);
    } catch (error) {
      return catchLinkedInError(error, "Error getting current user");
    }
  };

  getMyFollowerCount = async (): Promise<
    Response<{ followerCount: number }>
  > => {
    try {
      const { status, data } = await this.client_rest_V202506.get(
        `/memberFollowersCount?q=me`
      );

      const followerCount = data.elements[0].memberFollowersCount;

      return createSuccessResponse({ followerCount }, status);
    } catch (error) {
      console.error(error);
      return catchLinkedInError(error, "Error getting my follower count");
    }
  };

  getPostImpressions = async (
    urn: string
  ): Promise<Response<{ impressions: number }>> => {
    try {
      // Determine if it's a share or ugcPost URN
      const isShare = urn.includes("share:");
      const prefix = isShare ? "share:" : "ugc:";

      const { status, data } = await this.client_rest_V202506.get(
        `/memberCreatorPostAnalytics?q=entity&queryType=IMPRESSION&entity=(${prefix}${encodeURIComponent(
          urn
        )})&aggregation=TOTAL`
      );

      const impressions = data.elements[0].count;

      return createSuccessResponse({ impressions }, status);
    } catch (error) {
      console.error(error);
      return catchLinkedInError(error, "Error getting post impressions");
    }
  };

  getPeopleByUrn = async (
    personUrns: string[]
  ): Promise<
    Response<
      {
        id: string;
        urn: string;
        firstName: string;
        lastName: string;
        headline: string;
        vanityName: string;
        profileImageUrl: string | null;
        profileImageExpiresAt: Date | null;
      }[]
    >
  > => {
    const ids = personUrns.map((urn) => `(id:${urn.split(":")[3]})`);

    // https://learn.microsoft.com/en-us/linkedin/shared/integrations/people/profile-api?context=linkedin%2Fmarketing%2Fcontext&view=li-lms-2023-03#retrieve-other-members-profile
    const params = new URLSearchParams({
      ids: `List(${ids.join(",")})`,
      projection:
        "(results*(id,localizedFirstName,localizedLastName,vanityName,profilePicture(displayImage~digitalmediaAsset:playableStreams)))",
    });

    const paramsWithParens = params
      .toString()
      .replaceAll(/%3A/g, ":")
      .replaceAll(/%28/g, "(")
      .replaceAll(/%29/g, ")")
      .replaceAll(/%2C/g, ",");

    try {
      // https://learn.microsoft.com/en-us/linkedin/shared/integrations/people/profile-api#retrieve-current-members-profile
      const { status, data } = await this.client_v2.get(
        `/people?${paramsWithParens}`
      );

      const peopleData = Object.values(data.results) as {
        id: string;
        localizedFirstName: string;
        localizedLastName: string;
        vanityName: string;
        localizedHeadline: string;
        profilePicture?: {
          "displayImage~": {
            elements: ImageElement[];
          };
        };
      }[];

      const people = removeEmptyElems(
        peopleData.map((person) => {
          if (person.id === "private") {
            return null;
          }

          const profilePicture = person.profilePicture;

          const { url: profileImageUrl, expiresAt: profileImageExpiresAt } =
            this._getProfileImageUrl(
              profilePicture ? profilePicture["displayImage~"].elements : null
            );

          return {
            id: person.id,
            urn: `urn:li:person:${person.id}`,
            firstName: person.localizedFirstName?.trim(),
            lastName: person.localizedLastName?.trim(),
            headline: person.localizedHeadline,
            vanityName: person.vanityName,
            profileImageUrl: profileImageUrl,
            profileImageExpiresAt: profileImageExpiresAt,
          };
        })
      );

      return createSuccessResponse(people, status);
    } catch (error) {
      return catchLinkedInError(error, "Error searching by person URN");
    }
  };

  getOrganizations = async (): Promise<
    Response<
      {
        id: string;
        urn: string;
        profileImageUrl: string;
        profileImageExpiresAt: Date | null;
        state: LinkedInOrganziationState;
        role: LinkedInOrganizationRole;
        organizationType: OrganizationType;
        primaryOrganizationType: PrimaryOrganizationType;
        website: string;
        description: string;
        name: string;
        vanityName: string;
      }[]
    >
  > => {
    try {
      const params = new URLSearchParams({
        q: "roleAssignee",
        state: "APPROVED",
        count: "200",
      });

      // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/organizations/organization-access-control-by-role?view=li-lms-2024-01&tabs=http#sample-request-3
      const { status, data } = await this.client_rest_V202411.get(
        `/organizationAcls?${params}`
      );

      const elements: {
        roleAssignee: string;
        state: LinkedInOrganziationState;
        role: LinkedInOrganizationRole;
        organization: string;
      }[] = data.elements;

      const organizationResponse = await this.getOrganizationsByUrn(
        elements.map((element) => element.organization)
      );

      if (organizationResponse.errors) {
        return organizationResponse;
      }

      const organizations = organizationResponse.data;

      return createSuccessResponse(
        organizations.map((organization) => {
          const element = elements.find(
            (element) => element.organization === organization.urn
          );

          assert(element);

          return {
            ...organization,
            state: element.state,
            role: element.role,
          };
        }),
        status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error querying user's organizations");
    }
  };

  _initializeImageUpload = async (
    ownerUrn: string
  ): Promise<
    Response<{
      imageUrn: string;
      uploadUrl: string;
    }>
  > => {
    const params = new URLSearchParams({
      action: "initializeUpload",
    });

    try {
      // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/images-api?view=li-lms-2024-01&tabs=http#initialize-image-upload
      const response = await this.client_rest_V202411.post(
        `/images?${params}`,
        {
          initializeUploadRequest: {
            owner: ownerUrn,
          },
        }
      );

      const data = response.data.value;

      return createSuccessResponse(
        {
          imageUrn: data.image,
          uploadUrl: data.uploadUrl,
        },
        response.status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error initializing image upload");
    }
  };

  _initializeVideoUpload = async ({
    ownerUrn,
    fileSizeBytes,
    uploadThumbnail,
    uploadCaptions,
  }: {
    ownerUrn: string;
    fileSizeBytes: number;
    uploadThumbnail: boolean;
    uploadCaptions: boolean;
  }): Promise<
    Response<{
      videoUrn: string;
      uploadToken: string;
      uploadInstructions: {
        uploadUrl: string;
        firstByte: number;
        lastByte: number;
      }[];
      thumbnailUploadUrl: string | null;
      captionsUploadUrl: string | null;
    }>
  > => {
    const params = new URLSearchParams({
      action: "initializeUpload",
    });

    try {
      // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/videos-api?view=li-lms-2024-01&tabs=http#initialize-video-upload
      const response = await this.client_rest_V202411.post(
        `/videos?${params}`,
        {
          initializeUploadRequest: {
            owner: ownerUrn,
            fileSizeBytes,
            uploadThumbnail,
            uploadCaptions,
          },
        }
      );

      const data = response.data.value as {
        video: string;
        uploadToken: string;
        uploadInstructions: {
          uploadUrl: string;
          firstByte: number;
          lastByte: number;
        }[];
        thumbnailUploadUrl?: string;
        captionsUploadUrl?: string;
      };

      return createSuccessResponse(
        {
          videoUrn: data.video,
          uploadToken: data.uploadToken,
          uploadInstructions: data.uploadInstructions,
          thumbnailUploadUrl: data.thumbnailUploadUrl || null,
          captionsUploadUrl: data.captionsUploadUrl || null,
        },
        response.status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error initializing video upload");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/documents-api?view=li-lms-2023-07&tabs=http
  _initializeDocumentUpload = async (
    ownerUrn: string
  ): Promise<
    Response<{
      documentUrn: string;
      uploadUrl: string;
    }>
  > => {
    const params = new URLSearchParams({
      action: "initializeUpload",
    });

    try {
      const response = await this.client_rest_V202411.post(
        `/documents?${params}`,
        {
          initializeUploadRequest: {
            owner: ownerUrn,
          },
        }
      );

      const data = response.data.value as {
        uploadUrlExpiresAt: number;
        uploadUrl: string;
        document: string;
      };

      return createSuccessResponse(
        {
          documentUrn: data.document,
          uploadUrl: data.uploadUrl,
        },
        response.status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error initializing document upload");
    }
  };

  getVideo = async (
    videoUrn: string
  ): Promise<
    Response<
      {
        id: string;
        duration: number;
        aspectRatioWidth: number;
        aspectRatioHeight: number;
        ownerUrn: string;
        thumbnailUrl: string;
        downloadUrl: string;
      } & (
        | {
            status: "PROCESSING" | "WAITING_UPLOAD" | "AVAILABLE";
          }
        | {
            status: "PROCESSING_FAILED";
            processingFailureReason: string;
          }
      )
    >
  > => {
    try {
      // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/videos-api?view=li-lms-2023-03&tabs=http#get-a-video
      const response = await this.unversionedClient.get(`/videos/${videoUrn}`);

      const video = response.data;

      if (video.processingFailureReason) {
        console.log(response);
      }

      return createSuccessResponse(
        {
          id: video.id,
          duration: video.duration,
          aspectRatioWidth: video.aspectRatioWidth,
          aspectRatioHeight: video.aspectRatioHeight,
          ownerUrn: video.owner,
          thumbnailUrl: video.thumbnail,
          downloadUrl: video.downloadUrl,
          status: video.status,
          processingFailureReason: video.processingFailureReason,
        },
        response.status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error querying video");
    }
  };

  _finalizeVideoUpload = async (
    videoUrn: string,
    uploadToken: string,
    uploadedPartIDs: string[]
  ): Promise<
    Response<
      {
        id: string;
        duration: number;
        aspectRatioWidth: number;
        aspectRatioHeight: number;
        ownerUrn: string;
        thumbnailUrl: string;
        downloadUrl: string;
      } & (
        | {
            status: "PROCESSING" | "WAITING_UPLOAD" | "AVAILABLE";
          }
        | {
            status: "PROCESSING_FAILED";
            processingFailureReason: string;
          }
      )
    >
  > => {
    const params = new URLSearchParams({
      action: "finalizeUpload",
    });

    try {
      // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/videos-api?view=li-lms-2024-01&tabs=http#finalize-video-upload
      const response = await this.client_rest_V202411.post(
        `/videos?${params}`,
        {
          finalizeUploadRequest: {
            video: videoUrn,
            uploadToken,
            uploadedPartIds: uploadedPartIDs,
          },
        }
      );

      const videoResponse = await this.getVideo(videoUrn);

      if (videoResponse.errors) {
        return videoResponse;
      }

      return createSuccessResponse(videoResponse.data, videoResponse.status);
    } catch (error) {
      return catchLinkedInError(error, "Error finalizing video upload");
    }
  };

  uploadAssets = async ({
    authorUrn,
    assets,
    coverPhoto,
    captions,
  }: {
    authorUrn: string;
    assets: {
      signedUrl: string;
      mimetype: string;
      // It's only required for video
      fileSizeBytes?: number;
    }[];
    coverPhoto?: {
      signedUrl: string;
      metadata: {
        mimetype: string;
      };
    } | null;
    captions?: {
      signedUrl: string;
    } | null;
  }): Promise<Response<string[]>> => {
    try {
      const assetUrns: string[] = [];
      let nonCreatedStatus = null;

      for await (const asset of assets) {
        const isImage = asset.mimetype.split("/")[0] === "image";
        const isVideo = asset.mimetype.split("/")[0] === "video";
        const isPDF = asset.mimetype === "application/pdf";
        const assetBuffer = await getAssetBuffer(asset.signedUrl);

        if (isImage) {
          const initResponse = await this._initializeImageUpload(authorUrn);
          if (initResponse.errors) {
            return createErrorResponse(
              initResponse.errors.map(
                (error) => `Error initializing image upload: ${error}`
              ),
              initResponse.status
            );
          }

          const data = initResponse.data;

          const { imageUrn, uploadUrl } = data;

          const response = await axios.put(uploadUrl, assetBuffer, {
            headers: {
              Authorization: `Bearer ${this.accessToken}`,
              "Content-Type": asset.mimetype,
            },
          });

          if (response.status !== StatusCodes.CREATED) {
            nonCreatedStatus = response.status;
          }

          assetUrns.push(imageUrn);
        } else if (isVideo) {
          assert(asset.fileSizeBytes, "File size is required for video upload");

          console.log(`${authorUrn} Initializing Video Upload`);
          const initResponse = await this._initializeVideoUpload({
            ownerUrn: authorUrn,
            fileSizeBytes: asset.fileSizeBytes,
            uploadThumbnail: !!coverPhoto,
            uploadCaptions: !!captions,
          });
          if (initResponse.errors) {
            return createErrorResponse(
              initResponse.errors.map(
                (error) => `Error initializing video upload: ${error}`
              ),
              initResponse.status
            );
          }

          const data = initResponse.data;

          const {
            videoUrn,
            uploadToken,
            uploadInstructions,
            thumbnailUploadUrl,
            captionsUploadUrl,
          } = data;

          console.log(`${authorUrn} Splitting Video`);
          const videoSplits = await splitSync(assetBuffer, {
            bytes: "4M",
          });

          assert(
            uploadInstructions.length === videoSplits.length,
            "Upload instructions must match the number of video splits"
          );

          console.log(`${authorUrn} Uploading Video Splits`);
          const promises = uploadInstructions.map((instruction, i) => {
            return axios.put(instruction.uploadUrl, videoSplits[i], {
              headers: {
                Authorization: `Bearer ${this.accessToken}`,
                "Content-Type": asset.mimetype,
              },
            });
          });

          const responses = await Promise.all(promises);

          const nonSuccessResponse = responses.find(
            (response) => response.status !== StatusCodes.OK
          );

          if (nonSuccessResponse) {
            nonCreatedStatus = nonSuccessResponse.status;
          }

          const uploadedPartIDs = responses.map(
            (response) => response.headers["etag"] as string
          );

          if (thumbnailUploadUrl && coverPhoto) {
            console.log(`${authorUrn} Uploading Thumbnail`);
            const thumbnailBuffer = await getAssetBuffer(coverPhoto.signedUrl);

            try {
              const thumbnailResponse = await axios.put(
                thumbnailUploadUrl,
                thumbnailBuffer,
                {
                  headers: {
                    Authorization: `Bearer ${this.accessToken}`,
                    "Content-Type": coverPhoto.metadata.mimetype,
                  },
                }
              );

              if (thumbnailResponse.status !== StatusCodes.CREATED) {
                console.log(
                  `Error uploading thumbnail ${thumbnailResponse.status}: ${thumbnailResponse.data}`
                );

                return createErrorResponse(
                  ["Error uploading thumbnail"],
                  StatusCodes.INTERNAL_SERVER_ERROR
                );
              }
            } catch (error) {
              console.log("Error uploading thumbnail", error);
              return createErrorResponse(
                ["Error uploading thumbnail"],
                StatusCodes.INTERNAL_SERVER_ERROR
              );
            }
          }

          if (captionsUploadUrl && captions) {
            console.log(`${authorUrn} Uploading Captions`);
            try {
              // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/videos-api?view=li-lms-2023-11&tabs=http#upload-captions
              const formData = new FormData();
              formData.append(
                "metadata",
                JSON.stringify({
                  format: "SRT",
                  formattedForEasyReader: true,
                  largeText: true,
                  source: "USER_PROVIDED",
                  locale: {
                    variant: "AMERICAN",
                    country: "US",
                    language: "EN",
                  },
                  transcriptType: "CLOSED_CAPTION",
                })
              );

              const captionsContent = await getAssetBuffer(captions.signedUrl);

              formData.append("file", Buffer.from(captionsContent), {
                filename: "captions.srt",
                contentType: "text/plain",
              });

              const captionsResponse = await axios.post(
                captionsUploadUrl,
                formData,
                {
                  headers: {
                    ...formData.getHeaders(),
                    Authorization: `Bearer ${this.accessToken}`,
                  },
                }
              );

              if (captionsResponse.status !== StatusCodes.CREATED) {
                console.log(
                  `Error uploading thumbnail ${captionsResponse.status}: ${captionsResponse.data}`
                );

                return createErrorResponse(
                  ["Error uploading thumbnail"],
                  StatusCodes.INTERNAL_SERVER_ERROR
                );
              }
            } catch (error) {
              console.log("Error uploading captions", error);
              return createErrorResponse(
                ["Error uploading captions"],
                StatusCodes.INTERNAL_SERVER_ERROR
              );
            }
          }

          console.log(`${authorUrn} Finalizing Video Upload`);
          await this._finalizeVideoUpload(
            videoUrn,
            uploadToken,
            uploadedPartIDs
          );

          let waitingToUpload = true;
          while (waitingToUpload) {
            console.log(
              `${authorUrn} Video (${videoUrn}): Waiting for video to upload`
            );
            const videoResponse = await this.getVideo(videoUrn);
            const video = videoResponse.data;
            if (video) {
              const { status } = video;
              console.log(`${authorUrn} Video (${videoUrn}): Status ${status}`);

              if (status === "AVAILABLE") {
                console.log(
                  `${authorUrn}Video (${videoUrn}): Available, continuing`
                );
                waitingToUpload = false;
              } else if (status === "PROCESSING_FAILED") {
                console.log(
                  `${authorUrn} Video (${videoUrn}): Video processing failed ${video.processingFailureReason}`
                );
                return createErrorResponse(
                  [video.processingFailureReason],
                  StatusCodes.INTERNAL_SERVER_ERROR
                );
              } else {
                console.log(
                  `${authorUrn} Video (${videoUrn}): Video waiting to upload sleeping 1s`
                );
                await new Promise((resolve) => setTimeout(resolve, 1000));
              }
            }
          }

          assetUrns.push(videoUrn);
        } else if (isPDF) {
          const { data } = await this._initializeDocumentUpload(authorUrn);

          assert(data);

          const { documentUrn, uploadUrl } = data;

          const response = await axios.put(uploadUrl, assetBuffer, {
            headers: {
              Authorization: `Bearer ${this.accessToken}`,
              "Content-Type": asset.mimetype,
            },
          });

          if (response.status !== StatusCodes.CREATED) {
            nonCreatedStatus = response.status;
          }

          assetUrns.push(documentUrn);
        }
      }

      if (nonCreatedStatus != null) {
        console.log(
          authorUrn,
          `Upload failed with status code ${nonCreatedStatus}`
        );
        return createErrorResponse(
          [`Upload failed with status code ${nonCreatedStatus}`],
          nonCreatedStatus
        );
      } else {
        return createSuccessResponse(
          assetUrns,
          nonCreatedStatus || StatusCodes.CREATED
        );
      }
    } catch (error) {
      return catchLinkedInError(error, "Error uploading assets");
    }
  };

  checkAssetUploadStatus = async (
    assetUrn: string
  ): Promise<
    Response<{
      status: string;
    }>
  > => {
    try {
      const assetUrnSplit = assetUrn.split(":");
      const assetID = assetUrnSplit[assetUrnSplit.length - 1];

      // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/vector-asset-api?view=li-lms-2024-01&tabs=http#check-status-of-upload
      const response = await this.client_rest_V202411.get(`/assets/${assetID}`);

      const data: {
        id: string;
        mediaTypeFamily: string;
        created: number;
        lastModified: number;
        recipes: {
          recipe: string;
          status: string;
        }[];
        serviceRelationships: {
          identifier: string;
          relationshipType: string;
        };
        status: string;
      } = response.data.response;

      return createSuccessResponse(
        {
          status: data.recipes[0].status,
        },
        response.status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error checking asset upload status");
    }
  };

  createPost = async ({
    authorUrn,
    commentary,
    documentTitle,
    assets = [],
    coverPhoto,
    captions,
    parentUrn,
  }: {
    authorUrn: string;
    commentary?: string | null;
    documentTitle?: string | null;
    assets?: {
      name: string;
      signedUrl: string;
      mimetype: string;
      fileSizeBytes: number;
    }[];
    coverPhoto?: {
      signedUrl: string;
      metadata: {
        mimetype: string;
      };
    } | null;
    captions?: {
      signedUrl: string;
    } | null;
    parentUrn?: string;
  }): Promise<
    Response<{
      postUrn: string;
    }>
  > => {
    try {
      const uploadAssetResponse = await this.uploadAssets({
        authorUrn,
        assets,
        coverPhoto,
        captions,
      });
      if (uploadAssetResponse.errors) {
        console.log(
          `${authorUrn} Error uploading assets: ${uploadAssetResponse.errors.join(
            ". "
          )}`
        );
        return uploadAssetResponse;
      }

      const assetUrns = uploadAssetResponse.data;

      const commentaryWithoutMentions = commentary?.replaceAll(
        linkedinMentionRegex,
        ""
      );
      const websites =
        uniq((commentaryWithoutMentions || "").match(urlRegex)) || [];
      const firstWebsite = websites[0];
      let content;
      if (parentUrn == null) {
        if (assets.length === 0) {
          console.log(`${authorUrn} no assets, checking for websites`);

          console.log(`${authorUrn} checking for metadata: ${firstWebsite}`);
          const metadata = await getLinkMetadata(firstWebsite);
          if (metadata) {
            const imageUrl = metadata.image;

            console.log(`${authorUrn} website image: ${imageUrl}`);

            let imageUrn;
            if (imageUrl) {
              const stream = got.stream(imageUrl);

              let mimetype: null | string | undefined = null;
              try {
                mimetype = (await fileTypeFromStream(stream))?.mime;
              } catch (error) {
                console.log("Unable to get file", error);
              }

              console.log(`${authorUrn} website image mimetype: ${mimetype}`);

              if (mimetype) {
                const { data: imageUrns } = await this.uploadAssets({
                  authorUrn,
                  assets: [
                    {
                      signedUrl: imageUrl,
                      mimetype,
                    },
                  ],
                });

                imageUrn = imageUrns ? imageUrns[0] : null;
              }
            }

            content = {
              article: {
                source: firstWebsite,
                title: metadata.title,
                description: (metadata.description || "").slice(0, 4000),
                ...(imageUrn ? { thumbnail: imageUrn } : null),
              },
            };
          } else {
            content = undefined;
          }
        } else if (assetUrns.length === 1) {
          if (assets[0].mimetype === "application/pdf") {
            content = {
              media: {
                id: assetUrns[0],
                title: documentTitle || assets[0].name.split(".")[0],
              },
            };
          } else {
            content = {
              media: {
                id: assetUrns[0],
              },
            };
          }
        } else {
          content = {
            multiImage: {
              images: assetUrns.map((assetUrn) => ({
                id: assetUrn,
              })),
            },
          };
        }
      }

      const cleanedCommentary =
        content?.article != null &&
        parentUrn == null &&
        assets.length === 0 &&
        commentary?.endsWith(firstWebsite)
          ? commentary.slice(0, -firstWebsite.length).trimEnd()
          : commentary;

      const payload = {
        author: authorUrn,
        commentary: cleanedCommentary || "",
        visibility: "PUBLIC",
        distribution: {
          feedDistribution: "MAIN_FEED",
          targetEntities: [],
          thirdPartyDistributionChannels: [],
        },
        ...(content && { content }),
        lifecycleState: "PUBLISHED",
        isReshareDisabledByAuthor: false,
        // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/posts-api?view=li-lms-2023-09&tabs=http#reshare-creation-request
        ...(parentUrn && {
          reshareContext: {
            parent: parentUrn,
          },
        }),
      };

      // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/posts-api?view=li-lms-2023-03&tabs=http#create-a-post
      const response = await this.client_rest_V202411.post(`/posts`, payload);

      const postUrn = response.headers["x-restli-id"];

      assert(postUrn);

      return createSuccessResponse({ postUrn }, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error creating post");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/posts-api?view=li-lms-2023-11&tabs=http#update-posts
  updatePost = async ({
    postUrn,
    commentary,
  }: {
    postUrn: string;
    commentary: string;
  }): Promise<
    Response<{
      success: true;
    }>
  > => {
    try {
      const urlPostUrn = postUrn.replaceAll(":", "%3A");

      const response = await this.client_rest_V202411.post(
        `/posts/${urlPostUrn}`,
        {
          patch: {
            $set: {
              commentary: commentary,
            },
          },
        }
      );

      return createSuccessResponse(
        {
          success: true,
        },
        response.status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error updating post");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/posts-api?view=li-lms-2023-09&tabs=http#delete-posts
  deletePost = async ({
    postUrn,
  }: {
    postUrn: string;
  }): Promise<
    Response<{
      success: true;
    }>
  > => {
    const urlPostUrn = postUrn.replaceAll(":", "%3A");

    try {
      const { status, data } = await this.client_rest_V202411.delete(
        `/posts/${urlPostUrn}`
      );

      return createSuccessResponse(
        {
          success: true,
        },
        status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error deleting post");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/comments-api?view=li-lms-2023-09&tabs=http#create-a-comment
  createComment = async ({
    authorUrn,
    postUrn,
    commentary,
  }: {
    authorUrn: string;
    postUrn: string;
    commentary: string;
  }): Promise<
    Response<{
      commentUrl: string;
    }>
  > => {
    try {
      const tags = commentary.match(/@\[.*?\]\(urn:li:.*?\)/g) || [];
      let removedLength = 0;
      const attributes = tags.map((tag) => {
        const start = commentary.indexOf(tag);
        const fullLength = tag.length;
        const urnMatch = tag.match(/\(urn:li:.*?\)/);
        if (urnMatch) {
          const urn = urnMatch[0].slice(1, -1);

          const isPerson = urn.includes("person");

          const lengthToRemove = urn.length + "@[]()".length;

          removedLength += lengthToRemove;

          return {
            length: fullLength - lengthToRemove,
            start: start - removedLength + lengthToRemove,
            value: {
              [isPerson ? "person" : "organization"]: {
                [isPerson ? "person" : "organization"]: urn,
              },
            },
          };
        }
      });
      const text = commentary.replace(/@\[.*?\]\(urn:li:.*?\)/g, (match) =>
        match.split("]")[0].slice(2)
      );
      const message = {
        text,
        attributes,
      };

      const payload = {
        actor: authorUrn,
        object: postUrn,
        message,
      };

      const urlPostUrn = postUrn.replaceAll(":", "%3A");

      // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/comments-api?view=li-lms-2024-01&tabs=http#create-a-comment
      const response = await this.client_rest_V202411.post(
        `/socialActions/${urlPostUrn}/comments`,
        payload
      );
      const data = response.data;

      const commentUrl = `https://www.linkedin.com/feed/update/${data.object}?commentUrn=${data.commentUrn}`;

      return createSuccessResponse({ commentUrl }, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error creating comment");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/comments-api#delete-a-comment
  deleteComment = async ({
    postUrn,
    commentID,
    actorUrn,
  }: {
    postUrn: string;
    commentID: string;
    actorUrn: string;
  }): Promise<Response<{ success: boolean }>> => {
    try {
      const urlPostUrn = postUrn.replaceAll(":", "%3A");
      const urlActorUrn = actorUrn.replaceAll(":", "%3A");

      const response = await this.client_rest_V202411.delete(
        `/socialActions/${urlPostUrn}/comments/${commentID}?actor=${urlActorUrn}`
      );

      return createSuccessResponse({ success: true }, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error deleting comment");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/comments-api?view=li-lms-2025-02&tabs=http#get-a-comment
  getComment = async ({
    postUrn,
    commentID,
  }: {
    postUrn: string;
    commentID: string;
  }): Promise<Response<{ comment: Comment }>> => {
    try {
      const response = await this.client_rest_V202411.get(
        `/socialActions/${postUrn.replaceAll(":", "%3A")}/comments/${commentID}`
      );

      return createSuccessResponse(response.data, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error getting comment");
    }
  };

  getOrganizationFollowerCount = async (
    organizationUrn: string
  ): Promise<
    Response<{
      followerCount: number;
    }>
  > => {
    try {
      const params = new URLSearchParams({
        edgeType: "COMPANY_FOLLOWED_BY_MEMBER",
      });

      // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/organizations/organization-lookup-api?view=li-lms-2024-01&tabs=http#retrieve-organization-follower-count
      const response = await this.client_rest_V202411.get(
        `/networkSizes/${organizationUrn.replaceAll(":", "%3A")}?${params}`
      );

      const data: {
        firstDegreeSize: number;
      } = response.data;

      return createSuccessResponse(
        {
          followerCount: data.firstDegreeSize,
        },
        response.status
      );
    } catch (error) {
      return catchLinkedInError(
        error,
        "Error querying organization follower count"
      );
    }
  };

  getPostStatistics = async ({
    organizationUrn,
    postType,
    postUrns,
  }: {
    organizationUrn: string;
    postType: "share" | "ugcPost";
    postUrns: string[];
  }): Promise<
    Response<{
      [urn: string]: {
        clickCount: number;
        commentCount: number;
        engagement: number;
        impressionCount: number;
        likeCount: number;
        shareCount: number;
        uniqueImpressionsCount: number;
      };
    }>
  > => {
    if (postUrns.length === 0) {
      return createSuccessResponse({}, StatusCodes.OK);
    }

    try {
      const paramObject: { [key: string]: string } = {
        q: "organizationalEntity",
        organizationalEntity: organizationUrn,
      };
      if (postType === "share") {
        // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/organizations/share-statistics?view=li-lms-2023-03&tabs=http#retrieve-statistics-for-specific-shares
        paramObject["shares"] = `List(${postUrns.join(",")})`;
      } else {
        // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/organizations/share-statistics?view=li-lms-2023-03&tabs=http#retrieve-statistics-for-specific-ugc-posts
        paramObject["ugcPosts"] = `List(${postUrns.join(",")})`;
      }

      const params = new URLSearchParams(paramObject);

      // Replace both left and right parens + commas with their URL encoded equivalents
      const paramsWithParens = params
        .toString()
        .replaceAll(/%28/g, "(")
        .replaceAll(/%29/g, ")")
        .replaceAll(/%2C/g, ",");

      const response = await this.client_rest_V202411.get(
        `/organizationalEntityShareStatistics?${paramsWithParens}`
      );

      const data: {
        elements: RequireAtLeastOne<
          {
            share?: string;
            ugcPost?: string;
            organizationalEntity: string;
            totalShareStatistics: {
              clickCount: number;
              commentCount: number;
              engagement: number;
              impressionCount: number;
              likeCount: number;
              shareCount: number;
              uniqueImpressionsCount: number;
            };
          },
          "share" | "ugcPost"
        >[];
      } = response.data;

      const postStatistics: {
        [urn: string]: {
          clickCount: number;
          commentCount: number;
          engagement: number;
          impressionCount: number;
          likeCount: number;
          shareCount: number;
          uniqueImpressionsCount: number;
        };
      } = {};

      data.elements.forEach((post) => {
        const urn = (post.share || post.ugcPost) as string;

        postStatistics[urn] = {
          clickCount: post.totalShareStatistics.clickCount,
          commentCount: post.totalShareStatistics.commentCount,
          engagement: post.totalShareStatistics.engagement,
          impressionCount: post.totalShareStatistics.impressionCount,
          likeCount: post.totalShareStatistics.likeCount,
          shareCount: post.totalShareStatistics.shareCount,
          uniqueImpressionsCount:
            post.totalShareStatistics.uniqueImpressionsCount,
        };
      });

      return createSuccessResponse(postStatistics, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error querying post statistics");
    }
  };

  _convertRawPosts = (rawPosts: RawPost[]): Post[] => {
    const personTagRe = /urn:li:person:(.+?)\)/g;
    const organizationTagRe = /urn:li:organization:(\d+)/g;
    // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/little-text-format?view=li-lms-2023-06#hashtagtemplate
    // e.g. {hashtag|\\#|manufacturing}
    const hashtagRe = /#\|.+?}/g;

    return rawPosts.map((post) => {
      const isPoll = post.content?.poll ? true : false;
      const mediaID = post.content?.media?.id;
      const referenceID = post.content?.reference?.id;

      const imageUrn = mediaID?.includes("image") ? [mediaID] : [];
      const imageUrns =
        post.content?.multiImage?.images.map((image) => image.id) || imageUrn;
      const videoUrn = mediaID?.includes("video") ? mediaID : null;
      const documentUrn = mediaID?.includes("document") ? mediaID : null;
      const eventUrn = referenceID?.includes("event") ? referenceID : null;
      const jobUrn = referenceID?.includes("job") ? referenceID : null;
      const celebrationUrn = referenceID?.includes("celebrate")
        ? referenceID
        : null;

      const personTags = (post.commentary.match(personTagRe) || []).map((tag) =>
        tag.replace(")", "")
      );
      const organizationTags = post.commentary.match(organizationTagRe) || [];
      const hashtags = (post.commentary.match(hashtagRe) || []).map((tag) =>
        tag.replace("#|", "").replace("}", "")
      );

      const commentary = post.commentary === "" ? null : post.commentary;
      const quotedPostUrn = post.reshareContext?.parent || null;

      let type: LinkedInPostType = LinkedInPostType.Text;
      if (post.content?.article) {
        type = LinkedInPostType.Article;
      } else if (videoUrn) {
        type = LinkedInPostType.Video;
      } else if (imageUrns.length === 1) {
        type = LinkedInPostType.Image;
      } else if (imageUrns.length > 1) {
        type = LinkedInPostType.MultiImage;
      } else if (documentUrn) {
        type = LinkedInPostType.Document;
      } else if (eventUrn) {
        type = LinkedInPostType.Event;
      } else if (jobUrn) {
        type = LinkedInPostType.Job;
      } else if (isPoll) {
        type = LinkedInPostType.Poll;
      } else if (celebrationUrn) {
        type = LinkedInPostType.Celebration;
      } else if (quotedPostUrn) {
        if (commentary == null) {
          type = LinkedInPostType.Repost;
        } else {
          type = LinkedInPostType.QuotePost;
        }
      }

      return {
        id: post.id,
        publishedAt: new Date(post.publishedAt || post.createdAt),
        postCreatedAt: new Date(post.createdAt),
        lastModifiedAt: new Date(post.lastModifiedAt),
        visibility: post.visibility,
        commentary,
        article: post.content?.article
          ? {
              description: post.content.article.description,
              thumbnailUrn: post.content.article.thumbnail,
              url: post.content.article.source,
              title: post.content.article.title,
            }
          : null,
        videoUrn,
        imageUrns,
        documentUrn,
        jobUrn,
        eventUrn,
        celebrationUrn,
        authorUrn: post.author.replace("organizationBrand", "organization"),
        isAdvertisement: !!post.adContext,
        personTags,
        personTagCount: personTags.length,
        organizationTags,
        organizationTagCount: organizationTags.length,
        hashtags,
        hashtagCount: hashtags.length,
        quotedPostUrn,
        type,
        rawResponse: post,
      };
    });
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/network-update-social-actions?view=li-lms-2024-02&tabs=http#create-a-like-on-a-share
  likePost = async ({
    actorUrn,
    postUrn,
  }: {
    actorUrn: string;
    postUrn: string;
  }): Promise<Response<{ id: string }>> => {
    try {
      const response = await this.client_rest_V202411.post(
        `/socialActions/${encodeURIComponent(postUrn)}/likes`,
        {
          actor: actorUrn,
          object: postUrn,
        }
      );

      const data = response.data as {
        id: string;
      };

      return createSuccessResponse(data, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error liking post");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/network-update-social-actions?view=li-lms-2024-02&tabs=http#create-a-like-on-a-share
  likeComment = async ({
    actorUrn,
    postUrn,
    commentUrn,
  }: {
    actorUrn: string;
    postUrn: string;
    commentUrn: string;
  }): Promise<Response<{ id: string }>> => {
    try {
      const response = await this.client_rest_V202411.post(
        `/socialActions/${commentUrn
          .replaceAll(":", "%3A")
          .replaceAll("(", "%28")
          .replaceAll(")", "%29")
          .replaceAll(",", "%2C")}/likes`,
        {
          actor: actorUrn,
          object: postUrn,
        }
      );

      const data = response.data as {
        id: string;
      };

      return createSuccessResponse(data, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error liking post");
    }
  };

  getPostFirstLikeDate = async ({
    urn,
  }: {
    urn: string;
  }): Promise<Response<Date | null>> => {
    try {
      const params = new URLSearchParams({
        count: "50",
      });

      const response = await this.client_rest_V202411.get(
        `/socialActions/${encodeURIComponent(urn)}/likes?${params}`
      );

      const rawLikes: RawLike[] = response.data.elements;
      const earliestLike = sortBy(rawLikes, "created.time")[0];

      if (earliestLike) {
        return createSuccessResponse(
          new Date(earliestLike.created.time),
          response.status
        );
      } else {
        return createSuccessResponse(null, response.status);
      }
    } catch (error) {
      return catchLinkedInError(error, "Error getting post's first like");
    }
  };

  getPosts = async ({
    urns,
  }: {
    urns: string[];
  }): Promise<
    Response<{
      posts: Post[];
      statuses: {
        [urn: string]: number;
      };
      erroredUrns: string[];
    }>
  > => {
    try {
      const params = new URLSearchParams({
        ids: `List(${urns.join(",")})`,
      })
        .toString()
        .replaceAll(/%28/g, "(")
        .replaceAll(/%29/g, ")")
        .replaceAll(/%2C/g, ",");

      const response = await this.client_rest_V202411.get(`/posts?${params}`, {
        headers: {
          "X-RestLi-Method": "BATCH_GET",
        },
      });

      const erroredUrns = Object.keys(response.data.errors);

      const rawPosts: RawPost[] = Object.values(response.data.results);
      const posts = this._convertRawPosts(rawPosts);
      const statuses = response.data.statuses as {
        [urn: string]: number;
      };

      return createSuccessResponse(
        { posts, statuses, erroredUrns },
        response.status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error querying posts");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/posts-api?view=li-lms-2023-03&tabs=http#find-posts-by-authors
  getOrganizationPosts = async (
    organizationUrn: string
  ): Promise<
    Response<
      (Post & {
        stats: {
          clickCount: number;
          commentCount: number;
          engagement: number;
          impressionCount: number;
          likeCount: number;
          shareCount: number;
          uniqueImpressionsCount: number;
        };
      })[]
    >
  > => {
    try {
      const params = new URLSearchParams({
        q: "author",
        author: organizationUrn,
        viewContext: "AUTHOR",
        sortBy: "CREATED",
        count: "100",
      });

      // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/posts-api?tabs=http#find-posts-by-authors
      const response = await this.client_rest_V202411.get(`/posts?${params}`);

      const data: RawPost[] = response.data.elements;

      const posts = this._convertRawPosts(data);
      const shares = posts.filter((post) => post.id.includes("share:"));
      const ugcPosts = posts.filter((post) => post.id.includes("ugcPost:"));

      const statResponses = await Promise.all([
        this.getPostStatistics({
          organizationUrn,
          postType: "share",
          postUrns: shares.map((post) => post.id),
        }),
        this.getPostStatistics({
          organizationUrn,
          postType: "ugcPost",
          postUrns: ugcPosts.map((post) => post.id),
        }),
      ]);

      const erroredResponses = statResponses.filter(
        (response) => response.success === false
      );

      if (erroredResponses.length) {
        const erroredResponse = erroredResponses[0];
        return createErrorResponse(
          erroredResponse.errors as string[],
          erroredResponse.status,
          erroredResponse.rawError
        );
      }

      const postStatistics = {
        ...statResponses[0].data,
        ...statResponses[1].data,
      };

      const postsWithStats = posts.map((post) => {
        const stats = postStatistics[post.id] || {
          clickCount: 0,
          commentCount: 0,
          engagement: 0,
          impressionCount: 0,
          likeCount: 1,
          shareCount: 0,
          uniqueImpressionsCount: 0,
        };

        return {
          ...post,
          stats,
        };
      });

      return createSuccessResponse(postsWithStats, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error querying organization posts");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/share-api?view=li-lms-unversioned&tabs=http#retrieve-activities-and-their-shares
  getPostUrnsByActivityUrns = async (
    activityUrns: string[]
  ): Promise<
    Response<{
      [activityUrn: string]: string;
    }>
  > => {
    try {
      const params = new URLSearchParams({
        ids: `List(${activityUrns.join(",")})`,
      })
        .toString()
        .replaceAll(/%28/g, "(")
        .replaceAll(/%29/g, ")")
        .replaceAll(/%2C/g, ",");

      const response = await this.client_v2.get(`/activities?${params}`);
      const data: {
        results: {
          [activityUrn: string]: {
            domainEntity: string;
          };
        };
      } = response.data;

      const urnMapping: { [activityUrn: string]: string } = {};
      Object.keys(data.results).forEach((activityUrn) => {
        urnMapping[activityUrn] = data.results[activityUrn].domainEntity;
      });

      return createSuccessResponse(urnMapping, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error querying posts by activity URN");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/posts-atmention-search-api?view=li-lms-2025-02&tabs=http#search-by-keyword-for-organization-pages
  searchOrganizationFollowers = async ({
    keywords,
    organizationUrn,
  }: {
    keywords: string;
    organizationUrn: string;
  }): Promise<
    Response<
      {
        urn: string;
        firstName: string;
        lastName: string;
        headline: string;
      }[]
    >
  > => {
    try {
      const params = new URLSearchParams({
        q: "organizationFollowers",
        keywords,
        organization: organizationUrn,
      });

      const response = await this.client_rest_V202411.get(
        `/peopleTypeahead?${params}`
      );

      const rawData = response.data.elements as {
        member: string;
        firstName: string;
        lastName: string;
        headline: string;
      }[];

      const data = rawData.map((element) => ({
        urn: element.member,
        firstName: element.firstName,
        lastName: element.lastName,
        headline: element.headline,
      }));

      return createSuccessResponse(data, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error searching people");
    }
  };

  searchConnections = async ({
    keywords,
    personUrn,
  }: {
    keywords: string;
    personUrn: string;
  }): Promise<
    Response<
      { urn: string; firstName: string; lastName: string; headline: string }[]
    >
  > => {
    try {
      const params = new URLSearchParams({
        q: "connections",
        keywords,
        person: personUrn,
      });

      const response = await this.client_rest_V202411.get(
        `/peopleTypeahead?${params}`
      );

      const rawData = response.data.elements as {
        member: string;
        firstName: string;
        lastName: string;
        headline: string;
      }[];

      const data = rawData.map((element) => ({
        urn: element.member,
        firstName: element.firstName,
        lastName: element.lastName,
        headline: element.headline,
      }));

      return createSuccessResponse(data, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error searching connections");
    }
  };

  // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/shares/posts-atmention-search-api?view=li-lms-2023-08&tabs=http#search-by-vanity-url-for-organization-page
  getPersonByVanityName = async ({
    vanityName,
    organizationUrn,
  }: {
    vanityName: string;
    organizationUrn: string;
  }): Promise<
    Response<{
      id: string;
      urn: string;
      firstName: string;
      lastName: string;
      headline: string;
      vanityName: string;
      profileImageUrl: string | null;
      profileImageExpiresAt: Date | null;
    }>
  > => {
    try {
      const params = new URLSearchParams({
        q: "vanityUrlAsOrganization",
        vanityUrl: `https://www.linkedin.com/in/${vanityName}`,
        organization: organizationUrn,
      });

      // https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/posts-atmention-search-api?view=li-lms-2024-01&tabs=http#search-by-vanity-url-for-organization-page
      const response = await this.client_rest_V202411.get(
        `/vanityUrl?${params}`
      );

      const personUrn = response.data.elements[0]?.member;

      const personResponse = await this.getPeopleByUrn([personUrn]);

      if (personResponse.errors) {
        return createErrorResponse(
          personResponse.errors,
          personResponse.status,
          personResponse.rawError
        );
      }

      const data = personResponse.data;

      return createSuccessResponse(data[0], personResponse.status);
    } catch (error) {
      return catchLinkedInError(error, "Error querying person by username");
    }
  };

  getImagesByUrn = async (
    urns: string | string[]
  ): Promise<
    Response<
      {
        urn: string;
        originalUrn: string;
        owner: string;
        status: string;
        downloadUrl: string;
        downloadUrlExpiresAt: Date;
      }[]
    >
  > => {
    try {
      const imageUrns = (Array.isArray(urns) ? urns : [urns]).map((urn) =>
        urn.replace("digitalmediaAsset", "image").replaceAll(":", "%3A")
      );
      const response = await this.client_rest_V202411.get(
        `/images?ids=List(${imageUrns.join(",")})`
      );

      const images = Object.values(response.data.results) as {
        id: string;
        owner: string;
        status: string;
        downloadUrl: string;
        downloadUrlExpiresAt: number;
      }[];

      return createSuccessResponse(
        images.map((image) => ({
          ...image,
          urn: image.id,
          originalUrn: image.id.replace("image", "digitalmediaAsset"),
          downloadUrlExpiresAt: new Date(image.downloadUrlExpiresAt),
        })),
        response.status
      );
    } catch (error) {
      return catchLinkedInError(error, "Error querying images by URN");
    }
  };

  getNonAdministeredOrganizationsByUrn = async (
    urns: string | string[]
  ): Promise<
    Response<{
      id: string;
    }>
  > => {
    const organizationIDs = (Array.isArray(urns) ? urns : [urns]).map((urn) =>
      urn.split(":").pop()
    );

    try {
      const response = await this.client_rest_V202411.get(
        `/organizationsLookup`,
        {
          params: {
            ids: `List(${organizationIDs.join(",")})`,
          },
        }
      );

      return createSuccessResponse(response.data, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error querying public organizations");
    }
  };

  getOrganizationsByUrn = async (
    urns: string | string[]
  ): Promise<
    Response<
      {
        id: string;
        urn: string;
        name: string;
        vanityName: string;
        website: string;
        description: string;
        organizationType: OrganizationType;
        primaryOrganizationType: PrimaryOrganizationType;
        profileImageUrl: string;
        profileImageExpiresAt: Date | null;
        createdAt: Date;
      }[]
    >
  > => {
    const organizationIDs = (Array.isArray(urns) ? urns : [urns]).map((urn) =>
      urn.split(":").pop()
    );

    try {
      const response = await this.client_rest_V202411.get(`/organizations`, {
        params: {
          ids: `List(${organizationIDs.join(",")})`,
        },
      });

      const results = Object.values(response.data.results) as {
        id: number;
        created: {
          actor: string;
          time: number; // 1674160535440
        };
        vanityName: string;
        localizedDescription: string;
        localizedName: string;
        localizedWebsite: string;
        organizationType: OrganizationType;
        primaryOrganizationType: PrimaryOrganizationType;
        logoV2?: {
          original: string;
          cropped: string;
        };
      }[];

      const imagesResponse = await this.getImagesByUrn(
        removeEmptyElems(results.map((result) => result.logoV2?.cropped))
      );

      if (imagesResponse.errors) {
        return createErrorResponse(
          imagesResponse.errors,
          imagesResponse.status,
          imagesResponse.rawError
        );
      }

      const images = imagesResponse.data;

      const organizations = results.map((result) => {
        const image = images.find(
          (image) => image.originalUrn === result.logoV2?.cropped
        );

        return {
          id: result.id.toString(),
          urn: `urn:li:organization:${result.id}`,
          name: result.localizedName,
          vanityName: result.vanityName,
          website: result.localizedWebsite,
          description: result.localizedDescription,
          organizationType: result.organizationType,
          primaryOrganizationType: result.primaryOrganizationType,
          profileImageUrl:
            image?.downloadUrl ||
            "https://app.meetassembly.com/linkedin/default_organization.png",
          profileImageExpiresAt: image?.downloadUrlExpiresAt || null,
          createdAt: new Date(result.created.time),
        };
      });

      return createSuccessResponse(organizations, response.status);
    } catch (error) {
      return catchLinkedInError(error, "Error querying organization by URN");
    }
  };

  getOrganizationByVanityName = async (
    vanityName: string
  ): Promise<
    Response<{
      id: string;
      urn: string;
      name: string;
      website: string;
      vanityName: string;
      primaryOrganizationType: PrimaryOrganizationType;
      profileImageUrl: string;
      profileImageExpiresAt: Date | null;
    } | null>
  > => {
    try {
      const params = new URLSearchParams({
        q: "vanityName",
        vanityName,
      });

      const response = await this.client_rest_V202411.get(
        `/organizations?${params}`
      );

      const element = response.data.elements[0] as {
        id: number;
        vanityName: string;
        localizedName: string;
        primaryOrganizationType: PrimaryOrganizationType;
        localizedWebsite: string;
        logoV2: {
          cropped: string;
        };
      };

      if (!element) {
        return createSuccessResponse(null, response.status);
      } else {
        const logoUrn = element.logoV2.cropped;
        const imageResponse = await this.getImagesByUrn(logoUrn);
        if (imageResponse.errors) {
          return createErrorResponse(
            imageResponse.errors,
            imageResponse.status,
            imageResponse.rawError
          );
        }

        const image = imageResponse.data[0];
        return createSuccessResponse(
          {
            id: element.id.toString(),
            urn: `urn:li:organization:${element.id}`,
            name: element.localizedName,
            website: element.localizedWebsite,
            vanityName: element.vanityName,
            primaryOrganizationType: element.primaryOrganizationType,
            profileImageUrl: image.downloadUrl,
            profileImageExpiresAt: image.downloadUrlExpiresAt,
          },
          response.status
        );
      }
    } catch (error) {
      return catchLinkedInError(
        error,
        "Error querying organization by username"
      );
    }
  };

  getFollowerCount = async (
    urn: string
  ): Promise<Response<{ followerCount: number }>> => {
    // https://learn.microsoft.com/en-us/linkedin/marketing/integrations/community-management/organizations/organization-lookup-api?view=li-lms-2023-06&tabs=http#retrieve-organization-follower-count
    if (urn.includes("organization")) {
      return this.getOrganizationFollowerCount(urn);
    } else {
      return createErrorResponse(
        ["Follower count can only be retrieved for organizations"],
        StatusCodes.BAD_REQUEST
      );
    }
  };
}

export default LinkedIn;
