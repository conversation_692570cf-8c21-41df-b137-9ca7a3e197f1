import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { useElementSize, useResizeObserver } from "@mantine/hooks";
import Uppy from "@uppy/core";
import { arrayMoveImmutable } from "array-move";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { useMemo, useRef, useState } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import AssetUploadContainer from "~/components/ui/AssetUploadContainer";
import { useBreakpoints } from "~/hooks";
import { AssetType } from "~/types/Asset";
import { handleFilesUpload, trpc } from "~/utils";
import EditImageModal, {
  EditImageModalProperties,
} from "~/utils/EditImageModal";
import { AssetsUploadReorderControls } from "../../files/AssetControls";
import AssetsNewDesign, { UploadingAsset } from "../../files/AssetsNewDesign";
import FileDropzone from "../../files/FileDropzone";
import FileUploadInput from "../../files/FileUploadInput";

type Props = {
  linkedInContent: {
    id: string;
    copy: string;
    documentTitle: string | null;
  };
  coverPhotoUrl?: string;
  isReadOnly: boolean;
  draggedOver: boolean;
  setDraggedOver: (isDragged: boolean) => void;
  uploadingAssets: UploadingAsset[];
  getValidFiles: (files: File[]) => Promise<File[]>;
  uppy: Uppy | null;
  assets: AssetType[];
  setAssets: (
    value: AssetType[] | ((prev: AssetType[]) => AssetType[])
  ) => void;
};

const LinkedInAssetPreviewNewDesign = ({
  linkedInContent,
  coverPhotoUrl,
  isReadOnly,
  draggedOver,
  setDraggedOver,
  uploadingAssets,
  getValidFiles,
  uppy,
  assets,
  setAssets,
}: Props) => {
  const { ref: heightRef, height: calculatedHeight } = useElementSize();
  const height = calculatedHeight || 460;
  const { id: contentID, documentTitle } = linkedInContent;
  const { isMobile } = useBreakpoints();

  const [numPages, setNumPages] = useState<number>(1);
  const [isHovering, setIsHovering] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);

  const [pdfRef, rect] = useResizeObserver<HTMLDivElement>();

  const assetsRef = useRef<HTMLDivElement>(null);
  const randomID = useMemo(() => Math.random().toString(36), []);

  const [isEditingAssets, setIsEditingAssets] = useState<boolean>(false);
  const deleteAssetMutation = trpc.linkedInContent.deleteAsset.useMutation();
  const updateAssetPositionMutation =
    trpc.linkedInContent.updateAssetPositions.useMutation();
  const [editImageModalProperties, setEditImageModalProperties] =
    useState<EditImageModalProperties | null>(null);

  const acceptedFileTypesMap = {
    "image/jpeg": [".jpg", ".jpeg"],
    "image/png": [".png"],
    "image/webp": [".webp"],
    "image/gif": [".gif"],
    "video/mp4": [".mp4"],
    "video/quicktime": [".mov"],
    "application/pdf": [".pdf"],
  };

  const acceptedMimeTypes = Object.keys(acceptedFileTypesMap);

  const onSortEnd = (oldIndex: number, newIndex: number) => {
    setAssets((assets) => {
      const newAssets = arrayMoveImmutable(assets, oldIndex, newIndex);

      updateAssetPositionMutation.mutate({
        assets: newAssets.map((asset, index) => ({
          id: asset.id,
          position: index,
        })),
      });

      return newAssets;
    });
  };

  const onEditClick = (asset: AssetType) => {
    if (
      asset.metadata.mimetype.startsWith("image/") &&
      asset.metadata.mimetype !== "image/gif"
    ) {
      setEditImageModalProperties({
        title: "Edit Image",
        channel: "LinkedIn",
        maxSizeMB: 5,
        isOpen: true,
        asset,
      });
    }
  };

  const onDeleteClick = (asset: AssetType) => {
    setAssets((prevAssets) =>
      prevAssets.filter((prevAsset) => prevAsset.id !== asset.id)
    );
    deleteAssetMutation.mutate({
      linkedInAssetID: asset.id,
    });
  };

  const handleUploadButtonClick = () => {
    const fileInput = document.getElementById(
      `file-upload-${randomID}`
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  };

  const renderContent = () => {
    if (isEditingAssets) {
      return (
        <AssetsNewDesign
          assets={assets}
          uploadingAssets={uploadingAssets}
          onDeleteClick={(asset) => {
            setAssets((prevAssets) => {
              const newAssets = prevAssets.filter(
                (prevAsset) => prevAsset.id !== asset.id
              );
              if (newAssets.length === 0) {
                setIsEditingAssets(false);
              }
              return newAssets;
            });
            deleteAssetMutation.mutate({
              linkedInAssetID: asset.id,
            });
          }}
          editImageModalProperties={editImageModalProperties}
          onEditClick={(asset) => {
            onEditClick(asset);
          }}
          onUpload={async (file, updateAssetID) => {
            if (uppy) {
              if (updateAssetID) {
                uppy.addFile({
                  name: file.name,
                  type: file.type,
                  data: file,
                  meta: {
                    contentID,
                    updateAssetID,
                  },
                });
              } else {
                await handleFilesUpload({
                  files: [file],
                  uppy,
                  meta: {
                    contentID,
                  },
                  getValidFiles,
                });
              }
            }
          }}
          onEditImageModalClose={() => setEditImageModalProperties(null)}
          showNewEditButton={true}
          refetchAssets={() => {}}
          isReadOnly={isReadOnly}
          onDrop={async (files) => {
            setDraggedOver(false);
            if (uppy) {
              await handleFilesUpload({
                files,
                uppy,
                meta: {
                  contentID,
                },
                getValidFiles,
              });
            }
          }}
          showDropzone={draggedOver}
          acceptedFileTypes={acceptedFileTypesMap}
          onSortEnd={onSortEnd}
          onCloseClick={() => setIsEditingAssets(false)}
          onUploadButtonClick={handleUploadButtonClick}
        />
      );
    }

    const uploadedAssets = assets;
    const newUploadingAssets = uploadingAssets.filter(
      (asset) => !asset.updateAssetID
    );

    const hasUploadedVideo =
      uploadedAssets[0]?.metadata.mimetype.startsWith("video");
    const uploadingVideo = newUploadingAssets.find((asset) =>
      asset.type.startsWith("video/")
    );
    const isVideo = hasUploadedVideo || uploadingVideo;

    if (isVideo) {
      return (
        <AssetUploadContainer
          asset={!!uploadingVideo ? uploadingVideo : uploadedAssets[0]}
          coverPhotoUrl={coverPhotoUrl}
          onDeleteClick={onDeleteClick}
          isReadOnly={isReadOnly}
          useLinkedInLayout={true}
        />
      );
    }

    const displayAssets = [...uploadedAssets, ...newUploadingAssets];
    if (displayAssets.length === 0) {
      return null;
    } else if (displayAssets.length === 1) {
      const asset = displayAssets[0];
      const isPDF =
        "metadata" in asset && asset.metadata?.mimetype === "application/pdf";

      if (isPDF) {
        pdfjs.GlobalWorkerOptions.workerSrc = new URL(
          "pdfjs-dist/build/pdf.worker.min.js",
          import.meta.url
        ).toString();

        return (
          <div
            // @ts-expect-error
            ref={pdfRef}
            className="group relative flex w-full items-center justify-center"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            <div>
              <AnimatePresence>
                {isHovering && pageNumber === 1 && (
                  <motion.div
                    className={classNames(
                      "font-body absolute top-0 z-10 flex w-full items-center justify-end bg-black/60 px-4 text-left font-medium text-white opacity-0 transition-opacity group-hover:opacity-100"
                    )}
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 40 }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.4 }}
                  >
                    {documentTitle || asset.name.split(".")[0]}
                    <span className="font-body-sm ml-1">
                      • {numPages} pages
                    </span>
                  </motion.div>
                )}
              </AnimatePresence>
              <button
                disabled={pageNumber === 1}
                onClick={() => setPageNumber(pageNumber - 1)}
                className={classNames(
                  "absolute inset-y-1/2 left-0 z-10 flex h-8 w-8 items-center justify-center rounded-full bg-black/60 opacity-0 transition-all hover:bg-black",
                  { "group-hover:opacity-100": pageNumber !== 1 }
                )}
              >
                <ChevronLeftIcon className="h-4 w-4 text-white" />
              </button>
              <button
                disabled={pageNumber === numPages}
                onClick={() => setPageNumber(pageNumber + 1)}
                className={classNames(
                  "absolute inset-y-1/2 right-0 z-10 flex h-8 w-8 items-center justify-center rounded-full bg-black/60 opacity-0 transition-all hover:bg-black",
                  {
                    "group-hover:opacity-100": pageNumber !== numPages,
                  }
                )}
              >
                <ChevronRightIcon className="h-4 w-4 text-white" />
              </button>
            </div>
            <Document
              file={"signedUrl" in asset ? asset.signedUrl : ""}
              onLoadSuccess={(document) => {
                setNumPages(document.numPages);
              }}
            >
              <Page
                pageNumber={pageNumber}
                width={rect.width || undefined}
                renderTextLayer={false}
                renderAnnotationLayer={false}
              />
            </Document>
          </div>
        );
      } else {
        return (
          <AssetUploadContainer
            asset={asset}
            className="mx-auto w-auto"
            isReadOnly={isReadOnly}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
          />
        );
      }
    } else if (displayAssets.length === 2) {
      // Calculate aspect ratios and relative widths
      const aspectRatios = displayAssets.map(
        (asset) => (asset.width || 1) / (asset.height || 1)
      );
      const totalAspectRatio = aspectRatios.reduce(
        (sum, ratio) => sum + ratio,
        0
      );

      // Calculate proportional widths
      const widthPercentages = aspectRatios.map(
        (ratio) => (ratio / totalAspectRatio) * 100
      );

      return (
        <div className="flex gap-0.5">
          {displayAssets.map((asset, index) => (
            <div
              key={index}
              style={{
                width: `${widthPercentages[index]}%`,
                maxHeight: "500px",
              }}
            >
              <AssetUploadContainer
                asset={asset}
                className="h-auto w-full"
                isReadOnly={isReadOnly}
                onEditClick={onEditClick}
                onDeleteClick={onDeleteClick}
              />
            </div>
          ))}
        </div>
      );
    } else {
      const firstImage = displayAssets[0];
      const remainingImages = displayAssets.slice(1, 4);
      const missingImagesCount = displayAssets.slice(4).length;
      const firstImageAspectRatio =
        (firstImage.width || 1) / (firstImage.height || 1);
      const aspectRatios = remainingImages.map(
        (asset) => (asset.width || 1) / (asset.height || 1)
      );
      if (firstImageAspectRatio > 4 / 3) {
        // Vertical layout: First image on top, rest below
        const widestAspectRatio = Math.max(...aspectRatios);
        return (
          <div className="flex w-full flex-col gap-0.5">
            <AssetUploadContainer
              asset={firstImage}
              className="h-auto w-full"
              isReadOnly={isReadOnly}
              onEditClick={onEditClick}
              onDeleteClick={onDeleteClick}
            />
            <div className="flex gap-0.5">
              {remainingImages.map((image, index) => {
                return (
                  <div
                    key={image.id}
                    className="relative flex flex-1 items-center justify-center overflow-hidden"
                    style={{
                      aspectRatio: widestAspectRatio,
                    }}
                  >
                    <AssetUploadContainer
                      asset={image}
                      className="h-auto w-full"
                      style={{
                        width: "100%",
                        height: "100%",
                      }}
                      showControls={
                        !(
                          index === remainingImages.length - 1 &&
                          missingImagesCount > 0
                        )
                      }
                      isReadOnly={isReadOnly}
                      onEditClick={onEditClick}
                      onDeleteClick={onDeleteClick}
                    />
                    {index === remainingImages.length - 1 &&
                      missingImagesCount > 0 && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                          <span className="text-4xl font-medium text-white">
                            +{missingImagesCount}
                          </span>
                        </div>
                      )}
                  </div>
                );
              })}
            </div>
          </div>
        );
      } else {
        return (
          <div className="flex gap-0.5">
            <div ref={heightRef} className="h-full">
              <AssetUploadContainer
                asset={firstImage}
                className="h-full max-h-[540px] w-auto"
                style={{
                  height: isMobile ? undefined : height,
                }}
                isReadOnly={isReadOnly}
                onEditClick={onEditClick}
                onDeleteClick={onDeleteClick}
              />
            </div>
            <div
              className="flex h-full w-1/2 flex-col gap-0.5"
              style={{ height: height }}
            >
              {remainingImages.map((image, index) => (
                <div className="relative overflow-hidden" key={image.id}>
                  <AssetUploadContainer
                    asset={image}
                    className="h-full w-full object-cover"
                    style={{
                      height: height / remainingImages.length,
                      aspectRatio: "auto",
                    }}
                    showControls={
                      !(
                        index === remainingImages.length - 1 &&
                        missingImagesCount > 0
                      )
                    }
                    isReadOnly={isReadOnly}
                    onEditClick={onEditClick}
                    onDeleteClick={onDeleteClick}
                  />
                  {index === remainingImages.length - 1 &&
                    missingImagesCount > 0 && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                        <span className="text-4xl font-medium text-white">
                          +{missingImagesCount}
                        </span>
                      </div>
                    )}
                </div>
              ))}
            </div>
          </div>
        );
      }
    }
  };

  return (
    <div
      className={classNames("w-full", {
        "min-h-[338px]": draggedOver,
      })}
    >
      <FileUploadInput
        inputID={`file-upload-${randomID}`}
        contentID={contentID}
        uppy={uppy}
        getValidFiles={getValidFiles}
        acceptedMimeTypes={acceptedMimeTypes}
        isDisabled={uploadingAssets.length > 0}
        multiple={true}
      />
      {draggedOver ? (
        <motion.div
          key="dropzone"
          className="mb-3.5 w-full"
          initial={{ height: 338, opacity: 0 }}
          animate={{
            height: 338,
            opacity: 100,
          }}
        >
          <FileDropzone
            onDrop={async (files) => {
              setDraggedOver(false);
              if (uppy) {
                await handleFilesUpload({
                  files,
                  uppy,
                  meta: {
                    contentID,
                  },
                  getValidFiles,
                });
              }
            }}
            acceptedFileTypes={acceptedFileTypesMap}
          />
        </motion.div>
      ) : (
        <motion.div
          key="assets"
          ref={assetsRef}
          initial={{ opacity: 0 }}
          animate={{
            opacity: 100,
          }}
          className={classNames({
            hidden: assets.length === 0 && uploadingAssets?.length === 0,
          })}
        >
          <div className="group/asset-controls relative -mr-7 mb-3.5 -ml-8 opacity-100 transition-all sm:mr-0 sm:ml-0">
            {renderContent()}
            {assets.length > 0 && !isEditingAssets && !isReadOnly && (
              <AssetsUploadReorderControls
                onUploadClick={handleUploadButtonClick}
                onReorderClick={() => setIsEditingAssets(true)}
                isReadOnly={isReadOnly}
              />
            )}
          </div>
        </motion.div>
      )}
      {editImageModalProperties && (
        <EditImageModal
          title={editImageModalProperties.title}
          asset={editImageModalProperties.asset}
          maxSizeMB={editImageModalProperties.maxSizeMB}
          channel={editImageModalProperties.channel}
          isOpen={editImageModalProperties.isOpen}
          onClose={() => setEditImageModalProperties(null)}
          onUpload={async (file) => {
            if (uppy && editImageModalProperties.asset) {
              uppy.addFile({
                name: file.name,
                type: file.type,
                data: file,
                meta: {
                  contentID,
                  updateAssetID: editImageModalProperties.asset.id,
                },
              });
            }
          }}
        />
      )}
    </div>
  );
};

export default LinkedInAssetPreviewNewDesign;
