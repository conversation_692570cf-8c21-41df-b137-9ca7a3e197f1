import { Asset } from "@prisma/client";
import sortBy from "lodash/sortBy";
import { AssetType } from "~/types/Asset";
import { removeEmptyElems } from "~/utils";
import enhanceAssetType from "./assets/enhanceAssetType";

const getAssetsForSlack = (post: {
  id: string;
  workspaceID: string;
  slackContent: {
    assets: {
      id: string;
      asset: Asset;
      position: number;
    }[];
  } | null;
}): AssetType[] => {
  if (!post.slackContent) {
    return [];
  }

  return removeEmptyElems(
    sortBy(post.slackContent.assets, "position").map((asset) => ({
      ...enhanceAssetType(asset.asset),
      id: asset.id,
      position: asset.position,
    }))
  );
};

export default getAssetsForSlack;
