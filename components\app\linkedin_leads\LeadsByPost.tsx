import {
  ArrowDownTrayIcon,
  ArrowLeftIcon,
  ChatBubbleLeftEllipsisIcon,
  HandThumbUpIcon,
  MinusIcon,
} from "@heroicons/react/24/outline";
import NumberFlow from "@number-flow/react";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import assert from "assert";
import { AnimatePresence, motion } from "framer-motion";
import { DateTime } from "luxon";
import { useQueryState } from "nuqs";
import { useMemo } from "react";
import { toast } from "sonner";
import { Checkbox, Loader, TextLink, Tooltip } from "~/components";
import Avatar from "~/components/ui/Avatar";
import Button from "~/components/ui/Button";
import ButtonGroup from "~/components/ui/ButtonGroup";
import DropdownMenu from "~/components/ui/DropdownMenu";
import Table from "~/components/ui/Table";
import { downloadCSV, handleTrpcError, trpc } from "~/utils";
import getLinkedInProfilePermalink from "~/utils/linkedin/getLinkedInProfilePermalink";
import { RouterOutput } from "~/utils/trpc";

type LeadsByPostProps = {
  integrationID: string | null;
};

type Lead =
  RouterOutput["leads"]["linkedIn"]["getLeadsByPost"]["post"]["leads"][number];

const LeadsByPost = ({ integrationID }: LeadsByPostProps) => {
  const [selectedPostID, setSelectedPostID] = useQueryState<string | null>(
    "selectedPost",
    {
      parse: (value) => value ?? null,
      defaultValue: null,
    }
  );

  const getPostsByIntegrationQuery =
    trpc.leads.linkedIn.getPostsByIntegration.useQuery(
      {
        integrationID: integrationID!,
      },
      { enabled: !!integrationID }
    );

  const getPostLeadsQuery = trpc.leads.linkedIn.getLeadsByPost.useQuery(
    {
      postID: selectedPostID,
    },
    { enabled: !!selectedPostID }
  );

  const initializePostScrapingJobMutation =
    trpc.leads.linkedIn.initializePostScrapingJob.useMutation({
      onSuccess: () => {
        getPostsByIntegrationQuery.refetch();
        toast.success("Leads data is being refreshed", {
          description: "This may take a few minutes",
        });
      },
      onError: (error) => {
        handleTrpcError({
          error,
          title: "Failed to refresh leads data",
        });
      },
    });

  const columnHelper = createColumnHelper<Lead>();

  const columns = useMemo(() => {
    return [
      // Add selection column
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (
            <Checkbox
              value={table.getIsAllPageRowsSelected()}
              onChange={(value) => table.toggleAllPageRowsSelected(!!value)}
              intent="basic"
            />
          );
        },
        cell: ({ row }) => (
          <Checkbox
            value={row.getIsSelected()}
            onChange={(checked) => {
              row.toggleSelected();
            }}
            intent="basic"
          />
        ),
        size: 50,
      }),

      columnHelper.accessor(
        (row) => `${row.author.firstName} ${row.author.lastName}`,
        {
          header: "Name",
          id: "name",
          cell: (info) => (
            <div className="flex items-center gap-2">
              <Avatar.Root>
                <Avatar.Image
                  src={info.row.original.author.profileImageUrl}
                  className="h-6 w-6 rounded-full"
                />
                <Avatar.Fallback>
                  <img
                    src="/linkedin/default_user.png"
                    className="h-6 w-6 rounded-full"
                  />
                </Avatar.Fallback>
              </Avatar.Root>
              <TextLink
                href={getLinkedInProfilePermalink({
                  vanityName: info.row.original.author.vanityName,
                  type: "User",
                })}
                value={`${info.row.original.author.firstName} ${info.row.original.author.lastName}`}
              />
            </div>
          ),
        }
      ),
      columnHelper.accessor("type", {
        header: "Type",
        cell: (info) => (
          <div className="mr-4">
            <Tooltip value={info.row.original.text}>
              <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1">
                {info.getValue() === "COMMENT" ? (
                  <ChatBubbleLeftEllipsisIcon className="h-4 w-4" />
                ) : (
                  <HandThumbUpIcon className="h-4 w-4" />
                )}
                1
              </div>
            </Tooltip>
          </div>
        ),
      }),
      columnHelper.accessor((row) => row.author.currentJob?.title, {
        header: "Role",
        id: "role",
        cell: (info) => {
          const title = info.getValue();
          if (!title) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            return (
              <div className="font-body-sm max-w-48 truncate">{title}</div>
            );
          }
        },
      }),
      columnHelper.accessor((row) => row.author.currentJob, {
        header: "Company",
        id: "company",
        cell: (info) => {
          const currentJob = info.getValue();
          if (!currentJob) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else if (currentJob.organization) {
            return (
              <div className="bg-lightest-gray-50 w-fit max-w-48 truncate rounded-full px-2 py-1">
                <a
                  href={getLinkedInProfilePermalink({
                    vanityName: currentJob.organization.vanityName,
                    type: "Organization",
                  })}
                  target="_blank"
                  className="hover:text-secondary transition-colors"
                >
                  {currentJob.organization.name}
                </a>
              </div>
            );
          } else {
            return (
              <div className="bg-lightest-gray-50 w-fit max-w-48 truncate rounded-full px-2 py-1">
                {currentJob.organizationName}
              </div>
            );
          }
        },
      }),
      columnHelper.accessor((row) => row.author.currentJob, {
        header: "Industry",
        id: "industry",
        cell: (info) => {
          const currentJob = info.getValue();
          if (!currentJob) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            const industry =
              currentJob.organization?.organizationData?.industry;
            if (!industry) {
              return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
            } else {
              return (
                <div className="bg-lightest-gray-50 w-fit max-w-48 truncate rounded-full px-2 py-1">
                  {industry}
                </div>
              );
            }
          }
        },
      }),
      columnHelper.accessor((row) => row.author.currentJob?.location, {
        header: "Location",
        id: "location",
        cell: (info) => {
          const location = info.getValue();
          if (!location) {
            return <MinusIcon className="text-medium-dark-gray h-4 w-4" />;
          } else {
            return (
              <Tooltip value={location}>
                <div className="max-w-40 truncate">{location}</div>
              </Tooltip>
            );
          }
        },
      }),
    ];
  }, []);

  const post = getPostLeadsQuery.data?.post;

  const leadsTable = useReactTable({
    data: post?.leads || [],
    columns: columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const integration = getPostsByIntegrationQuery.data?.integration;

  if ((selectedPostID != null && post == null) || integration == null) {
    return (
      <div className="flex w-full items-center justify-center">
        <Loader size="lg" />
      </div>
    );
  } else if (post != null) {
    const profile = integration.account;
    const { lastScrapedEngagementsAt, isScraping } = post;
    const hasBeenScraped = lastScrapedEngagementsAt != null;

    const handleDownloadCSV = () => {
      let selectedRows = leadsTable.getSelectedRowModel().rows;
      if (selectedRows.length === 0) {
        selectedRows = leadsTable.getRowModel().rows;
      }

      const headers = [
        "id",
        "type",
        "text",
        "first_name",
        "last_name",
        "headline",
        "url",
        "follower_count",
        "job_title",
        "job_start_date",
        "job_end_date",
        "company",
        "company_url",
        "company_industry",
      ];

      const rows = selectedRows.map((row) => {
        const lead = row.original;

        return [
          lead.id,
          lead.type,
          lead.text || "",
          lead.author.firstName,
          lead.author.lastName,
          lead.author.headline,
          getLinkedInProfilePermalink({
            vanityName: lead.author.vanityName,
            type: "User",
          }),
          lead.author.followerCount,
          lead.author.currentJob?.title,
          lead.author.currentJob?.startDate.toISOString().replace("Z", ""),
          lead.author.currentJob?.endDate?.toISOString().replace("Z", ""),
          lead.author.currentJob?.organizationName,
          lead.author.currentJob?.organization
            ? getLinkedInProfilePermalink({
                vanityName: lead.author.currentJob?.organization?.vanityName,
                type: "Organization",
              })
            : "",
          lead.author.currentJob?.organization?.organizationData?.industry,
        ];
      });

      assert(profile, "Selected integration not found");

      const fileName = `linkedin_leads_${profile.vanityName}_${
        new Date().toISOString().split("T")[0]
      }.csv`;

      downloadCSV({
        headers,
        rows,
        fileName,
      });
    };

    const stats = post.stats?.[0];
    const likeCount = stats?.likeCount;
    const commentCount = stats?.commentCount;

    return (
      <div className="font-body-sm mt-4 flex flex-col gap-3 pb-4">
        <div className="flex items-center justify-between">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {
              setSelectedPostID(null);
            }}
          >
            <ArrowLeftIcon className="h-4 w-4" />
            Back to All Posts
          </Button>
        </div>
        <div className="border-lightest-gray flex w-full items-center justify-between rounded-sm border p-5">
          <div className="flex items-center gap-3">
            <Avatar.Root>
              <Avatar.Image
                src={profile.profileImageUrl}
                className="h-7 w-7 rounded-full"
              />
            </Avatar.Root>
            <div className="w-44 truncate overflow-clip text-left font-medium">
              {post.post?.title || post.commentary}
            </div>
            <div className="flex items-center gap-8">
              <div className="text-medium-dark-gray">{profile.name}</div>
              {hasBeenScraped ? (
                <div className="flex items-center gap-2">
                  <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1">
                    <HandThumbUpIcon className="h-4 w-4" />
                    <div>{post.reactions.length}</div>
                  </div>
                  <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1">
                    <ChatBubbleLeftEllipsisIcon className="h-4 w-4" />
                    <div>{post.comments.length}</div>
                  </div>
                </div>
              ) : likeCount != null && commentCount != null ? (
                <div className="flex items-center gap-2">
                  <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1 opacity-50">
                    <HandThumbUpIcon className="h-4 w-4" />
                    <div>{likeCount}</div>
                  </div>
                  <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1 opacity-50">
                    <ChatBubbleLeftEllipsisIcon className="h-4 w-4" />
                    <div>{commentCount}</div>
                  </div>
                  <div className="text-medium-dark-gray">No leads data yet</div>
                </div>
              ) : (
                <div className="text-medium-dark-gray">No leads data yet</div>
              )}
            </div>
          </div>
          <>
            {isScraping ? (
              <div className="text-medium-dark-gray flex items-center gap-2">
                <Loader size="sm" color="mediumDarkGray" />
                Getting latest leads data
              </div>
            ) : (
              <div className="flex items-center gap-3">
                {hasBeenScraped && (
                  <div className="text-medium-dark-gray">
                    Last updated{" "}
                    {DateTime.fromJSDate(lastScrapedEngagementsAt).toRelative()}
                  </div>
                )}
                <Button
                  size="sm"
                  variant="secondary"
                  disabled={initializePostScrapingJobMutation.isPending}
                  onClick={() => {
                    initializePostScrapingJobMutation.mutate({
                      linkedInPostID: post.id,
                    });
                  }}
                >
                  {hasBeenScraped ? "Refresh Leads" : "Get Leads Data"}
                </Button>
              </div>
            )}
          </>
        </div>
        <div className="flex flex-col gap-2">
          <Table.Toolbar>
            <AnimatePresence initial={false} mode="popLayout">
              {leadsTable.getSelectedRowModel().rows.length > 0 ? (
                <motion.div
                  key="selected-toolbar"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.18, ease: "easeOut" }}
                >
                  <ButtonGroup.Root>
                    <ButtonGroup.Details>
                      <span className="text-secondary">
                        <NumberFlow
                          value={leadsTable.getSelectedRowModel().rows.length}
                        />
                      </span>{" "}
                      <span className="text-medium-dark-gray">Selected</span>
                    </ButtonGroup.Details>
                    <DropdownMenu.Root>
                      <DropdownMenu.Trigger>
                        <ButtonGroup.Button>
                          <ArrowDownTrayIcon className="h-4 w-4" />
                          Export Selected
                        </ButtonGroup.Button>
                      </DropdownMenu.Trigger>
                      <DropdownMenu.Content align="start" className="space-y-1">
                        <DropdownMenu.Item onClick={handleDownloadCSV}>
                          Export as CSV
                        </DropdownMenu.Item>
                      </DropdownMenu.Content>
                    </DropdownMenu.Root>
                  </ButtonGroup.Root>
                </motion.div>
              ) : (
                <motion.div
                  key="all-toolbar"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.18, ease: "easeOut" }}
                >
                  <DropdownMenu.Root>
                    <DropdownMenu.Trigger>
                      <Button variant="secondary" size="sm">
                        <ArrowDownTrayIcon className="h-4 w-4" />
                        Export Leads
                      </Button>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content align="start" className="space-y-1">
                      <DropdownMenu.Item onClick={handleDownloadCSV}>
                        Export as CSV
                      </DropdownMenu.Item>
                    </DropdownMenu.Content>
                  </DropdownMenu.Root>
                </motion.div>
              )}
            </AnimatePresence>
          </Table.Toolbar>
        </div>
        <Table.Root>
          <Table.Header>
            {leadsTable.getHeaderGroups().map((headerGroup) => (
              <Table.Row key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <Table.Head key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </Table.Head>
                ))}
              </Table.Row>
            ))}
          </Table.Header>
          <Table.Body>
            {leadsTable.getRowModel().rows.map((row) => {
              return (
                <Table.Row key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <Table.Cell key={cell.id} className="py-2">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </Table.Cell>
                  ))}
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table.Root>
      </div>
    );
  } else {
    const profile = integration.account;

    return (
      <div className="font-body-sm flex flex-col gap-3">
        {profile.posts.map((post) => {
          const { lastScrapedEngagementsAt, isScraping } = post;
          const hasBeenScraped = lastScrapedEngagementsAt != null;

          const stats = post.stats?.[0];
          const likeCount = stats?.likeCount;
          const commentCount = stats?.commentCount;

          return (
            <button
              key={post.id}
              className="border-lightest-gray hover:border-basic flex w-full items-center justify-between rounded-sm border p-5 transition-colors"
              onClick={() => {
                setSelectedPostID(post.id);
              }}
            >
              <div className="flex items-center gap-3">
                <Avatar.Root>
                  <Avatar.Image
                    src={profile.profileImageUrl}
                    className="h-7 w-7 rounded-full"
                  />
                </Avatar.Root>
                <div className="w-44 truncate overflow-clip text-left font-medium">
                  {post.post?.title || post.commentary}
                </div>
                <div className="flex items-center gap-8">
                  <div className="text-medium-dark-gray">{profile.name}</div>
                  {hasBeenScraped ? (
                    <div className="flex items-center gap-2">
                      <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1">
                        <HandThumbUpIcon className="h-4 w-4" />
                        <div>{post._count.reactions}</div>
                      </div>
                      <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1">
                        <ChatBubbleLeftEllipsisIcon className="h-4 w-4" />
                        <div>{post._count.comments}</div>
                      </div>
                    </div>
                  ) : likeCount != null && commentCount != null ? (
                    <div className="flex items-center gap-2">
                      <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1 opacity-50">
                        <HandThumbUpIcon className="h-4 w-4" />
                        <div>{likeCount}</div>
                      </div>
                      <div className="bg-lightest-gray-50 flex w-fit items-center gap-1 rounded-3xl px-2 py-1 opacity-50">
                        <ChatBubbleLeftEllipsisIcon className="h-4 w-4" />
                        <div>{commentCount}</div>
                      </div>
                      <div className="text-medium-dark-gray">
                        No leads data yet
                      </div>
                    </div>
                  ) : (
                    <div className="text-medium-dark-gray">
                      No leads data yet
                    </div>
                  )}
                </div>
              </div>
              <>
                {isScraping ? (
                  <div className="text-medium-dark-gray flex items-center gap-2">
                    <Loader size="sm" color="mediumDarkGray" />
                    Getting latest leads data
                  </div>
                ) : (
                  <div className="flex items-center gap-3">
                    {hasBeenScraped && (
                      <div className="text-medium-dark-gray">
                        Last updated{" "}
                        {DateTime.fromJSDate(
                          lastScrapedEngagementsAt
                        ).toRelative()}
                      </div>
                    )}
                    <Button
                      size="sm"
                      variant="secondary"
                      disabled={initializePostScrapingJobMutation.isPending}
                      onClick={(e) => {
                        e.stopPropagation();
                        initializePostScrapingJobMutation.mutate({
                          linkedInPostID: post.id,
                        });
                      }}
                    >
                      {hasBeenScraped ? "Refresh Leads" : "Get Leads Data"}
                    </Button>
                  </div>
                )}
              </>
            </button>
          );
        })}
      </div>
    );
  }
};

export default LeadsByPost;
