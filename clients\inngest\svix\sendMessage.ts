import { slugify } from "inngest";
import { Svix } from "svix";
import { inngest } from "../inngest";

const sendMessageToSvix = inngest.createFunction(
  {
    id: slugify("Svix Send Message"),
    name: "Svix Send Message",
    throttle: {
      limit: 400,
      period: "1s",
      burst: 400,
    },
  },
  { event: "svix.sendMessage" },
  async ({ event, step }) => {
    await step.run("Send Message to Svix", async () => {
      const { applicationID, eventType, channels, payload } = event.data;

      const svix = new Svix(process.env.SVIX_API_KEY!);

      await svix.message.create(applicationID, {
        eventType,
        channels,
        payload,
      });
    });
  }
);

export default sendMessageToSvix;
