import prisma from "~/clients/Prisma";

import { LinkedInAccountType, LinkedInOrganizationRole } from "@prisma/client";
import assert from "assert";
import LinkedIn from "~/clients/linkedin/LinkedIn";
import { removeEmptyElems } from "~/utils";

export const analyticsRoles: LinkedInOrganizationRole[] = [
  "ADMINISTRATOR",
  "CONTENT_ADMINISTRATOR",
  "CURATOR",
  "ANALYST",
];

const getLinkedInPosts = async (integration: {
  accessToken: string;
  account: {
    id: string;
    urn: string;
    type: LinkedInAccountType;
    name: string;
  };
  role: LinkedInOrganizationRole;
}) => {
  const { account, role } = integration;

  if (account.type === "User") {
    console.log(`${account.name}: Skipping user account`);
    return;
  } else if (!analyticsRoles.includes(role)) {
    console.log(`${account.name}: Skipping non-analytics role ${role}`);
    return;
  }

  console.log(`${account.name}: BEGIN querying posts`);

  const linkedin = new LinkedIn(integration.accessToken);
  const response = await linkedin.getOrganizationPosts(account.urn);

  const posts = response.data;

  if (posts == null) {
    console.log(`${account.name}: ERROR querying posts`, response.errors);
    return;
  }

  assert(posts != null, "Posts cannot be null");

  const articleData = removeEmptyElems(posts.map((post) => post.article));
  await prisma.linkedInArticle.createMany({
    data: articleData,
    skipDuplicates: true,
  });

  const articles = await prisma.linkedInArticle.findMany({
    where: {
      url: {
        in: articleData.map((article) => article.url),
      },
    },
  });

  const numPosts = await prisma.linkedInPost.createMany({
    data: posts.map((post) => {
      const { article, stats, ...rest } = post;

      let articleID = null;
      if (article != null) {
        articleID = articles.find((article) => article.url === article.url)?.id;
        assert(articleID != null, "Article ID cannot null");
      }

      return {
        ...rest,
        articleID,
      };
    }),
    skipDuplicates: true,
  });

  const numStats = await prisma.linkedInPostStats.createMany({
    data: posts.map((post) => {
      const { stats } = post;

      return {
        postID: post.id,
        ...stats,
      };
    }),
  });

  const assemblyPosts = await prisma.post.findMany({
    where: {
      channels: {
        has: "LinkedIn",
      },
      linkedInLink: {
        in: posts.map(
          (post) => `https://www.linkedin.com/feed/update/${post.id}`
        ),
      },
      linkedInPostID: null,
    },
    select: {
      id: true,
      linkedInLink: true,
    },
  });

  const promises = assemblyPosts.map(async (post) => {
    if (!post.linkedInLink) {
      return;
    }
    const urn = post.linkedInLink.split("/").pop();

    await prisma.post.update({
      where: {
        id: post.id,
      },
      data: {
        linkedInPostID: urn,
      },
    });
  });
  await Promise.all(promises);

  console.log(
    `${account.name}: FINISHED ${numPosts.count} posts and ${numStats.count} stats`
  );
};

export default getLinkedInPosts;
