import { useMounted } from "@mantine/hooks";
import { Channel } from "@prisma/client";
import React, { ReactNode } from "react";
import AnimateChangeInHeight from "~/components/AnimateChangeInHeight";
import HelpTooltip from "~/components/HelpTooltip";
import Skeleton from "~/components/ui/Skeleton";
import { getFullName } from "~/utils";
import AccountChannelAvatar from "../../AccountChannelAvatar";

const Root = ({
  children,
  isLoading,
  isEmpty,
}: {
  children: ReactNode;
  isLoading: boolean;
  isEmpty: boolean;
}) => {
  const isMounted = useMounted();

  if (isLoading || !isMounted) {
    return (
      <div className="border-light-gray animate-pulse rounded-2xl border px-7 py-2">
        {[...Array(3)].map((_, index) => (
          <React.Fragment key={index}>
            {index > 0 && <div className="bg-light-gray h-px" />}
            <div className="flex items-center justify-between py-3.5">
              <div className="flex items-center gap-2.5">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex flex-col gap-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
              <Skeleton className="h-5 w-5 rounded-full" />
            </div>
          </React.Fragment>
        ))}
      </div>
    );
  }

  if (isEmpty) {
    return null;
  }

  return (
    <div className="border-light-gray max-h-[476px] overflow-y-auto rounded-2xl border px-7 py-2">
      <AnimateChangeInHeight>
        {React.Children.toArray(children).map((child, index, array) => (
          <div key={index}>
            {child}
            {index < array.length - 1 && <div className="bg-light-gray h-px" />}
          </div>
        ))}
      </AnimateChangeInHeight>
    </div>
  );
};
Root.displayName = "SocialProfiles";

type ProfileProps = {
  children: ReactNode;
};

const Profile = ({ children }: ProfileProps) => {
  return (
    <div className="group flex items-center justify-between py-3.5">
      {children}
    </div>
  );
};
Profile.displayName = "SocialProfilesProfile";

type ProfileDetailsProps = {
  profile: {
    id: string;
    name: string;
    detail?: string | null;
    channel: Channel;
    profileImageUrl: string;
    numScheduledPosts?: number;
    isReintegrationRequired?: boolean;
  } & (
    | {
        createdAt: Date;
        createdBy: {
          firstName: string | null;
          lastName: string | null;
        };
      }
    | {
        createdAt?: never;
        createdBy?: never;
      }
  );
};

const ProfileDetails = ({ profile }: ProfileDetailsProps) => {
  const { id, name, detail, channel, profileImageUrl, createdAt, createdBy } =
    profile;

  return (
    <div key={id} className="flex items-center gap-2.5">
      <div className="shrink-0">
        <AccountChannelAvatar
          size={32}
          account={{
            channel,
            profileImageUrl,
          }}
        />
      </div>
      <div className="min-w-0 flex-1">
        <div className="flex items-center gap-1">
          <p className="font-body-sm truncate font-medium">{name}</p>
          {createdAt && (
            <div className="opacity-0 transition-opacity group-hover:opacity-100">
              <HelpTooltip
                value={
                  <span>
                    Connected by{" "}
                    <span className="text-secondary">
                      {getFullName(createdBy, {
                        shortenedLastName: true,
                      })}
                    </span>{" "}
                    on{" "}
                    <span className="text-secondary">
                      {createdAt.toLocaleDateString()}
                    </span>
                  </span>
                }
              />
            </div>
          )}
        </div>
        <p className="font-body-sm text-medium-dark-gray truncate">{detail}</p>
      </div>
    </div>
  );
};
ProfileDetails.displayName = "SocialProfilesProfileDetails";

export default {
  Root,
  Profile,
  ProfileDetails,
};
