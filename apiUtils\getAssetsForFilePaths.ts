import assert from "assert";
import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import supabaseAdmin from "~/clients/supabaseAdminClient";
import { ONE_YEAR_IN_SECONDS } from "~/constants";
import { AssetType } from "~/types/Asset";

const getAssetsForFilePaths = async (
  filePaths: {
    id: string;
    path: string;
  }[],
  options?: {
    includeSubDirectoryFiles?: boolean;
  }
) => {
  const promises = filePaths.map(async (filePath) => {
    const response = await supabaseAdmin.storage
      .from("assets")
      .list(filePath.path);
    const assets = response.data;
    const id = filePath.id;

    if (assets == null || assets.length == 0) {
      return { id, assets: [] };
    } else {
      const files = assets.filter((asset) => asset.metadata?.size > 0);
      const filePaths = files.map((file) => `${filePath.path}/${file.name}`);

      const folders = assets.filter((asset) => asset.metadata == null);
      const folderPaths = folders.map(
        (folder) => `${filePath.path}/${folder.name}`
      );
      let subDirectoryFiles: AssetType[] = [];
      if (options?.includeSubDirectoryFiles && folderPaths.length > 0) {
        const subPromises = folderPaths.map(async (path) => {
          return await getAssetsForFilePaths(
            [
              {
                id,
                path,
              },
            ],
            {
              includeSubDirectoryFiles: true,
            }
          );
        });

        const subdirectoryFileResponse = await Promise.all(subPromises);
        subDirectoryFiles = subdirectoryFileResponse.flatMap((response) => {
          assert(response[id]);
          return response[id];
        });
      }

      const { data } = await supabaseAdmin.storage
        .from("assets")
        .createSignedUrls(filePaths, ONE_YEAR_IN_SECONDS);

      if (!data) {
        return { id, assets: subDirectoryFiles };
      }

      const assetObjects = sortBy(
        files.map((asset) => {
          const filesWithPath = data.map((file) => {
            const path = decodeURIComponent(file.signedUrl)
              .split("?")[0]
              .split("object/sign/assets/")[1];

            return {
              ...file,
              createdPath: path,
            };
          });

          const fileData = filesWithPath.find(
            (file) =>
              file.path?.endsWith(asset.name) ||
              file.createdPath?.endsWith(asset.name)
          );
          assert(fileData);

          const url = fileData.signedUrl.replace(
            "http://localhost:54321",
            "https://ray-sweet-chipmunk.ngrok-free.app"
          );

          const metadata = asset.metadata as {
            eTag: string;
            size: number;
            mimetype: string;
            cacheControl: string;
            lastModified: string;
            contentLength: string;
          };

          return {
            ...asset,
            ...fileData,
            metadata: metadata,
            path: fileData.path || fileData.createdPath,
            url,
            mimetype: metadata.mimetype as string,
            eTag: metadata.eTag.replaceAll('"', ""),
            signedUrl: url,
            sizeBytes: metadata.size,
            sizeMB: metadata.size / 1_024 / 1_024,
            sizeKB: metadata.size / 1_024,
            urlExpiresAt: DateTime.now()
              .plus({ seconds: ONE_YEAR_IN_SECONDS })
              .toJSDate(),
            updatedAt: new Date(asset.updated_at),
            createdAt: new Date(asset.created_at),
          };
        }),
        "createdAt"
      ) as AssetType[];

      return { id, assets: [...assetObjects, ...subDirectoryFiles] };
    }
  });

  const results = await Promise.all(promises);
  const assetsMap: { [id: string]: AssetType[] } = {};
  results.forEach(({ id, assets }) => {
    assetsMap[id] = assets;
  });

  return assetsMap;
};

export default getAssetsForFilePaths;
