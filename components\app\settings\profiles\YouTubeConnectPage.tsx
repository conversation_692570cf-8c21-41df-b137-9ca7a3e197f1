import { Divider } from "~/components";
import SettingsLayout from "~/components/app/settings/SettingsLayout";
import ConnectProfile from "~/components/app/settings/profiles/ConnectProfile";
import Breadcrumb from "~/components/ui/Breadcrumb";
import Button from "~/components/ui/Button";
import { useWindowTitle } from "~/hooks";
import { openIntercomChat, trpc } from "~/utils";
import { setCookie } from "~/utils/cookies";

type Props = {
  type: "scheduling";
};

const handleYouTubeLogin = (url: string, accountID?: string) => {
  setCookie(
    "YOUTUBE_INTEGRATION",
    {
      accountID,
      referrer: window.location.pathname,
    },
    {
      path: "/",
      secure: true,
      sameSite: "lax",
      maxAge: 5 * 60, // 5 minutes in seconds
    }
  );

  window.location.href = url;
};

const YouTubeConnectPage = ({ type }: Props) => {
  useWindowTitle("YouTube Integration");

  const generateAuthUrlQuery =
    trpc.integration.youtube.generateAuthUrl.useQuery();

  const url = generateAuthUrlQuery.data?.url!;

  return (
    <SettingsLayout.Root>
      <Breadcrumb.Root className="mb-8">
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="/settings/profiles">
              Social Profiles
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href={`/settings/profiles/${type}`}>
              Choose Channel
            </Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Page>Connect</Breadcrumb.Page>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
      <ConnectProfile.Root>
        <ConnectProfile.Header
          channel="YouTubeShorts"
          description="Connect your YouTube Shorts account to Assembly."
        />
        <ConnectProfile.Content>
          <Button size="sm" onClick={() => handleYouTubeLogin(url)}>
            Connect YouTube
          </Button>
        </ConnectProfile.Content>
        <Divider className="my-2 md:my-4" />
        <ConnectProfile.Footer>
          <div className="space-y-1.5">
            <p className="font-medium">
              Note: only the owner of the account can connect.
            </p>
            <p className="text-medium-dark-gray">
              Due to restrictions from YouTube's side, only the owner of the
              YouTube account can integrate.
            </p>
          </div>
          <div className="space-y-1.5">
            <p className="font-medium">Need Help?</p>
            <p className="text-medium-dark-gray">
              Check out our help center article for a video walkthrough on how
              to connect. If you're still having issues, please{" "}
              <Button
                size="sm"
                variant="link"
                onClick={() => {
                  openIntercomChat(
                    "I'm having trouble connecting my YouTube channel: {details here}"
                  );
                }}
              >
                chat with us
              </Button>
              .
            </p>
          </div>
        </ConnectProfile.Footer>
      </ConnectProfile.Root>
    </SettingsLayout.Root>
  );
};

export default YouTubeConnectPage;
export { handleYouTubeLogin };
