import { PhotoIcon } from "@heroicons/react/24/outline";
import classNames from "classnames";
import uniq from "lodash/uniq";
import { useState } from "react";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import { useWorkspace } from "~/providers";
import { getDefaultProfilePicture } from "~/utils";

type TikTokAccount = {
  id: string;
  name: string | null;
  profileImageUrl: string | null;
};

type Props = {
  tikTokContent: {
    copy?: string;
    assets: {
      id: string;
      signedUrl: string;
      metadata: {
        mimetype: string;
      };
      createdAt: Date;
    }[];
  };
  connectedAccount: TikTokAccount | null | undefined;
};

const TikTokPostPreview = ({ tikTokContent, connectedAccount }: Props) => {
  const { currentWorkspace } = useWorkspace();
  const [showMore, setShowMore] = useState<boolean>(false);
  const nonEmpty =
    (tikTokContent.copy && tikTokContent.copy != "") ||
    tikTokContent.assets.length > 0;

  const accountName = connectedAccount?.name || currentWorkspace?.name;
  const profilePictureUrl =
    connectedAccount?.profileImageUrl ?? getDefaultProfilePicture("TikTok");

  const copy = tikTokContent?.copy;

  const cleanedCopy = (copy || "")
    .split("\n")
    .map((line) => line.trim())
    .join(" ");

  const videoUrl = tikTokContent.assets[0]?.signedUrl;

  if (!nonEmpty) {
    return (
      <div className="font-body text-medium-dark-gray mt-1">
        Preview will show when post content is added below
      </div>
    );
  } else {
    return (
      <div className="scrollbar-hidden flex h-[408px] justify-center overflow-auto">
        <div
          className={classNames(
            "relative w-[230px] rounded-xl text-[8px] text-white",
            {
              "bg-black": !!videoUrl,
              "bg-medium-gray": !videoUrl,
            }
          )}
        >
          {videoUrl ? (
            <div className="flex h-full items-center justify-center overflow-clip rounded-xl">
              <video
                src={tikTokContent?.assets[0].signedUrl}
                loop={true}
                onClick={(e) => {
                  const video = e.target as HTMLVideoElement;
                  if (video.paused) {
                    video.play();
                  } else {
                    video.pause();
                  }
                }}
              />
              {/* Add an overlay if showMore is true */}
              {showMore && (
                <div
                  className={classNames(
                    "absolute inset-0 rounded-xl bg-black transition-all",
                    {
                      "bg-opacity-0": !showMore,
                      "bg-opacity-40": showMore,
                    }
                  )}
                />
              )}
            </div>
          ) : (
            <div className="flex h-full flex-col items-center justify-center gap-2 text-white">
              <PhotoIcon className="h-6 w-6" />
              <div className="font-body text-center">
                Upload video in the <br />
                editor below
              </div>
            </div>
          )}
          {/* Top Side Icons */}
          <div className="absolute top-5 flex w-full justify-between px-2.5">
            <img src="/tiktok/live_icon.png" className="h-[14px] w-[14px]" />
            <div className="flex items-start gap-3 text-[8px] text-white">
              <div className="opacity-80">Following</div>
              <div className="flex flex-col items-center gap-0.5">
                <div>For You</div>
                <div className="h-0.5 w-4 bg-white" />
              </div>
            </div>
            <svg
              fill="currentColor"
              fontSize="24px"
              viewBox="0 0 48 48"
              xmlns="http://www.w3.org/2000/svg"
              width="14px"
              height="14px"
            >
              <path d="M21.26 7a15.26 15.26 0 1 0 0 30.52 15.26 15.26 0 0 0 0-30.52ZM2 22.26A19.26 19.26 0 1 1 36.23 34.4l9.65 9.66c.3.29.3.76 0 1.06l-1.76 1.76c-.3.3-.77.3-1.06 0l-9.66-9.65A19.26 19.26 0 0 1 2 22.27Z"></path>
            </svg>
          </div>
          {/* Bottom Right Side Icons */}
          <div className="absolute right-2 bottom-3 flex flex-col gap-4">
            <svg
              viewBox="0 0 48 48"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
              width="18px"
              height="18px"
            >
              <g
                filter="url(#Icon_Color-Like_Shadow_Alt_1_svg__b)"
                clipPath="url(#Icon_Color-Like_Shadow_Alt_1_svg__a)"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M15 4.5c6 0 9 4 9 4s3-4 9-4c7 0 12 5.5 12 12.5 0 8-6.54 15.13-12.5 20.5C28.82 40.81 26 43 24 43s-4.9-2.2-8.5-5.5C9.64 32.13 3 25 3 17 3 10 8 4.5 15 4.5Z"
                  fill="#fff"
                  fillOpacity="0.9"
                  shapeRendering="crispEdges"
                ></path>
              </g>
              <defs>
                <clipPath id="Icon_Color-Like_Shadow_Alt_1_svg__a">
                  <path fill="#fff" d="M0 0h48v48H0z"></path>
                </clipPath>
                <filter
                  id="Icon_Color-Like_Shadow_Alt_1_svg__b"
                  x="-1.5"
                  y="1.5"
                  width="51"
                  height="47.5"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood
                    floodOpacity="0"
                    result="BackgroundImageFix"
                  ></feFlood>
                  <feColorMatrix
                    in="SourceAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  ></feColorMatrix>
                  <feOffset dy="1.5"></feOffset>
                  <feGaussianBlur stdDeviation="2.25"></feGaussianBlur>
                  <feComposite in2="hardAlpha" operator="out"></feComposite>
                  <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"></feColorMatrix>
                  <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_81245_5661"
                  ></feBlend>
                  <feColorMatrix
                    in="SourceAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  ></feColorMatrix>
                  <feOffset dy="1.5"></feOffset>
                  <feGaussianBlur stdDeviation="0.75"></feGaussianBlur>
                  <feComposite in2="hardAlpha" operator="out"></feComposite>
                  <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"></feColorMatrix>
                  <feBlend
                    in2="effect1_dropShadow_81245_5661"
                    result="effect2_dropShadow_81245_5661"
                  ></feBlend>
                  <feBlend
                    in="SourceGraphic"
                    in2="effect2_dropShadow_81245_5661"
                    result="shape"
                  ></feBlend>
                </filter>
              </defs>
            </svg>
            <svg
              viewBox="0 0 48 48"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
            >
              <g filter="url(#Icon_Color-Comment_Shadow_svg__a)">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M38.5 35.31c4.1-4.11 6.5-8.4 6.5-13.38C45 11.8 35.73 3.6 24.3 3.6S3.6 11.8 3.6 21.93C3.6 32.05 13.17 39 24.6 39v3.36c0 1.06 1.1 1.74 2.04 1.24 2.92-1.59 8.33-4.76 11.85-8.29ZM14.23 19.46a2.95 2.95 0 0 1 2.96 2.93 2.95 2.95 0 0 1-2.96 2.94 2.95 2.95 0 0 1-2.95-2.94 2.95 2.95 0 0 1 2.95-2.93Zm13.02 2.93a2.95 2.95 0 0 0-2.96-2.93 2.95 2.95 0 0 0-2.96 2.93 2.95 2.95 0 0 0 2.96 2.94 2.95 2.95 0 0 0 2.96-2.94Zm7.1-2.93a2.95 2.95 0 0 1 2.95 2.93 2.95 2.95 0 0 1-2.96 2.94 2.95 2.95 0 0 1-2.95-2.94 2.95 2.95 0 0 1 2.95-2.93Z"
                  fill="#fff"
                  fillOpacity="0.9"
                ></path>
              </g>
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M24.6 39s11.47-.89 16.26-7.02c-4.8 6.75-9.59 10.43-13.78 11.66C22.88 44.87 24.6 39 24.6 39Z"
                fill="#000"
                fillOpacity="0.03"
              ></path>
              <defs>
                <filter
                  id="Icon_Color-Comment_Shadow_svg__a"
                  x="1.2"
                  y="2.4"
                  width="46.2"
                  height="44.97"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood
                    floodOpacity="0"
                    result="BackgroundImageFix"
                  ></feFlood>
                  <feColorMatrix
                    in="SourceAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  ></feColorMatrix>
                  <feOffset dy="1.2"></feOffset>
                  <feGaussianBlur stdDeviation="1.2"></feGaussianBlur>
                  <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"></feColorMatrix>
                  <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1_2867"
                  ></feBlend>
                  <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1_2867"
                    result="shape"
                  ></feBlend>
                </filter>
              </defs>
            </svg>
            <svg
              viewBox="0 0 48 48"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
            >
              <g
                filter="url(#Icon_Color-Share_Shadow_Alt_2_svg__b)"
                clipPath="url(#Icon_Color-Share_Shadow_Alt_2_svg__a)"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M25.56 4.07a1.98 1.98 0 0 0-2.15-.42 1.95 1.95 0 0 0-1.21 1.8v8.34c-5.4.35-10.04 2.2-13.43 5.68C4.97 23.35 3 29.03 3 36.19c0 .79.48 1.5 1.22 ******** 1.58.13 2.14-.42 3.34-3.31 7.65-4.56 11.25-4.95 1.8-.2 3.37-.18 4.5-.1h.09v9.03c0 .78.46 1.48 1.18 ********* 1.56.16 2.13-.37l18.87-17.49a1.94 1.94 0 0 0 .04-2.8L25.56 4.07Z"
                  fill="#fff"
                  fillOpacity="0.9"
                  shapeRendering="crispEdges"
                ></path>
              </g>
              <defs>
                <clipPath id="Icon_Color-Share_Shadow_Alt_2_svg__a">
                  <path fill="#fff" d="M0 0h48v48H0z"></path>
                </clipPath>
                <filter
                  id="Icon_Color-Share_Shadow_Alt_2_svg__b"
                  x="-1.5"
                  y="0.5"
                  width="51"
                  height="49"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood
                    floodOpacity="0"
                    result="BackgroundImageFix"
                  ></feFlood>
                  <feColorMatrix
                    in="SourceAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  ></feColorMatrix>
                  <feOffset dy="1.5"></feOffset>
                  <feGaussianBlur stdDeviation="2.25"></feGaussianBlur>
                  <feComposite in2="hardAlpha" operator="out"></feComposite>
                  <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"></feColorMatrix>
                  <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_81245_5665"
                  ></feBlend>
                  <feColorMatrix
                    in="SourceAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  ></feColorMatrix>
                  <feOffset dy="1.5"></feOffset>
                  <feGaussianBlur stdDeviation="0.75"></feGaussianBlur>
                  <feComposite in2="hardAlpha" operator="out"></feComposite>
                  <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"></feColorMatrix>
                  <feBlend
                    in2="effect1_dropShadow_81245_5665"
                    result="effect2_dropShadow_81245_5665"
                  ></feBlend>
                  <feBlend
                    in="SourceGraphic"
                    in2="effect2_dropShadow_81245_5665"
                    result="shape"
                  ></feBlend>
                </filter>
              </defs>
            </svg>
            <img className="h-4 w-4 rounded-full" src={profilePictureUrl} />
          </div>
          {/* Bottom section */}
          <div className="absolute bottom-3 left-3">
            <div className="flex flex-col gap-1">
              <div className="font-semibold">
                {accountName?.replace("@", "")}
              </div>
              <div
                className={classNames(
                  "transition-max-height mr-6 h-full leading-tight duration-300",
                  {
                    "max-h-[12px] overflow-clip": !showMore,
                    "scrollbar-hidden max-h-[200px] overflow-auto": showMore,
                  }
                )}
                onClick={() => {
                  if (showMore) {
                    setShowMore(false);
                  }
                }}
              >
                {cleanedCopy?.split("\n").map((line, index) => {
                  if (line === "") {
                    return <div key={index} className="py-1" />;
                  } else {
                    let parsedLine = line;

                    const usernameRegex = /(@[A-Za-z0-9_\.]{1,24})/g;
                    const usernameTags = uniq(line.match(usernameRegex) || []);

                    usernameTags.forEach((usernameWithAt) => {
                      const username = usernameWithAt.replace("@", "");

                      parsedLine = parsedLine.replaceAll(
                        usernameWithAt,
                        `[${usernameWithAt}](https://www.tiktok.com/@${username})`
                      );
                    });

                    const hashtags = uniq(line.match(/(#[\w\d_]+)/g)) || [];

                    hashtags.forEach((hashtag, index) => {
                      const tag = hashtag.replace("#", "");

                      const tagRegex = new RegExp(`${hashtag}\\b`, "g");

                      parsedLine = parsedLine.replaceAll(
                        tagRegex,
                        `[${hashtag}](https://www.tiktok.com/tag/${tag})`
                      );
                    });

                    return (
                      <div key={index} className="flex items-center" dir="auto">
                        <ReactMarkdown
                          className="mr-0 w-fit break-words"
                          linkTarget="_blank"
                          children={parsedLine}
                          components={{
                            h1: ({ node, ...props }) => {
                              return (
                                <span>
                                  # <span {...props} />
                                </span>
                              );
                            },
                            h2: ({ node, ...props }) => {
                              return (
                                <span>
                                  ## <span {...props} />
                                </span>
                              );
                            },
                            h3: ({ node, ...props }) => {
                              return (
                                <span>
                                  ### <span {...props} />
                                </span>
                              );
                            },
                            strong: "span",
                            em: "span",
                            ol: ({ node, ...props }) => {
                              return (
                                <ol
                                  {...props}
                                  className="list-inside list-decimal"
                                />
                              );
                            },
                            ul: ({ node, ...props }) => {
                              // @ts-expect-error children is not defined on the node
                              const children = props.children[1].props.children;
                              return (
                                <ol {...props} className="list-inside">
                                  <li>- {children}</li>
                                </ol>
                              );
                            },
                            a: ({ node, ...props }) => {
                              return (
                                <a
                                  onClick={(e) => {
                                    e.stopPropagation();
                                  }}
                                  className="font-semibold text-white"
                                  {...props}
                                />
                              );
                            },
                          }}
                        />
                        {!showMore && index === 0 && (
                          <button
                            className="text-light-gray whitespace-nowrap"
                            onClick={() => setShowMore(true)}
                          >
                            ...
                          </button>
                        )}
                      </div>
                    );
                  }
                })}
              </div>
              <div className="flex items-center gap-1">
                <img
                  className="h-2 w-2"
                  src="https://lf16-tiktok-web.tiktokcdn-us.com/obj/tiktok-web-tx/tiktok/webapp/main/webapp-mobile/1f379b351b5c1d53e1a9.png"
                />
                <div>original sound - {accountName?.replace("@", "")}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default TikTokPostPreview;
