import { EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import { useViewportSize } from "@mantine/hooks";
import DropdownMenu from "~/components/ui/DropdownMenu";
import { AssetType } from "~/types/Asset";
import AssetButton from "./AssetButton";

export const AssetControls = ({
  asset,
  isReadOnly,
  onEditClick,
  onDeleteClick,
  isSmall = false,
}: {
  asset: AssetType;
  isReadOnly: boolean;
  onEditClick?: (asset: AssetType) => void;
  onDeleteClick: (asset: AssetType) => void;
  isSmall?: boolean;
}) => {
  const { width } = useViewportSize();
  const isExtraSmall = width <= 375;

  const onDownloadClick = (asset: AssetType) => {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", asset.signedUrl, true);
    xhr.responseType = "blob";
    xhr.onload = function () {
      const urlCreator = window.URL || window.webkitURL;
      const assetUrl = urlCreator.createObjectURL(this.response);
      const tag = document.createElement("a");
      tag.href = assetUrl;
      tag.download = asset.name;
      document.body.appendChild(tag);
      tag.click();
      document.body.removeChild(tag);
    };
    xhr.send();
  };

  const controls =
    isExtraSmall || isSmall ? (
      <DropdownMenu.Root>
        <DropdownMenu.Trigger>
          <div className="font-body-sm max-h-7 rounded-[2.5px] bg-white/60 p-1.5 text-black outline-hidden backdrop-blur-xs transition-colors hover:bg-white">
            <EllipsisHorizontalIcon className="h-4 w-4" />
          </div>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content position="bottom-left">
          <DropdownMenu.Group>
            {asset.metadata?.mimetype.startsWith("image/") &&
              asset.metadata?.mimetype !== "image/gif" &&
              !isReadOnly &&
              onEditClick && (
                <DropdownMenu.Item
                  icon="PaintBrushIcon"
                  onClick={() => onEditClick(asset)}
                >
                  Edit
                </DropdownMenu.Item>
              )}
            <DropdownMenu.Item
              icon="ArrowDownTrayIcon"
              onClick={() => onDownloadClick(asset)}
            >
              Download
            </DropdownMenu.Item>
            <DropdownMenu.Separator />
            <DropdownMenu.Item
              icon="TrashIcon"
              intent="danger"
              onClick={() => onDeleteClick(asset)}
            >
              Delete
            </DropdownMenu.Item>
          </DropdownMenu.Group>
        </DropdownMenu.Content>
      </DropdownMenu.Root>
    ) : (
      <>
        {asset.metadata?.mimetype.startsWith("image/") &&
          asset.metadata?.mimetype !== "image/gif" &&
          !isReadOnly &&
          onEditClick && (
            <AssetButton
              icon="PaintBrushIcon"
              onClick={() => onEditClick(asset)}
            />
          )}
        <AssetButton
          icon="ArrowDownTrayIcon"
          onClick={() => onDownloadClick(asset)}
        />
        <AssetButton
          icon="TrashIcon"
          onClick={(e) => {
            e.stopPropagation();
            onDeleteClick(asset);
          }}
        />
      </>
    );

  return (
    <div className="z-10 opacity-0 transition-opacity group-hover/asset:opacity-100">
      <div className="absolute top-3 right-2 flex gap-1.5 md:right-3.5">
        {controls}
      </div>
    </div>
  );
};

export const AssetsUploadReorderControls = ({
  onUploadClick,
  onReorderClick,
  isReadOnly,
}: {
  onUploadClick: () => void;
  onReorderClick: () => void;
  isReadOnly: boolean;
}) => {
  if (isReadOnly) {
    return null;
  }

  return (
    <div className="absolute top-3 left-3 z-1 flex gap-1.5 opacity-0 transition-opacity group-hover/asset-controls:opacity-100">
      <AssetButton icon="PhotoIcon" label="Upload" onClick={onUploadClick} />
      <AssetButton
        icon="ArrowPathIcon"
        onClick={onReorderClick}
        label="Reorder"
      />
    </div>
  );
};
