import classNames from "classnames";
import TweetPreview from "./TweetPreview";

type Props = {
  tweets: {
    id: string;
    copy: string;
    assets:
      | {
          signedUrl: string;
          metadata: {
            mimetype: string;
          };
        }[]
      | null;
  }[];
  connectedAccount?: {
    profileImageUrl: string | null;
    name: string;
    verified: boolean;
    verifiedType: string;
    isBasicVerifiedOverride: boolean;
    username: string;
  } | null;
  maxHeight?: number | "full";
};

const TweetThreadPreview = ({
  tweets,
  connectedAccount,
  maxHeight = 600,
}: Props) => {
  const hasNonEmptyTweet =
    tweets.filter((tweet) => tweet.copy != "" || tweet.assets?.length).length >
    0;

  return (
    <div>
      {!hasNonEmptyTweet ? (
        <div className="font-body text-medium-dark-gray mt-1">
          Preview will show when post content is added below
        </div>
      ) : (
        <div className="flex min-h-[300px] items-center justify-center">
          <div
            className={classNames(
              "scrollbar-hidden font-chirp h-fit w-[600px] space-y-4 rounded-lg bg-white p-3 md:p-6",
              {
                [`max-h-[${maxHeight}px] overflow-auto`]:
                  typeof maxHeight === "number",
                "max-h-full": maxHeight === "full",
              }
            )}
          >
            {tweets.map((tweet, index) => {
              return (
                <TweetPreview
                  key={`${tweet.id}-${index}`}
                  connectedAccount={connectedAccount}
                  tweet={tweet}
                  index={index}
                  numTweets={tweets.length}
                />
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default TweetThreadPreview;
