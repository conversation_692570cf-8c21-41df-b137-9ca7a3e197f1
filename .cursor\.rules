# Cursor Rules for Assembly - NextJS, React, TypeScript, tRPC, ShadCN/Radix

## Project Structure & Architecture

### NextJS Pages Router

- Use the Pages Router pattern with `pages/` directory structure
- Place global app configuration in `pages/_app.tsx` and document setup in `pages/_document.tsx`
- Use `pages/api/` for API routes with proper error handling
- Implement error boundaries with `pages/_error.jsx` for Sentry integration
- Follow the pattern: `pages/[feature]/[sub-feature].tsx` or `pages/[feature]/[id].tsx`
- Use dynamic routes with `[param]` syntax for dynamic pages
- Place authentication pages in `pages/` root (login, sign-up, etc.)
- Organize authenticated pages under `pages/authed/` directory

### File Organization

- Keep components in `components/` with subdirectories: `app/`, `ui/`, `form/`
- `components/ui` is the gold star of components and we should import from there first
- Place API utilities in `apiUtils/` organized by feature (e.g., `apiUtils/facebook/`, `apiUtils/twitter/`)
- Store tRPC routers in `server/routers/` with index files for aggregation
- Use `utils/` for shared utility functions
- Keep types in `types/` directory
- Place constants in `constants/` directory
- Store API routes in `pages/api/` following RESTful patterns

## TypeScript Patterns

### Type Safety

- Always use strict TypeScript with `strict: true`
- Define interfaces for all component props
- Use Zod schemas for tRPC input validation
- Leverage Prisma-generated types for database entities
- Use discriminated unions for complex state management

### Component Patterns

```typescript
// Use forwardRef for components that need ref forwarding
const Component = React.forwardRef<HTMLDivElement, ComponentProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div ref={ref} className={cn("base-classes", className)} {...props}>
        {children}
      </div>
    );
  }
);
Component.displayName = "Component";
```

## tRPC Best Practices

### Router Structure

- Organize routers by feature in `server/routers/`
- Use `protectedProcedure` for authenticated routes
- Use `adminProcedure` for admin-only routes
- Always validate inputs with Zod schemas
- Handle errors with `TRPCError` and proper error codes

### Query/Mutation Patterns

```typescript
// Use proper error handling and optimistic updates
const mutation = trpc.post.create.useMutation({
  onError: (error) => {
    handleTrpcError({ title: "Error Creating Post", error });
  },
  onSuccess: (data) => {
    trpcUtils.post.getMany.invalidate();
    router.push(`/posts/${data.post.id}`);
  },
});
```

### Context and Middleware

- Use `getUserWorkspaceID()` utility for workspace-scoped operations
- Implement proper authorization checks in procedures
- Use `skipBatch: true` for critical operations that shouldn't be batched

## ShadCN/Radix UI Patterns

### Component Structure

- Always use Radix primitives as the foundation
- Implement consistent styling with `cn()` utility
- Use `class-variance-authority` for component variants
- Follow the compound component pattern (e.g., `Card.Root`, `Card.Header`)

### Styling Guidelines

```typescript
// Use CSS variables for theming (Tailwind v4)
const buttonVariants = cva(
  "relative focus-visible:ring-basic w-fit font-body-sm inline-flex gap-1 items-center justify-center rounded-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-70",
  {
    variants: {
      variant: {
        default: "bg-basic text-basic-light hover:bg-basic/80",
        secondary: "bg-slate text-basic hover:bg-light-gray",
        danger: "bg-danger text-white hover:bg-danger/90",
        ghost: "bg-transparent text-basic hover:bg-basic/10",
        link: "underline-offset-4 hover:underline text-basic",
      },
      size: {
        default: "h-10 px-6 py-2 font-eyebrow-lg",
        sm: "h-8 px-2 py-1",
        lg: "h-12 px-8 py-2",
        icon: "h-10 w-10 p-0",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);
```

### Accessibility

- Always include proper ARIA attributes
- Use semantic HTML elements
- Implement keyboard navigation
- Add proper focus management
- Include screen reader support

## State Management

### React Patterns

- Use `useState` for local component state
- Use `useEffect` for side effects with proper cleanup but try to avoid using
- Implement custom hooks for reusable logic
- Use tRPC's React Query integration for server state management
- Leverage Context API for global state (User, Workspace, etc.)

### Provider Pattern

```typescript
// Follow the established provider pattern in _app.tsx
export const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);

  return (
    <UserContext.Provider value={{ user, setUser }}>
      {children}
    </UserContext.Provider>
  );
};
```

## API Integration

### Social Media Integrations

- Follow the pattern: `apiUtils/[platform]/[action].ts`
- Use consistent error handling with `catch[Platform]Error` utilities
- Implement proper OAuth flows with callback handling
- Store integration data in nested objects (e.g., `integration.page.externalID`)

### File Upload & Assets

- Use Uppy for file uploads with proper progress handling
- Implement asset optimization and compression
- Store assets with proper metadata and relationships
- Use signed URLs for secure file access

## Performance & Optimization

### NextJS Optimizations

- Use `next/dynamic` for code splitting
- Implement proper image optimization with `next/image`
- Use `React.memo` for expensive components
- Leverage `useMemo` and `useCallback` appropriately
- Use `getStaticProps` and `getServerSideProps` appropriately for data fetching

### Database Patterns

- Use Prisma with proper indexing
- Implement efficient queries with `select` statements
- Use database transactions for complex operations
- Leverage Prisma Accelerate for read replicas

## Error Handling

### Frontend Error Handling

- Use `handleTrpcError` utility for consistent error display
- Implement proper loading states with skeleton components
- Use toast notifications for user feedback
- Implement retry mechanisms for failed operations
- Use `pages/_error.jsx` for global error handling

### Backend Error Handling

- Use `TRPCError` with appropriate error codes
- Implement proper validation with Zod
- Log errors with Sentry integration
- Return meaningful error messages

## Testing & Quality

### Code Quality

- Use ESLint with TypeScript rules
- Implement proper TypeScript strict mode
- Use Prettier for consistent formatting
- Add JSDoc comments for complex functions

### Testing Patterns

- Write unit tests for utility functions
- Test tRPC procedures with proper mocking
- Test React components with React Testing Library
- Use proper test data with Faker.js

## Security

### Authentication & Authorization

- Use Supabase for authentication
- Implement proper session management
- Use workspace-based access control
- Validate user permissions at the procedure level

### Data Protection

- Use Prisma Field Encryption for sensitive data
- Implement proper input sanitization
- Use HTTPS for all API calls
- Validate file uploads and content types

## Deployment & Monitoring

### Build Process

- Use Bun for faster builds
- Implement proper environment variable management
- Use Sentry for error monitoring and performance tracking
- Generate source maps for debugging

### Monitoring

- Use Sentry for error tracking
- Implement proper logging
- Monitor performance with Sentry Performance
- Track user analytics with PostHog

## Common Patterns

### Form Handling

- Use React Hook Form for form state management
- Implement proper validation with Zod
- Use controlled components for complex forms
- Handle form submission with proper loading states

### Modal & Dialog Patterns

- Use Radix Dialog for modals
- Implement proper focus management
- Use portal rendering for overlays
- Handle keyboard navigation (Escape to close)

### Table Patterns

- Use TanStack Table for complex data tables
- Implement proper sorting and filtering
- Use virtual scrolling for large datasets
- Implement row selection and bulk actions

### Navigation

- Use Next.js router for client-side navigation
- Implement proper breadcrumbs
- Use active state management for navigation
- Handle deep linking properly
- Use `router.push()` and `router.replace()` appropriately

## Code Style

### Naming Conventions

- Use PascalCase for components
- Use camelCase for functions and variables
- Use kebab-case for file names
- Use UPPER_SNAKE_CASE for constants

### Import Organization

```typescript
// External libraries
import React from "react";
import { useRouter } from "next/router";

// Internal utilities
import { cn } from "~/utils";
import { trpc } from "~/utils";

// Components
import { Button, Alert } from "~/components";
import { PostHeader } from "~/components/app/posts";

// Types
import type { Post } from "~/types";
```

### Comments & Documentation

- Add JSDoc comments for complex functions
- Document component props with TypeScript interfaces
- Add inline comments for complex business logic
- Document API endpoints with proper descriptions

## Pages Router Specific Patterns

### Page Structure

```typescript
// Use proper page structure with getServerSideProps when needed
import { GetServerSideProps } from "next";
import type { NextPage } from "next";

interface PageProps {
  data: SomeData;
}

const Page: NextPage<PageProps> = ({ data }) => {
  return <div>{/* page content */}</div>;
};

export const getServerSideProps: GetServerSideProps<PageProps> = async (context) => {
  // Server-side data fetching
  return {
    props: {
      data: someData,
    },
  };
};

export default Page;
```

### API Routes

```typescript
// Use proper API route structure
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    // Handle request
    res.status(200).json({ success: true });
  } catch (error) {
    res.status(500).json({ error: "Internal server error" });
  }
}
```

### Layout Management

- Use the Layout component in `pages/_app.tsx` for global layout
- Implement page-specific layouts as components
- Use proper provider wrapping in `_app.tsx`
- Handle authentication redirects in `_app.tsx` or individual pages
