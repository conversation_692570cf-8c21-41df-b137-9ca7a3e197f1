import { InstagramContentType } from "@prisma/client";
import assert from "assert";
import { AxiosInstance } from "axios";
import { DateTime } from "luxon";
import { createSuccessResponse, Response } from "~/clients/Response";
import { removeEmptyElems } from "~/utils";
import catchFacebookError from "../../catchFacebookError";
import { IGPost, RawIGPost } from "../InstagramTypes";

class PostsService {
  private readonly client: AxiosInstance;
  private readonly platform: "Facebook" | "Instagram";

  constructor(
    client: AxiosInstance,
    { platform }: { platform: "Facebook" | "Instagram" }
  ) {
    this.client = client;
    this.platform = platform;
  }

  private convertRawPost = (rawPost: RawIGPost): IGPost => {
    const mediaType = rawPost.media_type;
    const mediaProductType = rawPost.media_product_type;

    const impressionCount =
      rawPost.insights.data.find((insight) => insight.name === "views")
        ?.values[0].value || 0;
    const likeCount = rawPost.like_count;
    const reachCount =
      rawPost.insights.data.find((insight) => insight.name === "reach")
        ?.values[0].value || 0;
    const savedCount =
      rawPost.insights.data.find((insight) => insight.name === "saved")
        ?.values[0].value || 0;
    const shareCount =
      rawPost.insights.data.find((insight) => insight.name === "shares")
        ?.values[0].value || 0;
    const followCount =
      rawPost.insights.data.find((insight) => insight.name === "follows")
        ?.values[0].value || 0;
    const profileVisitCount =
      rawPost.insights.data.find((insight) => insight.name === "profile_visits")
        ?.values[0].value || 0;
    const commentCount = rawPost.comments_count;

    const caption = rawPost.caption || null;
    const hashtags = caption?.match(/#[a-z0-9_]+/gi) || [];
    const hashtagCount = hashtags.length;

    const captionMentions = caption?.match(/@[a-z0-9_]+/gi) || [];
    const captionMentionCount = captionMentions.length;

    const reelSharedToFeed =
      mediaProductType === "REELS" ? rawPost.is_shared_to_feed || false : null;

    const carouselImageUrls = removeEmptyElems(
      (rawPost.children?.data || []).map((child) =>
        child.media_type === "IMAGE" ? child.media_url : null
      )
    );
    const carouselVideoUrls = removeEmptyElems(
      (rawPost.children?.data || []).map((child) =>
        child.media_type === "VIDEO" ? child.media_url : null
      )
    );
    const imageUrls =
      rawPost.media_type === "IMAGE" ? [rawPost.media_url] : carouselImageUrls;
    const videoUrls =
      rawPost.media_type === "VIDEO"
        ? removeEmptyElems([rawPost.media_url])
        : carouselVideoUrls;
    console.log(rawPost);
    const firstChildMediaUrl = rawPost.children?.data[0]?.media_url;
    const thumbnailUrl =
      rawPost.thumbnail_url || rawPost.media_url || firstChildMediaUrl;
    assert(thumbnailUrl, "Thumbnail URL is required");

    const permalink = rawPost.permalink;

    return {
      id: rawPost.id,

      shortcode: rawPost.shortcode,
      permalink,

      username: rawPost.username,

      mediaType,
      mediaProductType,

      caption,
      hashtags,
      hashtagCount,
      captionMentions,
      captionMentionCount,

      reelSharedToFeed,

      imageUrls,
      videoUrls,

      thumbnailUrl,

      impressionCount,
      reachCount,
      savedCount,
      likeCount,
      commentCount,
      profileVisitCount,
      shareCount,
      followCount,

      publishedAt: new Date(rawPost.timestamp),

      rawResponse: rawPost,
    };
  };

  // https://developers.facebook.com/docs/instagram-platform/instagram-graph-api/reference/ig-media/insights#new-metrics
  get = async ({
    mediaID,
    type,
  }: {
    mediaID: string;
    type: InstagramContentType;
  }): Promise<Response<IGPost>> => {
    let fields = [
      "id",
      "shortcode",
      "permalink",
      "caption",
      "timestamp",
      "media_url",
      "thumbnail_url",
      "media_type",
      "media_product_type",
      "is_shared_to_feed",
      `children{${["id", "media_url", "media_type"].join(",")}}`,
      "like_count",
      "comments_count",
      "username",
    ];

    if (type === "Feed") {
      fields.push(
        `insights.metric(${[
          "comments",
          "follows",
          "likes",
          "profile_activity",
          "profile_visits",
          "shares",
          "total_interactions",
        ].join(",")})`
      );
    } else if (type === "Reel") {
      fields.push(
        `insights.metric(${[
          "comments",
          "ig_reels_avg_watch_time",
          "ig_reels_video_view_total_time",
          "likes",
          "views",
          "reach",
          "saved",
          "shares",
          "total_interactions",
        ].join(",")})`
      );
    } else if (type === "Story") {
      fields.push(
        `insights.metric(${[
          "follows",
          "profile_visits",
          "shares",
          "total_interactions",
        ].join(",")})`
      );
    }
    const params = new URLSearchParams({
      fields: fields.join(","),
    });

    try {
      const { status, data } = await this.client.get(`/${mediaID}?${params}`);

      const rawData: RawIGPost = data;

      const post = this.convertRawPost(rawData);

      return createSuccessResponse(post, status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };

  // https://developers.facebook.com/docs/instagram-api/reference/ig-media#fields
  getForAccount = async (
    instagramAccountID: string,
    filters: {
      since?: number;
    }
  ): Promise<Response<IGPost[]>> => {
    const params = new URLSearchParams({
      fields: [
        "id",
        "shortcode",
        "permalink",
        "caption",
        "timestamp",
        "media_url",
        "thumbnail_url",
        "media_type",
        "media_product_type",
        "is_shared_to_feed",
        `children{${["id", "media_url", "media_type"].join(",")}}`,
        "like_count",
        "comments_count",
        `insights.metric(${[
          "likes",
          "reach",
          "saved",
          "shares",
          "views",
          "total_interactions",
          "follows",
          "profile_visits",
          "comments",
        ].join(",")})`,
        "username",
      ].join(","),
      limit: "100",
      since: filters.since
        ? filters.since.toString()
        : DateTime.now().minus({ years: 2 }).toUnixInteger().toString(),
    });

    try {
      const response = await this.client.get(
        `/${instagramAccountID}/media?${params}`
      );

      const mediaResponse: RawIGPost[] = response.data.data;

      const posts = mediaResponse.map((rawPost) => {
        return this.convertRawPost(rawPost);
      });

      return createSuccessResponse(posts, response.status);
    } catch (error) {
      return catchFacebookError(error);
    }
  };
}

export default PostsService;
