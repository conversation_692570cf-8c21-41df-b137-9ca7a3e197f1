import { useEffect, useRef, useState } from "react";
import ContentLoader from "react-content-loader";
import colors from "~/styles/colors";

const CalendarLoader = () => {
  const [dimentions, setDimentions] = useState({ width: 1000, height: 950 });
  const calendarRef = useRef<HTMLDivElement>(null);
  // Hardcoded values
  const rows = 2;
  const columns = 7;
  const padding = 10;

  const covers = Array(columns * rows).fill(1);

  useEffect(() => {
    if (calendarRef.current) {
      setDimentions({
        width: calendarRef.current.clientWidth,
        height: calendarRef.current.clientHeight,
      });
    }
  }, [calendarRef.current]);

  return (
    <div ref={calendarRef} className="h-full w-full px-4">
      <ContentLoader
        width="100%"
        height="100%"
        backgroundColor={colors.lightestGray}
        foregroundColor={colors.lightGray}
      >
        <rect x="0" y="10" rx="3" ry="3" width="100%" height="30" />

        {covers.map((g, i) => {
          let vx =
            ((i % columns) / columns) * (dimentions.width - padding * columns);
          return (
            <rect
              key={`${dimentions.height}-${i}`}
              x={vx + padding * (i % columns)}
              y={i < columns ? 80 : dimentions.height / 2 + padding * 4}
              rx="3"
              ry="3"
              width={dimentions.width / columns - padding}
              height={0.45 * dimentions.height}
            />
          );
        })}
      </ContentLoader>
    </div>
  );
};

export default CalendarLoader;
