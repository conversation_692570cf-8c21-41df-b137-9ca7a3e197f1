import { Asset } from "@prisma/client";
import getAssetsForFacebook from "apiUtils/getAssetsForFacebook";
import assert from "assert";
import { DateTime } from "luxon";
import Graph from "~/clients/facebook/Graph";
import { db } from "~/clients/Prisma";
import { GENERIC_ERROR_MESSAGE } from "~/constants";

type Post = {
  id: string;
  facebookIntegration: {
    page: {
      externalID: string;
    };
    accessToken: string;
  } | null;
  facebookContent: {
    id: string;
    copy: string;
    coverPhoto: Asset | null;
    assets: {
      id: string;
      position: number;
      asset: Asset;
      facebookMediaID: string | null;
      facebookMediaExpiresAt: Date | null;
    }[];
  } | null;
  title: string;
  workspaceID: string;
};

const uploadFacebookAssets = async (
  post: Post
): Promise<
  | {
      status: "Success";
      assetIDToMediaID: { [assetID: string]: string };
    }
  | {
      status: "NonRetriableError";
      error: string;
      rawError?: {} | null;
      isAuthError?: boolean;
    }
  | {
      status: "Error";
      error: string;
      rawError?: {} | null;
      isAuthError?: boolean;
    }
  | {
      status: "Skipped";
    }
> => {
  const { facebookContent, facebookIntegration } = post;

  if (!facebookContent) {
    console.error(`FB Upload Error post:${post.id}: No Copy to Post`);
    return {
      status: "NonRetriableError",
      error: "No Facebook content to post",
    };
  }

  if (!facebookIntegration) {
    console.error(`FB Upload Error post:${post.id}: No integration`);
    return {
      status: "NonRetriableError",
      error: "No Facebook integration",
    };
  }

  const fbAssets = facebookContent.assets;
  const firstAsset = fbAssets.find((asset) => asset.position === 0);
  if (!firstAsset) {
    console.error(`FB Skipped post:${post.id}: No assets to upload`);
    return {
      status: "Skipped",
    };
  }
  const postType = firstAsset.asset.mimetype.includes("video")
    ? "video"
    : "photo";

  if (postType === "video") {
    console.log(`FB Upload post:${post.id}: Video will be uploaded in band`);
    return {
      status: "Skipped",
    };
  } else {
    if (
      fbAssets.every(
        (asset) =>
          asset.facebookMediaID &&
          asset.facebookMediaExpiresAt &&
          DateTime.fromJSDate(asset.facebookMediaExpiresAt)
            .diffNow()
            .as("minutes") > 10
      )
    ) {
      console.log(`FB Upload post:${post.id}: All photos already uploaded`);
      return {
        status: "Skipped",
      };
    }
  }

  const graph = new Graph(facebookIntegration.accessToken);
  const facebookPageID = facebookIntegration.page.externalID;

  const { assets } = await getAssetsForFacebook(post);

  const uploadResponse = await graph.FB.uploadPhotos({
    accountID: facebookPageID,
    urls: assets
      .filter((asset) => asset.metadata.mimetype.includes("image"))
      .map((asset) => asset.signedUrl),
  });

  if (uploadResponse.errors) {
    const error = uploadResponse.errors.join(". ");

    try {
      console.error(`Error Uploading post:${post.id}: ${error}`);
      const isAuthError = uploadResponse.isAuthError;
      return {
        status: isAuthError ? "NonRetriableError" : "Error",
        error,
        rawError: uploadResponse.rawError,
        isAuthError,
      };
    } catch (err) {
      console.error(`Error Uploading post:${post.id}: ${err}`);
    } finally {
      return {
        status: "Error",
        error: GENERIC_ERROR_MESSAGE,
      };
    }
  }

  assert(uploadResponse.data);
  const assetUrlToMediaID = uploadResponse.data;

  await db.$transaction(async (tx) => {
    for await (const asset of assets) {
      const facebookMediaID = assetUrlToMediaID[asset.signedUrl];

      await tx.facebookAsset.update({
        where: {
          id: asset.id,
        },
        data: {
          facebookMediaID,
          facebookMediaUploadedAt: new Date(),
          facebookMediaExpiresAt: DateTime.now().plus({ days: 1 }).toJSDate(),
        },
      });
    }
  });

  const assetIDToMediaID = Object.keys(assetUrlToMediaID).reduce(
    (acc, url) => ({
      ...acc,
      [assets.find((asset) => asset.signedUrl === url)!.id]:
        assetUrlToMediaID[url],
    }),
    {}
  );

  console.log(`Uploaded FB Images Successfully post:${post.id}`);

  return {
    status: "Success",
    assetIDToMediaID,
  };
};

export default uploadFacebookAssets;
