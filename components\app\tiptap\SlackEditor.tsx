import {
  useIsEditorReady,
  useLiveblocksExtension,
} from "@liveblocks/react-tiptap";
import { ClientSideSuspense, RoomProvider } from "@liveblocks/react/suspense";

import Bold from "@tiptap/extension-bold";
import BulletList from "@tiptap/extension-bullet-list";
import Code from "@tiptap/extension-code";
import Document from "@tiptap/extension-document";
import { History } from "@tiptap/extension-history";
import Italic from "@tiptap/extension-italic";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, JSONContent, useEditor } from "@tiptap/react";
import { ClipboardEvent, useState } from "react";
import { useAutosave } from "react-autosave";
import TextDirection from "tiptap-text-direction";
import Turndown from "turndown";
import { Divider } from "~/components";
import { CHANNEL_ICON_MAP } from "~/types";
import { stripLiveblocksMarks } from "~/utils";
import { Threads } from "./Threads";
import {
  AutoMention,
  CharacterCount,
  CustomCommentThreadMark,
  Emoji,
  Link,
  PasteHandler,
} from "./extensions";
import CustomFloatingToolbar from "./ui/CustomFloatingToolbar";
import FloatingComposer from "./ui/FloatingComposer";
import FloatingToolbar from "./ui/FloatingToolbar";
import TextFormatButtons from "./ui/TextFormatButtons";
import TextStyleButtons from "./ui/TextStyleButtons";
import ToolbarActions from "./ui/ToolbarActions";

type BaseProps = {
  title: string;
  initialValue: JSONContent | null;
  placeholder: string;
  onSave: (editorState: JSONContent) => void;
  onUpdateContent: (textContent: string) => void;
  onImagePaste?: (event: ClipboardEvent<HTMLDivElement>) => void;
  isReadOnly?: boolean;
  isEditable?: boolean;
  roomID: string | null;
};

type SlackEditorProps = Omit<BaseProps, "roomID">;

const createBaseExtensions = (placeholder: string) => [
  Document,
  Paragraph,
  TextDirection.configure({
    types: ["heading", "paragraph"],
  }),
  Text,
  History,
  BulletList,
  OrderedList,
  ListItem,
  Bold,
  Italic,
  Code,
  Link.configure({
    openOnClick: true,
  }),
  Emoji,
  Placeholder.configure({
    placeholder,
    showOnlyWhenEditable: false,
  }),
  CharacterCount.configure({
    limit: 2000,
  }),
  // User, channel, and here mentions
  AutoMention.configure({
    baseUrl: null,
    regex: /(?:<@[A-Z0-9]+>|<#[A-Z0-9]+>|(?<=^|\s)@(?:here|everyone|channel))/g,
  }),
  PasteHandler,
];

const createTurndownInstance = () => {
  /** Markdown Rules - https://api.slack.com/reference/surfaces/formatting#visual-styles
   * 1. Bold: *bold*
   * 2. Italic: _italic_
   * 3. Strikethrough: ~strikethrough~
   * 4. Code: `code`
   * 5. Link: <https://www.example.com|link text>
   * 6. Blockquote: > blockquote
   * 7. List: - list item (not really supported)
   */

  const turndown = new Turndown({
    emDelimiter: "_",
    // @ts-expect-error - I do actually want a single star
    strongDelimiter: "*",
    bulletListMarker: "-",
    blankReplacement: (content, node) => {
      if (node.nodeName === "P") {
        return "[NEWLINE]\n";
      } else {
        return "[LISTITEM]";
      }
    },
  });

  turndown.addRule("paragraph", {
    filter: "p",
    replacement: (content) => {
      return content + "\n";
    },
  });

  turndown.addRule("list", {
    filter: ["ul", "ol"],
    replacement: function (content, node) {
      return content + "\n"; // Remove extra newlines from the front
    },
  });

  turndown.addRule("listItem", {
    filter: "li",
    replacement: function (content, node, options) {
      content = content
        .replace(/^\n+/, "") // Remove leading newlines
        .replace(/\n+$/, ""); // Remove trailing newlines

      const prefix = options.bulletListMarker + " ";
      const parent = node.parentNode;
      if (!parent) {
        return content;
      }
      const index = Array.prototype.indexOf.call(parent.children, node);
      const prefix_actual =
        parent.nodeName === "OL" ? `${index + 1}. ` : prefix;

      return prefix_actual + content + (node.nextSibling ? "\n" : "");
    },
  });

  return turndown;
};

const processMarkdown = (html: string, turndown: Turndown) => {
  return turndown
    .turndown(html)
    .replaceAll("[NEWLINE]\n", "\n")
    .replaceAll("[LISTITEM]", "")
    .replaceAll("@here", "<!here>")
    .replaceAll("@everyone", "<!everyone>")
    .replaceAll("@channel", "<!channel>")
    .replace(/(?<=^|\n)\s*- /gm, (match) => match.replace("- ", "• "))
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, "<$2|$1>"); // Convert markdown links to Slack format
};

// Loads the content in the editor and displays it as a fallback when comment threads are loading
const SlackEditorFallback = ({
  title,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  isEditable = false,
  isReadOnly = false,
}: SlackEditorProps) => {
  const cleanContent = stripLiveblocksMarks(initialValue);

  const [editorState, setEditorState] = useState<JSONContent | null>(
    cleanContent
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(cleanContent) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const turndown = createTurndownInstance();

  const editor = useEditor({
    extensions: createBaseExtensions(placeholder),
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const markdown = processMarkdown(html, turndown);

      onUpdateContent(markdown);

      const json = editor.getJSON();
      setEditorState(json);
    },
    immediatelyRender: false,
    content: cleanContent,
    editable: isEditable && !isReadOnly,
  });

  if (!editor) return null;

  return isEditable && !isReadOnly ? (
    <div className="py-2">
      <FloatingToolbar editor={editor}>
        {({ onHide }) => (
          <>
            <TextFormatButtons
              editor={editor}
              onHide={onHide}
              features={{ headings: [] }}
            />
            <Divider orientation="vertical" />
            <TextStyleButtons editor={editor} features={{ underline: false }} />
          </>
        )}
      </FloatingToolbar>
      <EditorContent editor={editor} />
    </div>
  ) : (
    <EditorContent editor={editor} />
  );
};

const SlackEditor = ({
  title,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
}: SlackEditorProps) => {
  const [editorState, setEditorState] = useState<JSONContent | null>(
    initialValue
  );

  useAutosave({
    data: editorState,
    onSave: (editorState) => {
      if (
        JSON.stringify(editorState) !== JSON.stringify(initialValue) &&
        editorState != null
      ) {
        onSave(editorState);
      }
    },
    interval: 500,
  });

  const liveblocksExtension = useLiveblocksExtension({
    initialContent: initialValue,
    mentions: false,
  });

  const turndown = createTurndownInstance();

  const editor = useEditor({
    extensions: [
      liveblocksExtension,
      CustomCommentThreadMark,
      ...createBaseExtensions(placeholder),
    ],
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const markdown = processMarkdown(html, turndown);

      onUpdateContent(markdown);

      const json = editor.getJSON();
      setEditorState(json);
    },
    immediatelyRender: false,
    editable: !isReadOnly,
  });

  const isEditorReady = useIsEditorReady();

  if (!editor) return null;

  return (
    <div
      className="py-2"
      onPaste={(event) => {
        if (onImagePaste) {
          onImagePaste(event);
        }
      }}
    >
      <CustomFloatingToolbar editor={editor}>
        <ToolbarActions
          editor={editor}
          features={{
            bulletList: true,
            orderedList: true,
            link: true,
          }}
        />
      </CustomFloatingToolbar>
      {title && <div className="font-eyebrow text-dark-gray">{title}</div>}

      {isEditorReady ? (
        <>
          <EditorContent editor={editor} />
          <FloatingComposer
            editor={editor}
            metadata={{
              channel: CHANNEL_ICON_MAP["Slack"].label,
            }}
          />
          <ClientSideSuspense fallback={null}>
            <Threads editor={editor} />
          </ClientSideSuspense>
        </>
      ) : (
        <SlackEditorFallback
          title={title}
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
        />
      )}
    </div>
  );
};

const SlackEditorWrapper = ({
  title,
  initialValue,
  placeholder,
  onSave,
  onUpdateContent,
  onImagePaste,
  isReadOnly,
  roomID,
}: BaseProps) => {
  if (!roomID) {
    return (
      <div className="py-2">
        {title && <div className="font-eyebrow text-dark-gray">{title}</div>}
        <SlackEditorFallback
          title={title}
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
          isReadOnly={isReadOnly}
          isEditable={!isReadOnly}
        />
      </div>
    );
  }

  return (
    <RoomProvider id={roomID} key={roomID} initialPresence={{ cursor: null }}>
      <ClientSideSuspense
        fallback={
          <div className="py-2">
            {title && (
              <div className="font-eyebrow text-dark-gray">{title}</div>
            )}
            <SlackEditorFallback
              title={title}
              initialValue={initialValue}
              placeholder={placeholder}
              onSave={onSave}
              onUpdateContent={onUpdateContent}
            />
          </div>
        }
      >
        <SlackEditor
          title={title}
          initialValue={initialValue}
          placeholder={placeholder}
          onSave={onSave}
          onUpdateContent={onUpdateContent}
          onImagePaste={onImagePaste}
          isReadOnly={isReadOnly}
        />
      </ClientSideSuspense>
    </RoomProvider>
  );
};

export default SlackEditorWrapper;
