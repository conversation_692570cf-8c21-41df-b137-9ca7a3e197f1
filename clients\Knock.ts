import { Knock } from "@knocklabs/node";
import { Channel } from "@prisma/client";
import capitalize from "lodash/capitalize";
import { DateTime } from "luxon";
import { CHANNEL_ICON_MAP } from "~/types";

type ContentType = "post" | "campaign";

type ContentData =
  | {
      post: { id: string; title: string };
      campaign?: never;
      message: string;
      header: string;
    }
  | {
      campaign: { id: string; name: string };
      post?: never;
      message: string;
      header: string;
    };

const ASSEMBLY_ACTOR = {
  id: "assembly",
  name: "Assembly",
  email: "<EMAIL>",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/logos/assembly_logo_text.png`,
};

const TWITTER_ACTOR = {
  id: "twitter",
  name: "Twitter",
  email: "<EMAIL>",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/channel_icons/twitter.png`,
};

const DISCORD_ACTOR = {
  id: "discord",
  name: "Discord",
  email: "<EMAIL>",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/channel_icons/discord.png`,
};

const LINKEDIN_ACTOR = {
  id: "linkedin",
  name: "LinkedIn",
  email: "<EMAIL>",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/channel_icons/linkedin.png`,
};

const INSTAGRAM_ACTOR = {
  id: "instagram",
  name: "Instagram",
  email: "<EMAIL>",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/channel_icons/instagram.png`,
};

const FACEBOOK_ACTOR = {
  id: "facebook",
  name: "Facebook",
  email: "<EMAIL>",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/channel_icons/facebook.png`,
};

const THREADS_ACTOR = {
  id: "threads",
  name: "Threads",
  email: "<EMAIL>",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/channel_icons/threads.png`,
};

const SLACK_ACTOR = {
  id: "slack",
  name: "Slack",
  email: "<EMAIL>",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/channel_icons/slack.png`,
};

const TIKTOK_ACTOR = {
  id: "tiktok",
  name: "TikTok",
  email: "<EMAIL>",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/channel_icons/tiktok.png`,
};

const YOUTUBE_SHORTS_ACTOR = {
  id: "youtube_shorts",
  name: "YouTube Shorts",
  email: "no-reply@youtube_shorts.com",
  avatar: `${process.env.NEXT_PUBLIC_DOMAIN}/channel_icons/youtube_shorts.png`,
};

class KnockClient {
  client: Knock = new Knock(process.env.KNOCK_API_KEY);
  tenants = this.client.tenants;
  users = this.client.users;
  workflows = this.client.workflows;

  constructor() {}

  _getRecipients = (recipients: string | string[]) => {
    if (Array.isArray(recipients)) {
      return recipients;
    } else {
      return [recipients];
    }
  };

  _getContentUrl = (contentType: ContentType, contentID: string) => {
    return `${process.env.NEXT_PUBLIC_DOMAIN}/${contentType}s/${contentID}`;
  };

  _parseContentData = (
    data: ContentData
  ): {
    contentType: ContentType;
    contentID: string;
    contentName: string;
    contentUrl: string;
    message: string;
    header: string;
  } => {
    if (data.post) {
      return {
        contentType: "post",
        contentID: data.post.id,
        contentName: data.post.title,
        contentUrl: this._getContentUrl("post", data.post.id),
        message: data.message,
        header: data.header,
      };
    } else {
      return {
        contentType: "campaign",
        contentID: data.campaign.id,
        contentName: data.campaign.name,
        contentUrl: this._getContentUrl("campaign", data.campaign.id),
        message: data.message,
        header: data.header,
      };
    }
  };

  triggerInvitedToAssembly = async (
    recipientEmail: string,
    actor: string,
    inviteID: string
  ) => {
    return await this.client.workflows.trigger("invited-to-assembly", {
      actor,
      recipients: [
        {
          id: recipientEmail,
          email: recipientEmail,
        },
      ],
      data: {
        inviteID,
      },
      cancellationKey: inviteID,
    });
  };

  triggerInvitedToWorkpace = async ({
    recipients,
    actor,
    tenant,
    inviteID,
  }: {
    recipients: string[] | string;
    actor: string;
    tenant: string;
    inviteID: string;
  }) => {
    return await this.client.workflows.trigger("invited-to-workspace", {
      actor,
      recipients: this._getRecipients(recipients),
      tenant,
      data: {
        inviteID,
      },
    });
  };

  triggerCommentOnSubscribedContentWorkflow = async (
    recipients: string | string[],
    actor: string,
    data: ContentData,
    tenant: string
  ) => {
    const { contentType, contentID, contentUrl, contentName, message, header } =
      this._parseContentData(data);

    return await this.client.workflows.trigger(
      "comment-on-assigned-approval-content",
      {
        recipients: this._getRecipients(recipients),
        actor,
        data: {
          contentID,
          contentName,
          contentType,
          contentUrl,
          message,
          header,
        },
        tenant,
      }
    );
  };

  triggerTaggedInCommentWorkflow = async (
    recipients: string | string[],
    actor: string,
    data: ContentData,
    tenant: string
  ) => {
    const { contentType, contentID, contentUrl, contentName, message, header } =
      this._parseContentData(data);

    return await this.client.workflows.trigger("tagged-in-comment", {
      recipients: this._getRecipients(recipients),
      actor,
      data: {
        contentID,
        contentName,
        contentType,
        contentUrl,
        message,
        header,
      },
      tenant,
    });
  };

  triggerRequestedApprovalWorkflow = async (
    recipients: string[] | string,
    actor: string,
    {
      contentID,
      contentType,
      contentName,
      message,
    }: {
      contentID: string;
      contentType: ContentType;
      contentName: string;
      message?: string;
    },
    tenant: string
  ) => {
    return await this.client.workflows.trigger("requested-approval", {
      recipients: this._getRecipients(recipients),
      actor,
      data: {
        contentType: capitalize(contentType),
        contentUrl: this._getContentUrl(contentType, contentID),
        contentName,
        contentID,
        message: message || null,
      },
      tenant: tenant,
    });
  };

  triggerContentApprovedWorkflow = async (
    recipients: string | string[],
    actor: string,
    {
      contentID,
      contentType,
      contentName,
    }: {
      contentID: string;
      contentType: ContentType;
      contentName: string;
    },
    tenant: string
  ) => {
    return await this.client.workflows.trigger("content-approved", {
      recipients: this._getRecipients(recipients),
      actor,
      data: {
        contentType,
        contentUrl: this._getContentUrl(contentType, contentID),
        contentName,
        contentID,
      },
      tenant: tenant,
    });
  };

  triggerApprovalDueWorkflow = async ({
    recipients,
    actorID,
    contentID,
    contentType,
    contentName,
    message,
    workspaceID,
  }: {
    recipients: string | string[];
    actorID: string;
    contentID: string;
    contentType: ContentType;
    contentName: string;
    message: string | null;
    workspaceID: string;
  }) => {
    return await this.client.workflows.trigger("approval-due-today", {
      recipients: this._getRecipients(recipients),
      actor: actorID,
      data: {
        contentType: capitalize(contentType),
        contentUrl: this._getContentUrl(contentType, contentID),
        contentName,
        message,
      },
      tenant: workspaceID,
    });
  };

  triggerApprovalMissingWorkflow = async ({
    recipients,
    postID,
    postName,
    workspaceID,
  }: {
    recipients: string | string[];
    postID: string;
    postName: string;
    workspaceID: string;
  }) => {
    return await this.client.workflows.trigger(
      "post-missing-required-approvals",
      {
        recipients: this._getRecipients(recipients),
        actor: ASSEMBLY_ACTOR,
        data: {
          postUrl: this._getContentUrl("post", postID),
          postName,
        },
        tenant: workspaceID,
      }
    );
  };

  triggerPostPostedWorkflow = async ({
    recipients,
    postID,
    postName,
    channel,
    status,
    workspaceID,
    error,
    channelPostUrl,
  }: {
    recipients: string | string[];
    postID: string;
    postName: string;
    channel: Channel;
    workspaceID: string;
  } & (
    | {
        status: "error";
        error: string;
        channelPostUrl?: never;
      }
    | {
        status: "success";
        channelPostUrl?: string | null;
        error?: never;
      }
  )) => {
    if (status === "success") {
      return await this.client.workflows.trigger("post-posted", {
        recipients: this._getRecipients(recipients),
        actor: ASSEMBLY_ACTOR,
        cancellationKey: `posted:${postID}-${channel}`,
        data: {
          postID,
          postName,
          postUrl: this._getContentUrl("post", postID),
          channel: CHANNEL_ICON_MAP[channel].label,
          channelPostUrl: channelPostUrl || this._getContentUrl("post", postID),
        },
        tenant: workspaceID,
      });
    } else {
      return await this.client.workflows.trigger("post-errored", {
        recipients: this._getRecipients(recipients),
        actor: ASSEMBLY_ACTOR,
        cancellationKey: `errored:${postID}-${channel}`,
        data: {
          postID,
          postName,
          postUrl: this._getContentUrl("post", postID),
          channel: CHANNEL_ICON_MAP[channel].label,
          error,
        },
        tenant: workspaceID,
      });
    }
  };

  cancelPostErroredWorkflow = async ({
    postID,
    channel,
  }: {
    postID: string;
    channel: Channel;
  }) => {
    return await this.client.workflows.cancel(
      "post-errored",
      `errored:${postID}-${channel}`
    );
  };

  triggerExpiringAccount = async ({
    recipients,
    accounts,
    expiresAt,
    channel,
  }: {
    recipients: string | string[];
    accounts: string[];
    expiresAt: Date;
    channel: Channel;
  }) => {
    return await this.client.workflows.trigger("expiring-connection", {
      recipients: this._getRecipients(recipients),
      actor: ASSEMBLY_ACTOR,
      data: {
        accounts,
        expiresAt: DateTime.fromJSDate(expiresAt).toLocaleString(
          DateTime.DATE_FULL
        ),
        isExpired: expiresAt < DateTime.now().endOf("day").toJSDate(),
        channel: CHANNEL_ICON_MAP[channel].label,
        reauthUrl: `${process.env.NEXT_PUBLIC_DOMAIN}/settings/profiles`,
      },
    });
  };
}

const knock = new KnockClient();

export default knock;
