import completeOnboardingTask from "apiUtils/completeOnboardingTask";
import countSocialProfiles from "apiUtils/countSocialProfiles";
import addSubscriptionItem from "apiUtils/stripe/addSubscriptionItem";
import axios from "axios";
import { StatusCodes } from "http-status-codes";
import type { NextApiRequest, NextApiResponse } from "next";
import getUserWorkspaceWithSocialProfileUsage from "server/routers/utils/getUserWorkspaceWithSocialProfileUsage";
import { slack } from "~/clients";
import prisma, { db } from "~/clients/Prisma";
import createSupabaseServerClient from "~/clients/createSupabaseServerClient";
import Threads from "~/clients/facebook/Threads";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";
import { getCookie } from "~/utils/cookies";

const handleError = (
  res: NextApiResponse,
  message: string,
  referrer: string
) => {
  const params = new URLSearchParams({
    error_message: message,
    channel: "Threads",
  });

  return res.redirect(StatusCodes.MOVED_TEMPORARILY, `${referrer}?${params}`);
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res
      .status(StatusCodes.METHOD_NOT_ALLOWED)
      .json({ message: "Method not allowed" });
  }

  const { code, state } = req.query;
  const threadsIntegrationCookie = getCookie(
    "THREADS_INTEGRATION",
    req.headers.cookie
  );

  if (!threadsIntegrationCookie) {
    return handleError(
      res,
      "Session expired, please try again",
      "/settings/profiles"
    );
  }

  const { accountID, referrer } = threadsIntegrationCookie;

  if (!code) {
    return handleError(res, "No authorization code provided", referrer);
  } else if (!state || state !== threadsIntegrationCookie?.state) {
    return handleError(res, "Session expired, please try again", referrer);
  }

  const supabase = await createSupabaseServerClient(req.headers.cookie);

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const workspace = await getUserWorkspaceWithSocialProfileUsage({
    user,
    currentWorkspaceID: getCookie("CURRENT_WORKSPACE_ID", req.headers.cookie),
  });

  if (!user || !workspace) {
    return handleError(res, "User not logged in", referrer);
  }

  try {
    let shortLivedToken;

    try {
      const response = await axios.post(
        "https://graph.threads.net/oauth/access_token",
        {
          client_id: process.env.NEXT_PUBLIC_THREADS_CLIENT_ID,
          client_secret: process.env.THREADS_CLIENT_SECRET,
          code,
          grant_type: "authorization_code",
          redirect_uri: `${process.env.NEXT_PUBLIC_DOMAIN}/api/auth/threads/callback`,
        }
      );

      shortLivedToken = response.data.access_token;
    } catch (error) {
      console.error("Threads Token Response Error", error);
      return handleError(res, "Unable to authenticate with Threads", referrer);
    }

    if (!shortLivedToken) {
      return handleError(res, "Unable to authenticate with Threads", referrer);
    }

    const authTokenResponse = await Threads.getOauthToken(shortLivedToken);
    if (authTokenResponse.errors) {
      console.error("Threads Token Response Errors", authTokenResponse.errors);
      return handleError(res, "Unable to authenticate with Threads", referrer);
    }

    const { accessToken, expiresAt, threadsUserID } = authTokenResponse.data;
    const threads = new Threads({
      accessToken,
      threadsUserID,
    });
    const userInfoResponse = await threads.getMe();

    if (!userInfoResponse.data) {
      return handleError(res, "Unable to authenticate with Threads", referrer);
    }

    const { data: userInfo } = userInfoResponse;

    const existingIntegration = await db.threadsIntegration.findFirst({
      where: {
        account: {
          threadsUserID: userInfo.id,
        },
      },
      select: {
        id: true,
        workspaceID: true,
        account: {
          select: {
            username: true,
          },
        },
      },
    });

    if (accountID && userInfo.id !== accountID) {
      const expectedThreadsAccount = await db.threadsAccount.findFirst({
        where: {
          threadsUserID: accountID,
        },
      });

      if (expectedThreadsAccount) {
        return handleError(
          res,
          `Switch to @${expectedThreadsAccount.username} on Threads first and then try again`,
          referrer
        );
      } else {
        return handleError(
          res,
          "Please login with the account you want to connect first before integrating",
          referrer
        );
      }
    }

    if (existingIntegration) {
      if (existingIntegration.workspaceID !== workspace.id) {
        return handleError(
          res,
          `@${existingIntegration.account.username} is already integrated on another Assembly workspace. Message support if this is a mistake.`,
          referrer
        );
      }

      await db.threadsIntegration.update({
        where: {
          id: existingIntegration.id,
        },
        data: {
          accessToken,
          accessTokenExpiresAt: expiresAt,
          isReintegrationRequired: false,
          account: {
            update: {
              name: userInfo.name,
              username: userInfo.username,
              profileImageUrl: userInfo.profileImageUrl,
              profileImageExpiresAt: userInfo.profileImageExpiresAt,
              biography: userInfo.biography,
            },
          },
        },
      });
    } else {
      const { subscription } = workspace;

      if (subscription) {
        const numSocialProfiles = countSocialProfiles(workspace);

        if (numSocialProfiles >= subscription.socialProfileLimit) {
          await addSubscriptionItem({
            workspaceID: workspace.id,
            itemName: "ADDITIONAL_SOCIAL_PROFILE",
            quantity: numSocialProfiles - subscription.socialProfileLimit + 1,
          });
        }
      }

      const threadsAccount = await db.threadsAccount.upsert({
        where: {
          threadsUserID: userInfo.id,
        },
        create: {
          name: userInfo.name,
          username: userInfo.username,
          profileImageUrl: userInfo.profileImageUrl,
          profileImageExpiresAt: userInfo.profileImageExpiresAt,
          biography: userInfo.biography,
          threadsUserID: userInfo.id,
        },
        update: {
          name: userInfo.name,
          username: userInfo.username,
          profileImageUrl: userInfo.profileImageUrl,
          profileImageExpiresAt: userInfo.profileImageExpiresAt,
          biography: userInfo.biography,
        },
      });

      const threadsIntegration = await db.threadsIntegration.create({
        data: {
          workspaceID: workspace.id,
          accessToken,
          accessTokenExpiresAt: expiresAt,
          userID: user.id,
          accountID: threadsAccount.id,
        },
        include: {
          account: true,
          user: true,
        },
      });

      const deletedIntegrations = await prisma.threadsIntegration.findMany({
        where: {
          accountID: threadsAccount.id,
          workspaceID: workspace.id,
          deletedTimeStamp: {
            not: 0,
          },
        },
      });

      if (deletedIntegrations.length > 0) {
        await db.post.updateMany({
          where: {
            threadsIntegrationID: {
              in: deletedIntegrations.map((integration) => integration.id),
            },
          },
          data: {
            threadsIntegrationID: threadsIntegration.id,
          },
        });
      }

      posthog.capture({
        distinctId: user.id,
        event: POSTHOG_EVENTS.socialAccount.connected,
        properties: {
          channel: "Threads",
        },
        groups: {
          workspace: workspace.id,
          company: workspace.company.id,
        },
      });

      await slack.newConnectedAccount({
        username: `@${threadsAccount.username}`,
        channel: "Threads",
        url: `https://threads.com/@${threadsAccount.username}`,
        workspace,
        connectedBy: threadsIntegration.user,
      });

      if (workspace._count.threadsIntegrations === 0) {
        await db.postDefault.upsert({
          where: {
            workspaceID: workspace.id,
          },
          create: {
            workspaceID: workspace.id,
            threadsIntegrationID: threadsIntegration.id,
          },
          update: {
            threadsIntegrationID: threadsIntegration.id,
          },
        });
      }

      await completeOnboardingTask({
        workspaceID: workspace.id,
        userID: user.id,
        taskType: "CONNECT_ACCOUNT",
        state: "COMPLETED",
      });
    }

    const params = new URLSearchParams({
      account_name: `@${userInfo.username}`,
      channel: "Threads",
      flow: accountID
        ? "reconnect_account"
        : existingIntegration
          ? "existing_account"
          : "new_account",
    });

    if (referrer.includes("/settings/profiles")) {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/profiles?${params}`
      );
    } else {
      return res.redirect(
        StatusCodes.MOVED_TEMPORARILY,
        `/settings/threads?${params}`
      );
    }
  } catch (error) {
    console.error("Threads auth error:", error);
    return handleError(res, "Error connecting Threads account", referrer);
  }
}
