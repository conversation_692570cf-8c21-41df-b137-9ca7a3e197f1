import Uppy from "@uppy/core";
import classNames from "classnames";
import uniq from "lodash/uniq";
import { useState } from "react";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";
import { useWorkspace } from "~/providers";
import { AssetType } from "~/types/Asset";
import { getDefaultProfilePicture } from "~/utils";
import { UploadingAsset } from "../../files/Assets";
import VideoPreviewContainer from "../VideoPreviewContainer";

type YouTubeIntegration = {
  id: string;
  account: {
    username: string;
    profileImageUrl: string;
  };
};

type Props = {
  content: {
    id: string;
    title: string | null;
  };
  asset: AssetType | null;
  connectedIntegration: YouTubeIntegration | null;
  isReadOnly: boolean;
  uppy?: Uppy | null;
  uploadingAssets: UploadingAsset[];
  getValidFiles: (files: File[]) => Promise<File[]>;
  onDrop: (files: File[]) => void;
  onDeleteClick: (asset: AssetType) => void;
  isDraggedOver: boolean;
};

const YouTubeShortsPostPreviewNewDesign = ({
  content,
  asset,
  connectedIntegration,
  isReadOnly,
  uppy,
  uploadingAssets,
  getValidFiles,
  onDrop,
  onDeleteClick,
  isDraggedOver,
}: Props) => {
  const { currentWorkspace } = useWorkspace();
  const [showMore, setShowMore] = useState<boolean>(false);

  const accountName =
    connectedIntegration?.account.username || currentWorkspace?.name;
  const profilePictureUrl =
    connectedIntegration?.account.profileImageUrl ??
    getDefaultProfilePicture("YouTubeShorts");

  const title = content?.title;

  const video = asset || undefined;

  return (
    <VideoPreviewContainer
      video={video}
      getValidFiles={getValidFiles}
      acceptedFileTypes={{ "video/*": [] }}
      contentID={content.id}
      uploadingAssets={uploadingAssets}
      isDraggedOver={isDraggedOver}
      isReadOnly={isReadOnly}
      uppy={uppy}
      onDrop={onDrop}
      onDeleteClick={onDeleteClick}
    >
      {showMore && (
        <div
          className={classNames(
            "absolute inset-0 rounded-xl bg-black transition-all",
            {
              "bg-opacity-0": !showMore,
              "bg-opacity-40": showMore,
            }
          )}
        />
      )}
      {/* Right section */}
      <div className="absolute right-3 bottom-5">
        <div className="flex flex-col items-center gap-5">
          <svg
            viewBox="0 0 32 32"
            className="h-6 w-6 fill-white"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M25.7183 11.9977C26.3259 11.9977 26.9005 12.2739 27.28 12.7484L27.9877 13.633C28.51 14.2858 28.5711 15.1945 28.141 15.9114L26.9248 17.9383L27.6157 19.6654C28.0045 20.6375 27.8584 21.742 27.2303 22.5796L26.3333 23.7755V25.9977C26.3333 27.1023 25.4379 27.9977 24.3333 27.9977H11.6666C10.5621 27.9977 9.66663 27.1023 9.66663 25.9977V12.8266C9.66663 11.9929 9.92713 11.18 10.4117 10.5016L15.6633 3.14937C15.7999 2.95816 16.0512 2.88785 16.2671 2.98041L16.3383 3.01091C18.1725 3.797 19.1467 5.81473 18.6217 7.73996L17.4605 11.9977H25.7183ZM5 13.3285C3.89543 13.3285 3 14.224 3 15.3285V25.9952C3 27.0998 3.89543 27.9952 5 27.9952H7.66667V13.3285H5Z"
            ></path>
          </svg>
          <svg
            viewBox="0 0 32 32"
            className="h-6 w-6 fill-white"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M5.28167 20.0001C4.6741 20.0001 4.09947 19.7239 3.71993 19.2495L3.01222 18.3648C2.48998 17.712 2.42886 16.8033 2.85897 16.0864L4.07513 14.0595L3.38429 12.3324C2.99545 11.3603 3.14153 10.2558 3.76972 9.41822L4.66667 8.22228L4.66667 6.00006C4.66667 4.89549 5.5621 4.00006 6.66667 4.00006L19.3333 4.00006C20.4379 4.00006 21.3333 4.89549 21.3333 6.00006L21.3333 19.1712C21.3333 20.0049 21.0728 20.8178 20.5883 21.4962L15.3367 28.8484C15.2001 29.0396 14.9488 29.1099 14.7328 29.0174L14.6617 28.9869C12.8275 28.2008 11.8532 26.1831 12.3783 24.2578L13.5395 20.0001L5.28167 20.0001ZM26 18.6693C27.1046 18.6693 28 17.7738 28 16.6693L28 6.0026C28 4.89803 27.1046 4.0026 26 4.0026L23.3333 4.0026L23.3333 18.6693L26 18.6693Z"
            ></path>
          </svg>
          <svg
            viewBox="0 0 32 32"
            className="h-6 w-6 fill-white"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M29 5.66667L29.0116 27.4454C29.012 28.1818 28.4154 28.779 27.679 28.7794C27.3251 28.7796 26.9857 28.6391 26.7355 28.3889L22.6665 24.0001H5.66667C4.2 24.0001 3 22.8001 3 21.3334V5.66667C3 4.2 4.2 3 5.66667 3H26.3467C27.8133 3 29 4.2 29 5.66667ZM7.99984 10.5001C7.99984 9.67164 8.67141 9.00007 9.49984 9.00007H22.4998C23.3283 9.00007 23.9998 9.67164 23.9998 10.5001C23.9998 11.3285 23.3283 12.0001 22.4998 12.0001H9.49984C8.67141 12.0001 7.99984 11.3285 7.99984 10.5001ZM9.49984 15.0001C8.67141 15.0001 7.99984 15.6716 7.99984 16.5001C7.99984 17.3285 8.67141 18.0001 9.49984 18.0001H18.4998C19.3283 18.0001 19.9998 17.3285 19.9998 16.5001C19.9998 15.6716 19.3283 15.0001 18.4998 15.0001H9.49984Z"
            ></path>
          </svg>
          <svg
            viewBox="0 0 32 32"
            className="h-6 w-6 fill-white"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M17.7375 5.26556L28.6745 15.2624C29.1083 15.6589 29.1083 16.3422 28.6745 16.7387L17.7375 26.7356C17.0958 27.3222 16.0628 26.8669 16.0628 25.9975V21.6217C16.0628 21.6217 16.0627 21.6217 16.0626 21.6217C9.92564 21.6217 6.69114 23.9378 5.1615 25.5968C4.80726 25.981 3.97329 25.7343 4.00015 25.2125C4.22558 20.8321 5.86088 10.8892 16.0626 10.8892C16.0627 10.8892 16.0628 10.8892 16.0628 10.8892V6.00368C16.0628 5.13426 17.0958 4.67898 17.7375 5.26556Z"
            ></path>
          </svg>
          <img src={profilePictureUrl || ""} className="h-6 w-6 rounded-full" />
        </div>
      </div>
      {/* Bottom section */}
      <div className="font-body-sm absolute bottom-5 left-3">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1.5">
            <img
              src={profilePictureUrl || ""}
              className="h-6 w-6 rounded-full"
            />
            <div className="font-medium">{accountName}</div>
            <div className="rounded-xs bg-[#BB271A] px-1 py-0.5">Subscribe</div>
          </div>
          <div
            className={classNames(
              "transition-max-height mr-9 h-full leading-tight duration-300",
              {
                "max-h-[18px] overflow-clip": !showMore,
                "scrollbar-hidden max-h-[300px] overflow-auto": showMore,
              }
            )}
            onClick={() => setShowMore(!showMore)}
          >
            {title?.split("\n").map((line, index) => {
              if (line === "") {
                return <div key={index} className="py-1" />;
              } else {
                let parsedLine = line;

                const hashtags = uniq(line.match(/(#[\w\d_]+)/g)) || [];

                hashtags.forEach((hashtag, index) => {
                  const tag = hashtag.replace("#", "");

                  const tagRegex = new RegExp(`${hashtag}\\b`, "g");

                  parsedLine = parsedLine.replaceAll(
                    tagRegex,
                    `[${hashtag}](https://www.youtube.com/hashtag/${tag}/shorts)`
                  );
                });

                return (
                  <div key={index} className="flex items-center" dir="auto">
                    <ReactMarkdown
                      className="mr-0 w-fit break-words"
                      linkTarget="_blank"
                      children={parsedLine}
                      components={{
                        h1: ({ node, ...props }) => {
                          return (
                            <span>
                              # <span {...props} />
                            </span>
                          );
                        },
                        h2: ({ node, ...props }) => {
                          return (
                            <span>
                              ## <span {...props} />
                            </span>
                          );
                        },
                        h3: ({ node, ...props }) => {
                          return (
                            <span>
                              ### <span {...props} />
                            </span>
                          );
                        },
                        em: "span",
                        ol: ({ node, ...props }) => {
                          return (
                            <ol
                              {...props}
                              className="list-inside list-decimal"
                            />
                          );
                        },
                        ul: ({ node, ...props }) => {
                          // @ts-expect-error children is not defined on the node
                          const children = props.children[1].props.children;
                          return (
                            <ol {...props} className="list-inside">
                              <li>- {children}</li>
                            </ol>
                          );
                        },
                        strong: ({ node, ...props }) => {
                          return (
                            <span
                              {...props}
                              className="font-medium text-white"
                            />
                          );
                        },
                        code: ({ node, ...props }) => {
                          return (
                            <span>
                              `<span {...props} />`
                            </span>
                          );
                        },
                        a: ({ node, ...props }) => {
                          return (
                            <a
                              onClick={(e) => {
                                e.stopPropagation();
                              }}
                              className="font-semibold text-white"
                              {...props}
                            />
                          );
                        },
                      }}
                    />
                  </div>
                );
              }
            })}
          </div>
        </div>
      </div>
    </VideoPreviewContainer>
  );
};

export default YouTubeShortsPostPreviewNewDesign;
