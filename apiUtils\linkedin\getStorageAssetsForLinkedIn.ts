import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForLinkedIn = async (post: {
  id: string;
  linkedInContent: {
    id: string;
  } | null;
  workspaceID: string;
}): Promise<{
  assets: AssetType[];
  coverPhoto: AssetType | null;
  captions: AssetType | null;
}> => {
  const { linkedInContent } = post;
  if (!linkedInContent) {
    return { assets: [], coverPhoto: null, captions: null };
  }

  const slug = CHANNEL_ICON_MAP["LinkedIn"].slug;

  const assetsMap = await getAssetsForFilePaths([
    {
      id: linkedInContent.id,
      path: `${post.workspaceID}/${post.id}/${slug}/${linkedInContent.id}`,
    },
    {
      id: "cover_photo",
      path: `${post.workspaceID}/${post.id}/${slug}/${linkedInContent.id}/cover_photo`,
    },
    {
      id: "captions",
      path: `${post.workspaceID}/${post.id}/${slug}/${linkedInContent.id}/captions`,
    },
  ]);

  const assets = assetsMap[linkedInContent.id];
  const coverPhoto = assetsMap["cover_photo"][0];
  const captions = assetsMap["captions"][0];

  return {
    assets,
    coverPhoto,
    captions,
  };
};

export default getStorageAssetsForLinkedIn;
