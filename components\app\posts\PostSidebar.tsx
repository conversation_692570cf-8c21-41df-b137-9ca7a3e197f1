import { useEffect, useState } from "react";

import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { useElementSize } from "@mantine/hooks";
import { Channel, PostStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import classNames from "classnames";
import { Divider, Label, Tooltip } from "~/components";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { handleTrpcError, trpc } from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import Approvals, { Approval } from "../Approvals";
import CampaignSelect from "../CampaignSelect";
import LabelSelect from "../LabelSelect";
import NotesEditor from "../tiptap/NotesEditor";
import { ScheduledPost } from "./PostPreviewContainer";
import SchedulePostSection from "./SchedulePostSection";
import StatusButton from "./StatusButton";
import { Subscriber } from "./Subscribers";

type Label = {
  id: string;
  name: string;
  color: string;
  backgroundColor: string;
  createdAt: Date;
};

type Post = {
  id: string;
  channels: Channel[];
  title: string;
  publishDate: Date | null;
  publishTime: Date | null;
  publishAt: Date | null;
  notesEditorState: JSONContent | null;
  isIdea: boolean;
  campaignID: string | null;
  labels: Label[];
  subscribers: Subscriber[];
  approvals: Approval[];
  status: PostStatus;
};

type Campaign = {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
};

type Props = {
  post: Post & {
    campaign: Campaign | null;
    scheduledPosts: ScheduledPost[];
  } & PostOrIdea;
  isReadOnly?: boolean;
};

const PostSidebar = ({ post, isReadOnly = false }: Props) => {
  const trpcUtils = trpc.useUtils();

  const { ref, height } = useElementSize();

  const [notesHTML, setNotesHTML] = useState<string | null>(null);
  const [notesModalOpen, setNotesModalOpen] = useState<boolean>(false);

  const [labels, setLabels] = useState<Label[]>(post.labels);

  const updatePostMutation = trpc.post.update.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Failed to Update Post", error });
    },
    onSuccess: (data) => {
      if (post.isIdea) {
        trpcUtils.idea.getAll.refetch();
      } else {
        trpcUtils.post.getMany.refetch();
      }
    },
  });

  const updateCampaign = (
    campaign: {
      id: string;
      name: string;
      startDate: Date;
      endDate: Date;
    } | null
  ) => {
    const newCampaignID = campaign != null ? campaign.id : null;
    updatePostMutation.mutate({
      id: post.id,
      campaignID: newCampaignID,
    });

    const currentPostData = trpcUtils.post.get.getData({ id: post.id });
    const currentPost = currentPostData?.post;
    if (currentPost) {
      trpcUtils.post.get.setData(
        { id: post.id },
        {
          post: {
            ...currentPost,
            campaignID: newCampaignID,
          },
          isReadOnly: isReadOnly,
        }
      );
    }

    if (post.isIdea) {
      const previousData = trpcUtils.idea.getAll.getData();
      if (previousData) {
        const previousIdeas = previousData.ideas;
        const updatedIdeas = previousIdeas.map((previousIdea) => {
          if (previousIdea.id === post.id) {
            return {
              ...previousIdea,
              campaign,
            };
          } else {
            return previousIdea;
          }
        });
        trpcUtils.idea.getAll.setData(undefined, {
          ideas: updatedIdeas,
        });
      }
    }
  };

  const updateLabelsMutation = trpc.label.updateLabels.useMutation({
    onSuccess: () => {
      if (post.isIdea) {
        const previousData = trpcUtils.idea.getAll.getData();
        if (previousData) {
          const previousIdeas = previousData.ideas;
          const updatedIdeas = previousIdeas.map((previousIdea) => {
            if (previousIdea.id === post.id) {
              return {
                ...previousIdea,
                labels,
              };
            } else {
              return previousIdea;
            }
          });
          trpcUtils.idea.getAll.setData(undefined, {
            ideas: updatedIdeas,
          });
        }
      } else {
        const previousData = trpcUtils.post.getMany.getData();
        if (previousData) {
          const previousPosts = previousData.posts;
          const updatedPosts = previousPosts.map((previousPost) => {
            if (previousPost.id === post.id) {
              return {
                ...previousPost,
                ...labels,
              };
            } else {
              return previousPost;
            }
          });
          trpcUtils.post.getMany.setData(undefined, {
            posts: updatedPosts,
          });
          trpcUtils.post.get.invalidate({ id: post.id });
        }
      }
    },
  });

  const labelQuery = trpc.label.getAll.useQuery();

  const updateLabels = (labels: Label[]) => {
    updateLabelsMutation.mutate({
      contentID: post.id,
      contentType: "post",
      labels,
    });
    labelQuery.refetch();
    setLabels(labels);
  };

  useEffect(() => {
    if (notesHTML && notesHTML.length) {
      const taskInputs = document.querySelectorAll(
        'li[data-type="taskItem"] label input'
      );
      taskInputs.forEach((input) => {
        if (input instanceof HTMLInputElement) {
          input.disabled = true;

          if (input.checked) {
            const listItem = input.closest("li");
            if (listItem) {
              listItem.classList.add("task-checked");
            }
          }
        }
      });
    }
  }, [notesHTML]);

  const attributes = [
    {
      label: "Campaign",
      value: (
        <div className="group flex items-center gap-2">
          <CampaignSelect
            value={post.campaignID}
            onChange={updateCampaign}
            defaultCampaigns={post.campaign ? [post.campaign] : undefined}
            isReadOnly={isReadOnly}
            dropdownPosition="right"
            publishDate={post.publishDate}
          />
          {post.campaignID && !isReadOnly && (
            <Tooltip value="Go to campaign">
              <a
                href={`/campaigns/${post.campaignID}`}
                className="opacity-0 transition-opacity group-hover:opacity-100"
              >
                <ArrowRightIcon className="text-secondary h-4 w-4" />
              </a>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      label: "Status",
      value: (
        <StatusButton post={post} isReadOnly={isReadOnly} showHotKey={true} />
      ),
    },
    {
      label: "Label",
      value: (
        <LabelSelect
          value={labels}
          onChange={updateLabels}
          isReadOnly={isReadOnly}
          isCreateable={true}
          options={labelQuery.data?.labels || post.labels}
          hotKeyOptions={{
            keys: "L",
            label: "Set Labels",
          }}
        />
      ),
    },
  ];

  return (
    <div className="w-full px-4 pt-2.5 pb-4 md:block md:w-[350px] md:px-0 md:pb-0">
      <div>
        <SchedulePostSection post={post} isReadOnly={isReadOnly} />
        <dl className="space-y-0.5">
          {attributes.map((attribute) => {
            return (
              <div
                key={attribute.label}
                className="flex h-fit min-h-[36px] items-start gap-4"
              >
                <dt className="font-eyebrow-sm text-medium-gray mt-1 w-[72px]">
                  {attribute.label}:
                </dt>
                <dd className="w-full font-medium">{attribute.value}</dd>
              </div>
            );
          })}
        </dl>
      </div>
      <Divider padding="md" />
      <Approvals
        id={post.id}
        type="post"
        approvals={post.approvals}
        isReadOnly={isReadOnly}
        publishDate={post.publishDate ?? undefined}
        publishAt={post.publishAt}
      />
      <Divider padding="md" />
      <div className="font-eyebrow-sm text-medium-gray mb-1">Notes:</div>
      {notesHTML && notesHTML.length && (
        <div className="font-body-sm">
          <div
            ref={ref}
            className={classNames(
              "mt-1 max-h-[100px] overflow-hidden text-ellipsis [&_li.task-checked]:text-gray-500 [&_li.task-checked]:line-through [&_li:not(ul[data-type=taskList]_li)]:ml-5 [&_li[data-checked=true]]:line-through [&_li[data-type=taskItem]_label_input]:cursor-not-allowed [&_li[data-type=taskItem]_label_input]:opacity-60 [&_ol_li]:list-decimal [&_ul_li:not(ul[data-type=taskList]_li)]:list-disc [&_ul[data-type=taskList]_li]:flex [&_ul[data-type=taskList]_li]:items-center [&_ul[data-type=taskList]_li]:gap-1",
              {
                "mb-2": height < 100,
              }
            )}
            dangerouslySetInnerHTML={{ __html: notesHTML }}
          />
          {height >= 100 && <div className="mb-2">...</div>}
        </div>
      )}
      <Button
        size="sm"
        onClick={() => {
          setNotesModalOpen(true);
        }}
        variant="secondary"
      >
        View Notes
      </Button>
      {/* This is a hacky solution so that on initial load of the PostSidebar notesHTML is set
          as the real Editor within the Modal isn't rendered until the modal is opened for the
          first time */}
      <div className="hidden">
        <NotesEditor
          initialValue={post.notesEditorState}
          onSave={() => {}}
          onUpdateContent={(copy) => setNotesHTML(copy)}
          placeholder="Add any notes or creative briefs for the post here"
          isReadOnly={true}
        />
      </div>
      <Credenza.Root open={notesModalOpen} onOpenChange={setNotesModalOpen}>
        <Credenza.Header>
          <Credenza.Title>Notes</Credenza.Title>
          <Credenza.Description className="sr-only">
            Add any notes or creative briefs for the post here
          </Credenza.Description>
        </Credenza.Header>
        <Credenza.Body>
          <NotesEditor
            initialValue={post.notesEditorState}
            onSave={(editorState) => {
              updatePostMutation.mutate({
                id: post.id,
                notesEditorState: editorState,
              });

              const postData = trpcUtils.post.get.getData({
                id: post.id,
              });
              if (postData) {
                trpcUtils.post.get.setData(
                  { id: post.id },
                  {
                    post: {
                      ...postData.post,
                      notesEditorState: editorState,
                    },
                    isReadOnly,
                  }
                );
              }
            }}
            onUpdateContent={(copy) => setNotesHTML(copy)}
            placeholder="Add any notes or creative briefs for the post here"
            isReadOnly={isReadOnly}
          />
        </Credenza.Body>
      </Credenza.Root>
    </div>
  );
};

export default PostSidebar;
