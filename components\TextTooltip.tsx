import { ReactNode } from "react";

import Tooltip from "./Tooltip";

type Props = {
  value: ReactNode | string;
  children: React.ReactElement | string;
};

const TextTooltip = ({ value, children }: Props) => {
  return (
    <Tooltip value={value} className="p-3">
      <span className="decoration-medium-gray underline decoration-dashed underline-offset-4">
        {children}
      </span>
    </Tooltip>
  );
};

export default TextTooltip;
