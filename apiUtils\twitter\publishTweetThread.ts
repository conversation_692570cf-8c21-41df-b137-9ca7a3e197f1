import { Asset } from "@prisma/client";
import getAssetBuffer from "apiUtils/getAssetBuffer";
import getAssetsForTweets from "apiUtils/getAssetsForTweets";
import getAuthedTwitterClient from "apiUtils/getAuthedTwitterClient";
import sortBy from "lodash/sortBy";
import { DateTime } from "luxon";
import { EUploadMimeType, SendTweetV2Params } from "twitter-api-v2";
import { inngest } from "~/clients/inngest/inngest";
import { GENERIC_ERROR_MESSAGE } from "~/constants";
import { ScheduledPostResult } from "~/types/ScheduledPostResult";
import { joinWithAnd } from "~/utils";

type Post = {
  id: string;
  tweets: {
    id: string;
    copy: string;
    threadPosition: number;
    engagements: {
      id: string;
      postDelaySeconds: number;
    }[];
    assets: {
      id: string;
      position: number;
      altText: string | null;
      asset: Asset;
    }[];
    mediaTaggedUsernames: string[];
  }[];
  twitterIntegration: {
    accessToken: string;
    accessSecret: string;
    account: {
      username: string;
    };
  } | null;
  workspaceID: string;
};

type TwitterResponseError = {
  error: boolean;
  code: number;
  rateLimit: { limit: number; remaining: number; reset: number };
  data: {
    title: string;
    details: string;
    type: string;
  };
  errors: {
    parameters: any;
    message: string;
  }[];
};

const publishTweetThread = async (
  post: Post,
  replyToTweetID?: string
): Promise<ScheduledPostResult> => {
  console.log(`Posting Tweet (postID: ${post.id})`);

  const twitterIntegration = post.twitterIntegration;

  if (!twitterIntegration) {
    console.error(`Error ${post.id}: No Twitter integration`);
    return {
      status: "NonRetriableError",
      error: "No Twitter integration",
    };
  } else {
    const client = getAuthedTwitterClient(twitterIntegration);

    const tweetAssets = await getAssetsForTweets(post);

    // Only try and post tweets that has either text or an asset
    const tweets = sortBy(post.tweets, "threadPosition").filter(
      (tweet) => tweet.copy.length > 0 || tweetAssets[tweet.id].length > 0
    );

    let mediaError: null | string = null;
    let rawMediaError: null | {
      title: string;
      details: string;
      type: string;
    } = null;
    const tweetPayload = await Promise.all(
      tweets.map(async (tweet, index): Promise<SendTweetV2Params> => {
        const assets = tweetAssets[tweet.id];
        let mediaIDs = null;

        let taggedUserIDs: string[] = [];

        if (assets?.length) {
          mediaIDs = await Promise.all(
            assets.map(async (asset) => {
              const mediaBuffer = await getAssetBuffer(asset.signedUrl);

              try {
                const mediaID = await client.v2.uploadMedia(mediaBuffer, {
                  media_type: asset.metadata.mimetype as EUploadMimeType,
                });

                const { altText } = asset;

                if (altText != null && altText.length > 0) {
                  await client.v2.createMediaMetadata(mediaID, {
                    alt_text: {
                      text: altText,
                    },
                  });
                }

                // const mediaStatus = await client.v2.get(
                //   `media/upload?media_id=${mediaID}&command=STATUS`
                // );
                // console.log("Media Status", mediaStatus);

                return mediaID;
              } catch (error) {
                try {
                  const twitterError = error as TwitterResponseError;
                  console.error(
                    `Full Twitter Upload Error ${asset.name}`,
                    twitterError
                  );
                  console.error(
                    `Twitter Upload Media Error ${asset.name}`,
                    twitterError.data
                  );
                  // @ts-ignore message does exist sometimes
                  rawMediaError = twitterError.data || error;
                  const errors = twitterError.errors || [error] || [];
                  mediaError = `Error uploading ${asset.name}: ${errors
                    .map((error) => error.message)
                    .join("\n")}`;
                  return "";
                } catch (error) {
                  console.error("Twitter Error Catch #1", error);
                  mediaError = GENERIC_ERROR_MESSAGE;
                  return "";
                }
              }
            })
          );

          if (tweet.mediaTaggedUsernames.length) {
            try {
              const users = await client.v2.usersByUsernames(
                tweet.mediaTaggedUsernames
              );

              taggedUserIDs = users.data.map((user) => user.id);
            } catch (error) {
              console.log("Error finding tagged users", error);
              mediaError = `Unable to find all tagged users, verify they exist: ${joinWithAnd(
                tweet.mediaTaggedUsernames.map((username) => `@${username}`)
              )}`;
            }
          }
        }

        return {
          text: tweet.copy,
          ...(index === 0 &&
            replyToTweetID && {
              reply: {
                in_reply_to_tweet_id: replyToTweetID,
              },
            }),
          ...(mediaIDs?.length && {
            media: {
              media_ids: mediaIDs as
                | [string]
                | [string, string]
                | [string, string, string]
                | [string, string, string, string],
              tagged_user_ids: taggedUserIDs,
            },
          }),
        };
      })
    );

    try {
      // If any media upload has errored we won't try posting the thread
      if (mediaError != null) {
        return {
          status: "Error",
          error: mediaError,
          rawError: rawMediaError || undefined,
        };
      }

      console.log(`Tweet Payload (postID: ${post.id})`, tweetPayload);
      const responses = await client.v2.tweetThread(tweetPayload);
      console.log(`Tweet Responses (postID: ${post.id})`, responses);

      const tweetID = responses[0].data.id;
      if (tweetID) {
        const tweetUrl = `https://twitter.com/${twitterIntegration.account.username}/status/${tweetID}`;

        console.log(`Tweet posted to ${tweetUrl}`);

        try {
          const engagements = tweets[0].engagements;

          if (engagements.length) {
            const events = engagements.map((engagement) => ({
              name: "twitter.postEngagement" as const,
              data: {
                externalPostID: tweetID,
                engagementID: engagement.id,
                delayUntil: DateTime.now()
                  .plus({
                    seconds: engagement.postDelaySeconds,
                  })
                  .toJSDate(),
                permalink: tweetUrl,
              },
            }));

            if (events.length) {
              await inngest.send(events);
            }
          }
        } catch (error) {
          console.error("Engagement Error", error);
        }

        return {
          status: "Success",
          postUrl: tweetUrl,
        };
      } else {
        return {
          status: "Error",
          error: GENERIC_ERROR_MESSAGE,
        };
      }
    } catch (error) {
      console.error(`Error Posting Tweet ${post.id}`, error);
      const twitterError = error as {
        error: true;
        data: {
          detail: string;
          title: string;
          status: number;
          type: string;
          errors?: {
            message: string;
            parameters: {
              resource_id: string;
              resource_type: string;
              field: string;
              value: string;
            };
          }[];
        };
      };

      try {
        const rawError = twitterError.data;
        console.error(`Error Posting Tweet ${post.id} rawError`, rawError);
        const twitterErrors = Array.isArray(rawError.errors)
          ? rawError.errors
          : [];
        const errorMessages = twitterErrors.map((error) => error.message);

        try {
          const errorMessage =
            errorMessages.length > 0
              ? errorMessages.join(". ")
              : rawError.detail || rawError.title || GENERIC_ERROR_MESSAGE;

          return {
            status: "Error",
            error: errorMessage,
            rawError,
          };
        } catch (error) {
          console.log("Twitter Error Catch #1", error);
          return {
            status: "Error",
            error: GENERIC_ERROR_MESSAGE,
          };
        }
      } catch (error) {
        console.log("Twitter Error Catch #2", error);

        return {
          status: "Error",
          error: GENERIC_ERROR_MESSAGE,
        };
      }
    }
  }
};

export default publishTweetThread;
