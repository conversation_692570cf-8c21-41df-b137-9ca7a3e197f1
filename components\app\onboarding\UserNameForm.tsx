import { ReferralSource } from "@prisma/client";
import { useForm } from "react-hook-form";
import { Label } from "~/components";
import { FormTextInput } from "~/components/form";
import FormRadioGroup from "~/components/form/FormRadioGroup";
import Button from "~/components/ui/Button";
import RadioGroup from "~/components/ui/RadioGroup";
import { useUser } from "~/providers";
import { REFERRAL_SOURCE_MAP } from "~/types/ReferralSourceType";
import { handleTrpcError, trpc } from "~/utils";
import * as OnboardingCard from "./OnboardingCard";

type FormValues = {
  firstName: string;
  lastName: string;
  referralSource: ReferralSource | undefined;
};

type Props = {
  onNextClick: () => void;
  hideReferralSource: boolean;
  isLoading: boolean;
};

const UserNameForm = ({
  onNextClick,
  hideReferralSource,
  isLoading,
}: Props) => {
  const { user } = useUser();

  const { control, handleSubmit } = useForm<FormValues>({
    defaultValues: {
      firstName: user?.firstName || undefined,
      lastName: user?.lastName || undefined,
      referralSource: user?.referralSource || undefined,
    },
  });

  const onboardUserMutation = trpc.user.onboard.useMutation();

  const handleOnboard = ({
    firstName,
    lastName,
    referralSource,
  }: FormValues) => {
    onboardUserMutation.mutate(
      {
        firstName,
        lastName,
        referralSource,
      },
      {
        onError: (error) => {
          handleTrpcError({ title: "Error Saving Name", error });
        },
      }
    );
    onNextClick();
  };

  return (
    <OnboardingCard.Content
      title="Let's setup your account"
      showTermsAndConditions={false}
      direction={1}
    >
      <form onSubmit={handleSubmit(handleOnboard)} className="mx-auto max-w-sm">
        <div className="mt-10 flex flex-col gap-4">
          <Label value="First name">
            <FormTextInput
              control={control}
              name="firstName"
              placeholder="Enter your first name"
              size="2xl"
              rules={{ required: true }}
              kind="outline"
            />
          </Label>
          <Label value="Last name">
            <FormTextInput
              control={control}
              name="lastName"
              placeholder="Enter your last name"
              size="2xl"
              rules={{ required: true }}
              kind="outline"
            />
          </Label>
          {!hideReferralSource && (
            <Label value="Where did you hear about us?">
              <FormRadioGroup
                control={control}
                name="referralSource"
                rules={{ required: true }}
              >
                {Object.values(ReferralSource).map((referralSource) => (
                  <RadioGroup.Item key={referralSource} value={referralSource}>
                    {REFERRAL_SOURCE_MAP[referralSource]}
                  </RadioGroup.Item>
                ))}
              </FormRadioGroup>
            </Label>
          )}
        </div>
        <div className="mt-8 flex justify-end">
          <Button
            size="lg"
            className="w-full"
            type="submit"
            isLoading={isLoading}
          >
            Continue
          </Button>
        </div>
      </form>
    </OnboardingCard.Content>
  );
};

export default UserNameForm;
