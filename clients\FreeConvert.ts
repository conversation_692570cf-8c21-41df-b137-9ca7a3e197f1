import { FreeConvertJobStatus } from "@prisma/client";
import catchAxiosError from "apiUtils/catchAxiosError";
import axios, { AxiosInstance } from "axios";
import { StatusCodes } from "http-status-codes";
import {
  createErrorResponse,
  createSuccessResponse,
  Response,
} from "./Response";

class FreeConvert {
  BASE_URL = "https://api.freeconvert.com/v1";

  client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: this.BASE_URL,
      headers: {
        Authorization: `Bearer ${process.env.FREE_CONVERT_ACCESS_TOKEN}`,
      },
    });
  }

  createCompressJob = async ({
    video,
    sizeCompression,
    resolutionCompression,
  }: {
    video: {
      url: string;
      filename: string;
      mimetype: string;
    };
    sizeCompression?:
      | {
          type: "by_percentage";
          percentage: number;
        }
      | {
          type: "size";
          maxFileSizeMB: number;
        };

    resolutionCompression?:
      | {
          type: "resolution_width";
          width: number;
        }
      | {
          type: "resolution_height";
          height: number;
        };
  }): Promise<
    Response<{
      id: string;
      status: FreeConvertJobStatus;
      startedAt: Date;
      createdAt: Date;
      updatedAt: Date;
    }>
  > => {
    try {
      let format = video.mimetype.split("/")[1];

      const isQuicktime = format === "quicktime";

      let importTask: Object = {
        operation: "import/url",
        url: video.url,
        filename: video.filename,
      };
      if (isQuicktime) {
        format = "mov";
      }

      const sizeCompressionOptions = sizeCompression
        ? sizeCompression.type === "size"
          ? {
              video_codec: "h264",
              compress_video: "by_size",
              video_compress_max_filesize: sizeCompression.maxFileSizeMB,
            }
          : {
              video_codec: "h264",
              compress_video: "by_percentage",
              video_compression_percentage: sizeCompression.percentage,
            }
        : null;

      const resolutionCompressionOptions = resolutionCompression
        ? resolutionCompression.type === "resolution_width"
          ? {
              video_codec: "h264",
              compress_video: "by_resolution",
              video_compression_resize_method: "by_width_keep_ar",
              video_custom_width: resolutionCompression.width,
            }
          : {
              video_codec: "h264",
              compress_video: "by_resolution",
              video_compression_resize_method: "by_height_keep_ar",
              video_custom_height: resolutionCompression.height,
            }
        : null;

      const body = {
        tasks: {
          import: importTask,
          ...(sizeCompressionOptions && {
            compressSize: {
              operation: "compress",
              input: "import",
              input_format: format,
              output_format: format,
              options: sizeCompressionOptions,
            },
          }),
          ...(resolutionCompressionOptions && {
            compressResolution: {
              operation: "compress",
              input: sizeCompressionOptions ? "compressSize" : "import",
              input_format: format,
              output_format: format,
              options: resolutionCompressionOptions,
            },
          }),
          export: {
            operation: "export/url",
            input: [
              resolutionCompressionOptions
                ? "compressResolution"
                : sizeCompressionOptions
                  ? "compressSize"
                  : "import",
            ],
          },
        },
      };

      const { status, data } = await this.client.post(`/process/jobs`, body);

      const rawData = data as {
        id: string;
        status: FreeConvertJobStatus;
        startedAt: string;
        createdAt: Date;
        updatedAt: string;
        tasks: {
          name: string;
          operation: string;
          result: Object;
        }[];
      };

      const job = {
        id: rawData.id,
        status: rawData.status,
        startedAt: new Date(rawData.startedAt),
        createdAt: new Date(rawData.createdAt),
        updatedAt: new Date(rawData.updatedAt),
      };

      return createSuccessResponse(job, status);
    } catch (error) {
      console.error("Error Initializing Job", error);
      return catchAxiosError(error, () => "Error Initializing Job");
    }
  };

  createCompressImageJob = async ({
    image,
    compression,
  }: {
    image: {
      url: string;
      filename: string;
      mimetype: string;
    };
    compression: {
      type: "size";
      maxFileSizeMB: number;
    };
  }): Promise<
    Response<{
      id: string;
      status: FreeConvertJobStatus;
      startedAt: Date;
      createdAt: Date;
      updatedAt: Date;
    }>
  > => {
    try {
      let format = image.mimetype.split("/")[1];

      let importTask: Object = {
        operation: "import/url",
        url: image.url,
        filename: image.filename,
      };

      let compressionOptions = {};

      let outputFormat = format;
      if (format === "png") {
        compressionOptions = {
          png_compression_quality: 80,
          compress_png_resize_output: "keep_original",
        };
      } else {
        compressionOptions = {
          jpeg_compression_method: "by_max_size",
          jpeg_compress_reduce_chroma_sampling: false,
          jpeg_compress_max_file_size: compression.maxFileSizeMB * 1024,
          jpeg_compress_use_grayscale: false,
        };
        outputFormat = "jpg";
      }

      const body = {
        tasks: {
          import: importTask,
          compress: {
            operation: "compress",
            input: "import",
            input_format: format,
            output_format: outputFormat,
            options: compressionOptions,
          },
          export: {
            operation: "export/url",
            input: ["compress"],
          },
        },
      };

      const { status, data } = await this.client.post(`/process/jobs`, body);

      const rawData = data as {
        id: string;
        status: FreeConvertJobStatus;
        startedAt: string;
        createdAt: Date;
        updatedAt: string;
        tasks: {
          name: string;
          operation: string;
          result: Object;
        }[];
      };

      const job = {
        id: rawData.id,
        status: rawData.status,
        startedAt: new Date(rawData.startedAt),
        createdAt: new Date(rawData.createdAt),
        updatedAt: new Date(rawData.updatedAt),
      };

      return createSuccessResponse(job, status);
    } catch (error) {
      console.error("Error Initializing Job", error);
      return catchAxiosError(error, () => "Error Initializing Job");
    }
  };

  getJob = async (
    jobID: string
  ): Promise<
    Response<{
      id: string;
      status: FreeConvertJobStatus;
      startedAt: Date;
      endedAt: Date;
      downloadUrl: string;
      expiresAt: Date;
      createdAt: Date;
      updatedAt: Date;
      size: number;
      width: number | null;
    }>
  > => {
    try {
      const { status, data } = await this.client.get(`/process/jobs/${jobID}`);

      const rawData = data as {
        id: string;
        status: FreeConvertJobStatus;
        startedAt: string;
        createdAt: Date;
        updatedAt: string;
        endedAt: string;
        tasks: {
          operation: "compress" | "export/url";
          payload: {
            options: {
              compress_video: "by_resolution";
              video_custom_width: number;
            };
          };
          result: {
            url: string;
            size: number;
          };
          expiresAt: string;
        }[];
      };

      const exportTask = rawData.tasks.find(
        (task) => task.operation === "export/url"
      );

      const resolutionCompressionTask = rawData.tasks.find(
        (task) =>
          task.operation === "compress" &&
          task.payload.options.compress_video === "by_resolution"
      );

      if (!exportTask) {
        return createErrorResponse(
          ["No Export Task"],
          StatusCodes.UNPROCESSABLE_ENTITY
        );
      }

      const job = {
        id: rawData.id,
        status: rawData.status,
        startedAt: new Date(rawData.startedAt),
        createdAt: new Date(rawData.createdAt),
        updatedAt: new Date(rawData.updatedAt),
        expiresAt: new Date(exportTask.expiresAt),
        endedAt: new Date(rawData.endedAt),
        downloadUrl: exportTask.result.url,
        size: exportTask.result.size,
        width:
          resolutionCompressionTask?.payload.options.video_custom_width || null,
      };

      return createSuccessResponse(job, status);
    } catch (error) {
      console.error("Error Initializing Job", error);
      return catchAxiosError(error, () => "Error Initializing Job");
    }
  };
}

export default FreeConvert;
