import { useEffect, useState } from "react";

import { v4 as uuidv4 } from "uuid";

import {
  ArrowUpRightIcon,
  EyeIcon,
  EyeSlashIcon,
} from "@heroicons/react/24/outline";
import {
  Channel,
  ScheduledPostStatus,
  WebflowPreviewType,
} from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import assert from "assert";
import { DateTime } from "luxon";
import { toast } from "sonner";
import { Tooltip } from "~/components";
import AutosizeInput from "~/components/AutosizeInput";
import Label from "~/components/Label";
import Select from "~/components/Select";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import Tabs from "~/components/ui/Tabs";
import { useWorkspace } from "~/providers";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import { cn, handleTrpcError, openConfirmationModal, trpc } from "~/utils";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import createRoom<PERSON> from "~/utils/liveblocks/createRoomID";
import WebflowEditorWrapper from "../../tiptap/WebflowEditor";
import EditorActionsBar from "../EditorActionsBar";
import { PostOption, PostOptions } from "../PostOptions";
import PostPreviewContainer from "../PostPreviewContainer";
import WebflowFieldInput, { WebflowField } from "./WebflowFieldInput";

type ScheduledPost = {
  status: ScheduledPostStatus;
  channel: Channel;
  error: string | null;
  publishAt: Date;
  integrationAccountName: string;
};

export type WebflowContent = {
  id: string;
  title: string | null;
  body: string | null;
  bodyHTML: string | null;
  editorState: JSONContent | null;
  fields: WebflowField[];
  uploading?: boolean;
};

export type WebflowIntegration = {
  id: string;
  siteName: string;
  collectionName: string;
};

type Props = {
  post: {
    id: string;
    title: string;
    campaignID: string | null;
    workspaceID: string;
    channels: Channel[];
    webflowAssets: { [id: string]: AssetType[] };
    webflowContent: WebflowContent | null;
    scheduledPosts: ScheduledPost[];
    webflowIntegration: WebflowIntegration | null;
  } & PostOrIdea;
  isReadOnly?: boolean;
};

const WebflowPost = ({ post, isReadOnly }: Props) => {
  const { currentWorkspace } = useWorkspace();
  const trpcUtils = trpc.useUtils();

  const [integration, setIntegration] = useState<WebflowIntegration | null>(
    post.webflowIntegration
  );
  const [content, setContent] = useState<WebflowContent>(
    post.webflowContent || {
      id: uuidv4(),
      title: null,
      body: null,
      bodyHTML: null,
      fields: [],
      editorState: null,
    }
  );
  const [assets, setAssets] = useState<{ [id: string]: AssetType[] }>(
    post.webflowAssets
  );
  const [previewType, setPreviewType] = useState<WebflowPreviewType | null>(
    null
  );
  const [preview, setPreivew] = useState<{
    itemID: string;
    url: string;
    postLiveUntil: Date;
  } | null>(null);

  const updatePostMutation = trpc.post.update.useMutation();
  const defaultPreviewTypeQuery =
    trpc.webflowContent.getDefaultPreviewType.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });

  trpc.webflowContent.get.useQuery(
    {
      postID: post.id,
    },
    {
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    }
  );
  const upsertWebflowContent = trpc.webflowContent.upsert.useMutation({
    onSuccess: () => {
      trpcUtils.post.get.invalidate({
        id: post.id,
      });
    },
  });
  const updateFieldMutation = trpc.webflowContent.updateFieldValue.useMutation({
    onError: (error) => {
      handleTrpcError({ title: "Error Saving Field", error });
    },
  });
  const refreshWebflowFieldsMutation =
    trpc.webflowContent.refreshFields.useMutation({
      onSuccess: (data) => {
        setContent((content) => ({
          ...content,
          fields: data.fields.map((field) => {
            return field as WebflowField;
          }),
        }));
      },
    });
  const previewPostMutation = trpc.webflowContent.previewPost.useMutation();
  const unpublishPreviewPostMutation =
    trpc.webflowContent.unpublishPreviewedPost.useMutation();

  const connectedAccountsQuery =
    trpc.integration.webflow.getConnectedSites.useQuery(undefined, {
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    });

  const connectedAccountData = connectedAccountsQuery.data;
  useEffect(() => {
    const setDefaultAccount = () => {
      if (integration == null && connectedAccountData?.sites.length) {
        const newSite = connectedAccountData.sites[0];
        setIntegration(newSite);
        updatePostMutation.mutate(
          {
            id: post.id,
            webflowIntegrationID: newSite.id,
          },
          {
            onSuccess: () => {
              refreshWebflowFieldsMutation.mutate({
                id: content.id,
              });
              trpcUtils.post.getIntegrations.refetch({
                id: post.id,
              });
            },
          }
        );
      }
    };
    setDefaultAccount();
  }, [connectedAccountData]);

  const workspaceAccounts =
    connectedAccountsQuery.data?.sites || (integration ? [integration] : []);

  useEffect(() => {
    if (integration) {
      updatePostMutation.mutate(
        {
          id: post.id,
          webflowIntegrationID: integration.id,
        },
        {
          onSuccess: () => {
            trpcUtils.post.getIntegrations.refetch({
              id: post.id,
            });
          },
        }
      );
    }
  }, [integration]);

  useEffect(() => {
    if (content && (!content.fields || content.fields.length === 0)) {
      setContent({
        ...content,
        fields: [],
      });
    }

    if (content && content.id) {
      upsertWebflowContent.mutate({
        id: content.id,
        title: content.title,
        body: content.body,
        bodyHTML: content.bodyHTML,
        editorState: content.editorState,
        postID: post.id,
      });
    }
  }, [content?.id]);

  return (
    <div>
      <PostPreviewContainer
        channel="Webflow"
        post={post}
        connectedAccount={
          integration && {
            integrationID: integration.id,
            name: `${integration.siteName} - ${integration.collectionName}`,
          }
        }
        accountOptions={workspaceAccounts.map((workspaceAccount) => ({
          integrationID: workspaceAccount.id,
          name: `${workspaceAccount.siteName} - ${workspaceAccount.collectionName}`,
        }))}
        setConnectedAccountID={(integrationID) => {
          openConfirmationModal({
            title: "Switch CMS account?",
            body: "Switching accounts will clear any of the values in the current Custom CMS Fields tab. This action cannot be undone.",
            primaryButton: {
              label: "Confirm",
              onClick: () => {
                const newSite = workspaceAccounts.find(
                  (option) => option.id === integrationID
                );
                setIntegration(newSite!);
              },
            },
          });
        }}
        additionalContent={
          <div
            className={cn({
              hidden:
                isReadOnly ||
                !!post.scheduledPosts.find(
                  (scheduledPost) =>
                    scheduledPost.channel === "Webflow" &&
                    scheduledPost.status === "Completed"
                ),
            })}
          >
            {preview == null || preview.postLiveUntil < new Date() ? (
              <div>
                <Button
                  size="sm"
                  variant="ghost"
                  isLoading={previewPostMutation.isPending}
                  onClick={() => {
                    setPreviewType(
                      defaultPreviewTypeQuery.data?.previewType || "Live"
                    );
                  }}
                >
                  <EyeIcon className="h-4 w-4" />
                  <div className="mt-0.5">Preview Post</div>
                </Button>
                <Credenza.Root
                  open={previewType != null}
                  onOpenChange={() => setPreviewType(null)}
                >
                  <Credenza.Header>
                    <Credenza.Title>Confirm Webflow Preview</Credenza.Title>
                    <Credenza.Description>
                      Select how you'd like to preview this post on Webflow
                    </Credenza.Description>
                  </Credenza.Header>
                  <Credenza.Body className="font-body-sm text-medium-dark-gray">
                    <div className="my-4">
                      <Select
                        options={[
                          {
                            label: "Preview on the Live Site",
                            value: "Live",
                          },
                          {
                            label: "Preview in the Webflow Designer",
                            value: "Designer",
                          },
                        ]}
                        value={previewType}
                        onChange={setPreviewType}
                      />
                    </div>
                    {previewType === "Live" ? (
                      <span>
                        In order to preview this post, Assembly will set this
                        blog post live on your Webflow site for{" "}
                        <span className="text-secondary">3 minutes</span>.
                        Afterwards, we will automatically delete the post from
                        the CMS, which will unpublish it from your site.
                        <br />
                        <br />
                        Note: This will only publish the CMS item to the live
                        website. Other ongoing design changes made in Webflow
                        will not be published.
                      </span>
                    ) : (
                      <span>
                        This will not publish the blog post to the live site.
                        Assembly will add this blog post to your Webflow CMS as
                        a Draft for{" "}
                        <span className="text-secondary">3 minutes</span>. You
                        can then preview the post in Webflow (Please note you'll
                        need the proper permissions on Webflow as well to view
                        this preview).
                      </span>
                    )}
                  </Credenza.Body>
                  <Credenza.Footer>
                    <Button
                      variant="secondary"
                      onClick={() => setPreviewType(null)}
                      disabled={previewPostMutation.isPending}
                    >
                      Cancel
                    </Button>
                    <Button
                      isLoading={previewPostMutation.isPending}
                      onClick={() => {
                        assert(previewType);
                        previewPostMutation.mutate(
                          {
                            id: content.id,
                            previewType,
                          },
                          {
                            onError: (error) => {
                              handleTrpcError({
                                title: "Error Previewing Post",
                                error,
                              });
                            },
                            onSuccess: (data) => {
                              window.open(data.url, "_blank");
                              setPreivew({
                                itemID: data.itemID,
                                url: data.url,
                                postLiveUntil: new Date(data.postLiveUntil),
                              });
                              setPreviewType(null);
                            },
                          }
                        );
                      }}
                    >
                      Confirm
                    </Button>
                  </Credenza.Footer>
                </Credenza.Root>
              </div>
            ) : (
              <div className="flex items-center gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  disabled={unpublishPreviewPostMutation.isPending}
                  onClick={() => {
                    window.open(preview.url, "_blank");
                  }}
                >
                  <ArrowUpRightIcon className="h-4 w-4" />
                  <div className="mt-0.5">View Post Preview</div>
                </Button>
                <Tooltip
                  value={
                    <span>
                      {previewType === "Live"
                        ? "Click to unpublish preview now. Preview will also automatically unpublish at"
                        : "Click to remove item from CMS now. Item will also automatically be removed at"}{" "}
                      <span className="text-secondary">
                        {DateTime.fromJSDate(preview.postLiveUntil).toFormat(
                          "h:mma"
                        )}
                      </span>
                      .
                    </span>
                  }
                >
                  <Button
                    size="sm"
                    variant="ghost"
                    isLoading={unpublishPreviewPostMutation.isPending}
                    onClick={() => {
                      unpublishPreviewPostMutation.mutate(
                        {
                          id: content.id,
                          itemID: preview.itemID,
                        },
                        {
                          onSuccess: () => {
                            setPreivew(null);
                            if (previewType === "Live") {
                              toast.success("Unpublished from Webflow", {
                                description:
                                  "This post preview has been removed from Webflow and item removed from CMS",
                              });
                            } else {
                              toast.success("Removed from CMS", {
                                description:
                                  "This post preview has been removed from CMS",
                              });
                            }
                          },
                          onError: (error) => {
                            handleTrpcError({
                              title: "Error Unpublishing Preview",
                              error,
                            });
                          },
                        }
                      );
                    }}
                  >
                    <EyeSlashIcon className="h-4 w-4" />
                    <div className="mt-0.5">
                      {previewType === "Live"
                        ? "Unpublish Preview"
                        : "Remove from CMS"}
                    </div>
                  </Button>
                </Tooltip>
              </div>
            )}
          </div>
        }
        isReadOnly={isReadOnly}
      />
      <Tabs.Root defaultValue="post_content">
        <Tabs.List>
          <Tabs.Trigger value="post_content">Post Content</Tabs.Trigger>
          <Tabs.Trigger value="custom_cms_fields">
            Custom CMS Fields
          </Tabs.Trigger>
        </Tabs.List>
        <Tabs.Content value="post_content">
          <div className="group pb-8">
            <div className="mb-7">
              <Label value="Blog Title">
                <AutosizeInput
                  value={content?.title || ""}
                  placeholder="Enter a title for your blog post here"
                  size="md"
                  isReadOnly={isReadOnly}
                  onChange={(title) => {
                    setContent({
                      ...content,
                      title,
                    });
                  }}
                  onBlur={() => {
                    upsertWebflowContent.mutate({
                      id: content.id,
                      title: content.title,
                      body: content.body,
                      bodyHTML: content.bodyHTML,
                      editorState: content.editorState,
                      postID: post.id,
                    });
                  }}
                />
              </Label>
            </div>
            <WebflowEditorWrapper
              key={content.id}
              contentID={content.id}
              title="Blog Copy"
              placeholder="Start drafting your blog post here"
              initialValue={content?.editorState}
              isReadOnly={isReadOnly}
              onSave={(editorState) => {
                assert(content);
                setContent({
                  ...content,
                  editorState,
                });

                upsertWebflowContent.mutate({
                  id: content.id,
                  title: content.title,
                  body: content.body,
                  bodyHTML: content.bodyHTML,
                  editorState,
                  postID: post.id,
                });
              }}
              onUpdateContent={(body, bodyHTML) => {
                setContent({
                  ...content,
                  body,
                  bodyHTML,
                });
              }}
              roomID={createRoomID({
                workspaceID: post.workspaceID,
                contentType: "post",
                postID: post.id,
                roomContentID: content.id,
              })}
            />
            <EditorActionsBar
              post={post}
              channel="Slack"
              visible={true}
              isReadOnly={isReadOnly}
              characterCount={{
                current: content.body?.length || 0,
                max: 40000,
              }}
            />
          </div>
        </Tabs.Content>
        <Tabs.Content value="custom_cms_fields">
          <div>
            <div className="pb-6">
              <div className="font-body pb-1 font-medium">
                Custom CMS Fields
              </div>
              <div className="font-body-sm text-medium-dark-gray pb-2">
                Set the values for any CMS fields. If you made changes to your
                CMS that aren't reflected here,{" "}
                <Button
                  variant="link"
                  size="sm"
                  onClick={() => {
                    toast.promise(
                      refreshWebflowFieldsMutation.mutateAsync({
                        id: content.id,
                      }),
                      {
                        loading: "Refreshing Webflow Fields",
                        success: () => {
                          return {
                            message: "Webflow Fields Refreshed",
                            description:
                              "Successfully refreshed Webflow fields",
                          };
                        },
                        error: (error) => {
                          return {
                            message: "Failed to Refresh Webflow Fields",
                            description: error.message as string,
                          };
                        },
                      }
                    );
                  }}
                >
                  click here
                </Button>{" "}
                to refresh your Webflow CMS connection.{" "}
              </div>
            </div>
            <PostOptions>
              {content.fields
                .sort((fieldA, fieldB) => {
                  return fieldA.position - fieldB.position;
                })
                .map((field) => (
                  <PostOption
                    key={field.id}
                    title={field.displayName}
                    description={field.helpText}
                    isRequired={field.isRequired}
                  >
                    <WebflowFieldInput
                      field={field}
                      blogTitle={content.title}
                      assets={assets[field.id] || []}
                      isReadOnly={isReadOnly}
                      storagePath={`${currentWorkspace?.id}/${post.id}/${CHANNEL_ICON_MAP["Webflow"].slug}/${content.id}/${field.id}`}
                      onChange={(value) => {
                        updateFieldMutation.mutate({
                          fieldID: field.id,
                          value,
                        });
                        setContent((content) => ({
                          ...content,
                          fields: content.fields.map((f) =>
                            f.id === field.id ? { ...f, value } : f
                          ),
                        }));
                      }}
                      onUpdateAssets={(newAssets) => {
                        setAssets((prevAssets) => ({
                          ...prevAssets,
                          [field.id]: newAssets,
                        }));
                      }}
                    />
                  </PostOption>
                ))}
            </PostOptions>
          </div>
        </Tabs.Content>
      </Tabs.Root>
    </div>
  );
};

export default WebflowPost;
