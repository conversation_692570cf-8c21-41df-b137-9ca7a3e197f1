import React, { <PERSON>E<PERSON>, <PERSON>boardEvent, MouseEvent } from "react";

import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import classnames from "classnames";

export type Kind = "underline" | "outline";
export type InputType = "text" | "password" | "email" | "number";
export type Size = "sm" | "2xl";

type TextInputProps = {
  /** Note that this must be a string, and cannot be null or undefined. */
  value: string;
  /** Called when the value of the textinput changes */
  onChange: (value: string) => void;
  /** The HTML type of this text input, see https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input for a complete list */
  type?: InputType;
  /** The UX of the button */
  kind?: Kind;
  /** The size of the text input */
  size?: Size;
  /** The style of the text */
  textStyle?: "font-body-sm" | "font-eyebrow";
  /** The placeholder text that will be displayed when the input is empty */
  placeholder?: string;
  /** Called when the input is clicked */
  onClick?: (e: MouseEvent<HTMLInputElement>) => void;
  /** Called when the input is focused */
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void;
  /** Called when the input is blurred */
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void;
  /** Called when a user presses enter */
  onEnterPress?: (e: KeyboardEvent<HTMLInputElement>) => void;
  /** Called when a user presses escape */
  onEscPress?: () => void;
  /** Whether the element should be auto-focused */
  autoFocus?: boolean;
  /** Whether there's a magnifying glass at the beginning of the input */
  showSearchIcon?: boolean;
  /** Whether the input is disabled or not */
  disabled?: boolean;
  /** Whether the input is readonly or not */
  isReadOnly?: boolean;
  /** Whether the input is invalid or not */
  isInvalid?: boolean;
  /** Suffix that will appear within and at the end of the input field */
  suffix?: string;
  /** Triggered when a user clicks down */
  onDownPress?: () => void;
  /** Triggered when a user clicks up */
  onUpPress?: () => void;
};

const TextInput = React.forwardRef<HTMLInputElement, TextInputProps>(
  (
    {
      type = "text",
      value,
      onChange,
      placeholder,
      kind = "underline",
      textStyle = "font-body-sm",
      size = "sm",
      autoFocus,
      showSearchIcon,
      onClick,
      onFocus,
      onBlur,
      onEnterPress,
      onEscPress,
      disabled = false,
      isReadOnly = false,
      isInvalid,
      suffix,
      onDownPress,
      onUpPress,
    },
    ref
  ) => {
    const className = classnames(
      "pb-1 pl-0.5 mt-1 w-full text-black outline-hidden bg-transparent focus:border-secondary",
      {
        "font-body-sm": textStyle === "font-body-sm",
        "font-eyebrow": textStyle === "font-eyebrow",
        /** Disabled Styles */
        "cursor-not-allowed": disabled,
        /** isReadOnly Styles */
        "cursor-default border-none shadow-none": isReadOnly,
        "h-6": size === "sm" && kind !== "underline",
        "h-14": size === "2xl",
      }
    );

    return (
      <div
        className={classnames(
          "flex w-full items-center gap-1 transition-colors",
          {
            /** Size */
            "rounded-md pl-3": size === "sm" && kind !== "underline",
            "rounded-[10px] pl-5": size === "2xl",
            /** Underline Styles */
            "focus-within:border-secondary border-b": kind === "underline",
            "border-light-gray": !isInvalid,
            "border-danger": isInvalid,
            /** Outline Styles */
            "border py-px": kind === "outline",
            "focus-within:border-secondary hover:border-secondary":
              kind === "outline" && !isInvalid && !isReadOnly,
            "cursor-default": isReadOnly,
          }
        )}
      >
        {showSearchIcon && (
          <MagnifyingGlassIcon className="text-medium-gray h-4 w-4" />
        )}
        <input
          ref={ref}
          type={type}
          value={value || ""}
          autoFocus={autoFocus}
          step={type === "number" ? "any" : undefined}
          onChange={(e) => onChange(e.target.value)}
          className={className}
          onKeyUp={(event) => {
            if (event.key === "Enter") {
              onEnterPress && onEnterPress(event);
            }
            if (event.key === "ArrowDown") {
              onDownPress && onDownPress();
            }
            if (event.key === "ArrowUp") {
              onUpPress && onUpPress();
            }
            if (event.key === "Escape") {
              onEscPress && onEscPress();
            }
          }}
          onClick={onClick}
          onFocus={onFocus}
          onBlur={onBlur}
          placeholder={placeholder}
          readOnly={isReadOnly}
          disabled={disabled}
        />
        {suffix && (
          <div className="pointer-events-none mt-1.5 mr-2 flex h-6 items-center justify-center rounded-md px-2 text-sm">
            {suffix}
          </div>
        )}
      </div>
    );
  }
);

TextInput.displayName = "TextInput";

export default TextInput;
