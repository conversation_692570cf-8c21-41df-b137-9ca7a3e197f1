import { useEffect, useState } from "react";

import { PostStatus } from "@prisma/client";
import { useRouter } from "next/router";
import { toast } from "sonner";
import AddNewButton from "~/components/AddNewButton";
import Combobox from "~/components/Combobox";
import { POST_STATUSES } from "~/types";
import {
  capitalizeWords,
  handleTrpcError,
  openConfirmationModal,
  trpc,
} from "~/utils";
import StatusBadge from "./StatusBadge";

type Props = {
  post: {
    id: string;
    status: PostStatus;
    isIdea: boolean;
  };
  size?: "sm" | "md";
  isReadOnly?: boolean;
  showHotKey?: boolean;
};

const StatusButton = ({
  post,
  size,
  isReadOnly = false,
  showHotKey = false,
}: Props) => {
  const trpcUtils = trpc.useUtils();
  const router = useRouter();

  const [status, setStatus] = useState<PostStatus>(post.status);
  const updatePostMutation = trpc.post.update.useMutation({
    onSuccess: () => {
      trpcUtils.post.getMany.invalidate();
      trpcUtils.subscriber.getForContent.refetch({ id: post.id, type: "post" });
    },
    onError: (error) => {
      handleTrpcError({ title: "Error Updating Status", error });
    },
  });
  const convertToIdeaMutation = trpc.post.convertToIdea.useMutation({
    onSuccess: () => {
      trpcUtils.post.getMany.invalidate();
      toast.success("Post Moved to Ideas", {
        description: "Successfully moved to Ideas",
      });
      router.push(`/ideas/${post.id}`);
      trpcUtils.subscriber.getForContent.refetch({ id: post.id, type: "post" });
    },
    onError: (error) => {
      handleTrpcError({ title: "Error Updating Post", error });
    },
  });

  useEffect(() => {
    setStatus(post.status);
  }, [post.status]);

  const updatePostStatus = (status: PostStatus | "Idea") => {
    if (status === "Idea") {
      openConfirmationModal({
        title: "Move post to Ideas?",
        body: `Changing the status to "Idea" will move your post from the calendar to the Ideas tab`,
        primaryButton: {
          label: "Confirm",
          onClick: () => {
            convertToIdeaMutation.mutate({
              id: post.id,
            });
          },
        },
      });
    } else {
      setStatus(status);
      updatePostMutation.mutate({
        id: post.id,
        status,
      });
      const currentPostData = trpcUtils.post.get.getData({ id: post.id });
      if (currentPostData) {
        trpcUtils.post.get.setData(
          { id: post.id },
          {
            post: {
              ...currentPostData.post,
              status,
            },
            isReadOnly,
          }
        );
      }
    }
  };

  if (post.isIdea) {
    return (
      <StatusBadge
        status="Idea"
        kind="badge"
        size={size}
        showHoverState={false}
        tooltipText={
          !isReadOnly
            ? "Ideas cannot change status until they're added to the calendar."
            : undefined
        }
        tooltipSide="bottom"
      />
    );
  }

  const options = POST_STATUSES.map((status) => {
    return {
      value: status as string,
      label: (
        <div className="flex items-center gap-1">
          <StatusBadge kind="icon" showHoverState={true} status={status} />
          <div className="font-eyebrow font-normal whitespace-nowrap">
            {capitalizeWords(status)}
          </div>
        </div>
      ),
      sectionName:
        status === "Scheduled" || status === "Posted"
          ? "Scheduled"
          : "Statuses",
      component: (
        <StatusBadge kind="badge" showHoverState={true} status={status} />
      ),
    };
  });
  options.unshift({
    value: "Idea",
    label: (
      <div className="flex items-center gap-1">
        <StatusBadge kind="icon" showHoverState={true} status="Idea" />
        <div className="font-eyebrow font-normal whitespace-nowrap">Idea</div>
      </div>
    ),
    sectionName: "Ideas",
    component: <StatusBadge kind="badge" showHoverState={true} status="Idea" />,
  });

  if (isReadOnly && !post.isIdea) {
    return <StatusBadge status={status} kind="badge" size={size} />;
  }

  return (
    <Combobox
      isSearchable={true}
      hideSelectedOption={true}
      placeholder={<AddNewButton label="Status" isUppercase={true} />}
      value={status}
      onChange={updatePostStatus}
      options={options}
      showSectionNames={false}
      hotKeyOptions={
        showHotKey
          ? {
              keys: "S",
              label: "Set Status",
            }
          : undefined
      }
    />
  );
};

export default StatusButton;
