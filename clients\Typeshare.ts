import catchAxiosError from "apiUtils/catchAxiosError";
import axios, { AxiosInstance } from "axios";
import { createSuccessResponse, Response } from "./Response";

class Typeshare {
  BASE_URL = "https://typeshare.co/api";

  client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: this.BASE_URL,
      headers: {
        Authorization: `Bearer ${process.env.TYPESHARE_BEARER_TOKEN}`,
      },
    });
  }

  getTwitterAccounts = async (
    username: string
  ): Promise<
    Response<
      {
        id: string;
        verified: boolean;
        name: string;
        username: string;
        profileImageUrl: string;
        location: string;
      }[]
    >
  > => {
    try {
      const { status, data } = await this.client.post(
        `/thread-editor/suggested-users`,
        {
          query: username,
        }
      );

      const rawUsers: {
        id_str: string;
        verified: boolean;
        profile_image_url_https: string;
        screen_name: string;
        location: string;
        name: string;
      }[] = data.data.users;

      const users = rawUsers.map((user) => ({
        // id is a number and is rounded so it fails to be parsed by the client
        id: user.id_str,
        profileImageUrl: user.profile_image_url_https,
        username: user.screen_name,
        location: user.location,
        name: user.name,
        verified: user.verified,
      }));

      return createSuccessResponse(users, status);
    } catch (error) {
      return catchAxiosError(error, () => "Error Querying Users");
    }
  };
}

export default Typeshare;
