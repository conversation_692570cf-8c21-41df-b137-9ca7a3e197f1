import { TRPCError } from "@trpc/server";
import catchZenstackDeleteError from "apiUtils/catchZenstackDeleteError";
import removeSubscriptionItem from "apiUtils/stripe/removeSubscriptionItem";
import { db } from "~/clients";
import posthog from "~/clients/posthog";
import POSTHOG_EVENTS from "~/constants/posthogEvents";

const disconnectDiscordAccount = async ({
  integrationID,
  workspaceID,
  userID,
}: {
  integrationID: string;
  workspaceID: string;
  userID: string;
}) => {
  const discordIntegration = await db.discordWebhook.findFirst({
    where: {
      id: integrationID,
      workspaceID,
    },
    select: {
      id: true,
      channel: true,
      username: true,
      workspace: {
        select: {
          id: true,
          companyID: true,
        },
      },
    },
  });

  if (!discordIntegration) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "No integration found",
    });
  } else {
    const workspace = discordIntegration.workspace;
    const postsToUpdate = await db.post.findMany({
      where: {
        discordWebhookID: discordIntegration.id,
        scheduledPosts: {
          some: {
            status: "Scheduled",
            channel: "Discord",
            publishAt: {
              gt: new Date(),
            },
          },
        },
      },
      select: {
        id: true,
      },
    });
    const postIDs = postsToUpdate.map((post) => post.id);

    // Cancel all scheduled posts for just discord
    await db.scheduledPost.updateMany({
      where: {
        channel: "Discord",
        status: "Scheduled",
        publishAt: {
          gt: new Date(),
        },
        post: {
          id: {
            in: postIDs,
          },
        },
      },
      data: {
        status: "Cancelled",
      },
    });

    const updatedPosts = await db.post.findMany({
      where: {
        id: {
          in: postIDs,
        },
      },
      select: {
        id: true,
        _count: {
          select: {
            scheduledPosts: {
              where: {
                status: "Scheduled",
              },
            },
          },
        },
      },
    });

    const postsWithoutScheduledPosts = updatedPosts.filter(
      (post) => post._count.scheduledPosts === 0
    );
    await db.post.updateMany({
      where: {
        id: {
          in: postsWithoutScheduledPosts.map((post) => post.id),
        },
      },
      data: {
        status: "Finalized",
      },
    });

    const postDefaults = await db.postDefault.findFirst({
      where: {
        workspaceID: workspaceID,
      },
      select: {
        discordWebhookID: true,
        workspace: {
          select: {
            discordWebhooks: {
              where: {
                id: {
                  not: discordIntegration.id,
                },
              },
              select: {
                id: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    if (postDefaults?.discordWebhookID === discordIntegration.id) {
      const oldestDiscordWebhook = postDefaults.workspace.discordWebhooks.sort(
        (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
      )[0];

      await db.postDefault.update({
        where: {
          workspaceID: workspaceID,
        },
        data: {
          discordWebhookID:
            oldestDiscordWebhook != null ? oldestDiscordWebhook.id : null,
        },
      });
    }

    await catchZenstackDeleteError(async () => {
      await db.discordWebhook.update({
        where: {
          id: discordIntegration.id,
        },
        data: {
          deletedTimeStamp: Math.floor(new Date().getTime() / 1000),
          deletedByID: userID,
        },
      });
    });

    await removeSubscriptionItem({
      workspaceID,
      itemName: "ADDITIONAL_SOCIAL_PROFILE",
      quantity: 1,
    });

    posthog.capture({
      distinctId: userID,
      event: POSTHOG_EVENTS.socialAccount.disconnected,
      properties: {
        channel: "Discord",
      },
      groups: {
        workspace: workspaceID,
        company: workspace.companyID,
      },
    });

    return discordIntegration;
  }
};

export default disconnectDiscordAccount;
