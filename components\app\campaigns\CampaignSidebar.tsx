import { useState } from "react";

import { MinusSmallIcon } from "@heroicons/react/24/outline";
import { DatePicker, Divider } from "~/components";
import { dateFromUTC, getDateUTC, trpc } from "~/utils";
import Approvals, { Approval } from "../Approvals";
import LabelSelect, { Label } from "../LabelSelect";
import { Subscriber } from "../posts/Subscribers";

type Props = {
  campaign: {
    id: string;
    name: string;
    startDate: Date;
    endDate: Date;
    subscribers: Subscriber[];
    approvals: Approval[];
    labels: {
      id: string;
      name: string;
      color: string;
      backgroundColor: string;
      createdAt: Date;
    }[];
  };
  isReadOnly: boolean;
};

const CampaignSidebar = ({ campaign, isReadOnly }: Props) => {
  const trpcUtils = trpc.useUtils();

  const [startDate, setStartDate] = useState<Date>(
    dateFromUTC(campaign.startDate)
  );
  const [endDate, setEndDate] = useState<Date>(dateFromUTC(campaign.endDate));
  const [labels, setLabels] = useState<Label[]>(campaign.labels);

  const updateCampaignMutation = trpc.campaign.update.useMutation({
    onSuccess: () => {
      trpcUtils.campaign.get.invalidate({
        id: campaign.id,
      });
      trpcUtils.campaign.getAll.refetch();
    },
  });

  const updateLabelsMutation = trpc.label.updateLabels.useMutation();

  const labelQuery = trpc.label.getAll.useQuery();

  const updateLabels = (labels: Label[]) => {
    updateLabelsMutation.mutate({
      contentID: campaign.id,
      contentType: "campaign",
      labels,
    });
    setLabels(labels);
  };

  const attributes = [
    {
      label: "Dates",
      value: (
        <div className="flex items-center gap-1">
          <div className="w-[70px]">
            <DatePicker
              value={startDate}
              onChange={(startDate) => {
                const startDateUTC = getDateUTC(startDate);
                updateCampaignMutation.mutate({
                  id: campaign.id,
                  startDate: startDateUTC,
                });
                setStartDate(startDate);
              }}
              isDisabled={isReadOnly}
              showBorder={false}
            />
          </div>
          <MinusSmallIcon className="text-medium-dark-gray h-4 w-4" />
          <div className="w-[70px]">
            <DatePicker
              value={endDate}
              onChange={(endDate) => {
                const endDateUTC = getDateUTC(endDate);
                updateCampaignMutation.mutate({
                  id: campaign.id,
                  endDate: endDateUTC,
                });
                setEndDate(endDate);
              }}
              isDisabled={isReadOnly}
              showBorder={false}
            />
          </div>
        </div>
      ),
    },
    {
      label: "Label",
      value: (
        <LabelSelect
          value={labels}
          onChange={updateLabels}
          isCreateable={true}
          // @ts-ignore seems to work?
          options={labelQuery.data?.labels || campaign.labels}
          isReadOnly={isReadOnly}
        />
      ),
    },
  ];

  return (
    <div className="w-[350px]">
      <dl className="space-y-0.5">
        {attributes.map((attribute) => {
          return (
            <div key={attribute.label} className="flex h-9 items-center gap-4">
              <dt className="font-eyebrow-sm text-medium-gray w-14">
                {attribute.label}:
              </dt>
              <dd className="col-span-2 font-medium">{attribute.value}</dd>
            </div>
          );
        })}
      </dl>
      <Divider padding="md" />
      <Approvals
        id={campaign.id}
        type="campaign"
        approvals={campaign.approvals}
        isReadOnly={isReadOnly}
      />
    </div>
  );
};

export default CampaignSidebar;
