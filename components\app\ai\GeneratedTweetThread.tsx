import { useEffect, useState } from "react";
import Button from "~/components/ui/Button";
import Credenza from "~/components/ui/Credenza";
import { GENERATED_TWEET_DELIMITER } from "~/constants";
import { formatTextAsTipTapEditorState } from "~/utils";
import { TweetContent } from "../posts/twitter/TweetThread";
import TweetThreadPreview from "../posts/twitter/TweetThreadPreview";
import TwitterEditor from "../tiptap/TwitterEditor";
import ContentContainer from "./ContentContainer";

type AccountOption = {
  integrationID: string;
  profileImageUrl: string | null;
  name: string;
  verified: boolean;
  verifiedType: string;
  isBasicVerifiedOverride: boolean;
  username: string;
};

type Props = {
  title: string | null;
  draftID?: string;
  content: string;
  navigateOnSuccess: boolean;
  isLoading: boolean;
  accountOptions: AccountOption[];
};

const GeneratedTweetThread = ({
  title,
  draftID,
  content,
  navigateOnSuccess,
  isLoading,
  accountOptions,
}: Props) => {
  const [editModalOpen, setEditModalOpen] = useState<boolean>(false);
  const [tweets, setTweets] = useState<
    (TweetContent & {
      assets: null;
    })[]
  >(
    content.split(GENERATED_TWEET_DELIMITER).map((tweet, i) => {
      const copy = tweet.replace(/^\s+|\s+$/g, "");
      return {
        id: i.toString(),
        copy,
        editorState: formatTextAsTipTapEditorState(copy),
        mediaTaggedUsernames: [],
        assets: null,
      };
    })
  );
  const [editedTweets, setEditedTweets] = useState<
    (TweetContent & {
      assets: null;
    })[]
  >([]);

  useEffect(() => {
    setTweets(
      content.split(GENERATED_TWEET_DELIMITER).map((tweet, i) => {
        const copy = tweet.replace(/^\s+|\s+$/g, "");
        return {
          id: i.toString(),
          copy,
          editorState: formatTextAsTipTapEditorState(copy),
          mediaTaggedUsernames: [],
          assets: null,
        };
      })
    );
  }, [content]);

  useEffect(() => {
    setEditedTweets(tweets);
  }, [tweets]);

  const [integrationID, setIntegrationID] = useState<string | null>(
    accountOptions.length > 0 ? accountOptions[0].integrationID : null
  );
  const twitterIntegration =
    accountOptions.find((option) => option.integrationID === integrationID) ||
    null;

  return (
    <>
      <ContentContainer
        title={title}
        channel="Twitter"
        draftID={draftID}
        isLoading={isLoading}
        content={tweets}
        navigateOnSuccess={navigateOnSuccess}
        connectedAccount={twitterIntegration}
        accountOptions={accountOptions.map((option) => ({
          value: option.integrationID,
          label: `@${option.username}`,
        }))}
        onEditClick={() => {
          setEditModalOpen(true);
        }}
        setConnectedAccountID={setIntegrationID}
      >
        <TweetThreadPreview
          tweets={tweets}
          connectedAccount={twitterIntegration}
          maxHeight="full"
        />
        <Credenza.Root open={editModalOpen} onOpenChange={setEditModalOpen}>
          <Credenza.Header>
            <Credenza.Title>Edit Copy</Credenza.Title>
            <Credenza.Description className="sr-only">
              Edit the copy for the post here
            </Credenza.Description>
          </Credenza.Header>
          <Credenza.Body>
            <div className="space-y-2">
              {editedTweets.map((tweet) => {
                const tweetID = tweet.id;
                return (
                  <TwitterEditor
                    key={tweet.id}
                    title="Tweet"
                    placeholder="Add copy for the post here"
                    initialValue={tweet.editorState}
                    onSave={(editorState) => {
                      const index = editedTweets.findIndex(
                        (tweet) => tweet.id === tweetID
                      );
                      const newTweets = [...editedTweets];
                      newTweets[index] = {
                        ...newTweets[index],
                        editorState,
                      };

                      setEditedTweets(newTweets);
                    }}
                    onUpdateContent={(copy) => {
                      const index = editedTweets.findIndex(
                        (tweet) => tweet.id === tweetID
                      );
                      const newTweets = [...editedTweets];
                      newTweets[index] = {
                        ...newTweets[index],
                        copy,
                      };

                      setEditedTweets(newTweets);
                    }}
                  />
                );
              })}
            </div>
            <Credenza.Footer>
              <Button
                variant="secondary"
                onClick={() => {
                  setEditedTweets(tweets);
                  setEditModalOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setTweets(editedTweets);
                  setEditModalOpen(false);
                }}
              >
                Save
              </Button>
            </Credenza.Footer>
          </Credenza.Body>
        </Credenza.Root>
      </ContentContainer>
    </>
  );
};

export default GeneratedTweetThread;
