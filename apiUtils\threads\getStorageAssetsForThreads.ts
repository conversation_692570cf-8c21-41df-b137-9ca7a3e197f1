import getAssetsForFilePaths from "apiUtils/getAssetsForFilePaths";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";

const getStorageAssetsForThreads = async (post: {
  id: string;
  workspaceID: string;
  threadsContent: {
    id: string;
  }[];
}): Promise<{ [id: string]: AssetType[] }> => {
  const filePaths = post.threadsContent.map((thread) => {
    return {
      id: thread.id,
      path: `${post.workspaceID}/${post.id}/${CHANNEL_ICON_MAP["Threads"].slug}/${thread.id}`,
    };
  });

  return await getAssetsForFilePaths(filePaths);
};

export default getStorageAssetsForThreads;
