import {
  EllipsisVerticalIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import { useDisclosure } from "@mantine/hooks";
import NumberFlow from "@number-flow/react";
import assert from "assert";
import copy from "copy-to-clipboard";
import sum from "lodash/sum";
import uniq from "lodash/uniq";
import uniqBy from "lodash/uniqBy";
import { DateTime } from "luxon";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import {
  Al<PERSON>,
  Badge,
  DatePicker,
  Divider,
  IconButton,
  Label,
  TextareaInput,
} from "~/components";
import CopyButton from "~/components/CopyButton";
import TextInput from "~/components/TextInput";
import UserAvatar from "~/components/UserAvatar";
import Accordion from "~/components/ui/Accordian";
import Button from "~/components/ui/Button";
import Drawer from "~/components/ui/Drawer";
import DropdownMenu from "~/components/ui/DropdownMenu";
import Skeleton from "~/components/ui/Skeleton";
import Tabs from "~/components/ui/Tabs";
import { useWindowTitle } from "~/hooks";
import { CHANNEL_ICON_MAP } from "~/types";
import {
  cn,
  getFullName,
  handleTrpcError,
  openConfirmationModal,
  trpc,
} from "~/utils";
import SettingsLayout from "../../settings/SettingsLayout";
import CompanyAdminTab from "./CompanyAdminTab";
import CompanySearch from "./CompanySearch";
import CreateWorkspaceModal from "./CreateWorkspaceModal";
import SubscriptionBadge from "./SubscriptionBadge";
import WorkspaceLimitsTable from "./WorkspaceLimitsTable";

import Switch from "~/components/ui/Switch";
import AccountChannelAvatar from "../../AccountChannelAvatar";
type Props = {
  id: string;
};

const CompanyPage = ({ id }: Props) => {
  const router = useRouter();
  const trpcUtils = trpc.useUtils();
  const [workspaceSearch, setWorkspaceSearch] = useState<string>("");
  const [hideInactiveWorkspaces, setHideInactiveWorkspaces] = useState(true);
  const [createWorkspaceModalOpen, createWorkspaceModalHandlers] =
    useDisclosure();
  const [subscriptionID, setSubscriptionID] = useState<string>("");
  const [notes, setNotes] = useState<string>("");

  const [workspaceNames, setWorkspaceNames] = useState<{
    [id: string]: string;
  }>({});

  const companyQuery = trpc.admin.company.get.useQuery({
    id,
  });
  const companyData = companyQuery.data?.company;
  useEffect(() => {
    if (companyData) {
      const subscription = companyData.subscription;
      if (subscription && subscriptionID === "") {
        setSubscriptionID(subscription.stripeSubscriptionID);
      }
      if (Object.keys(workspaceNames).length === 0) {
        const workspaceNames = companyData.workspaces.reduce(
          (acc, workspace) => {
            acc[workspace.id] = workspace.name;
            return acc;
          },
          {} as { [id: string]: string }
        );
        setWorkspaceNames(workspaceNames);
      }

      setNotes(companyData.notes || "");
    }
  }, [id, companyData]);

  const updateCompanyMutation = trpc.admin.company.update.useMutation({
    onSuccess: () => {
      toast.success("Company Notes Updated", {
        description: "Notes have been updated",
      });
    },
  });

  const company = companyQuery.data?.company;

  const disconnectIngrationMutation =
    trpc.admin.integration.disconnect.useMutation({
      onSuccess: () => {
        companyQuery.refetch();
      },
    });
  const removeUserFromWorkspaceMutation =
    trpc.admin.removeUserFromWorkspace.useMutation({
      onSuccess: () => {
        toast.success("User Removed from Workspace");
        companyQuery.refetch();
      },
      onError: (error) => {
        handleTrpcError({ title: "Error Removing User from Workspace", error });
      },
    });
  const deleteCompanyMutation = trpc.admin.company.delete.useMutation({
    onSuccess: () => {
      toast.success("Company Deleted");

      trpcUtils.admin.company.getAll.refetch();
      router.push("/admin/settings/companies");
    },
    onError: (error) => {
      handleTrpcError({ title: "Error Deleting Company", error });
    },
  });
  const deleteWorkspaceMutation =
    trpc.admin.company.deleteWorkspace.useMutation({
      onSuccess: () => {
        toast.success("Workspace Deleted");
        companyQuery.refetch();
      },
      onError: (error) => {
        handleTrpcError({ title: "Error Deleting Workspace", error });
      },
    });
  const convertWorkspaceSubscriptionMutation =
    trpc.admin.company.convertWorkspaceSubscription.useMutation({
      onError: (error) => {
        handleTrpcError({
          title: "Error Converting Workspace Subscription",
          error,
        });
      },
      onSuccess: (data, variables) => {
        assert(company);
        const { workspaceSubscription } = data;
        const workspaces = company.workspaces.map((workspace) => {
          if (workspace.id === variables.workspaceID) {
            return {
              ...workspace,
              subscription: workspaceSubscription,
            };
          } else {
            return workspace;
          }
        });
        toast.success("Workspace Subscription Updated", {
          description: "Update subscription in Stripe to reflect changes",
          action: {
            label: "View in Stripe",
            onClick: () => {
              window.open(
                `https://dashboard.stripe.com/customers/${company.stripeCustomerID}`,
                "_blank"
              );
            },
          },
        });
        trpcUtils.admin.company.get.setData(
          { id: company.id },
          { company: { ...company, workspaces } }
        );
      },
    });
  const updateWorkspaceNameMutation =
    trpc.admin.company.updateWorkspaceName.useMutation({
      onError: (error) => {
        handleTrpcError({ title: "Error Updating Workspace Name", error });
      },
    });
  const setTrialEndDateMutation =
    trpc.admin.company.setTrialEndDate.useMutation({
      onError: (error) => {
        handleTrpcError({
          title: "Error Updating Trial End Date",
          error,
        });
      },
    });

  const refreshProfilePictureMutation =
    trpc.admin.linkedIn.refreshProfilePicture.useMutation({
      onSuccess: () => {
        toast.success("Profile Picture Refreshed");
        companyQuery.refetch();
      },
      onError: (error) => {
        handleTrpcError({ title: "Error Refreshing Profile Picture", error });
      },
    });

  const saveStripeSubscriptionIDMutation =
    trpc.admin.company.setSubscription.useMutation({
      onSuccess: (data) => {
        const { newSubscription } = data;

        if (newSubscription) {
          toast.success("Subscription ID Saved", {
            description: `Subscription ID has been set to ${newSubscription.stripeSubscriptionID}`,
          });
        } else {
          toast.success("Subscription ID Removed", {
            description: `Subscription ID has been removed`,
          });
        }
        companyQuery.refetch();
      },
      onError: (error) => {
        handleTrpcError({ title: "Error Saving Subscription", error });
      },
    });

  const setIntegrationIsLeadsScrapingMutation =
    trpc.admin.linkedIn.setIntegrationIsLeadsScraping.useMutation({
      onSuccess: () => {
        companyQuery.refetch();
      },
    });

  const handleSaveSubscriptionID = () => {
    if (subscriptionID === company?.subscription?.stripeSubscriptionID) {
      return;
    }

    saveStripeSubscriptionIDMutation.mutate({
      id: company!.id,
      stripeSubscriptionID: subscriptionID.length ? subscriptionID : null,
    });
  };

  useWindowTitle(company ? company.name : "Assembly | Company Page");

  if (!company) {
    return (
      <div className="w-full max-w-5xl p-4 md:px-20 md:pt-8">
        <div className="mb-4 flex items-center justify-between md:mb-10">
          <Skeleton className="h-9 w-36 md:h-10 md:w-60" />
          <Skeleton className="h-9 w-40 md:h-10" />
        </div>
        <div className="mb-5 flex items-center gap-4">
          <Skeleton className="h-5 w-20" />
          <Skeleton className="h-5 w-40" />
        </div>
        <div className="mb-5 flex items-center gap-4">
          <Skeleton className="h-5 w-full" />
          <Skeleton className="h-5 w-40" />
        </div>
        <div className="mb-5 flex w-full items-center justify-between gap-4">
          <Skeleton className="h-5 w-40" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-20 rounded-full" />
            <Skeleton className="h-5 w-20 rounded-full" />
            <Skeleton className="h-5 w-20 rounded-full" />
            <Skeleton className="hidden h-5 w-20 rounded-full md:block" />
            <Skeleton className="h-5 w-5 rounded-full" />
          </div>
        </div>
        <div className="flex w-full items-center justify-end gap-2">
          <Skeleton className="h-5 w-20 rounded-full" />
          <Skeleton className="h-5 w-20 rounded-full" />
          <div className="h-5 w-5 rounded-full" />
        </div>
      </div>
    );
  }

  const workspaces = company.workspaces;
  const filteredWorkspaces = workspaces.filter((workspace) => {
    if (hideInactiveWorkspaces && !workspace.isActive) {
      return false;
    } else {
      return (
        workspaceSearch.length === 0 ||
        workspace.name.toLowerCase().includes(workspaceSearch.toLowerCase())
      );
    }
  });

  const workspaceUsers = workspaces.flatMap((workspace) =>
    workspace.userWorkspaces.map((userWorkspace) => userWorkspace.user)
  );
  const users = uniqBy(workspaceUsers, "id");
  const usersInMultipleWorkspaces = users.filter(
    (user) =>
      workspaceUsers.filter((workspaceUser) => workspaceUser.id === user.id)
        .length > 1
  );

  const { subscription } = company;
  const numSeats = subscription?.numSeats || null;
  const numSocialProfiles = subscription?.numSocialProfiles || null;

  return (
    <SettingsLayout.Root isFullWidth={true}>
      <SettingsLayout.Header>
        <SettingsLayout.Title>
          <div className="mx-auto flex max-w-5xl items-center justify-between">
            <div className="flex items-center gap-2">
              <div>{company.name}</div>
              <DropdownMenu.Root>
                <DropdownMenu.Trigger>
                  <div className="hover:bg-lightest-gray/50 rounded-full bg-inherit p-1.5 text-black transition-all">
                    <EllipsisVerticalIcon className="h-5 w-5" />
                  </div>
                </DropdownMenu.Trigger>
                <DropdownMenu.Content>
                  <DropdownMenu.Group>
                    <DropdownMenu.Item
                      icon="ArrowTopRightOnSquareIcon"
                      intent="none"
                      onClick={() => {
                        window.open(
                          `https://supabase.com/dashboard/project/mletdxmpsjlbfpnymbcu/editor/247977?filter=id%3Aeq%3A${company.id}`,
                          "_blank"
                        );
                      }}
                    >
                      View Company in Supabase
                    </DropdownMenu.Item>
                    <DropdownMenu.Item
                      icon="DocumentDuplicateIcon"
                      intent="none"
                      onClick={() => {
                        copy(company.id) &&
                          toast.success("Copied to clipboard", {
                            description: `Company ID copied to clipboard`,
                          });
                      }}
                    >
                      Copy Company ID
                    </DropdownMenu.Item>
                    <DropdownMenu.Item
                      icon="TrashIcon"
                      intent="danger"
                      onClick={() => {
                        openConfirmationModal({
                          title: `Delete ${company.name}?`,
                          body: (
                            <Alert
                              intent="danger"
                              title="This action cannot be undone"
                              message=" This will delete all associated workspaces,
                          integrations, and posts. Users won't be deleted, just
                          disconnected from this company and associated
                          workspaces."
                            />
                          ),
                          cancelButton: {
                            label: "Cancel, don't delete",
                          },
                          primaryButton: {
                            label: `Delete ${company.name}`,
                            variant: "danger",
                            onClick: () => {
                              deleteCompanyMutation.mutate({
                                id: company.id,
                              });
                            },
                          },
                        });
                      }}
                    >
                      Delete Company
                    </DropdownMenu.Item>
                  </DropdownMenu.Group>
                </DropdownMenu.Content>
              </DropdownMenu.Root>
            </div>
            <Drawer.Root>
              <Drawer.Trigger className="font-body-sm text-basic flex w-fit items-center gap-0.5 rounded-md p-2 shadow-sm transition-shadow hover:shadow-lg">
                <MagnifyingGlassIcon className="h-4 w-4" />
                <div>Search Companies</div>
              </Drawer.Trigger>
              <Drawer.Content className="mb-8">
                <CompanySearch />
              </Drawer.Content>
            </Drawer.Root>
          </div>
        </SettingsLayout.Title>
      </SettingsLayout.Header>
      <div className="mx-auto max-w-5xl space-y-2">
        <Tabs.Root defaultValue="workspaces">
          <Tabs.List>
            <Tabs.Trigger value="workspaces">
              Workspaces
              {workspaces.length > 1 ? ` (${filteredWorkspaces.length})` : ""}
            </Tabs.Trigger>
            <Tabs.Trigger value="admin">
              Admins (<NumberFlow value={company.userCompanies.length} />)
            </Tabs.Trigger>
            <Tabs.Trigger
              value="subscription"
              className="flex items-center gap-1"
            >
              Subscription
              <SubscriptionBadge company={company} />
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="workspaces">
            <div className="pb-20">
              <div className="flex w-full items-center justify-between">
                <div className="flex w-[400px] items-center gap-2">
                  <TextInput
                    value={workspaceSearch}
                    onChange={setWorkspaceSearch}
                    showSearchIcon={true}
                    placeholder="Search"
                  />
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={hideInactiveWorkspaces}
                      onCheckedChange={() => {
                        setHideInactiveWorkspaces(!hideInactiveWorkspaces);
                      }}
                    />
                    <div className="medium-dark-gray font-body-sm whitespace-nowrap">
                      Hide Inactive
                    </div>
                  </div>
                </div>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    createWorkspaceModalHandlers.toggle();
                  }}
                >
                  Add Workspace
                </Button>
                <CreateWorkspaceModal
                  companyID={company.id}
                  open={createWorkspaceModalOpen}
                  setOpen={createWorkspaceModalHandlers.toggle}
                  users={
                    usersInMultipleWorkspaces.length > 0
                      ? usersInMultipleWorkspaces
                      : users
                  }
                />
              </div>
              <Accordion.Root
                type="multiple"
                defaultValue={
                  workspaces.length === 1 ? [workspaces[0].id] : undefined
                }
              >
                {filteredWorkspaces.map((workspace) => {
                  const {
                    profiles,
                    engagementProfiles,
                    users,
                    numScheduledPosts,
                    numSocialProfiles,
                    numAIChats,
                    subscription,
                  } = workspace;

                  const updateWorkspaceName = (name: string) => {
                    if (name === workspace.name) {
                      return;
                    }

                    if (name.length === 0) {
                      toast.error("Invalid Name", {
                        description: "Workspace name cannot be empty",
                      });
                      return;
                    }

                    updateWorkspaceNameMutation.mutate({
                      id: workspace.id,
                      name,
                    });
                    trpcUtils.admin.company.get.setData(
                      {
                        id: company.id,
                      },
                      {
                        company: {
                          ...company,
                          workspaces: company.workspaces.map(
                            (companyWorkspace) => {
                              if (companyWorkspace.id === workspace.id) {
                                return {
                                  ...companyWorkspace,
                                  name,
                                };
                              } else {
                                return companyWorkspace;
                              }
                            }
                          ),
                        },
                      }
                    );
                    toast.success("Workspace Name Updated", {
                      description: `Workspace name is now ${name}`,
                    });
                  };

                  const isActive = users.length > 0;

                  const socialProfileLimit = subscription?.socialProfileLimit;
                  const seatLimit = subscription?.seatLimit;
                  const aiChatLimit = subscription?.aiChatLimit;
                  const tier = subscription?.tier || null;
                  const schedulePostLimit = subscription?.scheduledPostLimit;

                  return (
                    <Accordion.Item key={workspace.id} value={workspace.id}>
                      <Accordion.Trigger>
                        <div className="flex w-full items-center justify-between pr-2">
                          <div className="flex items-center gap-2">
                            <span
                              className={cn("text-left", {
                                "text-medium-gray": !isActive,
                              })}
                            >
                              {workspace.name}
                            </span>
                            {tier != null && (
                              <Badge
                                intent={
                                  tier === "Standard" ? "secondary" : "success"
                                }
                                isUppercase={true}
                                kind="light"
                                size="sm"
                                label={tier}
                              />
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <LimitBadge
                              label="Profiles"
                              value={numSocialProfiles}
                              limit={socialProfileLimit}
                            />
                            <LimitBadge
                              label="Posts"
                              value={numScheduledPosts}
                              limit={schedulePostLimit}
                            />
                            <LimitBadge
                              hiddenSm={true}
                              label="AI"
                              value={numAIChats}
                              limit={aiChatLimit}
                            />
                            <LimitBadge
                              hiddenSm={
                                seatLimit == null || users.length < seatLimit
                              }
                              label="Users"
                              value={users.length}
                              limit={seatLimit}
                            />
                          </div>
                        </div>
                      </Accordion.Trigger>
                      <Accordion.Content>
                        <div className="flex items-center gap-2">
                          <CopyButton
                            label="Workspace ID"
                            value={workspace.id}
                          />
                          <DropdownMenu.Root>
                            <DropdownMenu.Trigger>
                              <div className="hover:bg-lightest-gray/50 rounded-full bg-inherit p-1.5 text-black transition-all">
                                <EllipsisVerticalIcon className="h-4 w-4" />
                              </div>
                            </DropdownMenu.Trigger>
                            <DropdownMenu.Content>
                              <DropdownMenu.Group>
                                <DropdownMenu.Sub>
                                  <DropdownMenu.SubTrigger icon="PencilSquareIcon">
                                    Edit Name
                                  </DropdownMenu.SubTrigger>
                                  <DropdownMenu.SubContent>
                                    <Label value="Name">
                                      <TextInput
                                        value={workspaceNames[workspace.id]}
                                        onChange={(value) => {
                                          setWorkspaceNames(
                                            (prevWorkspaceNames) => ({
                                              ...prevWorkspaceNames,
                                              [workspace.id]: value,
                                            })
                                          );
                                        }}
                                        onBlur={() => {
                                          updateWorkspaceName(
                                            workspaceNames[workspace.id]
                                          );
                                        }}
                                        onEnterPress={() => {
                                          updateWorkspaceName(
                                            workspaceNames[workspace.id]
                                          );
                                        }}
                                      />
                                    </Label>
                                  </DropdownMenu.SubContent>
                                </DropdownMenu.Sub>
                              </DropdownMenu.Group>
                              <DropdownMenu.Separator />
                              <DropdownMenu.Group>
                                <DropdownMenu.Item
                                  isDisabled={tier === "Basic"}
                                  icon="AdjustmentsHorizontalIcon"
                                  onClick={() => {
                                    convertWorkspaceSubscriptionMutation.mutate(
                                      {
                                        workspaceID: workspace.id,
                                        tier: "Basic",
                                      }
                                    );
                                  }}
                                >
                                  Convert to Basic
                                </DropdownMenu.Item>
                                <DropdownMenu.Item
                                  isDisabled={tier === "Standard"}
                                  icon="AdjustmentsHorizontalIcon"
                                  onClick={() => {
                                    convertWorkspaceSubscriptionMutation.mutate(
                                      {
                                        workspaceID: workspace.id,
                                        tier: "Standard",
                                      }
                                    );
                                  }}
                                >
                                  Convert to Standard
                                </DropdownMenu.Item>
                                <DropdownMenu.Item
                                  icon="ArrowTopRightOnSquareIcon"
                                  onClick={() => {
                                    window.open(
                                      `https://supabase.com/dashboard/project/mletdxmpsjlbfpnymbcu/editor/725026?schema=public&filter=workspace_id%3Aeq%3A${workspace.id}`,
                                      "_blank"
                                    );
                                  }}
                                >
                                  View in Supabase
                                </DropdownMenu.Item>
                                <DropdownMenu.Item
                                  intent="danger"
                                  isDisabled={tier === null}
                                  icon="ArchiveBoxXMarkIcon"
                                  onClick={() => {
                                    convertWorkspaceSubscriptionMutation.mutate(
                                      {
                                        workspaceID: workspace.id,
                                        tier: null,
                                      }
                                    );
                                  }}
                                >
                                  Remove Limits
                                </DropdownMenu.Item>
                              </DropdownMenu.Group>
                              <DropdownMenu.Separator />
                              <DropdownMenu.Group>
                                <DropdownMenu.Item
                                  icon="TrashIcon"
                                  intent="danger"
                                  onClick={() => {
                                    openConfirmationModal({
                                      title: `Delete ${workspace.name}?`,
                                      body: (
                                        <Alert
                                          intent="danger"
                                          title="This action cannot be undone"
                                          message=" This will delete all associated
                    integrations and posts. Users won't be deleted, just
                    disconnected from this workspace."
                                        />
                                      ),
                                      cancelButton: {
                                        label: "Cancel, don't delete",
                                      },
                                      primaryButton: {
                                        label: `Delete ${workspace.name}`,
                                        variant: "danger",
                                        onClick: () => {
                                          deleteWorkspaceMutation.mutate({
                                            workspaceID: workspace.id,
                                          });
                                        },
                                      },
                                    });
                                  }}
                                >
                                  Delete Workspace
                                </DropdownMenu.Item>
                              </DropdownMenu.Group>
                            </DropdownMenu.Content>
                          </DropdownMenu.Root>
                        </div>
                        <div className="mt-6 flex flex-col items-start gap-4 md:flex-row">
                          <div className="flex items-start gap-1 md:gap-4">
                            <div className="flex flex-col items-center gap-2">
                              <div className="font-body-sm space-y-4">
                                <div>
                                  <span className="font-medium">Profiles</span>
                                  <div className="flex flex-col gap-1">
                                    <div className="font-body-sm mt-4 flex flex-col gap-2.5">
                                      {profiles.map((profile) => {
                                        return (
                                          <div
                                            key={profile.id}
                                            className="group flex items-center justify-between"
                                          >
                                            <div className="flex max-w-60 items-center gap-2">
                                              <AccountChannelAvatar
                                                size={32}
                                                account={{
                                                  channel: profile.channel,
                                                  profileImageUrl:
                                                    profile.profileImageUrl,
                                                }}
                                              />
                                              <div className="min-w-0 flex-1">
                                                <div className="flex items-center gap-1">
                                                  <p className="leading-4 font-medium">
                                                    {profile.name}
                                                  </p>
                                                  {profile.isLeadsScrapingEnabled && (
                                                    <div className="font-eyebrow-sm bg-secondary ml-0.5 flex max-h-4 items-center justify-center rounded-xs px-1.5 py-0.5 text-white">
                                                      Leads
                                                    </div>
                                                  )}
                                                </div>
                                                <p className="font-body-sm text-medium-dark-gray">
                                                  {profile.detail}
                                                </p>
                                              </div>
                                            </div>
                                            <DropdownMenu.Root>
                                              <DropdownMenu.Trigger>
                                                <div className="hover:bg-lightest-gray/50 rounded-full p-0.5 opacity-0 transition-all group-hover:opacity-100">
                                                  <EllipsisVerticalIcon className="h-5 w-5" />
                                                </div>
                                              </DropdownMenu.Trigger>
                                              <DropdownMenu.Content>
                                                {profile.channel ===
                                                  "LinkedIn" && (
                                                  <DropdownMenu.Item
                                                    icon="IdentificationIcon"
                                                    onClick={() => {
                                                      setIntegrationIsLeadsScrapingMutation.mutate(
                                                        {
                                                          integrationID:
                                                            profile.id,
                                                          isLeadsScrapingEnabled:
                                                            !profile.isLeadsScrapingEnabled,
                                                        }
                                                      );
                                                    }}
                                                  >
                                                    {profile.isLeadsScrapingEnabled
                                                      ? "Disable Leads Scraping"
                                                      : "Enable Leads Scraping"}
                                                  </DropdownMenu.Item>
                                                )}
                                                {profile.channel ===
                                                  "LinkedIn" && (
                                                  <DropdownMenu.Item
                                                    icon="UserCircleIcon"
                                                    onClick={() => {
                                                      refreshProfilePictureMutation.mutate(
                                                        {
                                                          integrationID:
                                                            profile.id,
                                                        }
                                                      );
                                                    }}
                                                  >
                                                    Refresh Profile Picture
                                                  </DropdownMenu.Item>
                                                )}
                                                <DropdownMenu.Item
                                                  icon="TrashIcon"
                                                  intent="danger"
                                                  onClick={() => {
                                                    openConfirmationModal({
                                                      title: `Delete ${
                                                        CHANNEL_ICON_MAP[
                                                          profile.channel
                                                        ].label
                                                      } Profile?`,
                                                      body: `Are you sure you want to delete the ${
                                                        CHANNEL_ICON_MAP[
                                                          profile.channel
                                                        ].label
                                                      } profile for ${
                                                        profile.name
                                                      }? This action cannot be undone.`,
                                                      cancelButton: {
                                                        label:
                                                          "Cancel, don't delete",
                                                      },
                                                      primaryButton: {
                                                        label: `Delete Profile`,
                                                        variant: "danger",
                                                        onClick: () => {
                                                          toast.promise(
                                                            disconnectIngrationMutation.mutateAsync(
                                                              {
                                                                id: profile.id,
                                                                workspaceID:
                                                                  workspace.id,
                                                                channel:
                                                                  profile.channel,
                                                              }
                                                            ),
                                                            {
                                                              loading:
                                                                "Disconnecting profile",
                                                              success:
                                                                "Profile disconnected",
                                                              error:
                                                                "Error disconnecting profile",
                                                            }
                                                          );
                                                        },
                                                      },
                                                    });
                                                  }}
                                                >
                                                  Delete Profile
                                                </DropdownMenu.Item>
                                              </DropdownMenu.Content>
                                            </DropdownMenu.Root>
                                          </div>
                                        );
                                      })}
                                    </div>
                                  </div>
                                </div>
                                <div>
                                  <span className="font-medium">
                                    Engagement Profiles
                                  </span>
                                  <div className="flex flex-col gap-1">
                                    <div className="font-body-sm mt-4 flex flex-col gap-2.5">
                                      {engagementProfiles.map((profile) => {
                                        return (
                                          <div
                                            key={profile.id}
                                            className="group flex items-center justify-between"
                                          >
                                            <div className="flex max-w-60 items-center gap-2">
                                              <AccountChannelAvatar
                                                size={32}
                                                account={{
                                                  channel: profile.channel,
                                                  profileImageUrl:
                                                    profile.profileImageUrl,
                                                }}
                                              />
                                              <div className="min-w-0 flex-1">
                                                <p className="leading-4 font-medium">
                                                  {profile.name}
                                                </p>
                                                <p className="font-body-sm text-medium-dark-gray">
                                                  {profile.detail}
                                                </p>
                                              </div>
                                            </div>
                                            <DropdownMenu.Root>
                                              <DropdownMenu.Trigger>
                                                <div className="hover:bg-lightest-gray/50 rounded-full p-0.5 opacity-0 transition-all group-hover:opacity-100">
                                                  <EllipsisVerticalIcon className="h-5 w-5" />
                                                </div>
                                              </DropdownMenu.Trigger>
                                              <DropdownMenu.Content>
                                                <DropdownMenu.Item
                                                  icon="TrashIcon"
                                                  intent="danger"
                                                  onClick={() => {
                                                    openConfirmationModal({
                                                      title: `Delete ${
                                                        CHANNEL_ICON_MAP[
                                                          profile.channel
                                                        ].label
                                                      } Profile?`,
                                                      body: `Are you sure you want to delete the ${
                                                        CHANNEL_ICON_MAP[
                                                          profile.channel
                                                        ].label
                                                      } profile for ${
                                                        profile.name
                                                      }? This action cannot be undone.`,
                                                      cancelButton: {
                                                        label:
                                                          "Cancel, don't delete",
                                                      },
                                                      primaryButton: {
                                                        label: `Delete Profile`,
                                                        variant: "danger",
                                                        onClick: () => {
                                                          toast.promise(
                                                            disconnectIngrationMutation.mutateAsync(
                                                              {
                                                                id: profile.id,
                                                                workspaceID:
                                                                  workspace.id,
                                                                channel:
                                                                  profile.channel,
                                                              }
                                                            ),
                                                            {
                                                              loading:
                                                                "Disconnecting profile",
                                                              success:
                                                                "Profile disconnected",
                                                              error:
                                                                "Error disconnecting profile",
                                                            }
                                                          );
                                                        },
                                                      },
                                                    });
                                                  }}
                                                >
                                                  Delete Profile
                                                </DropdownMenu.Item>
                                              </DropdownMenu.Content>
                                            </DropdownMenu.Root>
                                          </div>
                                        );
                                      })}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="flex flex-col items-center gap-2">
                              <div className="font-body-sm">
                                <span className="font-medium">Users</span>
                                <div className="flex flex-col gap-1">
                                  <div className="mt-4 flex flex-col gap-2.5">
                                    {users.map((user) => {
                                      return (
                                        <UserAvatar
                                          key={user.id}
                                          user={{
                                            ...user,
                                            detail: user.email,
                                          }}
                                          backgroundColor="darkGray"
                                          showFullName={true}
                                          showDetail={true}
                                          onXClick={() => {
                                            openConfirmationModal({
                                              title: `Remove of ${getFullName(
                                                user
                                              )}?`,
                                              body: `Are you sure you want to remove ${getFullName(
                                                user
                                              )} from ${
                                                workspace.name
                                              }? This action cannot be undone.`,
                                              cancelButton: {
                                                label: "Cancel, don't remove",
                                              },
                                              primaryButton: {
                                                label: `Remove User`,
                                                variant: "danger",
                                                onClick: () => {
                                                  removeUserFromWorkspaceMutation.mutate(
                                                    {
                                                      userID: user.id,
                                                      workspaceID: workspace.id,
                                                    }
                                                  );
                                                },
                                              },
                                            });
                                          }}
                                          isLoading={
                                            removeUserFromWorkspaceMutation.isPending &&
                                            removeUserFromWorkspaceMutation
                                              .variables?.userID === user.id
                                          }
                                        />
                                      );
                                    })}
                                    {workspace.invites.length > 0 && (
                                      <div>
                                        <Divider padding="none" />
                                        <div className="mt-2 flex flex-col gap-4">
                                          {workspace.invites.map((invite) => {
                                            return (
                                              <div
                                                key={invite.id}
                                                className="flex justify-between"
                                              >
                                                <div className="flex items-center gap-2 text-xs">
                                                  <UserAvatar
                                                    user={{
                                                      firstName: invite.email,
                                                      detail: `Invited ${DateTime.fromJSDate(
                                                        invite.createdAt
                                                      ).toLocaleString(
                                                        DateTime.DATETIME_MED
                                                      )}`,
                                                    }}
                                                    backgroundColor="lightestGray"
                                                    fontColor="mediumGray"
                                                    showFullName={true}
                                                    showDetail={true}
                                                  />
                                                </div>
                                              </div>
                                            );
                                          })}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div>
                            <span className="ml-4 font-medium">Limits</span>
                            <WorkspaceLimitsTable
                              companyID={company.id}
                              workspace={workspace}
                            />
                          </div>
                        </div>
                      </Accordion.Content>
                    </Accordion.Item>
                  );
                })}
                <div className="bg-lightest-gray/20 flex justify-end py-4 pr-6">
                  <div className="flex items-center gap-2">
                    <Badge
                      intent={
                        sum(
                          filteredWorkspaces.flatMap((workspace) =>
                            workspace.users.length > 0
                              ? workspace.numSocialProfiles
                              : 0
                          )
                        ) === 0
                          ? "info"
                          : "none"
                      }
                      tooltipText="This only includes profiles on active workspaces (at least one user)"
                      isUppercase={true}
                      kind="solid"
                      size="sm"
                      label={`Profiles: ${sum(
                        filteredWorkspaces.flatMap((workspace) =>
                          workspace.isActive ? workspace.numSocialProfiles : 0
                        )
                      )}/${
                        numSocialProfiles != null ? numSocialProfiles : "∞"
                      }`}
                    />
                    <Badge
                      intent="none"
                      isUppercase={true}
                      kind="solid"
                      size="sm"
                      label={`Users: ${
                        uniq(
                          filteredWorkspaces.flatMap((workspace) =>
                            workspace.users.map((user) => user.id)
                          )
                        ).length
                      }/${numSeats != null ? numSeats : "∞"}`}
                    />
                  </div>
                </div>
              </Accordion.Root>
            </div>
          </Tabs.Content>
          <Tabs.Content value="admin">
            <CompanyAdminTab
              company={company}
              users={users}
              usersInMultipleWorkspaces={usersInMultipleWorkspaces}
            />
          </Tabs.Content>
          <Tabs.Content value="subscription">
            <div className="max-w-sm space-y-4">
              <div className="flex w-full items-center gap-2">
                <div className="w-full">
                  <Label value="Subscription ID">
                    <TextInput
                      value={subscriptionID}
                      placeholder="Enter subscription ID"
                      onChange={setSubscriptionID}
                      onBlur={handleSaveSubscriptionID}
                      onEnterPress={handleSaveSubscriptionID}
                    />
                  </Label>
                </div>
                <div className="w-fit">
                  <Label value="Trial End Date">
                    <DatePicker
                      mode="single"
                      value={
                        company.trialEndsAt
                          ? DateTime.fromJSDate(company.trialEndsAt).toJSDate()
                          : undefined
                      }
                      isClearable={true}
                      onChange={(date) => {
                        setTrialEndDateMutation.mutate({
                          id: company.id,
                          trialEndsAt: date,
                        });
                        trpcUtils.admin.company.get.setData(
                          { id: company.id },
                          {
                            company: {
                              ...company,
                              trialEndsAt: date,
                            },
                          }
                        );
                      }}
                    />
                  </Label>
                </div>
              </div>
              {company.stripeCustomerID ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <CopyButton
                      label="Stripe Customer ID"
                      value={company.stripeCustomerID}
                    />
                    <IconButton
                      icon="ArrowTopRightOnSquareIcon"
                      tooltipText="View in Stripe"
                      onClick={() => {
                        window.open(
                          `https://dashboard.stripe.com/customers/${company.stripeCustomerID}`,
                          "_blank"
                        );
                      }}
                    />
                  </div>
                </div>
              ) : (
                <Alert
                  intent="none"
                  title="No Stripe Customer ID"
                  message="This company does not have a Stripe Customer ID"
                />
              )}
              <Label value="Notes">
                <TextareaInput
                  value={notes}
                  onChange={setNotes}
                  rows={5}
                  placeholder="Enter notes about the company or pricing"
                  onBlur={() => {
                    if (notes != company.notes) {
                      updateCompanyMutation.mutate({
                        id: company.id,
                        notes,
                      });
                    }
                  }}
                />
              </Label>
            </div>
          </Tabs.Content>
        </Tabs.Root>
      </div>
    </SettingsLayout.Root>
  );
};

const LimitBadge = ({
  label,
  value,
  limit,
  hiddenSm,
}: {
  label: string;
  value: number;
  limit: number | null | undefined;
  hiddenSm?: boolean;
}) => {
  return (
    <div
      className={cn({
        "hidden md:block": hiddenSm,
      })}
    >
      <Badge
        intent={
          value === 0
            ? "info"
            : value > (limit || Infinity)
              ? "warning"
              : "none"
        }
        isUppercase={true}
        kind={value > (limit || Infinity) ? "light" : "solid"}
        size="sm"
        label={`${label}: ${value}/${limit || "∞"}`}
      />
    </div>
  );
};

export default CompanyPage;
