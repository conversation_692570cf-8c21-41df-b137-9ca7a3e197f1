import * as Icons from "@heroicons/react/24/outline";
import { ReactNode, useState } from "react";

import {
  EllipsisHorizontalIcon,
  EllipsisVerticalIcon,
} from "@heroicons/react/24/outline";
import assert from "assert";
import classNames from "classnames";
import { Section, TextInput } from "~/components";
import Popover from "./ui/Popover";

export type Option = {
  key: string;
  icon?: keyof typeof Icons;
  label: ReactNode | string;
  searchableValue?: string;
  intent?: "danger" | "none" | "success";
  onClick: () => void;
};

export type SectionType = {
  key: string;
  label?: string;
  showSearch?: boolean;
  options: Option[];
  expandable?: boolean;
};

type WithOptions = {
  options: Option[];
  sections?: never;
};

type WithSections = {
  options?: never;
  sections: SectionType[];
};
type Props = (WithOptions | WithSections) & {
  button?: ReactNode;
  ellipsisOrientation?: "horizontal" | "vertical";
  disabled?: boolean;
  showSearch?: boolean;
  position?: "top-left" | "top-right";
};

const OptionsDropdown = ({
  options,
  sections,
  button,
  ellipsisOrientation = "horizontal",
  disabled = false,
  position = "top-left",
  showSearch = false,
}: Props) => {
  const [filters, setFilters] = useState<{ [sectionKey: string]: string }>({});

  let optionSections;
  if (sections) {
    optionSections = sections;
  }

  if (!optionSections && options) {
    optionSections = [
      {
        key: "section",
        options,
      },
    ];
  }

  assert(optionSections);

  return (
    <Popover.Root>
      <Popover.Trigger
        disabled={disabled}
        className={classNames({
          "pointer-events-none opacity-70": disabled,
        })}
      >
        {button || (
          <div className="bg-surface hover:bg-slate translate-y-0.5 rounded-full p-0.5 text-sm text-black transition-colors">
            {ellipsisOrientation === "horizontal" ? (
              <EllipsisHorizontalIcon className="h-5 w-5" />
            ) : (
              <EllipsisVerticalIcon className="h-5 w-5" />
            )}
          </div>
        )}
      </Popover.Trigger>
      <Popover.Content className="scrollbar-hidden max-h-60 min-w-48 overflow-scroll px-4 py-3">
        {optionSections.map((section) => {
          const filter = filters[section.key] || "";

          const filteredOptions = section.options.filter((option) => {
            if (filter.length === 0) {
              return true;
            } else {
              if (typeof option.label === "string") {
                return option.label
                  .toLowerCase()
                  .includes(filter.toLowerCase());
              } else {
                return option.searchableValue
                  ?.toLowerCase()
                  .includes(filter.toLowerCase());
              }
            }
          });

          const Options = (
            <div>
              {(showSearch || section.showSearch) && (
                <div className="mb-1">
                  <TextInput
                    value={filter}
                    autoFocus={true}
                    onChange={(newFilter) =>
                      setFilters({
                        ...filters,
                        [section.key]: newFilter,
                      })
                    }
                    placeholder="Search..."
                  />
                </div>
              )}
              {filteredOptions.map((option) => {
                const Icon = option.icon ? Icons[option.icon] : null;

                return (
                  <button
                    key={option.key}
                    onClick={option.onClick}
                    className={classNames(
                      "font-body-sm hover:bg-slate flex w-full items-center gap-1 rounded-md px-2 py-1 text-left whitespace-nowrap transition-colors",
                      {
                        "text-success": option.intent === "success",
                        "text-danger": option.intent === "danger",
                        "text-off-black":
                          option.intent === "none" || option.intent == null,
                      }
                    )}
                  >
                    {Icon && <Icon className="h-4 w-4" />}
                    {option.label}
                  </button>
                );
              })}
              {filteredOptions.length === 0 && (
                <div className="text-medium-gray mt-2 text-xs whitespace-nowrap">
                  No options found
                </div>
              )}
            </div>
          );

          return (
            <div key={section.key}>
              {section.label ? (
                <div className="w-full pt-2">
                  <Section
                    title={section.label}
                    size="sm"
                    expandable={section.expandable}
                  >
                    {Options}
                  </Section>
                </div>
              ) : (
                Options
              )}
            </div>
          );
        })}
      </Popover.Content>
    </Popover.Root>
  );
};

export default OptionsDropdown;
