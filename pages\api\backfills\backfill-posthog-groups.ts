import { NextApiRequest, NextApiResponse } from "next";

import { StatusCodes } from "http-status-codes";

import { db } from "~/clients";
import posthog from "~/clients/posthog";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const companies = await db.company.findMany({
    include: {
      workspaces: {
        include: {
          subscription: true,
        },
      },
    },
  });

  companies.forEach((company) => {
    posthog.groupIdentify({
      distinctId: company.id,
      groupType: "company",
      groupKey: company.id,
      properties: {
        name: company.name,
      },
    });
  });

  companies.forEach((company) => {
    company.workspaces.forEach((workspace) => {
      posthog.groupIdentify({
        distinctId: workspace.id,
        groupType: "workspace",
        groupKey: workspace.id,
        properties: {
          name: workspace.name,
          companyID: company.id,
        },
      });
    });
  });

  res.status(StatusCodes.OK).send({ companies: companies.length });
};

export default handler;
