import { Channel, PostStatus } from "@prisma/client";
import { JSONContent } from "@tiptap/core";
import classNames from "classnames";
import { motion } from "framer-motion";
import { useRouter } from "next/router";
import { useMemo } from "react";
import Alert from "~/components/ui/Alert";
import Button from "~/components/ui/Button";
import { EDITOR_CHANNELS } from "~/constants";
import { usePostIntegrations } from "~/providers";
import type {
  ConnectedIntegrationsMap,
  EditorChannel,
} from "~/providers/PostIntegrationsProvider";
import { CHANNEL_ICON_MAP } from "~/types";
import { AssetType } from "~/types/Asset";
import { PostOrIdea } from "~/utils/assertPostOrIdea";
import OtherChannelPostNewDesign from "./OtherChannelPostNewDesign";
import { ScheduledPost } from "./SchedulePostAccountSelect";
import type {
  DiscordContent,
  DiscordWebhook as DiscordWebhookRaw,
} from "./discord/DiscordPost";
import DiscordPostNewDesign from "./discord/DiscordPostNewDesign";
import type {
  FacebookContent,
  FacebookIntegration as FacebookIntegrationRaw,
} from "./facebook/FacebookPost";
import FacebookPostNewDesign from "./facebook/FacebookPostNewDesign";
import type {
  InstagramAsset,
  InstagramContent,
  InstagramIntegration as InstagramIntegrationRaw,
  InstagramPost as InstagramPostType,
} from "./instagram/InstagramPost";
import InstagramPostNewDesign from "./instagram/InstagramPostNewDesign";
import type {
  LinkedInAssetsType,
  LinkedInContent,
  LinkedInIntegration as LinkedinIntegrationRaw,
} from "./linkedin/LinkedInPost";
import LinkedInPostNewDesign from "./linkedin/LinkedInPostNewDesign";
import type {
  SlackContent,
  Slackintegration as SlackintegrationRaw,
} from "./slack/SlackPost";
import SlackPostNewDesign from "./slack/SlackPostNewDesign";
import ThreadsPost, {
  ThreadsContent,
  ThreadsIntegration,
} from "./threads/ThreadsPost";
import type {
  TikTokContent,
  TikTokIntegration as TikTokIntegrationRaw,
} from "./tiktok/TikTokPost";
import TikTokPostNewDesign from "./tiktok/TiktokPostNewDesign";
import TweetThread, {
  TweetAsset,
  TweetContent,
  TwitterIntegrationAccount,
} from "./twitter/TweetThread";
import type {
  WebflowContent,
  WebflowIntegration as WebflowIntegrationRaw,
} from "./webflow/WebflowPost";
import WebflowPostNewDesign from "./webflow/WebflowPostNewDesign";
import type {
  YouTubeShortsContent,
  YouTubeIntegration as YoutubeIntegrationRaw,
} from "./youtube_shorts/YouTubeShortsPost";
import YouTubeShortsPostNewDesign from "./youtube_shorts/YouTubeShortsPostNewDesign";

type RawPost = {
  id: string;
  title: string;
  status: PostStatus;
  campaignID: string | null;
  workspaceID: string;
  channels: Channel[];
  otherChannelEditorState: JSONContent | null;
  scheduledPosts: ScheduledPost[];
  discordAssets: AssetType[];
  discordContent: DiscordContent | null;
  discordWebhook: DiscordWebhookRaw | null;
  discordWebhookID: string | null;
  instagramIntegration: InstagramIntegrationRaw | null;
  instagramIntegrationID: string | null;
  instagramContent: InstagramContent | null;
  instagramAssets: {
    assets: InstagramAsset[];
    coverPhoto: AssetType | null;
  };
  instagramPost: InstagramPostType | null;
  instagramLink: string | null;
  facebookAssets: { assets: AssetType[]; coverPhoto: AssetType | null };
  facebookContent: FacebookContent | null;
  facebookIntegration: FacebookIntegrationRaw | null;
  facebookIntegrationID: string | null;
  facebookLink: string | null;
  linkedInAssets: LinkedInAssetsType;
  linkedInContent: LinkedInContent | null;
  linkedInIntegration: LinkedinIntegrationRaw | null;
  linkedInIntegrationID: string | null;
  linkedInPost: {
    id: string;
    publishedAt: Date | null;
    stats: {
      likeCount: number;
      commentCount: number;
      shareCount: number;
      impressionCount: number | null;
      impressionStatRecordedAt: Date | null;
    };
  } | null;
  linkedInLink: string | null;
  twitterIntegrationID: string | null;
  twitterIntegration: TwitterIntegrationAccount | null;
  tweetAssets: { [tweetID: string]: TweetAsset[] };
  tweets: TweetContent[];
  twitterLink: string | null;
  tikTokContent: TikTokContent | null;
  tikTokIntegration: TikTokIntegrationRaw | null;
  tikTokIntegrationID: string | null;
  tikTokAssets: AssetType[];
  tikTokLink: string | null;
  threadsAssets: {
    [id: string]: AssetType[];
  };
  threadsContent: ThreadsContent[];
  threadsIntegration: ThreadsIntegration | null;
  threadsIntegrationID: string | null;
  threadsLink: string | null;
  slackAssets: AssetType[];
  slackContent: SlackContent | null;
  slackIntegration: SlackintegrationRaw | null;
  slackIntegrationID: string | null;
  webflowAssets: { [webflowFieldID: string]: AssetType[] };
  webflowContent: WebflowContent | null;
  webflowIntegration: WebflowIntegrationRaw | null;
  webflowIntegrationID: string | null;
  youTubeShortsAssets: {
    asset: AssetType | null;
  };
  youTubeShortsContent: YouTubeShortsContent | null;
  youTubeIntegration: YoutubeIntegrationRaw | null;
  youTubeIntegrationID: string | null;
  youTubeLink: string | null;
  assets: AssetType[];
} & PostOrIdea;

type Props = {
  post: RawPost;
  isReadOnly: boolean;
};

const mapPostIntegrations = (
  post: RawPost,
  getChannelIntegration: <K extends keyof ConnectedIntegrationsMap>(
    channel: K,
    integrationID: string | null
  ) => ConnectedIntegrationsMap[K][number] | null
) => {
  if (!post) return post;

  return {
    ...post,
    facebookIntegration: getChannelIntegration(
      "Facebook",
      post.facebookIntegrationID
    ),
    linkedInIntegration: getChannelIntegration(
      "LinkedIn",
      post.linkedInIntegrationID
    ),
    instagramIntegration: getChannelIntegration(
      "Instagram",
      post.instagramIntegrationID
    ),
    tikTokIntegration: getChannelIntegration(
      "TikTok",
      post.tikTokIntegrationID
    ),
    discordWebhook: getChannelIntegration("Discord", post.discordWebhookID),
    slackIntegration: getChannelIntegration("Slack", post.slackIntegrationID),
    youTubeIntegration: getChannelIntegration(
      "YouTubeShorts",
      post.youTubeIntegrationID
    ),
    webflowIntegration: getChannelIntegration(
      "Webflow",
      post.webflowIntegrationID
    ),
  };
};

const PostMainBar = ({ post: postRaw, isReadOnly }: Props) => {
  const router = useRouter();
  const {
    currentChannel,
    connectedIntegrations,
    channelSyncState,
    clearSyncingChannel,
    getChannelIntegration,
  } = usePostIntegrations();

  const post = useMemo(
    () => mapPostIntegrations(postRaw, getChannelIntegration),
    [postRaw, getChannelIntegration]
  );

  if (!currentChannel) return null;

  const hasEditor = EDITOR_CHANNELS.includes(currentChannel);

  const showChannel = (channel: Channel) => {
    return post.channels.includes(channel);
  };

  const isChannelWithConnectedIntegration =
    EDITOR_CHANNELS.includes(currentChannel) &&
    !connectedIntegrations[currentChannel as EditorChannel].length;

  return (
    <div className="mt-6 flex flex-col gap-8">
      {isChannelWithConnectedIntegration && (
        <Alert.Root>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2.5">
              <Alert.Icon variant="info" />
              <Alert.Title>{`Connect a ${CHANNEL_ICON_MAP[currentChannel].label} profile to schedule`}</Alert.Title>
            </div>
            <Button
              onClick={() => {
                router.push("/settings/profiles");
              }}
              size="sm"
              variant="secondary"
            >
              Connect Profile
            </Button>
          </div>
        </Alert.Root>
      )}
      <div key="preview-wrapper" className="w-full">
        {showChannel("LinkedIn") && (
          <motion.div
            key="LinkedIn"
            className={classNames({
              hidden: currentChannel !== "LinkedIn",
            })}
          >
            <LinkedInPostNewDesign
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.LinkedIn === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("LinkedIn")}
            />
          </motion.div>
        )}
        {showChannel("Discord") && (
          <motion.div
            key="Discord"
            className={classNames({
              hidden: currentChannel !== "Discord",
            })}
          >
            <DiscordPostNewDesign
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Discord === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Discord")}
            />
          </motion.div>
        )}
        {showChannel("Twitter") && (
          <motion.div
            key="Twitter"
            className={classNames({
              hidden: currentChannel !== "Twitter",
            })}
          >
            <TweetThread
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Twitter === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Twitter")}
            />
          </motion.div>
        )}
        {showChannel("Instagram") && (
          <motion.div
            key="Instagram"
            className={classNames({
              hidden: currentChannel !== "Instagram",
            })}
          >
            <InstagramPostNewDesign
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Instagram === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Instagram")}
            />
          </motion.div>
        )}
        {showChannel("Facebook") && (
          <motion.div
            key="Facebook"
            className={classNames({
              hidden: currentChannel !== "Facebook",
            })}
          >
            <FacebookPostNewDesign
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Facebook === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Facebook")}
            />
          </motion.div>
        )}
        {showChannel("TikTok") && (
          <motion.div
            key="TikTok"
            className={classNames({
              hidden: currentChannel !== "TikTok",
            })}
          >
            <TikTokPostNewDesign
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.TikTok === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("TikTok")}
            />
          </motion.div>
        )}
        {showChannel("Threads") && (
          <motion.div
            key="Threads"
            className={classNames({
              hidden: currentChannel !== "Threads",
            })}
          >
            <ThreadsPost
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Threads === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Threads")}
            />
          </motion.div>
        )}
        {showChannel("Slack") && (
          <motion.div
            key="Slack"
            className={classNames({
              hidden: currentChannel !== "Slack",
            })}
          >
            <SlackPostNewDesign
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.Slack === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("Slack")}
            />
          </motion.div>
        )}
        {showChannel("YouTubeShorts") && (
          <motion.div
            key="YouTubeShorts"
            className={classNames({
              hidden: currentChannel !== "YouTubeShorts",
            })}
          >
            <YouTubeShortsPostNewDesign
              post={post}
              isReadOnly={isReadOnly}
              isSyncingComplete={channelSyncState.YouTubeShorts === "Complete"}
              clearIsSyncing={() => clearSyncingChannel("YouTubeShorts")}
            />
          </motion.div>
        )}
        {showChannel("Webflow") && (
          <motion.div
            key="Webflow"
            className={classNames({
              hidden: currentChannel !== "Webflow",
            })}
          >
            <WebflowPostNewDesign post={post} isReadOnly={isReadOnly} />
          </motion.div>
        )}
        {!hasEditor && (
          <motion.div
            key={currentChannel}
            className={classNames({
              hidden: hasEditor,
            })}
          >
            <OtherChannelPostNewDesign post={post} isReadOnly={isReadOnly} />
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default PostMainBar;
