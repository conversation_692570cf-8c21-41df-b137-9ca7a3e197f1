import sum from "lodash/sum";
import { db } from "~/clients";

const countEngagementProfiles = async (workspace: { id: string }) => {
  const workspaceWithEngagementProfiles = await db.workspace.findFirstOrThrow({
    where: {
      id: workspace.id,
    },
    select: {
      _count: {
        select: {
          linkedInIntegrations: {
            where: {
              type: "Engagement",
            },
          },
        },
      },
    },
  });

  return sum([workspaceWithEngagementProfiles._count.linkedInIntegrations]);
};

export default countEngagementProfiles;
