import Uppy from "@uppy/core";
import { DateTime } from "luxon";
import { ReactNode } from "react";
import { useWorkspace } from "~/providers";
import { AssetType } from "~/types/Asset";
import { UploadingAsset } from "../../files/AssetsNewDesign";
import DiscordAssetPreviewNewDesign from "./DiscordAssetPreviewNewDesign";

type DiscordWebhook = {
  id: string;
  account: {
    username: string;
    profileImageUrl: string | null;
  };
};

type Props = {
  contentID: string;
  assets: AssetType[];
  setAssets: (
    value: AssetType[] | ((prev: AssetType[]) => AssetType[])
  ) => void;
  uploadingAssets: UploadingAsset[];
  draggedOver: boolean;
  setDraggedOver: (isDragged: boolean) => void;
  uppy: Uppy | null;
  getValidFiles: (files: File[]) => Promise<File[]>;
  isReadOnly: boolean;
  editor: ReactNode;
  connectedIntegration: DiscordWebhook | null;
};

const DiscordPostPreviewNewDesign = ({
  contentID,
  assets,
  setAssets,
  uploadingAssets,
  draggedOver,
  setDraggedOver,
  uppy,
  getValidF<PERSON>,
  isReadOn<PERSON>,
  editor,
  connectedIntegration,
}: Props) => {
  const { currentWorkspace } = useWorkspace();

  return (
    <div className="font-discord flex items-center justify-center text-base">
      <div className="font-smoothing flex h-full w-full items-start gap-2 rounded-lg leading-4">
        <img
          className="h-10 w-10 rounded-full"
          src={
            connectedIntegration?.account.profileImageUrl ||
            "/discord/default_user.png"
          }
        />
        <div className="flex w-full flex-col">
          <div className="flex items-center gap-1">
            <span className="font-bold text-black">
              {connectedIntegration?.account.username || currentWorkspace?.name}
            </span>
            <div className="mr-1 rounded-[3px] bg-[#5865F2] px-1 text-[10px] leading-[15px] font-semibold text-white">
              BOT
            </div>
            <div
              key={DateTime.now().toFormat("h:mm a")}
              className="text-sm text-black"
            >
              Today at {DateTime.now().toFormat("h:mm a")}
            </div>
          </div>
          <div className="leading-[22px]">{editor}</div>
          <DiscordAssetPreviewNewDesign
            contentID={contentID}
            assets={assets}
            setAssets={setAssets}
            uploadingAssets={uploadingAssets}
            draggedOver={draggedOver}
            setDraggedOver={setDraggedOver}
            uppy={uppy}
            getValidFiles={getValidFiles}
            isReadOnly={isReadOnly}
          />
        </div>
      </div>
    </div>
  );
};

export default DiscordPostPreviewNewDesign;
