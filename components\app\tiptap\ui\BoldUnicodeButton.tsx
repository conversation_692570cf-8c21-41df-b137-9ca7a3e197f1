import { Icon } from "@liveblocks/react-ui";
import { Editor } from "@tiptap/react";
import {
  convertToUnicodeBold,
  isUnicodeBold,
} from "../utils/unicodeTextFormattingTools";
import UnicodeStyleButton from "./UnicodeStyleButton";

type Props = {
  editor: Editor | null;
  useCustomToolbarButton?: boolean;
};

const BoldUnicodeButton = ({
  editor,
  useCustomToolbarButton = false,
}: Props) => {
  return (
    <UnicodeStyleButton
      editor={editor}
      name="Bold"
      icon={<Icon.Bold />}
      isStyleActive={isUnicodeBold}
      convertToStyle={convertToUnicodeBold}
      shortcutKey="b"
      useCustomToolbarButton={useCustomToolbarButton}
    />
  );
};

export default BoldUnicodeButton;
