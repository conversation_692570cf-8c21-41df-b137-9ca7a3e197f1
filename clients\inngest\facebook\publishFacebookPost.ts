import publishFacebookPost from "apiUtils/facebook/publishFacebookPost";
import uploadFacebookAssets from "apiUtils/facebook/uploadFacebookAssets";
import notifyOfScheduledPost from "apiUtils/notifyOfScheduledPost";
import { NonRetriableError, RetryAfterError, slugify } from "inngest";
import { DateTime } from "luxon";
import { db } from "~/clients";
import { AuthError, ScheduledPostResult } from "~/types/ScheduledPostResult";
import { inngest } from "../inngest";

const RETRIES = 4;

export default inngest.createFunction(
  {
    id: slugify("Facebook Publish Post"),
    retries: RETRIES,
    name: "Facebook Publish Post",
    concurrency: {
      limit: 1,
      key: "event.data.postID",
    },
    cancelOn: [
      {
        event: "post.rescheduled",
        if: `event.data.channel == "Facebook" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.unscheduled",
        if: `event.data.channel == "Facebook" && event.data.postID == async.data.postID`,
      },
      {
        event: "post.deleted",
        match: "data.postID",
      },
    ],
    onFailure: async ({ error, event, step }) => {
      const data = event.data.event.data;
      const { postID } = data;
      console.log(`Post:${postID}: Final Error ${error.message}`);

      const isAuthError = error.name === "AuthError";

      const post = await db.post.findFirstOrThrow({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          subscribers: {
            select: {
              userID: true,
            },
          },
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
            },
          },
          facebookIntegration: {
            select: {
              page: {
                select: {
                  externalID: true,
                  name: true,
                },
              },
            },
          },
          scheduledPosts: {
            where: {
              channel: "Facebook",
              status: "Scheduled",
            },
          },
        },
      });

      const scheduledPost = post.scheduledPosts[0];

      if (scheduledPost == null) {
        console.log(`Post:${postID}: No longer scheduled`);
        return;
      }

      await notifyOfScheduledPost({
        post,
        scheduledPost,
        status: "Error",
        account: post.facebookIntegration
          ? {
              name: `${post.facebookIntegration.page.name}`,
              url: `https://www.facebook.com/${post.facebookIntegration.page.externalID}`,
            }
          : undefined,
        error: error.message,
        isAuthError,
      });
    },
  },
  [
    {
      event: "post.scheduled",
      if: `event.data.channel == "Facebook"`,
    },
    {
      event: "post.rescheduled",
      if: `event.data.channel == "Facebook"`,
    },
  ],
  async ({ event, step, attempt }) => {
    const { delayUntil, postID } = event.data;

    const delayUntil5MinutesBefore = DateTime.fromJSDate(new Date(delayUntil))
      .minus({
        minutes: 5,
      })
      .toJSDate();

    await step.sleepUntil(
      "Upload assets 5 minutes before",
      delayUntil5MinutesBefore
    );

    const getPost = async () => {
      return await db.post.findFirst({
        where: {
          id: postID,
        },
        select: {
          id: true,
          title: true,
          workspaceID: true,
          workspace: {
            select: {
              id: true,
              name: true,
              companyID: true,
              userWorkspaces: {
                select: {
                  userID: true,
                },
              },
              slackWebhooks: {
                where: {
                  type: "SuccessfulPost",
                },
                select: {
                  type: true,
                  url: true,
                },
              },
            },
          },
          subscribers: {
            select: {
              userID: true,
            },
          },
          createdByID: true,
          facebookIntegration: {
            select: {
              page: {
                select: {
                  name: true,
                  externalID: true,
                },
              },
              accessToken: true,
            },
          },
          facebookContent: {
            select: {
              id: true,
              copy: true,
              type: true,
              coverPhoto: true,
              engagements: {
                select: {
                  id: true,
                  postDelaySeconds: true,
                },
              },
              assets: {
                select: {
                  id: true,
                  position: true,
                  asset: true,
                  facebookMediaID: true,
                  facebookMediaExpiresAt: true,
                },
                orderBy: {
                  position: "asc",
                },
              },
            },
          },
          scheduledPosts: {
            where: {
              channel: "Facebook",
              status: "Scheduled",
            },
          },
          approvals: {
            where: {
              isBlocking: true,
              approvedAt: null,
            },
            select: {
              id: true,
            },
          },
        },
      });
    };

    const post = await getPost();

    if (post == null) {
      console.log(`Post:${postID}: Post not found`);
      return {
        status: "Skipped",
        comment: "Post has been deleted",
      };
    }

    const uploadResponse = await step.run(
      "Upload Facebook Assets",
      async () => {
        const uploadResponse = await uploadFacebookAssets(post);

        if (uploadResponse.status === "NonRetriableError") {
          if (uploadResponse.isAuthError) {
            throw new AuthError(uploadResponse.error);
          } else {
            throw new NonRetriableError(uploadResponse.error);
          }
        } else if (uploadResponse.status === "Error") {
          throw new Error(uploadResponse.error);
        }

        return uploadResponse;
      }
    );

    await step.sleepUntil("Wait for Scheduled Time", delayUntil);

    const response = await step.run("Publish Facebook Post", async () => {
      console.log(
        `Post:${postID}: Publishing Facebook Post Attempt ${attempt + 1}/${
          RETRIES + 1
        }`
      );

      const scheduledPost = post.scheduledPosts[0];

      if (scheduledPost == null) {
        console.log(`Post:${postID}: No longer scheduled`);
        return {
          status: "Skipped",
          comment: "No scheduled posts",
        };
      }

      if (post.approvals.length > 0) {
        console.log(`Post:${postID}: Blocked by approval`);
        return {
          status: "Skipped",
          comment: "Post has been blocked by approval",
        };
      }

      let publishResponse: ScheduledPostResult;
      if (uploadResponse.status === "Success") {
        const updatedPost = await getPost();
        if (updatedPost == null) {
          return {
            status: "Skipped",
            comment: "Post has been deleted",
          };
        }
        publishResponse = await publishFacebookPost(updatedPost);
      } else {
        publishResponse = await publishFacebookPost(post);
      }

      if (publishResponse.status === "NonRetriableError") {
        console.log(
          `Post:${postID}: NonRetriableError ${publishResponse.error}`
        );

        if (publishResponse.isAuthError) {
          throw new AuthError(publishResponse.error);
        } else {
          throw new NonRetriableError(publishResponse.error);
        }
      } else if (publishResponse.status === "Error") {
        console.log(
          `Post:${postID}: Publish Attempt ${attempt + 1}/${
            RETRIES + 1
          } Error ${publishResponse.error}`
        );

        throw new RetryAfterError(publishResponse.error, 10_000);
      }

      console.log(`Post:${postID}: Published YouTube Shorts Post`);

      await notifyOfScheduledPost({
        post,
        scheduledPost,
        account: {
          name: post.facebookIntegration!.page.name,
          url: `https://www.facebook.com/${
            post.facebookIntegration!.page.externalID
          }`,
        },
        ...publishResponse,
      });

      return publishResponse;
    });

    return response;
  }
);
