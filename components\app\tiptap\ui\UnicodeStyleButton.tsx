import { Too<PERSON>bar } from "@liveblocks/react-tiptap";
import { Editor } from "@tiptap/react";
import { Fragment, Node } from "prosemirror-model";
import { useEffect } from "react";
import { cleanUnicodeCharacters } from "../utils/unicodeTextFormattingTools";
import ToolbarButton from "./ToolbarButton";

type UnicodeStyleButtonProps = {
  editor: Editor | null;
  name: string;
  icon: React.ReactNode;
  isStyleActive: (text: string) => boolean;
  convertToStyle: (text: string) => string;
  shortcutKey?: string;
  useCustomToolbarButton?: boolean;
};

const UnicodeStyleButton = ({
  editor,
  name,
  icon,
  isStyleActive,
  convertToStyle,
  shortcutKey,
  useCustomToolbarButton = false,
}: UnicodeStyleButtonProps) => {
  // Helper function to process text nodes while preserving structure
  const applyUnicodeFormatting = () => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    if (from === to) return;

    // Get the selected content as a slice (preserves structure)
    const slice = editor.state.doc.slice(from, to);

    const processedFragment = processFragment(slice.content);

    // Create a transaction and replace the selection
    const tr = editor.state.tr.replaceWith(from, to, processedFragment);
    editor.view.dispatch(tr);

    editor.commands.setTextSelection(to);
  };

  // Process a fragment and apply Unicode formatting
  const processFragment = (fragment: Fragment) => {
    if (!fragment?.content) {
      return fragment;
    }

    const result: Node[] = [];

    fragment.forEach((node: Node) => {
      if (node.isText) {
        // Apply Unicode formatting to text nodes
        const text = node.text;
        let newText: string | null | undefined;

        if (text) {
          newText = isStyleActive(text)
            ? cleanUnicodeCharacters(text)
            : convertToStyle(text);
        } else {
          newText = text;
        }

        // Create a new text node with the same marks but updated text
        result.push(node.type.schema.text(newText ?? "", node.marks));
      } else {
        // For non-text nodes, process their content recursively
        const content = processFragment(node.content);
        result.push(node.copy(content));
      }
    });

    // Check if we can create a new fragment
    if (result.length > 0) {
      return Fragment.from(result);
    } else {
      return fragment;
    }
  };

  useEffect(() => {
    if (!editor || !shortcutKey) return;

    // Add the keyboard shortcut handler
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!(event.metaKey || event.ctrlKey)) return;
      if (event.key.toLowerCase() !== shortcutKey.toLowerCase()) return;

      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      if (!selectedText) return;

      event.preventDefault();
      if (isStyleActive(selectedText)) {
        editor
          .chain()
          .focus()
          .deleteSelection()
          .insertContent(cleanUnicodeCharacters(selectedText))
          .run();
      } else {
        editor
          .chain()
          .focus()
          .deleteSelection()
          .insertContent(convertToStyle(selectedText))
          .run();
      }
    };

    // Add event listener
    editor.view.dom.addEventListener("keydown", handleKeyDown);

    // Cleanup on unmount
    return () => {
      editor.view.dom.removeEventListener("keydown", handleKeyDown);
    };
  }, [editor, shortcutKey, isStyleActive, convertToStyle]);

  const isActive = () => {
    const { from, to } = editor?.state.selection || {};
    if (!editor || from === undefined || to === undefined) return false;
    const selectedText = editor.state.doc.textBetween(from, to);
    return !!selectedText && isStyleActive(selectedText);
  };

  if (useCustomToolbarButton) {
    return (
      <ToolbarButton isActive={isActive} onClick={applyUnicodeFormatting}>
        {icon}
      </ToolbarButton>
    );
  }

  return (
    <Toolbar.Toggle
      name={name}
      shortcut={shortcutKey ? `Mod-${shortcutKey.toUpperCase()}` : undefined}
      active={isActive()}
      icon={icon}
      onClick={applyUnicodeFormatting}
    />
  );
};

export default UnicodeStyleButton;
