import { Controller, Path } from "react-hook-form";

import { AutosizeInput, Label } from "~/components";

import { Error } from ".";

import Rules from "./rules";

import { RefObject } from "react";
import { Size } from "~/components/AutosizeInput";
import camelCaseToWords from "~/utils/camelCaseToWords";

type FormAutosizeInputProps<T> = {
  reference?: RefObject<HTMLInputElement>;
  control: any;
  name: Path<T>;
  placeholder?: string;
  label?: string;
  isOptional?: boolean;
  tooltipText?: string;
  autoFocus?: boolean;
  rules?: Rules;
  size?: Size;
  showBorder?: boolean;
};

const FormAutosizeInput = <T,>({
  control,
  name,
  placeholder,
  label,
  tooltipText,
  autoFocus,
  rules = {},
  size,
  showBorder = true,
}: FormAutosizeInputProps<T>) => {
  const required = rules.required === true;
  if (required) {
    rules = {
      ...rules,
      required: {
        value: true,
        message: `${label || camelCaseToWords(name)} is required`,
      },
    };
  }

  if (typeof rules.min === "number") {
    rules = {
      ...rules,
      min: { value: rules.min, message: `Minimum value is ${rules.min}` },
    };
  }

  if (typeof rules.max === "number") {
    rules = {
      ...rules,
      max: { value: rules.max, message: `Maximum value is ${rules.max}` },
    };
  }

  if (typeof rules.maxLength === "number") {
    rules = {
      ...rules,
      maxLength: {
        value: rules.maxLength,
        message: `Max length is ${rules.maxLength}`,
      },
    };
  }

  if (typeof rules.minLength === "number") {
    rules = {
      ...rules,
      minLength: {
        value: rules.minLength,
        message: `Max length is ${rules.minLength}`,
      },
    };
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={rules as object}
      render={({
        field: { onChange, onBlur, value, ref },
        fieldState: { error },
      }) => {
        return (
          <Label
            value={label}
            isRequired={!!rules.required}
            tooltipText={tooltipText}
          >
            <AutosizeInput
              inputRef={ref}
              name={name}
              autoFocus={autoFocus}
              value={value as string}
              onChange={onChange}
              onBlur={onBlur}
              placeholder={placeholder}
              size={size}
              showBorder={showBorder}
            />
            <Error error={error} />
          </Label>
        );
      }}
    />
  );
};

export default FormAutosizeInput;
