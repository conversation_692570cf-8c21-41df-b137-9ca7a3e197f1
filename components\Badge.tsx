import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNode, useState } from "react";

import classnames from "classnames";
import Tooltip, { TooltipSide } from "./Tooltip";

export type BadgeIntent =
  | "secondary"
  | "warning"
  | "info"
  | "danger"
  | "success"
  | "none";

type Size = "sm" | "md";

type BadgeProps = {
  /** Intent controls the color of the badge to convey meaning and reinforces importance */
  intent?: BadgeIntent;
  /** The text and border color */
  color?: string;
  /** The text and border color */
  backgroundColor?: string;
  /** Solid badges are filled in with the main color
   *  Light badges are filled in with a lighter opacity of the color + dark text
   *  Hollow badges have the text with the intent color and otherwise gray */
  kind?: "solid" | "light" | "hollow";
  /** The font size of the label */
  size?: Size;
  /** The icon displayed to the left of the label */
  icon?: ReactNode;
  /** The text to be displayed */
  tooltipText?: string | null;
  tooltipSide?: TooltipSide;
  label?: string;
  /** Whether the options should be uppercase */
  isUppercase?: boolean;
  children?: ReactNode;
  /** Whether the badge has a border */
  border?: boolean;
  onClick?: MouseEventHandler<HTMLDivElement>;
  showHoverState?: boolean;
  borderRadius?: "sm" | "full";
  truncateLabel?: boolean;
};

const Badge = ({
  intent,
  color,
  backgroundColor,
  kind = "light",
  icon,
  tooltipText,
  tooltipSide,
  label,
  isUppercase = false,
  border = true,
  borderRadius = "full",
  size = "md",
  onClick,
  showHoverState = false,
  children,
  truncateLabel = false,
}: BadgeProps) => {
  const [isHovering, setIsHovering] = useState<boolean>(false);

  const className = classnames(
    "inline-flex gap-1.5 items-center h-0 whitespace-nowrap w-content transition-colors resize-none",
    {
      grid: truncateLabel,
      /** Border */
      "border-[0.25px]": border,

      /** On Click */
      "cursor-pointer": !!onClick || showHoverState,
      "pointer-events-none": !onClick && !showHoverState,

      /** Border Radius */
      "rounded-xs": borderRadius === "sm",
      "rounded-full": borderRadius === "full",

      /** Font */
      "font-eyebrow-sm": size === "sm" && isUppercase,
      "font-eyebrow": size === "md" && isUppercase,
      "font-body-xs": size === "sm" && !isUppercase,
      "font-body-sm": size === "md" && !isUppercase,

      /** Label Size */
      "px-1.5 py-2": size === "sm",
      "py-3 px-3": size === "md",

      /** Basic Styles */
      "hover:border-secondary":
        intent === "secondary" && (!!onClick || showHoverState),

      "bg-secondary text-secondary-light":
        intent === "secondary" && kind === "solid",
      "bg-secondary-light text-secondary":
        intent === "secondary" && kind === "light",
      "bg-inherit text-secondary border-light-gray":
        intent === "secondary" && kind === "hollow",

      /** Warning Styles */
      "border-warning": intent === "warning",
      "bg-warning text-white": intent === "warning" && kind === "solid",
      "bg-warning-light text-warning": intent === "warning" && kind === "light",
      "text-warning": intent === "warning" && kind === "hollow",

      /** Info Styles */
      "bg-lightest-gray border-lightest-gray text-medium-dark-gray":
        intent === "info",
      // "bg-info text-white": intent === "info" && kind === "solid",
      // "bg-info/25 text-info": intent === "info" && kind === "light",

      /** Danger Styles */
      "border-danger": intent === "danger",
      "bg-danger text-white": intent === "danger" && kind === "solid",
      "bg-danger-light text-danger": intent === "danger" && kind === "light",
      "text-danger": intent === "danger" && kind === "hollow",

      /** Success Styles */
      "border-[#DCF1E9]": intent === "success",
      "bg-success text-white": intent === "success" && kind === "solid",
      "bg-[#DCF1E9] text-success": intent === "success" && kind === "light",

      /** None Styles */
      "bg-surface border-light-gray": intent === "none",
      "text-medium-gray": intent === "none" && kind === "light",
      "hover:text-dark-gray":
        intent === "none" && kind === "light" && (onClick || showHoverState),
      "border-light-gray hover:border-basic transition-colors":
        (!!onClick || showHoverState) && intent === "none",

      /** No Intent Styles */
      [`border-transparent hover:border-[${color}]`]: !intent,
    }
  );

  return (
    <Tooltip value={tooltipText} side={tooltipSide}>
      <div
        onClick={onClick}
        className={className}
        style={{
          color,
          backgroundColor,
          borderColor: !!intent
            ? undefined
            : isHovering && (!!onClick || showHoverState)
              ? color
              : "transparent",
        }}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        {icon}
        {label ? (
          <div className="w-full max-w-[230px] overflow-hidden text-ellipsis 2xl:max-w-full">
            {label}
          </div>
        ) : (
          children
        )}
      </div>
    </Tooltip>
  );
};

export default Badge;
