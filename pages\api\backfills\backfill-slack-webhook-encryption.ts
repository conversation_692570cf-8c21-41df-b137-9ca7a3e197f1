import { StatusCodes } from "http-status-codes";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/clients";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const webhooks = await db.slackWebhook.findMany({
    where: {
      url: {
        startsWith: "https://hooks.slack",
      },
    },
    select: {
      id: true,
      url: true,
      accessToken: true,
    },
  });

  let count = 0;
  for await (const webhook of webhooks) {
    console.log(`Updating webhook ${count}/${webhooks.length}`, webhook.id);
    const { id, url, accessToken } = webhook;

    await db.slackWebhook.update({
      where: { id },
      data: {
        url,
        accessToken,
      },
    });

    count++;
  }

  res.status(StatusCodes.OK).send({
    webhooks: webhooks.length,
  });
};

export default handler;
