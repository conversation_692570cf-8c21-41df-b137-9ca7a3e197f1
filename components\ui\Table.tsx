import * as React from "react";
import { cn } from "~/utils";

const Root = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
  <div className="border-lightest-gray relative w-full overflow-auto rounded-sm border">
    <table
      ref={ref}
      className={cn("font-body-sm w-full caption-bottom", className)}
      {...props}
    />
  </div>
));
Root.displayName = "Table";

const Toolbar = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("flex min-h-10 items-center", className)}>
      {children}
    </div>
  );
};

const Header = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead
    ref={ref}
    className={cn(
      "border-lightest-gray font-eyebrow text-medium-gray [&_tr]:border-b",
      className
    )}
    {...props}
  />
));
Header.displayName = "TableHeader";

const Body = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn("[&_tr:last-child]:border-0", className)}
    {...props}
  />
));
Body.displayName = "TableBody";

const Footer = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tfoot
    ref={ref}
    className={cn("border-t font-medium last:[&>tr]:border-b-0", className)}
    {...props}
  />
));
Footer.displayName = "TableFooter";

const Row = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      "data-[state=selected]:bg-muted border-b transition-colors",
      className
    )}
    {...props}
  />
));
Row.displayName = "TableRow";

const Head = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement> & {
    isSortable?: boolean;
  }
>(({ className, isSortable, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      "h-10 px-4 text-left align-middle font-medium whitespace-nowrap transition-colors [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
      {
        "hover:bg-lightest-gray-30 hover:cursor-pointer": isSortable,
      },
      className
    )}
    {...props}
  />
));
Head.displayName = "TableHead";

const Cell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    className={cn(
      "h-13 p-3 px-4 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
      className
    )}
    {...props}
  />
));
Cell.displayName = "TableCell";

const Caption = React.forwardRef<
  HTMLTableCellElement,
  React.HTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <caption
    ref={ref}
    className={cn("text-muted-foreground mt-4 text-sm", className)}
    {...props}
  />
));
Caption.displayName = "TableCaption";

export default {
  Root,
  Toolbar,
  Header,
  Body,
  Footer,
  Row,
  Head,
  Cell,
  Caption,
};
