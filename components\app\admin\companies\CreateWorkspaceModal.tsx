import { useForm } from "react-hook-form";
import { toast } from "sonner";
import Label from "~/components/Label";
import { FormCheckbox, FormSelect, FormTextInput } from "~/components/form";
import Button from "~/components/ui/Button";
import C<PERSON>enza from "~/components/ui/Credenza";
import { getFullName, handleTrpcError, trpc } from "~/utils";

type FormValues = {
  name: string;
  subscription: "Basic" | "Standard" | "Unlimited";
  users: {
    [userID: string]: boolean;
  };
};

type User = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
};

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  companyID: string;
  users: User[];
};

const CreateWorkspaceModal = ({ companyID, open, setOpen, users }: Props) => {
  const trpcUtils = trpc.useUtils();

  const createWorkspaceMutation =
    trpc.admin.company.createWorkspace.useMutation();

  const { control, handleSubmit } = useForm<FormValues>();

  const onSubmit = (data: FormValues) => {
    const userIDs = Object.keys(data.users).filter(
      (userID) => data.users[userID]
    );

    if (userIDs.length === 0) {
      return toast.error("No users selected", {
        description: "Please select at least one user",
      });
    } else {
      createWorkspaceMutation.mutate(
        {
          companyID,
          name: data.name,
          subscription:
            data.subscription === "Unlimited" ? null : data.subscription,
          userIDs,
        },
        {
          onSuccess: () => {
            trpcUtils.admin.company.get.refetch({
              id: companyID,
            });
            setOpen(false);
          },
          onError: (error) => {
            handleTrpcError({
              title: "Error creating workspace",
              error,
            });
          },
        }
      );
    }
  };

  return (
    <Credenza.Root open={open} onOpenChange={setOpen}>
      <Credenza.Header>
        <Credenza.Title>Create Workspace</Credenza.Title>
        <Credenza.Description>
          Create a new workspace with name, subscription and users
        </Credenza.Description>
      </Credenza.Header>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Credenza.Body className="space-y-4">
          <Label value="Name" isUppercase={false}>
            <FormTextInput
              control={control}
              kind="outline"
              name="name"
              rules={{ required: true }}
            />
          </Label>
          <Label value="Subscription" isUppercase={false}>
            <FormSelect
              control={control}
              name="subscription"
              options={[
                { label: "Basic", value: "Basic" },
                { label: "Standard", value: "Standard" },
                { label: "Unlimited", value: "Unlimited" },
              ]}
              rules={{ required: true }}
            />
          </Label>
          <Label value="Users" isUppercase={false}>
            <div className="mt-2 flex flex-col gap-2">
              {users.map((user) => (
                <FormCheckbox
                  key={user.id}
                  control={control}
                  name={`users.${user.id}`}
                  label={
                    <div className="flex items-center gap-1">
                      <span>{getFullName(user)}</span>
                      <span className="text-medium-dark-gray">
                        ({user.email})
                      </span>
                    </div>
                  }
                />
              ))}
            </div>
          </Label>
        </Credenza.Body>
        <Credenza.Footer>
          <Button type="submit" isLoading={createWorkspaceMutation.isPending}>
            Create Workspace
          </Button>
        </Credenza.Footer>
      </form>
    </Credenza.Root>
  );
};

export default CreateWorkspaceModal;
