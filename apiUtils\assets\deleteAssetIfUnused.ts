import { db } from "~/clients";
import supabaseAdminClient from "~/clients/supabaseAdminClient";

// If the asset is no longer used we can delete from the database & from storage
const deleteAssetIfUnused = async (assetID: string) => {
  const asset = await db.asset.findFirst({
    where: {
      id: assetID,
    },
    include: {
      _count: {
        select: {
          discordAssets: true,
          facebookAssets: true,
          facebookCoverPhotos: true,
          instagramAssets: true,
          instagramCoverPhotos: true,
          linkedInAssets: true,
          linkedInCoverPhotos: true,
          linkedInCaptions: true,
          slackAssets: true,
          threadsAssets: true,
          tweetAssets: true,
          tikTokAsset: true,
          webflowAssets: true,
          youTubeShortsAssets: true,
          postAssets: true,
        },
      },
    },
  });

  if (!asset) {
    // Most likely the asset was already deleted
    return {
      deleted: true,
    };
  }

  const isAssetUsed = Object.values(asset._count).some((count) => count > 0);

  if (isAssetUsed) {
    return {
      deleted: false,
    };
  } else {
    // Delete from storage if path is unused
    const assetsWithSamePath = await db.asset.findMany({
      where: {
        path: asset.path,
        id: {
          not: assetID,
        },
      },
    });

    if (assetsWithSamePath.length === 0) {
      await supabaseAdminClient.storage.from("assets").remove([asset.path]);
    }

    // Delete from database
    await db.asset.delete({
      where: {
        id: assetID,
      },
    });

    return {
      deleted: true,
    };
  }
};

export default deleteAssetIfUnused;
