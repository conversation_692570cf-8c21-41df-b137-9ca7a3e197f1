import assert from "assert";
import chunk from "lodash/chunk";
import { db } from "~/clients";
import LinkedInScraper from "~/clients/rapid_api/LinkedInScraper";
import { removeEmptyElems } from "~/utils";
import saveScrapedProfileData from "./saveScrapedProfileData";

const getLinkedInPostEngagements = async (activityUrns: string[]) => {
  const scraper = new LinkedInScraper();

  const posts = await db.linkedInPost.findMany({
    where: {
      activityUrn: {
        in: activityUrns,
      },
    },
  });

  const [commentsResponses, reactionsResponses] = await Promise.all([
    Promise.all(
      activityUrns.map(async (activityUrn) => {
        const commentsResponse = await scraper.post.getComments(activityUrn);
        if (!commentsResponse.success) {
          throw new Error(commentsResponse.errors.join(", "));
        }

        const postUrl = `https://www.linkedin.com/feed/update/${activityUrn}`;
        return commentsResponse.data.map((comment) => ({
          id: comment.urn,
          type: "COMMENT" as const,
          text: comment.text,
          postUrl: postUrl,
          permalink: comment.permalink,
          activityUrn: activityUrn,
          author: {
            id: comment.author.id,
            encryptedUrn: comment.author.encryptedUrn,
            url: comment.author.url,
          },
          publishedAt: comment.publishedAt,
        }));
      })
    ),
    Promise.all(
      activityUrns.map(async (activityUrn) => {
        const reactionsResponse = await scraper.post.getReactions(activityUrn);
        if (!reactionsResponse.success) {
          throw new Error(reactionsResponse.errors.join(", "));
        }

        const postUrl = `https://www.linkedin.com/feed/update/${activityUrn}`;
        return reactionsResponse.data.map((reaction) => ({
          id: reaction.author.id,
          postUrl: postUrl,
          type: reaction.type,
          activityUrn: activityUrn,
          author: {
            id: reaction.author.id,
            encryptedUrn: reaction.author.encryptedUrn,
            url: reaction.author.url,
          },
        }));
      })
    ),
  ]);

  const comments = commentsResponses.flat();
  const reactions = reactionsResponses.flat();

  const uniqueAuthorUrls = Array.from(
    new Set(
      removeEmptyElems(
        [...comments, ...reactions].map((engagement) => engagement.author.url)
      )
    )
  );

  console.log(`Unique author URLs`, uniqueAuthorUrls);

  // Get unique author URLs from both comments and reactions
  const encryptedUrnAuthorUrns = removeEmptyElems(
    uniqueAuthorUrls
      .filter((url) => !url.includes("/company/") && !url.endsWith("/in/"))
      .map((url) => {
        if (url.endsWith("/")) {
          return url.slice(0, -1).split("/").pop();
        }
        return url.split("/").pop();
      })
  );

  const existingProfiles = await db.linkedInScrapedAccountData.findMany({
    where: {
      encryptedUrn: {
        in: encryptedUrnAuthorUrns,
      },
    },
  });

  const missingProfiles = encryptedUrnAuthorUrns.filter(
    (encryptedUrn) =>
      !existingProfiles.some((profile) => profile.encryptedUrn === encryptedUrn)
  );

  const chunks = chunk(missingProfiles, 30);

  for (const chunk of chunks) {
    // Fetch profiles for each unique author
    await Promise.all(
      chunk.map(async (encryptedUrn) => {
        await saveScrapedProfileData(encryptedUrn);
      })
    );
  }

  const authorProfiles = await db.linkedInScrapedAccountData.findMany({
    where: {
      encryptedUrn: {
        in: encryptedUrnAuthorUrns,
      },
    },
    select: {
      id: true,
      encryptedUrn: true,
      firstName: true,
      lastName: true,
      headline: true,
      summary: true,
      vanityName: true,
      city: true,
      country: true,
      profileImageUrl: true,
      profileImageExpiresAt: true,
      followerCount: true,
      jobs: {
        select: {
          id: true,
          title: true,
          startDate: true,
          endDate: true,
          description: true,
          location: true,
          organizationName: true,
          organization: {
            select: {
              id: true,
              name: true,
              vanityName: true,
              profileImageUrl: true,
              organizationData: true,
            },
          },
        },
      },
    },
  });

  // Create author profile map
  const authorProfileMap = Object.fromEntries(
    removeEmptyElems(authorProfiles).map((profile) => [
      profile.encryptedUrn,
      {
        ...profile,
        currentJob: profile.jobs.find((job) => job.endDate === null),
      },
    ])
  );

  // Process comments with profiles
  const commentsWithProfiles = removeEmptyElems(
    comments.map((comment) => {
      const author = authorProfileMap[comment.author.encryptedUrn] || null;
      const post = posts.find(
        (post) => post.activityUrn === comment.activityUrn
      );
      assert(post, `Post not found for comment ${comment.id}`);
      return author ? { ...comment, author, post } : null;
    })
  );

  await db.linkedInPostComment.createMany({
    data: commentsWithProfiles.map((comment) => ({
      externalID: comment.id,
      text: comment.text,
      postID: comment.post.id,
      authorID: comment.author.id,
      permalink: comment.permalink,
      publishedAt: comment.publishedAt,
    })),
    skipDuplicates: true,
  });

  const reactionsWithProfiles = removeEmptyElems(
    reactions.map((reaction) => {
      const author = authorProfileMap[reaction.author.encryptedUrn] || null;
      const post = posts.find(
        (post) => post.activityUrn === reaction.activityUrn
      );
      assert(post, `Post not found for reaction ${reaction.id}`);
      return author ? { ...reaction, author, post } : null;
    })
  );

  await db.linkedInPostReaction.createMany({
    data: reactionsWithProfiles.map((reaction) => ({
      postID: reaction.post.id,
      type: reaction.type,
      authorID: reaction.author.id,
    })),
    skipDuplicates: true,
  });

  await db.linkedInPost.updateMany({
    where: {
      id: {
        in: posts.map((post) => post.id),
      },
    },
    data: {
      lastScrapedEngagementsAt: new Date(),
    },
  });

  const engagements = [
    ...commentsWithProfiles.map((comment) => ({
      ...comment,
      type: "COMMENT",
    })),
    ...reactionsWithProfiles.map((reaction) => ({
      ...reaction,
      text: null,
    })),
  ];

  return engagements;
};

export default getLinkedInPostEngagements;
