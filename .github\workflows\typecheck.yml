name: TypeCheck

on: pull_request

jobs:
  typecheck:
    name: TypeScript Check
    runs-on: ubuntu-latest
    environment: Production
    env:
      NPM_PQINA_AUTH_TOKEN: ${{ secrets.NPM_PQINA_AUTH_TOKEN }}
      NPM_TIPTAP_AUTH_TOKEN: ${{ secrets.NPM_TIPTAP_AUTH_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Setup .npmrc for private registries
        run: |
          echo "@tiptap-pro:registry=https://registry.tiptap.dev/" >> .npmrc
          echo "//registry.tiptap.dev/:_authToken=${NPM_TIPTAP_AUTH_TOKEN}" >> .npmrc
          echo "@pqina:registry=https://npm.pqina.nl/" >> .npmrc
          echo "//npm.pqina.nl/:_authToken=${NPM_PQINA_AUTH_TOKEN}" >> .npmrc

      - name: Debug .npmrc
        run: cat .npmrc | sed 's/_authToken=.*/_authToken=***hidden***/g'

      - name: Install dependencies
        run: bun install

      - name: Run type check
        run: node --max-old-space-size=4096 ./node_modules/.bin/tsc --noEmit
