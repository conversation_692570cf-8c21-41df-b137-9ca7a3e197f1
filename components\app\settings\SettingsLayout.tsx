import classNames from "classnames";
import React, { ReactNode, createContext, useContext } from "react";
import { cn } from "~/utils";

type Props = {
  children: ReactNode;
  isFullWidth?: boolean;
};

const Root = ({ children, isFullWidth = false }: Props) => {
  return (
    <div
      className={classNames(
        "scrollbar-hidden mx-auto h-full w-full overflow-auto px-5 py-4 pb-40 md:px-0 md:pt-8"
      )}
    >
      <div
        className={classNames("mx-auto", {
          "w-full md:px-20": isFullWidth,
          "max-w-[550px]": !isFullWidth,
        })}
      >
        {children}
      </div>
    </div>
  );
};

const HeaderContext = createContext(false);

const Header = ({ children }: { children: ReactNode }) => {
  // Check if Description exists among children
  const hasDescription = React.Children.toArray(children).some(
    (child) => React.isValidElement(child) && child.type === Description
  );

  return (
    <HeaderContext.Provider value={hasDescription}>
      {children}
    </HeaderContext.Provider>
  );
};

const Title = ({ children }: { children: string | ReactNode }) => {
  const hasDescription = useContext(HeaderContext);

  return (
    <h4
      className={classNames({
        "mb-2": hasDescription,
        "mb-4 md:mb-10": !hasDescription,
      })}
    >
      {children}
    </h4>
  );
};

const Description = ({
  children,
  className,
}: {
  children: string | ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("font-body-sm text-medium-dark-gray mb-8", className)}>
      {children}
    </div>
  );
};

const Sections = ({ children }: { children: ReactNode }) => {
  let childrenArray = React.Children.toArray(children);

  return (
    <div className="flex flex-col gap-10">
      {childrenArray.map((child, index) => {
        return <div key={index}>{child}</div>;
      })}
    </div>
  );
};

type SectionProps = {
  children: ReactNode;
};

const Section = ({ children }: SectionProps) => {
  return <div className="font-body-sm">{children}</div>;
};

const SectionTitle = ({ children }: { children: ReactNode }) => {
  return (
    <div className="mb-2 flex items-center gap-2 font-medium">{children}</div>
  );
};

const SectionDescription = ({ children }: { children: ReactNode }) => {
  return <div className="text-medium-dark-gray mb-4">{children}</div>;
};

export default {
  Root,
  Header,
  Title,
  Description,
  Sections,
  Section,
  SectionTitle,
  SectionDescription,
};
